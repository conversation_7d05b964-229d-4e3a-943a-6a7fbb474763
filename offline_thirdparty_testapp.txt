

https://shiv.readthedocs.io/en/latest/

https://pypi.org/project/shiv/

https://www.tutorialspoint.com/python/python_command_line_arguments.htm



On Mac:
pip install shiv


/Users/<USER>/Library/Python/3.8/bin/shiv

cd ~
mkdir hello-world
cd hello-world

vi hello.py
#--------------------------------
import sys

s_version = '1.0.0'

def main():
    argv = sys.argv[1:]
    response = 'Hello world'

    if argv:
        for arg in argv:
            if arg == '--version':
                response = s_version

    print (response)

if __name__ == "__main__":
    main()

#--------------------------------

vi setup.py

#--------------------------------
from setuptools import setup

setup(
    name="hello-world",
    version="0.0.1",
    description="Greet the world.",
    py_modules=["hello"],
    entry_points={
        "console_scripts": ["hello=hello:main"],
    },
 )
#--------------------------------

/Users/<USER>/Library/Python/3.8/bin/shiv --reproducible -c hello -o hello .

./hello

./hello --version























