# A dashboard for slicer page services

service = 'dashboard'
version = service + '.0.2'

_ = """
This file gets loaded to:
/var/www/html/dashboard.py

using:
sudo vi /var/www/html/dashboard.py

Followed up with:
sudo systemctl restart dashboard-runner.service


It also requires:

sudo vi /etc/httpd/conf.d/python-dashboard.conf
----- start copy -----
WSGIScriptAlias /dashboard /var/www/html/dashboard.py
----- end copy -----

sudo chown apache:apache /var/www/html/dashboard.py

sudo systemctl restart httpd
sudo systemctl restart dashboard-runner.service

sudo systemctl status dashboard-runner.service


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/dashboard-runner
sudo chmod +x /var/www/html/dashboard-runner

# ===== begin: start file
#!/usr/bin/env python
import dashboard
dashboard.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/dashboard-runner.service
sudo systemctl daemon-reload
sudo systemctl stop dashboard-runner.service
sudo systemctl start dashboard-runner.service
sudo systemctl enable dashboard-runner.service

systemctl status dashboard-runner.service

sudo systemctl restart dashboard-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep dashboard-runner

OR

tail -f /var/log/syslog | fgrep dashboard-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/dashboard-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import dashboard; print(dashboard.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/dashboard


"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_dashboard


"""

_ = """
Development notes:

Callbacks:
https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Client-side_web_APIs/Fetching_data
https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Asynchronous/Promises
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise

https://www.google.com/search?q=How+to+load+data+into+tables+with+the+fetch+API+in+JavaScript&sa=X&ved=2ahUKEwj-lN-o77H4AhVzEEQIHeWdCJAQ1QJ6BAgmEAE&biw=1536&bih=881&dpr=2

error handling:
https://itnext.io/error-handling-with-async-await-in-js-26c3f20bc06a
https://dmitripavlutin.com/javascript-fetch-async-await/

Have a local js variable (or from the DOM), that is initially empty.
Call a fetch with our current value, and expect the then clause to fire,
    if the server says its value is different from what we sent.


"""

_data_collection = """

dashboard_raw_root = /mnt/disks/SSD/var/log/slicer/dashboard/raw/

Old:
sudo ls -l /mnt/disks/SSD/var/log/slicer/dashboard/raw/

sudo cat /mnt/disks/SSD/var/log/slicer/dashboard/raw/1682294400/1682294400
(has all the dashboard items for that hour


New as of 2023.06.14:

sudo ls -l /mnt/disks/SSD/var/log/slicer/dashboard/raw/1686700800

-rw-r--r--. 1 <USER> <GROUP> 557 Jun 14 15:02 1686754800_
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:03 1686754800_?????
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:03 1686754800_(all)
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:03 1686754800_AZ032
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:03 1686754800_CA182
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:03 1686754800_CostaRica
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_DEN2
-rw-r--r--. 1 <USER> <GROUP> 553 Jun 14 15:01 1686754800_DOM02
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_IDF??
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_MEX??
-rw-r--r--. 1 <USER> <GROUP> 529 Jun 14 15:01 1686754800_MEX03
-rw-r--r--. 1 <USER> <GROUP> 553 Jun 14 15:01 1686754800_MEX04
-rw-r--r--. 1 <USER> <GROUP> 529 Jun 14 15:01 1686754800_MEX05
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_MEX09
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_MEX15
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_MEX18
-rw-r--r--. 1 <USER> <GROUP> 516 Jun 14 15:01 1686754800_MO001
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_MS001
-rw-r--r--. 1 <USER> <GROUP> 526 Jun 14 15:01 1686754800_OH001
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:01 1686754800_OH085
-rw-r--r--. 1 <USER> <GROUP> 543 Jun 14 15:01 1686754800_PR005
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_PR006
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_PR010
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_SDS2
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_TX001
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_TX010
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_TYS2
-rw-r--r--. 1 <USER> <GROUP> 523 Jun 14 15:02 1686754800_VPN1
-rw-r--r--. 1 <USER> <GROUP> 517 Jun 14 15:02 1686754800_zzz01

sudo cat /mnt/disks/SSD/var/log/slicer/dashboard/raw/1686700800/1686754800_
(for all)

sudo cat /mnt/disks/SSD/var/log/slicer/dashboard/raw/1686700800/1686754800_VPN1


"""

import copy
import datetime
import traceback
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:  # for unittest to work
    import address2location
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import reports
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

s_color_clear = "(0, 0, 0, 0.0)"
s_color_green_good = "(0, 255, 0, 0.3)"
s_color_red_warning = "(255, 0, 0, 0.3)"
s_color_yellow_caution = "(255, 255, 100, 0.3)"

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    s_stash_report_file = service_config['s_stash_report_file']
    dashboard_raw_root = service_config['dashboard_raw_root']

    if 'days_to_keep_dashboard_data' in service_config:
        days_to_keep_dashboard_data = service_config['days_to_keep_dashboard_data']
    else:
        days_to_keep_dashboard_data = 60

    sites_to_drop = {}
    if 'sites_to_drop' in service_config:
        sites_to_drop = service_config['sites_to_drop']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_raw_content(siteid=''):  # From what the dashboard runner processes has saved
    # ----------------------------
    postfix_match = '_' + siteid
    raw_content = {}
    try:
        dirs_found = os.listdir(dashboard_raw_root)
        for dir_found in dirs_found:
            folder = dashboard_raw_root + dir_found
            try:
                for file_found in os.listdir(folder):
                    if postfix_match == file_found[-len(postfix_match):]:
                        file_name = folder + '/' + file_found
                        try:
                            json_content = json.loads(open(file_name, 'r').read())
                            raw_content[file_found] = json_content
                        except:
                            pass  # maybe the cleanup process removed the files while we were here
            except:
                pass  # maybe the cleanup process removed the directory while we were here
    except:
        pass  # Maybe not created yet

    return raw_content


# ----------------------------
def year_month_day_dot_hour_from_timestamp(time_key):
    # ----------------------------
    date_time = datetime.datetime.fromtimestamp(int(time_key.split('_')[0]))
    # print ('date_time.year', date_time.year)
    # print ('date_time.month', date_time.month)
    # print ('date_time.day', date_time.day)
    # print ('date_time.hour', date_time.hour)
    return_value = ''
    return_value += "{:04d}".format(int(date_time.year))
    return_value += "{:02d}".format(int(date_time.month))
    return_value += "{:02d}".format(int(date_time.day))
    return_value += '.'
    return_value += "{:02d}".format(int(date_time.hour))
    return return_value


# ----------------------------
def get_plot_values_from_raw(raw_content, item_key, oldest_time=None):
    # ----------------------------
    return_value = {}
    return_value['x_values'] = []
    return_value['y1_values'] = []
    return_value['y2_values'] = []
    return_value['y3_values'] = []
    return_value['y1_maximum'] = None
    return_value['y1_minimum'] = None

    os.environ["TZ"] = "UTC"

    for time_key in sorted(raw_content.keys()):
        if oldest_time:
            if time_key > str(oldest_time):
                use_it = True
            else:
                use_it = False
        else:
            use_it = True

        if use_it:
            try:
                x = year_month_day_dot_hour_from_timestamp(time_key)
                y1 = str(raw_content[time_key][item_key])
                y2 = y1
                y3 = y1

                try:
                    y1_value = float(y1)

                    if return_value['y1_maximum'] is None:
                        return_value['y1_maximum'] = y1_value
                    if y1_value > return_value['y1_maximum']:
                        return_value['y1_maximum'] = y1_value

                    if return_value['y1_minimum'] is None:
                        return_value['y1_minimum'] = y1_value
                    if y1_value < return_value['y1_minimum']:
                        return_value['y1_minimum'] = y1_value
                except:
                    pass

                return_value['x_values'].append(x)
                return_value['y1_values'].append(y1)
                return_value['y2_values'].append(y2)
                return_value['y3_values'].append(y3)
            except:
                pass

    return return_value


# ----------------------------
def calculate_just_the_hour_time_from_epoch_time(epoch_time):
    # ----------------------------
    # make midnight of this day, but still as a whole second single value
    date_time = datetime.datetime.fromtimestamp(epoch_time)

    year = date_time.year
    month = date_time.month
    day = date_time.day
    hour = date_time.hour

    return datetime.datetime(year, month, day, hour, 0).strftime('%s')


# ----------------------------
def calculate_just_the_day_time_from_epoch_time(epoch_time):
    # ----------------------------
    # make midnight of this day, but still as a whole second single value
    date_time = datetime.datetime.fromtimestamp(epoch_time)

    year = date_time.year
    month = date_time.month
    day = date_time.day

    return datetime.datetime(year, month, day, 0, 0).strftime('%s')


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(dashboard status)'

    status = os.system('systemctl is-active --quiet dashboard-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = output_file + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# Main is the loop for the "dashboard-runner" that the service starts
# ====================================
def main():
    # ====================================
    seconds_to_wait_between_passes = 59
    pass_count = 0

    worked_on_file = '/dev/shm/' + service + '_main.txt'
    worked_on_content = ''
    open(worked_on_file, 'w').write(worked_on_content)

    time_of_last_report = 0
    while True:
        try:
            time.sleep(2)
            time_now = time.time()

            if abs(time_now - time_of_last_report) > seconds_to_wait_between_passes:
                time_of_process_start = time.time()

                worked_on_content = str(time_of_process_start)
                open(worked_on_file, 'w').write(worked_on_content)

                data_store_content = datastore.all_datastore()
                management_id_d = {}
                try:
                    # slicer03
                    import management
                    for siteid in management.get_management_block_list(data_store_content):
                        management_id_d[siteid] = True
                    management_id_d['(all)'] = True

                    for siteid in address2location.get_all_locations():
                        management_id_d[siteid] = True
                except:
                    # slicer01
                    for siteid in address2location.get_all_locations():
                        management_id_d[siteid] = True

                available_siteid = sorted(management_id_d.keys())

                # add the 'standard ones we need also'
                available_siteid.append('')
                #                available_siteid.append('unassigned')

                # just do all for now
                #                available_siteid = ['']

                for siteid in available_siteid:
                    worked_on_content += '\n' + 'siteid: ' + siteid
                    open(worked_on_file, 'w').write(worked_on_content)
                    the_issues = reports.get_top_level_issues(siteid)

                    # save the (raw) issues, at least the last one for each hour, of each day
                    try:
                        output_file = dashboard_raw_root + calculate_just_the_day_time_from_epoch_time(
                            the_issues['time_now']) + '/' + calculate_just_the_hour_time_from_epoch_time(
                            the_issues['time_now']) + '_' + siteid

                        worked_on_content += '\n' + 'output_file: ' + output_file
                        open(worked_on_file, 'w').write(worked_on_content)

                        do_atomic_write_if_different(output_file, json.dumps(the_issues))
                    except:
                        worked_on_content += '\n' + 'except: ' + traceback.format_exc().replace("\"", "'")
                        open(worked_on_file, 'w').write(worked_on_content)
                        pass

                    # look to clean up old data days
                    try:
                        dirs_found = os.listdir(dashboard_raw_root)
                        for dir_found in sorted(dirs_found):
                            try:
                                days_diff = (time_now - float(dir_found)) / (24 * 60 * 60)

                                folder = dashboard_raw_root + dir_found

                                if days_diff > float(days_to_keep_dashboard_data) + 1.0:
                                    command = 'sudo rm -rf ' + folder
                                    do_one_command(command)

                            except:
                                worked_on_content += '\n' + 'except: ' + traceback.format_exc().replace("\"", "'")
                                open(worked_on_file, 'w').write(worked_on_content)
                                pass  # maybe not a number (some other named directory)
                    except:
                        worked_on_content += '\n' + 'except: ' + traceback.format_exc().replace("\"", "'")
                        open(worked_on_file, 'w').write(worked_on_content)
                        pass  # Maybe not created yet

                    # get all the current raw data (including what we just saved)
                    raw_content = get_raw_content(siteid)

                    # Build the summary (from the raw issues) for stashing away
                    stash_report_content = get_reportables(the_issues, raw_content, siteid)
                    try:
                        do_atomic_write_if_different(stash_file_for_site(siteid), json.dumps(stash_report_content))
                    except:
                        worked_on_content += '\n' + 'except: ' + traceback.format_exc().replace("\"", "'")
                        open(worked_on_file, 'w').write(worked_on_content)
                        pass

                # do this last, so that we wait atleast seconds_to_wait_between_passes
                time_of_last_report = time.time()

                pass_count += 1
                time_to_show = '{0:.3f}'.format(time_of_last_report - time_of_process_start)
                open('/dev/shm/running_exceptions_' + service, 'w').write(
                    'pass_count : ' + str(pass_count) + ', ' + time_to_show)
        except:
            startup_exceptions = traceback.format_exc().replace("\"", "'")
            try:
                open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
            except:
                pass


# ====================================
def make_body_POST(environ):
    # ====================================
    # do work on content

    # then return what GET would have done
    return make_body_GET(environ)


# ====================================
def stash_file_for_site(siteid):
    # ====================================
    site_file_extension = ''
    if siteid:
        site_file_extension = '.' + siteid
    return_value = s_stash_report_file + site_file_extension

    return return_value


# ====================================
def get_reportables(the_issues, raw_content, siteid=''):
    # ====================================
    oldest_time = time.time() - 36 * 60 * 60

    service_pack_key_recent = ''
    if 'service_pack_key_recent' in the_issues:
        service_pack_key_recent = the_issues['service_pack_key_recent']

    plot_word = 'plot'
    plot_word_2 = 'plot all'

    #    if siteid:
    #        plot_word = ''
    #        plot_word_2 = ''

    # format = https://pyformat.info/

    # ===================================================================
    # ===================================================================
    # ===================================================================
    layout_sections = []

    # ===================================================================
    prefix = '[---- Metrics ----]'
    item_d_list = []

    # add item
    item_d_list.append({
        'item': 'new_image_percent',
        'title': 'Percent on new image',
        'format': '{0:.1f}',
        'help': 'Green above 95. Yellow above 90. Red 90 and lower.',
        'green_above': 95, 'yellow_above': 90, 'red_above': -3,
        'click_link': '/reports?device_view=(services),sort=(Image),sortd=fwd,status=current',
    })

    item_d_list.append({
        'item': 'recent_service_pack_percent',
        'title': 'Percent on new service pack',
        'format': '{0:.1f}',
        'help': 'Green above 80. Yellow above 50. Red 50 and lower.',
        'green_above': 80, 'yellow_above': 50, 'red_above': -3,
        'click_link': '/reports?device_view=(services),sort=service_pack,sortd=rev,status=current',
    })

    item_d_list.append({
        'item': 'on_iot_percent',
        'title': 'Percent on wifi using IOT',
        'format': '{0:.1f}',
        'help': 'Green above 80. Yellow above 50. Red 50 and lower.',
        'green_above': 80, 'yellow_above': 50, 'red_above': -3,
        'click_link': '/reports?device_view=(main),sort=ssid,sortd=fwd,ssid=cahiot,status=current',
    })

    # finish this prefix
    layout_section = {'prefix': prefix, 'items_d_list': item_d_list}
    layout_sections.append(layout_section)
    # ===================================================================
    # ===================================================================
    prefix = '[---- Warnings ----] Red above 0'
    item_d_list = []

    # add item
    item_d_list.append({
        'item': 'on_corp_count',
        'title': 'Devices on corp wifi',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(main),sort=ssid,sortd=fwd,ssid=corp,status=current',
    })

    item_d_list.append({
        'item': 'on_corp_and_not_service_pack_count',
        'title': 'Devices on corp wifi and not at SP target',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(main),sort=service_pack,sortd=fwd,ssid=corp,status=current',
    })

    item_d_list.append({
        'item': 'disk_high',
        'title': 'Disk utilization is high',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(memory),sort=(disk_use),sortd=rev,summary_view=(None),status=current',
    })

    item_d_list.append({
        'item': 'outages',
        'title': 'Support service Outage',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(services),sort=outage,sortd=fwd,summary_view=(None),status=current,outage=(not_empty)',
    })

    item_d_list.append({
        'item': 'inspect_comms',
        'title': 'inspect_comms',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(details),inspect_comms=1,sort=inspect_comms,sortd=fwd',
    })

    item_d_list.append({
        'item': 'certificates_flags',
        'title': 'certificates_flags',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/certificates',
    })

    item_d_list.append({
        'item': 'unassigned_management_ID',
        'title': 'unassigned_management_ID',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?mgmntblock=(unassigned)',
    })

    item_d_list.append({
        'item': 'screen_threshold_exceeded',
        'title': 'screen_threshold_exceeded',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?device_view=(scrn_res),sort=screen_over_threshold,sortd=rev,status=current',
    })

    item_d_list.append({
        'item': 'retired_device_current',
        'title': 'retired_device_current',
        'format': '{:d}',
        'red_above': 0,
        'click_link': '/reports?mgmntblock=(retired),status=current',
    })


    # finish this prefix
    layout_section = {'prefix': prefix, 'items_d_list': item_d_list}
    layout_sections.append(layout_section)
    # ===================================================================
    # ===================================================================
    prefix = '[---- Watches ----] Yellow above 0'
    item_d_list = []

    # add item
    item_d_list.append({
        'item': 'stuck_keys',
        'title': 'stuck_keys',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?device_view=(logging),sort=stuckkeys_human,sortd=fwd',
    })

    item_d_list.append({
        'item': 'high_temperature',
        'title': 'high_temperature',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?sort=temperature,sortd=rev,status=current',
    })

    item_d_list.append({
        'item': 'load_report_high',
        'title': 'Load High',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?device_view=(memory),sort=(stats_load_report),sortd=rev,summary_view=(None)',
    })

    item_d_list.append({
        'item': 'load_report_warning',
        'title': 'Load Warning',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?device_view=(memory),sort=(stats_load_report),sortd=rev,summary_view=(None)',
    })

    item_d_list.append({
        'item': 'mem_free_low',
        'title': 'Memory Free Low Warning',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?device_view=(memory),sort=memfree,sortd=fwd,summary_view=(None)',
    })

    item_d_list.append({
        'item': 'browser_wants_reset',
        'title': 'Browser wants a reset',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?brRsWd=1,status=current,device_view=(details),sort=BrowserRestartWanted,sortd=rev,summary_view=(None)',
    })

    item_d_list.append({
        'item': 'disk_warning',
        'title': 'Disk utilization is elevated',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?device_view=(memory),sort=(disk_use),sortd=rev,summary_view=(None),status=current',
    })

    item_d_list.append({
        'item': 'location_warning',
        'title': 'Location unassigned',
        'format': '{:d}',
        'yellow_above': 0,
        'click_link': '/reports?siteid=(unassigned)',
    })

    # finish this prefix
    layout_section = {'prefix': prefix, 'items_d_list': item_d_list}
    layout_sections.append(layout_section)
    # ===================================================================
    # ===================================================================
    prefix = '[---- Information... ----]'
    item_d_list = []

    # add item
    item_d_list.append({
        'item': 'current_count',
        'title': 'Count Current',
        'format': '{:d}',
        'click_link': '/reports?status=current',
    })

    item_d_list.append({
        'item': 'user_active_count',
        'title': 'User is active count',
        'format': '{:d}',
        'click_link': '/reports?device_view=(details),sort=UserInActive,sortd=fwd,status=current',
    })

    # finish this prefix
    layout_section = {'prefix': prefix, 'items_d_list': item_d_list}
    layout_sections.append(layout_section)
    # ===================================================================

    # ===================================================================
    # ===================================================================
    # ===================================================================
    # Build the result
    the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
    the_data['headers'].append('Category')
    the_data['headers'].append('Current')
    the_data['headers'].append('Min')
    the_data['headers'].append('Max')
    the_data['headers'].append('Link')
    the_data['headers'].append('Plot')
    the_data['headers'].append('Plot all')
    the_data['headers'].append('Notes')

    for layout_section in layout_sections:
        prefix_to_show = layout_section['prefix']
        items_d_list = layout_section['items_d_list']

        # header row for this prefix
        the_data['rows'].append([prefix_to_show, '', '', '', '', ''])
        the_data['links'].append(['', '', '', '', '', ''])
        the_data['color'].append(['', '', '', '', '', ''])
        prefix_to_show = ''

        # the data lines
        for item_d in items_d_list:
            the_count = '-2'
            item = item_d['item']

            if item in the_issues:
                the_count = the_issues[item]

            the_color = s_color_clear

            if 'red_above' in item_d:
                if float(the_count) > float(item_d['red_above']):
                    the_color = s_color_red_warning

            if 'yellow_above' in item_d:
                if float(the_count) > float(item_d['yellow_above']):
                    the_color = s_color_yellow_caution

            if 'green_above' in item_d:
                if float(the_count) > float(item_d['green_above']):
                    the_color = s_color_green_good

            plot_values = get_plot_values_from_raw(raw_content, item, oldest_time=oldest_time)

            value_min_to_display = plot_values['y1_minimum']
            value_max_to_display = plot_values['y1_maximum']
            if 'format' in item_d:
                try:
                    value_to_display = item_d['format'].format(float(the_count))
                except:
                    value_to_display = item_d['format'].format(int(the_count))

                if value_min_to_display:
                    try:
                        value_min_to_display = item_d['format'].format(float(value_min_to_display))
                    except:
                        value_min_to_display = item_d['format'].format(int(value_min_to_display))

                if value_max_to_display:
                    try:
                        value_max_to_display = item_d['format'].format(float(value_max_to_display))
                    except:
                        value_max_to_display = item_d['format'].format(int(value_max_to_display))
            else:
                value_to_display = str(the_count)
                value_min_to_display = str(value_min_to_display)
                value_max_to_display = str(value_max_to_display)

            if 'help' in item_d:
                the_help = item_d['help']
            else:
                the_help = ''

            the_data['rows'].append([
                prefix_to_show + item_d['title'],
                value_to_display,
                value_min_to_display,
                value_max_to_display,
                'click',
                plot_word,
                plot_word_2,
                the_help,
            ])
            the_data['links'].append([
                '',
                '',
                '',
                '',
                '[root_url]' + item_d['click_link'],
                '[root_url]' + '/datamine?view=summary,dashboard_raw=' + item,
                '[root_url]' + '/datamine?view=summaryall,dashboard_raw=' + item,
                '',
            ])
            the_data['color'].append([
                '',
                the_color,
                '',
                '',
                '',
                '',
                '',
                ''])

    # make site specific links
    if siteid:
        for link_index in range(0, len(the_data['links'])):
            new_links = []
            for original_link in the_data['links'][link_index]:
                if original_link:
                    new_links.append(original_link + ',siteid=' + siteid)
                else:
                    new_links.append('')
            the_data['links'][link_index] = copy.deepcopy(new_links)

    # ===================================================================
    # ===================================================================
    # ===================================================================

    return the_data


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    get_content = ''
    siteid = ''
    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'mgmntblock' in query_items:
        siteid = query_items['mgmntblock']
    if 'siteid' in query_items:
        siteid = query_items['siteid']
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':

            the_data = {'headers': [], 'rows': []}
            the_type = ''
            if 'type' in query_items:
                the_type = query_items['type']

            if the_type == 'issues':
                try:
                    the_data_raw = json.loads(open(stash_file_for_site(siteid), 'r').read())
                except:
                    #                    the_issues = reports.get_top_level_issues(siteid)
                    #                    the_data_raw = get_reportables(the_issues, siteid)
                    the_data_raw = {'headers': ['(not ready yet)'], 'rows': [], 'links': [], 'color': []}


                url_to_use = make_home_url_from_environ(environ)
                the_data = json.loads(json.dumps(the_data_raw).replace('[root_url]', url_to_use))

            else:
                # echo it back out, so that we can see it
                for key in query_items.keys():
                    the_data['headers'].append(key)
                    the_data['rows'].append([query_items[key]])

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other

        else:
            # The main page body

            url_to_use = make_home_url_from_environ(environ)
            if siteid:
                load_command = 'loadIntoTable("' + url_to_use + '/dashboard?get=data,type=issues,siteid=' + siteid + '", document.querySelector("table"));'
            else:
                load_command = 'loadIntoTable("' + url_to_use + '/dashboard?get=data,type=issues", document.querySelector("table"));'

            script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 15000);
"""

            # --------------------
            # Build the body
            # --------------------
            body = ''
            body += '<html>'

            body += '<head>'
            body += '<style type="text/css">'
            body += """
table {
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    border-collapse: collapse;
    font-family: 'Quicksand', sans-serif;
    overflow: hidden;
    font-weight: bold;
}

table thead th {
    background: #009578;
    color: #ffffff;
}

table td,
table th {
    padding: 10px 20px;
}

table tbody tr:nth-of-type(even) {
    background: #eeeeee;
}

table tbody tr:last-of-type {
    border-bottom: 2px solid #009578
}

            """

            body += '</style>'
            body += '</head>'

            body += '<body>'

            body += """
    <script>

    function URLjump(jumpLocation) {
        location.href = jumpLocation;
    }


    </script>
            """

            name_to_show = "Home"
            url_to_use = make_home_url_from_environ(environ)
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            extra_string = ''
            if sites_to_drop:
                extra_string = ' (omitting ' + ','.join(sorted(sites_to_drop.keys())) + ')'

            body += '<center>'
            body += 'Slicer Dashboard' + extra_string
            body += '</center>'

            if siteid:
                body += '<center><B>'
                body += 'Filtered on ' + siteid
                body += '</B></center>'

            body += '<center><B>'
            body += '<text id="display_test_data"></text>'
            body += '<br><br>'
            body += '</B></center>'

            body += '<center>'
            body += '<table>'
            body += '<thead></thead>'
            body += '<tbody></tbody>'
            body += '</table>'

            body += '</center>'

            # load table content from js data
            # https://www.youtube.com/watch?v=qBg8IB3u28s
            # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
            body += """
<script>

document.getElementById("display_test_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_test_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";


        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

        document.body.style.backgroundColor = "rgba(0,0,0,0)";
    } catch (error) {
        document.getElementById("display_test_data").innerText = "Data may not be ready yet; fetch error on " + url + "<br>" + error;
        document.body.style.backgroundColor = "rgba(255,0,0,0.3)";
    }
};

""" + script_fetch_content + """

</script>
        """

            body += '</body>\n' \
                    '</html>\n'

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'dashboard GET exception: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    try:
        if environ['REQUEST_METHOD'] == 'POST':
            body, other = make_body_POST(environ)
        elif environ['REQUEST_METHOD'] == 'GET':
            body, other = make_body_GET(environ)
        permissions.log_page_allowed(environ, service, other)
    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    if True:
        pass
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n' \
                    '<body>\n'
        html += body

        if other['add_wrapper']:
            html += '</body>\n' \
                    '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_dashboard(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_reportables_stats(self):
        the_issues = {'disk_warning': 15, 'recent_service_pack_percent': 70.2127659574468, 'inspect_comms': 0,
                      'outages': 1, 'browser_wants_reset': 30, 'total_count': 437, 'disk_high': 0,
                      'location_warning': 4, 'on_iot_percent': 1.21580547112462, 'time_now': 1664388826.969895,
                      'new_image_percent': 100.0, 'service_pack_key_recent': 'SP.026', 'on_corp_count': 240,
                      'mem_free_low': 0, 'current_count': 329, 'load_report_warning': 12, 'load_report_high': 2}

        date_time = datetime.datetime.fromtimestamp(the_issues['time_now'])
        year = date_time.year
        self.assertEqual(2022, year)
        month = date_time.month
        self.assertEqual(9, month)
        day = date_time.day
        self.assertEqual(28, day)

        os.environ["TZ"] = "UTC"

        # day (for making a folder)
        expected = datetime.datetime(2022, 9, 28, 0, 0).strftime('%s')
        actual = calculate_just_the_day_time_from_epoch_time(the_issues['time_now'])
        self.assertEqual(expected, actual)

        expected = datetime.datetime(2022, 9, 29, 0, 0).strftime('%s')
        actual = calculate_just_the_day_time_from_epoch_time(the_issues['time_now'] + 24 * 60 * 60)
        self.assertEqual(expected, actual)

        # hour (for making a file)
        expected = datetime.datetime(2022, 9, 28, 18, 0).strftime('%s')
        #        expected = '1664388000'
        actual = calculate_just_the_hour_time_from_epoch_time(the_issues['time_now'])
        self.assertEqual(expected, actual)

        test_dashboard_raw_root = '/mnt/disks/' + 'SSD/var/log/slicer/dashboard/raw/'
        expected = '/mnt/disks/' + 'SSD/var/log/slicer/dashboard/raw/1664323200/1664388000'
        output_file = test_dashboard_raw_root + calculate_just_the_day_time_from_epoch_time(
            the_issues['time_now']) + '/' + calculate_just_the_hour_time_from_epoch_time(the_issues['time_now'])
        self.assertEqual(expected, output_file)

    def test_year_month_day_dot_hour_from_timestamp(self):
        timestamp = '1664388000'
        expected = '20220928.18'
        actual = year_month_day_dot_hour_from_timestamp(timestamp)
        self.assertEqual(expected, actual)

    def test_year_month_day_dot_hour_from_timestamp_with_site(self):
        timestamp = '1664388000_'
        expected = '20220928.18'
        actual = year_month_day_dot_hour_from_timestamp(timestamp)
        self.assertEqual(expected, actual)

    def test_get_all_raw_data(self):
        raw_content = {}
        raw_content['1664388000'] = {"disk_warning": 14, "recent_service_pack_percent": 69.84615384615384,
                                     "inspect_comms": 1, "outages": 1, "browser_wants_reset": 29, "total_count": 437,
                                     "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2307692307692308,
                                     "time_now": 1664413166.626883, "new_image_percent": 100.0,
                                     "service_pack_key_recent": "SP.026", "on_corp_count": 235, "mem_free_low": 0,
                                     "current_count": 325, "load_report_warning": 11, "load_report_high": 4}
        raw_content['1664391600'] = {"disk_warning": 15, "recent_service_pack_percent": 70.1219512195122,
                                     "inspect_comms": 0, "outages": 1, "browser_wants_reset": 26, "total_count": 437,
                                     "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2195121951219512,
                                     "time_now": 1664467169.467556, "new_image_percent": 100.0,
                                     "service_pack_key_recent": "SP.026", "on_corp_count": 238, "mem_free_low": 0,
                                     "current_count": 328, "load_report_warning": 15, "load_report_high": 2}
        raw_content['1664395200'] = {"disk_warning": 16, "recent_service_pack_percent": 70.1219512195122,
                                     "inspect_comms": 0, "outages": 1, "browser_wants_reset": 26, "total_count": 437,
                                     "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2195121951219512,
                                     "time_now": 1664467169.467556, "new_image_percent": 100.0,
                                     "service_pack_key_recent": "SP.026", "on_corp_count": 238, "mem_free_low": 0,
                                     "current_count": 328, "load_report_warning": 15, "load_report_high": 2}

        expected_x_values = ['20220928.18', '20220928.19', '20220928.20']
        expected_y1_values = ['14', '15', '16']
        expected_y2_values = ['14', '15', '16']
        expected_y3_values = ['14', '15', '16']
        expected_y1_minimum = 14
        expected_y1_maximum = 16

        plot_values = get_plot_values_from_raw(raw_content, 'disk_warning')

        self.assertEqual(expected_x_values, plot_values['x_values'])
        self.assertEqual(expected_y1_values, plot_values['y1_values'])
        self.assertEqual(expected_y2_values, plot_values['y2_values'])
        self.assertEqual(expected_y3_values, plot_values['y3_values'])
        self.assertEqual(expected_y1_minimum, plot_values['y1_minimum'])
        self.assertEqual(expected_y1_maximum, plot_values['y1_maximum'])

        # test a time restriction
        expected_x_values = ['20220928.19', '20220928.20']
        expected_y1_values = ['15', '16']
        expected_y2_values = ['15', '16']
        expected_y3_values = ['15', '16']
        expected_y1_minimum = 15
        expected_y1_maximum = 16

        plot_values = get_plot_values_from_raw(raw_content, 'disk_warning', oldest_time='1664391599')

        self.assertEqual(expected_x_values, plot_values['x_values'])
        self.assertEqual(expected_y1_values, plot_values['y1_values'])
        self.assertEqual(expected_y2_values, plot_values['y2_values'])
        self.assertEqual(expected_y3_values, plot_values['y3_values'])
        self.assertEqual(expected_y1_minimum, plot_values['y1_minimum'])
        self.assertEqual(expected_y1_maximum, plot_values['y1_maximum'])

        raw_content = {}
        expected_x_values = []
        expected_y1_values = []
        expected_y2_values = []
        expected_y3_values = []
        expected_y1_minimum = None
        expected_y1_maximum = None

        plot_values = get_plot_values_from_raw(raw_content, 'disk_warning', oldest_time='1664391599')

        self.assertEqual(expected_x_values, plot_values['x_values'])
        self.assertEqual(expected_y1_values, plot_values['y1_values'])
        self.assertEqual(expected_y2_values, plot_values['y2_values'])
        self.assertEqual(expected_y3_values, plot_values['y3_values'])
        self.assertEqual(expected_y1_minimum, plot_values['y1_minimum'])
        self.assertEqual(expected_y1_maximum, plot_values['y1_maximum'])

    def test_get_all_raw_data_With_site_all(self):
        raw_content = {}
        raw_content['1664388000_'] = {"disk_warning": 14, "recent_service_pack_percent": 69.84615384615384,
                                      "inspect_comms": 1, "outages": 1, "browser_wants_reset": 29, "total_count": 437,
                                      "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2307692307692308,
                                      "time_now": 1664413166.626883, "new_image_percent": 100.0,
                                      "service_pack_key_recent": "SP.026", "on_corp_count": 235, "mem_free_low": 0,
                                      "current_count": 325, "load_report_warning": 11, "load_report_high": 4}
        raw_content['1664391600_'] = {"disk_warning": 15, "recent_service_pack_percent": 70.1219512195122,
                                      "inspect_comms": 0, "outages": 1, "browser_wants_reset": 26, "total_count": 437,
                                      "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2195121951219512,
                                      "time_now": 1664467169.467556, "new_image_percent": 100.0,
                                      "service_pack_key_recent": "SP.026", "on_corp_count": 238, "mem_free_low": 0,
                                      "current_count": 328, "load_report_warning": 15, "load_report_high": 2}
        raw_content['1664395200_'] = {"disk_warning": 16, "recent_service_pack_percent": 70.1219512195122,
                                      "inspect_comms": 0, "outages": 1, "browser_wants_reset": 26, "total_count": 437,
                                      "disk_high": 0, "location_warning": 4, "on_iot_percent": 1.2195121951219512,
                                      "time_now": 1664467169.467556, "new_image_percent": 100.0,
                                      "service_pack_key_recent": "SP.026", "on_corp_count": 238, "mem_free_low": 0,
                                      "current_count": 328, "load_report_warning": 15, "load_report_high": 2}

        expected_x_values = ['20220928.18', '20220928.19', '20220928.20']
        expected_y1_values = ['14', '15', '16']
        expected_y2_values = ['14', '15', '16']
        expected_y3_values = ['14', '15', '16']
        expected_y1_minimum = 14
        expected_y1_maximum = 16

        plot_values = get_plot_values_from_raw(raw_content, 'disk_warning')

        if True:
            self.assertEqual(expected_x_values, plot_values['x_values'])
            self.assertEqual(expected_y1_values, plot_values['y1_values'])
            self.assertEqual(expected_y2_values, plot_values['y2_values'])
            self.assertEqual(expected_y3_values, plot_values['y3_values'])
            self.assertEqual(expected_y1_minimum, plot_values['y1_minimum'])
            self.assertEqual(expected_y1_maximum, plot_values['y1_maximum'])

# End of source file
