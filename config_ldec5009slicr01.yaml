# Copyright 2018 Cardinal Health. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

### Change Log Start ###
# Additional information on how to use the when creating an instance can be found here:
# https://wiki.cardinalhealth.net/GCP_Create_VM_Instance
#2018-04-30 kristopher.leist    Reusable template, please copy to your project and edit accordingly.
#
###

### Example gcloud command for deployment.   ###
# gcloud deployment-manager deployments create iamds-dev --config iamds-dev-vm.yaml --project iam-np-cah
###


### Resource 1 ####
resources:
- name: ldec5009slicr01
  type: management-cah/composite:cah-vm
  properties:
    name: ldec5009slicr01
    projectId: mac-mgmt-np-cah
    serviceAccount: pi-mgmt-np-slicer-master
    machineType: n1-standard-1
    sourceImage: cah-centos-7
    zone: us-central1-c
    environment: nonprod
    lineOfBusiness: corp
    internetfacing: FALSE
    tags:
    - internet-us-central1
    - int-webserver
    labels:
      whobuilt: davidferguson
    metadata:
      adgroups: A-MacSysAdmins
      #startupScript:
    disks:
    - name: ldec5009slicr01-d
      sizeGb: 40
      diskType: pd-ssd
      autoDelete: False


#scopes:                                                 #default scopes will be applied.  https://cloud.google.com/sdk/gcloud/reference/beta/compute/instances/set-scopes
#-                                                       #additional scopes NOT currently supported.
#####################################################
### end of first vm                               ###