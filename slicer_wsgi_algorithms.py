service = "algorithms"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/algorithms.py

using:
sudo vi /var/www/html/algorithms.py

sudo chown apache:apache /var/www/html/algorithms.py

sudo systemctl restart httpd


"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_algorithms


"""

import json
import time
import unittest

startup_exceptions = ''

try:
    pass
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ====================================
def build_versions_summary(file_contents_d):
    # ====================================
    return_value = {}

    for file_name in file_contents_d:
        try:
            file_content_d = json.loads(file_contents_d[file_name])
            # self reporting
            if 'source' in file_content_d:
                source = 'pi_' + file_content_d['source']
                version = file_content_d['version']
                return_value[source] = version
        except:
            pass

    for file_name in file_contents_d:
        if 'runner' in file_contents_d:
            try:
                file_content_d = json.loads(file_contents_d[file_name])

                # runner reports for other services
                for item in file_content_d:
                    if 'service:' in item:
                        source = item.replace('service:', '')
                        version = file_content_d[item]
                        return_value[source] = version
            except:
                pass

    if 'runner' in file_contents_d:
        runner_d = json.loads(file_contents_d['runner'])
        for source in return_value:
            key = 'run_status:' + source
            if key in runner_d:
                if 'fail' == runner_d[key]:
                    return_value[source] = return_value[source] + ' *'
    else:
        # look for runner not even reporting, when other certain image 2.x.x services are reporting
        if ('logging' in file_contents_d) or ('bluetooth' in file_contents_d) or ('security' in file_contents_d):
            return_value['pi_runner'] = ' *'

    return return_value

# ====================================
def allowed_by_versions(versions_required=[], versions_available={}):
    # ====================================
    """
Compare the given versions to the required versions,
    and return True if we have the versions we need.

    requirements are a list of dictionary sets.
    within a set is an OR requirement.
    each list item becomes and AND requirement
    """

    expanded_versions_required = []
    if versions_required:
        for required in versions_required:
            key_list = list(required.keys())  # should be exactly one key
#            print ('print_versions_required_start', versions_required, required, required.keys(), key_list, 'print_versions_required_end')
            name = key_list[0]
            item = required[name]

            sub_items = []
            for sub_item in item.split('.'):
                sub_items.append(sub_item.zfill(10))
            new_item = '.'.join(sub_items)
            expanded_versions_required.append({name:new_item})

    expanded_versions_available = {}
    if versions_available:
        for key in versions_available.keys():
            item = versions_available[key]
            sub_items = []
            for sub_item in item.split('.'):
                sub_items.append(sub_item.zfill(10))
            new_item = '.'.join(sub_items)
            expanded_versions_available[key] = new_item

    print ('expanded_versions_required', expanded_versions_required)
    print ('expanded_versions_available', expanded_versions_available)

    if expanded_versions_required:
        return_value = True

        for required_or_set in expanded_versions_required:
            or_result = False
            for required_item in required_or_set:
                if required_item in expanded_versions_available:
                    if expanded_versions_available[required_item] >= required_or_set[required_item]:
                        or_result = True
            # AND it with the overall
            return_value = return_value and or_result
    else:
        return_value = True

    return return_value


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_version_compares_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(allowed_by_versions(), True)

    def test_version_compares_one_sided_available(self):
        """
        (fill in here)
        """
        self.assertEqual(allowed_by_versions([], {'image': '2.0'}), True)

    def test_version_compares_one_sided_required(self):
        """
        (fill in here)
        """
        self.assertEqual(allowed_by_versions([{'image': '2.0'}], {}), False)

    def test_version_compares_balanced(self):
        """
        (fill in here)
        """
        self.assertEqual(allowed_by_versions([{'image': '2.0'}], {'image': '2.0'}), True)
        self.assertEqual(allowed_by_versions([{'image': '2.0.1'}], {'image': '2.0.0'}), False)
        self.assertEqual(allowed_by_versions([{'image': '2.0.1'}], {'image': '2.0.1'}), True)

    def test_version_compares_misc_1(self):
        """
        (fill in here)
        """
        versions_required = [{'image': '2.0.0', 'pi_runner': 'R.2.0'}]
        versions_available = {'image': '2.0.1', 'pi_hmi': 'H.2.0'}
        expected = True
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

    def test_version_compares_misc_2(self):
        """
        (fill in here)
        """
        versions_required = [{'image': '2.0.0', 'pi_runner': 'R.2.0'}]
        versions_available = {'image': '2.0.1'}
        expected = True
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

    def test_version_compares_misc_3(self):
        """
        (fill in here)
        """
        versions_required = [{'image': '2.0.0', 'pi_runner': 'R.2.0'}]
        versions_available = {'image': '1.9.0', 'pi_runner': 'R.2.0'}
        expected = False
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

    def test_version_compares_misc_4(self):
        """
        (fill in here)
        """
        versions_required = [{'image': '2.0.0', 'pi_runner': 'R.2.1'}]
        versions_available = {'image': '1.9.0', 'pi_runner': 'R.2.0'}
        expected = False
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)


    def test_version_compares_List_AND(self):
        """
        (fill in here)
        """
        self.assertEqual(allowed_by_versions([{'image': '2.0.0'}, {'pi_runner': 'R.2.0'}], {'image': '2.0.1'}), False)
        self.assertEqual(
            allowed_by_versions([{'image': '2.0.0'}, {'pi_runner': 'R.2.0'}], {'image': '2.0.1', 'pi_runner': 'R.2.0'}),
            True)
        self.assertEqual(
            allowed_by_versions([{'image': '2.0.0'}, {'pi_runner': 'R.2.0'}], {'image': '2.0.1', 'pi_runner': 'R.1.9'}),
            False)

    def test_version_summary(self):
        """
        (fill in here)
        """
        expected = {'pi_monitor': 'M.1.2'}
        actual = build_versions_summary({
            'monitor': '{    "uptime":"259752",    "runner_version":"missing",    "address":"**************",    "calc_counts":4290,    "source":"monitor",    "version":"M.1.2",    "time":1651761608.430327,    "monitor_version":"M.1.2",    "id":"1000000040d54f48",    "runner_status":"stale"}'})
        self.assertEqual(actual, expected)

        expected = {'pi_monitor': 'M.1.2', 'pi_runner': 'R.6.4', 'pi_hmi': 'H.4.1'}
        actual = build_versions_summary({
            'monitor': '{    "uptime":"259752",    "runner_version":"missing",    "address":"**************",    "calc_counts":4290,    "source":"monitor",    "version":"M.1.2",    "time":1651761608.430327,    "monitor_version":"M.1.2",    "id":"1000000040d54f48",    "runner_status":"stale"}',
            'runner': '{"s_logging_net_rx_errors":"0","s_bluetooth_selected":"1","kernel":"5.10.63-v7l+","s_logging_wifi_noise":"","run_status:pi_monitor":"ok","image":"2.4.1","responsexce":"0","run_status:pi_config":"ok","s_bluetooth_connected":"0","Memory:MemAvailable":"3378094080","disk_use":"100","inode_use":"33","service:pi_runner":"R.6.4","service:pi_hmi":"H.4.1"}'})
        self.assertEqual(actual, expected)

        expected = {'pi_monitor': 'M.1.2', 'pi_runner': 'R.6.4', 'pi_hmi': 'H.4.1 *'}
        actual = build_versions_summary({
            'monitor': '{    "uptime":"259752",    "runner_version":"missing",    "address":"**************",    "calc_counts":4290,    "source":"monitor",    "version":"M.1.2",    "time":1651761608.430327,    "monitor_version":"M.1.2",    "id":"1000000040d54f48",    "runner_status":"stale"}',
            'runner': '{"run_status:pi_hmi":"fail","s_logging_net_rx_errors":"0","s_bluetooth_selected":"1","kernel":"5.10.63-v7l+","s_logging_wifi_noise":"","run_status:pi_monitor":"ok","image":"2.4.1","responsexce":"0","run_status:pi_config":"ok","s_bluetooth_connected":"0","Memory:MemAvailable":"3378094080","disk_use":"100","inode_use":"33","service:pi_runner":"R.6.4","service:pi_hmi":"H.4.1"}'})
        self.assertEqual(actual, expected)

        expected = {'pi_logging': 'L.1.2', 'pi_runner': ' *'}
        actual = build_versions_summary({'logging': '{   "source":"logging",    "version":"L.1.2"}'})
        self.assertEqual(actual, expected)

        expected = {'pi_bluetooth': 'B.1.2', 'pi_runner': ' *'}
        actual = build_versions_summary({'bluetooth': '{   "source":"bluetooth",    "version":"B.1.2"}'})
        self.assertEqual(actual, expected)

        expected = {'pi_security': 'S.1.2', 'pi_runner': ' *'}
        actual = build_versions_summary({'security': '{   "source":"security",    "version":"S.1.2"}'})
        self.assertEqual(actual, expected)

    def test_allowed_by_versions(self):
        versions_required = [{'pi_runner': 'R.9.8'}]
        versions_available = {'pi_runner': 'R.9.8'}
        expected = True
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

        versions_required = [{'pi_runner': 'R.9.8'}]
        versions_available = {'pi_runner': 'R.9.10'}
        expected = True
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

        versions_required = [{'pi_runner': 'R.19.8'}]
        versions_available = {'pi_runner': 'R.9.10'}
        expected = False
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)

        versions_required = [{'pi_runner': 'R.19.8'}]
        versions_available = {'pi_runner': 'R.19.10'}
        expected = True
        actual = allowed_by_versions(versions_required, versions_available)
        self.assertEqual(actual, expected)






    # End of file