# A receiver for monitor check-ins
# Do not add to this. We want it clean, simple, stable, and working at all times.
# pi_monitor calls in here.


service = 'checkin'
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/checkin.py

using:
sudo mkdir /var/log/slicer
sudo chown -R apache:apache /var/log/slicer

sudo vi /var/www/html/checkin.py

It also requires:

sudo vi /etc/httpd/conf.d/python-checkin.conf
----- start copy -----
WSGIScriptAlias /checkin /var/www/html/checkin.py
----- end copy -----

sudo chown apache:apache /var/www/html/checkin.py

sudo systemctl restart httpd


##############
# notes
##############

The url to hit it is https://slicer.cardinalhealth.net/checkin

It then saves the get request query content to:
/var/log/slicer/checkin/

The raw received data is organized as YYYYMMDD/HH/ in:
/var/log/slicer/checkin/raw/YYYYMMDD/HH/<timestamp>-<id>.txt

The recording of each checkin json of the query in:
/var/log/slicer/checkin/json/id/<id>


"""

import datetime
import json
import os
import shutil
import sys
import time
import traceback
import unittest

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import algorithms
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_raw_path = service_config['base_raw_path']
    # save the monitor checkin as though it also called datadrop (so that reporting finds it by id)
    datadrop_save_path = service_config['datadrop_save_path']
    checkin_file_root = service_config['checkin_file_root']
    days_to_keep = service_config['days_to_keep']
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = output_file + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ====================================
def cleanup_old_raw():
    # ====================================
    # only keep the last 2 weeks worth (plus one day, to allow for the current day)
    days_present = sorted(os.listdir(base_raw_path))

    if len(days_present) > days_to_keep:
        for day_to_remove in days_present[:-1 * days_to_keep]:
            try:
                shutil.rmtree(base_raw_path + day_to_remove)
            except:
                pass


# ====================================
def application(environ, start_response):
    # ====================================
    notes = ''

    try:
        cleanup_old_raw()
    except:
        pass

    try:
        # query = 'pi-m-10000000a499ee0d-10.216.210.124-1908-M.0.9-missing-stale'
        query = environ['QUERY_STRING']
        if 'pi-m-' in query:
            TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
            # 20210406201829131704
            notes += TS

            checkin = {}
            checkin['time'] = time.time()
            checkin['calc_counts'] = 1

            splits = query.split('-')

            if len(splits) > 2:
                checkin['id'] = splits[2]

                if len(splits) > 3:
                    checkin['address'] = splits[3]

                if len(splits) > 4:
                    checkin['uptime'] = splits[4]

                if len(splits) > 5:
                    checkin['monitor_version'] = splits[5]

                if len(splits) > 6:
                    checkin['runner_version'] = splits[6]

                if len(splits) > 7:
                    checkin['runner_status'] = splits[7]

                # I want to clean up based on time of arrival, so store based on time
                output_file = base_raw_path + TS[0:8] + '/' + TS[8:10] + '/' + TS + "_" + checkin['id'] + '.txt'
                notes += '<br>' + output_file

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                notes += '<br>' + 'save'
                do_atomic_write(output_file, query)
                _ = """
                with open(output_file, 'w') as f:
                    f.write(query)
                """
                notes += '<br>' + 'done'

                checkin_file = checkin_file_root + checkin['id']

                if not os.path.exists(os.path.dirname(checkin_file)):
                    os.makedirs(os.path.dirname(checkin_file))

                try:
                    with open(checkin_file, 'r') as f:
                        previous_checkin = json.loads(f.read())
                        if 'calc_counts' in previous_checkin:
                            checkin['calc_counts'] = int(previous_checkin['calc_counts']) + 1
                except:
                    pass

                do_atomic_write(checkin_file, json.dumps(checkin))
                _ = """
                with open(checkin_file, 'w') as f:
                    f.write(json.dumps(checkin))
                """

                datadrop_file = datadrop_save_path + checkin['id'] + '/' + 'monitor'
                # make it match all the other reporting
                checkin['source'] = 'monitor'
                checkin['version'] = '(missing)'
                try:
                    checkin['version'] = checkin['monitor_version']
                except:
                    pass

                if not os.path.exists(os.path.dirname(datadrop_file)):
                    os.makedirs(os.path.dirname(datadrop_file))
                try:
                    do_atomic_write(datadrop_file, json.dumps(checkin))
                    _ = """
                    with open(datadrop_file, 'w') as f:
                        f.write(json.dumps(checkin))
                    """
                except:
                    pass

                all_files_content = {}
                for file in os.listdir(datadrop_save_path + checkin['id'] + '/'):
                    try:
                        content = open(datadrop_save_path + checkin['id'] + '/' + file, 'r').read()
                        all_files_content[file] = content
                    except:
                        pass
                service_versions = algorithms.build_versions_summary(all_files_content)
                try:
                    do_atomic_write(datadrop_save_path + checkin['id'] + '/' + 'service_versions',
                                    json.dumps(service_versions))
                    _ = """
                    open(datadrop_save_path + checkin['id'] + '/' + 'service_versions', 'w').write(json.dumps(service_versions))
                    """
                except:
                    pass


    except:
        pass

    status = '200 OK'
    html = '<html>\n' \
           '<body>\n' \
           '<div style="width: 100%; font-size: 12px; font-weight: bold; text-align: center;">\n'

    html += str(sys.version_info) + '<br><br>'
    html += str(environ['QUERY_STRING']) + '<br>'
    html += str(notes) + '<br>'

    html += '</div>\n' \
            '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
