In order to migrate run data from one server to a new one:

Can be done anytime:

On the old server, get a snapshot of the datastore, and check it into GIT, then load to
    the new server (to populate all the profiles, so that the multimedia loads can happen)

Get the device media content from the multimedia page
    (download to ??? maybe one_drive, and then upload to the new server)

Last step:
- On the old server, get a snapshot of the datastore, and check it into GIT, then load to
    the new server


