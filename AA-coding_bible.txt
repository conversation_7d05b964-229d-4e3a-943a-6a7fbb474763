Coding Bible

=====================
Code Commandments
=====================
Be mindful of your fellow developers and make your code READABLE. Writing clever code hurts productivity of your team. Don't be that person.
If you don't know the answer, Ask.
Before asking a question, make sure you already did your research.
Always write your tests. It has saved a million lives already.

=====================
Version Control Commandments
=====================
Are you doing something awesome with the code repository? Branch out.
Take your efforts wisely. Always commit changes to GitHub.
Ready to ship that fantastic feature? Hit that Pull Request and follow the message format.
We remove our spoiled food from our refrigerator, right? So does stale branches. Delete those.
Pull Request Message Format
<type>(<optional scope>): subject
Example:
feat: Add user avatar upload image
chore(deps): update packages


Type        Description
-----       -----------
build       Build related changes (eg: npm related/ adding external dependencies).
chore       A code change that the external user won't see (eg: change to .gitignore file or .prettierrc file).
feat        A new feature.
fix         A bug fix.
docs        Documentation-related changes.
refactor    A code that neither fixes a bug nor adds a feature. (eg: You can use this when there are semantic changes like renaming a variable/ function name).
perf        A code that improves performance.
style       A code that is related to styling.
test        Adding new test or making changes to existing test.



