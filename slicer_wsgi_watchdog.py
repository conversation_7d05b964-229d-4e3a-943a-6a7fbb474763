# A watchdog for slicer page services

service = "watchdog"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/watchdog.py

using:
sudo vi /var/www/html/watchdog.py

It also requires:

sudo vi /etc/httpd/conf.d/python-watchdog.conf
----- start copy -----
WSGIScriptAlias /watchdog /var/www/html/watchdog.py
----- end copy -----

sudo chown apache:apache /var/www/html/watchdog.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/watchdog-runner
sudo chmod +x /var/www/html/watchdog-runner

# ===== begin: start file
#!/usr/bin/env python
import watchdog
watchdog.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/watchdog-runner.service
sudo systemctl daemon-reload
sudo systemctl stop watchdog-runner.service
sudo systemctl start watchdog-runner.service
sudo systemctl enable watchdog-runner.service

systemctl status watchdog-runner.service

sudo systemctl restart watchdog-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep watchdog-runner

OR

tail -f /var/log/syslog | fgrep watchdog-runner

Rocky9:

sudo tail -f /var/log/messages | fgrep watchdog-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/watchdog-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import watchdog; print(watchdog.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/watchdog

https://slicer.cardinalhealth.net/watchdog?siteid=PR005

https://slicer.cardinalhealth.net/watchdog?serial=100000002a5da842

https://slicer.cardinalhealth.net/watchdog?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_watchdog


"""

_look_for_items = """

uname -a
04 Linux lpec5009slicr04 5.14.0-427.18.1.el9_4.x86_64 #1 SMP PREEMPT_DYNAMIC Mon May 27 16:35:12 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
05 Linux lpec5009slicr05 5.14.0-427.18.1.el9_4.x86_64 #1 SMP PREEMPT_DYNAMIC Mon May 27 16:35:12 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux

version of other packages?


logged in user history:
last --help
last -a -d -w -x


"""

_setroubleshootd = """

https://unix.stackexchange.com/questions/509511/setroubleshootd-excessive-cpu-and-memory-usage

https://discussion.fedoraproject.org/t/flatpak-update-to-1-10-cause-setroubleshootd-take-lots-of-cpu-usage/74295/5

sudo tail -n 1000 -f /var/log/audit/audit.log | fgrep denied


Work on automation, to report on these logs:
ref: https://stackoverflow.com/questions/60655818/read-from-file-while-it-is-being-written-to-in-python

sudo python3
import time, os
import json
filename = '/var/log/audit/audit.log'

accumulators = {'open_paths':{}, 'actions':{}, 'action_last':{}, 'total':0}
file = open(filename, 'r', encoding = 'utf-8')
#Find the size of the file and move to the end
st_results = os.stat(filename)
st_size = st_results[6]
file.seek(st_size)
while 1:
    where = file.tell()
    line = file.readline()
    if not line:
        time.sleep(1)
#        file.seek(where)
    else:
        updated_something = False
        if 'denied' in line:
            accumulators['total'] += 1
#            print(chr(27) + "[2J") # clear screen
            parsed = parse_audit_line(line)
            the_action = parsed['action']
            if not the_action in accumulators['actions']:
                accumulators['actions'][the_action] = 0
                accumulators['action_last'][the_action] = ''
            accumulators['actions'][the_action] += 1
            accumulators['action_last'][the_action] = line
            updated_something = True
            if the_action == 'open':
                path_found = parsed['path']
                if not path_found in accumulators['open_paths']:
                    accumulators['open_paths'][path_found] = 0
                accumulators['open_paths'][path_found] += 1
                updated_something = True
            print(accumulators)
            if updated_something:
                try:
                    with open ('/dev/shm/test.txt', 'w') as f:
                        f.write(json.dumps(accumulators, indent=4, separators=(',',':')))
                except:
                    pass


Another terminal session:
sudo watch -n 1 cat /dev/shm/test.txt


{
    "open_paths":{
        "/var/log/slicer/datastore/override_permission_watchdog_read":23,
        "/var/log/slicer/rings/status_summary":19,
        "/mnt/disks/SSD/var/log/slicer/tasks/status_summary":24,
        "/var/log/slicer/tasks/task_status":14,
        "/var/log/slicer/datastore/override_permission_reports_read":2
    }
}



{
    "open_paths":{
        "/mnt/disks/SSD/var/log/slicer/tasks/status_summary":36,
        "/var/log/slicer/rings/status_summary":34,
        "/var/log/slicer/datastore/override_permission_watchdog_read":28,
        "/var/log/slicer/tasks/task_status":30
    },
    "actions":{
        "write":106,
        "open":128,
        "getattr":36,
        "ioctl":75,
        "read":74,
        "name_connect":73
    },
    "action_last":{
        "write":"type=AVC msg=audit(1717600264.202:207161): avc:  denied  { write } for  pid=6882 comm=\"httpd\" name=\"status_summary\" dev=\"sda2\" ino=100676374 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "open":"type=AVC msg=audit(1717600272.900:207166): avc:  denied  { open } for  pid=3391 comm=\"httpd\" path=\"/var/log/slicer/datastore/override_permission_watchdog_read\" dev=\"sda2\" ino=17189236 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "getattr":"type=AVC msg=audit(1717600243.119:207092): avc:  denied  { getattr } for  pid=3391 comm=\"httpd\" path=\"/mnt/disks/SSD/var/log/slicer/tasks/status_summary\" dev=\"sdb\" ino=4980766 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:unlabeled_t:s0 tclass=file permissive=1\n",
        "ioctl":"type=AVC msg=audit(1717600272.900:207165): avc:  denied  { ioctl } for  pid=3391 comm=\"httpd\" path=\"/dev/shm/permissions_live.txt\" dev=\"tmpfs\" ino=44 ioctlcmd=0x5401 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:tmpfs_t:s0 tclass=file permissive=1\n",
        "read":"type=AVC msg=audit(1717600272.900:207166): avc:  denied  { read } for  pid=3391 comm=\"httpd\" name=\"override_permission_watchdog_read\" dev=\"sda2\" ino=17189236 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "name_connect":"type=AVC msg=audit(1717600284.777:207173): avc:  denied  { name_connect } for  pid=3390 comm=\"httpd\" dest=443 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:http_port_t:s0 tclass=tcp_socket permissive=1\n"
    },
    "total":492
}




{
    "open_paths":{
        "/mnt/disks/SSD/var/log/slicer/tasks/status_summary":331,
        "/var/log/slicer/rings/status_summary":294,
        "/var/log/slicer/datastore/override_permission_watchdog_read":205,
        "/var/log/slicer/tasks/task_status":235,
        "/var/log/slicer/datastore/override_permission_reports_read":3
    },
    "actions":{
        "write":930,
        "open":1068,
        "getattr":331,
        "ioctl":692,
        "read":674,
        "name_connect":596,
        "read open":1,
        "write open":1
    },
    "action_last":{
        "write":"type=AVC msg=audit(1717618120.490:220248): avc:  denied  { write } for  pid=3397 comm=\"httpd\" name=\"status_summary\" dev=\"sda2\" ino=100676374 scontext=system_u:system_r:httpd_t:s0 tcontext=syst
em_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "open":"type=AVC msg=audit(1717618100.690:220225): avc:  denied  { open } for  pid=3396 comm=\"httpd\" path=\"/var/log/slicer/datastore/override_permission_watchdog_read\" dev=\"sda2\" ino=17189236 scontext=
system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "getattr":"type=AVC msg=audit(1717618064.474:220166): avc:  denied  { getattr } for  pid=3391 comm=\"httpd\" path=\"/mnt/disks/SSD/var/log/slicer/tasks/status_summary\" dev=\"sdb\" ino=4980766 scontext=syste
m_u:system_r:httpd_t:s0 tcontext=system_u:object_r:unlabeled_t:s0 tclass=file permissive=1\n",
        "ioctl":"type=AVC msg=audit(1717618100.689:220224): avc:  denied  { ioctl } for  pid=3396 comm=\"httpd\" path=\"/dev/shm/permissions_live.txt\" dev=\"tmpfs\" ino=44 ioctlcmd=0x5401 scontext=system_u:system_r
:httpd_t:s0 tcontext=system_u:object_r:tmpfs_t:s0 tclass=file permissive=1\n",
        "read":"type=AVC msg=audit(1717618120.489:220247): avc:  denied  { read } for  pid=3397 comm=\"httpd\" name=\"files\" dev=\"sdb\" ino=4980748 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:
unlabeled_t:s0 tclass=dir permissive=1\n",
        "name_connect":"type=AVC msg=audit(1717618105.129:220227): avc:  denied  { name_connect } for  pid=3390 comm=\"httpd\" dest=9999 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:jboss_managem
ent_port_t:s0 tclass=tcp_socket permissive=1\n",
        "read open":"type=AVC msg=audit(1717604415.219:210302): avc:  denied  { read open } for  pid=3396 comm=\"httpd\" path=\"/var/log/slicer/datastore/override_permission_watchdog_read\" dev=\"sda2\" ino=17189236
 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n",
        "write open":"type=AVC msg=audit(**********.509:216235): avc:  denied  { write open } for  pid=3396 comm=\"httpd\" path=\"/var/log/slicer/tasks/task_status\" dev=\"sda2\" ino=15158 scontext=system_u:system_r
:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n"
    },
    "total":4293
}





"""

import copy
import datetime
import traceback
import json
import os

try:
    import requests
except:
    pass  # for unittest
import shlex
import subprocess
import sys
import time
import unittest

s_max_log_length = 1000
s_seconds_holdoff = 25 * 60

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

s_log_root = ''
if 'log_root' in service_config:
    s_log_root = service_config['log_root']

try:  # for unittest to work
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ====================================
def convert_seconds_to_human(time_in_seconds):
    # ====================================
    the_report = ''

    time_to_use = int(time_in_seconds)

    if time_to_use < 0:
        the_report += '-'
        time_to_use = abs(time_to_use)

    days = int(time_to_use / (3600 * 24))
    time_to_use -= days * 3600 * 24

    hours = int(time_to_use / 3600)
    time_to_use -= hours * 3600

    minutes = int(time_to_use / 60)
    time_to_use -= minutes * 60

    seconds = time_to_use % 60

    if days > 0:
        if the_report:
            the_report += ' '
        the_report += str(days) + 'd'

    if hours > 0:
        if the_report:
            the_report += ' '
        the_report += str(hours) + 'h'

    if minutes > 0:
        if the_report:
            the_report += ' '
        the_report += str(minutes) + 'm'

    if (not the_report) or (seconds > 0):
        if the_report:
            the_report += ' '
        the_report += str(seconds) + 's'

    return the_report

# ----------------------------
def get_uptime():
    # ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0

# ----------------------------
def parse_last_log(content):
    # ----------------------------
    return_value = {}

    lines = content.split('\n')
    for line in lines:
        if line:
            splits = line.split()
            alias = splits[0]
            if '_' in alias:
                user_name = alias.split('_')[0] + '.' + alias.split('_')[1]
            else:
                user_name = alias
            if not user_name in return_value:
                return_value[user_name] = {}
            if not alias in return_value[user_name]:
                return_value[user_name][alias] = {'count': 0}
            return_value[user_name][alias]['count'] += 1
    return return_value


# ----------------------------
def build_report(file_content):
    # ----------------------------
    time_now = time.time()

    table_content = ''
    table_content += '<center>'
    table_content += '<table border="1" cellpadding="5">'
    table_content += '<tr>'
    table_content += '<td>'
    table_content += '<b>'
    table_content += 'Time'
    table_content += '</b>'
    table_content += '</td>'
    table_content += '<td>'
    table_content += '<b>'
    table_content += 'age'
    table_content += '</b>'
    table_content += '</td>'
    table_content += '<td>'
    table_content += '<b>'
    table_content += 'log entry'
    table_content += '</b>'
    table_content += '</td>'
    table_content += '</tr>'

    for item in file_content.split('\n'):
        if item:
            table_content += '<tr>'

            try:
                time_stamp_time = float(item.split(',')[0])
                date_to_show = datetime.datetime.fromtimestamp(time_stamp_time, tz=datetime.timezone.utc).strftime(
                    '%Y.%m.%d %H:%M:%S')
                elapsed_to_show = str(int(time_now - time_stamp_time))
            except:
                date_to_show = '(exception)'

            table_content += '<td>'
            table_content += date_to_show
            table_content += '</td>'

            table_content += '<td>'
            table_content += convert_seconds_to_human(elapsed_to_show)
            table_content += '</td>'

            for chunk in item.split(',')[1:]:
                table_content += '<td>'
                table_content += chunk
                table_content += '</td>'
            table_content += '</tr>'
    table_content += '</table>'
    table_content += '</center>'

    return table_content


# ----------------------------
def parse_audit_line(line):
    # ----------------------------
    return_value = {}
    if 'denied' in line:
        action = line.split('{')[1].split('}')[0].strip()
        return_value = {'type': 'denied', 'action': action}
        if action == 'open':
            return_value['path'] = line.split('path=')[1].split(' ')[0].strip().replace('"', '')
    return return_value


# ----------------------------
def make_entry_filename():
    # ----------------------------
    if s_log_root:
        return s_log_root + 'entries.txt'
    else:
        return ''

# ----------------------------
def trim_content_list(content, soft_limit=s_max_log_length):
    # ----------------------------

    content_list = content.split('\n')
    if not content_list[-1]:
        content_list.pop(-1)

    if len(content_list) > soft_limit:
        content_list_to_return = content_list[:soft_limit]
        for item_index in range(soft_limit, len(content_list)):
            if 'detected' in content_list[item_index]:
                content_list_to_return.append(content_list[item_index])
            elif 'initiated' in content_list[item_index]:
                content_list_to_return.append(content_list[item_index])
    else:
        content_list_to_return = content_list

    return content_list_to_return

# ----------------------------
def write_to_log(the_entry):
    # ----------------------------
    log_filename = make_entry_filename()
    if log_filename:
        try:
            content = open(log_filename, 'r').read()
        except:
            content = ''

    content_list = trim_content_list(content)

    uptime, fails = do_one_command('uptime')

    current_linked_folder = get_current_linked_folder()

    # new stuff goes at the front
    new_content = str(time.time()) + ', ' + the_entry + ', ' + '{' + current_linked_folder + ' ' + uptime.replace(',', ' ').split('\n')[
        0] + '}' + '\n'
    # all the existing content goes after
    new_content += '\n'.join(content_list)

    try:
        open(log_filename, 'w').write(new_content)
    except:
        pass


# ----------------------------
def check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart):
    # ----------------------------
    if seconds_since_last_restart < s_seconds_holdoff:
        return False
    else:
        if count_of_failed_get > 4:
            return True
        else:
            return False


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def work_to_do():
    # ====================================
    base_directory = '/dev/shm/'

    return_value = {}

    for file in os.listdir(base_directory):
        if 'service_start_stop_request_' in file:
            return_value[base_directory + file] = open(base_directory + file, 'r').read()

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(watchdog status)'

    status = os.system('systemctl is-active --quiet watchdog-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return_value += ' -> ' + str(len(work_to_do()))

    return return_value


# Main is the loop for the "watchdog-runner" that the service starts

def say_bye(signum, frame):
    write_to_log('watchdog' + ' main loop exited')
    exit(1)

# ----------------------------
def get_current_linked_folder():
    # ----------------------------
    folder_listing, f = do_one_command('ls -l /var/www')
    current_linked = extract_current_linked_folder(folder_listing)
    return current_linked

# ----------------------------
def extract_current_linked_folder(contents):
    # ----------------------------
    return_value = ''
    for line in contents.split('\n'):
        if 'html -> ' in line:
            return_value = line.split('html -> ')[1].split('/')[-1]
    return return_value

# ====================================
def main():
    # ====================================

    # https://stackoverflow.com/questions/25653061/invoke-a-function-when-the-program-is-terminated-or-pc-shutdown

    import signal
    signal.signal(signal.SIGTERM, say_bye)

    pass_count = 0
    time_of_last_get = 0
    count_of_failed_get = 0
    time_of_last_restart = time.time()

    write_to_log('watchdog' + ' main loop started')

    follow_up_after_restart_minutes = [5, 10, 15, 20, 25, 30, 60]
    restart_followups = []

    # look to see if this is starting up because of a machine reboot
    uptime_seconds = get_uptime()
    if uptime_seconds < 10 * 60: # if less than 10 minutes
        write_to_log('watchdog' + ' reboot detected')

    while True:
        try:
            pass_count += 1

            # Check for httpd operation
            time_now = time.time()
            if abs(time_now - time_of_last_get) > 60:
                time_of_last_get = time_now

                saw_at_least_one = False
                try:
                    url_to_test = service_config['home_url']
                    r = requests.get(url_to_test, verify=False, timeout=15.0)
                    saw_at_least_one = True
                except:
                    pass

                try:
                    url_to_test = service_config['home_url'].replace('https', 'http')
                    r = requests.get(url_to_test, verify=False, timeout=15.0)
                    saw_at_least_one = True
                except:
                    pass

                seconds_since_last_restart = time_now - time_of_last_restart
                if saw_at_least_one:
                    if count_of_failed_get > 0:
                        count_of_failed_get = 0
                        write_to_log('reset GET failed count = ' + str(
                            count_of_failed_get) + '; seconds_since_last_restart = ' + str(
                            int(seconds_since_last_restart)) + '/' + str(s_seconds_holdoff) + '; ' + service_config[
                                         'home_url'])
                else:
                    count_of_failed_get += 1
                    write_to_log('increment GET failed count = ' + str(
                        count_of_failed_get) + '; seconds_since_last_restart = ' + str(
                        int(seconds_since_last_restart)) + '/' + str(s_seconds_holdoff) + '; ' + service_config[
                                     'home_url'])

                need_restart = check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart)

                if need_restart:
                    count_of_failed_get = 0
                    time_of_last_restart = time.time()
                    restart_followups = []

                    # FixMe: Make this be default false, and then set true in a datastore item.
                    ok_to_do_the_restart = True
                    if ok_to_do_the_restart:
                        write_to_log('performing restart')
                        command = 'sudo systemctl restart httpd'
                        do_one_command(command)

                seconds_since_last_restart = time_now - time_of_last_restart
                for item in follow_up_after_restart_minutes:
                    if item not in restart_followups:
                        if seconds_since_last_restart > item * 60:
                            # do the followup, just once
                            restart_followups.append(item)
                            write_to_log('watchdog' + ' followup ' + str(item) + ' minutes since start/restart')

            work_items = work_to_do()
            for item in work_items:
                # sudo systemctl start upload-runner.service
                try:
                    splits = work_items[item].split()
                    if len(splits) > 1:
                        service = splits[0]
                        the_request = splits[1]

                        command = 'sudo systemctl ' + the_request + ' ' + service + '-runner.service'
                        #                    do_one_command(command)

                        os.remove(item)

                except:
                    pass

            time.sleep(0.1)
        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write('pass_count : ' + str(pass_count) + ' -> ' + str(
                traceback.format_exc().replace("\n", "<br>").replace("\"",
                                                                     "'")))  # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))


# ====================================
def make_body_POST(environ):
    # ====================================
    # do work on content

    # then return what GET would have done
    return make_body_GET(environ)


# ====================================
def make_body_GET(environ):
    # ====================================

    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += '<center>'

        body += '<br><br>'

        if s_log_root:
            log_filename = make_entry_filename()
            body += log_filename + '<br><br>'
            try:
                file_content = open(log_filename, 'r').read()

                body += build_report(file_content)
            except:
                body += '(log_root no content)'

        else:
            body += '(no log_root)'

        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, 'watchdog_') or permissions.permission_prefix_allowed(environ,
                                                                                                            'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other

# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_watchdog(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_check_if_restart_wanted(self):
        count_of_failed_get = 4
        seconds_since_last_restart = 60 * 60
        expected = False
        actual = check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart)
        self.assertEqual(expected, actual)

        count_of_failed_get = 5
        seconds_since_last_restart = 60 * 60
        expected = True
        actual = check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart)
        self.assertEqual(expected, actual)

        count_of_failed_get = 5
        seconds_since_last_restart = 25 * 60 - 1
        expected = False
        actual = check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart)
        self.assertEqual(expected, actual)

        count_of_failed_get = 5
        seconds_since_last_restart = 25 * 60
        expected = True
        actual = check_if_restart_wanted(count_of_failed_get, seconds_since_last_restart)
        self.assertEqual(expected, actual)

    def test_build_report(self):
        # just catch any exceptions
        file_content = """1717772123.3683128, { 14:55:23 up 4 days, 12:11,  1 user,  load average: 0.58, 0.52, 0.43
} watchdog main loop started"""
        actual = build_report(file_content)

    def build_long_entry(self):
        pass

    def test_parse_audit_line(self):
        line = ''
        expected = {}
        actual = parse_audit_line(line)
        self.assertEqual(expected, actual)

        line = 'type=AVC msg=audit(1717592838.477:201615): avc:  denied  { write } for  pid=3397 comm="httpd" name="status_summary" dev="sdb" ino=4980766 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:unlabeled_t:s0 tclass=file permissive=1'
        expected = {'type': 'denied', 'action': 'write'}
        actual = parse_audit_line(line)
        self.assertEqual(expected, actual)

        line = 'type=AVC msg=audit(1717592838.477:201615): avc:  denied  { open } for  pid=3397 comm="httpd" path="/mnt/disks/SSD/var/log/slicer/tasks/status_summary" dev="sdb" ino=4980766 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:unlabeled_t:s0 tclass=file permissive=1'
        expected = {'type': 'denied', 'action': 'open', 'path': '/mnt/disks/SSD/var/log/slicer/tasks/status_summary'}
        actual = parse_audit_line(line)
        self.assertEqual(expected, actual)

        line = 'type=AVC msg=audit(**********.509:216235): avc:  denied  { write open } for  pid=3396 comm=\"httpd\" path=\"/var/log/slicer/tasks/task_status\" dev=\"sda2\" ino=15158 scontext=system_u:system_r:httpd_t:s0 tcontext=system_u:object_r:var_log_t:s0 tclass=file permissive=1\n'

    def test_parse_last_log(self):
        content = ''
        expected = {}
        actual = parse_last_log(content)
        self.assertEqual(expected, actual)

        # from command 'last -a -d -w -x'
        content = """david_ferguson_cardinalhealth_c pts/0        Fri Jun  7 20:04    gone - no logout  LWJQGM163.cardinalhealth.net
"""
        expected = {'david.ferguson': {'david_ferguson_cardinalhealth_c': {'count': 1}}}
        actual = parse_last_log(content)
        self.assertEqual(expected, actual)

        content = """david_ferguson_cardinalhealth_c pts/0        Fri Jun  7 20:04    gone - no logout  LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Fri Jun  7 18:51 - 19:08  (00:17)     LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Fri Jun  7 18:21 - 18:42  (00:20)     LWJQGM163.cardinalhealth.net
"""
        expected = {'david.ferguson': {'david_ferguson_cardinalhealth_c': {'count': 1},
                                       'david_ferguson_cardinalhealth_co': {'count': 2}}}
        actual = parse_last_log(content)
        self.assertEqual(expected, actual)

        content = """david_ferguson_cardinalhealth_c pts/0        Fri Jun  7 20:04    gone - no logout  LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Fri Jun  7 18:51 - 19:08  (00:17)     LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Fri Jun  7 18:21 - 18:42  (00:20)     LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Fri Jun  7 14:42 - 14:42  (00:00)     LWJQGM163.cardinalhealth.net
david_ferguson_cardinalhealth_co pts/0        Tue Jun  4 17:11 - 17:43  (00:31)     LWH1885X3.cardinalhealth.net
mario.bismarjr pts/1        Sun Jun  2 10:27 - 10:34  (00:06)     lpec5009adm101.cardinalhealth.net
johnnichole.macanan pts/0        Sun Jun  2 10:19 - 10:43  (00:23)     lpec5009adm101.cardinalhealth.net
runlevel (to lvl 3)   Sun Jun  2 03:22   still running      0.0.0.0
reboot   system boot  Sun Jun  2 03:22   still running      0.0.0.0
shutdown system down  Sun Jun  2 03:21 - 03:22  (00:00)     0.0.0.0
justinrex.castro pts/0        Sat Jun  1 18:10 - 18:20  (00:10)     lpec5009adm101.cardinalhealth.net
"""

        # user command history: https://superuser.com/questions/309434/how-to-view-command-history-of-another-user-in-linux
        # mario.bismarjr
        # sudo tail /var/log/secure | grep mario.bismarjr
        # sudo ls -l /home
        # sudo cat /home/<USER>/.bash_history
        # sudo cat /home/<USER>/.bash_history

    def test_trim_content_list(self):
        content = 'line1'
        expected = ['line1']
        actual = trim_content_list(content)
        self.assertEqual(expected, actual)

        content = 'line1\n'
        expected = ['line1']
        actual = trim_content_list(content)
        self.assertEqual(expected, actual)

        content = 'line1\nline2'
        expected = ['line1', 'line2']
        actual = trim_content_list(content)
        self.assertEqual(expected, actual)

        content = 'line1\nline2'
        expected = ['line1', 'line2']
        actual = trim_content_list(content, soft_limit=2)
        self.assertEqual(expected, actual)

        content = 'line1\nline2\nline3'
        expected = ['line1', 'line2']
        actual = trim_content_list(content, soft_limit=2)
        self.assertEqual(expected, actual)

        content = 'line1\nline2\nline3\nreboot detected'
        expected = ['line1', 'line2', 'reboot detected']
        actual = trim_content_list(content, soft_limit=2)
        self.assertEqual(expected, actual)

        content = 'line1\nline2\nline3\nreboot detected\nline4'
        expected = ['line1', 'line2', 'reboot detected']
        actual = trim_content_list(content, soft_limit=2)
        self.assertEqual(expected, actual)

        content = 'line1\nline2\nline3\nreboot initiated\nline4'
        expected = ['line1', 'line2', 'reboot initiated']
        actual = trim_content_list(content, soft_limit=2)
        self.assertEqual(expected, actual)

# End of source file
