service = "config"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/config.py

using:
sudo vi /var/www/html/config.py

sudo chown apache:apache /var/www/html/config.py


"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python -m unittest slicer_wsgi_config


"""

import time
import unittest

startup_exceptions = ''

try:
    pass
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ====================================
def color(color=''):
    # ====================================
    return_value = '(204, 51, 255, 1.0)'

    # https://www.w3schools.com/colors/colors_picker.asp
    # https://rgbacolorpicker.com

    colors = {}
    colors['color_red_warning'] = '(255, 0, 0, 0.3)'
    colors['color_yellow_caution'] = '(255, 255, 100, 0.3)'
    colors['color_green'] = '(0, 255, 0, 0.3)'
    colors['color_clear'] = '(0, 0, 0, 0.0)'
    colors['color_purple'] = '(255, 0, 255, 0.3)'

    colors['color_version_is_ahead'] = '(120, 120, 120, 0.3)'
    colors['color_version_is_matched'] = '(0, 255, 0, 0.3)'
    colors['color_update_highlight'] = '(100, 100, 255, 0.3)'
    colors['color_version_is_just_behind_by_one'] = '(230, 230, 0, 0.3)'
    colors['color_version_is_just_behind_by_more_than_one'] = '(255, 0, 0, 0.3)'

    if color in colors:
        return_value = colors[color]

    return return_value


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_version_compares_exists(self):
        """
        (fill in here)
        """

        self.assertEqual(color('color_red_warning'), "(255, 0, 0, 0.3)")
        self.assertEqual(color('color_yellow_caution'), "(255, 255, 100, 0.3)")
        self.assertEqual(color('color_green'), "(0, 255, 0, 0.3)")
        self.assertEqual(color('color_clear'), "(0, 0, 0, 0.0)")
        self.assertEqual(color('color_purple'), "(255, 0, 255, 0.3)")

        # for service versions
        self.assertEqual(color('color_update_highlight'), "(100, 100, 255, 0.3)")  # a light blue for starters
        self.assertEqual(color('color_version_is_matched'), "(0, 255, 0, 0.3)")  # a light green
        self.assertEqual(color('color_version_is_ahead'), "(120, 120, 120, 0.3)")  # a light gray
        self.assertEqual(color('color_version_is_just_behind_by_one'),
                         "(230, 230, 0, 0.3)")  # a light yellow for starters
        self.assertEqual(color('color_version_is_just_behind_by_more_than_one'),
                         "(255, 0, 0, 0.3)")  # a light red for starters

        self.assertEqual(color('color_missing_this_one'),
                         "(204, 51, 255, 1.0)")  # an obnoxious purple, to draw attention to the fact that the color is missing
