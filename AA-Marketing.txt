What is it?
    General Purpose Computer
        (can easily run any web page)
    Small
        (the size of a credit card, and only one inch thick)
    Low cost
        ($100 all in, just add a monitor, keyboard, mouse)
    Low power
        (5 Watts, about the same as a night light; full shift on a 60 WHr battery)
    Low Maintenance
        No fans, no spinning disks


What Can it Do?
    Run any website
        kiosks, display boards, metrics boards, ERP, VOE
    Replace a Desktop
    Replace a Laptop
    Replace a Thin Client
    Interface to sensors and provide central visibility


What have we done?
    Cardinal PI OS
        Secure private web browsing is the only function
        Allow list enforced - work sites only
        Option to auto launch site (display boards)
        Option to auto logout - VOE and ERP systems

    Slicer
        Central Configuration and Management Portal

Small:
    Have many of them ready, standing by, to deploy for VOE uses.

Where can it be?
    Anywhere on the CAH wired network
    Where available, on the IOT wifi network


Why the pi?
    Lowest cost tech refresh available
    Regional RCS configurable




