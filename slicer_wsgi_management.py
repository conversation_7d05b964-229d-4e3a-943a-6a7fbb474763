# A device management block ID for assigning devices into blocks of commonly managed devices.

service = "management"
version = service + '.0.4'

description = """
This is to manage devices by a block ID, instead of the original, which was by site ID.


"""

release_notes = """
2023.04.17
management.0.4

be able to create new management id names

2023.02.01
management.0.2

add live data table

"""

_notes = """
QCAM Pharma Mobil Solution just reviewed and will be POC/Pilot at OH044-Groveport, OH and WV001-Wheeling, WV.

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/management.py

using:
sudo vi /var/www/html/management.py

It also requires:

sudo vi /etc/httpd/conf.d/python-management.conf
----- start copy -----
WSGIScriptAlias /management /var/www/html/management.py
----- end copy -----

sudo chown apache:apache /var/www/html/management.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/management-runner
sudo chmod +x /var/www/html/management-runner

# ===== begin: start file
#!/usr/bin/env python
import management
management.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/management-runner.service
sudo systemctl daemon-reload
sudo systemctl stop management-runner.service
sudo systemctl start management-runner.service
sudo systemctl enable management-runner.service

systemctl status management-runner.service

sudo systemctl restart management-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep management-runner

OR

tail -f /var/log/syslog | fgrep management-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/management-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import management; print(management.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/management

https://slicer.cardinalhealth.net/management?siteid=PR005

https://slicer.cardinalhealth.net/management?serial=100000002a5da842

https://slicer.cardinalhealth.net/management?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_management


"""

import copy
import traceback
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import view
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ----------------------------
def get_management_block_list(data_store_content):
    # ----------------------------
    return_value = []

    if 'management_block_list' in data_store_content:
        return_value = sorted(data_store_content['management_block_list'].split(','))

    return return_value


# ----------------------------
def get_managementID(data_store_content, id):
    # ----------------------------
    try:
        return data_store_content['device_managementID_' + id]
    except:
        return ''


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except ValueError:
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # later python 3
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    the_who = login.get_current_user(environ)

    value_to_use = ''
    if 'the_selection' in d:
        if 'newID_text_set' == str(d['the_selection'][0]):
            try:
                if 'newID_text' in d:
                    # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                    value_to_use = d['newID_text'][0].replace("\\", "").replace("<", "").replace(">", "").replace("|",
                                                                                                                  "<br>")

                    data_store_content = datastore.all_datastore()
                    the_list = get_management_block_list(data_store_content)
                    if not value_to_use in the_list:
                        the_list.append(value_to_use)

                    datastore.set_value('management_block_list', ','.join(the_list), who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        if 'newID_text_delete' == str(d['the_selection'][0]):
            try:
                if 'newID_text' in d:
                    # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                    value_to_use = d['newID_text'][0].replace("\\", "").replace("<", "").replace(">", "").replace("|",
                                                                                                                  "<br>")

                    data_store_content = datastore.all_datastore()
                    the_list = get_management_block_list(data_store_content)

                    the_new_list = []
                    for item in the_list:
                        if item == value_to_use:
                            pass
                        else:
                            the_new_list.append(item)

                    if not value_to_use in the_list:
                        the_list.append(value_to_use)

                    datastore.set_value('management_block_list', ','.join(the_new_list), who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:

        # main page
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

        load_url = url_to_use + '/' + service + '?get=data,type=issues'

        body += '<center>'
        body += version
        body += '</center>'

        body += '<br><br>'

        data_store_content = datastore.all_datastore()
        the_list = get_management_block_list(data_store_content)

        the_data = []
        for item in the_list:
            the_data.append({'id': item})
        the_config = {}
        the_config['highlight_sorted'] = True
        the_config['the_format'] = 'border="1" cellpadding="5"'

        try:
            body += view.simple(the_data, first_column='id', sort_column='id', the_config=the_config)
        except:
            pass  # slicer01

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    if True:
        # =================================================
        # Configuration Items, with individual submit(s)
        # =================================================
        color_red_warning = "(255, 0, 0, 0.3)"
        color_yellow_caution = "(255, 255, 100, 0.3)"
        color_green = "(0, 255, 0, 0.3)"
        color_clear = "(0, 0, 0, 0.0)"
        color_purple = "(255, 0, 255, 0.3)"

        body_add = ''
        body_add += '<br><br>'

        if permissions.permission_prefix_allowed(environ, service + '_'):
            all_d_list = []

            if permissions.permission_prefix_allowed(environ, service + '_' + 'create'):
                # ---------------
                # prompt for new
                # ---------------
                d = {}
                d['title'] = 'new management ID'
                d['option_value'] = "newID_text_set"
                d['text_field_name'] = "newID_text"
                d['current_value'] = datastore.get_value_stored(data_store_content, service + '_item_' + 'testvalue')
                d['values_to_show'] = None
                d['requires'] = []
                d['text_for_info_box'] = 'Enter a new management id'
                d['color_for_info_box'] = color_clear
                d['submit_name'] = 'make new'
                d['is_development'] = False
                all_d_list.append(copy.deepcopy(d))

            # ---------------
            # prompt for existing
            # ---------------
            block_id = {}
            for key in data_store_content.keys():
                if 'device_managementID_' in key:
                    the_id = data_store_content[key]
                    if not the_id in block_id:
                        block_id[the_id] = 0
                    block_id[the_id] += 1

            user_can_delete = permissions.permission_prefix_allowed(environ, service + '_' + 'delete')
            for id in get_management_block_list(data_store_content):
                the_count = 0
                if id in block_id:
                    the_count = block_id[id]

                d = {}
                d['title'] = 'existing: used ' + str(the_count) + ' time(s)'
                d['option_value'] = "newID_text_delete"
                d['text_field_name'] = "newID_text"
                d['current_value'] = id
                d['values_to_show'] = None
                d['requires'] = []
                d['submit_name'] = ''
                d['text_for_info_box'] = 'Must not be used for delete to show'
                d['color_for_info_box'] = color_clear
                if the_count == 0:
                    if user_can_delete:
                        d['submit_name'] = 'delete'
                        d['text_for_info_box'] = 'delete action happens immediately'
                        d['color_for_info_box'] = color_red_warning
                d['is_development'] = False
                all_d_list.append(copy.deepcopy(d))

            # ---------------
            # Build the content
            # ---------------
            body_add += '<center>'
            body_add += '<table border="1" cellpadding="5">'

            body_add += '<tr>'
            body_add += '<td>'
            body_add += '<B>Category</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Current<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>New<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>action</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Notes</B>'
            body_add += '</td>'
            body_add += '</tr>'

            for d in all_d_list:
                development_marker = ''
                use_it = True
                submit_name = 'Submit'
                if 'submit_name' in d:
                    submit_name = d['submit_name']

                if 'is_development' in d:
                    use_it = False
                    if d['is_development']:
                        if permissions.permission_allowed(environ, 'development_read'):
                            use_it = True
                            development_marker = '--- Development ---<br>'
                    else:
                        use_it = True

                if use_it:
                    body_add += '<tr>'
                    body_add += '<form method="post" action="">'
                    body_add += '<td>'
                    body_add += development_marker
                    body_add += d['title']
                    body_add += '</td>'

                    current_value = d['current_value']
                    if not current_value:
                        current_value = ''

                    body_add += '<td>'
                    body_add += development_marker
                    body_add += current_value
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker

                    body_add += '<select name="the_selection" id="the_selection" hidden>'
                    body_add += '<option value="' + d['option_value'] + '" selected>' + d['option_value'] + '</option>'
                    body_add += '</select>'
                    if d['values_to_show'] is None:
                        body_add += '<input type="text" size=25 name="' + d[
                            'text_field_name'] + '" value="' + current_value + '\">'
                    else:
                        body_add += '<select name="profile" id="profile">'
                        for key_name in d['values_to_show']:
                            name_to_show = ''
                            if key_name:
                                if 'name_to_show_rule' in d:
                                    if d['name_to_show_rule'] == 'screen_settings':
                                        name_to_show = key_name.split()[0].replace('(', ' (')
                                    else:
                                        name_to_show = key_name
                                else:
                                    name_to_show = key_name

                            if current_value == key_name:
                                body_add += '<option value="' + key_name + '" selected>' + name_to_show + '</option>'
                            else:
                                body_add += '<option value="' + key_name + '">' + name_to_show + '</option>'

                        body_add += '</select>'

                    reported_value = ''
                    if 'reported_value' in d:
                        reported_value = d['reported_value']
                    if reported_value:
                        body_add += '   {last report = ' + reported_value + '}'

                    body_add += ''
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker
                    if submit_name:
                        body_add += '<input type="submit" value="' + submit_name + '">'
                    body_add += '</td>'
                    body_add += '<td style="background-color:rgba' + d['color_for_info_box'] + '">'
                    body_add += development_marker
                    body_add += d['text_for_info_box']
                    body_add += '</td>'

                    body_add += '</form>'
                    body_add += '</tr>'

            body_add += '</table>'
            body_add += '</center>'

        else:
            body_add += '<center>'
            body_add += 'Current user does not have ' + service + ' permissions'
            body_add += '</center>'

        # ---------------
        # add the content
        # ---------------
        body += body_add

        # =================================================
        #
        # =================================================

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ,
                                             service + '_'):  # or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_management(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_get_managementID_empty(self):
        data_store_content = {}
        id = '1000000001744194'
        expected = ''
        actual = get_managementID(data_store_content, id)
        self.assertEqual(expected, actual)

    def test_get_managementID_set(self):
        data_store_content = {}
        data_store_content['device_managementID_1000000001744194'] = 'test block 1'
        id = '1000000001744194'
        expected = 'test block 1'
        actual = get_managementID(data_store_content, id)
        self.assertEqual(expected, actual)

    def test_get_management_block_list(self):
        data_store_content = {'management_block_list': 'block1,block2'}
        expected = ['block1', 'block2']
        actual = get_management_block_list(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'management_block_list': 'block2,block1'}
        expected = ['block1', 'block2']
        actual = get_management_block_list(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        expected = []
        actual = get_management_block_list(data_store_content)
        self.assertEqual(expected, actual)

# End of source file
