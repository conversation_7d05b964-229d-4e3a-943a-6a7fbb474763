# A documentation service for slicer
#
# After saving this file as a new name,
#    do a whole file replace of documentation with the new module name,
#    then, go into the permissions file, and add the new module there.

service = "documentation"
version = service + '.0.2'

release_notes = """
2025.05.06
documentation.0.1

Be able to serve the mkdocs generated 'site' folder, in production, on the server.

    <PERSON><PERSON> needs to build the site folder and contents:
        for each folder in the site, build link replacers,
            "(folder)/" that make it go through this module
            like "/documentation/site/(folder)",
            then do those replaces through all of the files in the site folder(s)

        Make sure loader copies the site folder, when making a 'modified' folder

    This module, when a reference comes in for a GET, should look like:
        documentation/getting-started/knowledge/
    and on that one, go get the index.html in the folder
        site/getting-started/knowledge/

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/documentation.py

using:
sudo vi /var/www/html/documentation.py

It also requires:

sudo vi /etc/httpd/conf.d/python-documentation.conf
----- start copy -----
WSGIScriptAlias /documentation /var/www/html/documentation.py
----- end copy -----

sudo chown apache:apache /var/www/html/documentation.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/documentation-runner
sudo chmod +x /var/www/html/documentation-runner

# ===== begin: start file
#!/usr/bin/env python
import documentation
documentation.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/documentation-runner.service
sudo systemctl daemon-reload
sudo systemctl stop documentation-runner.service
sudo systemctl start documentation-runner.service
sudo systemctl enable documentation-runner.service

systemctl status documentation-runner.service

sudo systemctl restart documentation-runner.service

systemctl --failed

systemctl status httpd


# show general status
systemctl;

# clear failed ones
sudo systemctl reset-failed


# Logging of std out
cat /var/log/syslog | fgrep documentation-runner

OR

tail -f /var/log/syslog | fgrep documentation-runner

Rocky9:

sudo tail -f /var/log/messages | fgrep documentation-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/documentation-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import documentation; print(documentation.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/documentation

https://slicer.cardinalhealth.net/documentation?siteid=PR005

https://slicer.cardinalhealth.net/documentation?serial=100000002a5da842

https://slicer.cardinalhealth.net/documentation?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_documentation


"""

import copy
import getpass
import hashlib
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import traceback
import unittest

s_this_user = getpass.getuser()

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    if startup_exceptions:
        startup_exceptions = s_this_user + ': ' + startup_exceptions
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ----------------------------
def junk(the_env):
    # ----------------------------
    if 'dave' in the_env:
        return True

    if 'jack' in the_env:
        return True

    if 'matt' in the_env:
        return True

    return False


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)  # this is where the atomic activity occurs


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    last_exception = ''

    while True:
        pass_count += 1
        try:
            pass


        except:
            last_exception = str(traceback.format_exc().replace("\n", "<br>").replace("\"","'"))

        open('/dev/shm/running_exceptions_' + service, 'w').write(
            'pass_count : ' + str(pass_count) + '\n' + last_exception)

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
    action_report = ''

    the_who = login.get_current_user(environ)

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except ValueError:
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # later python 3
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    value_to_use = ''
    if 'the_selection' in d:
        selection_key = str(d['the_selection'][0])
        if action_report:
            action_report += '\n'
        action_report += 'selection_key: ' + '"' + selection_key + '"'

        if 'testvalue_text_set' == selection_key:
            try:
                if 'testvalue_text' in d:
                    # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                    value_to_use = d['testvalue_text'][0].replace("\\", "").replace("<", "").replace(">", "").replace(
                        "|", "<br>")
                data_store_value_name = service + '_item_' + 'testvalue'
                previous_value = datastore.get_value(data_store_value_name)
                if action_report:
                    action_report += '\n'
                action_report += 'setting ' + data_store_value_name + ' from ' + '"' + previous_value + '"' + ' to ' + '"' + value_to_use + '"'
                datastore.set_value(data_store_value_name, value_to_use, who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

    # then return what GET would have done
    body, other = make_body_GET(environ)
    other['action_report'] = action_report
    return body, other


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count

    data_store_content = datastore.all_datastore()

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        REQUEST_URI = ''
        if 'REQUEST_URI' in environ:
            REQUEST_URI = environ['REQUEST_URI']

        if '/site/' in REQUEST_URI:

            REQUEST_URI = REQUEST_URI.replace('/documentation/site/documentation/site', '/documentation/site')

            argument = ''
            if '?' in REQUEST_URI:
                REQUEST_URI_splits = REQUEST_URI.split('?')
                argument = REQUEST_URI_splits[1]
                REQUEST_URI = REQUEST_URI_splits[0]

            full_filename = '/var/www/html/site/' + REQUEST_URI.split('/site/')[1]

            full_filename_splits = full_filename.split('/')
            if full_filename_splits[-1] == '':
                full_filename += 'index.html'

            file_name = full_filename.split('/')[-1]
            file_and_extension_splits = file_name.split('.')
            file_extension = ''
            if len(file_and_extension_splits) > 1:
                file_extension = file_and_extension_splits[-1]

            if os.path.exists(full_filename) and os.path.isfile(full_filename):
                body = open(full_filename, 'rb').read()
                if file_extension in ['html']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': False}
                elif file_extension in ['png']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'image/png')], 'add_wrapper': False}
                elif file_extension in ['jpg']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'image/jpeg')], 'add_wrapper': False}
                elif file_extension in ['gif']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'image/gif')], 'add_wrapper': False}
                elif file_extension in ['pdf']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'application/pdf')], 'add_wrapper': False}
                elif file_extension in ['js']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'application/javascript')], 'add_wrapper': False}
                elif file_extension in ['css']:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/css')], 'add_wrapper': False}
                else:
                    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': False}
            else:
                other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
                body = 'File not found: ' + full_filename

            return body, other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
            body += '<br><br>'
            body += '<center>'
            body += '<a href="documentation/site/">Slicer developer documentation</a>'
            body += '</center>'
            body += '<br><br>'


    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

        # =================================================
        #
        # =================================================

        body += '<br><br>'
        body += '<br><br>'

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, service + '_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']

        if other['add_wrapper']:
            html += '<html>\n'
            if 'head' in other:
                html += '<head>\n'
                html += other['head']
                html += '</head>\n'
            html += '<body>\n'
            html += body

            html += '</body>\n'
            html += '</html>\n'
        else:
            html = body
    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    if other['add_wrapper']:
        try:
            html = organization.wrap_page_with_session(environ, html)
            start_response(status, response_header)
        except:
            # still on slicer01
            # allow non wrapped response
            start_response(status, response_header)

        return [html.encode()]
    else:
        start_response(status, response_header)
        return [html]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_documentation(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        # real world one
        environ = {'UNIQUE_ID': 'ZbQoasMC9TDrrjpsbWHe9AAAAMw', 'GATEWAY_INTERFACE': 'CGI/1.1',
                   'SERVER_PROTOCOL': 'HTTP/1.1', 'REQUEST_METHOD': 'GET', 'QUERY_STRING': '',
                   'REQUEST_URI': '/organization', 'SCRIPT_NAME': '/organization', 'HTTP_HOST': '***********',
                   'HTTP_CONNECTION': 'keep-alive', 'HTTP_CACHE_CONTROL': 'max-age=0',
                   'HTTP_UPGRADE_INSECURE_REQUESTS': '1',
                   'HTTP_USER_AGENT': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                   'HTTP_ACCEPT': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                   'HTTP_ACCEPT_ENCODING': 'gzip, deflate', 'HTTP_ACCEPT_LANGUAGE': 'en-US,en;q=0.9',
                   'HTTP_COOKIE': 'login_time=20240126214015101941', 'SERVER_SIGNATURE': '',
                   'SERVER_SOFTWARE': 'Apache/2.4.57 (Rocky Linux) OpenSSL/3.0.7 mod_wsgi/4.7.1 Python/3.9',
                   'SERVER_NAME': '***********', 'SERVER_ADDR': '***********', 'SERVER_PORT': '80',
                   'REMOTE_ADDR': '**************', 'DOCUMENT_ROOT': '/var/www/html', 'REQUEST_SCHEME': 'http',
                   'CONTEXT_PREFIX': '', 'CONTEXT_DOCUMENT_ROOT': '/var/www/html', 'SERVER_ADMIN': 'root@localhost',
                   'SCRIPT_FILENAME': '/var/www/html/organization.py', 'REMOTE_PORT': '59606', 'PATH_INFO': '',
                   'mod_wsgi.script_name': '/organization', 'mod_wsgi.path_info': '', 'mod_wsgi.process_group': '',
                   'mod_wsgi.application_group': '', 'mod_wsgi.callable_object': 'application',
                   'mod_wsgi.request_handler': 'wsgi-script', 'mod_wsgi.handler_script': '',
                   'mod_wsgi.script_reloading': '1', 'mod_wsgi.listener_host': '', 'mod_wsgi.listener_port': '80',
                   'mod_wsgi.enable_sendfile': '0', 'mod_wsgi.ignore_activity': '0',
                   'mod_wsgi.request_start': '1706305642227287', 'mod_wsgi.request_id': 'ZbQoasMC9TDrrjpsbWHe9AAAAMw',
                   'mod_wsgi.script_start': '1706305642234602', 'wsgi.version': (1, 0), 'wsgi.multithread': True,
                   'wsgi.multiprocess': True, 'wsgi.run_once': False, 'wsgi.url_scheme': 'http',
                   'wsgi.errors': "<_io.TextIOWrapper name='' encoding='utf-8'>", 'wsgi.input': '',
                   'wsgi.input_terminated': True, 'wsgi.file_wrapper': '', 'apache.version': (2, 4, 53),
                   'mod_wsgi.version': (4, 7, 1), 'mod_wsgi.total_requests': 240, 'mod_wsgi.thread_id': 23,
                   'mod_wsgi.thread_requests': 8}

    def test_junk(self):
        environ = {}
        expected = False
        actual = junk(environ)
        self.assertEqual(expected, actual)

        environ = {'dave': 1}
        expected = True
        actual = junk(environ)
        self.assertEqual(expected, actual)

        environ = {'jack': 1}
        expected = True
        actual = junk(environ)
        self.assertEqual(expected, actual)

        environ = {'matt': 1}
        expected = True
        actual = junk(environ)
        self.assertEqual(expected, actual)

# End of source file
