# A thirdparty service for slicer

service = "thirdparty"
version = service + '.0.2.e'

release_notes = """
2023.02.01
thirdparty.0.2

add live data table

"""

_test = """
download
http://************/thirdparty?filetodownload=testapp_1.0.2

sudo mkdir /cardinal/thirdparty
cd /cardinal/thirdparty
sudo curl -k --output testapp_1.0.2 https://************/thirdparty?filetodownload=testapp_1.0.2
sudo chmod +x testapp_1.0.2
sudo ./testapp_1.0.2


http://************/thirdparty?filetodownload=qcam_20230622_161700

sudo mkdir /cardinal/thirdparty
cd /cardinal/thirdparty
sudo curl -k --output qcam_20230622_161700.zip https://************/thirdparty?filetodownload=qcam_20230622_161700

sudo unzip qcam_20230622_161700.zip
cd QCAM_Release_2023-06-22
sudo chmod +x ./installer.sh
sudo ./installer.sh




"""

_permissions = """
start_permissions
create:
read:
update:
delete:
download:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/thirdparty.py

using:
sudo vi /var/www/html/thirdparty.py

It also requires:

sudo vi /etc/httpd/conf.d/python-thirdparty.conf
----- start copy -----
WSGIScriptAlias /thirdparty /var/www/html/thirdparty.py
----- end copy -----

sudo chown apache:apache /var/www/html/thirdparty.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/thirdparty-runner
sudo chmod +x /var/www/html/thirdparty-runner

# ===== begin: start file
#!/usr/bin/env python
import thirdparty
thirdparty.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/thirdparty-runner.service
sudo systemctl daemon-reload
sudo systemctl stop thirdparty-runner.service
sudo systemctl start thirdparty-runner.service
sudo systemctl enable thirdparty-runner.service

systemctl status thirdparty-runner.service

sudo systemctl restart thirdparty-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep thirdparty-runner

OR

tail -f /var/log/syslog | fgrep thirdparty-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/thirdparty-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import thirdparty; print(thirdparty.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/thirdparty

https://slicer.cardinalhealth.net/thirdparty?siteid=PR005

https://slicer.cardinalhealth.net/thirdparty?serial=100000002a5da842

https://slicer.cardinalhealth.net/thirdparty?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_thirdparty


"""

_design_notes = """

Manual method:

???

Automation:

Guidbook (aka the rules, the step-by-step)
    How to install version x.y.z
    How to get status
    How to get logs
    How to un-install version x.y.z

Make the ThirdParty wsgi be a place to allow upload permission, and then
another permission to allow that file to be pulled (like uploads does it).
    Have a way to define a new "app", that makes a sub-section, that can then accept the
        upload of exe, and the guidebook details for each version, about how to use the app.



Then, have a way to inform the device that there is content it needs to pull.
    ? How can we do this???
    Have a pi_thirdparty service, that checks a list of what version
        of each app should be running, and then do the work to make it happen.
        How/where can we keep the "guide book" for how to install, get version, ...?

Have that pi_thirdparty deliver version and status once per X minutes to Slicer.
Have wsgi thirdparty provide data about each device, to the reporting?
Or, just have the data come back from runner shared details, and report directly?

Then, make a report tab to show what versions are on each device of each app.
Be able to set the desired version for each device, for each app, and then report on any that are not yet at the target version.

We need to do the diff reporting for services too, as a reminder to go investigate why a
device did not pull the update, or maybe it has been replaced, and needs set again.





Automation notes:
We need to allow the executable to be delivered with all content in a zip file.
    (make a place on Slicer to allow this upload?)

That zip content needs to be made into xxd content.

The xxd content then gets added to the agent file, and bump the version number. (Is this the right idea?)

The agent file is then loaded to slicer.
It is not in release notes of a service pack (SP), so it will not show in the interface, which is good, so that we can test.

With certain permissions (or combination of permissions), allow the agent to be pulled by a test device.

When testing proves out, then allow a check mark next to that version, to allow it to be applied in general use by the allowed devices.


QCAM notes:
Raw setup:

OS (5.15.84-v7l+)
Install libraries
Copy code
    config.json -> "installDirectory": "/home/<USER>/QCam",
Configure for serialnumber/endpoint URLS (need to locate where this gets stored)
    - ?normally these are user configured, through the UI?
    - main_ui.py -> CONFIG_FILE = "config.json"
Run code

==============================================
2023.06.12
Hi Chris, Kamal,

Here are the queue details for QCAM - Exceptions Portal Queues.

  please note that there is a change to Stage details from the previously shared details.

Stage:

Host: https://dev-cardinal-eai.servicebus.windows.net
Queue Name: stg-dscsa-qcam
Queue Url: https://dev-cardinal-eai.servicebus.windows.net/stg-dscsa-qcam

Prod:

Host: https://prod-cardinal-eai-dscsa-qcam.servicebus.windows.net
Queue Name: prod-dscsa-qcam
Queue Url: https://prod-cardinal-eai-dscsa-qcam.servicebus.windows.net/prod-dscsa-qcam

Let us know if you need any additional information.

Thanks,
Aditya
==============================================
Qcam Setup files

https://cardinalhealth.sharepoint.com/:f:/r/sites/QCam/Shared%20Documents/General/Presentations/QCAM%20SETUP%20FILES?csf=1&web=1&e=TswjRt

There is a file called "VERSION", at the root of files, with the value "0.0.1"

email 2023.06.20
Sunil,

I was able to make a first pass at the files you supplied. Here are my notes:

1)	The readme acknowledges that this current version is based on an old OS, and that there is intent to work with latest support OS in the future at some point, we would need to then do a complete swap out of an SD card, on any device delivered based on old OS, correct? Or, will we be given a step-by-step on how to upgrade in place?

2)	I was not able to obtain the exact OS that was given in the readme, so I was not able to test a setup from scratch. The readme describes starting with 5.15.84-v7l+, and I found that the current downloads for the OS only have a 5.10.x version.

3)	The readme does not specify where to put the runtime files into the raspberry pi directory structure; from looking at some of the other files, it appears that it should be "/home/<USER>/QCam"... how does the ldi user get created? (How that user gets created is missing from the readme)

4)	The method given to run the code appears to be to type in at the command line; is there not an automatic launch on power up?

5)	Is there.a full SD card "image" file that captures the basic setup? We use "applePiBaker" to make an image, that gets "file size reduced", and makes about a 1GB image file, no matter the size of the actual SD card being imaged.

6)	An observation, the code is launched with python coordinator.py, unless there is some alias that points it to python3, this is launching python 2, which has been end of life since Jan 1, 2020. I understand it still works I also have projects that depend on it continuing to run however, this is a new project, and it should be clearly understood by the team, that this choice has been made, and to ask How will this be upgraded to python3 in the future?

Cheers,
Dave

==============================================
2023.06.14
Serial,           WiFi Mac

100000003dd1585f, d8:3a:dd:06:b1:85

100000003497a4cb, e4:5f:01:09:f7:66


==============================================
==============================================
==============================================
==============================================
==============================================
==============================================
==============================================
==============================================

"""

import cgi
import copy
import datetime
import traceback
import json
import os
import shlex
import shutil
import stat
import subprocess
import sys
from tempfile import TemporaryFile
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
base_upload_path = ''
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_upload_path = service_config['base_upload_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def make_app_storage_filename(base_upload_path, application_name, application_version):
    # ====================================
    return base_upload_path + application_name + '_' + application_version


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    create_permission = permissions.permission_allowed(environ, service + '_create')
    the_who = login.get_current_user(environ)

    if create_permission:
        # use cgi module to read data
        body_of_form = read(environ)
        field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

        application_name = str(field_storage.getvalue('input_app_name_string'))
        application_version = str(field_storage.getvalue('input_version_string'))
        application_filename = ''

        # body = str(field_storage.list)
        # return body, other
        # [MiniFieldStorage('file_download_allowed', 'testapp_1.0.0'), MiniFieldStorage('file_download_allowed_allowed', 'Yes')]

        try:
            file_name = field_storage.getvalue('file_download_allowed')
            allow_to_allow = field_storage.getvalue('file_download_allowed_allowed')
            datastore.set_value('thirdparty_download_permission_' + str(file_name), allow_to_allow)
        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))
            pass

        try:
            file_name = field_storage.getvalue('file_delete_allowed')
            allow_to_allow = field_storage.getvalue('file_delete_allowed_key')
            if 'delete_it_now' == allow_to_allow:
                try:
                    os.remove(base_upload_path + file_name)
                except:
                    pass
                try:
                    os.remove(base_upload_path + file_name + '.md5')
                except:
                    pass
        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))
            pass

        for item in field_storage.list:
            if item.filename and application_name and application_version:
                application_filename = str(item.filename)

                file_content = item.file.read()

                if base_upload_path:
                    output_file = make_app_storage_filename(base_upload_path, application_name, application_version)

                    if os.path.isfile(output_file):
                        body = 'file already exists ' + output_file
                        return body, other
                    else:
                        if not os.path.exists(os.path.dirname(output_file)):
                            os.makedirs(os.path.dirname(output_file))

                        with open(output_file, 'wb') as f:
                            f.write(file_content)

                        body = 'file written ' + output_file
                        return body, other
                else:
                    body = 'base_upload_path is not defined'
                    return body, other

        body = ''
        body += application_name + ', ' + application_version + ', ' + application_filename

        # then return what GET would have done
        body, other = make_body_GET(environ)
        return body, other

    else:
        body = 'The current user (' + the_who + ') does not have permissions to create content here.'
        return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_data(environ):
    # ====================================

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
    the_type = ''
    if 'type' in query_items:
        the_type = query_items['type']

    if the_type == 'issues':
        live_data = get_live_data()

        the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

        the_data['headers'] = live_data['headers']

        for item in live_data['data']:
            row_content = []
            row_links = []
            row_colors = []
            for header_name in the_data['headers']:
                try:
                    item_content = item[header_name]
                except:
                    item_content = ''

                try:
                    item_link = item[header_name + '_link']
                except:
                    item_link = ''

                try:
                    item_color = item[header_name + '_color']
                except:
                    item_color = ''

                row_content.append(item_content)
                row_links.append(item_link)
                row_colors.append(item_color)

            the_data['rows'].append(row_content)
            the_data['links'].append(row_links)
            the_data['color'].append(row_colors)
    else:
        # echo it back out, so that we can see it
        for key in query_items.keys():
            the_data['headers'].append(key)
            the_data['rows'].append([query_items[key]])

    return the_data


# ====================================
def get_upload_files_and_base(filter=''):
    # ====================================
    return_value = []

    if filter:
        found = os.listdir(base_upload_path)
        for item in found:
            if filter in item:
                if not '.md5' in item:
                    return_value.append(item)
    else:
        return_value = os.listdir(base_upload_path)

    return sorted(return_value), base_upload_path


# ====================================
def make_body_GET(environ):
    # ====================================
    return make_get_content(environ)


# ====================================
def make_get_content(environ):
    # ====================================
    delete_permission = permissions.permission_allowed(environ, service + '_delete')
    create_permission = permissions.permission_allowed(environ, service + '_create')
    update_permission = permissions.permission_allowed(environ, service + '_update')
    download_permission = permissions.permission_allowed(environ, service + '_download')

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'filetodownload' in query_items:
        other = {'file': query_items['filetodownload']}
        return '', other
    else:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        try:
            if create_permission:
                body += "<br><br>"
                body += '<center>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<td>'
                body += """
    <form id="upload" name="upload" method=post enctype=multipart/form-data>
    1) Type in the app name to assign to the file:
    <br>
    <input style="font-size:20px;" type="text" size=25 name="input_app_name_string" id="input_app_name_string">
    <br>
    2) Type in a version to assign to the file:
    <br>
    <input style="font-size:20px;" type="text" size=25 name="input_version_string" id="input_version_string">
    <br>
    3) Select (Choose file):
    <br><br>
    <input type=file name=file_to_upload>"""
                body += '</td>'
                body += '<td>'
                body += """
        <input type=submit value=Upload>

        <br><br>
        4) Click the Upload button.

        </form>
        """
                body += '</td>'
                body += '</tr>'
                body += '</table>'
                body += '</center>'
                body += '<br><br>'

            body += '<br><br>'
            body += '<center>'
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            body += 'file'
            body += '</td>'
            body += '<td>'
            body += 'size'
            body += '</td>'
            body += '<td>'
            body += 'download'
            body += '</td>'
            body += '<td>'
            body += 'Available for download/install'
            body += '</td>'
            if delete_permission:
                body += '<td>'
                body += 'Delete option<br>(must not be available for download)'
                body += '</td>'
            body += '<td>'
            body += 'md5'
            body += '</td>'
            body += '<td>'
            body += 'Modified'
            body += '</td>'
            body += '<td>'
            body += 'Days Old'
            body += '</td>'

            body += '</tr>'

            url_to_use = make_home_url_from_environ(environ)

            files, base_to_use = get_upload_files_and_base()
            for file_name in files:
                if not '.md5' in file_name:
                    body += '<tr>'
                    body += '<td>'
                    body += file_name
                    body += '</td>'

                    body += '<td>'
                    body += make_file_size_human_readable(base_to_use + file_name)
                    body += '</td>'

                    is_allowed = False
                    result = datastore.get_value('thirdparty_download_permission_' + file_name)
                    if result == 'Yes':
                        is_allowed = True

                    if is_allowed:
                        the_string = "Yes"
                        next_Value = "No"
                        color = "(0, 255, 0, 0.3)"
                    else:
                        the_string = "No"
                        next_Value = "Yes"
                        color = "(255, 0, 0, 0.3)"

                    body += '<td>'
                    if download_permission:
                        body += '<a href="' + url_to_use + '/thirdparty' + '?filetodownload=' + file_name + '"> ' + 'download' + '</a>'
                    body += '</td>'

                    body += '<td style="background-color:rgba' + color + '">'
                    if update_permission:
                        body += '<form method="post" action="">'
                        body += '<select name="file_download_allowed" id="file_download_allowed" hidden>'
                        body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                        body += '</select>'

                        body += '<select name="file_download_allowed_allowed" id="file_download_allowed_allowed" hidden>'
                        body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                        body += '</select>'
                        body += '<center>'
                        body += '<input type="submit" value="' + the_string + '">'
                        body += '</center>'
                        body += '</form>'
                    else:
                        body += '<center>'
                        body += the_string
                        body += '</center>'

                    body += '</td>'

                    if delete_permission:
                        body += '<td>'
                        if not is_allowed:
                            next_Value = 'delete_it_now'
                            body += '<form method="post" action="">'
                            body += '<select name="file_delete_allowed" id="file_delete_allowed" hidden>'
                            body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                            body += '</select>'

                            body += '<select name="file_delete_allowed_key" id="file_delete_allowed_key" hidden>'
                            body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                            body += '</select>'
                            body += '<center>'
                            body += '<input type="submit" value="' + next_Value + '">'
                            body += '</center>'
                            body += '</form>'
                        body += '</td>'

                    body += '<td>'
                    md5_file = base_to_use + file_name + '.md5'
                    md5_value = ''
                    if os.path.isfile(md5_file):
                        with open(md5_file, 'r') as f:
                            md5_value = f.read()
                    body += md5_value
                    body += '</td>'

                    body += '<td>'
                    days_old = -1
                    try:
                        # file_to_use = '/mnt/disks/SSD/var/log/slicer/upload/files/slideshow.gif'
                        file_to_use = base_to_use + file_name
                        fileStatsObj = os.stat(file_to_use)
                        last_modified_time = fileStatsObj[stat.ST_MTIME]
                        days_old = int((time.time() - last_modified_time) / 60.0 / 60.0 / 24.0)
                        body += datetime.datetime.fromtimestamp(last_modified_time).strftime('%Y.%m.%d')

                    except:
                        body += file_to_use + ' ' + traceback.format_exc().replace("\"", "'")

                    body += '</td>'

                    body += '<td>'
                    body += str(days_old)
                    body += '</td>'

                    body += '</tr>'

            body += '</table>'
            body += '</center>'

        except Exception as e:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

        return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    #    if permissions.permission_prefix_allowed(environ, service + '_'): # or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)

        if 'file' in other:
            file_to_get = other['file']
            zOutFilename = base_upload_path + file_to_get
            size = os.path.getsize(zOutFilename)
            filelike = open(zOutFilename, 'rb')
            block_size = 1024
            # allow non wrapped response
            start_response("200 OK", [('Content-Type', 'application/octet-stream'), ('Content-length', str(size)),
                                      ('Content-Disposition', 'attachment; filename=' + file_to_get)])

            if 'wsgi.file_wrapper' in environ:
                return environ['wsgi.file_wrapper'](filelike, block_size)
            else:
                return iter(lambda: filelike.read(block_size), '')
        else:
            status = other['status']
            head = ''
            if 'head' in other:
                head = other['head']
            response_header = other['response_header']
            if other['add_wrapper']:
                html += '<html>\n'
                if head:
                    html += '<head>\n'
                    html += head
                    html += '</head>\n'
                html += '<body>\n'
            html += body
            if other['add_wrapper']:
                html += '</body>\n'
                html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_thirdparty(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_make_app_storage_filename(self):
        base_upload_path = '/test/'
        application_name = 'appname'
        application_version = '1.0.0'
        expected = '/test/appname_1.0.0'
        actual = make_app_storage_filename(base_upload_path, application_name, application_version)
        self.assertEqual(expected, actual)

# End of source file
