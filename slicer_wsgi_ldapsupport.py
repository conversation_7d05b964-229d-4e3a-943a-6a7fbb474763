# A ldapsupport service for slicer
#
# After saving this file as a new name, replace ldapsupport with the new module name,
#    then, go into the permissions file, and add the module there.

service = "ldapsupport"
version = service + '.0.2.e'

release_notes = """
2023.02.01
ldapsupport.0.2

add live data table

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/ldapsupport.py

using:
sudo vi /var/www/html/ldapsupport.py

It also requires:

sudo vi /etc/httpd/conf.d/python-ldapsupport.conf
----- start copy -----
WSGIScriptAlias /ldapsupport /var/www/html/ldapsupport.py
----- end copy -----

sudo chown apache:apache /var/www/html/ldapsupport.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/ldapsupport-runner
sudo chmod +x /var/www/html/ldapsupport-runner

# ===== begin: start file
#!/usr/bin/env python
import ldapsupport
ldapsupport.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/ldapsupport-runner.service
sudo systemctl daemon-reload
sudo systemctl stop ldapsupport-runner.service
sudo systemctl start ldapsupport-runner.service
sudo systemctl enable ldapsupport-runner.service

systemctl status ldapsupport-runner.service

sudo systemctl restart ldapsupport-runner.service

systemctl --failed

# show general status
systemctl;

# clear failed ones
sudo systemctl reset-failed


# Logging of std out
cat /var/log/syslog | fgrep ldapsupport-runner

OR

tail -f /var/log/syslog | fgrep ldapsupport-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/ldapsupport-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import ldapsupport; print(ldapsupport.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/ldapsupport

https://slicer.cardinalhealth.net/ldapsupport?siteid=PR005

https://slicer.cardinalhealth.net/ldapsupport?serial=100000002a5da842

https://slicer.cardinalhealth.net/ldapsupport?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_ldapsupport


"""

import copy
import hashlib
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import traceback
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ----------------------------
def get_user_group_member_results(user, domain, password, ldap_server, basedn):
    # ----------------------------
    command = 'ldapsearch -xLL -D "' + user + domain + '" -w "' + password + '" -H ' + ldap_server + ' -b "' + basedn + '" "(sAMAccountName=' + user + ')" -o ldif-wrap=no'
    results, fails = do_one_command(command)

    return results


# ----------------------------
def get_user_details_from_search_results(results):
    # ----------------------------
    return_value = {}
    for line in results.split('\n'):
        if ('memberOf:' in line):  # or ('managedObjects: ' in line):
            if 'CN=' in line:
                key = line.split('CN=')[1].split(',')[0]
                return_value[key] = True
    return return_value


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)  # this is where the atomic activity occurs


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1
        try:
            pass


        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write('pass_count : ' + str(pass_count) + ' -> ' + str(
                traceback.format_exc().replace("\n", "<br>").replace("\"",
                                                                     "'")))  # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    #    the_who = login.get_current_user(environ)
    the_who = ''

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # later python 3
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    value_to_use = ''
    if 'the_selection' in d:
        if 'testvalue_text_set' == str(d['the_selection'][0]):
            try:
                if 'testvalue_text' in d:
                    # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                    value_to_use = d['testvalue_text'][0].replace("\\", "").replace("<", "").replace(">", "").replace(
                        "|", "<br>")
                datastore.set_value(service + '_item_' + 'testvalue', value_to_use, who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_data(environ):
    # ====================================

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
    the_type = ''
    if 'type' in query_items:
        the_type = query_items['type']

    if the_type == 'issues':
        live_data = get_live_data()

        the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

        the_data['headers'] = live_data['headers']

        for item in live_data['data']:
            row_content = []
            row_links = []
            row_colors = []
            for header_name in the_data['headers']:
                try:
                    item_content = item[header_name]
                except:
                    item_content = ''

                try:
                    item_link = item[header_name + '_link']
                except:
                    item_link = ''

                try:
                    item_color = item[header_name + '_color']
                except:
                    item_color = ''

                row_content.append(item_content)
                row_links.append(item_link)
                row_colors.append(item_color)

            the_data['rows'].append(row_content)
            the_data['links'].append(row_links)
            the_data['color'].append(row_colors)
    else:
        # echo it back out, so that we can see it
        for key in query_items.keys():
            the_data['headers'].append(key)
            the_data['rows'].append([query_items[key]])

    return the_data


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count

    data_store_content = datastore.all_datastore()

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    get_content = ''
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':
            the_data = make_body_data(environ)

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

            load_url = url_to_use + '/' + service + '?get=data,type=issues'

            live_table_content_d = make_live_table_content(load_url)
            other['head'] = live_table_content_d['head']

            body = ''

            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            #    name_to_show = "Home"
            #    url_to_use = "https://slicer.cardinalhealth.net"
            #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '<center>'
            body += version
            body += '</center>'

            # ------------------------------------
            # Live dashboard view
            # ------------------------------------
            dashboard = ''
            dashboard += live_table_content_d['body']
            dashboard += live_table_content_d['javascript']

            body += dashboard

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    if service == 'temp' + 'late':  # Split the string, so that global replace does not find it when saving this as a new service
        body += '<br><br>'
        body += '<center>'
        body += 'environ'
        body += '<table border="1" cellpadding="5">'
        for item in sorted(environ.keys()):
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            body += '<td>'
            try:
                body += str(environ[item])
            except:
                body += '(na)'
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += 'logged in user'
        body += '<br><br>'
        #        body += str(login.get_current_user(environ))
        body += '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'test1'
        body += '</td>'
        body += '<td>'
        body += 'test2'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'

        # =================================================
        # Configuration Items, with individual submit(s)
        # =================================================
        color_red_warning = "(255, 0, 0, 0.3)"
        color_yellow_caution = "(255, 255, 100, 0.3)"
        color_green = "(0, 255, 0, 0.3)"
        color_clear = "(0, 0, 0, 0.0)"
        color_purple = "(255, 0, 255, 0.3)"

        body_add = ''
        body_add += '<br><br>'
        if permissions.permission_prefix_allowed(environ, service + '_' + 'create'):
            all_d_list = []

            # ---------------
            # with submit
            # ---------------
            d = {}
            d['title'] = 'test value'
            d['option_value'] = "testvalue_text_set"
            d['text_field_name'] = "testvalue_text"
            d['current_value'] = datastore.get_value_stored(data_store_content, service + '_item_' + 'testvalue')
            d['values_to_show'] = None
            d['requires'] = []
            d['text_for_info_box'] = 'Enter a new test value'
            d['color_for_info_box'] = color_clear
            d['submit_name'] = 'update value'
            d['is_development'] = False
            all_d_list.append(copy.deepcopy(d))

            # ---------------
            # with no submit
            # ---------------
            d = {}
            d['title'] = 'test value'
            d['option_value'] = "testvalue_text_set"
            d['text_field_name'] = "testvalue_text"
            d['current_value'] = datastore.get_value_stored(data_store_content, service + '_item_' + 'testvalue')
            d['values_to_show'] = None
            d['requires'] = []
            d['text_for_info_box'] = 'Enter a new test value'
            d['color_for_info_box'] = color_clear
            d['submit_name'] = ''
            d['is_development'] = False
            all_d_list.append(copy.deepcopy(d))

            # ---------------
            # Build the content
            # ---------------
            body_add += '<center>'
            body_add += '<table border="1" cellpadding="5">'

            body_add += '<tr>'
            body_add += '<td>'
            body_add += '<B>Category</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Current<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>New<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>action</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Notes</B>'
            body_add += '</td>'
            body_add += '</tr>'

            for d in all_d_list:
                development_marker = ''
                use_it = True
                submit_name = 'Submit'
                if 'submit_name' in d:
                    submit_name = d['submit_name']

                if 'is_development' in d:
                    use_it = False
                    if d['is_development']:
                        if permissions.permission_allowed(environ, 'development_read'):
                            use_it = True
                            development_marker = '--- Development ---<br>'
                    else:
                        use_it = True

                if use_it:
                    body_add += '<tr>'
                    body_add += '<form method="post" action="">'
                    body_add += '<td>'
                    body_add += development_marker
                    body_add += d['title']
                    body_add += '</td>'

                    current_value = d['current_value']
                    if not current_value:
                        current_value = ''

                    body_add += '<td>'
                    body_add += development_marker
                    body_add += current_value
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker

                    body_add += '<select name="the_selection" id="the_selection" hidden>'
                    body_add += '<option value="' + d['option_value'] + '" selected>' + d['option_value'] + '</option>'
                    body_add += '</select>'
                    if d['values_to_show'] is None:
                        body_add += '<input type="text" size=25 name="' + d[
                            'text_field_name'] + '" value="' + current_value + '\">'
                    else:
                        body_add += '<select name="profile" id="profile">'
                        for key_name in d['values_to_show']:
                            name_to_show = ''
                            if key_name:
                                if 'name_to_show_rule' in d:
                                    if d['name_to_show_rule'] == 'screen_settings':
                                        name_to_show = key_name.split()[0].replace('(', ' (')
                                    else:
                                        name_to_show = key_name
                                else:
                                    name_to_show = key_name

                            if current_value == key_name:
                                body_add += '<option value="' + key_name + '" selected>' + name_to_show + '</option>'
                            else:
                                body_add += '<option value="' + key_name + '">' + name_to_show + '</option>'

                        body_add += '</select>'

                    reported_value = ''
                    if 'reported_value' in d:
                        reported_value = d['reported_value']
                    if reported_value:
                        body_add += '   {last report = ' + reported_value + '}'

                    body_add += ''
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker
                    if submit_name:
                        body_add += '<input type="submit" value="' + submit_name + '">'
                    body_add += '</td>'
                    body_add += '<td style="background-color:rgba' + d['color_for_info_box'] + '">'
                    body_add += development_marker
                    body_add += d['text_for_info_box']
                    body_add += '</td>'

                    body_add += '</form>'
                    body_add += '</tr>'

            body_add += '</table>'
            body_add += '</center>'
        else:
            body_add += '<center>'
            body_add += '!!! No create content table shown !!!' + '<br>'
            body_add += 'Current user does not have ' + service + '_create permissions'
            body_add += '</center>'

        # ---------------
        # add the content
        # ---------------
        body += body_add

        # =================================================
        #
        # =================================================

        body += '<br><br>'
        body += '<br><br>'

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ,
                                             service + '_'):  # or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_ldapsupport(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_get_user_details_from_search_results(self):
        test_command = """

sAMAccountName
mail
userPrincipalName


bind_password = ''
bind_user = '<EMAIL>'
find_user = 'userPrincipalName=<EMAIL>'
find_user = 'mail=<EMAIL>'
find_user = 'mail=<EMAIL>'
find_user = 'mail=<EMAIL>'

find_user = 'sAMAccountName=joel.kortright' # @cordlogistics.com

command_need_pass_user = 'ldapsearch -xLL -D "(user_placeholder)" -w (password_placeholder) -H ldap://WPIL0219ADIDC02.cardinalhealth.net -b "dc=cardinalhealth,dc=net" "((find_placeholder))" -o ldif-wrap=no'
command = command_need_pass_user.replace('(password_placeholder)', bind_password).replace('(user_placeholder)', bind_user).replace('(find_placeholder)', find_user)
command

#import template
results,f = template.do_one_command(command)
get_user_details_from_search_results(results)

sudo python
#import ldapsupport
password = ''
user = 'david.ferguson'
domain = '@cardinalhealth.com'
ldap_server = 'ldap://WPIL0219ADIDC02.cardinalhealth.net'
basedn = 'dc=cardinalhealth,dc=net'
results = ldapsupport.get_user_group_member_results(user, domain, password, ldap_server, basedn)
d = ldapsupport.get_user_details_from_search_results(results)
d.keys()
"""
        results = ''
        expected = {}
        actual = get_user_details_from_search_results(results)
        self.assertEqual(expected, actual)

        results = """
memberOf: CN=A-APM0028269-Slicer-admins,OU=Application Groups,OU=Users and Groups,DC=cardinalhealth,DC=net
"""
        expected = {'A-APM0028269-Slicer-admins': True}
        actual = get_user_details_from_search_results(results)
        self.assertEqual(expected, actual)

        results = """
memberOf: CN=A-APM0028269-Slicer-admins,OU=Application Groups,OU=Users and Groups,DC=cardinalhealth,DC=net
memberOf: CN=A-APM0028269-SlicerRCS-admins,OU=Application Groups,OU=Users and Groups,DC=cardinalhealth,DC=net
"""
        expected = {'A-APM0028269-Slicer-admins': True, 'A-APM0028269-SlicerRCS-admins': True}
        actual = get_user_details_from_search_results(results)
        self.assertEqual(expected, actual)

    def test_slicer_admin(self):
        # add the AD group:
        # https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=b0e2764cdbe2af8073d93a92ba9619d9
        # request: New group
        # domain: Cardinalhealth.net
        # group type: Application Access
        # type: Normal
        # APM ID: APM0028269 (It then finds Slicer)
        # sub group: Slicer
        # type of access: Admins
        # group name (it builds it): A-APM0028269-Slicer-admins
        # description: This will allow an AD group setting for a user, to allow them admin permission inside the Slicer application.
        # owner: Matt.Wreede
        # user accounts: david.ferguson, steven.murphy, matt.wreede

        # made as req: REQ3698888
        pass

# End of source file
