# cah specific items

service = "settings"
version = service + '.cordis.0.4'


# ====================================
def get():
    # ====================================
    return_value = {
        'home_url': 'https://slicer.cordis.com',
        'trust_list': ['mario.sanchez02'],
        'data_drop_base': '/var/www/slicer/log/slicer/',
        'ram_disk_path': '/dev/shm/',
        'datastore_save_path': '/var/log/slicer/',
        'days_to_keep': 4,
        'sites_to_drop': {},
    }

    return_value['login_authentication'] = {
        'authentication_type': 'ldap',
        'user_domain': '@cordis.com',
        'ldap_servers': ['ldap://pjzwdc01.corp.cordis.com', 'ldap://PJZWDC01.CORP.CORDIS.COM', 'ldap://**********'],
    }

    # --------------------
    # Apache settings:
    # --------------------
    # https://stackoverflow.com/questions/44335970/apache-invalid-command-sslengine-perhaps-misspelled-or-defined-by-a-module-n

    files_path = '/var/www/htmlfiles'
    return_value['apache_files_path'] = files_path

    content_80 = """
<VirtualHost *:80>
ServerName slicer.cordis.com
</VirtualHost>
"""

    content_443 = """
<VirtualHost *:443>
ServerName slicer.cordis.com
DocumentRoot """ + files_path + """
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""

    return_value['apache_config_content'] = content_80 + content_443

    return return_value


import time


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

# end of file
