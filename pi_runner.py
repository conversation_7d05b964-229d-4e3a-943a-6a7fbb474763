_ = """
sudo mkdir /cardinal
sudo vi /cardinal/pi_runner.py
cd /cardinal
sudo python3 pi_runner.py install
cat /dev/shm/pi_runner_datadrop.txt


sudo systemctl restart pi-runner.service

sudo systemctl status pi-runner.service

tail -f /var/log/syslog
or
journalctl -u pi-runner.service --no-pager

"""

service = 'runner'
version = 'R.9.11'

release_notes = """
2025.06.05
R.9.11
On start, check if cleanup of old chromium data needs to occur (Supporting Carousel Player)

2025.04.30
R.9.10
Report time since browser start as browser up time.

2025.04.28
R.9.9
Screen report refinement, to reduce false positive 90 and greater reports.

2025.04.11
R.9.8
Option for multiple reboots on a fresh power up, to get screen recognized

2025.03.12
R.9.7
Increment screen grab, even if there are errors on sending the image

2024.12.16
R.9.6
Remove temporary files before writing new ones

2024.11.12
R.9.5
Change where the timezone gets set on startup;
    move from top of main, to inside run_original_main_thread.
Change the time setting to set localtime instead of timezone.

2024.04.18
R.9.4
Make a 'uname' save value, to get sent back to Slicer.
Make a 'bluez' save value, to get sent back to Slicer.
Eliminate popup on browser start.

2024.03.28
R.9.3
Cleanout /etc/localtime on startup

2024.03.19
R.9.2
Adding screen reporting (looking for screen resolution issue, which a reboot might fix)

2024.03.05
R.9.1
Support showgif actually having mp4 content.

2024.02.06
R.9.0
Add the address to the report data

2023.10.30
R.8.9
Handle python3.x(x) environment.

2023.10.19
R.8.8
Allow for more display setting options to be put into the screen config
(max_framebuffer_width, max_framebuffer_height, hdmi_pixel_freq_limit)

2023.08.21
R.8.7
Activity tracking now starts with a one day age, so that a restart does not look like user activity.

2023.08.14
R.8.6
Activity tracking now correctly ignores input devices that are in a non-connected state.

2023.07.10
R.8.5
Handle python3.9 environment.
Only allow configuration changes to apply if we are on a fully managed image.

2023.06.01
R.8.4
Add logging of the last user device that detected activity.
On screen grab, be sure scrot install ok

2023.03.27
R.8.3
Added logging of configuration changes, and reporting of total change count and time of last.

2023.02.15
R.8.2
On update from really old runner, install the needed libraries.

2023.02.07
R.8.1
On a forced browser restart, also restart any corp wifi connections;
to let us load the best certificate

2022.12.12
R.8.0
Get and do runnable commands from the server.

2022.12.06
R.7.8
More debug output.

2022.12.01
R.7.7
Handle the case where multiple call_home locations are given, and a service download is requested.
Increase call home timeout from 15 seconds to 30 seconds.

2022.11.28
R.7.6
Add a timeout on the call to get_cpu_temperature, because it can hang.

2022.10.26
R.7.5
No longer report if Slicer exceptions occur.
Allow screen resolution as a local configuration item, if there is none from a profile.
Allow screen scale as a local configuration item, if we have never been managed. (Matched change in pi_hmi)

2022.09.06
R.7.4
Clear report values on each version change, to know that we are starting fresh.

2022.09.06
R.7.3
When report save value changes, write the previous value to a file for inspection.

2022.09.06
R.7.2
Report which device is being used for network connection.

2022.09.02
R.7.1
Report statistics about the network connection changes.

2022.08.24
R.7.0
Report back the Chromium version.

2022.06.15
R.6.9
If a user input device is removed, then stop the monitoring on that device;
this was causing it to look like a user was active.

2022.05.31
R.6.8
If browser restart is required to accept changes, then write that change to the status file, so that hmi can show it.
Implement the reset_request from Slicer, to be able to remotely reset the browser.

2022.05.16
R.6.7
Report localtime as a full time report.

2022.05.13
R.6.6
Send the full screen_geometry information.

2022.05.09
R.6.5
Protect startup from failing on a bad boot count (file issue).

2022.04.24
R.6.4
Allow profile setting of 'disableincognito' = 'yes' to turn of the incognito mode.

2022.04.21
R.6.3
When restarting network, give it time to re-connect to WiFi.

2022.04.18
R.6.2
For debugging, write the report content to ram disk.

2022.03.08
R.6.1
Report the linux kernel version.

2022.02.10
R.6.0
Publish the user inactivity time as userinactive

2022.02.01
R.5.9
Add support for looking up the call_home_locations
Report on the status of the found services, if they are running ok, or fail.

2022.01.25
R.5.8
Make update to allow runner to be used on really old images.

2022.01.13
R.5.7
Keep a browser start count, to allow hmi service to know when to clear auto refresh settings.

2022.01.10
R.5.6
Manage the showgif content, to enable gif static/slideshow displays.

2022.01.07
R.5.5
Be able to add to the proxy bypass list based on profile content.

2022.01.07
R.5.4
Be able to update hosts file, based on data from the bookmarks. (Helps with DNS issues)

2021.12.29
R.5.3
Be able to set any conf_ configuration value(s) from Slicer. (First is the corp vs IOT wifi).
Send device time in to Slicer.

2021.12.22
R.5.2
Report on the response time for each datadrop report, as an indicator of network health.

2021.12.16
R.5.1
Pick up any shared data items,and pass them along to Slicer in our one minute report.
(This is how we are getting the live data feed from bluetooth logging.)

2021.12.12
R.5.0
Handle updates in priority order (runner last), so that all are completed on a bulk change.

2021.12.12
R.4.4
Handle the network bytes reporting roll over.

2021.12.10
R.4.3
Also report the count of 1K blocks on the disk most used.

2021.12.07
R.4.2
Send new data items: loadavg, disk_use, inode_use

2021.11.23
R.4.1
Make writes of all content be atomic (thread safe).

2021.11.23
R.4.0
Pull information fields from Slicer return data; like ring update count.

2021.10.29
R.3.9
Report wlan1 and eth1 mac addresses

2021.09.24
R.3.8
Monitor network utilisation, and report it to Slicer.

2021.09.23
R.3.7
Implement the "inactivity" measurement.

2021.09.23
R.3.6
Do scrot install in a new way.

2021.09.23
R.3.5
Pickup the browser_timeout value, and save it. Not ready to use it yet)
On screen grab, run the scrot install.
    On systems that already have it, it runs quick, and no change.
    On systems that do not have it, this will put it in place.

2021.09.20
R.3.4
do a once per update action to keep keyboard and mouse (usb) from going to sleep.

2021.09.16
R.3.1
Bump version, just to test pushing runner.

2021.09.09
R.3.0
When making the browser start file for tabs, contain each url in quotes,
so that a & does not get used as a command line character.
Do a correct pull of services from Slicer. (It got broken a while back)

2021.08.27
R.2.4
Add ability to do a screen grab.

2021.08.17
R.2.3
Making changes to correctly get full screen on Dell P2219H

2021.08.14
R.2.2
Set firewall rules (iptables) on startup, but still wide open for right now
Allow support for disabling kiosk mode on the browser.
Add delay at start, to get screen size settled

2021.07.20
R.2.1
Also send uptime in the checkin, to provide a faster update of that value in Slicer.
Create a boot_count, and relay that to Slicer.
React to a matching image:boot_count value of a reboot_request, and do a reboot.
Through bookmarks, allow the setting of allowing all keys to be active. ("allowkeys")

2021.07.12
R.2.0
Pull out special_menu setting
Do the pull of browser tab mode, and manage the start content, and browser restart.

2021.07.07
R.1.9
On screen_resolution change, build new settings, and reboot.

2021.06.23
R.1.8
On timezone change, restart the hmi process, so that it gets the new local time setting.

2021.06.22
R.1.7
Process the privoxy log, to reduce it to a report.
Pick up the clockview enable value from Slicer, and save it locally.

2021.06.14
R.1.6
Pick up the bluetooth enable value from Slicer, and save it locally.

2021.05.28
R.1.5
Send the screen width and height with the datadrop.

2021.05.27
R.1.4
Extract the new field 'browser_zoom' from the datadrop response.

2021.05.25
R.1.3
Extract the new field 'name_to_use' from the datadrop response.

2021.05.19
R.1.2
Include (add) in datadrop:
    eth0mac => eth0/wired mac address
    timezone => the (hopefully) three letter time zone identifier

Actions:
    Set the timezone
        Once, set timezone to the saved value. If none, then set to UTC.
        When datadrop returns content, set to the datadrop return value. If none, then set to UTC.

2021.05.15
R.1.1
Do the bookmarks update here now.

2021.05.13
R.1.0
Start building out datadrop, as the communications channel to Slicer.

2021.04.27
R.0.9
Move the landing page creation to the hmi service

2021.04.14
R.0.8
Start building a landing page for the pi to display on startup:
    - show serial number (ID and IDr)

2021.03.13
R.0.7
Write a wake count, so that others can know that I am still alive.

R.0.6
Roll the version, just to test that monitor will re-send based on my version changing.

R.0.5
Convert sleep from 60 to 1.
Write version string for others to find.

R.0.4
Now made as a stand alone from the service.
"""

_files_touched = """

/dev/shm/(many)

if_controlled_image:
    /boot/config.txt
        - screen resolution settings

    /etc/hostname

"""

screen_stream_notes = """
2022.01.04

Use scrot to take a screen grab, and send to slicer as a 'last_frame'.
On Slicer, store that in /dev/shm/last_frame/(serial)
Open a Slicer page, that has javascript to pull that last_frame file, and display it. (update on change)
Having that Slicer window open is what alerts Slicer to mark the device for streaming.
Maybe the streamer runs as a separate service, and has an open websocket to talk to Slicer.

https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API
https://www.fullstackpython.com/websockets.html

Tornado: https://www.tornadoweb.org/en/stable/
https://stackoverflow.com/questions/13471115/how-to-handle-a-request-with-https-protocol-in-tornado


"""

_WiFi_connection_stats = """
nmcli -f name,UUID,TIMESTAMP,TIMESTAMP-REAL c



NAME UUID TIMESTAMP TIMESTAMP-REAL
corp0 f0c05766-58ba-4b25-aef2-bbaeb1b11fae 1662061505 Thu 01 Sep 2022 19:45:05 UTC


nmcli -f UUID,name,TIMESTAMP,TIMESTAMP-REAL c

UUID                                  NAME  TIMESTAMP   TIMESTAMP-REAL
45aee000-7dbe-4c1c-85b3-494167837f70  eth0  1662123684  Fri 02 Sep 2022 09:01:24 EDT

----------------

 sudo cat /var/lib/NetworkManager/timestamps

[timestamps]
f3aedfa4-d45b-3e23-a56b-babd3d99b747=1638197105
f0c05766-58ba-4b25-aef2-bbaeb1b11fae=1661774411



1661774411 is from when the device booted, 3 days ago.

----------------

 cah-pi-su@cah-rp-100000000e3c804f:~ $ sudo systemctl restart pi-network.service
cah-pi-su@cah-rp-100000000e3c804f:~ $
cah-pi-su@cah-rp-100000000e3c804f:~ $ sudo cat /var/lib/NetworkManager/timestamps
[timestamps]
f3aedfa4-d45b-3e23-a56b-babd3d99b747=1638197105
532cdaa9-6934-4851-bc00-bec47df20140=1662062616
cah-pi-su@cah-rp-100000000e3c804f:~ $ nmcli -f name,UUID,TIMESTAMP,TIMESTAMP-REAL c
NAME UUID TIMESTAMP TIMESTAMP-REAL
corp0 532cdaa9-6934-4851-bc00-bec47df20140 1662062616 Thu 01 Sep 2022 20:03:36 UTC
cah-pi-su@cah-rp-100000000e3c804f:~ $ date
Thu 1 Sep 20:03:56 UTC 2022

(Reconnection pulled timestamp to current time, and there is a new UUID for the connection)

So, I should be able to just increment a count anytime the UUID changes, to capture that
a new connection happened.

----------------

"""

description = """
This is a service to perform the audits on the device, and to report
findings.

This also performs updates to what is running, and can perform updates to make the
device reach "current release" in place.

Have runner do in place upgrade to 2.x based on if the version string is not yet 2.x.
    Install all apt-get and pip installs.
    Pull code from Slicer, install
    configure auto start for Chromium
    set new version string
    reboot


"""

other_content = """
sudo vi /cardinal/pi-runner
sudo chmod +x /cardinal/pi-runner

# ===== begin: start file
#!/usr/bin/env python3
import pi_runner
pi_runner.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-runner.service
sudo systemctl daemon-reload
sudo systemctl stop pi-runner.service
sudo systemctl start pi-runner.service
sudo systemctl enable pi-runner.service

systemctl status pi-runner.service

sudo systemctl restart pi-runner.service

# Logging of std out
cat /var/log/syslog | fgrep pi-runner

OR

sudo tail -n 1000 -f /var/log/syslog | fgrep pi-runner

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-runner
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_runner


"""

_system_loading = """
sudo apt install glances -y
glances

Shows full command line start of chromium is 100% a lot of the time.

How to get that, and report, from runner?


"""

_disable_ipv6 = """
# https://www.howtoraspberry.com/2020/04/disable-ipv6-on-raspberry-pi/

sudo vi /etc/sysctl.conf

net.ipv6.conf.all.disable_ipv6 = 1
net.ipv6.conf.default.disable_ipv6 = 1
net.ipv6.conf.lo.disable_ipv6 = 1

#save

# run
sudo sysctl -p

# confirm
cat /proc/sys/net/ipv6/conf/all/disable_ipv6

# should get 1


"""

_screen_resolution = """
tvservice -s

Good:
state 0xa [HDMI CUSTOM RGB lim 16:9], 3840x2160 @ 30.00Hz, progressive

Powered on without display connected:
state 0x6 [DVI CUSTOM RGB full 4:3], 1024x768 @ 60.00Hz, progressive

Plug hdmi into the port: (overfilled)
state 0x6 [DVI CUSTOM RGB full 4:3], 1024x768 @ 60.00Hz, progressive

Reboot 1: (small upper left)
state 0xa [HDMI CUSTOM RGB lim 16:9], 3840x2160 @ 30.00Hz, progressive

Reboot2:
state 0xa [HDMI CUSTOM RGB lim 16:9], 3840x2160 @ 30.00Hz, progressive

"""

import copy
import json
import math
import os

try:
    import requests
except:
    pass  # for unittest to work on mac

import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

from concurrent.futures import ThreadPoolExecutor

s_executor = ThreadPoolExecutor(100)
# line up all of the worker threads
s_monitored_devices = {}
s_monitored_device_last_active = ''
s_time_last_active = time.time() - 60 * 60 * 24  # make this start as a really old time, so that we do not mistake a restart as user activity
s_activity_since_reset = False
s_previous_report = {'response_time': 0.0, 'exception_count': 0}

s_save_values_location = '/cardinal/save_values/' + service + '_' + version + '_'

s_runner_wake_file = '/dev/shm/pi_runner_wake.txt'
s_call_home_timeout = 30.0  # 2022.12.02 before this it was 15, but new servers are further away, and were timing out.

s_runnable_work_path = '/dev/shm/pi_runner_work/'

s_configuration_change_logs = '/cardinal/config_changes/'

s_boot_tracker_file = '/cardinal/boot_tracker.txt'

# ----------------------------
def clean_uname(uname):
    # ----------------------------
    just_the_savers = ' '.join(uname.split(' ')[2:])
    return just_the_savers.replace(' ', '_').replace('#', '_')


# ----------------------------
def find_sums_tail_length(sums):
    # ----------------------------
    return_value = 0

    for row in range(len(sums) - 1, -1, -1):
        if sums[row] == 0:
            return_value += 1
        else:
            break

    return return_value


# ----------------------------
def set_timezone(time_zone, the_who=''):
    # ----------------------------
    new = True

    set_timezone_report = '\n' + the_who + '\n'

    if new:
        _notes = """

watch -n 1 "date; cat /dev/shm/pi_runner_wake.txt; echo '\n'; echo 'boot '; cat /cardinal/boot_count.txt; echo '\n'; cat /cardinal/localhtml/timezone; echo '\n'; ls -l /etc/localtime; echo '\n'; cat /dev/shm/set_timezone_report.txt"
zdump -v /usr/share/zoneinfo/America/Chihuahua
"""

        try:
            # https://forums.raspberrypi.com/viewtopic.php?t=4977
            # clean out any left over timezone setting
            os.remove('/etc/timezone')
            set_timezone_report += 'removed /etc/timezone' + '\n'
        except:
            set_timezone_report += 'already gone /etc/timezone' + '\n'
            pass

        try:
            the_command = 'sudo ln -sfn /usr/share/zoneinfo/' + time_zone + ' ' + '/etc/localtime'
            pass_string, fail_string = do_one_command(the_command)
            set_timezone_report += the_command + '\n'
            set_timezone_report += pass_string + '\n'
            set_timezone_report += fail_string + '\n'
        except:
            set_timezone_report += 'set timezone exception' + '\n'

    else:
        try:
            # https://forums.raspberrypi.com/viewtopic.php?t=4977
            # clean out any left over timezone setting
            os.remove('/etc/localtime')
            set_timezone_report += 'removed /etc/localtime' + '\n'
        except:
            set_timezone_report += 'already gone /etc/localtime' + '\n'
            pass

        try:
            # set it
            # get options from 'timedatectl list-timezones | tee'
            the_command = make_timezone_command(time_zone)
            pass_string, fail_string = do_one_command(the_command)
            set_timezone_report += the_command + '\n'
            set_timezone_report += pass_string + '\n'
            set_timezone_report += fail_string + '\n'
        except:
            set_timezone_report += 'set timezone exception' + '\n'

    the_command = 'sudo systemctl restart ' + 'pi-hmi.service'
    try:
        do_one_command(the_command)
        set_timezone_report += the_command + '\n'
        set_timezone_report += pass_string + '\n'
        set_timezone_report += fail_string + '\n'
    except:
        set_timezone_report += 'exception on: ' + the_command + '\n'
        pass

    open('/dev/shm/set_timezone_report.txt', 'w').write(set_timezone_report)

# ----------------------------
def make_timezone_command(time_zone):
    # ----------------------------
    return 'sudo timedatectl set-timezone ' + time_zone


# ----------------------------
def write_show_html():
    # ----------------------------
    local_file = '/cardinal/localhtml/showgif.gif'
    header = read_header_from_file(local_file)
    file_type = determine_file_type_from_header(header)
    make_show_html(file_type)


# ----------------------------
def read_header_from_file(the_file):
    # ----------------------------
    return_value = []
    try:
        f = open(the_file, 'rb')
        return_value = f.read(30)
        f.close()
    except:
        pass
    return return_value


# ----------------------------
def determine_file_type_from_header(header):
    # ----------------------------
    # https://en.wikipedia.org/wiki/List_of_file_signatures
    file_type = 'unknown'
    if b'JFIF' in header:
        file_type = 'jpeg'
    elif b'GIF' in header:
        file_type = 'gif'
    elif b'PNG' in header:
        file_type = 'png'
    elif b'Exif' in header:
        file_type = 'jpg'
    elif b'ftyp' in header:
        file_type = 'mp4'
    return file_type


# ----------------------------
def my_best_address():
    # ----------------------------
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        #        s.connect(("*******", 80))
        # https://stackoverflow.com/questions/166506/finding-local-ip-addresses-using-pythons-stdlib
        s.connect(("**************", 1))  # does not need to be valid, and opens as UDP
        my_best = s.getsockname()[0]
        s.close()
    except:
        my_best = ''
    return my_best


# ----------------------------
def convert_config_values_from_resolution(resolution):
    # ----------------------------
    content = ''
    # resolution = name group mode valuesCommaSeparated
    # resolution = 1280x720r60 2 87 1280,720,60
    # resolution = VGA 1 1

    if resolution:
        group = resolution.split()[1]
        mode = resolution.split()[2]
        try:
            hdmi_cvt_values = resolution.split()[3].replace(',', ' ')
        except:
            hdmi_cvt_values = ''
        try:
            framebuffer_width_values = resolution.split()[4]
        except:
            framebuffer_width_values = ''
        try:
            framebuffer_height_values = resolution.split()[5]
        except:
            framebuffer_height_values = ''
        try:
            freq_limit_values = resolution.split()[6]
        except:
            freq_limit_values = ''

        content += "hdmi_group=" + group + "\n"
        content += "hdmi_mode=" + mode + "\n"
        if hdmi_cvt_values:
            content += "hdmi_cvt " + hdmi_cvt_values + "\n"
        if framebuffer_width_values:
            content += "max_framebuffer_width=" + framebuffer_width_values + "\n"
        if framebuffer_height_values:
            content += "max_framebuffer_height=" + framebuffer_height_values + "\n"
        if freq_limit_values:
            content += "hdmi_pixel_freq_limit=" + freq_limit_values + "\n"

    return content


# ----------------------------
def is_controlled_image(cardinal_dir):
    # ----------------------------
    if 'image_ver.txt' in cardinal_dir:
        return True
    else:
        return False


# ----------------------------
def get_item_from_network_query(pass_string, item='UUID'):
    # ----------------------------
    return_value = ''

    try:
        line_one_splits = pass_string.split('\n')[0].split()
        line_two_splits = pass_string.split('\n')[1].split()

        for index in range(0, len(line_one_splits)):
            if line_one_splits[index] == item:
                return_value = line_two_splits[index]
    except:
        pass

    return return_value


# ----------------------------
def get_saved_value(item):
    # ----------------------------
    return_value = ''
    output_file = s_save_values_location + item
    try:
        return_value = open(output_file, 'r').read()
    except:
        pass

    return return_value


# ----------------------------
def set_saved_value(item, content):
    # ----------------------------
    output_file = s_save_values_location + item
    previous_value_file = output_file + '.previous'
    return write_content_if_different(str(content), output_file, previous_value_file=previous_value_file)


# ----------------------------
def get_browser_start_marker():
    # ----------------------------
    return_value = ''
    try:
        return_value = open('/dev/shm/browser_start_marker', 'r').read()
    except:
        pass
    return return_value


# ----------------------------
def set_browser_restart_setting(content, help_content=''):
    # ----------------------------
    try:
        open('/dev/shm/browser_restart_setting', 'w').write(content)
        open('/dev/shm/browser_restart_setting_help', 'w').write(help_content)
    except:
        pass


# ----------------------------
def get_browser_restart_setting():
    # ----------------------------
    try:
        return open('/dev/shm/browser_restart_setting', 'r').read()
    except:
        return ''


# ----------------------------
def save_shared_counts(the_counts):
    # ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_logging_' + the_count
            temp_name = file_name.replace('shared_', 'temp_')
            with open(temp_name, 'w') as f:
                f.write(str(the_counts[the_count]))
            shutil.move(temp_name, file_name)
        except:
            pass


# ----------------------------
def write_content_if_different(content, output_file, with_execute=False, previous_value_file=''):
    # ----------------------------
    return_true_if_different = False
    # This does a temp file, then atomic move to final
    if content:
        last_content_written = ''
        last_content_source = 'default'
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        if os.path.isfile(output_file):
            with open(output_file, 'r') as f:
                last_content_written = f.read()
                last_content_source = 'read_from_file'

        if content != last_content_written:
            temp_file = output_file + '.tmp'  # must be on the same physical device as the destination, to be atomic on mv
            pass_string, fails = do_one_command('sudo rm ' + temp_file)
            with open(temp_file, 'w') as f:
                f.write(content)

            print("updating file: " + output_file)
            if with_execute:
                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)

            pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
            pass_string, fails = do_one_command('sudo mv ' + temp_file + ' ' + output_file)
            return_true_if_different = True

            if previous_value_file:
                try:
                    with open(previous_value_file, 'w') as f:
                        f.write(last_content_source + '\n' + last_content_written)
                except:
                    pass

    return return_true_if_different


# ----------------------------
def read_file(filename):
    # ----------------------------
    # The idea is that the read call will block until there is data
    try:
        with open(filename, 'rb') as f:
            return f.read(3)
    except:
        # OSError: [Errno 19] No such device
        return None


# ----------------------------
def get_network_use_values():
    # ----------------------------

    pass_string = """
eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
        inet **************  netmask 255.255.255.224  broadcast **************
        inet6 fe80::a197:aeb9:2858:d886  prefixlen 64  scopeid 0x20<link>
        ether dc:a6:32:96:56:c2  txqueuelen 1000  (Ethernet)
        RX packets 453108  bytes 223459013 (213.1 MiB)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 457509  bytes 57905346 (55.2 MiB)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0

lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
        inet 127.0.0.1  netmask *********
        inet6 ::1  prefixlen 128  scopeid 0x10<host>
        loop  txqueuelen 1000  (Local Loopback)
        RX packets 376332  bytes 250601806 (238.9 MiB)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 376332  bytes 250601806 (238.9 MiB)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0

wlan0: flags=4099<UP,BROADCAST,MULTICAST>  mtu 1500
        ether dc:a6:32:96:56:c4  txqueuelen 1000  (Ethernet)
        RX packets 0  bytes 0 (0.0 B)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 0  bytes 0 (0.0 B)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
    """

    _test = """
sudo python3
import pi_runner
pi_runner.get_network_use_values()
    """

    pass_string, fail_string = do_one_command('ifconfig')
    # print (pass_string)
    # print (fail_string)

    results = {'rxbytes': 0, 'txbytes': 0, 'time': time.time()}
    in_zone = False
    for line_read in pass_string.split('\n'):
        # print ('in_zone', in_zone)
        if ('eth0' in line_read) or ('wlan0' in line_read):
            in_zone = True
        if ('lo:' in line_read):
            in_zone = False
        if in_zone:
            # print ('line_read', line_read)
            if 'RX packets' in line_read:
                try:
                    bytes_shown = int(line_read.strip().split()[4])
                    results['rxbytes'] += bytes_shown
                except:
                    pass
            if 'TX packets' in line_read:
                try:
                    bytes_shown = int(line_read.strip().split()[4])
                    results['txbytes'] += bytes_shown
                except:
                    pass

    return results


# ----------------------------
def get_network_utilization_report():
    # ----------------------------
    return_value = ''

    save_file = '/dev/shm/pi_runner_network_utilization_last'

    try:
        with open(save_file, 'r') as f:
            previous_values = json.loads(f.read())
    except:
        previous_values = None

    current_values = get_network_use_values()
    try:
        with open(save_file, 'w') as f:
            f.write(json.dumps(current_values))
    except:
        pass

    reportables = []

    if previous_values:
        try:
            delta_rx = current_values['rxbytes'] - previous_values['rxbytes']
            delta_tx = current_values['txbytes'] - previous_values['txbytes']

            # ignore the roll over
            if delta_rx < 0:
                delta_rx = 0
            if delta_tx < 0:
                delta_tx = 0

            reportables.append(str(delta_rx))
            reportables.append(str(delta_tx))
            reportables.append(str(int(current_values['time'] - previous_values['time'])))
        except:
            pass

    for item in reportables:
        return_value += '(' + str(item) + ')'

    return return_value


# ----------------------------
def get_image_version():
    # ----------------------------
    image_version = "(missing)"
    try:
        with open('/cardinal/image_ver.txt', 'r') as f:
            content = f.read()
            # "Image version: 2.0.4"
        image_version = content.split(":")[1].strip()
    except:
        do_datadrop_debug(traceback.format_exc())

    return image_version


# ----------------------------
def get_boot_count():
    # ----------------------------
    boot_count = '0'
    try:
        with open("/cardinal/boot_count.txt", "r") as f:
            boot_count = f.read()
    except:
        pass

    return boot_count


# ----------------------------
def get_grab_count():
    # ----------------------------
    grab_count = '0'
    try:
        with open("/cardinal/grab_count.txt", "r") as f:
            grab_count = f.read()
    except:
        pass

    return grab_count


# ----------------------------
def increment_boot_count():
    # ----------------------------
    output_file = "/cardinal/boot_count.txt"

    current_count = get_boot_count()
    try:
        new_count = str(int(current_count) + 1)
    except:
        # we have seen failures some times (empty file, and file with three nulls), so protect against it
        new_count = '0'

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass


# ----------------------------
def increment_grab_count():
    # ----------------------------
    output_file = "/cardinal/grab_count.txt"
    current_count = get_grab_count()
    new_count = str(int(current_count) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass


# ----------------------------
def get_highest_disk_usage():
    # ----------------------------
    pass_string, fails = do_one_command('df -k')

    _ = """
Filesystem     1K-blocks    Used Available Use% Mounted on
/dev/root       14734612 2584796  11531888  19% /
devtmpfs         1800564       0   1800564   0% /dev
tmpfs            1965428   12456   1952972   1% /dev/shm
tmpfs            1965428  205632   1759796  11% /run
tmpfs               5120       4      5116   1% /run/lock
tmpfs            1965428       0   1965428   0% /sys/fs/cgroup
/dev/mmcblk0p1    258095   50227    207868  20% /boot
tmpfs             393084       0    393084   0% /run/user/1002
tmpfs             393084       0    393084   0% /run/user/1001
    """

    return_value = 0
    one_k_blocks = 0
    lines = pass_string.split('\n')
    for line in lines:
        splits = line.split()
        if len(splits) > 4:
            try:
                use_value = int(splits[4].replace('%', ''))
                if use_value > return_value:
                    return_value = use_value
                    one_k_blocks = int(splits[1])
            except:
                pass

    return {'percent_used': return_value, 'one_k_blocks': one_k_blocks}


# ----------------------------
def get_highest_inode_usage():
    # ----------------------------
    pass_string, fails = do_one_command('df -ih')

    _ = """
Filesystem     Inodes IUsed IFree IUse% Mounted on
/dev/root        912K   68K  844K    8% /
devtmpfs          83K   431   82K    1% /dev
tmpfs            163K    78  163K    1% /dev/shm
tmpfs            163K   639  163K    1% /run
tmpfs            163K     3  163K    1% /run/lock
tmpfs            163K    15  163K    1% /sys/fs/cgroup
/dev/mmcblk0p1      0     0     0     - /boot
tmpfs            163K    11  163K    1% /run/user/1002
tmpfs            163K    11  163K    1% /run/user/1001
    """

    return_value = 0
    lines = pass_string.split('\n')
    for line in lines:
        splits = line.split()
        if len(splits) > 4:
            try:
                use_value = int(splits[4].replace('%', ''))
                if use_value > return_value:
                    return_value = use_value
            except:
                pass

    return return_value


# ----------------------------
def chromium_version():
    # ----------------------------
    # chromium-browser --version
    try:
        pass_string, fail_string = do_one_command('chromium-browser --version')
        # pass_string = 'Chromium 92.0.4515.98 Built on Raspbian , running on Raspbian 10\n'
        # fail_string = 'dpkg-query: no packages found matching bluealsa\n'
        # our SD does not have a label, which is what the failure is about, so ignore it
        return pass_string.split()[1]
    except:
        return '(0.0.0.0)'


# ----------------------------
def get_uptime():
    # ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0


# ----------------------------
def loadavg():
    # ----------------------------
    try:
        with open('/proc/loadavg', 'r') as f:
            return (f.readline().replace(' ', '_').replace('\n', ''))
    except:
        return ''


# ----------------------------
def build_boot_config():
    # ----------------------------
    content = ""

    if True:
        # Original defaults
        content += "dtparam=audio=on" + "\n"
    else:
        # 2024.04.22 To try to get new OS (Bookworm) to play hdmi sound on pi4
        content += "#dtparam=audio=off" + "\n"

    # ones we opened up
    content += "disable_overscan=1" + "\n"
    content += "hdmi_force_hotplug=1" + "\n"
    content += "" + "\n"

    # dynamically set items
    try:
        resolution = get_my_screen_resolution()
        content += convert_config_values_from_resolution(resolution)
    except:
        pass

    # sectioned items after here
    content += "[pi4]" + "\n"
    content += "dtoverlay=vc4-fkms-v3d" + "\n"
    content += "max_framebuffers=2" + "\n"

    try:
        with open('/boot/config.txt', 'w') as f:
            f.write(content)
    except:
        pass


# ----------------------------
def do_privoxy_summary():
    # ----------------------------

    """
Privoxy log:
/var/log/privoxy/logfile

(It will get shorter at times, when it rolls)

pi@cah-rp-10000000e3669edf:~ $ sudo ls -l /var/log/privoxy
total 80
-rw-r----- 1 <USER> <GROUP>     52635 Jun 21 14:50 logfile
-rw-r--r-- 1 <USER> <GROUP> 23719 Jun 19 17:44 logfile.1.gz
pi@cah-rp-10000000e3669edf:~ $


2021-06-21 14:46:08.852 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:47:11.861 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:47:11.863 b62ff460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:14.862 b62ff460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:14.862 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:35.883 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:36.757 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:38.569 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:42.324 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443

with debug 1 set, you can also get:
2021-06-21 14:48:34.684 b6c89460 Request: mex03-dms.cardinalhealth.net:443/
2021-06-21 14:48:35.070 b62ff460 Request: cdn.jsdelivr.net:443/
2021-06-21 14:48:35.082 b58ff460 Request: cdn.datatables.net:443/
2021-06-21 14:48:35.087 b44ff460 Request: code.jquery.com:443/
2021-06-21 14:48:35.088 b3cfe460 Request: cdn.jsdelivr.net:443/
2021-06-21 14:48:35.089 b4eff460 Request: cdnjs.cloudflare.com:443/
2021-06-21 14:48:35.090 b30ff460 Request: cdn.datatables.net:443/
2021-06-21 14:48:35.091 b28fe460 Request: cdnjs.cloudflare.com:443/

cat /dev/shm/pi_runner_privoxy_summary.txt

"""

    lines = []
    try:
        with open('/var/log/privoxy/logfile', 'r') as f:
            lines = f.readlines()
    except:
        pass

    to_ignores = []
    to_ignores.append('www.google.com')
    to_ignores.append('content-autofill.googleapis.com')
    to_ignores.append('update.googleapis.com')
    to_ignores.append('safebrowsing.googleapis.com')
    to_ignores.append('fonts.googleapis.com')
    to_ignores.append('www.gstatic.com')
    to_ignores.append('accounts.google.com')
    to_ignores.append('support.google.com')
    to_ignores.append(
        'redirector.gvt1.com')  # https://www.bleepingcomputer.com/news/security/what-are-these-suspicious-google-gvt1com-urls/
    to_ignores.append('ssl.gstatic.com')  # Google static content
    # to_ignores.append('maxcdn.bootstrapcdn.com')

    report = {}
    for line_read in lines:
        if 'Blocked' in line_read:
            site = line_read.split()[-1]

            should_ignore = False

            # formatting like http://tfnkklroxbragen/
            if not '.' in site:
                should_ignore = True

            # explicit list
            for to_ignore in to_ignores:
                if to_ignore in site:
                    should_ignore = True

            if not should_ignore:
                if not site in report:
                    report[site] = 0
                report[site] += 1

    try:
        with open('/dev/shm/pi_runner_privoxy_summary.txt', 'w') as f:
            f.write(json.dumps(report, indent=4, separators=(',', ':')))
    except:
        pass


# ----------------------------
def extract_screen_geometry_from_fbset_pass(fbset_pass):
    # ----------------------------
    # make a single string, as the reportable up to Slicer
    return_value = ''
    for line in fbset_pass.split('\n'):
        if 'geometry' in line:
            return_value = line.split('geometry')[1].strip().replace(' ', '/')

    return return_value


# ----------------------------
def get_screen_size():
    # ----------------------------
    screen_width = '1920'
    screen_height = '1080'
    try:
        pass_string, fails = do_one_command('fbset -s')
        # pass_string = '\nmode "1280x800"\n    geometry 1280 800 1280 800 16\n    timings 0 0 0 0 0 0 0\n    accel true\n    rgba 5/11,6/5,5/0,0/0\nendmode\n\n'

        _ = """
cah-pi-su@cah-rp-10000000db1e87a5:~ $ fbset -s

mode "640x480"
    geometry 640 480 3840 2160 16
    timings 0 0 0 0 0 0 0
    accel true
    rgba 5/11,6/5,5/0,0/0
endmode

geometry is screen resolution and desktop resolution

              --geometry, -g ...
                     set  all geometry parameters at once in the order <xres> <yres> <vxres> <vyres>
                     <depth>, e.g.  -g 640 400 640 400 4

Maybe just report screen_geometry as the entire list shown?

        """

        splits = pass_string.split('\n')
        for item in splits:
            # print ('item', item)
            parts = item.strip().split()
            # print ('parts', parts)
            if parts:
                if parts[0] == 'geometry':
                    screen_width = int(parts[1])
                    screen_height = int(parts[2])
                    # print (screen_width, screen_height)
    except:
        pass

    return screen_width, screen_height

# ----------------------------
def get_my_extra_boots_count():
    # ----------------------------
    input_file = "/cardinal/localhtml/conf_extra_boots"

    return_value = 0
    try:
        if os.path.isfile(input_file):
            with open(input_file, 'r') as f:
                config_full = json.loads(f.read())
            serial = get_serial()
            if serial in config_full:
                try:
                    return_value = int(config_full[serial])
                except:
                    pass
    except:
        pass

    return return_value

# ----------------------------
def get_my_screen_resolution():
    # ----------------------------
    input_file = "/cardinal/localhtml/screen_resolution"

    return_value = ''
    try:
        if os.path.isfile(input_file):
            with open(input_file, 'r') as f:
                config_full = json.loads(f.read())
            serial = get_serial()
            if serial in config_full:
                return_value = copy.deepcopy(config_full[serial])
        else:
            local_configuration = '/cardinal/config_resolution.txt'
            if os.path.isfile(local_configuration):
                return_value = open(local_configuration, 'r').read()
    except:
        pass

    return return_value


# ----------------------------
def get_my_special_menu():
    # ----------------------------
    input_file = "/cardinal/localhtml/special_menu"

    return_value = ''
    try:
        with open(input_file, 'r') as f:
            config_full = json.loads(f.read())
        serial = get_serial()
        if serial in config_full:
            return_value = copy.deepcopy(config_full[serial])
    except:
        pass

    return return_value


# ----------------------------
def get_my_browser_timeout():
    # ----------------------------
    input_file = "/cardinal/localhtml/browser_timeout"
    browser_timeout = '0'
    try:
        with open(input_file, 'r') as f:
            content_full = json.loads(f.read())
        serial = get_serial()
        if serial in content_full:
            browser_timeout = copy.deepcopy(content_full[serial])
    except:
        pass

    return browser_timeout


# ----------------------------
def get_my_bookmarks():
    # ----------------------------
    input_file = "/cardinal/localhtml/bookmarks"
    bookmarks = {}
    try:
        with open(input_file, 'r') as f:
            bookmarks_full = json.loads(f.read())
        serial = get_serial()
        if serial in bookmarks_full:
            bookmarks = copy.deepcopy(bookmarks_full[serial])

    except:
        pass

    return bookmarks


# ----------------------------
def check_for_inactivity():
    # ----------------------------
    global s_monitored_devices, s_time_last_active, s_activity_since_reset, s_monitored_device_last_active

    browser_timeout_minutes = get_my_browser_timeout()

    try:
        with open('/dev/shm/pi_runner_browser_timeout', 'w') as f:
            f.write(str(browser_timeout_minutes))
    except:
        pass

    try:
        # see if there are new devices to monitor
        device_dir = '/dev/input/'
        devices_found = os.listdir(device_dir)
        for device_name in devices_found:
            if ('event' in device_name) or ('mouse' in device_name):
                full_device_path = device_dir + device_name
                if not full_device_path in s_monitored_devices:
                    s_monitored_devices[full_device_path] = {'executor': None,
                                                             'is_done': True}  # Make the arrival of the new device be an activity.

                    # job_id_thread1 = sched.add_job(thread_call_back_to_slicer, 'interval', seconds=30, coalesce=True)
                    # https://thehackerdiary.wordpress.com/tag/devinput-python/
                    # https://stackoverflow.com/questions/39948588/non-blocking-file-read/39948643

        # See if any devices disappeared, and remove them from monitoring
        for monitored_device in s_monitored_devices:
            device_name = monitored_device.replace(device_dir, '')
            if not device_name in devices_found:
                # not found, so kill the thread (if it was still running), and then remove the device

                try:
                    s_monitored_devices[monitored_device]['executor'].cancel(True)
                except:
                    pass
                del s_monitored_devices[monitored_device]

        # run through all the monitored devices, and if they are done, start a new executor
        any_active = False
        report_what = ''
        for monitored_device in sorted(s_monitored_devices):
            if s_monitored_devices[monitored_device]['executor']:
                s_monitored_devices[monitored_device]['is_done'] = s_monitored_devices[monitored_device][
                    'executor'].done()

            if s_monitored_devices[monitored_device]['is_done']:
                if not s_monitored_devices[monitored_device]['executor'] is None:  # ignore the initialization pass
                    the_result = s_monitored_devices[monitored_device]['executor'].result()

                    if the_result is None:
                        # this was a failed read, so do not call this active
                        pass
                    else:
                        report_what += monitored_device + ' -> active' + '\n'
                        any_active = True
                        s_monitored_device_last_active = monitored_device.replace(device_dir, '')

                # set up the first/next run of the executor
                try:
                    del (s_monitored_devices[monitored_device]['executor'])
                except:
                    with open('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
                        f.write(traceback.format_exc())

                try:
                    s_monitored_devices[monitored_device]['executor'] = s_executor.submit(read_file, monitored_device)
                except:
                    with open('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
                        f.write(traceback.format_exc())
            else:
                report_what += monitored_device + ' -> inactive' + '\n'

        if any_active:
            s_time_last_active = time.time()
            s_activity_since_reset = True

        delta_time = time.time() - s_time_last_active

        # tack on some extra periodic testing here
        try:
            command = 'uname -a'
            p, f = do_one_command(command)
            save_shared_counts({'uname': str(clean_uname(p))})
        except:
            pass

        try:
            command = 'dpkg --status bluez | fgrep Version'
            p, f = do_one_command(command)
            save_shared_counts({'bluez': str(p)})
        except:
            pass

        # publish this as the "inactivity time"
        save_shared_counts(
            {'userinactive': str(int(delta_time)), 'userdevicelastactive': s_monitored_device_last_active})

        compare_seconds = int(browser_timeout_minutes) * 60

        with open('/dev/shm/pi_runner_active_monitor_time_since', 'w') as f:
            f.write(str(delta_time) + '\n')
            f.write(str(browser_timeout_minutes) + ' minutes\n')
            f.write(str(compare_seconds) + ' compare_seconds\n')
            f.write(str(s_activity_since_reset) + ' s_activity_since_reset\n')
            f.write(str(s_monitored_device_last_active) + ' s_monitored_device_last_active\n')
            f.write('\n')
            f.write(report_what + '\n')  # a long list, built above here

        if int(browser_timeout_minutes) > 0:
            if (compare_seconds < delta_time) and s_activity_since_reset:
                # do the reset
                kill_browser()

                # and wait another interval before doing it again
                s_time_last_active = time.time()

                #
                s_activity_since_reset = False

                with open('/dev/shm/pi_runner_active_monitor_time_since', 'w') as f:
                    f.write(str(delta_time) + ' was reset' + '\n')
                    f.write(str(browser_timeout_minutes) + ' minutes\n')
                    f.write(str(s_activity_since_reset) + ' s_activity_since_reset\n')
                    f.write(report_what + '\n')

    #  watch -n 1 cat /dev/shm/pi_runner_active_monitor_time_since

    except:
        with open('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
            f.write(traceback.format_exc())


# ----------------------------
def check_hosts_for_update():
    # ----------------------------
    output_file = '/etc/hosts'

    content = """
127.0.0.1  localhost
::1        localhost ip6-localhost ip6-loopback
ff02::1    ip6-allnodes
ff02::2    ip6-allrouters
*********  """ + make_host_name()

    hosts_to_add = []
    bookmarks = get_my_bookmarks()
    for key in sorted(bookmarks):
        if 'hosts' in bookmarks[key]:
            for host in bookmarks[key]['hosts']:
                if not host in hosts_to_add:
                    hosts_to_add.append(host)
    for host in hosts_to_add:
        content += '\n' + host
    content += '\n'

    write_content_if_different(content, output_file)

    _ = """
Test adding a name resolve, for Troy McDaniel

sudo vi /etc/hosts
(Add line to end)
********** wpec5009scsql10

ping wpec5009scsql10
    """


# ----------------------------
def check_browser_for_restart():  # this is running as root
    # ----------------------------
    # see if the browser requires a restart
    screen_width, screen_height = get_screen_size()

    try:
        temp_file = "/cardinal/browserstart" # This is used in /home/<USER>/.xinitrc to keep the browser running
        start_content_before = ""
        with open(temp_file, 'r') as f:
            start_content_before = f.read()

        start_content_after = build_browser_start_content(get_my_bookmarks(), screen_width, screen_height)

        if start_content_before:
            if start_content_before != start_content_after:
                # do the update, and kill the browser
                pass_string, fails = do_one_command('sudo rm ' + temp_file)
                with open(temp_file, 'w') as f:
                    f.write(start_content_after)

                set_browser_restart_setting('browser configuration update',
                                            'There has been an update to the browser startup configuration. Please reset the browser, or reboot the device, for this change to become active.')
                pass_string, fails = do_one_command('sudo chown worker:worker ' + '/dev/shm/browser_restart_setting')

                _ = """
                kill_browser()
                """
    except:
        pass

    # always set mode and permissions, to clean up any weirdness
    try:
        pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)
        pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
    except:
        pass


# ----------------------------
def get_browser_start_count():
    # ----------------------------
    _ = """
cd /cardinal
python3 -c 'import pi_runner; print (pi_runner.get_browser_start_count())'
    """

    the_count = '0'
    try:
        with open("/dev/shm/pi_runner_browser_start_count.txt", "r") as f:
            the_count = f.read()
    except:
        pass

    return the_count


# ----------------------------
def increment_browser_start_count():  # run from the worker thread, on browser start/restart
    # ----------------------------
    exception_log_file = '/dev/shm/pi_runner_browser_exceptions.txt'
    open(exception_log_file, 'w').write('')

    _ = """
cd /cardinal
python3 -c 'import pi_runner; print (pi_runner.increment_browser_start_count())'
    """
    output_file = "/dev/shm/pi_runner_browser_start_count.txt"
    current_count = get_browser_start_count()
    new_count = str(int(current_count) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        open(exception_log_file, 'a').write('-'*30 + '\n' + str(traceback.format_exc()))
        pass

    # take this opportunity to clean up the restart markers for browser
    try:
        open('/cardinal/localhtml/index.html', 'w').write(
            '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>')
    except:
        open(exception_log_file, 'a').write('-'*30 + '\n' + str(traceback.format_exc()))
        print('failed to make start index.html')

    # clear any existing change indicators
    try:
        if get_browser_start_marker():
            set_browser_restart_setting('')
            os.remove('/dev/shm/browser_start_marker')
    except:
        open(exception_log_file, 'a').write('-'*30 + '\n' + str(traceback.format_exc()))
        pass


    output_file = "/dev/shm/pi_runner_browser_start_time.txt"
    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(str(time.time()))
    except:
        open(exception_log_file, 'a').write('-'*30 + '\n' + str(traceback.format_exc()))
        pass


# ----------------------------
def build_browser_start_content(bookmarks, screen_width, screen_height):
    # ----------------------------
    _ = """ 2024.10.14 Browser crash on simple sites
    # enabled logging below, then watched
    tail -f -n 100 /dev/shm/chromium_log.txt

    elf_dynamic_array_reader.h(61)] tag not found

    Google:
    https://forums.raspberrypi.com/viewtopic.php?t=320424

    http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/

    The Chromium 92.0.4515.98 (base image 2.4.1) could not get beyond this error.
    The Chromium 122.0.6261.89 (base image 2.4.2.SP.51) does not error out.

chromium-browser --version

wget  "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-browser_88.0.4324.187-rpt1_armhf.deb"
wget "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-codecs-ffmpeg-extra_88.0.4324.187-rpt1_armhf.deb"
sudo apt install -y --no-install-recommends --allow-downgrades --allow-change-held-packages ./chromium-browser_88.0.4324.187-rpt1_armhf.deb ./chromium-codecs-ffmpeg-extra_88.0.4324.187-rpt1_armhf.deb
sudo rm chromium-browser_88.0.4324.187-rpt1_armhf.deb
sudo rm chromium-codecs-ffmpeg-extra_88.0.4324.187-rpt1_armhf.deb

chromium-browser --version

sudo chown -Rv _apt:root /var/cache/apt/archives/partial/
sudo chmod -Rv 700 /var/cache/apt/archives/partial/

sudo apt-get update
wget  "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-browser_122.0.6261.89-rpt1_armhf.deb"
wget "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-codecs-ffmpeg-extra_122.0.6261.89-rpt1_armhf.deb"
sudo apt install -y --no-install-recommends --allow-downgrades --allow-change-held-packages ./chromium-browser_122.0.6261.89-rpt1_armhf.deb ./chromium-codecs-ffmpeg-extra_122.0.6261.89-rpt1_armhf.deb
sudo rm chromium-browser_122.0.6261.89-rpt1_armhf.deb
sudo rm chromium-codecs-ffmpeg-extra_122.0.6261.89-rpt1_armhf.deb
chromium-browser --version


sudo apt-get update
sudo apt-get upgrade -y
sudo apt dist-upgrade -y

wget  "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-browser_126.0.6478.164-rpt1_armhf.deb"
wget "http://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/chromium-codecs-ffmpeg-extra_126.0.6478.164-rpt1_armhf.deb"
sudo apt install -y --no-install-recommends --allow-downgrades --allow-change-held-packages ./chromium-browser_126.0.6478.164-rpt1_armhf.deb ./chromium-codecs-ffmpeg-extra_126.0.6478.164-rpt1_armhf.deb

getting:
The following information may help to resolve the situation:
The following packages have unmet dependencies:
 chromium-browser : Depends: libgcc-s1 (>= 3.5) but it is not installable
                    Depends: libxcomposite1 (>= 1:0.4.5) but 1:0.4.4-2 is to be installed
 chromium-codecs-ffmpeg-extra : Depends: libgcc-s1 (>= 3.5) but it is not installable
E: Unable to correct problems, you have held broken packages.


sudo rm chromium-browser_126.0.6478.164-rpt1_armhf.deb
sudo rm chromium-codecs-ffmpeg-extra_126.0.6478.164-rpt1_armhf.deb
chromium-browser --version



"""

    format_to_use = 1
    keys_settings = """
xmodmap -e "keycode 37="
xmodmap -e "keycode 67="
xmodmap -e "keycode 105="
xmodmap -e "keycode 133="
xmodmap -e "keycode 134="
"""
    kiosk_mode_string = "--kiosk "

    incognito_mode_enabled = True

    for key in sorted(bookmarks):
        if 'format' in bookmarks[key]:
            if bookmarks[key]['format'] == 'tabs':
                format_to_use = 2

        if 'allowkeys' in bookmarks[key]:
            if bookmarks[key]['allowkeys'] == 'all':
                keys_settings = "setxkbmap -layout us" + "\n"  # this clears the image setup of blocking ctrl and other keys.

        if 'disablekiosk' in bookmarks[key]:
            if bookmarks[key]['disablekiosk'] == 'yes':
                kiosk_mode_string = ''

        if 'disableincognito' in bookmarks[key]:
            if bookmarks[key]['disableincognito'] == 'yes':
                incognito_mode_enabled = False

    content = keys_settings

    content += """python3 -c "import sys; sys.path.append('/cardinal'); import pi_runner; pi_runner.increment_browser_start_count()\"""" + "\n"

    content += "cp /cardinal/localhtml/chromium_Default_Preferences /home/<USER>/.config/chromium/Default/Preferences" + "\n"
    content += "echo yes > /dev/shm/browser_start_marker" + "\n"

    # https://peter.sh/experiments/chromium-command-line-switches/
    content += "chromium-browser \\" + "\n"
    content += """      --proxy-server="https=127.0.0.1:8118;http=127.0.01:8118" \\""" + "\n"

    bypass_to_add = []
    for key in sorted(bookmarks):
        if 'bypass' in bookmarks[key]:
            for host in bookmarks[key]['bypass']:
                if not host in bypass_to_add:
                    bypass_to_add.append(host)

    if bypass_to_add:
        content += """      --proxy-bypass-list=\"""" + ','.join(bypass_to_add) + """\" \\""" + "\n"

    content += "      --window-size=""" + str(screen_width) + "," + str(screen_height) + " \\" + "\n"
    content += "      --window-position=0,0 \\" + "\n"
    content += "      --start-fullscreen \\" + "\n"
    if incognito_mode_enabled:
        content += "      --incognito \\" + "\n"
    content += "      --noerrdialogs \\" + "\n"
    content += "      --disable-translate \\" + "\n"
    content += "      --no-first-run \\" + "\n"
    content += "      --fast \\" + "\n"
    content += "      --fast-start \\" + "\n"
    content += "      --disable-infobars \\" + "\n"
    content += "      --disable-features=TranslateUI \\" + "\n"
    content += "      --disable-features=Translate \\" + "\n"
    content += "      --disk-cache-dir=/dev/null \\" + "\n"
    content += "      --overscroll-history-navigation=0 \\" + "\n"
    content += "      --disable-pinch \\" + "\n"

    # 2024.10.11
#    content += "       --enable-logging=stderr --v=1 > /dev/shm/chromium_log.txt 2>&1" + "\n"

    # 2024.04.22 Trying to make it now show "Restore Pages? Chromium didn't shut down correctly." message
    #    content += "      --noerrordialogs \\" + "\n"

    # 2024.03.06
    # To fix the issue seen on "chrome://gpu" page on the pi:
    # "Accelerated video decode has been disabled... Disabled Features: video_decode"
    content += "      --enable-features=VaapiVideoDecoder \\" + "\n"

    # To fix the issue seen on "chrome://gpu" page on the pi:
    # "Gpu compositing has been disabled... Disabled Features: gpu_compositing"
    # the following line does the trick, takes like 90 seconds new, and 2 seconds when already up to date.
    # sudo apt-get install libgles2-mesa libgles2-mesa-dev xorg-dev-y

    # use this to allow autoplay with audio unmuted
    # https://chromeenterprise.google/policies/
    content += "      --autoplay-policy=no-user-gesture-required \\" + "\n"
    content += "      --AutoplayAllowed=true \\" + "\n"

    # 4K display has playback stutters, try these:
    if False:
        content += "      --ignore-gpu-blocklist \\" + "\n"
        content += "      --enable-gpu-compositing \\" + "\n"
        content += "      --enable-accelerated-video-decode \\" + "\n"
        content += "      --enable-accelerated-mjpeg-decode \\" + "\n"
        content += "      --enable-native-gpu-memory-buffers \\" + "\n"
        content += "      --enable-gpu-rasterization \\" + "\n"

    content += kiosk_mode_string

    #    prefix_url = '--app='
    prefix_url = ''

    if format_to_use == 1:
        content += prefix_url + '"file:///cardinal/localhtml/index.html"'

    if format_to_use == 2:
        for key in sorted(bookmarks):
            content += prefix_url + ' "' + bookmarks[key]['url'] + '" '

    return content


# ----------------------------
def get_my_zoom():
    # ----------------------------
    input_file = "/cardinal/localhtml/browser_zoom"

    zoom_to_use = '100'
    try:
        if os.path.isfile(input_file):
            with open(input_file, 'r') as f:
                zooms_full = json.loads(f.read())
            serial = get_serial()
            if serial in zooms_full:
                zoom_to_use = copy.deepcopy(zooms_full[serial])
        else:
            local_configuration = '/cardinal/config_zoom.txt'
            if os.path.isfile(local_configuration):
                zoom_to_use = open(local_configuration, 'r').read()

    except:
        pass

    return zoom_to_use


# ----------------------------
def kill_browser():
    # ----------------------------
    try:
        # While we are at it, give corp wifi a chance to start from scratch
        pass_string, fail_string = do_one_command("sudo nmcli con delete corp0")
        pass_string, fail_string = do_one_command("sudo nmcli con delete corp1")
    except:
        pass

    try:
        pass_string, fail_string = do_one_command(
            "ps ax")  # | fgrep chromium-browser | fgrep cardinal | cut -d ' ' -sf1")

        process_number = ''
        for item in pass_string.split("\n"):
            if 'chromium-browser' in item:
                if 'cardinal' in item:
                    process_number = item.split()[0].strip()

        if process_number:
            # 14373
            pass_string, fail_string = do_one_command("sudo kill " + process_number)
    except:
        print(traceback.format_exc())


# ----------------------------
def set_browser_zoom_level(zoom_level):
    # ----------------------------
    notes = """
scale    chrome value
 0.50   -3.****************
 0.75   -1.****************
 0.80   -1.****************
 0.90   -0.****************
1.00    0.0
1.10    0.****************
1.25    1.****************
1.50    2.***************
X       Y

Y ~= 5.4848 ln(X)

# https://community.volumio.org/t/how-to-preset-chromium-browser-scale-zoom-factor/38944/2
Y = log(X, 1.2)

    """

    try:
        scale = float(zoom_level) / 100.0

        #    original_file = "/home/<USER>/.config/chromium/Default/Preferences"
        #    original = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"Discover great apps,\n games,\n extensions and themes for Chromium.","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":"[\"gaia.l.a.r\",\n[]\n]\n"},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":-2.0},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Crashed","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""
        #    trimmed = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":""},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":-2.0},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Crashed","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""

        first_part = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":""},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":"""
        second_part = """},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Normal","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""

        value_string_to_use = str(math.log(float(scale), 1.2))

        content = first_part + value_string_to_use + second_part

        output_file = "/cardinal/localhtml/chromium_Default_Preferences"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo chown -R worker:worker ' + output_file)

    except:
        print(traceback.format_exc())


# ====================================
def calculate_rot13(s):
    # ====================================
    chars = "abcdefghijklmnopqrstuvwxyz"
    trans = chars[13:] + chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c) > -1 else c
    return ''.join(rot_char(c) for c in s)


# ----------------------------
def get_cpu_temperature():
    # ----------------------------
    pass_string, fail_string = do_one_command('vcgencmd measure_temp', timeout=5)
    try:
        return_value = pass_string.split('=')[1].split("'")[0]
    except:
        return_value = "-1000"

    return return_value


# ----------------------------
def get_my_showgif_md5():
    # ----------------------------
    input_file = "/cardinal/localhtml/showgif_md5"

    value_to_use = ''
    try:
        with open(input_file, 'r') as f:
            value_full = json.loads(f.read())
        serial = get_serial()
        if serial in value_full:
            value_to_use = copy.deepcopy(value_full[serial])
    except:
        pass

    return value_to_use


# ----------------------------
def make_show_html(file_type='gif'):
    # ----------------------------
    # https://www.w3schools.com/html/html_images_background.asp
    # ==============================================
    output_file = '/cardinal/localhtml/showgif.html'
    # ==============================================
    content = """
<!DOCTYPE html>
<html>
<head>
<style>
body {
  background-image: url('showgif.gif');
  background-attachment: fixed;
  background-size: 100% 100%;
}
</style>
</head>
</html>
    """
    write_content_if_different(content, output_file, with_execute=False)

    # ==============================================
    #    output_file = '/cardinal/localhtml/showmp4.html'
    # ==============================================
    if file_type == 'mp4':
        content = """
<!DOCTYPE html>
<html>
<head>
<style>
.video-container {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 99%;
  height: 100%;
  overflow: hidden;
}
.video-container video {
  /* Make video to at least 100% wide and tall */
  min-width: 100%;
  min-height: 100%;

  /* Setting width & height to auto prevents the browser from stretching or squishing the video */
  width: auto;
  height: auto;

  /* Center the video */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
</style>
</head>
<body>
<div class="video-container">
<video autoplay loop controls id="myVideo">
  <source src="showgif.gif" type="video/mp4">
</video>
</div>
</body>
</html>
    """

    # other video options, that are maybe not used
    # muted
    # controls
    write_content_if_different(content, output_file, with_execute=False)


# ----------------------------
def call_home_locations():
    # ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"] OR ['https://slicer.systems']
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'", '"'))
    except:
        pass

    return response


# ----------------------------
def get_server_file_to_local(file_to_get, file_to_save):
    # ----------------------------
    # reach out to Slicer, get the file, then do the work
    try:
        the_site_to_call = call_home_locations()[0]
        the_request_url = the_site_to_call + '/download?' + 'filetodownload=' + file_to_get  # showgif.gif
        output_file = file_to_save

        response = requests.get(the_request_url, verify=False, timeout=45.0)

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        if response.status_code == 200:
            with open(output_file, 'wb') as f:
                f.write(response.content)
    except:
        print('download failed')
        print(traceback.format_exc())


# ----------------------------
def get_do_command(do_command_id):
    # ----------------------------
    try:
        the_site_to_call = call_home_locations()[0]
        the_request_url = the_site_to_call + '/devicecommand?' + 'do_command=' + do_command_id

        do_datadrop_debug('--get-- the_request_url: ' + the_request_url)

        r = requests.get(the_request_url, verify=False, timeout=s_call_home_timeout)
        url_result = r.text

        do_datadrop_debug('--got-- url_result: ' + url_result)

        # look into the content to get the command number, instead of trusting the fetch filename
        command_number = int(url_result.split('\n')[0].split(',')[0])

        output_file = s_runnable_work_path + 'do_command_' + "{:06d}".format(command_number) + '/do_command'
        do_datadrop_debug('--save-- filename: ' + output_file)

        # Then save it out, to the place where the main loop will get it handled
        write_content_if_different(url_result, output_file)
    except:
        pass


# ----------------------------
def set_service_start(service_name, print_debug=False):
    # ----------------------------
    do_datadrop_debug('--get-- is a service file')
    # for initial debug
    service_start_file = '/cardinal/' + service_name.replace('_', '-')
    service_full_name = service_name.replace('_', '-') + '.service'
    service_conf_file = '/lib/systemd/system/' + service_full_name

    start_content = ""
    start_content += "#!/usr/bin/env python3\n"
    start_content += "import " + service_name + "\n"
    start_content += service_name + ".main()\n"

    service_conf = """
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=""" + service_start_file + """
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
    """

    with open(service_start_file, 'w') as f:
        f.write(start_content)

    with open(service_conf_file, 'w') as f:
        f.write(service_conf)

    commands = []
    commands.append('sudo chmod +x ' + service_start_file)
    commands.append('sudo systemctl daemon-reload')

    if service_name == 'pi_runner':
        commands.append('sudo systemctl restart ' + service_full_name)
    else:
        commands.append('sudo systemctl stop ' + service_full_name)
        commands.append('sudo systemctl start ' + service_full_name)

        # When restarting network, give it time to re-connect to WiFi.
        if service_name == 'pi_network':
            time.sleep(10)

    commands.append('sudo systemctl enable ' + service_full_name)
    commands.append('sudo systemctl status ' + service_full_name)

    for command in commands:
        p, f = do_one_command(command)
        if print_debug:
            print("---------------------------")
            print(" cmd: " + command)
            print("pass: " + p)
            print("fail: " + f)

    do_datadrop_debug('--get-- systemctl enable enable: ' + p + f)


# ----------------------------
def get_service(service_name, service_version):
    # ----------------------------
    # reach out to Slicer, get the file, then do the work
    try:
        the_site_to_call = call_home_locations()[0]
        the_request_url = the_site_to_call + '/download?' + 'service=' + service_name + ',version=' + service_version

        do_datadrop_debug('--get-- the_request_url: ' + the_request_url)

        r = requests.get(the_request_url, verify=False, timeout=s_call_home_timeout)
        url_result = r.text

        service_content = calculate_rot13(url_result)

        # make sure it really is a service file
        is_service_file = True
        reason_not_service = ''

        test_strings = ['version =', 'release_notes =', '# ===== begin: start file', '# ===== begin: service file',
                        'def main():']
        for test_string in test_strings:
            if not test_string in service_content:
                is_service_file = False
                reason_not_service += "no:" + test_string
                do_datadrop_debug('--get-- missing: ' + test_string)

        #        with open('/dev/shm/reason_not_service', 'w') as f:
        #            f.write(reason_not_service)

        if is_service_file:
            service_code_file = '/cardinal/' + service_name + '.py'
            with open(service_code_file, 'w') as f:
                f.write(service_content)

            set_service_start(service_name)
        else:
            do_datadrop_debug('--get-- not a service file')

    except:
        exception_caught = traceback.format_exc()
        with open('/dev/shm/pi_' + service + '_get_service_exception', 'w') as f:
            f.write(exception_caught)
        do_datadrop_debug('--get-- exception_caught: ' + exception_caught)


# ----------------------------
def do_one_command(command, timeout=None):
    # ----------------------------
    import shlex
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    if timeout:
        doit = subprocess.Popen(command_splits, universal_newlines=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
        try:
            doit.wait(timeout=timeout)
            (mem_string, fails) = doit.communicate()
        except:
            (mem_string, fails) = '', 'timeout'
    else:
        doit = subprocess.Popen(command_splits, universal_newlines=True,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
        (mem_string, fails) = doit.communicate()

    #    print (timeout, command, mem_string, fails)
    return (mem_string, fails)


# ----------------------------
def make_host_name():
    # ----------------------------
    serial = get_serial()
    host_name = 'cah-rp-' + serial
    return host_name


# ----------------------------
def do_one_time():
    # ----------------------------

    set_firewall_rules()

    increment_boot_count()

    uptime = get_uptime()
    if uptime < 30:
        # This is a hard system boot, not just a runner process restart
        try:
            boot_tracker_data = json.loads(open(s_boot_tracker_file, 'r').read())
            boots_left_to_do = boot_tracker_data['boots_left_to_do']   # if this is not in there, it trips the exception
        except:
            boot_tracker_data = {'boots_left_to_do':0}

        if boot_tracker_data['boots_left_to_do'] > 0:
            boot_tracker_data['boots_left_to_do'] -= 1
            write_content_if_different(json.dumps(boot_tracker_data), s_boot_tracker_file)
            time.sleep(5)
            do_one_command('sudo reboot')
        else:
            # this time we are going to let it fully boot up

            # get ready for the next boot
            extra_boots = get_my_extra_boots_count()
            try:
                boot_tracker_data = {'boots_left_to_do':extra_boots, 'from':'boot'}
                write_content_if_different(json.dumps(boot_tracker_data), s_boot_tracker_file)
            except:
                pass

    if is_controlled_image(os.listdir('/cardinal/')):
        # set the hostname
        try:
            host_name = make_host_name()

            do_one_command('sudo hostname ' + host_name)

            file_out = '/etc/hostname'
            with open(file_out, 'w') as f:
                f.write(host_name)
        except:
            pass

        check_hosts_for_update()

        # set timezone to last known
        save_file = '/cardinal/localhtml/timezone'
        try:
            time_zone = 'UTC'
            with open(save_file, 'r') as f:
                time_zone = f.read()
            set_timezone(time_zone, 'do_one_time')
        except:
            pass

        set_browser_zoom_level(get_my_zoom())

        write_show_html()

        # clean up any content we want, if this is running on a new device
        serial = get_serial()
        previous_serial_filename = '/cardinal/previous_serial.txt'
        if os.path.isfile(previous_serial_filename): # make sure that we have started up like this at least once
            previous_serial = open(previous_serial_filename, 'r').read()
            if not previous_serial == serial:
                # clear out any saved database from the browser (Carousel Player project)
                try:
                    do_one_command('rm -rf /home/<USER>/.config/chromium/Default/IndexedDB')
                except:
                    pass
                try:
                    do_one_command('rm -rf "/home/<USER>/.config/chromium/Default/Service Worker"')
                except:
                    pass
        try:
            open(previous_serial_filename, 'w').write(serial)
        except:
            pass

# ----------------------------
def set_firewall_rules():
    # ----------------------------
    _notes = """
# Firewall
# https://www.digitalocean.com/community/tutorials/iptables-essentials-common-firewall-rules-and-commands
# https://www.cyberciti.biz/tips/linux-iptables-4-block-all-incoming-traffic-but-allow-ssh.html

# show current rules:
sudo iptables -S

    """

    if True:
        commands = []

        # flush all rules
        commands.append('sudo iptables -F')

        # allow all, until the lower section here is correct
        commands.append('sudo iptables -P OUTPUT ACCEPT')
        commands.append('sudo iptables -P INPUT ACCEPT')
        commands.append('sudo iptables -P FORWARD ACCEPT')

    else:
        commands = []
        # flush all rules
        commands.append('sudo iptables -F')
        commands.append('sudo iptables -X')

        # Setting default filter policy
        commands.append(
            'sudo iptables -P OUTPUT ACCEPT')  # need our outbound 443 access to Slicer to work, and to function as a web browser
        commands.append('sudo iptables -P INPUT DROP')
        commands.append('sudo iptables -P FORWARD DROP')

        # drop bad packets
        commands.append('sudo iptables -A INPUT -m conntrack --ctstate INVALID -j DROP')

        # Allow unlimited traffic on loopback
        commands.append('sudo iptables -A INPUT -i lo -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o lo -j ACCEPT')

        # web browser activity from the pi to the outside
        commands.append('sudo iptables -A OUTPUT -o eth0 -p tcp --dport 80 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o eth0 -p tcp --dport 443 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o wlan0 -p tcp --dport 80 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append(
            'sudo iptables -A OUTPUT -o wlan0 -p tcp --dport 443 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o wlan1 -p tcp --dport 80 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append(
            'sudo iptables -A OUTPUT -o wlan1 -p tcp --dport 443 -m state --state NEW,ESTABLISHED -j ACCEPT')

        # Allow ssh
        commands.append('sudo iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW,ESTABLISHED -j ACCEPT')

        # Network time outbound, and responses?

    try:
        for command in commands:
            pass_string, fails = do_one_command(command)

    except:
        pass


# ----------------------------
def get_serial():
    # ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial


# ----------------------------
def get_mac_address_for_port_name(portName):
    # ----------------------------
    return_value = ''
    pass_string, fails = do_one_command('ip -j addr')
    # pass_string = '[{"ifindex":1,"ifname":"lo","flags":["LOOPBACK","UP","LOWER_UP"],"mtu":65536,"qdisc":"noqueue","operstate":"UNKNOWN","group":"default","txqlen":1000,"link_type":"loopback","address":"00:00:00:00:00:00","broadcast":"00:00:00:00:00:00","addr_info":[{"family":"inet","local":"127.0.0.1","prefixlen":8,"scope":"host","label":"lo","valid_life_time":4294967295,"preferred_life_time":4294967295},{"family":"inet6","local":"::1","prefixlen":128,"scope":"host","valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":2,"ifname":"eth0","flags":["BROADCAST","MULTICAST","UP","LOWER_UP"],"mtu":1500,"qdisc":"mq","operstate":"UP","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c2","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691098,"preferred_life_time":691098},{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","secondary":true,"dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691103,"preferred_life_time":604703},{"family":"inet6","local":"fe80::d21:c163:5965:731e","prefixlen":64,"scope":"link","noprefixroute":true,"valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":3,"ifname":"wlan0","flags":["NO-CARRIER","BROADCAST","MULTICAST","UP"],"mtu":1500,"qdisc":"pfifo_fast","operstate":"DOWN","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c4","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[]}]'
    try:
        # print ('pass_string', pass_string)
        the_list = json.loads(pass_string)
        for index in range(0, len(the_list)):
            # the_list[1].keys()
            # [u'addr_info', u'operstate', u'qdisc', u'group', u'mtu', u'broadcast', u'flags', u'address', u'ifindex', u'txqlen', u'ifname', u'link_type']
            # print ("ifname", the_list[index]['ifname'])
            if the_list[index]['ifname'] == portName:
                return_value = the_list[index]['address']
    except:
        pass

    return return_value


# ----------------------------
def get_memory_details():
    # ----------------------------
    _ = """
MemTotal:        1911320 kB
MemFree:         1194012 kB
MemAvailable:    1533404 kB
Buffers:           51552 kB
Cached:           442692 kB
SwapCached:            0 kB
Active:           199556 kB
Inactive:         439384 kB
Active(anon):      13020 kB
Inactive(anon):   224548 kB
Active(file):     186536 kB
Inactive(file):   214836 kB
Unevictable:        2784 kB
Mlocked:              16 kB
HighTotal:       1232896 kB
HighFree:         629608 kB
LowTotal:         678424 kB
LowFree:          564404 kB
SwapTotal:        102396 kB
SwapFree:         102396 kB
Dirty:               196 kB
Writeback:             0 kB
AnonPages:        147560 kB
Mapped:           221896 kB
Shmem:             92876 kB
KReclaimable:      22724 kB
Slab:              41424 kB
SReclaimable:      22724 kB
SUnreclaim:        18700 kB
KernelStack:        1936 kB
PageTables:         6724 kB
NFS_Unstable:          0 kB
Bounce:                0 kB
WritebackTmp:          0 kB
CommitLimit:     1058056 kB
Committed_AS:    1299952 kB
VmallocTotal:     245760 kB
VmallocUsed:        5408 kB
VmallocChunk:          0 kB
Percpu:              512 kB
CmaTotal:         262144 kB
CmaFree:          249456 kB
    """

    result = {}
    try:
        with open('/proc/meminfo', 'r') as f:
            content = f.read()

            for line in content.split('\n'):
                if ':' in line:
                    splits = line.split(':')
                    title = splits[0]
                    value_string = splits[1].strip()
                    the_number = value_string.split(' ')[0]
                    the_units = value_string.split(' ')[1]
                    the_bytes = int(the_number)
                    if the_units == 'kB':
                        the_bytes = the_bytes * 1024
                    elif the_units == 'MB':
                        the_bytes = the_bytes * 1024 * 1024
                    result[title] = str(the_bytes)

    except:
        pass

    return result


# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
    # ----------------------------
    the_file = '/dev/shm/pi_' + service + '_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')


# ----------------------------
def get_services_status():
    # ----------------------------
    result = {}
    services_running = get_services_running()
    for service_name in services_running:
        status = os.system('systemctl is-active --quiet ' + service_name.replace('_', '-') + '.service')

        if str(status) == '0':
            return_value = 'ok'
        else:
            return_value = 'fail'
        result[service_name] = return_value

    return result


# ----------------------------
def get_kernel_version():
    # ----------------------------
    try:
        return str(os.uname().release)  # '5.10.63-v7l+'
    except:
        return ''


# ----------------------------
def get_services_running():
    # ----------------------------
    # ------------------------
    # find all of our services that are in place
    services_running = {}
    try:
        service_dir = '/lib/systemd/system/'
        all_services = os.listdir(service_dir)
        for service_name in all_services:
            if len(service_name) > 3:
                if service_name[0:3] == "pi-":
                    # print ('service_name', service_name)
                    version_file = "/dev/shm/" + service_name.replace('-', '_').replace('.service', '') + '_version.txt'
                    version_string = '(missing)'
                    # print ('version_file', version_file)
                    try:
                        with open(version_file, 'r') as f:
                            version_string = f.read()
                    except:
                        pass
                    services_running[service_name.replace('-', '_').replace('.service', '')] = version_string
    except:
        do_datadrop_debug(traceback.format_exc())

    return services_running


# ----------------------------
def calculate_device_number_from_device_value(device):
    # ----------------------------
    device_number = '0'
    lookup = {'--': '1', 'eth0': '2', 'wlan0': '3', 'wlan1': '4'}
    if device in lookup:
        device_number = lookup[device]

    return device_number


# ----------------------------
def do_discovery():
    # ----------------------------
    pass_string, fails = do_one_command('nmcli -f DEVICE,UUID c')
    uuid = get_item_from_network_query(pass_string, item='UUID')
    device = get_item_from_network_query(pass_string, item='DEVICE')

    did_uuid_change = set_saved_value('uuid', uuid)
    did_device_change = set_saved_value('device', device)

    network_connects = 0
    network_device_changes = 0
    try:
        network_connects = int(get_saved_value('network_connects'))
    except:
        pass
    try:
        network_device_changes = int(get_saved_value('network_device_changes'))
    except:
        pass

    if did_uuid_change:
        network_connects += 1
    if did_device_change:
        network_device_changes += 1

    set_saved_value('network_connects', network_connects)
    set_saved_value('network_device_changes', network_device_changes)
    set_saved_value('network_device_number', calculate_device_number_from_device_value(device))


# ----------------------------
def build_just_slicer_report():
    # ----------------------------
    global s_previous_report

    do_discovery()
    serial = get_serial()

    browser_restart_setting = get_browser_restart_setting()
    if browser_restart_setting:
        browser_restart_wanted = '1'
    else:
        browser_restart_wanted = '0'

    the_data = []
    the_data.append('source=' + service)
    the_data.append('serial=' + serial)
    the_data.append('version=' + version)
    the_data.append('wlan0mac=' + get_mac_address_for_port_name('wlan0'))
    the_data.append('eth0mac=' + get_mac_address_for_port_name('eth0'))
    the_data.append('address=' + my_best_address())

    the_data.append('wlan1mac=' + get_mac_address_for_port_name('wlan1'))
    the_data.append('eth1mac=' + get_mac_address_for_port_name('eth1'))
    the_data.append('responsetime=' + "{:.3f}".format(s_previous_report['response_time']))
    the_data.append('responsexce=' + str(s_previous_report['exception_count']))

    the_data.append('chromium=' + str(chromium_version()))
    the_data.append('network_connects=' + str(get_saved_value('network_connects')))
    the_data.append('network_device_changes=' + str(get_saved_value('network_device_changes')))
    the_data.append('network_device_number=' + str(get_saved_value('network_device_number')))

    the_data.append('uptime=' + str(get_uptime()))
    the_data.append('loadavg=' + str(loadavg()))
    disk_usage = get_highest_disk_usage()
    the_data.append('disk_use=' + str(disk_usage['percent_used']))
    the_data.append('one_K=' + str(disk_usage['one_k_blocks']))

    the_data.append('inode_use=' + str(get_highest_inode_usage()))

    the_data.append('boot_count=' + str(get_boot_count()))
    the_data.append('screengrab_count=' + str(get_grab_count()))

    screen_width, screen_height = get_screen_size()
    the_data.append('screen_width=' + str(screen_width))
    the_data.append('screen_height=' + str(screen_height))

    pass_string, fails = do_one_command('fbset -s')
    the_data.append('screen_geometry=' + str(extract_screen_geometry_from_fbset_pass(pass_string)))

    the_data.append('brRsWd=' + browser_restart_wanted)

    # pick up any shared items (from other processes)
    share_point = '/dev/shm/'
    files_found = os.listdir(share_point)
    for file_found in files_found:
        if 'shared_' in file_found:
            name = file_found.replace('shared_', 's_')
            value = ''
            try:
                with open(share_point + file_found, 'r') as f:
                    value = f.read().split('\n')[0].replace(',',
                                                            '')  # limit to first line, and no commas, to get ready to send it
                the_data.append(name + '=' + str(value))
            except:
                pass

    local_device_path = '/cardinal/localhtml/'
    try:
        files_found = os.listdir(local_device_path)
    except:
        files_found = []

    for file_found in files_found:
        if 'dev_' == file_found[:4]:
            # these are device settings, from local choices, that need updated in Slicer
            try:
                raw = json.loads(open(local_device_path + file_found, 'r').read())
                the_data.append(file_found + '=' + str(raw[serial]))
            except:
                pass
    # ------------------------
    found_time_zone = "???"
    pass_string, fail_string = do_one_command('timedatectl')
    _ = """
       Local time: Wed 2021-05-19 17:11:32 UTC
   Universal time: Wed 2021-05-19 17:11:32 UTC
         RTC time: n/a
        Time zone: UTC (UTC, +0000)
System clock synchronized: yes
      NTP service: active
  RTC in local TZ: no
    """
    found_time_local = extract_local_time(pass_string)

    for line in pass_string.split('\n'):
        if 'Time zone:' in line:
            splits = line.split(':')
            found_time_zone = splits[1].strip().split(' ')[0]
    the_data.append('timezone=' + found_time_zone)
    the_data.append('timelocal=' + found_time_local)

    # ------------------------
    image_version = get_image_version()

    the_data.append('image=' + image_version)

    pass_string, fail_string = do_one_command('cat /etc/hostname')
    try:
        the_data.append('hostname=' + pass_string.split('\n')[0])
    except:
        do_datadrop_debug(traceback.format_exc())

    # ------------------------

    # pass_string = "networkuse=(1)(2)(3)"
    try:
        the_data.append('networkuse=' + get_network_utilization_report())
    except:
        do_datadrop_debug(traceback.format_exc())

    # ------------------------

    # pass_string = "temp=44.8'C"
    try:
        temperature = get_cpu_temperature()
        if float(temperature) > -999:
            the_data.append('temperature=' + temperature)
    except:
        do_datadrop_debug(traceback.format_exc())

    # ------------------------
    # find all of our services that are in place
    services_running = get_services_running()
    for service_name in services_running:
        the_data.append('service:' + service_name + '=' + services_running[service_name])

    services_running = get_services_status()
    for service_name in services_running:
        the_data.append('run_status:' + service_name + '=' + services_running[service_name])

    memory_details = get_memory_details()
    to_send_list = ['MemTotal', 'MemFree', 'MemAvailable', 'Buffers', 'Cached']
    for to_send in to_send_list:
        if to_send in memory_details:
            the_data.append('Memory:' + to_send + '=' + memory_details[to_send])

    the_data.append('kernel=' + get_kernel_version())

    # as the very last item, send our time, so that it can be compared to the server time
    the_data.append('devicetime=' + str(time.time()))

    browseruptime = -1
    saved_time_string_of_last_browser_start = None
    try:
        saved_time_string_of_last_browser_start = open('/dev/shm/pi_runner_browser_start_time.txt', 'r').read()
    except:
        pass

    if not saved_time_string_of_last_browser_start:
        try:
            output_file = "/dev/shm/pi_runner_browser_start_time.txt"
            saved_time_string_of_last_browser_start = str(time.time())
            write_content_if_different(saved_time_string_of_last_browser_start, output_file)
        except:
            pass

    if saved_time_string_of_last_browser_start:
        browseruptime = int(time.time() - float(saved_time_string_of_last_browser_start))

    the_data.append('browseruptime=' + str(browseruptime))

    return the_data


# ----------------------------
def do_slicer_report(call_home_location, the_data):
    # ----------------------------
    global s_previous_report

    url_result = ''

    the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

    try:
        open('/dev/shm/runner_report', 'w').write(the_report_url)
    except:
        pass

    # print ('the_report_url', the_report_url)
    # {"temperature":"45.2","service:pi-monitor":"M.2.2","wlan0mac":"dc:a6:32:96:56:c4","hostname":"cah-pi-10000000e3669edf","service:pi-network":"N.1.0","source":"runner","version":"R.1.0","service:pi-runner":"R.1.0","time":1620917416.788708,"serial":"10000000e3669edf","service:pi-hmi":"H.0.6"}

    # check in with slicer
    time_start = time.time()
    do_datadrop_debug('---- request start ----')
    try:
        r = requests.get(the_report_url, verify=False, timeout=s_call_home_timeout)
        url_result = r.text
        s_previous_report['response_time'] = time.time() - time_start
        s_previous_report['exception_count'] = 0
    except:
        do_datadrop_debug(traceback.format_exc())
        do_datadrop_debug('---- request exception ----')
        url_result = 'exception'
        s_previous_report['exception_count'] += 1
    do_datadrop_debug('---- request end ----')

    if s_previous_report['exception_count'] > 0:
        # try any other set of urls, to see if it is just Slicer, or is the network down
        did_get_any = False

        if False:  # 2022.12.01 Stop doing this check, and just let exceptions increase
            for test_url in ['http://google.com', 'http://google.com']:
                try:
                    do_datadrop_debug('---- request test url: ' + test_url)
                    r = requests.get(test_url, verify=False, timeout=5.0)
                    do_datadrop_debug('---- request success')
                    did_get_any = True
                    break  # exit on the first hit
                except:
                    do_datadrop_debug('---- request failed to test url: ' + test_url)
                    pass

        if did_get_any:
            s_previous_report['exception_count'] = 0

    # do not do this anymore
    # write_content_if_different(str(s_previous_report['exception_count']), '/dev/shm/pi_runner_slicer_exceptions.txt')

    return url_result


# ----------------------------
def do_handle_call_home_result(serial, url_result):
    # ----------------------------
    slicer_update_bonus = False
    work_report = ''

    configuration_changes = []

    try:
        result_json = json.loads(url_result)  # This will throw exception if the previous block passed 'exception'
        open('/dev/shm/pi_runner_result_json', 'w').write(url_result)

        # {"services": {"pi_runner": "R.1.0", "pi_hmi": "H.0.7"}}

        # make a new pattern, that if the field is named "info_", then lump process that here
        # These are light weight, non-verified to serial number
        for item in result_json:
            if 'info_' in item:
                output_file = "/cardinal/localhtml/" + item
                content = str(result_json[item])

                write_content_if_different(content, output_file)

        if 'timezone' in result_json:
            time_zone = str(result_json['timezone'])
        else:
            time_zone = 'UTC'

        content = time_zone
        output_file = "/cardinal/localhtml/timezone"

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        try:
            with open(output_file, 'r') as f:
                existing_content = f.read()
        except:
            existing_content = ''

        if existing_content != content:
            configuration_changes.append('Timezone changed from [' + existing_content + '] to [' + content + ']')
            with open(output_file, 'w') as f:
                f.write(content)

            set_timezone(time_zone, 'do_handle_call_home_result')

        if 'showgif_md5' in result_json:
            md5_before = get_my_showgif_md5()

            output_file = "/cardinal/localhtml/showgif_md5"
            content = json.dumps(result_json['showgif_md5'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                with open(output_file, 'w') as f:
                    f.write(content)

            md5_after = get_my_showgif_md5()

            if md5_after != md5_before:
                try:
                    local_file = '/cardinal/localhtml/showgif.gif'
                    get_server_file_to_local('showgif_' + serial + '.gif', local_file)
                    write_show_html()

                    need_to_restart_browser = False
                    bookmarks = get_my_bookmarks()
                    for key in sorted(bookmarks):
                        if 'url' in bookmarks[key]:
                            if 'file:///cardinal/localhtml/showgif.html' == bookmarks[key]['url']:
                                need_to_restart_browser = True
                    #                                if 'file:///cardinal/localhtml/showmp4.html' == bookmarks[key]['url']:
                    #                                    need_to_restart_browser = True
                    # do not require that it be autolaunch. If it was manually launched,
                    # then this will alert the people that there is new content.

                    if need_to_restart_browser:
                        kill_browser()
                except:
                    # make it so that it retries at the next opportunity
                    os.remove("/cardinal/localhtml/showgif_md5")

        if 'browser_zoom' in result_json:
            zoom_before = get_my_zoom()

            output_file = "/cardinal/localhtml/browser_zoom"
            content = json.dumps(result_json['browser_zoom'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'browser_zoom changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

            zoom_after = get_my_zoom()

            if zoom_after != zoom_before:
                set_browser_zoom_level(zoom_after)
                kill_browser()

        if 'clockview_enabled' in result_json:
            output_file = "/cardinal/localhtml/clockview_enabled"
            content = json.dumps(result_json['clockview_enabled'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'clockview_enabled changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

        if 'bluetooth_enabled' in result_json:
            output_file = "/cardinal/localhtml/bluetooth_enabled"
            content = json.dumps(result_json['bluetooth_enabled'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'bluetooth_enabled changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

        if 'special_menu' in result_json:
            output_file = "/cardinal/localhtml/special_menu"
            content = json.dumps(result_json['special_menu'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'special_menu changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

        if 'screen_resolution' in result_json:
            output_file = "/cardinal/localhtml/screen_resolution"
            content = json.dumps(result_json['screen_resolution'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'screen_resolution changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

                if is_controlled_image(os.listdir('/cardinal/')):
                    # also build a new boot configuration
                    build_boot_config()

                    # and do the reboot
                    do_one_command('sudo reboot')

        if 'name_to_use' in result_json:
            output_file = "/cardinal/localhtml/name_to_use"
            content = json.dumps(result_json['name_to_use'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'name_to_use changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

        if 'browser_timeout' in result_json:
            output_file = "/cardinal/localhtml/browser_timeout"
            content = json.dumps(result_json['browser_timeout'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append(
                    'browser_timeout changed from [' + existing_content + '] to [' + content + ']')
                with open(output_file, 'w') as f:
                    f.write(content)

        # like "conf_wifi_connect"
        for individual_item in result_json:
            if 'conf_' == individual_item[:5]:
                output_file = "/cardinal/localhtml/" + individual_item
                dev_file = "/cardinal/localhtml/" + 'dev_' + individual_item[5:]

                content = json.dumps(result_json[individual_item])

                if os.path.isfile(dev_file):
                    try:
                        raw = json.loads(open(dev_file, 'r').read())

                        delete_dev = False
                        if not serial in raw:
                            delete_dev = True  # this cleans up any old serial number item
                        else:
                            dev_value = str(raw[serial])
                            if dev_value == result_json[individual_item][serial]:
                                delete_dev = True  # Once they match, remove the device side setting
                        if delete_dev:
                            os.remove(dev_file)
                    except:
                        pass

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    configuration_changes.append(
                        individual_item + ' changed from [' + existing_content + '] to [' + content + ']')
                    with open(output_file, 'w') as f:
                        f.write(content)

        if 'bookmarks' in result_json:
            output_file = "/cardinal/localhtml/bookmarks"
            content = json.dumps(result_json['bookmarks'])

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                configuration_changes.append('bookmarks' + ' changed')
                with open(output_file, 'w') as f:
                    f.write(content)

            check_browser_for_restart()
            check_hosts_for_update()

        # if 'runnable' in result_json:
        if 'do_command' in result_json:
            if serial in result_json['do_command']:
                get_do_command(result_json['do_command'][serial])

        if 'services' in result_json:
            services_running = get_services_running()
            services_to_fetch = {}
            runner_to_fetch = {}

            # for initial debug
            with open('/dev/shm/runner_services_list', 'w') as f:
                f.write(json.dumps(result_json['services']))
            # {"pi_security": "S.1.1"}

            with open('/dev/shm/runner_services_running', 'w') as f:
                f.write(json.dumps(services_running))

            # compare whats requested, compared to what I have already running, and pull the difference
            for item in result_json['services']:
                need_to_pull = True
                if item in services_running:
                    if result_json['services'][item] == services_running[item]:
                        # it matches what I already have
                        need_to_pull = False

                if need_to_pull:
                    work_report += 'need to pull: ' + item + ' ' + result_json['services'][item] + '\n'
                    if item == 'pi_runner':
                        runner_to_fetch[item] = result_json['services'][item]
                    else:
                        services_to_fetch[item] = result_json['services'][item]

            # do the updates
            for service_name in services_to_fetch:
                get_service(service_name, services_to_fetch[service_name])  # name, version
                work_report += 'pulled: ' + service_name + '\n'

            # If there is a runner update, do it last, since that is this code running right here
            for service_name in runner_to_fetch:
                get_service(service_name, runner_to_fetch[service_name])  # name, version
                work_report += 'pulled: ' + service_name + '\n'

        if 'screengrab_request' in result_json:
            do_datadrop_debug('---- screen grab start')
            if serial in result_json['screengrab_request']:
                do_datadrop_debug('---- screen grab serial matched')
                screengrab_request = result_json['screengrab_request'][serial]

                # Only check the non-empty ones
                if screengrab_request:
                    do_datadrop_debug('---- screen grab screengrab_request ' + str(screengrab_request))
                    try:
                        with open("/dev/shm/debug_screengrab.txt", 'w') as f:
                            f.write(screengrab_request + '\n')
                    except:
                        pass

                    # Cross check this with my current grab_count, to see if I should act on it
                    debug_string = "1"
                    try:
                        debug_string = "1a"
                        if screengrab_request == get_image_version() + ":" + str(get_grab_count()).zfill(6):
                            do_datadrop_debug('---- screen grab count matches')
                            debug_string = "2"
                            # do it
                            image_name = 'screen_' + serial + '_' + get_image_version() + ":" + str(
                                get_grab_count()).zfill(6) + '.png'
                            image_file_name = "/dev/shm/" + image_name

                            get_screen_grab_captured_to_local(image_file_name)

                            # now try to send to slicer
                            # https://stackabuse.com/how-to-upload-files-with-pythons-requests-library/
                            the_site_to_call = call_home_locations()[0]
                            the_post_url = the_site_to_call + '/upload'
                            debug_string = "3"
                            do_datadrop_debug('---- screen grab image file ' + str(image_file_name))
                            test_file = open(image_file_name, "rb")
                            debug_string = "4"
                            do_datadrop_debug('---- screen grab post to ' + str(the_post_url))
                            try:
                                test_response = requests.post(the_post_url, verify=False,
                                                              files={"form_field_name": test_file},
                                                              timeout=s_call_home_timeout)
                                            # If this time out happens, then the exception block handles it
                            except:
                                test_response = 'test_response: exception'
                                do_datadrop_debug('---- screen grab send 1 exception')
                                do_datadrop_debug(traceback.format_exc())

                            do_datadrop_debug('---- screen grab post 1 response: ' + str(test_response))
                            debug_string = "5"
                            test_file.close()
                            debug_string = "6"

                            if 'http://' in the_post_url:
                                # also try to send to the https version
                                test_file = open(image_file_name, "rb")
                                debug_string = "4"
                                do_datadrop_debug('---- screen grab https post to ' + str(the_post_url))
                                try:
                                    test_response = requests.post(the_post_url.replace('http://', 'https://'),
                                                                  verify=False, files={"form_field_name": test_file},
                                                                  timeout=s_call_home_timeout)
                                except:
                                    test_response = 'test_response: exception'
                                    do_datadrop_debug('---- screen grab send 2 exception')
                                    do_datadrop_debug(traceback.format_exc())

                                do_datadrop_debug('---- screen grab https post 2 response: ' + str(test_response))
                                debug_string = "5"
                                test_file.close()

                            # mark that we attempted to send it
                            increment_grab_count()
                            debug_string = "7"

                            pass_string, fails = do_one_command('sudo rm ' + image_file_name)

                            slicer_update_bonus = True
                            do_datadrop_debug('---- screen grab complete')
                    except:
                        do_datadrop_debug('---- screen grab exception')
                        do_datadrop_debug(traceback.format_exc())
                        debug_string = "E " + traceback.format_exc()
                        pass

                    try:
                        with open("/dev/shm/debug_screendebug.txt", 'w') as f:
                            f.write(debug_string + '\n')
                    except:
                        pass

        if 'reset_request' in result_json:  # Browser Reset
            if serial in result_json['reset_request']:
                reset_request = result_json['reset_request'][serial]

                # Only check the non-empty ones
                if reset_request:
                    try:
                        with open("/dev/shm/debug_reset.txt", 'w') as f:
                            f.write(reset_request)
                    except:
                        pass

                    # Cross check this with my current boot_count, to see if I should act on it
                    if reset_request == get_image_version() + ":" + str(get_boot_count()):
                        # do it
                        kill_browser()

        # -----------------------------------
        # write content to s_configuration_change_logs
        # write time of last change, update a total count file, write log for humans
        # -----------------------------------
        count_file = s_configuration_change_logs + 'counts.txt'
        last_time_file = s_configuration_change_logs + 'time.txt'
        human_read_file = s_configuration_change_logs + 'readme.txt'
        total_counts = 0
        last_time = 0
        try:
            total_counts = int(open(count_file, 'r').read())
        except:
            pass

        try:
            last_time = int(float(open(last_time_file, 'r').read()))
        except:
            pass

        new_counts = len(configuration_changes)
        if new_counts:
            total_counts += new_counts
            write_content_if_different(str(total_counts), count_file)
            last_time = time.time()
            write_content_if_different(str(last_time), last_time_file)
            try:
                with open(human_read_file, 'a') as f:
                    for item in configuration_changes:
                        f.write(item + '\n')
            except:
                pass

        # publish this as the "total counts", "time of last change"
        save_shared_counts({'configchanges': str(int(total_counts))})
        save_shared_counts({'configtime': str(int(last_time))})

        # -----------------------------------
        # Always take the reboot request last,
        #     to let all data writing activities happen before this
        # -----------------------------------
        if 'reboot_request' in result_json:
            if serial in result_json['reboot_request']:
                reboot_request = result_json['reboot_request'][serial]

                # Only check the non-empty ones
                if reboot_request:
                    try:
                        with open("/dev/shm/debug_reboot.txt", 'w') as f:
                            f.write(reboot_request)
                    except:
                        pass

                    # Cross check this with my current boot_count, to see if I should act on it
                    if reboot_request == get_image_version() + ":" + str(get_boot_count()):
                        # do it
                        do_one_command('sudo reboot')

    except:
        do_datadrop_debug(traceback.format_exc())

    return slicer_update_bonus, work_report


# ----------------------------
def extract_local_time(pass_string):
    # ----------------------------
    return_value = '???'
    for line in pass_string.split('\n'):
        if 'Local time:' in line:
            splits = line.split('Local time:')
            return_value = splits[1].strip().replace(' ', '/')
    return return_value


# ----------------------------
def do_datadrop_work():
    # ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    # monitor with 'watch -n 1 cat /dev/shm/pi_runner_datadrop.txt'

    do_datadrop_debug('runner data drop: Start', do_reset=True)

    serial = get_serial()
    slicer_update_bonus = False

    the_data = build_just_slicer_report()

    location_list = call_home_locations()
    for call_home_index in range(0, len(location_list)):
        call_home_location = location_list[call_home_index]

        do_datadrop_debug('====================================')
        do_datadrop_debug('Call home to : ' + call_home_location)
        do_datadrop_debug('====================================')

        url_result = do_slicer_report(call_home_location, the_data)
        do_datadrop_debug('++++++++++++++++++++++++++++++++++++')

        if call_home_index == 0:  # Only let myself be managed by the primary, all others just get the data
            do_datadrop_debug('---- managment check start ----')
            slicer_update_bonus, work_report = do_handle_call_home_result(serial, url_result)

            if work_report:
                do_datadrop_debug('||||--------------------------------')
                do_datadrop_debug(work_report)
                do_datadrop_debug('||||--------------------------------')

            if slicer_update_bonus:
                do_datadrop_debug('---- slicer bonus start ----')
                the_bonus_data = build_just_slicer_report()
                url_result = do_slicer_report(call_home_location, the_bonus_data)
                do_datadrop_debug('---- slicer bonus end ----')
            do_datadrop_debug('---- managment check end ----')

    do_datadrop_debug('====================================')

    # try to not let keyboard and mouse sleep:
    # https://forums.linuxmint.com/viewtopic.php?t=273460
    # echo on | sudo tee /sys/bus/usb/devices/*/power/level >/dev/null
    # test: cat  /sys/bus/usb/devices/*/power/level
    file_out = '/dev/shm/pi_runner_usbpoweron.txt'
    command_file = '/dev/shm/pi_runner_usbpoweron'
    try:
        with open(command_file, 'w') as f:
            f.write('echo on | sudo tee /sys/bus/usb/devices/*/power/level >/dev/null')

        pass_string, fails = do_one_command('sudo chmod +x ' + command_file)
        pass_string, fails = do_one_command('sudo ' + command_file)

        content = 'pass_string:\n' + pass_string + '\nfail_string:\n' + fail_string
    except:
        content = traceback.format_exc()

    try:
        with open(file_out, 'w') as f:
            f.write(content)
    except:
        pass

    do_datadrop_debug('runner data drop: End')


# ----------------------------
def do_maintenance():
    # ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    try:
        do_datadrop_debug(':::: do_privoxy_summary start')
        do_privoxy_summary()
        do_datadrop_debug(':::: do_privoxy_summary end')
    except:
        do_datadrop_debug(':::: do_privoxy_summary exception')
        pass

    do_datadrop_debug(':::: check_for_inactivity start')
    check_for_inactivity()
    do_datadrop_debug(':::: check_for_inactivity end')

    do_datadrop_debug(':::: do_datadrop_work start')
    do_datadrop_work()
    do_datadrop_debug(':::: do_datadrop_work end')

    # update boot tracker, in case the data just changed
    extra_boots = get_my_extra_boots_count()
    try:
        boot_tracker_data = {'boots_left_to_do':extra_boots, 'from':'do_maintenance'}
        write_content_if_different(json.dumps(boot_tracker_data), s_boot_tracker_file)
    except:
        pass


# ----------------------------
def upload_file_to_server(file_name):
    # ----------------------------
    try:
        the_site_to_call = call_home_locations()[0]
        the_post_url = the_site_to_call + '/upload'
        test_file = open(file_name, "rb")
        test_response = requests.post(the_post_url, verify=False, files={"form_field_name": test_file},
                                      timeout=s_call_home_timeout)
        test_file.close()

    except:
        print(traceback.format_exc())


# ----------------------------
def main():
    # ----------------------------
    do_one_time()  # do this here, to get the timezone set (so that the scheduler does not get upset)

    try:
        from apscheduler.schedulers.background import BackgroundScheduler
        _ = """
If this fails on:
import pytz_deprecation_shim as pds
with:
ValueError: source code string cannot contain null bytes

Then do a:
pip3 install pytz-deprecation-shim --force-reinstall
if: It did let the runner do its thing, then go on with life.

if: Did not help:
    Some systems have old base code? (2.4.1):
        sudo apt-get update
        sudo apt-get upgrade
            This may be interactive, so keep an open terminal session.
                pick "Americas"
                pick "New York"
                pick "Americas"
                pick "New York"

if: Did not help, the SD card might be corrupted, and this is not fixing it)

        """
    except:
        do_command = "sudo pip3 install apscheduler"
        timeout = 30
        one_pass, done_fail = do_one_command(do_command, timeout)

    # To fix the issue seen on "chrome://gpu" page on the pi:
    # "Gpu compositing has been disabled... Disabled Features: gpu_compositing"
    # this is for video drivers for mp4 playback
    # the following line does the trick, takes like 90 seconds new, and 2 seconds when already up to date.
    do_command = "apt-get install libgles2-mesa libgles2-mesa-dev xorg-dev -y"
    timeout = 60 * 3
    one_pass, done_fail = do_one_command(do_command, timeout)

    from apscheduler.schedulers.background import BackgroundScheduler
    sched = BackgroundScheduler(daemon=True)  # Set as a daemon so it will be killed once the main thread is dead.
    job_id_thread1 = sched.add_job(run_original_main_thread) # this one does a while True, so it does not need the interval
    job_id_thread2 = sched.add_job(run_extra_main_thread, 'interval', seconds=5, coalesce=True)
    job_id_thread3 = sched.add_job(run_screen_reporting_thread, 'interval', seconds=5 * 60, coalesce=True)
    sched.start()
    while True:
        time.sleep(1)
    sched.shutdown()


# ----------------------------
def get_screen_grab_captured_to_local(image_file_name):
    # ----------------------------
    traceback_report = ''

    try:
        temp_file = "/dev/shm/screen_grab"
        cmd = """
rm /dev/shm/screen.xxd
xhost +local:pi
export DISPLAY=:0
scrot """ + image_file_name + """
"""
        pass_string, fails = do_one_command('sudo rm ' + temp_file)
        with open(temp_file, 'w') as f:
            f.write(cmd)
        print('cmd', cmd)

        pass_string, fails = do_one_command('which scrot')
        traceback_report += '\nA ' + pass_string + '; ' + fails
        # to test:
        # sudo apt-get remove scrot -y
        if not 'scrot' in pass_string:
            print('scrot exists test failed... trying to install scrot...')
            pass_string, fails = do_one_command('sudo apt update -y')  # This goes to the internet
            pass_string, fails = do_one_command('sudo apt-get install --fix-missing -y scrot')  # this hits local only
            print('scrot installed')

        pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)
        traceback_report += '\nB ' + pass_string + '; ' + fails
        pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
        traceback_report += '\nC ' + pass_string + '; ' + fails
        pass_string, fails = do_one_command('sudo runuser -u worker -- ' + temp_file)
        traceback_report += '\nD ' + pass_string + '; ' + fails
        pass_string, fails = do_one_command('sudo rm ' + temp_file)
        traceback_report += '\nE ' + pass_string + '; ' + fails
    except:
        traceback_report = traceback.format_exc()

    return traceback_report


# ----------------------------
def run_screen_reporting_thread():
    # ----------------------------
    time_started = time.time()

    try:
        os.remove('/dev/shm/screen_grab_to_report.png')
    except:
        pass

    try:
        import cv2
        import numpy
    except:
        timeout = 300
        do_command = "sudo apt install python3-opencv -y"
        one_pass, done_fail = do_one_command(do_command, timeout)
        # https://numpy.org/devdocs/user/troubleshooting-importerror.html
        do_command = "sudo apt-get install libatlas-base-dev -y"
        one_pass, done_fail = do_one_command(do_command, timeout)

        # sudo pip3 uninstall numpy -y
        # sudo apt install python3-numpy

    open('/dev/shm/debug_pi_runner_run_screen_reporting_thread', 'w').write('1')
    try:
        # Screen report to Slicer
        # process the image, to see if there is a lower bar of black, or measure what percentage up from the
        #   bottom we have to come, before we get to non black... so a white screen reports 0, a black screen reports 100,
        #   and a screen with resolution issues would report something in the 20 to 50% range, depending on the settings.
        # so, for each row of pixels, sum them, and represent in an array

        # Do one cycle worth, and return; we will get called again
        result = get_screen_grab_captured_to_local('/dev/shm/screen_grab_to_report.png')
        open('/dev/shm/debug_pi_runner_run_screen_reporting_thread', 'w').write('2: ' + result)

        import cv2
        import numpy

        img = cv2.imread('/dev/shm/screen_grab_to_report.png')
        _ = """
>>> len(img)
2160
>>> len(img[0])
3840
"""

        sums = []
        for row in range(0, len(img)):
            sums.append(img[row].sum())

        tail_length = find_sums_tail_length(sums)
        total = len(sums)
        percent = int((100.0 * float(tail_length)) / float(total))

        # black_wrap_image.gif as the content to display (an old screen grab)
        # percent 25, tail 560 of 2160, elapsed 6.805940389633179

        try:
            previous_percent = int(open('/dev/shm/runner_screen_report_previous.txt', 'r').read())
        except:
            previous_percent = 0

        time_ended = time.time()

        persistent_threshold = 90
        if (percent >= persistent_threshold) and (not previous_percent >= persistent_threshold):
            # do not report the first really high reading, in case it was a multimedia player, going through a black transition
            did_report_it = False
        else:
            save_shared_counts({'screenreport': str(percent)})
            did_report_it = True

        try:
            open('/dev/shm/runner_screen_report_previous.txt', 'w').write(str(percent))
        except:
            pass

        open('/dev/shm/debug_pi_runner_run_screen_reporting_thread', 'w').write(
            'percent ' + str(percent) + ', ' + 'tail ' + str(tail_length) + ' of ' + str(
                total) + ', ' + 'elapsed ' + str(time_ended - time_started) + '\n' + \
                'percent' + ' ' + str(percent) + ', ' + 'previous_percent' + ' ' + str(previous_percent) + ', ' + 'did_report_it' + ' ' + str(did_report_it))

    except:
        # don't die on something silly
        open('/dev/shm/debug_pi_runner_run_screen_reporting_thread', 'w').write(traceback.format_exc())
        pass


# watch -n 10 cat /dev/shm/debug_pi_runner_run_screen_reporting_thread

# ----------------------------
def run_extra_main_thread():
    # ----------------------------
    try:
        # Do one cycle worth, and return; we will get called again
        if not os.path.exists(os.path.dirname(s_runnable_work_path)):
            os.makedirs(os.path.dirname(s_runnable_work_path))

        _ = """
s_runnable_work_path
    /do_command_1234
        /do_command (atomic write, so if it is there, it is complete)
        (process picks it up, and deletes it after writing the done_command file)
            1234, 15
            ls -l /

        /done_pass
        /done_fail
        /done_exception

        (written last:)
        /done_command (the content that was originally in do_command)

        (zzz_prefix_serial_commandId_timeInSeconds.zip, to be sent back to server)
        /zzz_done_command_10000000aaef9969_1234.txt

The zzz makes it sort to the bottom of the upload listing,
    to not swamp the top of the other content there.

    """

        files = os.listdir(s_runnable_work_path)
        for found_file in files:
            if 'do_command' in found_file:
                found_folder = s_runnable_work_path + found_file + '/'

                found_do_work = found_folder + 'do_command'
                if os.path.isfile(found_do_work):
                    content = open(found_do_work, 'r').read()
                    splits = content.split('\n')

                    # (command_id), (timeout)
                    sub_splits = splits[0].split(',')
                    command_id = sub_splits[0]

                    timeout = 15
                    if len(sub_splits) > 1:
                        try:
                            timeout = float(sub_splits[1])
                        except:
                            pass

                    if len(splits) > 1:
                        do_command = splits[1]

                    if command_id and do_command:
                        done_pass = ''
                        done_fail = ''
                        done_exception = ''

                        try:
                            done_pass, done_fail = do_one_command(do_command, timeout)
                        except:
                            done_exception = traceback.format_exc()

                        if not done_pass:
                            done_pass = '(empty)'
                        if not done_fail:
                            done_fail = '(empty)'
                        if not done_exception:
                            done_exception = '(empty)'

                        output_content = ""
                        output_content += '--> command:' + '\n' + content + '\n'
                        output_content += '--> done_pass:' + '\n' + done_pass + '\n'
                        output_content += '--> done_fail:' + '\n' + done_fail + '\n'
                        output_content += '--> done_exception:' + '\n' + done_exception + '\n'

                        output_file = found_folder + 'zzz_done_command_' + get_serial() + '_' + command_id + '.txt'
                        write_content_if_different(output_content, output_file)

                        upload_file_to_server(output_file)

                        os.remove(found_folder + 'do_command')
    except:
        # don't die on something silly
        pass


# ----------------------------
def run_original_main_thread():
    # ----------------------------

    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string for " + service + ": " + version)

    # on initial boot, maybe the screen has not fully communicated with the pi yet.
    # Give it some time to settle, and then grab the screen size

    time.sleep(15)
    check_browser_for_restart()

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()
        do_datadrop_debug(':::: back from do_maintenance')
        time_now = time.time()
        while (abs(time_now - time.time()) < 60):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            if wake_count % 10 == 0:
                do_datadrop_debug('wake count:' + str(wake_count))
            try:
                with open(s_runner_wake_file, 'w') as f:
                    f.write(str(wake_count))
            except:
                print("!!! failed to write wake_count")


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Command line interface
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ====================================
def get_os_from_release(release):
    # ====================================
    return_value = ''

    for item in release.split('\n'):
        splits = item.split('=')
        if len(splits) > 1:
            if splits[0] == 'ID':
                return_value = splits[1].replace('"', '')

    return return_value


# ====================================
def get_os_name():
    # ====================================
    os_release = open('/etc/os-release', 'r').read()
    os_name = get_os_from_release(os_release)

    return os_name


# ====================================
def get_items_to_run_OS(os_name):
    # ====================================

    items_to_run = []

    # FixMe: Need to eventually do all os setup items here, instead of in AA-SD-image-building.txt
    items_to_run.append('sudo apt-get install xxd')

    # For the mp4 content to work... get it pre-installed here
    items_to_run.append('sudo apt-get install libgles2-mesa libgles2-mesa-dev xorg-dev -y')

    return items_to_run


# ====================================
def install_os_needed_content():
    # ====================================
    time_start = time.time()
    print('Do the install ...')

    os_name = get_os_name()

    commands = get_items_to_run_OS(os_name)

    if commands:
        print('Do install of system libraries...')
        for command in commands:
            print("---------------------------")
            print(" cmd: " + command)
            p, f = do_one_command(command)
            print("pass: " + p)
            print("fail: " + f)
            if f:
                print("^^^^^^^^^^^^^^^^^^^^^^^^^^^")
                print("^^^^^^^^^^^^^^^^^^^^^^^^^^^")
                print("^^^^^^   FAILED    ^^^^^^^^")
                print("^^^^^^^^^^^^^^^^^^^^^^^^^^^")
                print("^^^^^^^^^^^^^^^^^^^^^^^^^^^")
                exit(1)

        print('--------------------')
        print('\n\n\n')
    else:
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('OS not yet handled: ' + os_name)
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('!!!!!!!!!!!!!!!!!!!!!!!')

    time_end = time.time()
    print('total time ' + str(int(time_end - time_start)) + ' seconds')


# ====================================
def main_interactive():
    # ====================================

    if len(sys.argv) > 1:
        for arg in sys.argv:
            print(arg)
            if arg == 'install':
                # clean out any left over timezone setting
                set_timezone('UTC', 'main_interactive')

                install_os_needed_content()
                set_service_start('pi_' + service, print_debug=True)
    else:
        print('use: sudo python3 ' + 'pi_' + service + '.py install')


# ====================================
if __name__ == '__main__':
    # ====================================
    main_interactive()


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_screen_geometry(self):
        fbset_pass = """

mode "640x480"
    geometry 640 480 3840 2160 16
    timings 0 0 0 0 0 0 0
    accel true
    rgba 5/11,6/5,5/0,0/0
endmode
"""
        expected = "640/480/3840/2160/16"
        actual = extract_screen_geometry_from_fbset_pass(fbset_pass)
        self.assertEqual(actual, expected)

    def test_extract_local_time(self):

        pass_string = """
               Local time: Mon 2022-05-16 12:55:06 EDT
           Universal time: Mon 2022-05-16 16:55:06 UTC
                 RTC time: n/a
                Time zone: America/New_York (EDT, -0400)
System clock synchronized: yes
              NTP service: active
          RTC in local TZ: no
        """
        expected = "Mon/2022-05-16/12:55:06/EDT"
        actual = extract_local_time(pass_string)
        self.assertEqual(actual, expected)

    def test_get_item_from_network_query(self):
        the_command = "nmcli -f UUID,name,TIMESTAMP,TIMESTAMP-REAL c"
        pass_string = """UUID                                  NAME  TIMESTAMP   TIMESTAMP-REAL
45aee000-7dbe-4c1c-85b3-494167837f70  eth0  1662123684  Fri 02 Sep 2022 09:01:24 EDT
"""
        expected = "45aee000-7dbe-4c1c-85b3-494167837f70"
        actual = get_item_from_network_query(pass_string)
        self.assertEqual(actual, expected)

    def test_get_item_from_network_query_missing(self):
        the_command = "nmcli -f name,TIMESTAMP,TIMESTAMP-REAL c"
        pass_string = """NAME  TIMESTAMP   TIMESTAMP-REAL
eth0  1662123684  Fri 02 Sep 2022 09:01:24 EDT
"""
        expected = ""
        actual = get_item_from_network_query(pass_string)
        self.assertEqual(actual, expected)

    def test_get_item_from_network_query_in_second_column(self):
        the_command = "nmcli -f name,UUID,TIMESTAMP,TIMESTAMP-REAL c"
        pass_string = """NAME  UUID                                  TIMESTAMP   TIMESTAMP-REAL
eth0  45aee000-7dbe-4c1c-85b3-494167837f70  1662123684  Fri 02 Sep 2022 09:01:24 EDT
"""
        expected = "45aee000-7dbe-4c1c-85b3-494167837f70"
        actual = get_item_from_network_query(pass_string)
        self.assertEqual(actual, expected)

    def test_get_item_from_network_query_in_second_column_explicit_item(self):
        the_command = "nmcli -f name,UUID,TIMESTAMP,TIMESTAMP-REAL c"
        pass_string = """NAME  UUID                                  TIMESTAMP   TIMESTAMP-REAL
eth0  45aee000-7dbe-4c1c-85b3-494167837f70  1662123684  Fri 02 Sep 2022 09:01:24 EDT
"""
        expected = "45aee000-7dbe-4c1c-85b3-494167837f70"
        actual = get_item_from_network_query(pass_string, item='UUID')
        self.assertEqual(actual, expected)

    def test_get_item_from_network_query_empty_command_response(self):
        the_command = "nmcli -f name,UUID,TIMESTAMP,TIMESTAMP-REAL c"
        pass_string = ""
        expected = ""
        actual = get_item_from_network_query(pass_string)
        self.assertEqual(actual, expected)

    def test_get_device_from_network_query(self):
        the_command = 'nmcli -f DEVICE,UUID c'
        pass_string = """DEVICE  UUID
eth0    45aee000-7dbe-4c1c-85b3-494167837f70"""
        expected = "eth0"
        actual = get_item_from_network_query(pass_string, item="DEVICE")
        self.assertEqual(actual, expected)

    def test_calculate_device_number_from_device_value(self):
        device = 'junk'
        expected = '0'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

        device = 'junk2'
        expected = '0'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

        device = '--'  # No wire connected, and no valid wifi to connect to
        expected = '1'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

        device = 'eth0'
        expected = '2'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

        device = 'wlan0'
        expected = '3'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

        device = 'wlan1'
        expected = '4'
        actual = calculate_device_number_from_device_value(device)
        self.assertEqual(actual, expected)

    def test_is_controlled_image(self):
        cardinal_dir = []
        expected = False
        actual = is_controlled_image(cardinal_dir)
        self.assertEqual(actual, expected)

        cardinal_dir = ['image_ver.txt']
        expected = True
        actual = is_controlled_image(cardinal_dir)
        self.assertEqual(actual, expected)

    def test_convert_config_values_from_resolution(self):
        resolution = 'VGA(640x480) 1 1'
        expected = """hdmi_group=1
hdmi_mode=1
"""
        actual = convert_config_values_from_resolution(resolution)
        self.assertEqual(actual, expected)

        resolution = '4Kuhd(3840x2160) 2 87 3840,2160,24'
        expected = """hdmi_group=2
hdmi_mode=87
hdmi_cvt 3840 2160 24
"""
        actual = convert_config_values_from_resolution(resolution)
        self.assertEqual(actual, expected)

        resolution = '4Kuhd(3840x2160) 2 87 3840,2160,24 3840 2160 400000000'
        expected = """hdmi_group=2
hdmi_mode=87
hdmi_cvt 3840 2160 24
max_framebuffer_width=3840
max_framebuffer_height=2160
hdmi_pixel_freq_limit=400000000
"""
        actual = convert_config_values_from_resolution(resolution)
        self.assertEqual(actual, expected)

    def test_determine_file_type_from_header(self):

        _test = """
cd gif_mp4_files
python3
import os
files_found = os.listdir()
for file_found in files_found:
    header = read_header_from_file(file_found)
    file_type = determine_file_type_from_header(header)
    if file_type != file_found.split('.')[-1]:
        print ('!!!!!!!!!!!!!!!!!!!!!!!!!')
    print (header, ',', file_found, ',', file_type)
    if file_type != file_found.split('.')[-1]:
        print ('!!!!!!!!!!!!!!!!!!!!!!!!!')

"""

        test_cases = []
        if True:
            test_cases.append([b'<!DOCTYPE html><html lang="en-', 'rabbit.html', 'unknown'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show1_(active)_20240131150914_(orig_file)_Heroe_de_Calidad_-_Diciembre.gif',
                               'gif'])
            test_cases.append(
                [b'GIF89a\xe0\x01\x0e\x01\xf7\x00\x00\xff\xff\xff\xc2\x8d\n\xc7\xcb\x99\xade.\xc8\xa3\x0e=\x0c',
                 'fine.gif', 'gif'])
            test_cases.append(
                [b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x00\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x04\x04\x04\x04\x04',
                 'iu.jpeg', 'jpeg'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show2_(active)_20240220151157_(orig_file)_Bloque_2.gif',
                               'gif'])
            test_cases.append(
                [b"GIF89a\xe0\x01\x0e\x01\xf7\x00\x00\t\x19\x18\x08\x1b%\x12\x1c'\x0c$&\x18)&\x1b3", 'beach.gif',
                 'gif'])
            test_cases.append([b'\x00\x00\x00 ftypisom\x00\x00\x02\x00isomiso2avc1mp', 'sample-mp4-file.mp4', 'mp4'])
            test_cases.append(
                [b'GIF89a\xe0\x01q\x01\xf7\x00\x00\x00\xff\x00\x0b\x12\x1d\x0b\x13 \r\x13\x1e\r\x15!\x10\x18',
                 'giphy_s.gif', 'gif'])
            test_cases.append(
                [b'GIF89a\x90\x01\xe1\x00\xf5?\x00\xff\xfd\xa0\xff\xff\xfc\xf4\xa3Q\xff\xcf.\xf8n\x0f\xd1N',
                 'profile_VPN1_multimedia_demo_(active)_20240102213930_(orig_file)_fireplace.gif', 'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_TX134_multimedia_Show1_(active)_20240214183927_(orig_file)_Bloque_2.gif',
                               'gif'])
            test_cases.append(
                [b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x01\x1d\x00\x00\x00m\x08\x06\x00\x00\x00\x9c',
                 'image001[9756].png', 'png'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show2_(active)_20240208190242_(orig_file)_Bloque_2.0_presentacion.gif',
                               'gif'])
            test_cases.append(
                [b'GIF89a\x80\x02h\x01\xf7\x00\x00\x00\x00\x00\x00\n\x16\x0e\x10\x0e\x02\x10\x1d\x16\x01\x00\x11\x0e',
                 'not-vortex-small.gif', 'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show4_(active)_20240215194645_(orig_file)_San_Valentin.gif',
                               'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show4_(active)_20240213161624_(orig_file)_TV__13_de_Febrero.gif',
                               'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show1_(active)_20240220151134_(orig_file)_Bloque_1_presentacion.gif',
                               'gif'])
            test_cases.append(
                [b'GIF89a&\x026\x01\xf7\x00\x00Z\x8c\xacK9C\x8b\x97\xac\xed\xb0\xb2\xb9\xd6\xed\xb7\xca', 'KITT.gif',
                 'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show4_(active)_20231221163103_(orig_file)_TV__2_Noviembre_2023 (1).gif',
                               'gif'])
            test_cases.append([b'GIF89a\xe0\x01\x08\x01\xf7\x00\x00\xb0\xad\xb53DF\xd6\xa3Thw\xaa\xad\xbb\xd2\xce\xcc',
                               'gallery-1471381857-gif-season-2.gif', 'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show1_(active)_20240208190222_(orig_file)_Bloque_1_presentacion.gif',
                               'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_MEX03_multimedia_Show3_(active)_20240208190349_(orig_file)_Bloque_3,0_presentacion.gif',
                               'gif'])
            test_cases.append([
                                  b'GIF89a\xe0\x01\xcc\x00\xf7\xcb\x00\x00\x00\x00\x01\x01\x01\x02\x02\x02\x03\x03\x03\x04\x04\x04\x05\x05',
                                  'rain.gif', 'gif'])
            test_cases.append(
                [b'GIF89a\xe0\x01h\x01\xf7F\x00\x08\x1a\x15\t+\x11\n\x1a$\n9*\x0b \x18\x0e ', 'static.gif', 'gif'])
            test_cases.append(
                [b'GIF89a\xe0\x01\x0e\x01\xf7\xff\x00\xd2\xdb\xde\xd0\xd6\xd2\xbe\xb4\xa8--\x11.$\x1a\xbe\xbd',
                 'wheelie.gif', 'gif'])
            test_cases.append([b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x01R\x00\x00\x00\xbb\x08\x06\x00\x00\x007',
                               'Paugh, Jeffrey.png', 'png'])
            test_cases.append([b'\xff\xd8\xff\xe1\x0ezExif\x00\x00MM\x00*\x00\x00\r\n\xff\xd8\xff\xed\x00\x0cAdob',
                               '7a8b0017852b90a645bf02.jpg', 'jpg'])
            test_cases.append(
                [b'GIF89a\x90\x01b\x01\xe6v\x00\x00\x00\x00\x02\x02\x08\x05\t\x11\x13\x17!\x0c\x12\x1e\x02\x06',
                 'tumblr_mj0vvcqnzx1qdlh1io1_400.gif', 'gif'])
            test_cases.append([b'GIF89ad\x00d\x00\xf4\x00\x00\xff\xff\xff\xe0\x1f\x1f\xf0\x9b\x9b\xeczz\xe6NN\xe7S',
                               'CAH-loading.gif', 'gif'])
            test_cases.append(
                [b'GIF89a\xe8\x033\x02\xf6\x00\x00\x00\x00\x00$\x00\x00H\x00\x00l\x00\x00\x90\x00\x00\xb4\x00',
                 'Cardinal-Health_intro_GIF_ipad.gif', 'gif'])
            test_cases.append([b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\xec\x00\x00\x01A\x08\x06\x00\x00\x00t',
                               'peopling.png', 'png'])
            test_cases.append(
                [b'GIF89a\xfa\x00\xb4\x00\xf0\x00\x00\x00\x00\x00\xff\xff\xff!\xff\x0bNETSCAPE', 'life.gif', 'gif'])
            test_cases.append([b'GIF89aU\x03\xe0\x01p\x00\x00!\xff\x0bNETSCAPE2.0\x03\x01\x00',
                               'profile_TX134_multimedia_Show1_(active)_20240214183927_(orig_file)_Bloque_2 2.gif',
                               'gif'])
            test_cases.append(
                [b'GIF89a\xf4\x01\xaa\x01\xe6\x00\x00\xaa\xee\xdc\xea\xfb\xff\xdc\xaaF\x99\xee\xee\xfd3"n\xab',
                 'pooh-color.gif', 'gif'])
            test_cases.append([b'GIF89a\xf4\x01\xaa\x01\xf4\x00\x00\x1d\x1d\x1d   >>>BBBHHHVV', 'pooh.gif', 'gif'])
            test_cases.append([b'\x00\x00\x00\x18ftypmp42\x00\x00\x00\x00isommp42\x00\x01\x08Cmo',
                               'profile_VPN1_multimedia_test_mp4_(active)_20240306212533_(orig_file)_Juarez_Presource.mp4',
                               'mp4'])
            test_cases.append(
                [b'GIF89a\x90\x01\xe1\x00\xf5?\x00\xff\xfd\xa0\xff\xff\xfc\xf4\xa3Q\xff\xcf.\xf8n\x0f\xd1N',
                 'fireplace.gif', 'gif'])
            test_cases.append([
                                  b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x00\x00\x01\x00\x01\x00\x00\xff\xdb\x00\x84\x00\t\x06\x07\x12\x12',
                                  'fine1.jpeg', 'jpeg'])

        for test_case in test_cases:
            header = test_case[0]
            expected = test_case[2]
            actual = determine_file_type_from_header(header)
            self.assertEqual(actual, expected)

    def test_make_timezone_command(self):
        time_zone = 'UTC'
        expected = 'sudo timedatectl set-timezone UTC'
        actual = make_timezone_command(time_zone)
        self.assertEqual(actual, expected)

    def test_find_sums_tail_length(self):
        sums = [10, 11, 12, 15, 0, 0, 0]
        expected = 3
        actual = find_sums_tail_length(sums)
        self.assertEqual(actual, expected)

    def test_clean_uname(self):
        uname = 'Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux'
        expected = '5.10.103-v7l+__1529_SMP_Tue_Mar_8_12:24:00_GMT_2022_armv7l_GNU/Linux'
        actual = clean_uname(uname)
        self.assertEqual(actual, expected)

        uname = 'Linux cah-rp-ec885c8aa5f46f0d 6.6.20+rpt-rpi-v8 #1 SMP PREEMPT Debian 1:6.6.20-1+rpt1 (2024-03-07) aarch64 GNU/Linux'
        expected = '6.6.20+rpt-rpi-v8__1_SMP_PREEMPT_Debian_1:6.6.20-1+rpt1_(2024-03-07)_aarch64_GNU/Linux'
        actual = clean_uname(uname)
        self.assertEqual(actual, expected)

# End of source file
