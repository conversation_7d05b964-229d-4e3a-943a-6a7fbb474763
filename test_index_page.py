#!/usr/bin/env python3
"""
Test script specifically for the index page functionality
"""

import sys
import os

def test_index_page():
    """Test that the index page works correctly"""
    print("Testing index page functionality...")
    
    try:
        # Import the index module
        import slicer_wsgi_index
        
        # Create a test environment
        environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'HTTP_COOKIE': '',
            'QUERY_STRING': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.url_scheme': 'http'
        }
        
        # Test the make_body function
        body, other = slicer_wsgi_index.make_body(environ)
        
        print("✅ Index page make_body() function works")
        print(f"   Body length: {len(body)} characters")
        
        # Check for expected content
        if 'Slicer' in body:
            print("✅ Contains 'Slicer' in content")
        else:
            print("⚠️  Does not contain 'Slicer' in content")
        
        # Test the full WSGI application
        responses = []
        def start_response(status, headers):
            responses.append((status, headers))
        
        result = slicer_wsgi_index.application(environ, start_response)
        
        if responses:
            status, headers = responses[0]
            print(f"✅ WSGI application returns status: {status}")
            
            # Check if it's a redirect or content
            if status.startswith('302'):
                print("✅ Redirects as expected (likely to login)")
            elif status.startswith('200'):
                print("✅ Returns content successfully")
            else:
                print(f"⚠️  Unexpected status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Index page test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_permissions_function():
    """Test the missing permissions function"""
    print("\nTesting permissions cache function...")
    
    try:
        from dev import permissions, datastore
        
        # Get current datastore content
        data_store_content = datastore.all_datastore()
        
        # Test the make_permissions_cache_from_datastore function
        cache = permissions.make_permissions_cache_from_datastore(data_store_content)
        
        print("✅ make_permissions_cache_from_datastore() function works")
        print(f"   Cache keys: {list(cache.keys())}")
        
        if 'users' in cache:
            print(f"   Users found: {list(cache['users'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Permissions cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all index page tests"""
    print("=" * 60)
    print("Slicer Index Page - Specific Test Suite")
    print("=" * 60)
    
    tests = [
        test_index_page,
        test_permissions_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Index Page Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Index page is working correctly!")
    else:
        print("❌ Some index page tests failed.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
