_ = """
sudo vi /cardinal/pi_bluetooth.py

sudo systemctl restart pi-bluetooth.service
sudo systemctl stop pi-bluetooth.service


tail -f /dev/shm/BLUETOOTH_OUTPUT

"""

_ = """
The workflow here involves having the primary service launch a
helper tmux session, that periodically gets killed, and restarted,
and it is that tmux session that interacts with the bluetoothd process,
with its output going to /dev/shm/DBLUETOOTH_OUTPUT

Logging opportunities are around:
killing that tmux
connection attempts ending in failure, or success

What would show up well on an hourly summary (min, max, mean):
Connected seconds so far

Number of connection attempts (total, like boot counts?)

"""


use_info_command_during_discovery = False   # new normal is False
fake_extra_devices_active = False           # make False after completion of stress testing
fake_extra_devices_count = 50 # 50

service = 'bluetooth'
version = 'B.2.3'

connect_method = 2  # 1 is where the device requests the pairing, 2 is where the human requests the pairing.

# Original settings
connection_check_time = 3   # This is the thread operating rate
thread_bluetooth_deadman_time = 20
tmux_restart_time = 60 * 30

last_device_poll_time = 5   # maybe this one in PR005 generates too much traffic, too often?
reaction_step_one_time = 60
reaction_step_two_time = 60
reaction_trusted_time = 60
pairing_holdoff_time = 20

# try new settings
last_device_poll_time = 60 # Try a longer interval for finding new devices, to not swamp the communications
reaction_step_one_time = last_device_poll_time * 3
reaction_step_two_time = last_device_poll_time * 3
reaction_trusted_time = last_device_poll_time * 5

release_notes = """
General Note:
Any time the bluetooth service is changed, there is a restart of all bluetooth functions,
which will disconnect any active bluetooth devices at that point.

2021.12.16
B.2.3
Add reporting of the counts related to conecting to a device.

2021.09.01
B.2.2
Since we no longer collect info during discovery, we do not get RSSI, so don't show that column.

2021.08.31
B.2.1
Put back in the periodic 'power on' command.
Disable the use of the info command; maybe this was overloading scanners?

2021.08.26
B.2.0
Change the retry count on connecting to a new device.

2021.08.24
B.1.9
No longer try to eliminate old devices; we are no longer using "trust" as a connection requirement.
Correctly look at all devices in the selection list, to see which one is picked.

2021.08.20
B.1.8
If mode 2, then do not force the trust early (do not swamp with lots of trusted devices)

2021.08.18
B.1.7
Actually modify the polling period from 5 seconds to 60.

2021.08.12
B.1.6
Do not poll so often, to reduce loading where there are lots of bluetooth devices.

2021.07.13
B.1.5
Add a hint to the top of the pairing list to do a single click.

2021.07.12
B.1.4
Remove the prompt for 'alt F4', and instead provide a link to click, to get back to the home page.
Once we are into the pairing process, set 'scan off'

2021.07.03
B.1.3
When paired, show a less tall barcode, to not take so much vertical space in the page.

2021.07.02
B.1.2
Pull the name of the device (serial number) to show in the pairing selection table.

2021.06.28
B.1.1
Improve the connection start process, to automatically retry connecting on initial failure.
??? Replace the self pairing barcode with a link to selection page of available devices.

2021.06.02
B.1.0
Start a new service, to connect to the Zebra RS6000, initially for PR005

"""

other_content = """
sudo vi /cardinal/pi-bluetooth
sudo chmod +x /cardinal/pi-bluetooth

# ===== begin: start file
#!/usr/bin/env python3
import pi_bluetooth
pi_bluetooth.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-bluetooth.service
sudo systemctl daemon-reload
sudo systemctl stop pi-bluetooth.service
sudo systemctl start pi-bluetooth.service
sudo systemctl enable pi-bluetooth.service

sudo systemctl restart pi-bluetooth.service

systemctl status pi-bluetooth.service

# Logging of std out
cat /var/log/syslog | fgrep pi-bluetooth

OR

tail -n 1000 -f /var/log/syslog | fgrep pi-bluetooth

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=bluetooth.target

[Service]
ExecStart=/cardinal/pi-bluetooth
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

other_notes = """
https://git.kernel.org/pub/scm/bluetooth/bluez.git/about/
http://www.bluez.org


"""
notes_20210928 = """
Do we have a vulnerability to data matrix with control codes:

https://barcode.tec-it.com/en/DataMatrix?data=This%20is%20a%20Data%20Matrix%20by%20TEC-IT


"""


import copy
import datetime
import json
import os
import requests
import shutil
import socket
import stat
import subprocess
import sys
import time
import traceback

from apscheduler.schedulers.background import BackgroundScheduler

s_days_to_keep = 14
s_base_raw_path = "/cardinal/log/pi_bluetooth/"

# ----------------------------
def log_cleanup():
# ----------------------------
    days_present = sorted(os.listdir(s_base_raw_path))
    if len(days_present) > s_days_to_keep:
        for day_to_remove in days_present[:-1 * s_days_to_keep]:
            try:
                shutil.rmtree(s_base_raw_path + day_to_remove)
            except:
                pass

# ----------------------------
def log_generic(sublog, log_string):
# ----------------------------
    TSfast = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    log_string = TSfast + " : " + log_string

    TS = datetime.datetime.now().strftime('%Y%m%d')
    output_file = s_base_raw_path + TS + "/" + sublog

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'a') as f:
        f.write(log_string + '\n')

    log_cleanup()

# ----------------------------
def log_a_special(log_string):
# ----------------------------
    increment_a_count("specials_count")

    log_generic('special', log_string)

# ----------------------------
def is_running_bluetoothd():
# ----------------------------
    _ = """
8821 ?        Ss     0:00 tmux new-session -d -s BluetoothDSession sudo bluetoothd |& tee /dev/shm/DBLUETOOTH_OUTPUT
30038 pts/2    Ss+    0:00 bash -c sudo bluetoothd |& tee /dev/shm/DBLUETOOTH_OUTPUT
30040 pts/2    S+     0:00 sudo bluetoothd
30042 pts/2    S+     0:00 bluetoothd
31342 pts/3    S+     0:00 grep -F --color=auto bluetoothd
    """

    return_value = False
    try:
        pass_string, fail_string = do_one_command("ps ax")

        process_number = ''
        for item in pass_string.split("\n"):
            if ('bluetoothd' in item) and (not 'sudo' in item) and (not 'grep' in item):
                process_number = item.split()[0].strip()

        if process_number:
            # 14373
            return_value = True
    except:
        print(traceback.format_exc())

    return return_value


# ----------------------------
def kill_bluetoothd():
# ----------------------------
    try:
        pass_string, fail_string = do_one_command("ps ax")

        process_number = ''
        for item in pass_string.split("\n"):
            if 'bluetoothd' in item:
                process_number = item.split()[0].strip()

        if process_number:
            # 14373
            pass_string, fail_string = do_one_command("sudo kill " + process_number)
    except:
        print(traceback.format_exc())

# ----------------------------
def kill_tmux_bluetoothd_session():
# ----------------------------
    try:
        pass_string, fail_string = do_one_command("ps ax") # | fgrep chromium-browser | fgrep cardinal | cut -d ' ' -sf1")

        process_number = ''
        for item in pass_string.split("\n"):
            if 'tmux new-session -d -s BluetoothDSession sudo bluetoothd' in item:
                process_number = item.split()[0].strip()

        if process_number:
            # 14373
            pass_string, fail_string = do_one_command("sudo kill " + process_number)
    except:
        print(traceback.format_exc())


# ----------------------------
def kill_tmux_session():
# ----------------------------
    try:
        pass_string, fail_string = do_one_command("ps ax") # | fgrep chromium-browser | fgrep cardinal | cut -d ' ' -sf1")

        process_number = ''
        for item in pass_string.split("\n"):
            if 'tmux new-session -d -s BluetoothSession sudo bluetoothctl' in item:
                process_number = item.split()[0].strip()

        if process_number:
            # 14373
            pass_string, fail_string = do_one_command("sudo kill " + process_number)
    except:
        print(traceback.format_exc())

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial


# Globals for the threads to use
G_thread_manage_bluetooth_connections = {}
G_thread_manage_bluetooth_connections['serial'] = get_serial()

G_thread_manage_bluetooth_connections['bluetoothd'] = {}
G_thread_manage_bluetooth_connections['bluetoothd']['condition'] = 'start'
G_thread_manage_bluetooth_connections['bluetoothd']['time_of_last'] = time.time()

G_thread_manage_bluetooth_connections['state'] = {}
G_thread_manage_bluetooth_connections['state']['started'] = ''
G_thread_manage_bluetooth_connections['state']['paired-devices'] = []
G_thread_manage_bluetooth_connections['state']['devices'] = []
G_thread_manage_bluetooth_connections['state']['last_device_poll_time'] = 0
G_thread_manage_bluetooth_connections['state']['last_device_stale_time'] = 0

# new reporting:
G_thread_manage_bluetooth_connections['state']['time_entered_connected'] = 0
G_thread_manage_bluetooth_connections['state']['interval_spent_connected'] = 0

#
G_thread_manage_bluetooth_connections['state']['tmux_file_start_time'] = 0

G_thread_manage_bluetooth_connections['state']['Agent_unregistered'] = 0
G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = 0

G_thread_manage_bluetooth_connections['state']['last_device_seen_address'] = ''
G_thread_manage_bluetooth_connections['state']['debug'] = ''
G_thread_manage_bluetooth_connections['state']['blue_address'] = ''

G_thread_manage_bluetooth_connections['state']['trusted'] = {}

G_thread_manage_bluetooth_connections['state']['Connected_address'] = ''
G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] = 0

G_thread_manage_bluetooth_connections['state']['Connected'] = []
G_thread_manage_bluetooth_connections['state']['Paired'] = []
G_thread_manage_bluetooth_connections['state']['RSSI'] = []

G_thread_manage_bluetooth_connections['log_file_handle'] = None
G_thread_manage_bluetooth_connections['log_file_lines_count'] = 0
G_thread_manage_bluetooth_connections['startup_report'] = ''

G_thread_manage_bluetooth_connections['status'] = '(no status)'

G_thread_call_back_to_slicer = {}
G_thread_call_back_to_slicer['time_of_last_send'] = 0
G_thread_call_back_to_slicer['serial'] = get_serial()


# ----------------------------
def get_a_count(the_name):
# ----------------------------
    the_count = '0'
    try:
        with open("/cardinal/bluetooth_" + the_name + ".txt", "r") as f:
            the_count = f.read()
    except:
        pass

    return the_count

# ----------------------------
def increment_a_count(the_name):
# ----------------------------
    output_file = "/cardinal/bluetooth_" + the_name + ".txt"
    new_count = str(int(get_a_count(the_name)) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)

# ----------------------------
def thread_manage_bluetooth_connections():
# ----------------------------
    global G_thread_manage_bluetooth_connections

    enabled = get_my_bluetooth_enable(G_thread_manage_bluetooth_connections['serial'])

    if not enabled: # resolve an empty string to be the default of No
        enabled = 'No'

    if enabled == "Yes":
        if not G_thread_manage_bluetooth_connections['state']['started']:
            G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero'
    else:
        G_thread_manage_bluetooth_connections['state']['started'] = ''


    # tell hmi what my enabled status is
    try:
        with open('/dev/shm/pi_bluetooth_enabled.txt', 'w') as f:
            f.write(enabled)
    except:
        pass

    # manage the running of bluetoothd, The default start can fail, so we need to totally control it
    # interesting structures in sudo ls -l /var/lib/bluetooth/DC:A6:32:96:56:C6/80:6F:B0:96:16:08/info
    kill_it = False

    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-zero':
        kill_it = True

    if G_thread_manage_bluetooth_connections['bluetoothd']['condition'] == 'start':
        # kill the default one that might be running
        kill_bluetoothd()
        G_thread_manage_bluetooth_connections['bluetoothd']['condition'] = 'monitor'

    if G_thread_manage_bluetooth_connections['bluetoothd']['condition'] == 'monitor':
        # check that there is a running bluetoothd
        if abs(time.time() - G_thread_manage_bluetooth_connections['bluetoothd']['time_of_last']) > thread_bluetooth_deadman_time:
            kill_it = True

        if not is_running_bluetoothd():
            log_a_special('bluetoothd was found to be not running')
            kill_it = True

        if kill_it:
            # kill any old session we may have made
            kill_tmux_bluetoothd_session()

            # Clean out any saved information
            do_one_command('sudo rm -rf /var/lib/bluetooth')

            # start our own
            with open('/dev/shm/pi_bluetoothd_tmux_starter', 'w') as f:
                f.write("tmux new-session -d -s BluetoothDSession 'sudo bluetoothd |& tee /dev/shm/DBLUETOOTH_OUTPUT'")
            do_one_command('sudo chmod +x /dev/shm/pi_bluetoothd_tmux_starter')
            do_one_command('sudo /dev/shm/pi_bluetoothd_tmux_starter')
            time.sleep(2)

    # manage the tmux session separate from the bluetooth data state
    time_now = time.time()
    if kill_it or (abs(G_thread_manage_bluetooth_connections['state']['tmux_file_start_time'] - time_now) > tmux_restart_time):
        # -------------------
        # kill any existing tmux;
        # -------------------
        kill_tmux_session()
        try:
            G_thread_manage_bluetooth_connections['log_file_handle'].close()
            G_thread_manage_bluetooth_connections['log_file_handle'] = None
        except:
            pass
        do_one_command('sudo rm /dev/shm/BLUETOOTH_OUTPUT')

        # -------------------
        # Get the tmux session running
        # -------------------
        with open('/dev/shm/pi_bluetooth_tmux_starter', 'w') as f:
            f.write("tmux new-session -d -s BluetoothSession 'sudo bluetoothctl -a NoInputNoOutput |& tee /dev/shm/BLUETOOTH_OUTPUT'")
        do_one_command('sudo chmod +x /dev/shm/pi_bluetooth_tmux_starter')
        do_one_command('sudo /dev/shm/pi_bluetooth_tmux_starter')
        time.sleep(2)
        G_thread_manage_bluetooth_connections['state']['tmux_file_start_time'] = time_now


    # ####################################################################################
    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-zero': # one-time config
        G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
        G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = 0
        G_thread_manage_bluetooth_connections['state']['trusted'] = {}
        # -------------------
        # Set up the mode
        # -------------------
        # kill, from ps ax, /usr/lib/bluetooth/bluetoothd
        do_one_command('sudo hciconfig hci0 reset')
        do_one_command('sudo hciconfig hci0 sspmode 1')

        # -------------------
        # set up the pairing barcode
        # -------------------
        try:
            result = do_one_command('hcitool dev')
            # result = ('Devices:\n\thci0\tDC:A6:32:96:56:C6\n', '')
            G_thread_manage_bluetooth_connections['state']['blue_address'] = result[0].split()[2]
            # blue_address = 'DC:A6:32:96:56:C6'
        except:
            G_thread_manage_bluetooth_connections['state']['blue_address'] = ''


        if G_thread_manage_bluetooth_connections['state']['blue_address']:
            try:
                # https://supportcommunity.zebra.com/s/article/Extended-Pairing-Barcode-format?language=en_US
                code128_data = code128_format_link(G_thread_manage_bluetooth_connections['state']['blue_address'], True)
                code128_image = code128_format_image(code128_data)
                code128_image.save("/dev/shm/pi_bluetooth_paircode.png", "PNG")

                code128_data = code128_format_link("TEST1234", False)
                code128_image = code128_format_image(code128_data)
                code128_image.save("/dev/shm/pi_bluetooth_testcode.png", "PNG")

                # get bluetooth started
                time.sleep(3)  # let things settle
                do_one_command('sudo tmux send-keys -t BluetoothSession "power on" Enter')
                time.sleep(3)  # let things settle
                do_one_command('sudo tmux send-keys -t BluetoothSession "power on" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "scan on" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "pairable on" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "agent NoInputNoOutput" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "default-agent" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "paired-devices" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "paired-devices" Enter')
                # flush all to disk
                os.sync()

                time.sleep(15)  # let the device list build in the log file

                # all ok, so ready to move on

                G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-one'
            except:
                pass
                with open ('/dev/shm/pi_bluetooth_exception.txt', 'w') as f:
                    f.write(traceback.format_exc())

    # ---------------------
    # read the log file that should be open by now
    # ---------------------
    if G_thread_manage_bluetooth_connections['log_file_handle'] is None:
        try:
            G_thread_manage_bluetooth_connections['log_file_handle'] = open('/dev/shm/BLUETOOTH_OUTPUT', 'r')
        except:
            pass

    new_lines = []
    if G_thread_manage_bluetooth_connections['log_file_handle']:
        new_lines = G_thread_manage_bluetooth_connections['log_file_handle'].readlines()
        if new_lines:
            G_thread_manage_bluetooth_connections['bluetoothd']['time_of_last'] = time.time()

        # ---------------------
        # do reactions here
        # ---------------------

        # ####################################################################################
        if G_thread_manage_bluetooth_connections['state']['started'] == 'step-one': # one-time setup
            G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = 0
            if G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] == 0:
                G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = time.time()
            if abs(G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] - time.time()) > reaction_step_one_time:
                # we should not be stuck here if there are devices. If there are no devices, then it does not hurt to restart
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero'

            found_addresses = []
            # look for the results of the paired-devices (and any other devices that happened to self identify in the beginning)
            # the log file to this point is the entire initialization log (hopefully)
            for new_line in new_lines:
                if ('Device ' in new_line) and not ('Device has' in new_line):
                    # 'Device 80:6F:B0:96:16:08 RS6000 19114523022499   (RS507 compatible)'
                    # or like:
                    # '^[[0;94m[bluetooth]^[[0m# ^M^[[K[^[[0;93mCHG^[[0m] Device 5D:8C:EF:84:34:DE RSSI: -54'
                    try:
                        address = new_line.split('Device')[1].split()[0].strip()
                        found_addresses.append(address)
                        #G_thread_manage_bluetooth_connections['startup_report'] += '<br>' + address
                    except:
                        pass

            # clean out these old connections
            for address in found_addresses:
                do_one_command('sudo tmux send-keys -t BluetoothSession "disconnect ' + address + '" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "remove ' + address + '" Enter')
                try:
                    del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                except:
                    pass

            G_thread_manage_bluetooth_connections['state']['trusted'] = {}
            G_thread_manage_bluetooth_connections['state']['started'] = 'step-two'

        # ####################################################################################
        elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-two': # looking to make a connection
            G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
            if G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] == 0:
                G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = time.time()
            if abs(G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] - time.time()) > reaction_step_two_time:
                # we should not be stuck here if there are devices. If there are no devices, then it does not hurt to restart
                if len(G_thread_manage_bluetooth_connections['state']['trusted']) == 0:
                    G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero'
            for new_line in new_lines:
                if ('Device ' in new_line) and not ('Device has' in new_line):
                    try:
                        address = new_line.split('Device')[1].split()[0].strip()
                        if 'not available' in new_line:
                            try:
                                # 2021.08.25
                                pass
                                #del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                            except:
                                pass

                        if 'RS6000' in new_line:
                            if not address in G_thread_manage_bluetooth_connections['state']['trusted']:
                                if connect_method == 1: # if we want them to connect to use, then we need to trust. Otherwise, we will trust them when picked.
                                    do_one_command('sudo tmux send-keys -t BluetoothSession "trust ' + address + '" Enter')
                                    do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
                                else:
                                    # leave them in for now...?
                                    #do_one_command('sudo tmux send-keys -t BluetoothSession "trust ' + address + '" Enter')
                                    #do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
                                    pass

                                G_thread_manage_bluetooth_connections['state']['trusted'][address] = {"time":time.time(),"first_seen_time":time.time(),"pair_requested":False,"connected":False, "RSSI":"-"}
                                G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file'] = {'file_name':'/dev/shm/pi_bluetooth_' + address + '.html', 'last_access':'', 'debug':'(na)'}

                                # 2021.08.24 debug
                                if fake_extra_devices_active:
                                    for test_item in range (0,fake_extra_devices_count):
                                        G_thread_manage_bluetooth_connections['state']['trusted'][address+'_test_'+str(test_item).zfill(3)] = {"time":time.time(),"first_seen_time":time.time(),"pair_requested":False,"connected":False, "RSSI":"-"}
                                        G_thread_manage_bluetooth_connections['state']['trusted'][address+'_test_'+str(test_item).zfill(3)]['selection_file'] = {'file_name':'/dev/shm/pi_bluetooth_' + address+'_test_'+str(test_item).zfill(3) + '.html', 'last_access':'', 'debug':'(na)'}

                            else:
                                if ('CHG' in new_line) and ('RSSI:' in new_line):
                                    # it really is in there, keep it updated
                                    G_thread_manage_bluetooth_connections['state']['trusted'][address]["time"] = time.time()
                                    try:
                                        # [CHG] Device 78:23:F5:09:9B:93 RSSI: -63
                                        G_thread_manage_bluetooth_connections['state']['trusted'][address]["RSSI"] = new_line.split('RSSI:')[1].strip()
                                    except:
                                        pass

                    except:
                        pass

            # look to force a device dump, but back off if there are lots of devices.
            count_of_devices = len(G_thread_manage_bluetooth_connections['state']['trusted'])
            last_device_poll_time_to_use = 5 + 5 * count_of_devices
            if last_device_poll_time_to_use > last_device_poll_time:
                last_device_poll_time_to_use = last_device_poll_time

            # look for any devices we are trusting that are stale, and can be taken away
            if (time.time() - G_thread_manage_bluetooth_connections['state']['last_device_stale_time']) > last_device_poll_time_to_use:
                for address in G_thread_manage_bluetooth_connections['state']['trusted']:
                    if not G_thread_manage_bluetooth_connections['state']['trusted'][address]['connected']:

                        address_to_use = address
                        if fake_extra_devices_active:
                            address_to_use = address_to_use.split('_test_')[0]

                        if use_info_command_during_discovery:
                            do_one_command('sudo tmux send-keys -t BluetoothSession "info ' + address_to_use + '" Enter')
                        # will cause "Device 80:6F:B0:96:16:08 not available" eventually

                        if False:
                            if time.time() - G_thread_manage_bluetooth_connections['state']['trusted'][address]['time'] > reaction_trusted_time:
                                do_one_command('sudo tmux send-keys -t BluetoothSession "disconnect ' + address + '" Enter')
                                do_one_command('sudo tmux send-keys -t BluetoothSession "remove ' + address + '" Enter')
                                do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
                                try:
                                    del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                                except:
                                    pass
                    G_thread_manage_bluetooth_connections['state']['last_device_stale_time'] = time.time()

            if (time.time() - G_thread_manage_bluetooth_connections['state']['last_device_poll_time']) > last_device_poll_time_to_use:
                do_one_command('sudo tmux send-keys -t BluetoothSession "power on" Enter')
    #            do_one_command('sudo tmux send-keys -t BluetoothSession "scan on" Enter')
    #            do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
    #            do_one_command('sudo tmux send-keys -t BluetoothSession "pairable on" Enter')

                do_one_command('sudo tmux send-keys -t BluetoothSession "devices" Enter')
                G_thread_manage_bluetooth_connections['state']['last_device_poll_time'] = time.time()

            # Look for a 'connect' / 'disconnect' sequence of a trusted device (meaning that they scanned the bar code)
            for new_line in new_lines:
                if 'Failed to pair' in new_line:
                    for address in G_thread_manage_bluetooth_connections['state']['trusted']:
                        G_thread_manage_bluetooth_connections['state']['trusted'][address]['pair_requested'] = False

                if ('Device ' in new_line) and not ('Device has' in new_line):
                    for address in G_thread_manage_bluetooth_connections['state']['trusted']:
                        if address in new_line:
                            if 'Connected: yes' in new_line:
                                    if not G_thread_manage_bluetooth_connections['state']['trusted'][address]['connected']:
                                        # try to connect to it now
                                        if not G_thread_manage_bluetooth_connections['state']['trusted'][address]['pair_requested']:
                                            # hold off if we just saw it, in order to ignore it repairing with us based on an old connection
                                            if abs(time.time()-G_thread_manage_bluetooth_connections['state']['trusted'][address]['first_seen_time']) > pairing_holdoff_time:
                                                #do_one_command('sudo tmux send-keys -t BluetoothSession "pair ' + address + '" Enter')
                                                time.sleep(1)
                                                do_one_command('sudo tmux send-keys -t BluetoothSession "connect ' + address + '" Enter')
                                                time.sleep(5) # let the scanner finish its cycle
                                                G_thread_manage_bluetooth_connections['state']['trusted'][address]['pair_requested'] = True
                                            break

                # See if it is a quick single line, like "[CHG] Device 80:6F:B0:96:16:08 Connected: yes", which we should not trust
                #   or if it is in the report block for this device, which will look like "	Connected: yes"
                # Look to see that the device info report shows connected
                if ('Connected: yes' in new_line) and (not 'Device' in new_line):
                    G_thread_manage_bluetooth_connections['state']['trusted'][address]['connected'] = True
                    G_thread_manage_bluetooth_connections['state']['Connected_address'] = address
                    G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
                    G_thread_manage_bluetooth_connections['state']['Connected_Yes_Count'] = 0
                    G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] = 0
                    G_thread_manage_bluetooth_connections['state']['started'] = 'step-three'

            # check each trusted device, to see if any has been selected
            for address in G_thread_manage_bluetooth_connections['state']['trusted']:
                file_name = G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['file_name']

                try:
                    fileStatsObj = os.stat (file_name)
                    accessTime = time.ctime ( fileStatsObj [ stat.ST_ATIME ] )

                    if not G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['last_access']:
                        G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['last_access'] = str(accessTime)

                    if G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['last_access'] != str(accessTime):
                        do_one_command('sudo tmux send-keys -t BluetoothSession "trust ' + address + '" Enter')
                        time.sleep(1)
                        do_one_command('sudo tmux send-keys -t BluetoothSession "connect ' + address + '" Enter')

                        G_thread_manage_bluetooth_connections['state']['trusted'][address]['connected'] = True

                        # log the attempt
                        increment_a_count("selected_count")

                        G_thread_manage_bluetooth_connections['state']['Connected_address'] = address
                        G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
                        G_thread_manage_bluetooth_connections['state']['Connected_Yes_Count'] = 0
                        G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] = 0
                        G_thread_manage_bluetooth_connections['state']['started'] = 'step-three'
                except:
                    pass
                    G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['debug'] = traceback.format_exc()

        elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-three': # Look to see that the pairing is complete and stable
            G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
            G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = 0
            address = G_thread_manage_bluetooth_connections['state']['Connected_address']
            do_one_command('sudo tmux send-keys -t BluetoothSession "scan off" Enter')
            # see logfile_bluetooth_20210826a.txt for what happens right after scan off: a lot of device reports, including our device showing not yet connected. (line 533).
            # the logic after here needs to be ok with seeing one message like that, and so we now allow 1 to happen, instead of zero.
            # Also, the log might be showing a late response to an earlier device request. In any case, let at least one of those happen, without giving up.

            address_to_use = address
            if fake_extra_devices_active:
                address_to_use = address_to_use.split('_test_')[0]

            do_one_command('sudo tmux send-keys -t BluetoothSession "info ' + address_to_use + '" Enter')
            for new_line in new_lines:
                if ('Device ' in new_line) and not ('Device has' in new_line):
                    try:
                        address = new_line.split('Device')[1].split()[0].strip()
                        if 'not available' in new_line:
                            G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero' # try to really forget the device, and the trust
                            G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
                    except:
                        pass


                if 'Connected:' in new_line:
                    # is it a report about the address we care about?
                    if G_thread_manage_bluetooth_connections['state']['last_device_seen_address'] == G_thread_manage_bluetooth_connections['state']['Connected_address']:
                        if 'Connected: no' in new_line:
                            G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] += 1
                            G_thread_manage_bluetooth_connections['state']['Connected_Yes_Count'] = 0
                        else:
                            G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
                            G_thread_manage_bluetooth_connections['state']['Connected_Yes_Count'] += 1

            if G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] > 1: # here is where we allow one
                if G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] == 0:
                    # try to quickly pair again
                    do_one_command('sudo tmux send-keys -t BluetoothSession "disconnect ' + address + '" Enter')
                    time.sleep(1)
                    do_one_command('sudo tmux send-keys -t BluetoothSession "remove ' + address + '" Enter')
                    time.sleep(10)
                    do_one_command('sudo tmux send-keys -t BluetoothSession "trust ' + address + '" Enter')
                    time.sleep(1)
                    do_one_command('sudo tmux send-keys -t BluetoothSession "discoverable on" Enter')
                    time.sleep(1)
                    #do_one_command('sudo tmux send-keys -t BluetoothSession "pair ' + address + '" Enter')
                    time.sleep(1)
                    do_one_command('sudo tmux send-keys -t BluetoothSession "connect ' + address + '" Enter')
                    G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] = 1

            if (G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] > 2) or \
                ((G_thread_manage_bluetooth_connections['state']['Connected_No_retry_count'] > 0) and \
                (G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] > 1)):
                do_one_command('sudo tmux send-keys -t BluetoothSession "disconnect ' + address + '" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "remove ' + address + '" Enter')
                try:
                    del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                except:
                    pass
    #            G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
    #            G_thread_manage_bluetooth_connections['state']['started'] = 'step-one'
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero' # try to really forget the device, and the trust
                G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0

            if G_thread_manage_bluetooth_connections['state']['Connected_Yes_Count'] > 2:
                G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-four'

        # ####################################################################################
        elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-four': # looking to see if connection is ok
            G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] = 0
            G_thread_manage_bluetooth_connections['state']['time_entered_step-two'] = 0
            address = G_thread_manage_bluetooth_connections['state']['Connected_address']

            address_to_use = address
            if fake_extra_devices_active:
                address_to_use = address_to_use.split('_test_')[0]

            if use_info_command_during_discovery:
                do_one_command('sudo tmux send-keys -t BluetoothSession "info ' + address_to_use + '" Enter')
            for new_line in new_lines:
                if 'Connected:' in new_line:
                    # is it a report about the address we care about?
                    if G_thread_manage_bluetooth_connections['state']['last_device_seen_address'] == G_thread_manage_bluetooth_connections['state']['Connected_address']:
                        if 'Connected: no' in new_line:
                            G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] += 1
                        else:
                            G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0

            if (G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] > 0):
                do_one_command('sudo tmux send-keys -t BluetoothSession "disconnect ' + address + '" Enter')
                do_one_command('sudo tmux send-keys -t BluetoothSession "remove ' + address + '" Enter')
                try:
                    del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                except:
                    pass
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero' # try to really forget the device, and the trust
                G_thread_manage_bluetooth_connections['state']['Connected_No_Count'] = 0


        # Process any updates
        for new_line in new_lines:
            G_thread_manage_bluetooth_connections['log_file_lines_count'] += 1

            if 'Agent unregistered' in new_line:
                # not sure why this happens, but start over
                G_thread_manage_bluetooth_connections['state']['Agent_unregistered'] += 1
                G_thread_manage_bluetooth_connections['state']['started'] = 'step-zero'

            if ('Device ' in new_line) and not ('Device has' in new_line):
                try:
                    # new_line = "Device 80:6F:B0:96:16:08 (public)"
                    address = new_line.split('Device')[1].split()[0].strip()
                    G_thread_manage_bluetooth_connections['state']['last_device_seen_address'] = address
                except:
                    pass

            if connect_method == 1:
                if 'Trusted: no' in new_line:
                    # is it a report about the address we care about?
                    address = G_thread_manage_bluetooth_connections['state']['last_device_seen_address']
                    if address in G_thread_manage_bluetooth_connections['state']['trusted']:
                        try:
                            del G_thread_manage_bluetooth_connections['state']['trusted'][address]
                        except:
                            pass

            if ("RSSI:" in new_line) and (not "Device" in new_line):
                address = G_thread_manage_bluetooth_connections['state']['last_device_seen_address']
                if address in G_thread_manage_bluetooth_connections['state']['trusted']:
                    G_thread_manage_bluetooth_connections['state']['trusted'][address]["RSSI"] = new_line.split('RSSI:')[1].strip()

                if fake_extra_devices_active:
                    for test_item in range (0,fake_extra_devices_count):
                        address_new = address + "_test_" + str(test_item).zfill(3)
                        if address_new in G_thread_manage_bluetooth_connections['state']['trusted']:
                            G_thread_manage_bluetooth_connections['state']['trusted'][address_new]["RSSI"] = new_line.split('RSSI:')[1].strip()


            _ = """
Device 80:6F:B0:96:16:08 RS6000 19114523022499   (RS507 compatible)
[NEW] Device 80:6F:B0:96:16:08 RS6000 19114523022499   (RS507 compatible)

Device 80:6F:B0:96:16:08 (public)
	Name: RS6000 19114523022499   (RS507 compatible)
	Alias: RS6000 19114523022499   (RS507 compatible)
            """
            if (" RS6000 " in new_line) and ("Device" in new_line):
                try:
                    address = new_line.split('Device')[1].split()[0].strip()
                    name = new_line.split('RS6000')[1].split()[0].strip()
                    if address in G_thread_manage_bluetooth_connections['state']['trusted']:
                        G_thread_manage_bluetooth_connections['state']['trusted'][address]["name"] = name

                    if fake_extra_devices_active:
                        for test_item in range (0,fake_extra_devices_count):
                            address_new = address + "_test_" + str(test_item).zfill(3)
                            if address_new in G_thread_manage_bluetooth_connections['state']['trusted']:
                                G_thread_manage_bluetooth_connections['state']['trusted'][address_new]["name"] = name + "_test_" + str(test_item).zfill(3)

                except:
                    pass

            if ("RSSI:" in new_line) and (not "CHG" in new_line):
                address = G_thread_manage_bluetooth_connections['state']['last_device_seen_address']
                if address in G_thread_manage_bluetooth_connections['state']['trusted']:
                    try:
                        G_thread_manage_bluetooth_connections['state']['trusted'][address]["RSSI"] = new_line.split('RSSI:')[1].strip()
                    except:
                        G_thread_manage_bluetooth_connections['state']['trusted'][address]["RSSI"] = "-201"

                if fake_extra_devices_active:
                    for test_item in range (0,fake_extra_devices_count):
                        address_new = address + "_test_" + str(test_item).zfill(3)
                        if address_new in G_thread_manage_bluetooth_connections['state']['trusted']:
                            try:
                                G_thread_manage_bluetooth_connections['state']['trusted'][address_new]["RSSI"] = new_line.split('RSSI:')[1].strip()
                            except:
                                G_thread_manage_bluetooth_connections['state']['trusted'][address_new]["RSSI"] = "-201"




    # put out a status line, for the HMI to show
    status_line = ""
    status_line += str(G_thread_manage_bluetooth_connections['log_file_lines_count'])
    status_line += " " + G_thread_manage_bluetooth_connections['state']['started']
    status_line += " " + str(len(G_thread_manage_bluetooth_connections['state']['trusted']))
    status_line += '<br>'
    status_line += str(G_thread_manage_bluetooth_connections['state']['Connected_No_Count'])
    status_line += '<br>'
    status_line += G_thread_manage_bluetooth_connections['state']['debug']

    debug_line = ''
    if G_thread_manage_bluetooth_connections['state']['Agent_unregistered'] > 0:
        debug_line += str(G_thread_manage_bluetooth_connections['state']['Agent_unregistered'])
    if G_thread_manage_bluetooth_connections['state']['time_entered_step-one'] > 0:
        debug_line += 't:'+str(int(G_thread_manage_bluetooth_connections['state']['time_entered_step-one']))


    # Build my status content to be shown by the HMI process
    content = ''
    link_status = ''

    # Do reporting
    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-four':
        if not G_thread_manage_bluetooth_connections['state']['time_entered_connected']:
            G_thread_manage_bluetooth_connections['state']['time_entered_connected'] = time.time()
        else:
            G_thread_manage_bluetooth_connections['state']['interval_spent_connected'] = time.time() - G_thread_manage_bluetooth_connections['state']['time_entered_connected']
    else:
        G_thread_manage_bluetooth_connections['state']['time_entered_connected'] = 0
        G_thread_manage_bluetooth_connections['state']['interval_spent_connected'] = 0

    try:
        file_name = '/dev/shm/shared_bluetooth_connected'
        temp_name = file_name.replace('shared_', 'temp_')
        with open (temp_name, 'w') as f:
            f.write(str(int(G_thread_manage_bluetooth_connections['state']['interval_spent_connected'])))
        shutil.move(temp_name, file_name)
    except:
        pass

    for count_name in ['selected_count', 'specials_count']:
        try:
            file_name = '/dev/shm/shared_bluetooth_' + count_name.split('_')[0]
            temp_name = file_name.replace('shared_', 'temp_')
            with open (temp_name, 'w') as f:
                f.write(str(get_a_count(count_name)))
            shutil.move(temp_name, file_name)
        except:
            pass

    # Do updates for HMI to show to the user
    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-zero':
        content += 'Bluetooth' + '<br>'
        content += '<br>' + '(searching.)'
        link_status = '(searching.)'

    elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-one':
        link_status = '(searching..)'
        content += 'Bluetooth' + '<br>'
        content += '<br>' + '(searching..)'

    elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-two':
        if len(G_thread_manage_bluetooth_connections['state']['trusted']) == 0:
            link_status = '(searching...)'
        else:
            if G_thread_manage_bluetooth_connections['state']['trusted'][address]['pair_requested']:
                link_status = '(pairing..)'
            else:
                link_status = '(ready to pair)'

        if connect_method == 1:
            content += 'Bluetooth' + '<br>'
            content += link_status + '<br>'
            content += '<img src="file:///dev/shm/pi_bluetooth_paircode.png" alt="(barcode)" width="400" height="100">' + '<br>'
            content += '(scan to pair)'
#               content += status_line + '<br>'
        if connect_method == 2:
            content += '<table>'
            content += '<tr>'
            content += '<td>'
            content += 'RS6000' + '<br>'
            content += 'Bluetooth' + '<br>'
            content += '1) Hold side button' + '<br>'
            content += '2) Install battery' + '<br>'
            content += '3) Wait for beep' + '<br>'
            content += '4) Scan 2D to configure ->' + '<br>'
            content += '5) <a href="file:///dev/shm/pi_bluetooth_list.html">Click here to pick</a>'
            content += '</td>'

            content += '<td>'
            content += '<img src="file:///cardinal/rs6000config.png" alt="configure scanner" width="200" height="200">'
            content += '</td>'

            content += '</tr>'
            content += '</table>'


    elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-three':
        content += 'Bluetooth' + '<br>'
        content += '<br>' + '(pairing...)'
        link_status = '(pairing...)'

    elif G_thread_manage_bluetooth_connections['state']['started'] == 'step-four':

        content += '<table><tr><td>'
        content += 'Bluetooth '
        content += 'is Paired to '

        name = ''
        address = G_thread_manage_bluetooth_connections['state']['Connected_address']
        if address in G_thread_manage_bluetooth_connections['state']['trusted']:
            if "name" in G_thread_manage_bluetooth_connections['state']['trusted'][address]:
                name = G_thread_manage_bluetooth_connections['state']['trusted'][address]["name"]
        if name:
            link_status = '(Paired to ' + name + ", " + address + ')'
            content += name + ", " + address
        else:
            link_status = '(Paired to ' + address + ')'
            content += address

        content += '<br>' + '<img src="file:///dev/shm/pi_bluetooth_testcode.png" alt="(barcode)" width="400" height="50">' + '<br>'

        content += """
<script type="text/javascript">
function FocusOnInput()
{
     document.getElementById("testtext").focus();
}
</script>
<body onload="FocusOnInput()">
        """
        content += '<br>' + 'Test:' + '<input style="font-size:20px;" type="text" size=25 name="testtext" id="testtext">'
        content += """
</body>
        """
        content += '</td></tr></table>'


    else:
        content += 'Bluetooth' + '<br>'
        content += '<br>' + '(searching)'

#    if debug_line:
#        content += '<br>' + debug_line

    try:
        with open('/dev/shm/pi_bluetooth_status.txt', 'w') as f:
            f.write(content)
        # flush all to disk
        os.sync()
    except:
        pass


    # build the list of options
    td_style_centered = '<td style="font-size:30px"><center>'
    list_page = ""

    list_page += '<center>'
    list_page += '<meta http-equiv="refresh" content="5" >'

    list_page += '<br>'

    # outer table
    list_page += '<table border="0" cellpadding="10">'
    list_page += '<tr>'

    list_page += '<td>'

    # Inner table 1

    list_page += '</td>'

    # Inner table 2
    list_page += '<td>'
    list_page += '<table border="1" cellpadding="10">'

    rssi_notes = """
    -110 and lower = Very poor connection and likely to be unusable
    -100 to -109 = Poor connection
    -70 to -99 = Good connection
    -40 to -55 = Very strong connection
    """

    list_page += '<tr>'

    list_page += '<td style="font-size:20px">'
    list_page += 'name (single click)'
    list_page += '</td>'

    list_page += '<td style="font-size:20px">'
    list_page += link_status
    list_page += '</td>'

    list_page += '<td style="text-align: center;">' + '<center>'
    list_page += '<a href="file:///cardinal/localhtml/index.html">Click here to <br>return to home page</a>'
    list_page += '</center></td>'

    list_page += '</tr>'

    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-two': # looking to make a connection
        for address in sorted(G_thread_manage_bluetooth_connections['state']['trusted']):
            list_page += '<tr>'

            try:
                name = G_thread_manage_bluetooth_connections['state']['trusted'][address]["name"]
            except:
                name = ''

            list_page += '<td style="font-size:20px">'
            list_page += '<a href="file:///dev/shm/pi_bluetooth_' + address + '.html">'
            list_page += name
            list_page += '</a>'
            list_page += '</td>'

            list_page += '<td style="font-size:20px">'
            list_page += '<a href="file:///dev/shm/pi_bluetooth_' + address + '.html">'
            list_page += address
            list_page += '</a>'
            list_page += '</td>'

            if use_info_command_during_discovery:
                color = "(255, 0, 0, 0.3)" # Red
                try:
                    rssi = int(G_thread_manage_bluetooth_connections['state']['trusted'][address]["RSSI"])
                except:
                    rssi = -200
                if rssi > -100:
                    color = "(255, 255, 100, 0.4)" # Yellow
                if rssi > -70:
                    color = "(0, 255, 0, 0.4)" # Green
                list_page += '<td style="background-color:rgba' + color + '">'
                list_page += str(rssi)
                list_page += '</td>'
            else:
                list_page += '<td>'
                list_page += '</td>'

            # 2021.08.24
            if False:
                list_page += '<td style="font-size:12px">'
                try:
                    file_name = '/dev/shm/pi_bluetooth_' + address + '.html'
                    fileStatsObj = os.stat (file_name)
                    accessTime = time.ctime ( fileStatsObj [ stat.ST_ATIME ] )
                    list_page += str(accessTime)
                    list_page += '<br>' + G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['file_name']
                    list_page += '<br>' + G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['last_access']
                    list_page += '<br>' + G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['debug']
                    list_page += '<br>' + str(G_thread_manage_bluetooth_connections['state']['trusted'][address]['connected'])

                except:
                    pass
                list_page += '</td>'

            list_page += '</tr>'

    list_page += '<tr>'
    list_page += '</tr>'

    list_page += '<tr>'
    list_page += '<td id="timertoshow" style="text-align: center;">' + '<center>'
    list_page += '0 seconds'
    list_page += '</center></td>'

    list_page += '</tr>'

    list_page += '</table>'
    list_page += '</center>'
    list_page += """
<script>
var seconds = 0;
var el = document.getElementById('timertoshow');
function incrementSeconds() {
    seconds += 1;
    el.innerText = "" + seconds + " seconds";
}
var cancel = setInterval(incrementSeconds, 1000);
</script>
    """
    list_page += '</td>'

    # outer table
    list_page += '</tr>'

    # outer table end
    list_page += '</table>'

    if G_thread_manage_bluetooth_connections['state']['started'] == 'step-four':
        list_page = """
<script>
window.onload = function() {
    window.location.href = "/cardinal/localhtml/index.html";
}
</script>
        """





    redirect = ""
    redirect += """
<script>
window.onload = function() {
    window.location.href = "/dev/shm/pi_bluetooth_list.html";
}
</script>

    """

    try:
        with open('/dev/shm/pi_bluetooth_list.html.temp', 'w') as f:
            f.write(list_page)
        # do atomic move
        cmd = 'sudo mv -f /dev/shm/pi_bluetooth_list.html.temp /dev/shm/pi_bluetooth_list.html'
        do_one_command(cmd)


        for address in sorted(G_thread_manage_bluetooth_connections['state']['trusted']):
            file_name = G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['file_name']

            if not os.path.isfile(file_name):
                # set it up just once
                with open(file_name, 'w') as f:
                    f.write(redirect)

                fileStatsObj = os.stat (file_name)
                accessTime = time.ctime ( fileStatsObj [ stat.ST_ATIME ] )
                G_thread_manage_bluetooth_connections['state']['trusted'][address]['selection_file']['last_access'] = str(accessTime)

        # ls -lu /dev/shm/pi_bluetooth*
    except:
        pass




    _ = """
Once:
tmux new-session -d -s BluetoothSession 'sudo bluetoothctl -a NoInputNoOutput |& tee /dev/shm/BLUETOOTH_OUTPUT'

sudo hciconfig hci0 sspmode 1


Look at log:
sudo tail -f /run/shm/BLUETOOTH_OUTPUT

Join an existing session:
# https://tmuxcheatsheet.com
sudo su
tmux list-sessions
tmux attach-session -t BluetoothSession

Once per start:
sudo tmux send-keys -t BluetoothSession "power on" Enter
sudo tmux send-keys -t BluetoothSession "scan on" Enter
sudo tmux send-keys -t BluetoothSession "discoverable on" Enter
sudo tmux send-keys -t BluetoothSession "pairable on" Enter
sudo tmux send-keys -t BluetoothSession "agent NoInputNoOutput" Enter
sudo tmux send-keys -t BluetoothSession "default-agent" Enter

Clear any previous pairings
sudo tmux send-keys -t BluetoothSession "paired-devices" Enter

(Device 80:6F:B0:96:16:08 RS6000 19114523022499   (RS507 compatible))
sudo tmux send-keys -t BluetoothSession "disconnect 80:6F:B0:96:16:08" Enter
sudo tmux send-keys -t BluetoothSession "remove 80:6F:B0:96:16:08" Enter


sudo tmux send-keys -t BluetoothSession "devices" Enter
(look for any with RS6000)
sudo tmux send-keys -t BluetoothSession "trust 80:6F:B0:96:16:08" Enter
sudo tmux send-keys -t BluetoothSession "discoverable on" Enter

(Beep sequences are shown on page 25 of 424)

(See if I can do without this one: have them scan the un-pairing bar code; page 80 of 424)

(have them scan the pairing bar code)

(See a connect disconnect sequence)

[CHG] Controller DC:A6:32:96:56:C6 Discoverable: yes
[CHG] Device 80:6F:B0:96:16:08 RSSI: -67
[CHG] Device 80:6F:B0:96:16:08 Connected: yes
[CHG] Device 80:6F:B0:96:16:08 Connected: no
[CHG] Device 80:6F:B0:96:16:08 Connected: yes
[CHG] Device 80:6F:B0:96:16:08 Modalias: bluetooth:v01F1p000Cd0201
[CHG] Device 80:6F:B0:96:16:08 UUIDs: 00001000-0000-1000-8000-00805f9b34fb
[CHG] Device 80:6F:B0:96:16:08 UUIDs: 00001124-0000-1000-8000-00805f9b34fb
[CHG] Device 80:6F:B0:96:16:08 UUIDs: 00001200-0000-1000-8000-00805f9b34fb
[CHG] Device 80:6F:B0:96:16:08 ServicesResolved: yes
[CHG] Device 80:6F:B0:96:16:08 Paired: yes
[CHG] Device 6E:D0:64:E5:F3:50 RSSI: -77
[CHG] Device 80:6F:B0:96:16:08 ServicesResolved: no
[CHG] Device 80:6F:B0:96:16:08 Connected: no
[CHG] Device 6E:D0:64:E5:F3:50 RSSI: -69
[CHG] Device 80:6F:B0:96:16:08 RSSI: -56


Once per device:
([NEW] Device 80:6F:B0:96:16:08 RS6000 19114523022499   (RS507 compatible))
sudo tmux send-keys -t BluetoothSession "pair 80:6F:B0:96:16:08" Enter
sudo tmux send-keys -t BluetoothSession "connect 80:6F:B0:96:16:08" Enter


sudo tmux send-keys -t BluetoothSession "info 80:6F:B0:96:16:08" Enter




Check on tmux session:
sudo tmux ls
(BluetoothSession: 1 windows (created Wed Jun  2 18:37:00 2021) [80x24])

ps ax | fgrep tmux | fgrep bluetoothctl
(26659 ?        Ss     0:00 tmux new-session -d -s BluetoothSession sudo bluetoothctl |& tee /run/shm/BLUETOOTH_OUTPUT)

debugging:
https://wiki.ubuntu.com/DebuggingBluetooth


sudo btmon

    """

_ = """
Other notes:

Might want to periodically clear the tmux log file.
Maybe on a disconnect, we kill the tmux, clear the file, and restart fresh?
Maybe if no connections in an hour, we reset also?
Not sure how else to manage the growth of that logging file.
One way would be to have this service restart itself, that starts with a fresh log, and fresh state.

"""


# ----------------------------
def get_my_bluetooth_enable(serial):
# ----------------------------
    input_file = "/cardinal/localhtml/bluetooth_enabled"

    return_value = ''
    try:
        with open(input_file, 'r') as f:
            json_full = json.loads(f.read())
        if serial in json_full:
            return_value = copy.deepcopy(json_full[serial])
    except:
        pass

    return return_value


# ----------------------------
def do_one_time():
# ----------------------------
    make_RS6000_Reset_barcode()


# ----------------------------
def thread_call_back_to_slicer():
# ----------------------------
    global G_thread_call_back_to_slicer
    need_to_send = False

    results = {}

    time_now = time.time()
    if abs(time_now - G_thread_call_back_to_slicer['time_of_last_send']) > 60 * 30: # 1800 seconds
        need_to_send = True

    the_data = []
    the_data.append('source=' + service)
    the_data.append('serial=' + G_thread_call_back_to_slicer['serial'])
    the_data.append('version=' + version)

    for key in results:
        the_data.append(key + "=" + str(results[key]))

    the_report_url = 'https://slicer.cardinalhealth.net/datadrop?' + ','.join(the_data)

    if need_to_send:
        G_thread_call_back_to_slicer['time_of_last_send'] = time_now

        # check in with slicer
        try:
            r = requests.get(the_report_url, verify=False, timeout=15.0)
            url_result = r.text

        except:
            url_result = 'exception'


        # url_result = '{"bookmarks":{"10000000e3669edf":{"1": {"title": "EDHR", "url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login","whitelist":["edhr-na-jz.cardinalhealth.net"]}}}}'
        try:
            result_json = json.loads(url_result)

        except:
            print ("url_result was not a valid json")

        # save locally, for testing
        try:
            with open('/dev/shm/pi_bluetooth_datadrop.txt', 'w') as f:
                f.write(the_report_url)
                f.write('\n\n' + str(url_result))
            print (the_report_url)
        except:
            print ("!!! failed to write datadrop string")

        _ = """
look with:
watch cat /dev/shm/pi_bluetooth_datadrop.txt


        """


# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_bluetooth.cpython-37.pyc", "/cardinal/pi_bluetooth.pyc")

    print ('version=' + version)

    if os.path.isfile("/cardinal/pi_bluetooth.py"):
        os.remove("/cardinal/pi_bluetooth.py")

    do_one_time()

    try:
        with open('/dev/shm/pi_bluetooth_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")


    # line up all of the worker threads
    sched = BackgroundScheduler(daemon=True)

    job_id_thread1 = sched.add_job(thread_call_back_to_slicer, 'interval', seconds=30, coalesce=True)
    job_id_thread2 = sched.add_job(thread_manage_bluetooth_connections, 'interval', seconds=connection_check_time, coalesce=True)

    sched.start()
    while True:
        time.sleep(1)

    sched.shutdown()

    _ = """
            try:
                manage_bluetooth_connections()
            except:
                pass
    """

# BarCode
from PIL import Image
from PIL import ImageDraw

# Copied from http://en.wikipedia.org/wiki/Code_128
# Value Weights 128A    128B    128C
CODE128_CHART = """
0       212222  space   space   00
1       222122  !       !       01
2       222221  "       "       02
3       121223  #       #       03
4       121322  $       $       04
5       131222  %       %       05
6       122213  &       &       06
7       122312  '       '       07
8       132212  (       (       08
9       221213  )       )       09
10      221312  *       *       10
11      231212  +       +       11
12      112232  ,       ,       12
13      122132  -       -       13
14      122231  .       .       14
15      113222  /       /       15
16      123122  0       0       16
17      123221  1       1       17
18      223211  2       2       18
19      221132  3       3       19
20      221231  4       4       20
21      213212  5       5       21
22      223112  6       6       22
23      312131  7       7       23
24      311222  8       8       24
25      321122  9       9       25
26      321221  :       :       26
27      312212  ;       ;       27
28      322112  <       <       28
29      322211  =       =       29
30      212123  >       >       30
31      212321  ?       ?       31
32      232121  @       @       32
33      111323  A       A       33
34      131123  B       B       34
35      131321  C       C       35
36      112313  D       D       36
37      132113  E       E       37
38      132311  F       F       38
39      211313  G       G       39
40      231113  H       H       40
41      231311  I       I       41
42      112133  J       J       42
43      112331  K       K       43
44      132131  L       L       44
45      113123  M       M       45
46      113321  N       N       46
47      133121  O       O       47
48      313121  P       P       48
49      211331  Q       Q       49
50      231131  R       R       50
51      213113  S       S       51
52      213311  T       T       52
53      213131  U       U       53
54      311123  V       V       54
55      311321  W       W       55
56      331121  X       X       56
57      312113  Y       Y       57
58      312311  Z       Z       58
59      332111  [       [       59
60      314111  \       \       60
61      221411  ]       ]       61
62      431111  ^       ^       62
63      111224  _       _       63
64      111422  NUL     `       64
65      121124  SOH     a       65
66      121421  STX     b       66
67      141122  ETX     c       67
68      141221  EOT     d       68
69      112214  ENQ     e       69
70      112412  ACK     f       70
71      122114  BEL     g       71
72      122411  BS      h       72
73      142112  HT      i       73
74      142211  LF      j       74
75      241211  VT      k       75
76      221114  FF      l       76
77      413111  CR      m       77
78      241112  SO      n       78
79      134111  SI      o       79
80      111242  DLE     p       80
81      121142  DC1     q       81
82      121241  DC2     r       82
83      114212  DC3     s       83
84      124112  DC4     t       84
85      124211  NAK     u       85
86      411212  SYN     v       86
87      421112  ETB     w       87
88      421211  CAN     x       88
89      212141  EM      y       89
90      214121  SUB     z       90
91      412121  ESC     {       91
92      111143  FS      |       92
93      111341  GS      }       93
94      131141  RS      ~       94
95      114113  US      DEL     95
96      114311  FNC3    FNC3    96
97      411113  FNC2    FNC2    97
98      411311  ShiftB  ShiftA  98
99      113141  CodeC   CodeC   99
100     114131  CodeB   FNC4    CodeB
101     311141  FNC4    CodeA   CodeA
102     411131  FNC1    FNC1    FNC1
103     211412  StartA  StartA  StartA
104     211214  StartB  StartB  StartB
105     211232  StartC  StartC  StartC
106     2331112 Stop    Stop    Stop
""".split()

VALUES   = [int(value) for value in CODE128_CHART[0::5]]
WEIGHTS  = dict(zip(VALUES, CODE128_CHART[1::5]))
CODE128B = dict(zip(CODE128_CHART[3::5], VALUES))

def code128_format_link(text_to_encode, isBluetoothAddressLink):
    """
    Generate a link barcode from bluetooth address
    """
    if isBluetoothAddressLink:
        text     = "B" + str(text_to_encode.replace(':',''))
    else:
        text     = str(text_to_encode)

    pos      = 0
    length   = len(text)
    charset = CODE128B
    codes   = [charset['StartB']]

    if isBluetoothAddressLink:
        # FNC3
        codes.append(96)

    # Data
    while pos < length:
        # Encode Code B one character at a time
        codes.append(charset[text[pos]])
        pos += 1
    # Checksum
    checksum = 0
    for weight, code in enumerate(codes):
        checksum += max(weight, 1) * code
    codes.append(checksum % 103)
    # Stop Code
    codes.append(charset['Stop'])
    return codes

def code128_format_image(data, height=100, thickness=3, quiet_zone=True):
    barcode_widths = []
    for code in data:
        for weight in WEIGHTS[code]:
            barcode_widths.append(int(weight) * thickness)
    width = sum(barcode_widths)
    x = 0
    if quiet_zone:
        width += 20 * thickness
        x = 10 * thickness
    # Monochrome Image
    img  = Image.new('1', (width, height), 1)
    draw = ImageDraw.Draw(img)
    draw_bar = True
    for width in barcode_widths:
        if draw_bar:
            draw.rectangle(((x, 0), (x + width - 1, height)), fill=0)
        draw_bar = not draw_bar
        x += width
    return img

def make_RS6000_Reset_barcode():
    # This content is from the 2D code that Raul sent.
    # Not from the config file; that one does not allow the pairing.
    content = """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    """

    output_file = "/cardinal/rs6000config.xxd"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    # make back into image file
    content = """
#!/usr/bin/env sh
cat /cardinal/rs6000config.xxd | xxd -p -r >/cardinal/rs6000config.png
sudo rm /cardinal/rs6000config.xxd
    """
    output_file = "/cardinal/pi_bluetooth_tempscript"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    report = ''

    pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_bluetooth_tempscript')
    report += "pass1\n" + pass_string + "fail1\n" + fails

    pass_string, fails = do_one_command('sudo /cardinal/pi_bluetooth_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    # clean up
    pass_string, fails = do_one_command('sudo rm /cardinal/pi_bluetooth_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    with open ("/dev/shm/bluetooth_report.txt", "w") as f:
        f.write(report)
















