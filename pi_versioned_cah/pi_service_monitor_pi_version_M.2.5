_ = """
sudo vi /cardinal/pi_monitor.py

sudo systemctl restart pi-monitor.service

sudo systemctl status pi-monitor.service

# Logging of std out
sudo cat /var/log/syslog | fgrep pi-monitor

"""


service = 'monitor'
version = 'M.2.5'

release_notes = """
2023.07.06
M.2.5
Handle python3.9 environment.

2022.05.06
M.2.4
Eliminate all but the first print (reduce logging to minimum)

2022.01.31
M.2.3
Centralize the call_home location information.

2021.05.05
M.2.2
Bump the version, to be able to identify devices with full image 2.0.3 or later

2021.04.20
M.1.2
Put a timeout on the call home, to not hang.

2021.04.13
M.1.1
No longer check in with the chef server

2021.04.06
M.1.0

Now also checking in with the slicer server.

2021.03.15
M.0.9
Emit version as a print, so that logging sees it, and it can be reported.
Bury the chef call into a capture of stdout and std error, so that it does not pollute my log.

2021.03.13
M.0.8
If runner reads go bad, print exception string.

M.0.7
Look to the runner wake count, and report if it is alive or not.

M.0.6
Trigger a new send based on the runner version changing.

M.0.5
Convert sleep from 60 to 1.

M.0.4
Report the version from pi_runner also.

M.0.3
Now made as a stand alone from the service.

"""

test_code_for_slicer_server_comms = """
https://stackoverflow.com/questions/15445981/how-do-i-disable-the-security-certificate-check-in-python-requests


on pi:


python3
import requests
r = requests.get('https://slicer.cardinalhealth.net', verify=False)
r.text

r = requests.get('https://slicer.cardinalhealth.net/checkin?pi-m-20000000cf60766f-10.216.211.9-14647-M.0.9-missing-stale', verify=False)

"""

other_content = """
sudo vi /cardinal/pi-monitor
sudo chmod +x /cardinal/pi-monitor

# ===== begin: start file
#!/usr/bin/env python3
import pi_monitor
pi_monitor.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-monitor.service
sudo systemctl daemon-reload
sudo systemctl stop pi-monitor.service
sudo systemctl start pi-monitor.service
sudo systemctl enable pi-monitor.service

systemctl status pi-monitor.service

# Logging of std out
cat /var/log/syslog | fgrep pi-monitor

OR

tail -f /var/log/syslog | fgrep pi-monitor

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-monitor
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

import json
import os
import shutil
import socket
import subprocess
import sys
import time
import unittest

try:
    import requests
except:
    pass # unittest

# ----------------------------
def do_one_command(command):
# ----------------------------
    command_splits = command.split(" ")
    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)

# ----------------------------
def my_best_address():
# ----------------------------
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        my_best = s.getsockname()[0]
        s.close()
    except:
        my_best = ''
    return my_best

# ----------------------------
def uptime():
# ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def get_runner_version():
# ----------------------------
    return_value = 'missing'

    try:
        if os.path.isfile('/dev/shm/pi_runner_version.txt'):
            with open('/dev/shm/pi_runner_version.txt', 'r') as f:
                return_value = f.read()
    except:
        pass
        #print("exception in get_runner_version")
    return return_value

# ----------------------------
def get_runner_wake():
# ----------------------------
    return_value = 'missing'

    try:
        if os.path.isfile('/dev/shm/pi_runner_wake.txt'):
            with open('/dev/shm/pi_runner_wake.txt', 'r') as f:
                return_value = f.read()
    except:
        pass
        #print("exception in get_runner_wake")
    return return_value

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        shutil.copy2("/cardinal/__pycache__/pi_monitor.cpython-37.pyc", "/cardinal/pi_monitor.pyc")
    except:
        shutil.copy2("/cardinal/__pycache__/pi_monitor.cpython-39.pyc", "/cardinal/pi_monitor.pyc")

    if os.path.isfile("/cardinal/pi_monitor.py"):
        os.remove("/cardinal/pi_monitor.py")

    daemon = True

    serial = get_serial()

    time_of_last_send = 0
    last_address = ''
    last_runner_version = ''
    last_runner_wake = ''
    runner_alive = False
    last_runner_alive = True

    print ('version=' + version)
    try:
        with open('/dev/shm/pi_monitor_version.txt', 'w') as f:
            f.write(version)
    except:
        pass
        #print ("!!! failed to write version string")

    while True:
        need_to_send = False

        current_address = my_best_address()

        if current_address:
            up_time = str(uptime())
            runner_version = get_runner_version()
            runner_wake = get_runner_wake()

            if runner_wake == last_runner_wake:
                # report is stale, it is not running
                runner_alive = False
            else:
                runner_alive = True

            last_runner_wake = runner_wake

            if last_runner_alive != runner_alive:
                need_to_send = True

            last_runner_alive = runner_alive
            if runner_alive:
                runner_alive_str = 'live'
            else:
                runner_alive_str = 'stale'

            current_id = 'pi-m-' + serial + '-' + current_address + '-' + up_time + '-' + version + '-' + runner_version + '-' + runner_alive_str

            if current_address != last_address:
                last_address = current_address
                #print ('id=' + current_id)
                need_to_send = True

            time_now = time.time()
            if abs(time_now - time_of_last_send) > 60 * 30:
                need_to_send = True

            if last_runner_version != runner_version:
                last_runner_version = runner_version
                need_to_send = True

            if need_to_send:
                time_of_last_send = time_now

                # check in with slicer
                for call_home_location in call_home_locations():
                    try:
                        r = requests.get(call_home_location + '/checkin?' + current_id, verify=False, timeout=10.0)

                        try:
                            debug_file_name = call_home_location.replace('/','').replace(':','')
                            with open('/dev/shm/pi_monitor_checkinresult_' + debug_file_name, 'w') as f:
                                f.write(r.text)
                        except:
                            pass

                    except:
                        pass

        if not daemon:
            break

        #print ('version=' + version)
        #print ('sleep with address =', current_address)
        time_now = time.time()
        while (abs(time_now - time.time()) < 60):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')


