_ = """
sudo vi /cardinal/pi_network.py

sudo systemctl restart pi-network.service

sudo systemctl restart pi-network.service; exit

sudo systemctl status pi-network.service

"""

# Check priority:
# https://askubuntu.com/questions/165679/how-to-manage-available-wireless-network-priority
# nmcli -f autoconnect-priority,name c


service = 'network'
version = 'N.7.6'

s_wifi_iot_only = True # in both network, hmi, and settings

release_notes = """
2023.10.25
N.7.6
After an external radio reset, dwell for an amount of time, to let the radio hardware be completely back and ready.

2023.09.20
N.7.5
Reworked the connection management, to better track the connection changes.

2023.08.28
N.7.4
Adding persistent count of the number of times nic changed between int and ext.
nic_change_count

2023.06.30
N.7.3
On config page, also show the connection speed with the channel number.

2023.03.24
N.7.2
Disable/remove corp WiFi, leaving only IOT WiFi

2023.02.08
N.7.1
Second screen HMI content obfustication

2023.02.07
N.7.0
WiFi cert for corp, that does not expire until 2024.02.06

2023.01.31
N.6.9
Clean up reporting.

2022.10.26
N.6.8
Parse the wifi config file for preshare key.
Allow saved changes to wifi config take effect immediately (instead of requiring a reboot).
#Move the cert load out of here... just manage here. (keep existing until all converted)

2022.10.10
N.6.7
Handle getting on corp with invalid (expired) certificate, and also a valid certificate available.
Undo the startup wipe of wireless connections (introduced in N.5.2)

2022.10.06
N.6.6
Report the radio resets count back to Slicer.

2022.10.03
N.6.5
On external radio start, do a usb reset on it first, to get a clean starting point each time.

2022.09.27
N.6.4
Refresh NetworkManager often, to keep network connection reports as fresh as possible.

2022.09.20
N.6.3
Allow for consistency on the wifi connection.

2022.07.15
N.5.5
Allow for wifi configuration to come from a data file: /cardinal/wifi_config.txt

2022.04.25
N.5.4
Network setting to ignore carrier loss when hopping WiFi access point (coaster).

2022.04.20
N.5.3
Wifi hopper option (jump to better signal access point)

2022.03.03
N.5.2
On first start up, always remove all wireless connections, to be sure that we are using the latest certificate.

2022.03.02
N.5.1
Support old image updates, to detect eth0 connections.

2022.03.01
N.5.0
Update the wifi certificate.

2022.02.28
N.4.2
Add support for showing wifi data on a served up webpage.

2022.01.31
N.4.1
Add support for looking up the call_home_locations

2021.12.29
N.4.0
Allow for manual configuration of IOT, from Slicer side, and from pi side.

2021.12.29
N.3.9
Completely eliminate IOT references.

2021.12.21
N.3.8
Disable IOT connection attempt.

2021.12.07
N.3.7
If the identified lan changes (wlan0, wlan1, eth0), then send new status.

2021.11.16
N.3.6
Set to connect to IOT as priority over corp.
Clean up a corp single config situation, and go dual for corp and iot.
When changing wifi configuration, restart network manager also, in order to take effect immediately.

2021.11.09
N.3.1
Fix the reporting, to correctly show wlanX, when connected.
Add support to allow wlan1 (external wifi adapter) to be configured

2021.11.03
N.2.1
Add to the network report: the adapter providing the access (wlan0, wlan1, eth0, ...).

2021.07.23
N.2.0
Add support for cah-iot wifi connection, in addition to corp wifi connection.

2021.05.15
N.1.2
Move the bookmarks update out of here, and into runner.

2021.05.14
N.1.1
If wired, delete all wireless connections.
If not wired, add corp connection.

2021.05.11
N.1.0
Get ready to be managed that we can be connected to corp, depending on not being on wired,
But not active yet. Save this as a 'corp still manually managed' version.

2021.05.10
N.0.6
Correct the corp certificate creation. (The ascii was emitting slash character which
was acting as an escape character, and causing incorrect results)

2021.05.10
N.0.5
Make the timeout be 15 seconds, instead of 3, for talking to Slicer.
The Slicer response is now calculated, instead of a direct lookup.
Put the manage_network_connections into play

2021.05.06
N.0.4
Add the parsing of the datadrop response, looking for data that we need to deal with.
Bookmarks

2021.04.20
N.0.1
Borrow from the monitor 1.1 file, and adapt to be a network monitor, reporter, and
perhaps even a controller of the network settings / connectivity.

"""

_20230530_new_wifi_radio_device = """

AC1200 model AC3L (The one delivered to me was version:V2)

# ignore_line_count_down_for_syntax_checking
https://www.amazon.com/dp/B07FCN6WGX/ref=sspa_dk_detail_2?psc=1&pd_rd_i=B07FCN6WGX&pd_rd_w=aXUgQ&content-id=amzn1.sym.7b21e0c7-2d6d-4279-a40b-74d2b0593b5a&pf_rd_p=7b21e0c7-2d6d-4279-a40b-74d2b0593b5a&pf_rd_r=DJT6RNRFDTW6Z4H2M045&pd_rd_wg=Mkyue&pd_rd_r=6132122a-a9b0-46cd-a2b3-8ab47539568e&s=electronics&sp_csd=d2lkZ2V0TmFtZT1zcF9kZXRhaWxfdGhlbWF0aWM&spLa=ZW5jcnlwdGVkUXVhbGlmaWVyPUEyU0NJRktWU0hOTUZCJmVuY3J5cHRlZElkPUEwMzc0OTQ3MVUzM1FSWElaS1RMSyZlbmNyeXB0ZWRBZElkPUEwMTMzOTEwTFc1T0EyVlRBVEFRJndpZGdldE5hbWU9c3BfZGV0YWlsX3RoZW1hdGljJmFjdGlvbj1jbGlja1JlZGlyZWN0JmRvTm90TG9nQ2xpY2s9dHJ1ZQ==

https://deb.trendtechcn.com/
sudo su
wget deb.trendtechcn.com/install -O /tmp/install && sh /tmp/install


Package manager is: apt-get
Could not detect the adapter!
Please insert the BrosTrend WiFi adapter into a USB slot
and press [Enter] to continue.
If you don't have the adapter currently, you may type:
  (a) to install the 8812au driver for the old AC1L/AC3L models before 2019, or
  (b) to install the 88x2bu driver for the new AC1L/AC3L version 2 models, or
  (c) to install the 8821cu driver for the AC5L model, or
  (d) to install the 8852bu driver for the AX1L/AX4L models, or
  (q) to quit without installing a driver
Please type your choice, or [Enter] to autodetect: b


option "b" for V2. Taking a long time to install... 20 minutes so far, stalled at 78%,
but still making progress (line prints show the work "Building initial module for 5.10.103-v7l+")
Complete after like 25 to 30 minutes

sudo apt-get install speedtest-cli

speedtest-cli

======================================
--> New external radio, on IOT connected at 54 Mbps:
Hosted by Windstream (Chicago, IL) [439.55 km]: 22.392 ms
Download: 15.97 Mbit/s
Upload: 18.44 Mbit/s

--> New external radio, on corp connected at 400 Mbps:
Hosted by Ablative Bespoke & Visionary Networks Corp (Madison, WI) [627.16 km]: 24.879 ms
Download: 93.58 Mbit/s
Upload: 102.70 Mbit/s

======================================
--> Original external radio on IOT connected at 54 Mbps:
Hosted by Windstream (Chicago, IL) [439.55 km]: 21.23 ms
Download: 18.50 Mbit/s
Upload: 23.40 Mbit/s

--> Original external radio on corp connected at 270 Mbps:
Hosted by Windstream (Chicago, IL) [439.55 km]: 59.715 ms
Download: 27.34 Mbit/s
Upload: 48.27 Mbit/s

======================================

a device in PR005:
cah-pi-su@cah-rp-1000000096a5f1df:~ $ sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi | sort
byod:20\:A6\:CD\:D4\:6B\:D1: :64:5320 MHz:37:54 Mbit/s
byod:20\:A6\:CD\:D4\:6D\:11: :132:5660 MHz:39:54 Mbit/s
byod:20\:A6\:CD\:DF\:C5\:90: :112:5560 MHz:40:54 Mbit/s
cah-iot:20\:A6\:CD\:D4\:25\:32:*:40:5200 MHz:45:54 Mbit/s
cah-iot:20\:A6\:CD\:D4\:6B\:D3: :64:5320 MHz:37:54 Mbit/s
cah-iot:20\:A6\:CD\:D4\:6D\:13: :132:5660 MHz:39:54 Mbit/s
corp:20\:A6\:CD\:D4\:25\:31: :40:5200 MHz:45:54 Mbit/s
corp:20\:A6\:CD\:D4\:6B\:D0: :64:5320 MHz:35:540 Mbit/s
corp:20\:A6\:CD\:D4\:6D\:10: :132:5660 MHz:39:540 Mbit/s
corp:20\:A6\:CD\:DF\:C5\:91: :112:5560 MHz:37:540 Mbit/s
guest:20\:A6\:CD\:D4\:6B\:D2: :64:5320 MHz:35:54 Mbit/s
guest:20\:A6\:CD\:D4\:6D\:12: :132:5660 MHz:37:54 Mbit/s

======================================


"""

_thoughts = """
2022.09.14
Working on a thought... if there is a way that I can do a non-permanent change of the mac address in the external radio,
then someone unplugs it, and takes it, the mac address would be the default original mac address of that device,
and not match the one we have in our list.... That could meet everyones needs.

https://askubuntu.com/questions/1121523/how-do-i-get-networkmanager-to-assign-a-fixed-mac-address-to-eth0

sudo nmcli con modify Default 802-3-ethernet.cloned-mac-address 00:12:34:56:78:9a

daves test device

wlan0
dc:a6:32:96:56:c4

wlan1 (Panda)
9c:ef:d5:fc:d0:f6

sudo nmcli con modify Default 802-3-ethernet.cloned-mac-address 00:12:34:56:78:9a

sudo nmcli connection add type wifi con-name "cah-iot1" ifname wlan1 ssid "cah-iot" 802-11-wireless.cloned-mac-address dc:a6:32:96:56:c6


sudo nmcli connection modify --temporary "corp1" 802-11-wireless.cloned-mac-address dc:a6:32:96:56:c4
sudo nmcli con down corp1 && sudo nmcli con up corp1
ip addr

#sudo nmcli connection modify "corp1" 802-11-wireless.assigned-mac-address dc:a6:32:96:56:c4


https://udger.com/resources/mac-address-vendor-detail?name=raspberry_pi_foundation

raspberry pi
28:CD:C1:xx:xx:xx
B8:27:EB:xx:xx:xx
DC:A6:32:xx:xx:xx
E4:5F:01:xx:xx:xx

2022.09.19
47/63 in PR005 have internal as wlan0 (75%)
16/63 in PR005 have internal as wlan1 (25%)


Tests to run:
    On wlan1, upgrade from 5.4 to 6+ and see that it connects. (corp)

    plug into wired, and unplug wired, to see that wifi connects ok (both corp and iot)

    Leave on external IOT for a long time, and make sure it stays responsive
        even with the NetworkManager restart in place, ok so long as I only do "up" and not a "down"
        before that, and then restart network manager.


Passing Tests:
    Lab device, with wlan1 as internal, and wlan0 as external... remove external, and see that internal
    connects. This currently fails on N.6.2, fixed in N.6.3

    With internal as wlan1, what happens when unplugging external radio?
        Does internal move to wlan0, or stay at wlan1?
            Stays at wlan1, and replugging external re-shows at wlan0.


"""

_usb_reset_logic = """
sudo python3
dev_path = '/dev/bus/usb/001/008'
import os
import fcntl
USBDEVFS_RESET = 21780
f = open(dev_path, 'w', os.O_WRONLY)
fcntl.ioctl(f, USBDEVFS_RESET, 0)
f.close()


sudo usbreset 001/008

"""

_multiple_valid_corp_wifi_certs = """
Make network use old corp cert, see if it throws any message on connect attempt, using the nee page 2 view.

Be able to have pi pull many certs, keep them all, and rotate through a list to find one that works (ping *******? If no error message to be used)

Report inventory of certs, and which one we are currently using.

Write a non-volatile of which one worked last, and start there on next attempt.

Naming scheme to show date of expiration, and also a marker of somekind in case there are two different ones for the same day.
20231010a
20231010b


"""

other_content = """
sudo vi /cardinal/pi-network
sudo chmod +x /cardinal/pi-network

# ===== begin: start file
#!/usr/bin/env python3
import pi_network
pi_network.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-network.service
sudo systemctl daemon-reload
sudo systemctl stop pi-network.service
sudo systemctl start pi-network.service
sudo systemctl enable pi-network.service
systemctl status pi-network.service

sudo systemctl restart pi-network.service

systemctl status pi-network.service

# Logging of std out
sudo cat /var/log/syslog | fgrep pi-network

sudo cat /var/log/syslog | fgrep pi-network | fgrep scan:

OR

tail -n 1000 -f /var/log/syslog | fgrep pi-network

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-network
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_network_cah


"""

_Transmit_power_notes = """
https://www.digitalcitizen.life/double-wifi-speed-windows-intel-network-cards/


https://raspberrypi.stackexchange.com/questions/90932/restrict-wifi-range-of-pi-3b

sudo iw dev wlan0 info

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 31.00 dBm

sudo iw dev wlan0 set txpower fixed 1000

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 10.00 dBm


sudo iw dev wlan0 set txpower fixed 500

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 5.00 dBm


DELL:
https://www.digitalcitizen.life/double-wifi-speed-windows-intel-network-cards/
2: Medium-Low

The power setting does not survive a reboot.


wlan0, set to 100 sets to 1.0 dBm. Set to anything less gets 0 dBm (? no limit to request on top end)
wlan1, set to less than 200 gets error "command failed: Operation not supported (-95)".  (? no limit to request on top end)

Can not set wlan0 above 31 dBm
Can not set wlan1 to any value (fixed at 20)

if wlan1 not plugged in, get "command failed: No such device (-19)" (to both info and set power)

Power level discussion:
https://community.element14.com/products/raspberry-pi/f/forum/20444/rt3070-wireless-adapter-adjust-tx-power

Power management:
https://www.lifewire.com/usb-wifi-adapter-raspberry-pi-4058093

iwconfig
lo        no wireless extensions.

eth0      no wireless extensions.

wlan0     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=31 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on

wlan1     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=20 dBm
          Retry short  long limit:2   RTS thr:off   Fragment thr:off
          Power Management:off


https://askubuntu.com/questions/85214/how-can-i-prevent-iwconfig-power-management-from-being-turned-on
https://thepihut.com/blogs/raspberry-pi-tutorials/disable-wifi-power-management
https://unix.stackexchange.com/questions/269661/how-to-turn-off-wireless-power-management-permanently

Q: is the internal radio getting power managed to the off position in 1.4.8 images, and
    the external radio seems to have power management off, and so that's the reason the
    external radio helped them operate? Also, external PAU09 is 2.4 and 5 GHz. (Internal is also)
    What is the 20 dBm limit on wlan1, combined with the antenna, yields a LOWER total transmit
    power, thus justifying using the internal radio, with the power turned down?
    wlan0 = 31 dBm with antenna ?
    wlan1 = 20 dBm with 5dBi antenna = 25 dBm when aligned?

sudo /sbin/iwconfig wlan0 power off
(survive reboot = NO)
sudo iw wlan0 get power_save


wlan0: on AP104 at signal quality 50
South: in 3rd floor default conference room, not under an access point
31dBm -> 63  mW/m^2 on upload test  at 11MBps
20 dBm -> 3.5   at 11MBps (Still get 125 to 250 mW/m^2 bursts for 1 second)
10dBm -> 0.5 at 11MBps
 5dBm -> 0.2 at 11MBps
 3dBm -> 0.125 at 9 to 11MBps
 1dBm -> 0.100 at 9 to 11MBps

walk to kitchen, still on AP104 at 40, at 1dbm, get 1 MPBs, set to 2dBm, get 2.5 MBps, set back to 1, get 2.5, at 5 get 3.6
at 10 get 7, at 31 get 12, at 10 get 7

restart network, connected to 108 at 50, set to 1 get 11, set to 5 get 12, and walk more

Every 20 seconds, get burst of 40 mW/m^2 no matter the power setting. Is this bluetooth? (stop that service, and burst still happens)
Our rescan is every 4 seconds?
stopping network service makes the every 20 second burst stop.
Burst is from "sudo nmcli -c no device wifi list --rescan yes"
This does not do it all the time, just when its been a while since it did a full scan: "sudo nmcli -c no device wifi list"

Try disabling the scan, and turning the power down, and then walk around.


"""

_channel_connection = """
https://developer-old.gnome.org/NetworkManager/stable/nm-settings-nmcli.html

(Search for channel, look to the result near the bottom of the page)

sudo nmcli connection modify corp0  802-11-wireless.band a  802-11-wireless.channel 52

Try...
sudo nmcli connection modify corp0 802-11-wireless.bssid 34:FC:B9:75:C0:92


sudo wpa_cli status



This works...
https://forums.raspberrypi.com/viewtopic.php?t=224243

sudo wpa_cli -i wlan0 status

# Set the "preferred bssid"
# Chan 052
sudo wpa_cli -i wlan0 bssid 0 34:FC:B9:75:C0:92; sudo wpa_cli -i wlan0 reassociate


# Chan 104
sudo wpa_cli -i wlan0 bssid 0 34:FC:B9:75:BA:32; sudo wpa_cli -i wlan0 reassociate

# Chan 108
sudo wpa_cli -i wlan0 bssid 0 44:48:C1:81:A1:31; sudo wpa_cli -i wlan0 reassociate

# dis-allow list
https://gist.github.com/penguinpowernz/1d36a38af4fac4553562410e0bd8d6cf

# Chan 104
sudo wpa_cli -i wlan0 blacklist 34:FC:B9:75:BA:32; sudo wpa_cli -i wlan0 reassociate

# view blacklist
sudo wpa_cli -i wlan0 blacklist

#clear list
sudo wpa_cli -i wlan0 blacklist clear; sudo wpa_cli -i wlan0 reassociate
blacklist clear

!!! Cahnges are not persisting... something is resetting these choices



"""

# from Scott Wolke, for PR005 (which includes PR010, because they share a controller)
bssid_to_AP_raw = """
00:4e:35:fc:26:51  corp     N/A   10.216.237.73   a      ap    161/12.0/29.3     0       NW-PR005-AP-1B-047  0        17d:2h:6m:42s    1500  -          2    T          A                              no
00:4e:35:fc:26:52  cah-iot  N/A   10.216.237.73   a      ap    161/12.0/29.3     0       NW-PR005-AP-1B-047  0        17d:2h:6m:48s    1500  -          164  T          A                              no
20:a6:cd:d4:25:31  corp     N/A   10.216.201.45   a      ap    40/12.0/24.0      0       NW-PR010-AP-1C-033  0        72d:3h:37m:43s   1500  -          2    T          A                              no
20:a6:cd:d4:25:32  cah-iot  N/A   10.216.201.45   a    ap    40/12.0/24.0      1       NW-PR010-AP-1C-033  0        72d:3h:37m:35s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6b:71  corp     N/A   10.216.201.38   a      ap    40/12.0/24.0      2       NW-PR010-AP-1D-034  0        72d:3h:37m:45s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6b:72  cah-iot  N/A   10.216.201.38   a    ap    40/12.0/24.0      2       NW-PR010-AP-1D-034  0        72d:3h:37m:36s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6b:91  corp     N/A   10.216.237.84   a      ap    48/12.0/23.5      3       NW-PR005-AP-2A-009  0        72d:3h:37m:42s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6b:92  cah-iot  N/A   10.216.237.84   a    ap    48/12.0/23.5      4       NW-PR005-AP-2A-009  0        72d:3h:37m:26s   1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6b:d0  corp     N/A   10.216.201.20   a-VHT  ap    144-/15.0/29.5    1       NW-PR010-AP-2B-017  0        72d:3h:37m:44s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6b:d1  byod     N/A   10.216.201.20   a      ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:33s   1500  -          2    T          A                              no
20:a6:cd:d4:6b:d2  guest    N/A   10.216.201.20   a    ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:26s   1500  -          166  T          A                              no
20:a6:cd:d4:6b:d3  cah-iot  N/A   10.216.201.20   a      ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:29s   1500  -          164  T          A                              no
20:a6:cd:d4:6c:31  corp     N/A   10.216.201.24   a      ap    40/12.0/24.0      8       NW-PR010-AP-1A-039  0        72d:3h:37m:45s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6c:32  cah-iot  N/A   10.216.201.24   a    ap    40/12.0/24.0      10      NW-PR010-AP-1A-039  0        72d:3h:37m:36s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6c:f1  corp     N/A   10.216.201.42   a      ap    36/12.0/24.0      0       NW-PR010-AP-1C-014  0        31d:11h:38m:1s   1500  -          2    T          A                              no
20:a6:cd:d4:6c:f2  cah-iot  N/A   10.216.201.42   a    ap    36/12.0/24.0      0       NW-PR010-AP-1C-014  0        31d:11h:37m:52s  1500  -          164  T          A                              no
20:a6:cd:d4:6d:10  corp     N/A   10.216.201.31   a-VHT  ap    112-/14.0/30.0    4       NW-PR010-AP-2B-008  0        31d:11h:40m:1s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6d:12  guest    N/A   10.216.201.31   a    ap    112/14.0/30.0     1       NW-PR010-AP-2B-008  0        31d:11h:39m:52s  1500  -          166  T          sA U  (AAC=10.216.254.223)     no
20:a6:cd:d4:6d:30  corp     N/A   10.216.237.36   a-VHT  ap    153-/14.0/33.8    2       NW-PR005-AP-2B-053  0        72d:3h:37m:44s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6d:31  byod     N/A   10.216.237.36   a      ap    153/14.0/33.8     1       NW-PR005-AP-2B-053  0        72d:3h:37m:44s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6d:32  guest    N/A   10.216.237.36   a    ap    153/14.0/33.8     0       NW-PR005-AP-2B-053  0        72d:3h:37m:35s   1500  -          166  T          A                              no
20:a6:cd:d4:6d:33  cah-iot  N/A   10.216.237.36   a      ap    153/14.0/33.8     0       NW-PR005-AP-2B-053  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:71  corp     N/A   10.216.201.39   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-032  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d4:6d:72  cah-iot  N/A   10.216.201.39   a    ap    157/12.0/27.8     0       NW-PR010-AP-1E-032  0        72d:3h:37m:35s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:d1  corp     N/A   10.216.201.54   a      ap    48/12.0/23.5      0       NW-PR010-AP-1D-035  0        72d:3h:37m:45s   1500  -          2    T          A                              no
20:a6:cd:d4:6d:d2  cah-iot  N/A   10.216.201.54   a    ap    48/12.0/23.5      0       NW-PR010-AP-1D-035  0        72d:3h:37m:36s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:f1  corp     N/A   10.216.237.38   a      ap    40/12.0/24.0      2       NW-PR005-AP-2A-012  0        31d:11h:38m:1s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6d:f2  cah-iot  N/A   10.216.237.38   a    ap    40/12.0/24.0      0       NW-PR005-AP-2A-012  0        31d:11h:37m:52s  1500  -          164  T          A                              no
20:a6:cd:d4:6e:71  corp     N/A   10.216.201.29   a      ap    149/12.0/27.8     0       NW-PR010-AP-2B-036  0        72d:3h:37m:45s   1500  -          2    T          A                              no
20:a6:cd:d4:6e:72  cah-iot  N/A   10.216.201.29   a    ap    149/12.0/27.8     4       NW-PR010-AP-2B-036  0        72d:3h:37m:36s   1500  -          164  T          A U sU                         no
20:a6:cd:d4:6e:b1  corp     N/A   10.216.201.28   a      ap    161/12.0/27.8     4       NW-PR010-AP-2B-037  0        72d:3h:37m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6e:f1  corp     N/A   10.216.201.25   a      ap    149/12.0/27.8     6       NW-PR010-AP-1A-038  0        10d:15h:10m:31s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:70:31  corp     N/A   10.216.237.42   a      ap    161/12.0/27.8     2       NW-PR005-AP-1F-014  0        42d:22h:23m:28s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:70:b2  guest    N/A   10.216.237.29   a    ap    124/14.0/30.0     3       NW-PR005-AP-1B-016  0        17d:2h:8m:5s     1500  -          166  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:7b:30  corp     N/A   10.216.237.113  a-VHT  ap    136-/13.0/30.0    3       NW-PR005-AP-2G-058  0        43d:1h:45m:59s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:7b:31  byod     N/A   10.216.237.113  a      ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:59s   1500  -          2    T          A                              no
20:a6:cd:d4:7b:32  guest    N/A   10.216.237.113  a    ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:50s   1500  -          166  T          A                              no
20:a6:cd:d4:7b:33  cah-iot  N/A   10.216.237.113  a      ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:53s   1500  -          164  T          A                              no
20:a6:cd:d4:e6:52  cah-iot  N/A   10.216.201.33   a      ap    48/12.0/23.5      2       NW-PR010-AP-1D-022  0        72d:3h:37m:29s   1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:ed:70  corp     N/A   10.216.201.41   a      ap    157/12.0/27.8     0       NW-PR010-AP-1C-027  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d4:ed:72  cah-iot  N/A   10.216.201.41   a      ap    157/12.0/27.8     0       NW-PR010-AP-1C-027  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:14:50  corp     N/A   10.216.201.36   a      ap    40/12.0/24.0      0       NW-PR010-AP-1E-016  0        23h:41m:58s      1500  -          2    T          A                              no
20:a6:cd:d6:14:52  cah-iot  N/A   10.216.201.36   a      ap    40/12.0/24.0      0       NW-PR010-AP-1E-016  0        23h:41m:54s      1500  -          164  T          A                              no
20:a6:cd:d6:15:70  corp     N/A   10.216.201.47   a      ap    161/12.0/27.8     0       NW-PR010-AP-1C-005  0        72d:3h:37m:43s   1500  -          2    T          A                              no
20:a6:cd:d6:15:72  cah-iot  N/A   10.216.201.47   a      ap    161/12.0/27.8     0       NW-PR010-AP-1C-005  0        72d:3h:37m:39s   1500  -          164  T          A                              no
20:a6:cd:d6:1b:31  corp     N/A   10.216.201.35   a      ap    36/12.0/24.0      1       NW-PR010-AP-1D-030  0        72d:3h:37m:45s   1500  -          2    T          A sU                           no
20:a6:cd:d6:1b:32  cah-iot  N/A   10.216.201.35   a      ap    36/12.0/24.0      3       NW-PR010-AP-1D-030  0        72d:3h:37m:39s   1500  -          164  T          A sU                           no
20:a6:cd:d6:1b:d1  corp     N/A   10.216.201.49   a      ap    161/12.0/27.8     0       NW-PR010-AP-1D-028  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:1b:d2  cah-iot  N/A   10.216.201.49   a      ap    161/12.0/27.8     0       NW-PR010-AP-1D-028  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:1c:b1  corp     N/A   10.216.201.44   a      ap    161/15.0/27.8     2       NW-PR010-AP-1C-021  0        72d:3h:37m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d6:1c:d1  corp     N/A   10.216.201.50   a      ap    149/12.0/27.8     0       NW-PR010-AP-1D-024  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:1c:d2  cah-iot  N/A   10.216.201.50   a      ap    149/12.0/27.8     0       NW-PR010-AP-1D-024  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:23:52  cah-iot  N/A   10.216.201.32   a      ap    161/12.0/27.8     1       NW-PR010-AP-1D-010  0        72d:3h:37m:39s   1500  -          164  T          sA U  (AAC=10.216.254.223)     no
20:a6:cd:d6:25:50  corp     N/A   10.216.201.30   a-VHT  ap    52+/13.0/29.0     9       NW-PR010-AP-2B-007  0        72d:3h:37m:43s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d6:25:71  corp     N/A   10.216.201.43   a      ap    149/12.0/27.8     0       NW-PR010-AP-1C-009  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:25:72  cah-iot  N/A   10.216.201.43   a      ap    149/12.0/27.8     0       NW-PR010-AP-1C-009  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:df:c5:f2  cah-iot  N/A   10.216.201.59   a    ap    36/12.0/24.0      2       NW-PR010-AP-1E-019  0        10d:15h:10m:22s  1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
24:f2:7f:a5:4c:51  corp     N/A   10.216.201.51   a    ap    157/12.0/27.8     0       NW-PR010-AP-1D-012  0        72d:3h:37m:36s   1500  -          2    T          A                              no
24:f2:7f:a5:4c:52  cah-iot  N/A   10.216.201.51   a      ap    157/12.0/27.8     0       NW-PR010-AP-1D-012  0        72d:3h:37m:45s   1500  -          164  T          A                              no
24:f2:7f:a5:4e:31  corp     N/A   10.216.201.27   a    ap    48/12.0/23.5      5       NW-PR010-AP-1A-042  0        10d:15h:12m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
24:f2:7f:a5:4e:32  cah-iot  N/A   10.216.201.27   a      ap    48/12.0/23.5      1       NW-PR010-AP-1A-042  0        10d:15h:12m:31s  1500  -          164  T          sA U  (AAC=10.216.254.223)     no
24:f2:7f:a5:4e:51  corp     N/A   10.216.201.34   a    ap    40/12.0/24.0      1       NW-PR010-AP-1D-018  0        72d:3h:37m:35s   1500  -          2    T          A sU                           no
24:f2:7f:a5:4e:52  cah-iot  N/A   10.216.201.34   a      ap    40/12.0/24.0      1       NW-PR010-AP-1D-018  0        72d:3h:37m:44s   1500  -          164  T          A U                            no
7c:57:3c:1b:32:f1  corp     N/A   10.216.237.23   a      ap    48/12.0/23.5      7       NW-PR005-AP-1B-022  0        17d:2h:8m:10s    1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
7c:57:3c:1b:35:51  corp     N/A   10.216.237.22   a      ap    44/12.0/24.0      3       NW-PR005-AP-1F-019  0        42d:22h:23m:16s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
a8:bd:27:84:9d:91  corp     N/A   10.216.201.37   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-011  0        72d:3h:37m:39s   1500  -          2    T          A                              no
a8:bd:27:84:9d:92  cah-iot  N/A   10.216.201.37   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-011  0        72d:3h:37m:43s   1500  -          164  T          A                              no
b0:b8:67:cf:2a:b0  corp     N/A   10.216.237.32   a-VHT  ap    56-/14.0/29.0     3       NW-PR005-AP-2A-052  0        72d:3h:37m:39s   1500  -          2    T          A U sU                         no
b0:b8:67:cf:2a:b1  byod     N/A   10.216.237.32   a      ap    56/14.0/29.0      0       NW-PR005-AP-2A-052  0        72d:3h:37m:39s   1500  -          2    T          A                              no
b0:b8:67:cf:2a:b3  cah-iot  N/A   10.216.237.32   a      ap    56/14.0/29.0      0       NW-PR005-AP-2A-052  0        72d:3h:37m:43s   1500  -          164  T          A                              no
e8:26:89:8e:30:31  corp     N/A   10.216.237.111  a      ap    40/12.0/24.0      1       NW-PR005-AP-2G-036  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8e:30:32  cah-iot  N/A   10.216.237.111  a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-036  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:32:91  corp     N/A   10.216.237.103  a      ap    48/12.0/23.5      4       NW-PR005-AP-2G-033  0        31d:11h:41m:51s  1500  -          2    T          A U sU                         no
e8:26:89:8e:32:92  cah-iot  N/A   10.216.237.103  a      ap    48/12.0/23.5      3       NW-PR005-AP-2G-033  0        31d:11h:41m:55s  1500  -          164  T          A sU                           no
e8:26:89:8e:39:31  corp     N/A   10.216.237.30   a      ap    149/12.0/27.8     0       NW-PR005-AP-1F-006  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8e:39:32  cah-iot  N/A   10.216.237.30   a      ap    149/12.0/27.8     0       NW-PR005-AP-1F-006  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:60:d1  corp     N/A   10.216.237.89   a    ap    36/12.0/24.0      1       NW-PR005-AP-1F-011  0        42d:22h:23m:25s  1500  -          2    T          A U                            no
e8:26:89:8e:60:d2  cah-iot  N/A   10.216.237.89   a      ap    36/12.0/24.0      0       NW-PR005-AP-1F-011  0        42d:22h:23m:32s  1500  -          164  T          A                              no
e8:26:89:8e:61:11  corp     N/A   10.216.237.105  a    ap    149/12.0/27.8     0       NW-PR005-AP-2G-038  0        43d:1h:45m:41s   1500  -          2    T          A                              no
e8:26:89:8e:61:12  cah-iot  N/A   10.216.237.105  a      ap    149/12.0/27.8     0       NW-PR005-AP-2G-038  0        43d:1h:45m:48s   1500  -          164  T          A                              no
e8:26:89:8e:71:11  corp     N/A   10.216.237.119  a      ap    36/12.0/24.0      1       NW-PR005-AP-2G-037  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8e:71:12  cah-iot  N/A   10.216.237.119  a      ap    36/12.0/24.0      0       NW-PR005-AP-2G-037  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:72:b1  corp     N/A   10.216.237.96   a      ap    40/12.0/24.0      3       NW-PR005-AP-2G-032  0        10d:15h:12m:25s  1500  -          2    T          A U sU                         no
e8:26:89:8e:72:b2  cah-iot  N/A   10.216.237.96   a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-032  0        10d:15h:12m:29s  1500  -          164  T          A                              no
e8:26:89:8e:73:90  corp     N/A   10.216.237.79   a-VHT  ap    100+/13.0/30.0    4       NW-PR005-AP-2B-054  0        31d:11h:41m:51s  1500  -          2    T          A U sU                         no
e8:26:89:8e:73:91  byod     N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8e:73:92  guest    N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:55s  1500  -          166  T          A                              no
e8:26:89:8e:73:93  cah-iot  N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:9b:b1  corp     N/A   10.216.237.95   a    ap    48/12.0/23.5      3       NW-PR005-AP-1F-005  0        10d:15h:6m:38s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8e:b4:71  corp     N/A   10.216.237.68   a    ap    40/12.0/24.0      2       NW-PR005-AP-1F-025  0        31d:11h:39m:52s  1500  -          2    T          sA U  (AAC=10.216.254.223)     no
e8:26:89:8e:cd:b0  corp     N/A   10.216.237.55   a-VHT  ap    120-/13.0/30.0    4       NW-PR005-AP-2G-056  0        10d:15h:10m:29s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8e:d8:f0  corp     N/A   10.216.237.34   a-VHT  ap    140+/14.0/29.5    1       NW-PR005-AP-2A-051  0        31d:11h:41m:55s  1500  -          2    T          A sU                           no
e8:26:89:8e:d8:f1  byod     N/A   10.216.237.34   a    ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8e:d8:f2  guest    N/A   10.216.237.34   a      ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:57s  1500  -          166  T          A                              no
e8:26:89:8e:d8:f3  cah-iot  N/A   10.216.237.34   a      ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:dd:31  corp     N/A   10.216.237.69   a    ap    157/12.0/27.8     0       NW-PR005-AP-1F-018  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8e:dd:32  cah-iot  N/A   10.216.237.69   a      ap    157/12.0/27.8     0       NW-PR005-AP-1F-018  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:f9:31  corp     N/A   10.216.237.109  a    ap    40/12.0/24.0      0       NW-PR005-AP-2G-043  0        43d:1h:45m:45s   1500  -          2    T          A                              no
e8:26:89:8e:f9:32  cah-iot  N/A   10.216.237.109  a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-043  0        43d:1h:45m:54s   1500  -          164  T          A                              no
e8:26:89:8f:1e:70  corp     N/A   10.216.237.66   a-VHT  ap    124+/15.0/30.0    4       NW-PR005-AP-2B-055  0        10d:15h:12m:31s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:25:31  corp     N/A   10.216.237.21   a    ap    44/15.0/24.0      0       NW-PR005-AP-1F-010  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:25:32  cah-iot  N/A   10.216.237.21   a      ap    44/15.0/24.0      0       NW-PR005-AP-1F-010  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:3d:f1  corp     N/A   10.216.237.99   a    ap    48/12.0/23.5      0       NW-PR005-AP-2G-040  0        43d:1h:45m:43s   1500  -          2    T          A                              no
e8:26:89:8f:3d:f2  cah-iot  N/A   10.216.237.99   a      ap    48/12.0/23.5      0       NW-PR005-AP-2G-040  0        43d:1h:45m:52s   1500  -          164  T          A                              no
e8:26:89:8f:48:b1  corp     N/A   10.216.237.112  a    ap    157/12.0/27.8     3       NW-PR005-AP-2G-029  0        10d:15h:10m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:57:d1  corp     N/A   10.216.237.67   a    ap    161/12.0/27.8     0       NW-PR005-AP-1F-008  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:57:d2  cah-iot  N/A   10.216.237.67   a      ap    161/12.0/27.8     0       NW-PR005-AP-1F-008  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:5b:b1  corp     N/A   10.216.237.118  a    ap    36/12.0/24.0      2       NW-PR005-AP-2G-031  0        10d:15h:10m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:65:91  corp     N/A   10.216.237.20   a    ap    157/12.0/27.8     0       NW-PR005-AP-1F-007  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:65:92  cah-iot  N/A   10.216.237.20   a      ap    157/12.0/27.8     0       NW-PR005-AP-1F-007  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:6e:d1  corp     N/A   10.216.237.77   a    ap    36/12.0/24.0      2       NW-PR005-AP-1E-001  0        31d:11h:41m:48s  1500  -          2    T          A sU                           no
e8:26:89:8f:6e:d2  cah-iot  N/A   10.216.237.77   a      ap    36/12.0/24.0      0       NW-PR005-AP-1E-001  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:8f:f1  corp     N/A   10.216.237.104  a      ap    44/12.0/24.0      1       NW-PR005-AP-2G-034  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8f:8f:f2  cah-iot  N/A   10.216.237.104  a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-034  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:9e:71  corp     N/A   10.216.237.33   a      ap    36/12.0/24.0      5       NW-PR005-AP-2A-004  0        10d:15h:12m:27s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:ae:51  corp     N/A   10.216.237.101  a      ap    36/12.0/24.0      6       NW-PR005-AP-2G-027  0        10d:15h:12m:25s  1500  -          2    T          A U sU                         no
e8:26:89:8f:ae:52  cah-iot  N/A   10.216.237.101  a      ap    36/12.0/24.0      2       NW-PR005-AP-2G-027  0        10d:15h:12m:29s  1500  -          164  T          A sU                           no
e8:26:89:8f:b2:31  corp     N/A   10.216.237.102  a      ap    36/12.0/24.0      1       NW-PR005-AP-2G-039  0        43d:1h:45m:52s   1500  -          2    T          A U                            no
e8:26:89:8f:b2:32  cah-iot  N/A   10.216.237.102  a      ap    36/12.0/24.0      0       NW-PR005-AP-2G-039  0        43d:1h:45m:56s   1500  -          164  T          A                              no
e8:26:89:8f:b8:b1  corp     N/A   10.216.237.106  a      ap    161/12.0/27.8     3       NW-PR005-AP-2G-030  0        10d:15h:10m:25s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:b9:31  corp     N/A   10.216.237.110  a      ap    44/12.0/24.0      1       NW-PR005-AP-2G-044  0        43d:1h:45m:44s   1500  -          2    T          A sU                           no
e8:26:89:8f:b9:32  cah-iot  N/A   10.216.237.110  a      ap    44/12.0/24.0      5       NW-PR005-AP-2G-044  0        43d:1h:45m:48s   1500  -          164  T          A U sU                         no
e8:26:89:8f:c1:31  corp     N/A   10.216.237.85   a      ap    157/12.0/27.8     0       NW-PR005-AP-2G-035  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8f:c1:32  cah-iot  N/A   10.216.237.85   a      ap    157/12.0/27.8     0       NW-PR005-AP-2G-035  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:c2:f0  corp     N/A   10.216.237.90   a-VHT  ap    140+/13.0/29.5    8       NW-PR005-AP-1A-023  0        17d:1h:33m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:c2:f2  guest    N/A   10.216.237.90   a      ap    140/13.0/29.5     1       NW-PR005-AP-1A-023  0        17d:1h:33m:48s   1500  -          166  T          sA U  (AAC=10.216.254.223)     no
e8:26:89:8f:d0:51  corp     N/A   10.216.237.98   a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-028  0        10d:15h:12m:25s  1500  -          2    T          A                              no
e8:26:89:8f:d0:52  cah-iot  N/A   10.216.237.98   a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-028  0        10d:15h:12m:29s  1500  -          164  T          A                              no
"""

_nlc_OH044_20230613 = """
National Logistics Center
10.232.192.164

:00\:30\:44\:71\:2B\:3D: :6:2437 MHz:75:130 Mbit/s
byod:6C\:C4\:9F\:85\:FD\:B1: :116:5580 MHz:30:54 Mbit/s
byod:6C\:C4\:9F\:85\:FF\:F1: :112:5560 MHz:37:54 Mbit/s
byod:6C\:C4\:9F\:86\:05\:51: :124:5620 MHz:80:54 Mbit/s
byod:6C\:C4\:9F\:86\:17\:F1: :100:5500 MHz:45:54 Mbit/s
byod:6C\:C4\:9F\:86\:59\:D1: :140:5700 MHz:45:54 Mbit/s
byod:6C\:C4\:9F\:87\:0A\:B1: :153:5765 MHz:32:54 Mbit/s
byod:6C\:C4\:9F\:87\:11\:11: :64:5320 MHz:39:54 Mbit/s
byod:6C\:C4\:9F\:87\:60\:11: :132:5660 MHz:80:54 Mbit/s
cah-iot:34\:8A\:12\:2B\:20\:D2: :153:5765 MHz:32:54 Mbit/s
cah-iot:6C\:C4\:9F\:85\:FD\:B3: :116:5580 MHz:29:54 Mbit/s
cah-iot:6C\:C4\:9F\:85\:FF\:F3: :112:5560 MHz:37:54 Mbit/s
cah-iot:6C\:C4\:9F\:86\:05\:53: :124:5620 MHz:80:54 Mbit/s
cah-iot:6C\:C4\:9F\:86\:17\:F3: :100:5500 MHz:45:54 Mbit/s
cah-iot:6C\:C4\:9F\:86\:2A\:F2: :149:5745 MHz:37:54 Mbit/s
cah-iot:6C\:C4\:9F\:86\:59\:D3: :140:5700 MHz:45:54 Mbit/s
cah-iot:6C\:C4\:9F\:86\:B9\:12: :40:5200 MHz:29:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:0A\:B3: :153:5765 MHz:34:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:11\:13: :64:5320 MHz:40:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:11\:72: :161:5805 MHz:27:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:38\:F2: :48:5240 MHz:45:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:60\:13: :132:5660 MHz:80:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:6F\:12: :40:5200 MHz:29:54 Mbit/s
cah-iot:6C\:C4\:9F\:87\:80\:52: :149:5745 MHz:25:54 Mbit/s
cah-iot:CC\:88\:C7\:19\:DC\:72: :149:5745 MHz:24:54 Mbit/s
corp:34\:8A\:12\:2B\:20\:D1: :153:5765 MHz:32:54 Mbit/s
corp:6C\:C4\:9F\:85\:FD\:B0: :116:5580 MHz:30:540 Mbit/s
corp:6C\:C4\:9F\:85\:FF\:F0: :112:5560 MHz:37:540 Mbit/s
corp:6C\:C4\:9F\:86\:05\:50: :124:5620 MHz:80:540 Mbit/s
corp:6C\:C4\:9F\:86\:17\:F0: :100:5500 MHz:44:540 Mbit/s
corp:6C\:C4\:9F\:86\:2A\:F1: :149:5745 MHz:37:54 Mbit/s
corp:6C\:C4\:9F\:86\:59\:D0: :140:5700 MHz:45:540 Mbit/s
corp:6C\:C4\:9F\:87\:0A\:B0: :153:5765 MHz:34:540 Mbit/s
corp:6C\:C4\:9F\:87\:11\:10: :64:5320 MHz:40:540 Mbit/s
corp:6C\:C4\:9F\:87\:11\:71: :161:5805 MHz:25:54 Mbit/s
corp:6C\:C4\:9F\:87\:38\:F1: :48:5240 MHz:45:54 Mbit/s
corp:6C\:C4\:9F\:87\:60\:10:*:132:5660 MHz:67:540 Mbit/s
corp:6C\:C4\:9F\:87\:80\:51: :149:5745 MHz:25:54 Mbit/s
corp:CC\:88\:C7\:19\:DC\:71: :149:5745 MHz:24:54 Mbit/s
guest:6C\:C4\:9F\:85\:FD\:B2: :116:5580 MHz:30:54 Mbit/s
guest:6C\:C4\:9F\:85\:FF\:F2: :112:5560 MHz:37:54 Mbit/s
guest:6C\:C4\:9F\:86\:05\:52: :124:5620 MHz:80:54 Mbit/s
guest:6C\:C4\:9F\:86\:17\:F2: :100:5500 MHz:44:54 Mbit/s
guest:6C\:C4\:9F\:86\:59\:D2: :140:5700 MHz:45:54 Mbit/s
guest:6C\:C4\:9F\:87\:0A\:B2: :153:5765 MHz:34:54 Mbit/s
guest:6C\:C4\:9F\:87\:11\:12: :64:5320 MHz:39:54 Mbit/s
guest:6C\:C4\:9F\:87\:60\:12: :132:5660 MHz:80:54 Mbit/s
"""



import copy
import json
import os
import pdb
try:
    import requests
except:
    # for unittest
    pass

import shutil
import socket
import subprocess
import sys
import threading
import time
import traceback
import unittest

from sys import version as python_version
from cgi import parse_header, parse_multipart

if python_version.startswith('3'):
    from urllib.parse import parse_qs
    from http.server import BaseHTTPRequestHandler,HTTPServer
else:
    from urlparse import parse_qs
    from BaseHTTPServer import BaseHTTPRequestHandler,HTTPServer

#from apscheduler.schedulers.background import BackgroundScheduler

# Globals
s_corp_wifi_certs_path = "/cardinal/corp_certs/"
s_corp_wifi_cert_last_used_path = "/cardinal/"

s_corp_wifi_certs_2_path = "/cardinal/wifi_certs/" # provided by pi_organization.py

s_save_values_location = '/cardinal/save_values/' + service + '_' + version + '_'

s_crypt_cahiot_preshare = 'g9nHZ3QvYZ'
s_http_page_count = 0
s_http_previous_path = ''

s_force_rescan = True
s_run_speed_tests = False
s_pop_to_top_signal_channel = False

s_is_engineering_device = False

s_forced_rescan_count = 0
s_bssid_to_ap = None

s_seconds_per_manage_pass = 4

s_lan_worst_restarts = 0

s_nic_tracking = {}

# ----------------------------
def is_nic_changed(nic1, nic2):
# ----------------------------
    if nic1 == nic2:
        return False
    else:
        return True

# ----------------------------
def get_saved_value(item):
# ----------------------------
    return_value = ''
    output_file = s_save_values_location + item
    try:
        return_value = open(output_file, 'r').read()
    except:
        pass

    return return_value

# ----------------------------
def set_saved_value(item, content):
# ----------------------------
    output_file = s_save_values_location + item
    previous_value_file = output_file + '.previous'
    return write_content_if_different(str(content), output_file, previous_value_file=previous_value_file)

# ----------------------------
def write_content_if_different(content, output_file, with_execute=False, previous_value_file=''):
# ----------------------------
    return_true_if_different = False
    # This does a temp file, then atomic move to final
    if content:
        last_content_written = ''
        last_content_source = 'default'
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        if os.path.isfile(output_file):
            with open(output_file, 'r') as f:
                last_content_written = f.read()
                last_content_source = 'read_from_file'

        if content != last_content_written:
            temp_file = output_file + '.tmp' # must be on the same physical device as the destination, to be atomic on mv
            with open(temp_file, 'w') as f:
                f.write(content)


            print ("updating file: " + output_file)
            if with_execute:
                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)

            pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
            pass_string, fails = do_one_command('sudo mv ' + temp_file + ' ' + output_file)
            return_true_if_different = True

            if previous_value_file:
                try:
                    with open(previous_value_file, 'w') as f:
                        f.write(last_content_source + '\n' + last_content_written)
                except:
                    pass

    return return_true_if_different

# ----------------------------
def rate_str_from_rate(rate):
# ----------------------------
    return rate.split()[0] + rate.split()[1][0]

# ----------------------------
def get_uptime():
# ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0

# ----------------------------
def get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list):
# ----------------------------
    return_value = ''

    try:
        if current_cert in corp_wifi_certs_list:
            return_value = corp_wifi_certs_list[(1 + corp_wifi_certs_list.index(current_cert)) % len(corp_wifi_certs_list)]
        else:
            return_value = corp_wifi_certs_list[0]
    except:
        pass

    return return_value

# ----------------------------
def find_external_radio_from_usbreset(content):
# ----------------------------
    return_value = ''

    for line in content.split('\n'):
        if ('802' in line) and ('WLAN' in line):
            splits = line.strip().split()
            if len(splits) > 1:
                return_value = splits[1]
    return return_value

# ----------------------------
def find_external_radio_from_lsusb_dash_v(lsusb_t):
# ----------------------------
    in_what_we_want = False

    return_value = ''

    bus_number = ''
    for line in lsusb_t.split('\n'):
        splits = line.split()
        if len(splits) > 2:
            if splits[1] == 'Bus':
                bus_number = splits[2].split('.')[0]

        if 'rt2800usb' in line:

            device_number = ''
            if ': Dev' in line:
                device_number = line.split(': Dev')[1].split(',')[0]
            if bus_number and device_number:
                return_value = '{:03d}'.format(int(bus_number)) + '/' + '{:03d}'.format(int(device_number))

    return return_value

# ----------------------------
def is_connection_active(line):
# ----------------------------
    if len(line.split(':')) > 3:
        if line.split(':')[3]:
            return True
        else:
            return False
    else:
        return False


# ----------------------------
def wireless_to_configure_from_wlanX(device):
# ----------------------------
    return device[-1:]


# ----------------------------
def get_wifi_mapping(devices_report):
# ----------------------------
    internal = ''
    external = ''

    in_wlan0 = False
    in_wlan1 = False

    wlan0_present = False
    wlan1_present = False

    for line in devices_report.split('\n'):
        try:
            if line[0:6] == 'wlan0:':
                in_wlan0 = True
                in_wlan1 = False
                wlan0_present = True
            if line[0:6] == 'wlan1:':
                in_wlan0 = False
                in_wlan1 = True
                wlan1_present = True
        except:
            pass

        if "brcmfmac" in line:
            if in_wlan0:
                internal = 'wlan0'
            if in_wlan1:
                internal = 'wlan1'

    if wlan1_present:
        if internal == 'wlan0':
            external = 'wlan1'

    if wlan0_present:
        if internal == 'wlan1':
            external = 'wlan0'

    return_value = {'internal_wifi':internal, 'external_wifi':external}

    return return_value

# ----------------------------
def make_bssid_to_ap():
# ----------------------------
    return_value = {}

    for item in bssid_to_AP_raw.split('\n'):
        splits = item.split()
        if len(splits) > 8:
            full_ap = splits[8].split('-')[1] + ':' + splits[8].split('-')[-1]
            return_value[splits[0].upper()] = full_ap

    return return_value

# ----------------------------
def get_mac_address_for_port_name(portName):
# ----------------------------
    return_value = ''
    pass_string, fails = do_one_command('ip -j addr')
    # pass_string = '[{"ifindex":1,"ifname":"lo","flags":["LOOPBACK","UP","LOWER_UP"],"mtu":65536,"qdisc":"noqueue","operstate":"UNKNOWN","group":"default","txqlen":1000,"link_type":"loopback","address":"00:00:00:00:00:00","broadcast":"00:00:00:00:00:00","addr_info":[{"family":"inet","local":"127.0.0.1","prefixlen":8,"scope":"host","label":"lo","valid_life_time":4294967295,"preferred_life_time":4294967295},{"family":"inet6","local":"::1","prefixlen":128,"scope":"host","valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":2,"ifname":"eth0","flags":["BROADCAST","MULTICAST","UP","LOWER_UP"],"mtu":1500,"qdisc":"mq","operstate":"UP","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c2","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691098,"preferred_life_time":691098},{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","secondary":true,"dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691103,"preferred_life_time":604703},{"family":"inet6","local":"fe80::d21:c163:5965:731e","prefixlen":64,"scope":"link","noprefixroute":true,"valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":3,"ifname":"wlan0","flags":["NO-CARRIER","BROADCAST","MULTICAST","UP"],"mtu":1500,"qdisc":"pfifo_fast","operstate":"DOWN","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c4","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[]}]'
    try:
        #print ('pass_string', pass_string)
        the_list = json.loads(pass_string)
        for index in range(0, len(the_list)):
            # the_list[1].keys()
            # [u'addr_info', u'operstate', u'qdisc', u'group', u'mtu', u'broadcast', u'flags', u'address', u'ifindex', u'txqlen', u'ifname', u'link_type']
            #print ("ifname", the_list[index]['ifname'])
            if the_list[index]['ifname'] == portName:
                return_value = the_list[index]['address']
    except:
        pass

    return return_value

# ----------------------------
def wifi_config_from_content(content):
# ----------------------------
    return_value = {}

    if content:
        splits = content.split('\n')[0].split(",")
        return_value['ssid'] = splits[0]

        if len(splits) > 1:
            return_value['psk'] = splits[1]

    return return_value

# ----------------------------
def save_shared_counts(the_counts):
# ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_' + service + '_' + the_count
            do_atomic_write_if_different(file_name, str(the_counts[the_count]))
        except:
            pass

# ----------------------------
def get_ping_from_result(input_string):
# ----------------------------
    return_value = ''

    for line in input_string.split('\n'):
        if 'time=' in line:
            return_value = line.split('time=')[1]

    return return_value


# ----------------------------
def get_speed_from_upload_result(input_string):
# ----------------------------
    return_value = ''

    splits = input_string.split()
    for index_count in range(1, len(splits)):
        if 'MBps' in splits[index_count]:
            return_value = splits[index_count-1] + ' MB/s'

    return return_value

# ----------------------------
def get_parens_items(input_string):
# ----------------------------
    return_value = []

    if ('(' in input_string):
        location = input_string.find('(')
        for first_split in input_string[location:].split('('):
            if ')' in first_split:
                return_value.append(first_split.split(')')[0])

    return return_value

# ----------------------------
def get_speed_from_download_result(fail_string):
# ----------------------------
    return_value = '(no download speed)'

    for the_option in get_parens_items(fail_string):
        if ' ' in the_option:
            return_value = the_option

    return return_value

# ----------------------------
def make_future_path_string(current_path, key, value):
# ----------------------------
    future_path = copy.deepcopy(current_path)
    future_path[key] = value
    path_string = ''
    for item in future_path:
        if path_string:
            path_string += ','
        path_string += item + '=' + future_path[item]
    return path_string

# ----------------------------
def set_wlan_tx_power(wlan, the_string_value):
# ----------------------------
    content, fails = do_one_command('iw dev ' + wlan + ' set txpower fixed ' + the_string_value)

# ----------------------------
def get_wifi_info(wlan):
# ----------------------------
    content, fails = do_one_command('iw dev ' + wlan + ' info')

    return content

# ----------------------------
def get_wifi_tx_power(content):
# ----------------------------
    return_value = ''

    for line in content.replace('\t','').split('\n'):
        if 'txpower' in line:
            try:
                return_value = line.split()[1] + ' ' + line.split()[2]
            except:
                pass

    return return_value

# ----------------------------
def get_wifi_results(wifi_list):
# ----------------------------
    return_value = {}

    # sudo nmcli -c no -t -f ssid,bssid,signal,in-use dev wifi
    list_to_use = wifi_list.replace('\:','<colon>')

    for line_item in list_to_use.split('\n'):
        splits = line_item.split(':')

        if len(splits) > 6:
            ssid = splits[0].replace('<colon>',':')

            if not ssid:
                ssid = '(hidden)'

            if not ssid in return_value:
                return_value[ssid] = {}

            bssid = splits[1].replace('<colon>',':')
            in_use = splits[2].replace('<colon>',':')
            channel = splits[3].replace('<colon>',':')
            freq = splits[4].replace('<colon>',':')
            signal = splits[5].replace('<colon>',':')
            rate = splits[6].replace('<colon>',':')

            if bssid in return_value[ssid]:
                if '*' in return_value[ssid][bssid]['in_use']:
                    pass
                    # let it ride
                else:
                    return_value[ssid][bssid] = {'in_use':in_use, 'channel':channel, 'freq':freq, 'signal':signal, 'rate':rate}
            else:
                return_value[ssid][bssid] = {'in_use':in_use, 'channel':channel, 'freq':freq, 'signal':signal, 'rate':rate}

    return return_value

# ----------------------------
def get_access_points_for_ssid(wifi_state, ssid):
# ----------------------------
    return_value = []
    if ssid in wifi_state:
        for bssid in wifi_state[ssid]:
            return_value.append((wifi_state[ssid][bssid]['in_use'], wifi_state[ssid][bssid]['channel'], wifi_state[ssid][bssid]['signal']))

    return return_value

# ----------------------------
def get_my_wifi_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_connect"
    serial = get_serial()

    setting = 'corp'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_connect"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting

# ----------------------------
def get_my_coaster_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_coaster"
    serial = get_serial()

    setting = '0'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_coaster"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting


# ----------------------------
def get_my_hopper_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_hopper"
    serial = get_serial()

    setting = 'no-hop'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_hopper"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")
    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)

# ----------------------------
def do_atomic_write_if_different(output_file, content):
# ----------------------------
    did_write = False

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        # flush all to disk
#        os.sync()

        shutil.move(temp_name, output_file)

        did_write = True

    return did_write

# ----------------------------
def write_lan_detail(content):
# ----------------------------
    do_atomic_write_if_different('/dev/shm/pi_network_detail.txt', content)


# ----------------------------
def restart_network_manager():
# ----------------------------
    global s_lan_worst_restarts

    is_working = True


    count_how_many = 0
    while is_working:
        count_how_many += 1
        set_wifi_command = 'sudo systemctl restart NetworkManager'
        pass_string, fails = do_one_command(set_wifi_command)

        print ('pass:' + pass_string)
        print ('other:' + fails)

        if fails:
            is_working = True
        else:
            is_working = False
            time.sleep(0.10)

    if count_how_many > s_lan_worst_restarts:
        s_lan_worst_restarts = count_how_many

    print ('s_lan_worst_restarts', s_lan_worst_restarts)
    return count_how_many

# ----------------------------
def manage_network_connections(current_state, current_inputs):
# ----------------------------
    # unpack the inputs
    wifi_list = current_inputs['wifi_list']
    connection_list = current_inputs['connection_list']
    wifi_mac_address_to_use = current_inputs['wifi_mac_address_to_use']
    wifi_mapping = current_inputs['wifi_mapping']
    usbreset_content = current_inputs['usbreset_content']
    current_uptime = current_inputs['current_uptime']
    wifi_config_content = current_inputs['wifi_config_content']
    coaster = current_inputs['coaster']

    # build the return values
    shared_counts = {}
    actions_list = []
    trace_back_string = ''

    # -------------------------------
    # get the current state filled in
    # -------------------------------
    current_state['s_allow_wifi_config_connection'] = wifi_config_from_content(wifi_config_content)
    if not 's_time_of_last_pop' in current_state:
        current_state['s_time_of_last_pop'] = 0
    if not 's_allow_wifi_config_previous' in current_state:
        current_state['s_allow_wifi_config_previous'] = None
    if not 's_pass_count' in current_state:
        current_state['s_pass_count'] = 0
    if not 's_passes_not_connected' in current_state:
        current_state['s_passes_not_connected'] = 0
    if not 's_usb_reset_radio_count' in current_state:
        current_state['s_usb_reset_radio_count'] = 0

    # -------------------------------
    # start working on it
    # -------------------------------
    if current_state['s_allow_wifi_config_connection'] != current_state['s_allow_wifi_config_previous']:
        current_state['s_allow_wifi_config_previous'] = copy.copy(current_state['s_allow_wifi_config_connection'])
        # make it drop all wifi, and start again
        current_state['s_pass_count'] = 0
        current_uptime = 1 # override the uptime, to make it look like we just started, and allow the change

    the_counts = {'signal':0} # values to be written to the shared space, so that they get sent to Slicer

    # -------------------------------
    # Coaster
    if coaster != '0':
        current_state['NetworkManager.conf_content'] = """[main]
ignore-carrier=""" + coaster + """
plugins=ifupdown,keyfile

[ifupdown]
managed=false"""
    else:
        current_state['NetworkManager.conf_content'] = """[main]
plugins=ifupdown,keyfile

[ifupdown]
managed=false"""



    try:
        current_state['s_pass_count'] += 1
        my_signal = 0

        # -------------------------------
        # decide what to take away

        # new as of 2022.09.19
        wifi_mac_address_external = current_inputs['wifi_mac_address_external']
        if wifi_mapping['external_wifi']:
            wireless_to_configure = wireless_to_configure_from_wlanX(wifi_mapping['external_wifi'])
        else:
            wireless_to_configure = wireless_to_configure_from_wlanX(wifi_mapping['internal_wifi'])
        wireless_lan_internal_number = wireless_to_configure_from_wlanX(wifi_mapping['internal_wifi'])

        list_to_remove = []
        is_wired = False
        is_wifi_configured = False

        ssid_name = 'cah-iot'

        is_hopper_enabled = (current_inputs['hopper_setting'] == 'hop')

        splits = connection_list.split('\n')

        for item in splits:
            parts = item.split(':')
            if len(parts) > 3:
                connection_name = parts[0]
                connection_type = parts[2]
                connection_port = parts[3]

                if ('Wired' in connection_name) or ('ethernet' in connection_type):
                    if connection_port:
                        is_wired = True
                        print ("Is Wired")
                    else:
                        # no wire plugged in, remove this open item
                        actions_list.append({'do_one_command':"sudo nmcli con delete \"" + connection_name + "\""})

        if current_inputs["time_now"] == 1697486765.8537405:
            pdb.set_trace()

        for item in splits:
            parts = item.split(':')
            if len(parts) > 3:
                connection_name = parts[0]
                connection_type = parts[2]
                connection_port = parts[3]

                if 'wireless' in connection_type:
                    is_early_since_boot = False
                    if current_uptime:
                        if current_uptime < 120:
                            is_early_since_boot = True
                    if is_wired or (is_early_since_boot and (current_state['s_pass_count'] == 1)) : # on first pass, start from wireless scratch (to let a new cert apply, specifically)
                        # remove all wireless
                        list_to_remove.append(connection_name)
                    else:
                        # remove all wireless but config and cah-iot
                        remove_it = True

                        if 'cah-iot' + wireless_to_configure in connection_name:
                            remove_it = False
                        if 'config' + wireless_to_configure in connection_name:
                            remove_it = False

                        # be able to disconnect from a removed/unplugged adapter
                        if connection_port:
                            if not 'wlan' + wireless_to_configure in connection_port:
                                remove_it = True
                            else:
                                current_state['s_passes_not_connected'] = 0
                        else:
                            # check that an external USB radio might have gotten unplugged and replugged
                            current_state['s_passes_not_connected'] += 1
                            if current_state['s_passes_not_connected'] > 30 / s_seconds_per_manage_pass:
                                remove_it = True

                        # check that external has correct mac (this cleans up old connections from older firmware setups, it should be very rare)
                        if 'wlan' + wireless_to_configure in connection_port:
                            if wifi_mac_address_external:
                                if wifi_mac_address_external != wifi_mac_address_to_use:
                                    remove_it = True

                        if remove_it:
                            current_state['s_passes_not_connected'] = 0
                            list_to_remove.append(connection_name)
                        else:
                            is_wifi_configured = True
#                            print ("Is wifi Configured")

        for connection_name in list_to_remove:
            actions_list.append({'do_one_command':"sudo nmcli con delete \"" + connection_name + "\""})


        # -------------------------------
        # decide what to add

        # if not on wired, then add the allowed wifi networks
        if is_wired:
            pass

        else:
            # -------------------------------
            # We are WiFi: See what our condition is, and what to do, if anything

            if current_state['s_allow_wifi_config_connection']:
                ssid_name = current_state['s_allow_wifi_config_connection']['ssid']

            if not is_wifi_configured:
                wifi_note = ''

                set_wifi_actions_list = []

                set_wifi_command = ''
                set_wifi_command_followup = ''

                if wireless_to_configure != wireless_lan_internal_number:
                    # is on external
                    # try to reset it, to get a clean start each time
                    device_to_reset = find_external_radio_from_usbreset(usbreset_content)
                    if device_to_reset:
                        actions_list.append({'do_one_command':'usbreset ' + device_to_reset})
                        actions_list.append({'dwell_seconds':5})
                        current_state['s_usb_reset_radio_count'] += 1

                if ssid_name == 'cah-iot':
                    preshare = calculate_rot13c(s_crypt_cahiot_preshare)
                    set_wifi_command = 'sudo nmcli connection add type wifi con-name "cah-iot' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "' + preshare + '"'
                    if wifi_mac_address_to_use and wireless_to_configure != wireless_lan_internal_number:
                        # is on external, clone the mac from the internal
                        set_wifi_command_followup = 'sudo nmcli connection modify --temporary "cah-iot' + wireless_to_configure + '"  802-11-wireless.cloned-mac-address ' + wifi_mac_address_to_use + ' && sudo nmcli con up cah-iot' + wireless_to_configure
                    else:
                        set_wifi_command_followup = 'sudo nmcli con up cah-iot' + wireless_to_configure

                    set_wifi_actions_list.append({'do_one_command':set_wifi_command})
                    set_wifi_actions_list.append({'do_one_command':set_wifi_command_followup})
                else:
                    # 'config'
                    pass



                if current_state['s_allow_wifi_config_connection']: # for testing on other networks, but still name the connection 'corp'
                    set_wifi_actions_list = [] # start over

                    # from /cardinal/wifi_config.txt
                    ssid_name = current_state['s_allow_wifi_config_connection']['ssid']

                    preshare = ''
                    if 'psk' in current_state['s_allow_wifi_config_connection']:
                        if current_state['s_allow_wifi_config_connection']['psk']:
                            preshare = current_state['s_allow_wifi_config_connection']['psk']

                    if preshare:
                        set_wifi_command = 'sudo nmcli connection add type wifi con-name "config' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "' + preshare + '"'
                    else:
                        set_wifi_command = 'sudo nmcli connection add type wifi con-name "config' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '"'

                    if wifi_mac_address_to_use and wireless_to_configure != wireless_lan_internal_number:
                        # is on external, clone the mac from the internal
                        set_wifi_command_followup = 'sudo nmcli connection modify --temporary "config' + wireless_to_configure + '"  802-11-wireless.cloned-mac-address ' + wifi_mac_address_to_use + ' && sudo nmcli con up config' + wireless_to_configure
                    else:
                        set_wifi_command_followup = 'sudo nmcli con up config' + wireless_to_configure

                    set_wifi_actions_list.append({'do_one_command':set_wifi_command})
                    set_wifi_actions_list.append({'do_one_command':set_wifi_command_followup})

                if set_wifi_actions_list:

                    set_wifi_actions_list.append({'restart_network_manager':None})

                    set_wifi_actions_list.append({'do_one_command':'/sbin/iwconfig wlan0 power off'}) # turn off the power save feature, so that it does not disable
                    set_wifi_actions_list.append({'do_one_command':'/sbin/iwconfig wlan1 power off'}) # turn off the power save feature, so that it does not disable

                for set_wifi_action in set_wifi_actions_list:
                    actions_list.append(set_wifi_action)


            # -------------------------------
            # do wifi watching, to report findings, and to see if we need to hop
            results_d = get_wifi_results(wifi_list)
            if ssid_name in results_d:
                mine = ''
                my_signal = 0
                strongest_bssid = ''
                max_strength = 0
                for bssid in results_d[ssid_name]:
                    try:
                        if int(results_d[ssid_name][bssid]['signal']) > max_strength:
                            max_strength = int(results_d[ssid_name][bssid]['signal'])
                            strongest_bssid = bssid
                    except:
                        pass

                    if results_d[ssid_name][bssid]['in_use'] == '*':
                        mine = bssid

                        try:
                            my_signal = int(results_d[ssid_name][bssid]['signal'])
                            the_counts['signal'] = my_signal
                        except:
                            pass

                # seconds, delta required
                pop_schedule = [[0,100],[10,50],[20,25],[30,15]]
                pop_if_mine_below =  45
                time_since = current_inputs['time_now'] - current_state['s_time_of_last_pop']
                signal_delta_needed = 100
                for pair in pop_schedule:
                    if time_since > pair[0]:
                        signal_delta_needed = pair[1]

                changed_to_channel = ''
                if is_hopper_enabled:
                    do_the_pop = False
                    if max_strength > my_signal + signal_delta_needed:
                        do_the_pop = True
                    if my_signal < pop_if_mine_below:
                        do_the_pop = True

                    if do_the_pop:
                        command = 'wpa_cli -i wlan' + wireless_to_configure + ' bssid 0 ' + strongest_bssid
                        actions_list.append({'do_one_command':command})

                        command = 'wpa_cli -i wlan' + wireless_to_configure + ' reassociate'
                        actions_list.append({'do_one_command':command})

                        current_state['s_time_of_last_pop'] = current_inputs['time_now']
                        changed_to_channel = results_d[ssid_name][strongest_bssid]['channel']

        shared_counts['signal'] = my_signal
        shared_counts['corp_cert'] = ''

    except:
        trace_back_string = traceback.format_exc()

    return current_state, shared_counts, actions_list, trace_back_string

# ----------------------------
def read_file_contents(the_file):
# ----------------------------
    try:
        return_value = open(the_file).read()
    except:
        return_value = ''
    return return_value

# ----------------------------
def write_debug_string(debug_string):
# ----------------------------
    debug_name = '/dev/shm/pi_network_manage_debug.txt' # leaving this on will take up space on the second screen on the pi
    debug_name = '/dev/shm/pi_network_manage_notes.txt'

    if True:
        try:
            do_atomic_write_if_different(debug_name, debug_string)
        except:
            pass

# ----------------------------
def get_wifi_scan():
# ----------------------------
    global s_forced_rescan_count
    results = {}

    # if we are wired, then there is no wifi to report
    results['ssid'] = '(not_on_wifi)'
    results['chan'] = ''
    results['signal'] = ''
    results['cah-iot'] = 'No'
    results['lan'] = 'dne'
    results['nic'] = 'int'
    results['corp_cert'] = ''

    connection_list, fails = do_one_command('nmcli -t c s')
    #corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0
    #Wired connection 1:1acbc619-206d-3148-9ca9-50e49677f612:802-3-ethernet:
    splits = connection_list.split('\n')
    for item in splits:
        parts = item.split(':')
        if len(parts) > 3:
            if parts[3]:
                results['lan'] = parts[3]
                devices_report, fails = do_one_command('nmcli -t')
                wifi_mapping = get_wifi_mapping(devices_report)
                if results['lan'] == wifi_mapping['external_wifi']:
                    results['nic'] = 'ext'

    # do a scan, and force it to actually do the scan, not just return from cache
    if s_force_rescan:
        s_forced_rescan_count += 1
        result, fails = do_one_command('nmcli -c no device wifi list --rescan yes') # This is what gives it the chance to change channels (aggressive mode)
    else:
        result, fails = do_one_command('nmcli -c no device wifi list') # rescan causes a high power burst... lets avoid that

    for line in result.split('\n'):
        if line:
            if 'cah-iot' in line:
                results['cah-iot'] = 'Yes'
            is_the_inuse = False
            if line[0] == '*':
                is_the_inuse = True
                line = ' ' + line[1:]

            splits = line.split()
            #print (splits)

            if is_the_inuse:
                if len(splits) > 2:
                    results['ssid'] = splits[0]
                    results['chan'] = splits[2]
                if len(splits) > 5:
                    results['signal'] = splits[5]

    return results

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ====================================
def calculate_rot13b(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz9876543210ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    trans = chars[13:]+chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ====================================
def calculate_rot13c(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz9876543210ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    trans = chars[int(len(chars)/2):]+chars[:int(len(chars)/2)]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ----------------------------
def do_one_time():
# ----------------------------
    pass


# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def build_get_page_body(the_path='/', results_d={}, wlan0_info='', wlan_active='wlan0'):
# ----------------------------
    # localhost:7040, gives the_path = '/'
    # localhost:7040/?set=1234, gives the_path = '/?set=1234'
    global s_http_page_count, s_http_previous_path, s_bssid_to_ap
    s_http_page_count += 1

    split_path = {}
    for item in the_path.replace('/','').replace('?','').split(','):
        try:
            split_path[item.split('=')[0]] = item.split('=')[1]
        except:
            pass

    lines_of_data = {}
    signal_thresholds = [95,90,85,80,75,70,65,60,55,50,45,40,35,30,25,20,15,10]

    for item in sorted(results_d):
        lines_of_data[item] = {}
        for signal_threshold in signal_thresholds:
            lines_of_data[item][signal_threshold] = ''

        for bssid in results_d[item]:
            signal = results_d[item][bssid]['signal']
            ap_name = ''
            if s_bssid_to_ap is None:
                s_bssid_to_ap = make_bssid_to_ap() # build it just once

            if bssid.upper() in s_bssid_to_ap:
                ap_name = s_bssid_to_ap[bssid]

            try:
                column = signal_thresholds[-1]
                for threshold_to_test in sorted(signal_thresholds):
                    if int(signal) >= threshold_to_test:
                        column = threshold_to_test

                if lines_of_data[item][column]:
                    lines_of_data[item][column] += '<br>'

                # test
                #ap_name = 'test:001'

                if ap_name:
                    # show ap name
                    ap_name_to_show = ap_name
                    if ':' in ap_name:
                        ap_name_to_show = ap_name.split(':')[1]
                    lines_of_data[item][column] += '<B>' + ap_name_to_show + results_d[item][bssid]['in_use'].replace(' ','&nbsp') + '</B>'
                    lines_of_data[item][column] += '<br>'

                # show channel number
                channel_str = '{:03d}'.format(int(results_d[item][bssid]['channel']))
                rate_str = rate_str_from_rate(results_d[item][bssid]['rate'])
                in_use_str = results_d[item][bssid]['in_use'].replace(' ','&nbsp')
                if int(results_d[item][bssid]['channel']) < 32: # 2.4 GHz band
                    if True:
                        # new is to show channel number and rate
                        lines_of_data[item][column] += '<i>' + channel_str + ' ' + rate_str + ' ' + in_use_str + '</i>'
                    else:
                        # old was to just show the channel number
                        lines_of_data[item][column] += '<i>' + channel_str + in_use_str + '</i>'
                else:
                    if True:
                        lines_of_data[item][column] += channel_str + ' ' + rate_str + ' ' + in_use_str
                    else:
                        lines_of_data[item][column] += '{:03d}'.format(int(results_d[item][bssid]['channel'])) + results_d[item][bssid]['in_use'].replace(' ','&nbsp')
            except:
                pass

    body = ''

    refresh_time = 1
#    if s_run_speed_tests:
#        refresh_time = 5

    body += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

    # the link home does not work... because it is a file that get tagged as 'accessed', not actually loaded?
#    body += '<B><a href="file:///cardinal/localhtml/index.html" style="text-decoration:none;color:inherit">Home</a></B>'

    # only works if you don't click on any of the internal links, and "open another window in the current context"
#    body += '<B><a href="JavaScript:window.close()" style="text-decoration:none;color:inherit">Close</a></B>'

#    body += '<B><a href="#" onclick="close_window();return false;">close</a></B>'

#    body += """<a href="blablabla" onclick="setTimeout(function(){var ww = window.open(window.location, '_self'); ww.close(); }, 1000);">If you click on this the window will be closed after 1000ms</a>"""


    body += '<br>'

    body += '<center>'
    body += '<table border="1" cellpadding="5">'
    body += '<tr>'
    body += '<td>'
    body += '<B>wlan0Tx</B>'
    body += '</td>'
    body += '<td>'
    body += get_wifi_tx_power(wlan0_info)
    body += '</td>'

    if s_is_engineering_device:
        for power_level in ['31.00', '20.00', '10.00', '5.00', '4.00', '3.00', '2.00', '1.00']:
            body += '<td>'
            power_string = power_level.replace('.','')
            path_string = make_future_path_string(split_path, 'wlan0power', power_string)
            body += ' ' + '<a href="?' + path_string + '">' + power_level + '</a>'
        body += '</td>'
    body += '</tr>'


    body += '<tr>'
    body += '<td>'
    body += '<B>force rescan on</B>'
    body += '</td>'
    body += '<td>'
    body += str(s_force_rescan)
    body += '</td>'

    if s_is_engineering_device:
        body += '<td>'
        if s_force_rescan:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'force_rescan', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'

        body += '<td>'
        if s_force_rescan:
            path_string = make_future_path_string(split_path, 'force_rescan', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'

    body += '<td>'
    body += str(s_forced_rescan_count)
    body += '</td>'

    body += '</tr>'



    is_hopper_enabled = (get_my_hopper_setting() == 'hop')

    body += '<tr>'
    body += '<td>'
    body += '<B>hopper on</B>'
    body += '</td>'
    body += '<td>'
    body += str(is_hopper_enabled)
    body += '</td>'

    if s_is_engineering_device:
        body += '<td>'
        if is_hopper_enabled:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'pop_to_top_signal_channel', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if is_hopper_enabled:
            path_string = make_future_path_string(split_path, 'pop_to_top_signal_channel', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'
    body += '<td>'
    body += '<B>Coaster</B>'
    body += '</td>'
    body += '<td>'
    body += str(get_my_coaster_setting())
    body += '</td>'

    body += '</tr>'



    if s_is_engineering_device:
        body += '<tr>'
        body += '<td>'
        body += '<B>config(ed) WiFi</B>'
        body += '</td>'
        body += '<td>'
#        body += str(s_allow_wifi_config_connection)
        body += '</td>'

        body += '<td>'

        _ = """
        if s_allow_wifi_config_connection:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'allow_ferg_connection', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if s_allow_wifi_config_connection:
            path_string = make_future_path_string(split_path, 'allow_ferg_connection', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        """

        body += '</td>'
        body += '</tr>'

    if True:
        body += '<tr>'
        body += '<td>'
        body += '<B>run speed test</B>'
        body += '</td>'
        body += '<td>'
        body += str(s_run_speed_tests)
        body += '</td>'

        body += '<td>'
        if s_run_speed_tests:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'speed_test', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if s_run_speed_tests:
            path_string = make_future_path_string(split_path, 'speed_test', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'
        body += '</tr>'

    body += '<td>'
    body += str(s_http_page_count)
    body += '</td>'
    body += '</tr>'
    body += '</table>'

    body += '<br>'

    if False:
        body += 'prev: ' + s_http_previous_path
        body += '<br>'
        body += 'path: ' + the_path
        body += '<br>'
        s_http_previous_path = the_path
    body += '</center>'


    show_options = []
    show_options.append({'title':'corp_and_iot', 'list':['corp','cah-iot']})
    show_options.append({'title':'corp', 'list':['corp']})
    show_options.append({'title':'iot', 'list':['cah-iot']})
    show_options.append({'title':'all', 'list':[]})

    current_show = show_options[0]['title'] # take the first one as the default
    if 'show=' in the_path:
        current_show = the_path.split('show=')[1].split(',')[0]

    body += '<center>'
    body += 'WiFi Scan results ' + wlan_active + ' for ' + '<B>' + current_show + '</B>' + ': '

    show_list = []
    for show_option in show_options:
        option = show_option['title']
        if current_show == option:
            show_list = show_option['list']
        if option == current_show:
            body += '....[' + '' + option + '' + ']'
        else:
            path_string = make_future_path_string(split_path, 'show', option)
            body += '....[' + '<a href="/?' + path_string + '">' + option + '</a>' + ']'

    body += '<br><br>'
#    body += 'show_list: ' + str(show_list)
#    body += '<br><br>'

    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td title="channels less than 32 are 2.4GHz (italics); above that is 5GHz; in bold indicates the access point name">'
    body += '<B>ssid</B>'
    body += '</td>'
    for signal in signal_thresholds:
        body += '<td>'
        body += '{:03d}'.format(signal)
        body += '</td>'

    body += '</tr>'

    for item in sorted(results_d):
        if show_list:
            use_it = item in show_list
        else:
            use_it = True # empty list means to allow all

        if use_it:
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            color = '(0,255,0,0.25)'

            for signal in signal_thresholds:
                text = '<br>'.join(sorted(lines_of_data[item][signal].split('<br>')))
                if not text:
                    text = '&nbsp' * 4

                if '*' in text:
                    body += '<td style="background-color:rgba' + color + '">'
                else:
                    body += '<td>'
                body += text
                body += '</td>'
            body += '</tr>'
    body += '</table>'
    body += '</center>'

    body += '<center>'
    body += '<br>'

    try:
        # this next line fails on unit test on macOS
        new_content = 'wlan0 ' + get_mac_address_for_port_name('wlan0') + '     wlan1 ' + get_mac_address_for_port_name('wlan1')
        body += new_content
    except:
        pass

    body += '<br>'

    try:
        body += open('/dev/shm/network_notes_wpa_exception.txt', 'r').read().replace('\n','<br>')
    except:
        #body += traceback.format_exc()
        body += '<br>' + '(good news, no wpa exceptions reported)'

    if s_is_engineering_device:
        try:
            body += open('/dev/shm/pi_network_manage_notes.txt', 'r').read().replace('\n','<br>')
        except:
            #body += traceback.format_exc()
            body += '<br>' + '(ok news, no pi_network_manage_notes reported)'

        try:
            body += '<br>' + 'pi_network_nic.txt:' + '<br>' + open('/dev/shm/pi_network_nic.txt', 'r').read().replace('\n','<br>')
        except:
            #body += traceback.format_exc()
            body += '<br>' + '(ok news, no pi_network_nic.txt reported)'

    body += '</center>'


    return body

# ----------------------------
def get_wifi_list():
# ----------------------------
    wifi_list, fail = do_one_command('sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi')
    return wifi_list

# ----------------------------
def build_and_start_manage_network_connections(dummy_arg):
# ----------------------------
    current_state = {}

    network_state_exception_file = '/dev/shm/pi_network_daemon2_exception'
    open(network_state_exception_file, 'w').write('starting...')

    network_state_trace_file = '/dev/shm/network_state.txt'
    open(network_state_trace_file, 'w').write('')

    time_service_action = time.time()

    while True:
        try:

            # ---------------------------
            # get the current condition of what the device "sees"
            # ---------------------------
            current_inputs = {}

            current_inputs['time_now'] = time.time()
            current_inputs['wifi_list'] = get_wifi_list()
            current_inputs['devices_report'], fails = do_one_command('nmcli -t')
            current_inputs['connection_list'], fails = do_one_command('nmcli -t c s')
            current_inputs['wifi_mapping'] = get_wifi_mapping(current_inputs['devices_report'])
            wireless_lan_internal_number = wireless_to_configure_from_wlanX(current_inputs['wifi_mapping']['internal_wifi'])
            current_inputs['wifi_mac_address_to_use'] = get_mac_address_for_port_name('wlan' + wireless_lan_internal_number)

            wifi_mac_address_external = ''
            if current_inputs['wifi_mapping']['external_wifi']:
                wireless_lan_external_number = wireless_to_configure_from_wlanX(current_inputs['wifi_mapping']['external_wifi'])
                wifi_mac_address_external = get_mac_address_for_port_name('wlan' + wireless_lan_external_number)
            current_inputs['wifi_mac_address_external'] = wifi_mac_address_external
            current_inputs['usbreset_content'], fails = do_one_command('usbreset')
            current_inputs['current_uptime'] = get_uptime()
            current_inputs['wifi_config_content'] = read_file_contents('/cardinal/wifi_config.txt')
            current_inputs['coaster'] = get_my_coaster_setting()
            current_inputs['hopper_setting'] = get_my_hopper_setting()
            current_inputs['s_is_engineering_device'] = s_is_engineering_device

#            print ("scan: " + str(current_uptime) + ' ' + str(int(float(time.time()))) + ' ' + str(current_state['s_pass_count']), connection_list, fails)

            # ---------------------------
            # process on this information, given our current state
            # ---------------------------
            new_state, shared_counts, actions_list, trace_back_string = manage_network_connections(copy.copy(current_state), current_inputs)

            # ---------------------------
            # do whatever it says, to drive the system to the new intended state
            # ---------------------------
            did_change_content = do_atomic_write_if_different('/etc/NetworkManager/NetworkManager.conf', new_state['NetworkManager.conf_content'])
            if did_change_content:
                restart_network_manager()

            action_results = []
            for action in actions_list:
                if 'dwell_seconds' in action:
                    seconds = None
                    try:
                        seconds = float(action['dwell_seconds'])
                    except:
                        pass

                    if seconds:
                        time.sleep(seconds)
                        action_results.append({'dwell_seconds':seconds})

                if 'restart_network_manager' in action:
                    counts = restart_network_manager()
                    action_results.append({'restart_network_manager':counts})

                if 'do_one_command' in action:
                    the_command = action['do_one_command']
                    if 'substitute' in action:
                        the_command = the_command.replace(action['substitute']['match'], open(action['substitute']['read_from_file'],'r').read())

                    for cmdline in the_command.split('&&'):
                        result, fails = do_one_command(cmdline)
                        action_results.append({'cmdline':cmdline, 'result':result, 'fails':fails})

            if shared_counts:
                save_shared_counts(shared_counts)


            # old style globals to be updated
            save_shared_counts({'ext_radio_resets':new_state['s_usb_reset_radio_count']})
            do_atomic_write_if_different(s_corp_wifi_cert_last_used_path + 's_corp_wifi_cert_in_use', '')

            # ---------------------------
            # write out whatever we want to about it
            # ---------------------------
            do_atomic_write_if_different('/dev/shm/pi_network_wifi_list', current_inputs['wifi_list'])
            if trace_back_string:
                do_atomic_write_if_different('/dev/shm/network_notes_wpa_exception.txt', trace_back_string)
                write_lan_detail(trace_back_string)

            # ---------------------------
            # for debugging, write current state, and the action list to a review place
            # ---------------------------
            try:
                # only write the ones that result is us doing actions
                if actions_list:
                    network_trace = {}
                    network_trace['time'] = time.time()
                    network_trace['time_since_last_action'] = network_trace['time'] - time_service_action
                    time_service_action = network_trace['time']
                    network_trace['current_state'] = current_state
                    network_trace['current_inputs'] = current_inputs
                    network_trace['new_state'] = new_state
                    network_trace['actions_list'] = actions_list
                    network_trace['trace_back_string'] = trace_back_string
                    network_trace['action_results'] = action_results

                    formatted_trace = ''
                    formatted_trace += json.dumps(network_trace['time']) + '\n'
                    formatted_trace += json.dumps(network_trace['current_state']) + '\n'
                    formatted_trace += json.dumps(network_trace['new_state']) + '\n'
                    formatted_trace += json.dumps(network_trace['actions_list']) + '\n'
                    formatted_trace += json.dumps(network_trace['trace_back_string']) + '\n'

                    write_debug_string(formatted_trace)

                    try:
                        network_state_trace = open(network_state_trace_file, 'r').read().split('\n')
                    except:
                        network_state_trace = []

                    while len(network_state_trace) > 100:
                        network_state_trace.pop(0)

                    network_state_trace.append(json.dumps(network_trace))

                    open(network_state_trace_file, 'w').write('\n'.join(network_state_trace))
            except:
                open(network_state_trace_file, 'w').write(traceback.format_exc())


            # move the state over
            current_state = copy.copy(new_state)

        except:
            open(network_state_exception_file, 'w').write(traceback.format_exc())

        time_of_start_of_scan = time.time()
        while (abs(time_of_start_of_scan - time.time()) < s_seconds_per_manage_pass):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)

# ----------------------------
def build_and_start_web_server(port_number):
# ----------------------------
    # be a web server
    # https://stackoverflow.com/questions/23264569/python-3-x-basehttpserver-or-http-server

    class MyServer(BaseHTTPRequestHandler):
        def do_GET(self):
            global s_force_rescan, s_run_speed_tests, s_pop_to_top_signal_channel

            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            response = "<html><head><title>" + ' ' + service + "</title></head><body>"

            if self.path == '/favicon.ico':
                pass
            else:
                try:
                    wifi_list = open('/dev/shm/pi_network_wifi_list', 'r').read()
                except:
                    wifi_list = ''
                results_d = get_wifi_results(wifi_list)
                wlan0_info = get_wifi_info('wlan0')

                path_splits = self.path.replace('/','').replace('?','').split(',')
                #self.wfile.write(bytes(str(path_splits) + '<br>', "utf-8"))
                for item in path_splits:
                    if 'force_rescan=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_force_rescan = False
                            if new_value == 'true':
                                s_force_rescan = True

                    if 'speed_test=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_run_speed_tests = False
                            if new_value == 'true':
                                s_run_speed_tests = True

                    if 'pop_to_top_signal_channel=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_pop_to_top_signal_channel = False
                            if new_value == 'true':
                                s_pop_to_top_signal_channel = True

                    if 'wlan0power=' in item:
                        try:
                            old_power = get_wifi_tx_power(wlan0_info).replace('.','').split()[0]
                            new_power = item.split('=')[1]
                            if old_power != new_power:
                                set_wlan_tx_power('wlan0', new_power)
                                #self.wfile.write(bytes(old_power + ', ' + new_power, "utf-8"))
                                wlan0_info = get_wifi_info('wlan0')
                        except:
                            pass

                wlan_active = 'wlan0'
                devices_report, fails = do_one_command('nmcli -t')
                if 'wlan1' in devices_report:
                    wlan_active = 'wlan1'


                response += build_get_page_body(self.path, results_d, wlan0_info,wlan_active)
                self.wfile.write(bytes(response, "utf-8"))
                response = ''

                if s_run_speed_tests:
                    self.wfile.write(bytes("<br><center>running speed tests...</center>", "utf-8"))

                    content, fails = do_one_command('ping slicer.cardinalhealth.net -c 1')
                    self.wfile.write(bytes("<center> ping= " + get_ping_from_result(content) + "</center>", "utf-8"))

                    if False:
                        content, fails = do_one_command('wget slicer.cardinalhealth.net/speedtest?size=10000000 --timeout 25.0 -O testfile1')
                        download_speed = get_speed_from_download_result(fails)
                        self.wfile.write(bytes("<center> download= " + download_speed + "</center>", "utf-8"))

                        content, fails = do_one_command('curl -v -F filename=testfile --max-time 25 -F upload=@testfile1 https://slicer.cardinalhealth.net/speedtest')
                        upload_speed = get_speed_from_upload_result(content)
                        self.wfile.write(bytes("<center> upload= " + upload_speed + "</center>", "utf-8"))

            response += "</body></html>"

            self.wfile.write(bytes(response, "utf-8"))

    myServer = HTTPServer(("localhost", port_number), MyServer)

    try:
        myServer.serve_forever()
    except KeyboardInterrupt:
        pass

    myServer.server_close()

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """
    global s_nic_tracking

    try:
        network_starts_count = 1 + int(get_saved_value('network_starts_count'))
    except:
        network_starts_count = 1

    set_saved_value('network_starts_count', network_starts_count)

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_network.cpython-37.pyc", "/cardinal/pi_network.pyc")

    print ('version=' + version)

    if os.path.isfile("/cardinal/pi_network.py"):
        os.remove("/cardinal/pi_network.py")

    do_one_time()

    daemon = True

    time_of_last_send = 0

    try:
        with open('/dev/shm/pi_network_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    last_channel = ''
    last_lan = ''
    serial = get_serial()

    daemon = threading.Thread(name='daemon_server',
                          target=build_and_start_web_server,
                          args=[7040])
    daemon.setDaemon(True) # Set as a daemon so it will be killed once the main thread is dead.
    daemon.start()

    daemon2 = threading.Thread(name='daemon_manage_network_connections',
                          target=build_and_start_manage_network_connections,
                          args=['1234'])
    daemon2.setDaemon(True) # Set as a daemon so it will be killed once the main thread is dead.
    daemon2.start()

    while True:
        time_of_start_of_scan = time.time()
        need_to_send = False

        if True:
            # this performs the side effect of forcing an actual rescan of the ssid/channel choice.
            results = get_wifi_scan()

            nic_save_file = '/dev/shm/pi_network_nic.txt'

            if not 'nic' in s_nic_tracking:
                s_nic_tracking['nic'] = results['nic']
                saved_value = get_saved_value('nic_change_count')
                if saved_value:
                    try:
                        s_nic_tracking['changed_counts'] = int(saved_value)
                    except:
                        s_nic_tracking['changed_counts'] = 0
                else:
                    s_nic_tracking['changed_counts'] = 0

                do_atomic_write_if_different(nic_save_file, json.dumps(s_nic_tracking) + '\nnetwork_starts_count=' + str(network_starts_count))
            else:
                before_count = s_nic_tracking['changed_counts']

                if 'nic' in results:
                    is_changed = is_nic_changed(s_nic_tracking['nic'], results['nic'])

                    if is_changed:
                        s_nic_tracking['changed_counts'] += 1
                        set_saved_value('nic_change_count', s_nic_tracking['changed_counts'])
                        s_nic_tracking['nic'] = results['nic']

                    after_count = s_nic_tracking['changed_counts']

                    do_atomic_write_if_different(nic_save_file, results['nic'] + '\n' +
                        s_nic_tracking['nic'] + '\n' +
                        str(before_count) + '\n' +
                        str(after_count) + '\n' +
                        str(is_nic_changed(s_nic_tracking['nic'], results['nic'])) + '\n' +
                        '\nnetwork_starts_count=' + str(network_starts_count))


            the_counts = {}
            the_counts['nic'] = results['nic']
            the_counts['nic_change_count'] = s_nic_tracking['changed_counts']
            save_shared_counts(the_counts)

            # write to local store, for others to use (hmi)
            do_atomic_write_if_different('/dev/shm/pi_network_scan.txt', json.dumps(results))

            time_now = time.time()
            if abs(time_now - time_of_last_send) > 60 * 30: # 1800 seconds
                need_to_send = True

            if abs(time_now - time_of_last_send) > 60:
                if 'chan' in results:
                    if last_channel != results['chan']:
                        last_channel = results['chan']
                        need_to_send = True
                if 'lan' in results:
                    if last_lan != results['lan']:
                        last_lan = results['lan']
                        need_to_send = True

            the_data = []
            the_data.append('source=' + service)
            the_data.append('serial=' + serial)
            the_data.append('version=' + version)

            for key in results:
                the_data.append(key + "=" + str(results[key]))

            for call_home_location in call_home_locations():
                the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

                if need_to_send:
                    time_of_last_send = time_now


                    # check in with slicer
                    try:
                        r = requests.get(the_report_url, verify=False, timeout=15.0)
                        url_result = r.text

                    except:
                        url_result = 'exception'


                    # url_result = '{"bookmarks":{"10000000e3669edf":{"1": {"title": "EDHR", "url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login","whitelist":["edhr-na-jz.cardinalhealth.net"]}}}}'
                    try:
                        result_json = json.loads(url_result)

                    except:
                        print ("url_result was not a valid json")

                # save locally, for testing
                try:
                    with open('/dev/shm/pi_network_datadrop.txt', 'w') as f:
                        f.write(the_report_url)
                        f.write('\n\n' + str(url_result))
                    print (the_report_url)
                except:
                    print ("!!! failed to write datadrop string")

                    _ = """
look with:
watch cat /dev/shm/pi_network_datadrop.txt
                    """

        if not (daemon and daemon2):
            break # This exits the While True, which ends the main loop process, which gets noticed, and the service is restarted by the OS.

        while (abs(time_of_start_of_scan - time.time()) < 4):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_wifi_list_parsing_simple(self):
        """
        (fill in here)
        """
        self.maxDiff = None

        wifi_list = ''
        self.assertEqual(get_wifi_results(wifi_list), {})

        # sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi
        wifi_list = """cah-iot:34\:FC\:B9\:71\:0A\:D0: :52:5260 MHz:55:54 Mbit/s"""

        expected_result = {
                              'cah-iot': {
                                '34:FC:B9:71:0A:D0': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  },
                          }

        self.assertEqual(get_wifi_results(wifi_list), expected_result)

    def test_wifi_list_parsing(self):
        """
        (fill in here)
        """
        self.maxDiff = None

        wifi_list = ''
        self.assertEqual(get_wifi_results(wifi_list), {})

        # sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi
        wifi_list = """cah-iot:34\:FC\:B9\:71\:0A\:D0: :52:5260 MHz:55:54 Mbit/s
byod:34\:FC\:B9\:71\:0A\:D2: :52:5260 MHz:55:54 Mbit/s
guest:34\:FC\:B9\:71\:0A\:D3: :52:5260 MHz:55:54 Mbit/s
corp:34\:FC\:B9\:71\:12\:31: :36:5180 MHz:52:540 Mbit/s
cah-iot:34\:FC\:B9\:71\:12\:30: :36:5180 MHz:52:54 Mbit/s
byod:34\:FC\:B9\:71\:12\:32: :36:5180 MHz:52:54 Mbit/s
guest:34\:FC\:B9\:71\:12\:33: :36:5180 MHz:52:54 Mbit/s
corp:34\:FC\:B9\:71\:0A\:D1:*:52:5260 MHz:52:540 Mbit/s
corp:34\:FC\:B9\:71\:0A\:D1: :52:5260 MHz:52:540 Mbit/s
corp:34\:FC\:B9\:71\:3C\:B1: :60:5300 MHz:30:540 Mbit/s
cah-iot:34\:FC\:B9\:71\:3C\:B0: :60:5300 MHz:30:54 Mbit/s
byod:34\:FC\:B9\:71\:3C\:B2: :60:5300 MHz:30:54 Mbit/s
guest:34\:FC\:B9\:71\:3C\:B3: :60:5300 MHz:29:54 Mbit/s
corp:34\:FC\:B9\:71\:03\:D1: :132:5660 MHz:25:540 Mbit/s
Hotspot10EE:22\:AD\:56\:24\:10\:EE: :6:2437 MHz:24:65 Mbit/s
corp:34\:FC\:B9\:71\:13\:51: :44:5220 MHz:24:540 Mbit/s
guest:34\:FC\:B9\:71\:13\:53: :44:5220 MHz:24:54 Mbit/s
cah-iot:34\:FC\:B9\:71\:13\:50: :44:5220 MHz:24:54 Mbit/s
byod:34\:FC\:B9\:71\:13\:52: :44:5220 MHz:24:54 Mbit/s
byod:34\:FC\:B9\:71\:03\:D2: :132:5660 MHz:24:54 Mbit/s
guest:34\:FC\:B9\:71\:03\:D3: :132:5660 MHz:24:54 Mbit/s
cah-iot:34\:FC\:B9\:71\:03\:D0: :132:5660 MHz:24:54 Mbit/s
"""

        expected_result = {
                              'cah-iot': {
                                '34:FC:B9:71:0A:D0': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:12:30': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:3C:B0': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:13:50': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:03:D0': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'byod': {
                                  '34:FC:B9:71:0A:D2': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:32': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B2': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:52': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D2': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'guest': {
                                  '34:FC:B9:71:0A:D3': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:33': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B3': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '29', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:53': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D3': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'corp': {
                                  '34:FC:B9:71:12:31': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:0A:D1': {'in_use': '*', 'channel': '52', 'freq': '5260 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:3C:B1': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:03:D1': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '25', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:13:51': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '540 Mbit/s'}
                                  },
                              'Hotspot10EE': {
                                  '22:AD:56:24:10:EE': {'in_use': ' ', 'channel': '6', 'freq': '2437 MHz', 'signal': '24', 'rate': '65 Mbit/s'}
                                  }
                          }

        self.assertEqual(get_wifi_results(wifi_list), expected_result)

        expected_result2 = [(' ','36','52'),('*','52','52'),(' ','60','30'),(' ','132','25'),(' ','44','24')]

        #self.assertEqual(get_access_points_for_ssid(expected_result, 'corp'), expected_result2)

    def test_wifi_list_parsing_2(self):
        self.maxDiff = None

        # daves house, with some blind ssid
        wifi_list = """:2A\:D0\:F5\:D2\:83\:A7: :56:5280 MHz:100:540 Mbit/s
"""
        expected_result = {
    '(hidden)': {
                 '2A:D0:F5:D2:83:A7': {'channel': '56',
                                'freq': '5280 MHz',
                                'in_use': ' ',
                                'rate': '540 Mbit/s',
                                'signal': '100'}}}


        self.assertEqual(get_wifi_results(wifi_list), expected_result)

    def test_wifi_list_parsing_3(self):
        self.maxDiff = None

        # daves house, with some hidden ssid
        wifi_list = """ferg:28\:D0\:F5\:A2\:83\:A8: :7:2442 MHz:100:540 Mbit/s
:2A\:D0\:F5\:D2\:83\:A8: :7:2442 MHz:100:540 Mbit/s
ferg_5G:2A\:D0\:F5\:92\:83\:A7: :56:5280 MHz:100:540 Mbit/s
:2A\:D0\:F5\:D2\:83\:A7: :56:5280 MHz:100:540 Mbit/s
ferg:34\:98\:B5\:00\:22\:FC: :7:2442 MHz:70:270 Mbit/s
ferg:34\:98\:B5\:00\:22\:FD: :36:5180 MHz:49:270 Mbit/s
"""
        expected_result = {
    '(hidden)': {
                 '2A:D0:F5:D2:83:A7': {'channel': '56',
                                'freq': '5280 MHz',
                                'in_use': ' ',
                                'rate': '540 Mbit/s',
                                'signal': '100'},
                '2A:D0:F5:D2:83:A8': {'channel': '7',
                                'freq': '2442 MHz',
                                'in_use': ' ',
                                'rate': '540 Mbit/s',
                                'signal': '100'},
            },
    'ferg': {'28:D0:F5:A2:83:A8': {'channel': '7',
                                'freq': '2442 MHz',
                                'in_use': ' ',
                                'rate': '540 Mbit/s',
                                'signal': '100'},
          '34:98:B5:00:22:FC': {'channel': '7',
                                'freq': '2442 MHz',
                                'in_use': ' ',
                                'rate': '270 Mbit/s',
                                'signal': '70'},
          '34:98:B5:00:22:FD': {'channel': '36',
                                'freq': '5180 MHz',
                                'in_use': ' ',
                                'rate': '270 Mbit/s',
                                'signal': '49'}},
    'ferg_5G': {'2A:D0:F5:92:83:A7': {'channel': '56',
                                   'freq': '5280 MHz',
                                   'in_use': ' ',
                                   'rate': '540 Mbit/s',
                                   'signal': '100'}}}


        self.assertEqual(get_wifi_results(wifi_list), expected_result)

    def test_make_content(self):
        results_d = {
                              'cah-iot': {
                                '34:FC:B9:71:0A:D0': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:12:30': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:3C:B0': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:13:50': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:03:D0': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'byod': {
                                  '34:FC:B9:71:0A:D2': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:32': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B2': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:52': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D2': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'guest': {
                                  '34:FC:B9:71:0A:D3': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:33': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B3': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '29', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:53': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D3': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'corp': {
                                  '34:FC:B9:71:12:31': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:0A:D1': {'in_use': '*', 'channel': '52', 'freq': '5260 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:3C:B1': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:03:D1': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '25', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:13:51': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '540 Mbit/s'}
                                  },
                              'Hotspot10EE': {
                                  '22:AD:56:24:10:EE': {'in_use': ' ', 'channel': '6', 'freq': '2437 MHz', 'signal': '24', 'rate': '65 Mbit/s'}
                                  }
                          }
        build_get_page_body('/',results_d)


    def test_get_tx_power_from_info(self):

        content = """
Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 31.00 dBm
"""
        expected_result = '31.00 dBm'
        self.assertEqual(get_wifi_tx_power(content), expected_result)

        content = """
Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 20.00 dBm
"""
        expected_result = '20.00 dBm'
        self.assertEqual(get_wifi_tx_power(content), expected_result)

    def test_get_items_in_parensthesis(self):
        input_string = '1234'
        expected_result = []
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234'
        expected_result = []
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234)'
        expected_result = ['1234']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234)(456)'
        expected_result = ['1234', '456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234) junk here (456)'
        expected_result = ['1234', '456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '1234) junk here (456)'
        expected_result = ['456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '1234) junk here (456) junk after'
        expected_result = ['456']
        self.assertEqual(get_parens_items(input_string), expected_result)


    def test_get_download_speed_from_fail_string(self):
        fail_string = '--2022-04-01 12:10:06--  http://slicer.cardinalhealth.net/speedtest?size=10000000\nResolving slicer.cardinalhealth.net (slicer.cardinalhealth.net)... 10.50.40.47\nConnecting to slicer.cardinalhealth.net (slicer.cardinalhealth.net)|10.50.40.47|:80... connected.\nHTTP request sent, awaiting response... 301 Moved Permanently\nLocation: https://slicer.cardinalhealth.net/speedtest?size=10000000 [following]\n--2022-04-01 12:10:06--  https://slicer.cardinalhealth.net/speedtest?size=10000000\nConnecting to slicer.cardinalhealth.net (slicer.cardinalhealth.net)|10.50.40.47|:443... connected.\nHTTP request sent, awaiting response... 200 OK\nLength: unspecified [text/html]\nSaving to: testfile1\n\n     0K .......... .......... .......... .......... ..........  526K\n    50K .......... .......... .......... .......... ..........  957K\n   100K .......... .......... .......... .......... .......... 1019K\n   150K .......... .......... .......... .......... .......... 8.70M\n   200K .......... .......... .......... .......... .......... 1024K\n   250K .......... .......... .......... .......... .......... 1.16M\n   300K .......... .......... .......... .......... .......... 2.91M\n   350K .......... .......... .......... .......... .......... 1.42M\n   400K .......... .......... .......... .......... .......... 1.08M\n   450K .......... .......... .......... .......... .......... 1.48M\n   500K .......... .......... .......... .......... .......... 3.54M\n   550K .......... .......... .......... .......... .......... 1.03M\n   600K .......... .......... .......... .......... .......... 5.71M\n   650K .......... .......... .......... .......... .......... 1.23M\n   700K .......... .......... .......... .......... .......... 1.22M\n   750K .......... .......... .......... .......... .......... 3.29M\n   800K .......... .......... .......... .......... .......... 1.17M\n   850K .......... .......... .......... .......... .......... 1.60M\n   900K .......... .......... .......... .......... .......... 2.12M\n   950K .......... .......... .......... .......... .......... 1.71M\n  1000K .......... .......... .......... .......... .......... 2.55M\n  1050K .......... .......... .......... .......... .......... 1.58M\n  1100K .......... .......... .......... .......... .......... 3.55M\n  1150K .......... .......... .......... .......... .......... 1.48M\n  1200K .......... .......... .......... .......... .......... 1.17M\n  1250K .......... .......... .......... .......... .......... 2.59M\n  1300K .......... .......... .......... .......... .......... 1.81M\n  1350K .......... .......... .......... .......... .......... 2.15M\n  1400K .......... .......... .......... .......... .......... 2.82M\n  1450K .......... .......... .......... .......... .......... 1.71M\n  1500K .......... .......... .......... .......... .......... 4.41M\n  1550K .......... .......... .......... .......... .......... 2.37M\n  1600K .......... .......... .......... .......... .......... 1.34M\n  1650K .......... .......... .......... .......... .......... 1.42M\n  1700K .......... .......... .......... .......... .......... 6.84M\n  1750K .......... .......... .......... .......... .......... 2.90M\n  1800K .......... .......... .......... .......... .......... 1.71M\n  1850K .......... .......... .......... .......... .......... 2.65M\n  1900K .......... .......... .......... .......... .......... 1.95M\n  1950K .......... .......... .......... .......... .......... 3.41M\n  2000K .......... .......... .......... .......... .......... 1.53M\n  2050K .......... .......... .......... .......... .......... 4.86M\n  2100K .......... .......... .......... .......... .......... 2.49M\n  2150K .......... .......... .......... .......... .......... 2.00M\n  2200K .......... .......... .......... .......... .......... 3.48M\n  2250K .......... .......... .......... .......... .......... 1.75M\n  2300K .......... .......... .......... .......... .......... 4.79M\n  2350K .......... .......... .......... .......... .......... 3.31M\n  2400K .......... .......... .......... .......... .......... 1.45M\n  2450K .......... .......... .......... .......... .......... 6.95M\n  2500K .......... .......... .......... .......... .......... 1.50M\n  2550K .......... .......... .......... .......... .......... 3.80M\n  2600K .......... .......... .......... .......... .......... 5.05M\n  2650K .......... .......... .......... .......... .......... 1.53M\n  2700K .......... .......... .......... .......... .......... 5.52M\n  2750K .......... .......... .......... .......... .......... 5.44M\n  2800K .......... .......... .......... .......... .......... 1.46M\n  2850K .......... .......... .......... .......... .......... 7.36M\n  2900K .......... .......... .......... .......... .......... 9.96M\n  2950K .......... .......... .......... .......... .......... 1.52M\n  3000K .......... .......... .......... .......... .......... 8.93M\n  3050K .......... .......... .......... .......... .......... 4.69M\n  3100K .......... .......... .......... .......... .......... 1.64M\n  3150K .......... .......... .......... .......... .......... 5.27M\n  3200K .......... .......... .......... .......... .......... 1.34M\n  3250K .......... .......... .......... .......... .......... 10.2M\n  3300K .......... .......... .......... .......... .......... 6.12M\n  3350K .......... .......... .......... .......... .......... 5.35M\n  3400K .......... .......... .......... .......... .......... 1.71M\n  3450K .......... .......... .......... .......... .......... 5.51M\n  3500K .......... .......... .......... .......... .......... 5.52M\n  3550K .......... .......... .......... .......... .......... 1.78M\n  3600K .......... .......... .......... .......... .......... 4.41M\n  3650K .......... .......... .......... .......... .......... 5.44M\n  3700K .......... .......... .......... .......... .......... 1.54M\n  3750K .......... .......... .......... .......... .......... 10.2M\n  3800K .......... .......... .......... .......... .......... 4.90M\n  3850K .......... .......... .......... .......... .......... 2.20M\n  3900K .......... .......... .......... .......... .......... 3.63M\n  3950K .......... .......... .......... .......... .......... 4.52M\n  4000K .......... .......... .......... .......... .......... 3.00M\n  4050K .......... .......... .......... .......... .......... 2.46M\n  4100K .......... .......... .......... .......... .......... 6.27M\n  4150K .......... .......... .......... .......... .......... 6.57M\n  4200K .......... .......... .......... .......... .......... 2.36M\n  4250K .......... .......... .......... .......... .......... 3.18M\n  4300K .......... .......... .......... .......... .......... 8.98M\n  4350K .......... .......... .......... .......... .......... 4.70M\n  4400K .......... .......... .......... .......... .......... 1.70M\n  4450K .......... .......... .......... .......... .......... 10.1M\n  4500K .......... .......... .......... .......... .......... 3.90M\n  4550K .......... .......... .......... .......... .......... 4.04M\n  4600K .......... .......... .......... .......... .......... 2.12M\n  4650K .......... .......... .......... .......... .......... 9.73M\n  4700K .......... .......... .......... .......... .......... 7.05M\n  4750K .......... .......... .......... .......... .......... 2.79M\n  4800K .......... .......... .......... .......... .......... 2.54M\n  4850K .......... .......... .......... .......... .......... 10.1M\n  4900K .......... .......... .......... .......... .......... 2.16M\n  4950K .......... .......... .......... .......... .......... 9.88M\n  5000K .......... .......... .......... .......... .......... 3.60M\n  5050K .......... .......... .......... .......... .......... 10.2M\n  5100K .......... .......... .......... .......... .......... 5.04M\n  5150K .......... .......... .......... .......... .......... 2.12M\n  5200K .......... .......... .......... .......... .......... 6.89M\n  5250K .......... .......... .......... .......... .......... 7.58M\n  5300K .......... .......... .......... .......... .......... 7.06M\n  5350K .......... .......... .......... .......... .......... 2.01M\n  5400K .......... .......... .......... .......... .......... 4.66M\n  5450K .......... .......... .......... .......... .......... 5.63M\n  5500K .......... .......... .......... .......... .......... 6.15M\n  5550K .......... .......... .......... .......... .......... 2.41M\n  5600K .......... .......... .......... .......... .......... 2.85M\n  5650K .......... .......... .......... .......... .......... 5.58M\n  5700K .......... .......... .......... .......... .......... 3.99M\n  5750K .......... .......... .......... .......... .......... 5.72M\n  5800K .......... .......... .......... .......... .......... 5.09M\n  5850K .......... .......... .......... .......... .......... 5.62M\n  5900K .......... .......... .......... .......... .......... 5.16M\n  5950K .......... .......... .......... .......... .......... 9.74M\n  6000K .......... .......... .......... .......... .......... 2.46M\n  6050K .......... .......... .......... .......... .......... 5.58M\n  6100K .......... .......... .......... .......... .......... 3.58M\n  6150K .......... .......... .......... .......... .......... 8.75M\n  6200K .......... .......... .......... .......... .......... 9.51M\n  6250K .......... .......... .......... .......... .......... 2.98M\n  6300K .......... .......... .......... .......... .......... 7.90M\n  6350K .......... .......... .......... .......... .......... 3.99M\n  6400K .......... .......... .......... .......... .......... 7.81M\n  6450K .......... .......... .......... .......... .......... 10.1M\n  6500K .......... .......... .......... .......... .......... 2.81M\n  6550K .......... .......... .......... .......... .......... 10.3M\n  6600K .......... .......... .......... .......... .......... 4.27M\n  6650K .......... .......... .......... .......... .......... 10.1M\n  6700K .......... .......... .......... .......... .......... 10.4M\n  6750K .......... .......... .......... .......... .......... 2.26M\n  6800K .......... .......... .......... .......... .......... 7.50M\n  6850K .......... .......... .......... .......... .......... 6.67M\n  6900K .......... .......... .......... .......... .......... 5.80M\n  6950K .......... .......... .......... .......... .......... 10.2M\n  7000K .......... .......... .......... .......... .......... 2.00M\n  7050K .......... .......... .......... .......... .......... 5.36M\n  7100K .......... .......... .......... .......... .......... 10.2M\n  7150K .......... .......... .......... .......... .......... 9.14M\n  7200K .......... .......... .......... .......... .......... 3.02M\n  7250K .......... .......... .......... .......... .......... 4.36M\n  7300K .......... .......... .......... .......... .......... 5.27M\n  7350K .......... .......... .......... .......... .......... 10.2M\n  7400K .......... .......... .......... .......... .......... 9.40M\n  7450K .......... .......... .......... .......... .......... 1.99M\n  7500K .......... .......... .......... .......... .......... 3.79M\n  7550K .......... .......... .......... .......... .......... 7.76M\n  7600K .......... .......... .......... .......... .......... 8.25M\n  7650K .......... .......... .......... .......... .......... 10.4M\n  7700K .......... .......... .......... .......... .......... 2.99M\n  7750K .......... .......... .......... .......... .......... 6.74M\n  7800K .......... .......... .......... .......... .......... 4.95M\n  7850K .......... .......... .......... .......... .......... 10.2M\n  7900K .......... .......... .......... .......... .......... 10.1M\n  7950K .......... .......... .......... .......... .......... 10.4M\n  8000K .......... .......... .......... .......... .......... 2.14M\n  8050K .......... .......... .......... .......... .......... 8.35M\n  8100K .......... .......... .......... .......... .......... 7.89M\n  8150K .......... .......... .......... .......... .......... 10.2M\n  8200K .......... .......... .......... .......... .......... 9.33M\n  8250K .......... .......... .......... .......... .......... 10.4M\n  8300K .......... .......... .......... .......... .......... 2.95M\n  8350K .......... .......... .......... .......... .......... 5.30M\n  8400K .......... .......... .......... .......... .......... 4.90M\n  8450K .......... .......... .......... .......... .......... 10.1M\n  8500K .......... .......... .......... .......... .......... 10.2M\n  8550K .......... .......... .......... .......... .......... 10.4M\n  8600K .......... .......... .......... .......... .......... 3.32M\n  8650K .......... .......... .......... .......... .......... 4.00M\n  8700K .......... .......... .......... .......... .......... 5.33M\n  8750K .......... .......... .......... .......... .......... 10.3M\n  8800K .......... .......... .......... .......... .......... 8.40M\n  8850K .......... .......... .......... .......... .......... 10.4M\n  8900K .......... .......... .......... .......... .......... 5.34M\n  8950K .......... .......... .......... .......... .......... 4.80M\n  9000K .......... .......... .......... .......... .......... 4.02M\n  9050K .......... .......... .......... .......... .......... 9.66M\n  9100K .......... .......... .......... .......... .......... 2.88M\n  9150K .......... .......... .......... .......... .......... 10.3M\n  9200K .......... .......... .......... .......... .......... 8.41M\n  9250K .......... .......... .......... .......... .......... 10.2M\n  9300K .......... .......... .......... .......... .......... 10.4M\n  9350K .......... .......... .......... .......... .......... 4.06M\n  9400K .......... .......... .......... .......... .......... 3.34M\n  9450K .......... .......... .......... .......... .......... 11.4M\n  9500K .......... .......... .......... .......... .......... 18.8M\n  9550K .......... .......... .......... .......... .......... 19.0M\n  9600K .......... .......... .......... .......... .......... 3.42M\n  9650K .......... .......... .......... .......... .......... 3.81M\n  9700K .......... .......... .......... .......... .......... 3.65M\n  9750K .......... .....                                       16.2M=3.0s\n\n2022-04-01 12:10:17 (3.15 MB/s) - testfile1 saved [10000030]\n\n'
        expected_result = '3.15 MB/s'
        self.assertEqual(get_speed_from_download_result(fail_string), expected_result)

        fail_string = ''
        expected_result = '(no download speed)'
        self.assertEqual(get_speed_from_download_result(fail_string), expected_result)

    def test_get_upload_speed_from_pass_string(self):
        pass_string = '<html>\n<body>\nsize of file uploaded is 10000030 in time of 9.46198511124 seconds: which gives rate of 1056863 Bps or 1056.863 KBps or 1.056863 MBps</body>\n</html>\n'
        expected_result = '1.056863 MB/s'
        self.assertEqual(get_speed_from_upload_result(pass_string), expected_result)

    def test_ping_value_from_pass_string(self):
        pass_string = 'PING slicer.cardinalhealth.net (10.50.40.47) 56(84) bytes of data.\n64 bytes from slicer.cardinalhealth.net (10.50.40.47): icmp_seq=1 ttl=57 time=27.4 ms\n\n--- slicer.cardinalhealth.net ping statistics ---\n1 packets transmitted, 1 received, 0% packet loss, time 0ms\nrtt min/avg/max/mdev = 27.437/27.437/27.437/0.000 ms\n'
        expected_result = '27.4 ms'
        self.assertEqual(get_ping_from_result(pass_string), expected_result)

    def test_make_bssid_to_ap(self):
        bssid_to_ap = make_bssid_to_ap()
        self.assertEqual(bssid_to_ap['E8:26:89:8F:D0:52'], 'PR005:028')
        self.assertEqual(bssid_to_ap['20:A6:CD:D4:E6:52'], 'PR010:022')

    def test_wifi_config_from_content(self):
        content = ""
        expected = {}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)

        content = "test123"
        expected = {'ssid':'test123'}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)

        content = "test123\n"
        expected = {'ssid':'test123'}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)

        content = "test123,psk5678"
        expected = {'ssid':'test123', 'psk':'psk5678'}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)


    def test_get_wifi_mapping_internal_wlan0(self):
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan1: connected to corp1
	"Ralink RT5572"
	wifi (rt2800usb), 9C:EF:D5:FA:B4:0A, hw, mtu 1500
	ip4 default
	inet4 ************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::cf8f:142f:f3d8:3e04/64
	route6 fe80::/64

wlan0: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan1

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan0', 'external_wifi':'wlan1'}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)

    def test_get_wifi_mapping_internal_wlan0_only(self):
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan0: connected to corp1
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500
	ip4 default
	inet4 **************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::3f53:64e5:8637:974d/64
	route6 fe80::/64

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan0

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan0', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)


    def test_get_wifi_mapping_internal_wlan1_only(self):
        # cah-rp-10000000b7578519
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """eth0: connected to eth0
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500
	inet4 10.211.6.199/24
	route4 10.211.6.0/24
	route4 0.0.0.0/0
	inet6 fe80::a12a:3000:9219:7b2d/64
	route6 fe80::/64

wlan1: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan1', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)

    def test_get_wifi_mapping_internal_wlan1(self):
        # cah-rp-10000000b7578519
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan1: connected to corp1
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500
	ip4 default
	inet4 **************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::3f53:64e5:8637:974d/64
	route6 fe80::/64

wlan0: disconnected
	"Ralink RT5572"
	wifi (rt2800usb), 9C:EF:D5:FA:B4:0A, hw, mtu 1500

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan1

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan1', 'external_wifi':'wlan0'}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)



    def test_get_wifi_mapping_internal_wlan0_on_wire(self):
        devices_report = """eth0: connected to eth0
	"eth0"
	ethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500
	inet4 10.211.6.227/24
	route4 0.0.0.0/0
	route4 10.211.6.0/24

wlan0: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""


        expected = {'internal_wifi':'wlan0', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)


    def test_wireless_to_configure_from_wlanX(self):
        device = 'wlan0'
        expected = '0'
        actual = wireless_to_configure_from_wlanX(device)
        self.assertEqual(expected, actual)

        device = 'wlan1'
        expected = '1'
        actual = wireless_to_configure_from_wlanX(device)
        self.assertEqual(expected, actual)

    def test_is_connection_active(self):
        line = 'corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0'
        expected = True
        actual = is_connection_active(line)
        self.assertEqual(expected, actual)

        line = 'corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:'
        expected = False
        actual = is_connection_active(line)
        self.assertEqual(expected, actual)

    def test_find_external_radio_from_lsusb_dash_v(self):
        content = """/:  Bus 02.Port 1: Dev 1, Class=root_hub, Driver=xhci_hcd/4p, 5000M
/:  Bus 01.Port 1: Dev 1, Class=root_hub, Driver=xhci_hcd/1p, 480M
    |__ Port 1: Dev 2, If 0, Class=Hub, Driver=hub/4p, 480M
        |__ Port 1: Dev 7, If 0, Class=Vendor Specific Class, Driver=rt2800usb, 480M
        |__ Port 2: Dev 4, If 0, Class=Human Interface Device, Driver=usbhid, 1.5M
        |__ Port 2: Dev 4, If 1, Class=Human Interface Device, Driver=usbhid, 1.5M
        |__ Port 3: Dev 5, If 0, Class=Human Interface Device, Driver=usbhid, 12M
        |__ Port 4: Dev 6, If 0, Class=Human Interface Device, Driver=usbhid, 1.5M
"""

        # /dev/bus/usb/
        expected = "001/007"
        actual = find_external_radio_from_lsusb_dash_v(content)
        self.assertEqual(expected, actual)

    def test_find_external_radio_from_usbreset(self):
        content = """Usage:
  usbreset PPPP:VVVV - reset by product and vendor id
  usbreset BBB/DDD   - reset by bus and device number
  usbreset "Product" - reset by product name

Devices:
  Number 001/008  ID 148f:5572  802.11 n WLAN
  Number 001/003  ID 062a:4101  2.4G Wireless Mouse
  Number 001/002  ID 2109:3431  USB2.0 Hub
  Number 001/006  ID 046d:c52b  USB Receiver
  Number 001/004  ID 4037:2804  2.4G Composite Devic"""
        expected = "001/008"
        actual = find_external_radio_from_usbreset(content)
        self.assertEqual(expected, actual)

    def test_get_next_corp_wifi_to_try(self):
        corp_wifi_certs_list = ['20220401a','20230401a']
        current_cert = ''
        expected = '20220401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

        current_cert = '20220401a'
        expected = '20230401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

        current_cert = '20230401a'
        expected = '20220401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

        corp_wifi_certs_list = []
        current_cert = '20230401a'
        expected = ''
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)


    def test_s_lan_detail_string(self):
        # ok, or on expired cert... eventually the connection will drop, and we move on to the next.
        s_lan_detail_string_pass = "Connection 'corp0' (81ed4954-6347-4234-bb6b-5747e28d6f54) successfully added."
        s_lan_detail_string_fails = ""

        # when cert is has a mismatched password
        s_lan_detail_string_pass = ""
        s_lan_detail_string_fails = "Error: failed to modify 802-1x.private-key: 802-1x.private-key: Couldn't verify PKCS#12 file: The Message Authentication Code verification failed.."

        # when cert is junk content
        s_lan_detail_string_pass = ""
        s_lan_detail_string_fails = "Error: failed to modify 802-1x.private-key: 802-1x.private-key: not a valid private key."

    def test_should_we_rotate_corp_wifi(self):
        time_since = 0

    def test_rate_str_from_rate(self):
        rate = "540 Mbit/s"
        expected = "540M"
        actual = rate_str_from_rate(rate)
        self.assertEqual(expected, actual)

        rate = "54 Mbit/s"
        expected = "54M"
        actual = rate_str_from_rate(rate)
        self.assertEqual(expected, actual)

        rate = "130 Mbit/s"
        expected = "130M"
        actual = rate_str_from_rate(rate)
        self.assertEqual(expected, actual)

    def test_is_nic_changed(self):
        nic1 = 'int'
        nic2 = 'int'
        expected = False
        actual = is_nic_changed(nic1, nic2)
        self.assertEqual(expected, actual)

        nic1 = 'int'
        nic2 = 'ext'
        expected = True
        actual = is_nic_changed(nic1, nic2)
        self.assertEqual(expected, actual)


    def test_manage_network_connections_wired(self):

        current_state = {}
        current_inputs = {}
        current_inputs['time_now'] = 0
        current_inputs['wifi_list'] = ''
        current_inputs['devices_report'] = ''
        current_inputs['connection_list'] = 'Wired connection 1:1acbc619-206d-3148-9ca9-50e49677f612:802-3-ethernet:eth0'
        current_inputs['wifi_mac_address_to_use'] = ''
        current_inputs['wifi_mac_address_external'] = ''
        current_inputs['wifi_mapping'] = {'internal_wifi':'wlan0', 'external_wifi':''}
        current_inputs['usbreset_content'] = ''
        current_inputs['current_uptime'] = 1
        current_inputs['wifi_config_content'] = ''
        current_inputs['coaster'] = '0'
        current_inputs['hopper_setting'] = ''
        current_inputs['s_is_engineering_device'] = False

        expected_state = {}
        expected_state['s_allow_wifi_config_connection'] = {}
        expected_state['s_allow_wifi_config_previous'] = {}
        expected_state['s_pass_count'] = 1
        expected_state['NetworkManager.conf_content'] =  '[main]\n' + \
                                 'plugins=ifupdown,keyfile\n' + \
                                 '\n' + \
                                 '[ifupdown]\n' + \
                                 'managed=false'
        expected_state['s_passes_not_connected'] = 0
        expected_state['s_usb_reset_radio_count'] = 0
        expected_state['s_time_of_last_pop'] = 0

        expected_actions = []
        expected_counts = {'signal': 0, 'corp_cert': ''}

        actual_state, shared_counts, actual_actions, trace_back_string = \
            manage_network_connections(current_state, current_inputs)

        print ('actual_actions', actual_actions)

        self.assertEqual(expected_state, actual_state)
        self.assertEqual(expected_counts, shared_counts)
        self.assertEqual(expected_actions, actual_actions)
        self.assertEqual('', trace_back_string)


    def test_manage_network_connections_wifi_empty(self):
        self.maxDiff = None

        current_state = {}
        current_inputs = {}
        current_inputs['time_now'] = 0
        current_inputs['wifi_list'] = ''
        current_inputs['devices_report'] = ''
        current_inputs['connection_list'] = ''
        current_inputs['wifi_mac_address_to_use'] = ''
        current_inputs['wifi_mac_address_external'] = ''
        current_inputs['wifi_mapping'] = {'internal_wifi':'wlan0', 'external_wifi':''}
        current_inputs['usbreset_content'] = ''
        current_inputs['current_uptime'] = 1
        current_inputs['wifi_config_content'] = ''
        current_inputs['coaster'] = '0'
        current_inputs['hopper_setting'] = ''
        current_inputs['s_is_engineering_device'] = False

        expected_state = {}
        expected_state['s_allow_wifi_config_connection'] = {}
        expected_state['s_allow_wifi_config_previous'] = {}
        expected_state['s_pass_count'] = 1
        expected_state['NetworkManager.conf_content'] =  '[main]\n' + \
                                 'plugins=ifupdown,keyfile\n' + \
                                 '\n' + \
                                 '[ifupdown]\n' + \
                                 'managed=false'
        expected_state['s_passes_not_connected'] = 0
        expected_state['s_usb_reset_radio_count'] = 0
        expected_state['s_time_of_last_pop'] = 0

        expected_actions = [{'do_one_command': 'sudo nmcli connection add type wifi con-name "cah-iot0" '
                     'ifname wlan0 ssid "cah-iot" -- wifi-sec.key-mgmt wpa-psk '
                     'wifi-sec.psk "BVIm5bvQ65"'},
  {'do_one_command': 'sudo nmcli con up cah-iot0'},
  {'restart_network_manager': None},
  {'do_one_command': '/sbin/iwconfig wlan0 power off'},
  {'do_one_command': '/sbin/iwconfig wlan1 power off'}]

        expected_counts = {'signal': 0, 'corp_cert': ''}

        actual_state, shared_counts, actual_actions, trace_back_string = \
            manage_network_connections(current_state, current_inputs)

        print ('actual_actions', actual_actions)

        self.assertEqual(expected_state, actual_state)
        self.assertEqual(expected_counts, shared_counts)
        self.assertEqual(expected_actions, actual_actions)
        self.assertEqual('', trace_back_string)

    def test_manage_network_connections_wifi_iot(self):
        self.maxDiff = None

        current_state = {'s_pass_count':2,
                        's_allow_wifi_config_previous':{}}
        current_inputs = {}
        current_inputs['time_now'] = 0
        current_inputs['wifi_list'] = ''
        current_inputs['devices_report'] = ''
        current_inputs['connection_list'] = 'cah-iot0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0'
        current_inputs['wifi_mac_address_to_use'] = ''
        current_inputs['wifi_mac_address_external'] = ''
        current_inputs['wifi_mapping'] = {'internal_wifi':'wlan0', 'external_wifi':''}
        current_inputs['usbreset_content'] = ''
        current_inputs['current_uptime'] = 120
        current_inputs['wifi_config_content'] = ''
        current_inputs['coaster'] = '0'
        current_inputs['hopper_setting'] = ''
        current_inputs['s_is_engineering_device'] = False

        expected_state = {}
        expected_state['s_allow_wifi_config_connection'] = {}
        expected_state['s_allow_wifi_config_previous'] = {}
        expected_state['s_pass_count'] = 3
        expected_state['NetworkManager.conf_content'] =  '[main]\n' + \
                                 'plugins=ifupdown,keyfile\n' + \
                                 '\n' + \
                                 '[ifupdown]\n' + \
                                 'managed=false'
        expected_state['s_passes_not_connected'] = 0
        expected_state['s_usb_reset_radio_count'] = 0
        expected_state['s_time_of_last_pop'] = 0

        expected_actions = []
        expected_counts = {'signal': 0, 'corp_cert': ''}

        actual_state, shared_counts, actual_actions, trace_back_string = \
            manage_network_connections(current_state, current_inputs)

        print ('actual_actions', actual_actions)

        self.assertEqual(expected_state, actual_state)
        self.assertEqual(expected_counts, shared_counts)
        self.assertEqual(expected_actions, actual_actions)
        self.assertEqual('', trace_back_string)

    def test_manage_network_connections_wifi_ferg(self):
        self.maxDiff = None

        current_state = {'s_allow_wifi_config_previous':{}}
        current_inputs = {}
        current_inputs['time_now'] = 0
        current_inputs['wifi_list'] = ''
        current_inputs['devices_report'] = ''
        current_inputs['connection_list'] = ''
        current_inputs['wifi_mac_address_to_use'] = ''
        current_inputs['wifi_mac_address_external'] = ''
        current_inputs['wifi_mapping'] = {'internal_wifi':'wlan0', 'external_wifi':''}
        current_inputs['usbreset_content'] = ''
        current_inputs['current_uptime'] = 120
        current_inputs['wifi_config_content'] = 'ferg,fergferg'
        current_inputs['coaster'] = '0'
        current_inputs['hopper_setting'] = ''
        current_inputs['s_is_engineering_device'] = True

        expected_state = {}
        expected_state['s_allow_wifi_config_connection'] = {'ssid': 'ferg', 'psk': 'fergferg'}
        expected_state['s_allow_wifi_config_previous'] =  {'ssid': 'ferg', 'psk': 'fergferg'}
        expected_state['s_pass_count'] = 1
        expected_state['NetworkManager.conf_content'] =  '[main]\n' + \
                                 'plugins=ifupdown,keyfile\n' + \
                                 '\n' + \
                                 '[ifupdown]\n' + \
                                 'managed=false'
        expected_state['s_passes_not_connected'] = 0
        expected_state['s_usb_reset_radio_count'] = 0
        expected_state['s_time_of_last_pop'] = 0

        expected_actions = [{'do_one_command': 'sudo nmcli connection add type wifi con-name "config0" ifname wlan0 ssid "ferg" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "fergferg"'},
{'do_one_command': 'sudo nmcli con up config0'},
{'restart_network_manager': None},
{'do_one_command': '/sbin/iwconfig wlan0 power off'},
{'do_one_command': '/sbin/iwconfig wlan1 power off'}]

        expected_counts = {'signal': 0, 'corp_cert': ''}

        actual_state, shared_counts, actual_actions, trace_back_string = \
            manage_network_connections(current_state, current_inputs)

        print ('actual_actions', actual_actions)

        self.assertEqual(expected_state, actual_state)
        self.assertEqual(expected_counts, shared_counts)
        self.assertEqual(expected_actions, actual_actions)
        self.assertEqual('', trace_back_string)

    def test_pop(self):
        actual = ['1','2']
        actual.pop(0)
        expected = ['2']
        self.assertEqual(expected, actual)

    def test_manage_network_connections_wifi_live_ferg(self):
        self.maxDiff = None

        current_state = {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 0, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 26, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 0, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}
        current_inputs = {"wifi_list": "ferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:74:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:59:270 Mbit/s\n",
                          "devices_report": "wlan0: connecting (configuring) to config0\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\neth0: unavailable\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n",
                          "connection_list": "config0:2e18f0aa-0d2d-44e3-aa52-edcbb07c72fe:802-11-wireless:wlan0\n",
                          "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": ""},
                          "wifi_mac_address_to_use": "dc:a6:32:96:56:c4",
                          "wifi_mac_address_external": "",
                          "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/040  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n",
                          "current_uptime": 956068,
                          "wifi_config_content": "ferg,fergferg",
                          "coaster": "10000",
                          "hopper_setting": "hop",
                          "s_is_engineering_device": True}
        current_inputs['time_now'] = 0
        expected_state = {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 0, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 27, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 0, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}


        expected_actions = [{"do_one_command": "sudo nmcli con delete \"config0\""},
                            {"do_one_command": "sudo nmcli connection add type wifi con-name \"config0\" ifname wlan0 ssid \"ferg\" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk \"fergferg\""},
                            {"do_one_command": "sudo nmcli con up config0"},
                            {"restart_network_manager": None},
                            {"do_one_command": "/sbin/iwconfig wlan0 power off"},
                            {"do_one_command": "/sbin/iwconfig wlan1 power off"},
                            {"save_shared_counts": {"signal": 0, "corp_cert": ""}}]

        expected_actions = [{'do_one_command': 'wpa_cli -i wlan0 bssid 0 28:D0:F5:A2:83:A8'},
                        {'do_one_command': 'wpa_cli -i wlan0 reassociate'}]
        expected_counts = {'signal': 0, 'corp_cert': ''}

        actual_state, shared_counts, actual_actions, trace_back_string = \
            manage_network_connections(current_state, current_inputs)

        print ('actual_actions', actual_actions)

        self.assertEqual(expected_state, actual_state)
        self.assertEqual(expected_counts, shared_counts)
        self.assertEqual(expected_actions, actual_actions)
        self.assertEqual('', trace_back_string)


    def test_manage_network_connections_wifi_live_sequence_ferg(self):
        self.maxDiff = None

        # before pasting here, do a replace of \\n with ???
        log_string = """
{"time": 1697569918.7766528, "time_since_last_action": 47.11757946014404, "current_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 0, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 7, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 0, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "current_inputs": {"time_now": 1697569904.7159731, "wifi_list": "ferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:72:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:60:270 Mbit/s\n\n", "devices_report": "wlan0: disconnected\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\nwlan1: disconnected\n\t\"Ralink RT5572\"\n\twifi (rt2800usb), 9C:EF:D5:FC:D0:F6, hw, mtu 1500\n\neth0: unavailable\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n", "connection_list": "", "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": "wlan1"}, "wifi_mac_address_to_use": "dc:a6:32:96:56:c4", "wifi_mac_address_external": "9c:ef:d5:fc:d0:f6", "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/004  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n  Number 001/008  ID 148f:5572  802.11 n WLAN\n", "current_uptime": 17288, "wifi_config_content": "ferg,fergferg", "coaster": "10000", "hopper_setting": "hop", "s_is_engineering_device": true}, "new_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569904.7159731, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 8, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "actions_list": [{"do_one_command": "usbreset 001/008"}, {"do_one_command": "sudo nmcli connection add type wifi con-name \"config1\" ifname wlan1 ssid \"ferg\" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk \"fergferg\""}, {"do_one_command": "sudo nmcli connection modify --temporary \"config1\"  802-11-wireless.cloned-mac-address dc:a6:32:96:56:c4 && sudo nmcli con up config1"}, {"restart_network_manager": null}, {"do_one_command": "/sbin/iwconfig wlan0 power off"}, {"do_one_command": "/sbin/iwconfig wlan1 power off"}, {"do_one_command": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8"}, {"do_one_command": "wpa_cli -i wlan1 reassociate"}], "trace_back_string": "", "action_results": [{"cmdline": "usbreset 001/008", "result": "Resetting 802.11 n WLAN ... ok\n", "fails": ""}, {"cmdline": "sudo nmcli connection add type wifi con-name \"config1\" ifname wlan1 ssid \"ferg\" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk \"fergferg\"", "result": "Connection 'config1' (b9b8baab-9305-449e-95f1-45f07752b650) successfully added.\n", "fails": ""}, {"cmdline": "sudo nmcli connection modify --temporary \"config1\"  802-11-wireless.cloned-mac-address dc:a6:32:96:56:c4 ", "result": "", "fails": ""}, {"cmdline": " sudo nmcli con up config1", "result": "Connection successfully activated (D-Bus active path: /org/freedesktop/NetworkManager/ActiveConnection/3)\n", "fails": ""}, {"restart_network_manager": 1}, {"cmdline": "/sbin/iwconfig wlan0 power off", "result": "", "fails": ""}, {"cmdline": "/sbin/iwconfig wlan1 power off", "result": "", "fails": ""}, {"cmdline": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8", "result": "OK\n", "fails": ""}, {"cmdline": "wpa_cli -i wlan1 reassociate", "result": "OK\n", "fails": ""}]}
{"time": 1697569923.5486155, "time_since_last_action": 4.771962642669678, "current_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569904.7159731, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 8, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "current_inputs": {"time_now": 1697569922.7854345, "wifi_list": "ferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:75:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:60:270 Mbit/s\n\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:74:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:60:270 Mbit/s\n", "devices_report": "wlan0: disconnected\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\nwlan1: disconnected\n\t\"Ralink RT5572\"\n\t1 connection available\n\twifi (rt2800usb), DC:A6:32:96:56:C4, hw, mtu 1500\n\neth0: unavailable\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n", "connection_list": "config1:b9b8baab-9305-449e-95f1-45f07752b650:802-11-wireless:\n", "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": "wlan1"}, "wifi_mac_address_to_use": "dc:a6:32:96:56:c4", "wifi_mac_address_external": "dc:a6:32:96:56:c4", "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/004  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n  Number 001/008  ID 148f:5572  802.11 n WLAN\n", "current_uptime": 17306, "wifi_config_content": "ferg,fergferg", "coaster": "10000", "hopper_setting": "hop", "s_is_engineering_device": true}, "new_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569922.7854345, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 9, "s_passes_not_connected": 1, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "actions_list": [{"do_one_command": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8"}, {"do_one_command": "wpa_cli -i wlan1 reassociate"}], "trace_back_string": "", "action_results": [{"cmdline": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8", "result": "OK\n", "fails": ""}, {"cmdline": "wpa_cli -i wlan1 reassociate", "result": "OK\n", "fails": ""}]}
{"time": 1697569928.50662, "time_since_last_action": 4.958004474639893, "current_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569922.7854345, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 9, "s_passes_not_connected": 1, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "current_inputs": {"time_now": 1697569927.5615747, "wifi_list": ":2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:74:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:60:270 Mbit/s\n\nferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:72:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:60:270 Mbit/s\n", "devices_report": "wlan1: connecting (getting IP configuration) to config1\n\t\"Ralink RT5572\"\n\twifi (rt2800usb), DC:A6:32:96:56:C4, hw, mtu 1500\n\nwlan0: disconnected\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\neth0: unavailable\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n", "connection_list": "config1:b9b8baab-9305-449e-95f1-45f07752b650:802-11-wireless:wlan1\n", "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": "wlan1"}, "wifi_mac_address_to_use": "dc:a6:32:96:56:c4", "wifi_mac_address_external": "dc:a6:32:96:56:c4", "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/004  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n  Number 001/008  ID 148f:5572  802.11 n WLAN\n", "current_uptime": 17311, "wifi_config_content": "ferg,fergferg", "coaster": "10000", "hopper_setting": "hop", "s_is_engineering_device": true}, "new_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569927.5615747, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 10, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "actions_list": [{"do_one_command": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8"}, {"do_one_command": "wpa_cli -i wlan1 reassociate"}], "trace_back_string": "", "action_results": [{"cmdline": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8", "result": "OK\n", "fails": ""}, {"cmdline": "wpa_cli -i wlan1 reassociate", "result": "OK\n", "fails": ""}]}
{"time": 1697571225.6405993, "time_since_last_action": 1297.1339793205261, "current_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697569927.5615747, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 229, "s_passes_not_connected": 0, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "current_inputs": {"time_now": 1697571209.2171047, "wifi_list": "\nferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:69:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:55:270 Mbit/s\n", "devices_report": "eth0: disconnected\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\nwlan0: disconnected\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\nwlan1: disconnected\n\t\"Ralink RT5572\"\n\twifi (rt2800usb), 9C:EF:D5:FC:D0:F6, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n", "connection_list": "config1:b9b8baab-9305-449e-95f1-45f07752b650:802-11-wireless:\n", "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": "wlan1"}, "wifi_mac_address_to_use": "dc:a6:32:96:56:c4", "wifi_mac_address_external": "9c:ef:d5:fc:d0:f6", "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/004  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n  Number 001/009  ID 148f:5572  802.11 n WLAN\n", "current_uptime": 18608, "wifi_config_content": "ferg,fergferg", "coaster": "10000", "hopper_setting": "hop", "s_is_engineering_device": true}, "new_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697571209.2171047, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 230, "s_passes_not_connected": 1, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "actions_list": [{"do_one_command": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8"}, {"do_one_command": "wpa_cli -i wlan1 reassociate"}], "trace_back_string": "", "action_results": [{"cmdline": "wpa_cli -i wlan1 bssid 0 28:D0:F5:A2:83:A8", "result": "FAIL\n", "fails": ""}, {"cmdline": "wpa_cli -i wlan1 reassociate", "result": "OK\n", "fails": ""}]}
{"time": 1697571231.1727614, "time_since_last_action": 5.532162189483643, "current_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697571209.2171047, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 230, "s_passes_not_connected": 1, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "current_inputs": {"time_now": 1697571229.6508021, "wifi_list": "ferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:70:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD:*:36:5180 MHz:54:270 Mbit/s\n\nferg:28\\:D0\\:F5\\:A2\\:83\\:A8: :7:2442 MHz:100:540 Mbit/s\nferg_5G:2A\\:D0\\:F5\\:92\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A7: :56:5280 MHz:100:540 Mbit/s\n:2A\\:D0\\:F5\\:D2\\:83\\:A8: :7:2442 MHz:97:540 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FC: :7:2442 MHz:74:270 Mbit/s\nferg:34\\:98\\:B5\\:00\\:22\\:FD: :36:5180 MHz:62:270 Mbit/s\n", "devices_report": "wlan1: connected to config1\n\t\"Ralink RT5572\"\n\twifi (rt2800usb), 9C:EF:D5:FC:D0:F6, hw, mtu 1500\n\tip4 default\n\tinet4 *************11/24\n\troute4 0.0.0.0/0\n\troute4 *************/24\n\tinet6 fe80::2d4a:c22f:6f2a:e68b/64\n\troute6 fe80::/64\n\neth0: connected to eth0\n\t\"eth0\"\n\tethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500\n\tinet4 **************/27\n\troute4 **************/27\n\troute4 0.0.0.0/0\n\tinet6 fe80::54c9:269f:20dd:7b6a/64\n\troute6 fe80::/64\n\nwlan0: disconnected\n\t\"Broadcom BCM43438 combo and Bluetooth Low Energy\"\n\twifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500\n\nlo: unmanaged\n\t\"lo\"\n\tloopback (unknown), 00:00:00:00:00:00, sw, mtu 65536\n\nDNS configuration:\n\tservers: *************\n\tdomains: lan\n\tinterface: wlan1\n\nUse \"nmcli device show\" to get complete information about known devices and\n\"nmcli connection show\" to get an overview on active connection profiles.\n\nConsult nmcli(1) and nmcli-examples(5) manual pages for complete usage details.\n", "connection_list": "config1:b9b8baab-9305-449e-95f1-45f07752b650:802-11-wireless:wlan1\neth0:281fe2d0-4bc3-4611-b212-375d43b6bcde:802-3-ethernet:eth0\n", "wifi_mapping": {"internal_wifi": "wlan0", "external_wifi": "wlan1"}, "wifi_mac_address_to_use": "dc:a6:32:96:56:c4", "wifi_mac_address_external": "9c:ef:d5:fc:d0:f6", "usbreset_content": "Usage:\n  usbreset PPPP:VVVV - reset by product and vendor id\n  usbreset BBB/DDD   - reset by bus and device number\n  usbreset \"Product\" - reset by product name\n\nDevices:\n  Number 001/004  ID 258a:0131  USB TOUCHPAD KEYBOARD\n  Number 001/002  ID 2109:3431  USB2.0 Hub\n  Number 001/009  ID 148f:5572  802.11 n WLAN\n", "current_uptime": 18613, "wifi_config_content": "ferg,fergferg", "coaster": "10000", "hopper_setting": "hop", "s_is_engineering_device": true}, "new_state": {"s_allow_wifi_config_connection": {"ssid": "ferg", "psk": "fergferg"}, "s_time_of_last_pop": 1697571209.2171047, "s_allow_wifi_config_previous": {"ssid": "ferg", "psk": "fergferg"}, "s_pass_count": 231, "s_passes_not_connected": 1, "s_usb_reset_radio_count": 1, "NetworkManager.conf_content": "[main]\nignore-carrier=10000\nplugins=ifupdown,keyfile\n\n[ifupdown]\nmanaged=false"}, "actions_list": [{"do_one_command": "sudo nmcli con delete \"config1\""}], "trace_back_string": "", "action_results": [{"cmdline": "sudo nmcli con delete \"config1\"", "result": "Connection 'config1' (b9b8baab-9305-449e-95f1-45f07752b650) successfully deleted.\n", "fails": ""}]}
        """


        sequence = []
        for line_found in log_string.split("\n"):
            line_scrubbed = line_found.split('cah-pi-su')[0].replace('\\n', '\n')
            print ('line_scrubbed', line_scrubbed)


        print ('log_string', log_string)




# End of source file