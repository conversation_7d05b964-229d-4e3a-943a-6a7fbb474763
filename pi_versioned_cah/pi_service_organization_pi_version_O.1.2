_ = """
sudo vi /cardinal/pi_organization.py

sudo systemctl restart pi-organization.service

watch -n 1 sudo cat /dev/shm/pi_organization_datadrop.txt

cat /cardinal/log/pi_organization_lastupdate.txt

"""

service = 'organization'
version = 'O.1.2'
description = """
This is a pi service for setting organization specific content.

logo for use by hmi

let this be where psk and ssid get set into the config file
(instead of image building instructions doing it?).


"""

release_notes = """
2024.03.12
O.1.2
Support Raspberry Pi5

2023.02.02
O.1.1
Make load_wifi_cert be consistent

2022.12.06
O.1.0
Pull details from a settings file, if present.

2022.11.30
O.0.4

Version bump for testing.

2022.10.31
O.0.3

put the company logo image into place.


"""

_notes = """
Use this file as both the "template" and one particular implementation.

"""


other_content = """
sudo vi /cardinal/pi-organization
sudo chmod +x /cardinal/pi-organization

# ===== begin: start file
#!/usr/bin/env python3
import pi_organization
pi_organization.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-organization.service
sudo systemctl daemon-reload
sudo systemctl stop pi-organization.service
sudo systemctl start pi-organization.service
sudo systemctl enable pi-organization.service

systemctl status pi-organization.service

sudo systemctl restart pi-organization.service

# organization of std out
sudo cat /var/log/syslog | fgrep pi-organization

OR

tail -f /var/log/syslog | fgrep pi-organization

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-organization
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_organization


"""

import copy
import json
import math
import os
try:
    import requests
except:
    pass # for unittest
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

# globals
s_corp_wifi_certs_path = "/cardinal/wifi_certs/"
s_corp_wifi_cert_last_used_path = "/cardinal/"

s_logo_xxd_last_used = ''
s_wifi_cert_last_used = {}

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)



# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
#    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass
# ----------------------------
def load_wifi_cert(cert_contents = {}):
# ----------------------------
    global s_corp_wifi_cert_in_use, s_corp_wifi_cert_just_loaded_in_use

    report = ''

    certs_path = s_corp_wifi_certs_path

    try:
        s_corp_wifi_cert_in_use = open(s_corp_wifi_cert_last_used_path + 's_corp_wifi_cert_in_use', 'r').read()
        s_corp_wifi_cert_just_loaded_in_use = True
    except:
        pass

    # start clean each time
    command = 'sudo rm -rf ' + certs_path
    pass_string, fails = do_one_command(command)
    report += '--------------------------' + '\n'
    report += 'command     = ' + command + '\n'
    report += 'pass_string = ' + pass_string + '\n'
    report += 'fails       = ' + fails + '\n'

    for cert_name in cert_contents.keys():
        output_file = certs_path + cert_name + "/TempCert.pass"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(cert_contents[cert_name]['password'])


        output_file = certs_path + cert_name + "/TempCert.xxd"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(cert_contents[cert_name]['content'])

        # make back into p12 file
        content = """
    #!/usr/bin/env sh
    cat """ + output_file + """ | xxd -p -r >""" + output_file.replace('.xxd', '.p12') + """
    sudo rm """ + output_file + """
        """
        output_file = "/cardinal/pi_network_tempscript"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        open(output_file, 'w').write(content)

        report += '--------------------------' + '\n'
        report += 'output_file = ' + output_file + '\n'
        report += 'content     = ' + content + '\n'

        command = 'sudo chmod +x /cardinal/pi_network_tempscript'
        pass_string, fails = do_one_command(command)
        report += '--------------------------' + '\n'
        report += 'command     = ' + command + '\n'
        report += 'pass_string = ' + pass_string + '\n'
        report += 'fails       = ' + fails + '\n'

        command = 'sudo /cardinal/pi_network_tempscript'
        pass_string, fails = do_one_command(command)
        report += '--------------------------' + '\n'
        report += 'command     = ' + command + '\n'
        report += 'pass_string = ' + pass_string + '\n'
        report += 'fails       = ' + fails + '\n'

        # clean up
        command = 'sudo rm /cardinal/pi_network_tempscript'
        pass_string, fails = do_one_command(command)
        report += '--------------------------' + '\n'
        report += 'command     = ' + command + '\n'
        report += 'pass_string = ' + pass_string + '\n'
        report += 'fails       = ' + fails + '\n'


        # save report
        try:
            output_file = '/dev/shm/load_wifi_cert_report_' + str(time.time()) + '.txt'
            open(output_file, 'w').write(report)
        except:
            pass

# ----------------------------
def make_organization_logo(logo_xxd_content):
# ----------------------------

    output_file = "/cardinal/organization_logo.xxd"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(logo_xxd_content)

    # make back into image file
    content = """
#!/usr/bin/env sh
cat /cardinal/organization_logo.xxd | xxd -p -r >/cardinal/organization_logo.gif
sudo rm /cardinal/organization_logo.xxd
    """
    output_file = "/cardinal/pi_organization_tempscript"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    report = ''

    pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_organization_tempscript')
    report += "pass1\n" + pass_string + "fail1\n" + fails

    pass_string, fails = do_one_command('sudo /cardinal/pi_organization_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    # clean up
    pass_string, fails = do_one_command('sudo rm /cardinal/pi_organization_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    with open ("/dev/shm/organization_report.txt", "w") as f:
        f.write(report)

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_organization_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('organization data drop: Start',True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in with slicer
            try:
                do_datadrop_debug('Start organization...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug('organization result: ' + url_result)

                try:
                    result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'


                    if 'action_request' in result_json:
                        pass
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())


    do_datadrop_debug('organization data drop: End')

# ----------------------------
def do_maintenance():
# ----------------------------
    global s_logo_xxd_last_used
    global s_wifi_cert_last_used
    # have each functional item run in its own try block, and report results as section in dictionary

#    do_datadrop()
    try:
        # https://www.geeksforgeeks.org/reloading-modules-python/
        import pi_settings              # gets us started (Only does work on first pass)
        import importlib
        importlib.reload(pi_settings)   # gets us changes (Does work every pass)

        settings = pi_settings.get()

        if 'logo_xxd' in settings:
            logo_content = settings['logo_xxd']
            if s_logo_xxd_last_used != logo_content:
                make_organization_logo(logo_content)
                s_logo_xxd_last_used = logo_content

        if 'wifi_cert' in settings:
            wifi_cert = settings['wifi_cert']
            if s_wifi_cert_last_used != wifi_cert:
                load_wifi_cert(wifi_cert)
                s_wifi_cert_last_used = wifi_cert

    except:
        open('/dev/shm/pi_organization_maintenance', 'w').write(traceback.format_exc())


# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # FixMe: do "runlevel", parse "N 3" and "N 5" to see that X11 is running in 5, and if that
    #        happens before timeout, then move ahead?
    time.sleep(5) # give the file system a full chance to settle, before messing with wifi certs

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string for " + service + ": " + version)

    do_one_time()

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 1):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_organization_wake.txt', 'w') as f:
                    f.write(str(wake_count) + ' ' + str(abs(time_now - time.time())))
            except:
                print ("!!! failed to write wake_count")



# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_organization(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)

