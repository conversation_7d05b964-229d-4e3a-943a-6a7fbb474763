_ = """
sudo vi /cardinal/pi_security.py

sudo systemctl restart pi-security.service

watch -n 1 sudo cat /dev/shm/pi_security_datadrop.txt

cat /cardinal/log/pi_security_lastupdate.txt

"""

service = 'security'
version = 'S.2.0'
description = """
This is a service to perform the audits on the device, and to report
findings.

"""

ttd = """
Leverage the recent additions to the pi_security installs:

Use debscan results to report cve list, and to know what to do about it.

Use debsums

Use apt-listbugs

Use debian-goodies

Use rkhunter, and read log file results

Use sysstat

Use auditd


Also:
https://wiki.debian.org/UnattendedUpgrades


"""

release_notes = """
2022.12.06
S.2.0
Modify the ssh limiting in tcp_full_config based on settings, if present

2022.11.03
S.1.9
Make the sshd allow be multi line, to correctly handle multiple hosts.
Move the maintenance cycle to the end of the 30 minute interval, to not impact startup operations

2022.08.31
S.1.8
Support ssh allow from any configured host.

2022.01.31
S.1.7
Add support for looking up the call_home_locations

2021.10.25
S.1.6
Remove apt-listbugs; it was causing other installs to lock up, and have no way to move forward (cups)

2021.09.29
S.1.5
Add install of apt-listbugs, debsecan

2021.09.23
S.1.4
Before trying to use lynis, do the install of lynis. If not there, it will install. If already there, no action.

2021.09.16
S.1.3
Limit ssh to just the admin user.
Limit inbound connections to just from slicer.

2021.09.14
S.1.2
Scan for updates available.
React to "do updates" request from Slicer. (device_osupdates_request_)
On start, do a bluetooth disable (take away the old default bluetooth setup), so that updates are clean.

2021.09.09
S.1.1
Version bump, just to test code load over service pull.

2021.08.10
S.1.0
Do lynis scan, and report up to Slicer.

"""

_updates = """
https://linuxhint.com/apt_get_upgrade_dist_upgrade/

install just one:
https://askubuntu.com/questions/44122/how-to-upgrade-a-single-package-using-apt-get

Check, but do not install:
https://unix.stackexchange.com/questions/19470/list-available-updates-but-do-not-install-them

Security updates available:
https://askubuntu.com/questions/774805/how-to-get-a-list-of-all-pending-security-updates

sudo apt-get list --upgradable |grep "/$(lsb_release -cs)-security"

OR:

sudo apt update -y
sudo apt-get --no-download -s dist-upgrade -V --fix-missing

0 upgraded, 0 newly installed, 0 to remove and 88 not upgraded.


ALSO:
eeprom update:
https://pimylifeup.com/raspberry-pi-update/

"""

_access = """
Only allow certain users access:
https://www.linuxshelltips.com/restrict-ssh-access-user/

Only allow access from certain hosts:
https://docs.rackspace.com/support/how-to/restrict-ssh-login-to-a-specific-ip-or-host/


"""

other_content = """
sudo vi /cardinal/pi-security
sudo chmod +x /cardinal/pi-security

# ===== begin: start file
#!/usr/bin/env python3
import pi_security
pi_security.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-security.service
sudo systemctl daemon-reload
sudo systemctl stop pi-security.service
sudo systemctl start pi-security.service
sudo systemctl enable pi-security.service

systemctl status pi-security.service

sudo systemctl restart pi-security.service

# Logging of std out
cat /var/log/syslog | fgrep pi-security

OR

tail -f /var/log/syslog | fgrep pi-security

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-security
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Chromium_update = """
sudo apt-get update
sudo apt-get full-upgrade -y

sudo apt-get install -y chromium-browser


https://archive.raspberrypi.org/debian/pool/main/c/chromium-browser/


https://www.tomshardware.com/how-to/upgrade-raspberry-pi-os-to-bullseye-from-buster


cah-pi-su@cah-rp-10000000aaef9969:/cardinal $ uname -a
Linux cah-rp-10000000aaef9969 5.10.63-v7l+ #1496 SMP Wed Dec 1 15:58:56 GMT 2021 armv7l GNU/Linux

sudo apt update
sudo apt dist-upgrade -y
sudo rpi-update
sudo nano /etc/apt/sources.list
find:
deb http://raspbian.raspberrypi.org/raspbian/ buster main contrib non-free rpi
change to:
deb http://raspbian.raspberrypi.org/raspbian/ bullseye main contrib non-free rpi

ctrl+X, Y

sudo apt update
sudo apt dist-upgrade
(Had to type Y to accept,
and later Q to get out of a reader,
then 'Configuring libc6:armhf' popup select Yes,
prompt "Configuration file '/etc/default/useradd'" selected Y
prompt "'/etc/privoxy/user.action'" selected Y
popup "A new version (/etc/privoxy/config.ucftmp) of configuration file /etc/privoxy/config" select "keep the local" [default]
prompt "'/etc/rkhunter.conf'" select Y
popup "A new version (/tmp/tmp.vY3fbIszR9) of configuration file /etc/ssh/sshd_config is available" select "keep the local"
)

sudo apt autoclean
sudo reboot



"""

_manual_build_chromium = """
https://chromium.googlesource.com/chromium/src/+/main/docs/linux/build_instructions.md

sudo apt-get install git -y

cd /cardinal
sudo git clone https://chromium.googlesource.com/chromium/tools/depot_tools.git

echo $PATH
(/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/games:/usr/games)

sudo mkdir /cardinal/chromium
cd /cardinal/chromium
export PATH="$PATH:/cardinal/depot_tools"
sudo /cardinal/depot_tools/fetch --nohooks --no-history chromium
(failed)
(try again)
sudo /cardinal/depot_tools/fetch --nohooks --no-history chromium
[E2022-09-07T09:10:40.171947-04:00 31034 0 annotate.go:273] original error: no space left on device

This was on a 16GB card, that had 75% free.
Need to capture how much free space this operation requires.
Start over with 64GB card

============================================================
Before:
cah-pi-su@cah-rp-10000000aaef9969:~ $ df -k
Filesystem     1K-blocks    Used Available Use% Mounted on
/dev/root       59673776 2194292  56245584   4% /
devtmpfs         1800564       0   1800564   0% /dev
tmpfs            1965428   12412   1953016   1% /dev/shm
tmpfs            1965428   25392   1940036   2% /run
tmpfs               5120       4      5116   1% /run/lock
tmpfs            1965428       0   1965428   0% /sys/fs/cgroup
/dev/mmcblk0p1    258095   51034    207062  20% /boot
tmpfs             393084       0    393084   0% /run/user/1002
tmpfs             393084       0    393084   0% /run/user/1001

After:


============================================================



"""
_chromium_notes = """
https://chromium.googlesource.com/chromium/src/+/main/docs/linux/build_instructions.md

release schedule?:

https://chromiumdash.appspot.com/schedule

Notes:

https://www.chromium.org/Home/chromium-security/

Vulnerabilities:

https://chromereleases.googleblog.com/search/label/Stable%20updates

"""

import copy
import json
import math
import os
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

try:
    import requests
except:
    pass # unittest

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def tcp_boiler_plate():
# ----------------------------
    desired_content= """# /etc/hosts.allow: list of hosts that are allowed to access the system.
#                   See the manual pages hosts_access(5) and hosts_options(5).
#
# Example:    ALL: LOCAL @some_netgroup
#             ALL: .foobar.edu EXCEPT terminalserver.foobar.edu
#
# If you're going to protect the portmapper use the name "rpcbind" for the
# daemon name. See rpcbind(8) and rpc.mountd(8) for further information.
#
"""
    return desired_content

# ----------------------------
def tcp_full_config(call_homes):
# ----------------------------
    return_value = tcp_boiler_plate()

    added_sshd = ''

    for item in call_homes:
        splits = item.split('//')
        if added_sshd:
            added_sshd += '\n'
        if len (splits) == 2:
            added_sshd += 'sshd: ' + splits[1]

    try:
        # https://www.geeksforgeeks.org/reloading-modules-python/
        import pi_settings              # gets us started
        import importlib
        importlib.reload(pi_settings)   # gets us changes

        settings = pi_settings.get()
        added_sshd2 = settings['added_sshd']

        for item in added_sshd2:
            if added_sshd:
                added_sshd += '\n'
            added_sshd += 'sshd: ' + item
    except:
        pass

    return_value += added_sshd + '\n'

    return return_value

# ----------------------------
def set_tcp_config():
# ----------------------------
    call_homes = call_home_locations()
    desired_content = tcp_full_config(call_homes)

    file_to_use = '/etc/hosts.allow'
    existing_content = ""
    try:
        with open(file_to_use, 'r') as f:
            existing_content = f.read()

        if not existing_content == desired_content:
            with open(file_to_use, 'w') as f:
                f.write(desired_content)

            do_one_command('sudo systemctl restart sshd')

    except:
        pass


    desired_content = """
# /etc/hosts.deny: list of hosts that are _not_ allowed to access the system.
#                  See the manual pages hosts_access(5) and hosts_options(5).
#
# Example:    ALL: some.host.name, .some.domain
#             ALL EXCEPT in.fingerd: other.host.name, .other.domain
#
# If you're going to protect the portmapper use the name "rpcbind" for the
# daemon name. See rpcbind(8) and rpc.mountd(8) for further information.
#
# The PARANOID wildcard matches any host whose name does not match its
# address.
#
# You may wish to enable this to ensure any programs that don't
# validate looked up hostnames still leave understandable logs. In past
# versions of Debian this has been the default.
# ALL: PARANOID
sshd: ALL
    """


    if True:
        file_to_use = '/etc/hosts.deny'
        existing_content = ""
        try:
            with open(file_to_use, 'r') as f:
                existing_content = f.read()

            if not existing_content == desired_content:
                with open(file_to_use, 'w') as f:
                    f.write(desired_content)

                do_one_command('sudo systemctl restart sshd')

        except:
            pass


# ----------------------------
def set_sshd_config():
# ----------------------------

    desired_content = """#	$OpenBSD: sshd_config,v 1.103 2018/04/09 20:41:22 tj Exp $

# This is the sshd server system-wide configuration file.  See
# sshd_config(5) for more information.

# This sshd was compiled with PATH=/usr/bin:/bin:/usr/sbin:/sbin

# The strategy used for options in the default sshd_config shipped with
# OpenSSH is to specify options with their default value where
# possible, but leave them commented.  Uncommented options override the
# default value.

#Port 22
#AddressFamily any
#ListenAddress 0.0.0.0
#ListenAddress ::

#HostKey /etc/ssh/ssh_host_rsa_key
#HostKey /etc/ssh/ssh_host_ecdsa_key
#HostKey /etc/ssh/ssh_host_ed25519_key

# Ciphers and keying
#RekeyLimit default none

# Logging
#SyslogFacility AUTH
#LogLevel INFO

# Authentication:

#LoginGraceTime 2m
PermitRootLogin no
#StrictModes yes
MaxAuthTries 2
MaxSessions 2

#PubkeyAuthentication yes

# Expect .ssh/authorized_keys2 to be disregarded by default in future.
#AuthorizedKeysFile	.ssh/authorized_keys .ssh/authorized_keys2

#AuthorizedPrincipalsFile none

#AuthorizedKeysCommand none
#AuthorizedKeysCommandUser nobody

# For this to work you will also need host keys in /etc/ssh/ssh_known_hosts
#HostbasedAuthentication no
# Change to yes if you don't trust ~/.ssh/known_hosts for
# HostbasedAuthentication
#IgnoreUserKnownHosts no
# Don't read the user's ~/.rhosts and ~/.shosts files
#IgnoreRhosts yes

# To disable tunneled clear text passwords, change to no here!
#PasswordAuthentication yes
#PermitEmptyPasswords no

# Change to yes to enable challenge-response passwords (beware issues with
# some PAM modules and threads)
ChallengeResponseAuthentication no

# Kerberos options
#KerberosAuthentication no
#KerberosOrLocalPasswd yes
#KerberosTicketCleanup yes
#KerberosGetAFSToken no

# GSSAPI options
#GSSAPIAuthentication no
#GSSAPICleanupCredentials yes
#GSSAPIStrictAcceptorCheck yes
#GSSAPIKeyExchange no

# Set this to 'yes' to enable PAM authentication, account processing,
# and session processing. If this is enabled, PAM authentication will
# be allowed through the ChallengeResponseAuthentication and
# PasswordAuthentication.  Depending on your PAM configuration,
# PAM authentication via ChallengeResponseAuthentication may bypass
# the setting of "PermitRootLogin without-password".
# If you just want the PAM account and session checks to run without
# PAM authentication, then enable this but set PasswordAuthentication
# and ChallengeResponseAuthentication to 'no'.
UsePAM yes

AllowAgentForwarding no
AllowTcpForwarding no
#GatewayPorts no
X11Forwarding no
#X11DisplayOffset 10
#X11UseLocalhost yes
#PermitTTY yes
PrintMotd no
#PrintLastLog yes
#TCPKeepAlive yes
#PermitUserEnvironment no
#Compression delayed
#ClientAliveInterval 0
ClientAliveCountMax 2
#UseDNS no
#PidFile /var/run/sshd.pid
#MaxStartups 10:30:100
#PermitTunnel no
#ChrootDirectory none
#VersionAddendum none

# no default banner path
#Banner none

# Allow client to pass locale environment variables
AcceptEnv LANG LC_*

# override default of no subsystems
Subsystem	sftp	/usr/lib/openssh/sftp-server

# Example of overriding settings on a per-user basis
#Match User anoncvs
#	X11Forwarding no
#	AllowTcpForwarding no
#	PermitTTY no
#	ForceCommand cvs server

AllowUsers cah-pi-su
"""

    try:
        # https://www.geeksforgeeks.org/reloading-modules-python/
        import pi_settings              # gets us started
        import importlib
        importlib.reload(pi_settings)   # gets us changes

        settings = pi_settings.get()
        added_users = settings['added_users']

        for item in added_users:
            desired_content += 'AllowUsers ' + item + '\n'
    except:
        pass

    file_to_use = '/etc/ssh/sshd_config'
    existing_content = ""
    try:
        with open(file_to_use, 'r') as f:
            existing_content = f.read()

        if not existing_content == desired_content:
            with open(file_to_use, 'w') as f:
                f.write(desired_content)

            do_one_command('sudo systemctl restart sshd')

    except:
        pass

# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
    list_of_cmds.append('sudo systemctl disable bluetooth.service')
    list_of_cmds.append('sudo systemctl stop bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_security_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def do_updates_screen():
# ----------------------------
    result = {}
    result['updates_available'] = '-1'

    # make the list be current
    pass_string, fail_string = do_one_command('sudo apt update -y')

    # See what the list has that we do not have
#    pass_string, fail_string = do_one_command('sudo apt-get --no-download -s dist-upgrade -V --fix-missing')
    pass_string, fail_string = do_one_command('sudo apt-get -s dist-upgrade -V --fix-missing')

    _ = """
The following packages will be upgraded:
   bluez (5.50-1.2~deb10u2 => 5.50-1.2~deb10u2+rpt1)
   libbluetooth3 (5.50-1.2~deb10u2 => 5.50-1.2~deb10u2+rpt1)
   libc-bin (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc-dev-bin (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc-l10n (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6 (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6-dbg (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6-dev (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libnftnl11 (1.1.2-2 => 1.1.7-1~bpo10+1~0)
   libraspberrypi-bin (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi-dev (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi-doc (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi0 (1:1.20210805-1 => 1:1.20210831-1)
   linux-libc-dev (1:1.20210805-1 => 1:1.20210831-1)
   locales (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   multiarch-support (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   raspberrypi-bootloader (1:1.20210805-1 => 1:1.20210831-1)
   raspberrypi-kernel (1:1.20210805-1 => 1:1.20210831-1)
   raspberrypi-sys-mods (20210706 => 20210901)
19 upgraded, 0 newly installed, 0 to remove and 0 not upgraded.
1 not fully installed or removed.
Inst libc6-dbg [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc6-dev [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc-dev-bin [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst linux-libc-dev [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc6 [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc6 (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst bluez [5.50-1.2~deb10u2] (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Inst libc-bin [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc-bin (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst libc-l10n [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Inst locales [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Inst libbluetooth3 [5.50-1.2~deb10u2] (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Inst libnftnl11 [1.1.2-2] (1.1.7-1~bpo10+1~0 Raspberry Pi Foundation:testing [armhf])
Inst libraspberrypi-doc [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi-dev [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst raspberrypi-kernel [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi-bin [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi0 [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst raspberrypi-bootloader [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Inst multiarch-support [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst raspberrypi-sys-mods [20210706] (20210901 Raspberry Pi Foundation:testing [armhf])
Conf libc6-dbg (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc6-dev (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc-dev-bin (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf linux-libc-dev (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf bluez (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Conf libc-l10n (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Conf locales (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Conf libbluetooth3 (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Conf libnftnl11 (1.1.7-1~bpo10+1~0 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-doc (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-dev (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-kernel (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-bin (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi0 (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-bootloader (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf multiarch-support (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-sys-mods (20210901 Raspberry Pi Foundation:testing [armhf])
  """

    count_security = 0
    for line in pass_string.split('\n'):
        if 'upgraded,' in line:
            # line = '0 upgraded, 0 newly installed, 0 to remove and 88 not upgraded.'
            try:
                result['updates_available'] = line.split('upgraded,')[0].strip()
            except:
                pass

            if 'security' in line.lower():
                count_security += 1

    result['updates_security'] = str(count_security)

    return result

# ----------------------------
def do_security_screen():
# ----------------------------
    result = {}
    result['hardening_index'] = '-1'

    # sudo lynis audit system -Q
    # get to a clean starting state:
    pass_string, fail_string = do_one_command('sudo dpkg --configure -a')

    # This makes things fail to normally install (cups)
    #pass_string, fail_string = do_one_command('sudo apt install -y apt-listbugs') # list bugs before taking updates

    # be sure to take it out
    pass_string, fail_string = do_one_command('sudo apt remove -y apt-listbugs') # list bugs before taking updates


    pass_string, fail_string = do_one_command('sudo apt install -y debsecan')     # report on current known vulnerabilities "debsecan --format detail"

    # know fixes only (basically, what needs updated)
    # debsecan --suite sid --only-fixed

    # to take the updates:
    # sudo apt-get install $(debsecan --suite sid --only-fixed --format packages)


    pass_string, fail_string = do_one_command('sudo apt install -y fail2ban')
    # fail2ban-client -h
    # (https://github.com/fail2ban/fail2ban)
    # sudo fail2ban-client status
    # sudo fail2ban-client status sshd
    # https://serverfault.com/questions/841183/how-to-show-all-banned-ip-with-fail2ban


    pass_string, fail_string = do_one_command('sudo apt install -y debsums')     # sudo debsums

    pass_string, fail_string = do_one_command('sudo apt install -y debian-goodies')     # sudo checkrestart
    # https://www.tecmint.com/use-debian-goodies-utilities-to-manage-debian-packages/

    pass_string, fail_string = do_one_command('sudo apt install -y sysstat')     # mpstat
    # https://www.tecmint.com/sysstat-commands-to-monitor-linux/

    pass_string, fail_string = do_one_command('sudo apt install -y auditd audispd-plugins')     # sudo cat /var/log/audit/audit.log


    pass_string, fail_string = do_one_command('sudo apt install -y rkhunter')
    # to test config: sudo rkhunter -C
    # sudo rkhunter --update
    # !!!!!!!!! causes a reboot !!!!!!!!! at "Performing filesystem checks"
    # sudo rkhunter --check --skip-keypress --summary
    # !!!!!!!!! causes a reboot !!!!!!!!! at "Performing filesystem checks"
    # Is that because it "touches" my reboot detector? (/dev/shm/pi_hmi_reboot.html)
    _notes = """
This worked, without rebooting:
sudo systemctl stop pi-hmi.service
sudo rkhunter --check --skip-keypress --summary
sudo systemctl start pi-hmi.service

sudo cat  /var/log/rkhunter.log

Also, check out if using files in the allowlist keeps them from being scanned (restart/reboot trigger files)

    """

    config_content = """
UPDATE_MIRRORS=1
MIRRORS_MODE=0
TMPDIR=/var/lib/rkhunter/tmp
DBDIR=/var/lib/rkhunter/db
SCRIPTDIR=/usr/share/rkhunter/scripts
UPDATE_LANG="en"
LOGFILE=/var/log/rkhunter.log
USE_SYSLOG=authpriv.warning
AUTO_X_DETECT=1
ALLOW_SSH_PROT_V1=2
ENABLE_TESTS=ALL
DISABLE_TESTS=suspscan hidden_ports hidden_procs deleted_files packet_cap_apps apps
SCRIPTWHITELIST=/bin/egrep
SCRIPTWHITELIST=/bin/fgrep
SCRIPTWHITELIST=/bin/which
SCRIPTWHITELIST=/usr/bin/ldd
SCRIPTWHITELIST=/usr/sbin/adduser
#WEB_CMD="/bin/false"
INSTALLDIR=/usr
    """

    if not os.path.isfile('/etc/rkhunter.conf.sav'):
        try:
            shutil.copy2('/etc/rkhunter.conf', '/etc/rkhunter.conf.sav')
        except:
            pass

    try:
        with open('/etc/rkhunter.conf', 'w') as f:
            f.write(config_content)
    except:
        pass

    pass_string, fail_string = do_one_command('sudo apt-get install --fix-missing -y lynis')
    pass_string, fail_string = do_one_command('sudo lynis audit system -Q --no-colors')

    _ = """
  Hardening index : 55 [###########         ]
  Tests performed : 213
  Plugins enabled : 1
  """

    for line in pass_string.split('\n'):
        if 'Hardening index' in line:
            result['hardening_index'] = line.split(':')[1].strip().split()[0]

    return result

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('security data drop: Start',True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        do_datadrop_debug('Start security screen...')
        results = do_security_screen()
        for item in results:
            the_data.append(item + '=' + results[item])

        do_datadrop_debug('Start updates screen...')
        results = do_updates_screen()
        for item in results:
            the_data.append(item + '=' + results[item])

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in with slicer
            try:
                do_datadrop_debug('Start reporting...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug('Reporting result: ' + url_result)

                try:
                    result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'


                    if 'action_request' in result_json:
                        if serial in result_json['action_request']:
                            action_request = result_json['action_request'][serial]

                            if action_request == 'update_at_next_chance':
                                # do it right here
                                do_datadrop_debug('OS Updates starting...')

                                output_file = "/cardinal/log/pi_security_lastupdate.txt"
                                if not os.path.exists(os.path.dirname(output_file)):
                                    os.makedirs(os.path.dirname(output_file))
                                with open(output_file, 'w') as f:
                                    f.write('')

                                # https://linux.die.net/man/8/apt-get
                                cmd = 'sudo DEBIAN_FRONTEND=noninteractive apt-get -y dist-upgrade -V --fix-missing'
                                pass_string, fail_string = do_one_command(cmd)
                                do_datadrop_debug('cmd: ' + cmd)
                                do_datadrop_debug('pass_string: ' + pass_string)
                                do_datadrop_debug('fail_string: ' + fail_string)

                                try:
                                    with open(output_file, 'a') as f:
                                        f.write('cmd:\n' + cmd + '\n')
                                        f.write('pass_string:\n' + pass_string + '\n')
                                        f.write('fail_string:\n' + fail_string + '\n')
                                except:
                                    do_datadrop_debug(traceback.format_exc())

                                cmd = 'sudo apt-get clean'
                                pass_string, fail_string = do_one_command(cmd)
                                do_datadrop_debug('cmd: ' + cmd)
                                do_datadrop_debug('pass_string: ' + pass_string)
                                do_datadrop_debug('fail_string: ' + fail_string)

                                try:
                                    with open(output_file, 'a') as f:
                                        f.write('cmd:\n' + cmd + '\n')
                                        f.write('pass_string:\n' + pass_string + '\n')
                                        f.write('fail_string:\n' + fail_string + '\n')
                                except:
                                    do_datadrop_debug(traceback.format_exc())


                                do_datadrop_debug('OS Updates completed.')
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())


    do_datadrop_debug('security data drop: End')

# ----------------------------
def do_maintenance():
# ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()



# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_security.cpython-37.pyc", "/cardinal/pi_security.pyc")

    if os.path.isfile("/cardinal/pi_security.py"):
        os.remove("/cardinal/pi_security.py")

    try:
        with open('/dev/shm/pi_security_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    do_one_time()

    wake_count = 0
    while True:
        minutes_counted = 0
        while (minutes_counted < 30):

            # this happens once a minute
            set_sshd_config()
            set_tcp_config()
            minutes_counted += 1

            # hang out for one minute
            time_now = time.time()
            while (abs(time_now - time.time()) < 60):
                # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
                time.sleep(1)
                wake_count += 1
                try:
                    with open('/dev/shm/pi_security_wake.txt', 'w') as f:
                        f.write(str(wake_count))
                except:
                    print ("!!! failed to write wake_count")

        # do system long term maintenance, up front, so report of version happens quickly
        do_maintenance()



# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_build_tcp_config_1(self):
        call_homes = ['https://slicer.cardinalhealth.net']
        expected = """# /etc/hosts.allow: list of hosts that are allowed to access the system.
#                   See the manual pages hosts_access(5) and hosts_options(5).
#
# Example:    ALL: LOCAL @some_netgroup
#             ALL: .foobar.edu EXCEPT terminalserver.foobar.edu
#
# If you're going to protect the portmapper use the name "rpcbind" for the
# daemon name. See rpcbind(8) and rpc.mountd(8) for further information.
#
sshd: slicer.cardinalhealth.net
"""
        actual = tcp_full_config(call_homes)
        self.assertEqual(expected, actual)

    def test_build_tcp_config_2(self):
        call_homes = ['https://slicer.cardinalhealth.net','https://test2.cardinalhealth.net']
        expected = """# /etc/hosts.allow: list of hosts that are allowed to access the system.
#                   See the manual pages hosts_access(5) and hosts_options(5).
#
# Example:    ALL: LOCAL @some_netgroup
#             ALL: .foobar.edu EXCEPT terminalserver.foobar.edu
#
# If you're going to protect the portmapper use the name "rpcbind" for the
# daemon name. See rpcbind(8) and rpc.mountd(8) for further information.
#
sshd: slicer.cardinalhealth.net
sshd: test2.cardinalhealth.net
"""
        actual = tcp_full_config(call_homes)
        self.assertEqual(expected, actual)























# End of File