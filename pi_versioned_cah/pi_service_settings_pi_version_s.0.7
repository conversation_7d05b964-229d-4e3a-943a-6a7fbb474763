service = 'settings'
version = 's.0.7'

s_wifi_iot_only = True # in both network, hmi, and settings

description = """
This is a pi service for setting organization specific content.

logo for use by hmi

let this be where psk and ssid get set into the config file
(instead of image building instructions doing it?).

"""

release_notes = """
2023.04.03
s.0.7
remove corp wifi certificate

2023.02.06
s.0.6
corp WiFi cert to 2024.02.06

2022.12.07
s.0.5
Make it so that organization pulls the settings

"""

# globals
s_corp_wifi_certs_path = "/cardinal/wifi_certs/"
s_corp_wifi_cert_last_used_path = "/cardinal/"

# ====================================
def get():
# ====================================
    return_value = {}

    return_value['added_users'] = ['opc']

    return_value['added_sshd'] = ['PVJZLSLC01.corp.cordis.com', '***********']

    return_value['logo_xxd'] = logo_xxd()

    return_value['wifi_cert'] = get_wifi_certs()

    return return_value

# ----------------------------
def get_wifi_certs():
# ----------------------------
    global s_corp_wifi_cert_in_use, s_corp_wifi_cert_just_loaded_in_use
    _ = """
2023.02.07
The original WiFi certificate was built on a Windows computer. The current one is built on a mac, using JAMF to complete the process.

Overview of the process:
    1) get the serial number of a mac
    2) set the User to the NSPIpad user
    3) Add the computer to the "no certs" group.
    4) Remove the computer from the "no certs" group. This causes them to be issued, using the current name.
    5) On the mac, in Keychain, export to p12 file
    6) set back to the original configuration
    7) Make the p12 file into xxd
    8) Add xxd to settings, and increment the version

Details:
    1) get the serial number of a mac (David.ferguson = C02F18MGMD6M)
    2) In JAMF, find that computer, in "User and Location" in the upper right,
        click "Edit", and set the Username to "<EMAIL>",
        click "Search", so that it fills in the Full Name and the Email Address,
        click "Save"
    3) In JAMF, in the left hand most panel, find "Static Computer Groups",
        right click, then "open in new tab",
        find
        "Remove Device From KF Policy - Use this to fix issues with KF deployments",
        select the tab "Assignments",
        in the lower right corner, click "Edit",
        in the "Filter Results" text box, paste in the serial number of the mac,
        in front of the line with the result, click the check box, so that it is checked,
        in the lower  right corner, click "Save",
            click back to the other tab, and select the tab "Management", and see that there
            might be a pending command, and wait for that to complete. (Refresh the page)
    4)  Go into the "Edit" mode on the Assignments page,
        in the "Filter Results" text box, paste in the serial number of the mac,
        in front of the line with the result, click the check box, so that it is un-checked,
        in the lower  right corner, click "Save",
            click back to the other tab, and select the tab "Management", and see that there
            might be a pending command, and wait for that to complete. (Refresh the page)

    5) On the Mac (close keychain if you had it open),
        open "keychain access",
        find "<EMAIL>",
        right click on it, and select "Export",
        browse to Downloads, make a folder with todays date, "********",
        set the "Save As" to "mac_export_1",
        set the file format to "Personal Information Exchange (.p12)",
        click "Save",
        Keychain Access might will pop up a request, click "Use Password...",
        it then prompts for your mac password, then type that, and continue.
        For the file password, use "GrsROYIW3WLVAWb"

    6) Go back and repeat steps 2, 3, 4 and set the user back to the original on the mac.

    7) Make the p12 file into xxd
        Open terminal on mac.
            cd ~/Downloads/********
            xxd -p mac_export_1.p12
        Select just the given output, and then "copy" to the clipboard.

    8) Add xxd to settings, and increment the version
        in the method "get_wifi_certs", go to the end of the "cert_contents" list,
        add a new entry with the expiration date as the index, like "20240206a"

            cert_contents_not_to_use['20240206a'] = {
                'password':'GrsROYIW3WLVAWb',
                'content':(tripple quoted content from the clipboard)
                }

@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
All below here is old; only keep it for reference
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

2022.10.17

Windows: (Fails to make private key exportable... something about policies?)
Make a new (fresh) cert:
Remote desktop to DW6888665558828
FA-OH085.NPSIpad
GrsROYIW3WLVAWb

te-CAH-RPI-User-TEMP-DO-NOT-USE-KF-93b44b28-44cd-4dea-aeb0-95cf12524ff5
or
OH085, NPSIpad

Start menu, control panel, search, hidden, File Explorer Options, Hidden Files and Folders, Show.

Explore, OH085, NPSIpad/AppData/Local/Microsoft/Credentials
right click, general, uncheck Read-Only, apply



Start menu, certmgr.msc, (do not open as control panel, use Microsoft Console Document)
Certificates, Personal, Certificates, "OH085, NPSIpad"
Right click, All Tasks, Advanced Operations, Renew This Certificate with the Same Key...
Next, Enroll, (Status: Succeeded), Finish
Right click (again), All Tasks, Export...
Next, (no private) Next, DER encoded, Next, save to
downloads/20221017a.cer
Finish

Open Teams, new message, attach the cer file.

Receive the file, and save in downloads folder on Mac.
Double click to open the file in keychain.
Find the cert in the list, right click, export,



Linux direct:
https://geekdudes.wordpress.com/2020/02/14/request-ssl-certificate-for-linux-machine-from-microsoft-certification-authority/


2022.01.26
back documenting the current wifi cert:

Account name: FA-OH085.NPSIpad

From the command that makes the wifi connection:
802-1x.identity "<EMAIL>"
802-1x.private-key-password "NgsA7cR3fqf?qnpA"
802-1x.private-key /cardinal/TempCert.p12

To see contents:
openssl pkcs12 -info  -in TempCert.p12
(enter the private key password from the connection line)
[It shows some details, then asks for "Enter PEM pass phrase:",
use the one from dwf rawNotes.docx file, search for "tag:dwf20220126a"]
(enter password)

    # made with "xxd -p TempCert2.p12" 2022.03.01 account name "FA-OH085.NPSIpad"

    # made by Bryan Runnels on March 1, 2022, expires...
    # cd "/Users/<USER>/OneDrive - Cardinal Health/RaspberryPi/certAsOf20220301"
    # rename pfx to p12
#    # openssl pkcs12 -in raspberrycert.pfx -nocerts -out private.key -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb
#    # openssl pkcs12 -export -out raspberrycert.p12 -inkey private.key -in certificate.cer -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb

check contents:
openssl pkcs12 -in /cardinal/TempCert.p12

Check expiration:
openssl pkcs12 -in /cardinal/TempCert.p12 -nokeys -passin pass:GrsROYIW3WLVAWb | openssl x509 -noout -enddate

then paste the one password for input and output
made with: xxd -p OH085NPSIpadFullchain.p12

Can we remove the passphrase requirement?
https://blog.armbruster-it.de/2010/03/remove-the-passphrase-from-a-pkcs12-certificate/

cd /cardinal/
sudo openssl pkcs12 -in /cardinal/TempCert.p12 -nodes -out temp.pem
sudo openssl pkcs12 -export -in temp.pem -out junk.p12
rm temp.pem
sudo chmod +r junk.p12

openssl pkcs12 -in /cardinal/junk.p12 -nokeys -passin pass: | openssl x509 -noout -enddate

2023.02.03
Try to make a new cert:
On Mac,

FA-OH085.NPSIpad (on cardinalhealth.net)
GrsROYIW3WLVAWb

ldapwhoami -h WPIL0219ADIDC02.cardinalhealth.net -D <EMAIL> -x -w GrsROYIW3WLVAWb -vvv

u:CARDINALHEALTH\FA-OH085.NPSIpad
Result: Success (0)

In JAMF, assign dave's mac to the ipad account, then pull/push certs (Ask Steve Murphy for details).
In keychain, export as mac_export_1.p12

cd ~/OneDrive\ -\ Cardinal\ Health/RaspberryPi/corp_wifi_cert/********
openssl pkcs12 -info  -in mac_export_1.p12

Check expiration:
openssl pkcs12 -in mac_export_1.p12 -nokeys -passin pass:GrsROYIW3WLVAWb | openssl x509 -noout -enddate

# make for software load:
xxd -p mac_export_1.p12




    """

    cert_contents = {}

    if s_wifi_iot_only:
        pass
    else:
        cert_contents['20240206a1'] = {
        'password':'GrsROYIW3WLVAWb',
        'content':"""30820d9702010330820d5e06092a864886f70d010701a0820d4f04820d4b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"""}

    return cert_contents

# ----------------------------
def logo_xxd():
# ----------------------------
    # downloaded : https://www.mycardinalhealth.net/media/1748/brand-guidelines-2020.pdf
    # then did a screen capture of just the area of the logo, pasted into Word,
    # right click on image: "save as picture" to gif, then did "xxd" of that file.

    # made with "xxd -p cardinal.gif"
    content = """47494638376118017a00e600000000001a1919201e1e2423232827272c2b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"""

    return content


other_content_to_convince_runner_we_are_a_service = """
# ===== begin: service file
# ===== begin: start file
"""

import os
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_settings.cpython-37.pyc", "/cardinal/pi_settings.pyc")

    if os.path.isfile("/cardinal/pi_settings.py"):
        os.remove("/cardinal/pi_settings.py")

    try:
        with open('/dev/shm/pi_settings_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    wake_count = 0
    while True:
        # do system maintenance

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_settings_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print ("!!! failed to write wake_count")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))

    def test_settings(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)

