_ = """
sudo vi /cardinal/pi_runner.py

sudo systemctl restart pi-runner.service

sudo systemctl status pi-runner.service

"""

service = 'runner'
version = 'R.5.9'

release_notes = """
2022.02.01
R.5.9
Add support for looking up the call_home_locations
Report on the status of the found services, if they are running ok, or fail.

2022.01.25
R.5.8
Make update to allow runner to be used on really old images.

2022.01.13
R.5.7
Keep a browser start count, to allow hmi service to know when to clear auto refresh settings.

2022.01.10
R.5.6
Manage the showgif content, to enable gif static/slideshow displays.

2022.01.07
R.5.5
Be able to add to the proxy bypass list based on profile content.

2022.01.07
R.5.4
Be able to update hosts file, based on data from the bookmarks. (Helps with DNS issues)

2021.12.29
R.5.3
Be able to set any conf_ configuration value(s) from Slicer. (First is the corp vs IOT wifi).
Send device time in to Slicer.

2021.12.22
R.5.2
Report on the response time for each datadrop report, as an indicator of network health.

2021.12.16
R.5.1
Pick up any shared data items,and pass them along to <PERSON>licer in our one minute report.
(This is how we are getting the live data feed from bluetooth logging.)

2021.12.12
R.5.0
Handle updates in priority order (runner last), so that all are completed on a bulk change.

2021.12.12
R.4.4
Handle the network bytes reporting roll over.

2021.12.10
R.4.3
Also report the count of 1K blocks on the disk most used.

2021.12.07
R.4.2
Send new data items: loadavg, disk_use, inode_use

2021.11.23
R.4.1
Make writes of all content be atomic (thread safe).

2021.11.23
R.4.0
Pull information fields from Slicer return data; like ring update count.

2021.10.29
R.3.9
Report wlan1 and eth1 mac addresses

2021.09.24
R.3.8
Monitor network utilisation, and report it to Slicer.

2021.09.23
R.3.7
Implement the "inactivity" measurement.

2021.09.23
R.3.6
Do scrot install in a new way.

2021.09.23
R.3.5
Pickup the browser_timeout value, and save it. Not ready to use it yet)
On screen grab, run the scrot install.
    On systems that already have it, it runs quick, and no change.
    On systems that do not have it, this will put it in place.

2021.09.20
R.3.4
do a once per update action to keep keyboard and mouse (usb) from going to sleep.

2021.09.16
R.3.1
Bump version, just to test pushing runner.

2021.09.09
R.3.0
When making the browser start file for tabs, contain each url in quotes,
so that a & does not get used as a command line character.
Do a correct pull of services from Slicer. (It got broken a while back)

2021.08.27
R.2.4
Add ability to to a screen grab.

2021.08.17
R.2.3
Making changes to correctly get full screen on Dell P2219H

2021.08.14
R.2.2
Set firewall rules (iptables) on startup, but still wide open for right now
Allow support for disabling kiosk mode on the browser.
Add delay at start, to get screen size settled

2021.07.20
R.2.1
Also send uptime in the checkin, to provide a faster update of that value in Slicer.
Create a boot_count, and relay that to Slicer.
React to a matching image:boot_count value of a reboot_request, and do a reboot.
Through bookmarks, allow the setting of allowing all keys to be active. ("allowkeys")

2021.07.12
R.2.0
Pull out special_menu setting
Do the pull of browser tab mode, and manage the start content, and browser restart.

2021.07.07
R.1.9
On screen_resolution change, build new settings, and reboot.

2021.06.23
R.1.8
On timezone change, restart the hmi process, so that it get the new local time setting.

2021.06.22
R.1.7
Process the privoxy log, to reduce it to a report.
Pick up the clockview enable value from Slicer, and save it locally.

2021.06.14
R.1.6
Pick up the bluetooth enable value from Slicer, and save it locally.

2021.05.28
R.1.5
Send the screen width and height with the datadrop.

2021.05.27
R.1.4
Extract the new field 'browser_zoom' from the datadrop response.

2021.05.25
R.1.3
Extract the new field 'name_to_use' from the datadrop response.

2021.05.19
R.1.2
Include (add) in datadrop:
    eth0mac => eth0/wired mac address
    timezone => the (hopefully) three letter time zone identifier

Actions:
    Set the timezone
        Once, set timezone to the saved value. If none, then set to UTC.
        When datadrop returns content, set to the datadrop return value. If none, then set to UTC.

2021.05.15
R.1.1
Do the bookmarks update here now.

2021.05.13
R.1.0
Start building out datadrop, as the communications channel to Slicer.

2021.04.27
R.0.9
Move the landing page creation to the hmi service

2021.04.14
R.0.8
Start building a landing page for the pi to display on startup:
    - show serial number (ID and IDr)

2021.03.13
R.0.7
Write a wake count, so that others can know that I am still alive.

R.0.6
Roll the version, just to test that monitor will re-send based on my version changing.

R.0.5
Convert sleep from 60 to 1.
Write version string for others to find.

R.0.4
Now made as a stand alone from the service.
"""

screen_stream_notes = """
2022.01.04

Use scrot to take a screen grab, and send to slicer as a 'last_frame'.
On Slicer, store that in /dev/shm/last_frame/(serial)
Open a Slicer page, that has javascript to pull that last_frame file, and display it. (update on change)
Having that Slicer window open is what alerts Slicer to mark the device for streaming.
Maybe the streamer runs as a separate service, and has an open websocket to talk to Slicer.

https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API
https://www.fullstackpython.com/websockets.html

Tornado: https://www.tornadoweb.org/en/stable/
https://stackoverflow.com/questions/13471115/how-to-handle-a-request-with-https-protocol-in-tornado


"""

description = """
This is a service to perform the audits on the device, and to report
findings.

This also performs updates to what is running, and can perform updates to make the
device reach "current release" in place.

Have runner do in place upgrade to 2.x based on if the version string is not yet 2.x.
    Install all apt-get and pip installs.
    Pull code from Slicer, install
    configure auto start for Chromium
    set new version string
    reboot


"""

other_content = """
sudo vi /cardinal/pi-runner
sudo chmod +x /cardinal/pi-runner

# ===== begin: start file
#!/usr/bin/env python3
import pi_runner
pi_runner.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-runner.service
sudo systemctl daemon-reload
sudo systemctl stop pi-runner.service
sudo systemctl start pi-runner.service
sudo systemctl enable pi-runner.service

systemctl status pi-runner.service

sudo systemctl restart pi-runner.service

# Logging of std out
cat /var/log/syslog | fgrep pi-runner

OR

sudo tail -n 1000 -f /var/log/syslog | fgrep pi-runner

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-runner
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""


import copy
import json
import math
import os
import requests
import shutil
import socket
import subprocess
import sys
import time
import traceback

from concurrent.futures import ThreadPoolExecutor
s_executor = ThreadPoolExecutor(100)
# line up all of the worker threads
s_monitored_devices = {}
s_time_last_active = time.time()
s_activity_since_reset = False
s_previous_report = {'response_time':0.0,'exception_count':0}

# ----------------------------
def write_content_if_different(content, output_file, with_execute=False):
# ----------------------------
    # This does a temp file, then atomic move to final
    if content:
        last_content_written = ''
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        if os.path.isfile(output_file):
            with open(output_file, 'r') as f:
                last_content_written = f.read()

        if content != last_content_written:
            temp_file = output_file + '.tmp' # must be on the same physical device as the destination, to be atomic on mv
            with open(temp_file, 'w') as f:
                f.write(content)


            print ("updating file: " + output_file)
            if with_execute:
                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)

            pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
            pass_string, fails = do_one_command('sudo mv ' + temp_file + ' ' + output_file)

# ----------------------------
def read_file(filename):
# ----------------------------
    with open(filename, 'rb') as f:
        return f.read(3)

# ----------------------------
def get_network_use_values():
# ----------------------------

    pass_string = """
eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
        inet **************  netmask 255.255.255.224  broadcast **************
        inet6 fe80::a197:aeb9:2858:d886  prefixlen 64  scopeid 0x20<link>
        ether dc:a6:32:96:56:c2  txqueuelen 1000  (Ethernet)
        RX packets 453108  bytes 223459013 (213.1 MiB)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 457509  bytes 57905346 (55.2 MiB)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0

lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
        inet 127.0.0.1  netmask *********
        inet6 ::1  prefixlen 128  scopeid 0x10<host>
        loop  txqueuelen 1000  (Local Loopback)
        RX packets 376332  bytes 250601806 (238.9 MiB)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 376332  bytes 250601806 (238.9 MiB)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0

wlan0: flags=4099<UP,BROADCAST,MULTICAST>  mtu 1500
        ether dc:a6:32:96:56:c4  txqueuelen 1000  (Ethernet)
        RX packets 0  bytes 0 (0.0 B)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 0  bytes 0 (0.0 B)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
    """

    _test = """
sudo python3
import pi_runner
pi_runner.get_network_use_values()
    """

    pass_string, fail_string = do_one_command('ifconfig')
    print (pass_string)
    print (fail_string)

    results = {'rxbytes':0, 'txbytes':0, 'time':time.time()}
    in_zone = False
    for line_read in pass_string.split('\n'):
        print ('in_zone', in_zone)
        if ('eth0' in line_read) or ('wlan0' in line_read):
            in_zone = True
        if ('lo:' in line_read):
            in_zone = False
        if in_zone:
            print ('line_read', line_read)
            if 'RX packets' in line_read:
                try:
                    bytes_shown = int(line_read.strip().split()[4])
                    results['rxbytes'] += bytes_shown
                except:
                    pass
            if 'TX packets' in line_read:
                try:
                    bytes_shown = int(line_read.strip().split()[4])
                    results['txbytes'] += bytes_shown
                except:
                    pass

    return results

# ----------------------------
def get_network_utilization_report():
# ----------------------------
    return_value = ''

    save_file = '/dev/shm/pi_runner_network_utilization_last'

    try:
        with open(save_file, 'r') as f:
            previous_values = json.loads(f.read())
    except:
        previous_values = None

    current_values = get_network_use_values()
    try:
        with open(save_file, 'w') as f:
            f.write(json.dumps(current_values))
    except:
        pass

    reportables = []

    if previous_values:
        try:
            delta_rx = current_values['rxbytes'] - previous_values['rxbytes']
            delta_tx = current_values['txbytes'] - previous_values['txbytes']

            #ignore the roll over
            if delta_rx < 0:
                delta_rx = 0
            if delta_tx < 0:
                delta_tx = 0

            reportables.append(str(delta_rx))
            reportables.append(str(delta_tx))
            reportables.append(str(int(current_values['time'] - previous_values['time'])))
        except:
            pass

    for item in reportables:
        return_value += '(' + str(item) + ')'

    return return_value

# ----------------------------
def get_image_version():
# ----------------------------
    image_version = "(missing)"
    try:
        with open('/cardinal/image_ver.txt', 'r') as f:
            content = f.read()
            # "Image version: 2.0.4"
        image_version = content.split(":")[1].strip()
    except:
        do_datadrop_debug(traceback.format_exc())

    return image_version

# ----------------------------
def get_boot_count():
# ----------------------------
    boot_count = '0'
    try:
        with open("/cardinal/boot_count.txt", "r") as f:
            boot_count = f.read()
    except:
        pass

    return boot_count

# ----------------------------
def get_grab_count():
# ----------------------------
    grab_count = '0'
    try:
        with open("/cardinal/grab_count.txt", "r") as f:
            grab_count = f.read()
    except:
        pass

    return grab_count

# ----------------------------
def increment_boot_count():
# ----------------------------
    output_file = "/cardinal/boot_count.txt"
    current_count = get_boot_count()
    new_count = str(int(current_count) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass

# ----------------------------
def increment_grab_count():
# ----------------------------
    output_file = "/cardinal/grab_count.txt"
    current_count = get_grab_count()
    new_count = str(int(current_count) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass

# ----------------------------
def get_highest_disk_usage():
# ----------------------------
    pass_string, fails = do_one_command('df -k')

    _ = """
Filesystem     1K-blocks    Used Available Use% Mounted on
/dev/root       14734612 2584796  11531888  19% /
devtmpfs         1800564       0   1800564   0% /dev
tmpfs            1965428   12456   1952972   1% /dev/shm
tmpfs            1965428  205632   1759796  11% /run
tmpfs               5120       4      5116   1% /run/lock
tmpfs            1965428       0   1965428   0% /sys/fs/cgroup
/dev/mmcblk0p1    258095   50227    207868  20% /boot
tmpfs             393084       0    393084   0% /run/user/1002
tmpfs             393084       0    393084   0% /run/user/1001
    """

    return_value = 0
    one_k_blocks = 0
    lines = pass_string.split('\n')
    for line in lines:
        splits = line.split()
        if len(splits) > 4:
            try:
                use_value = int(splits[4].replace('%',''))
                if use_value > return_value:
                    return_value = use_value
                    one_k_blocks = int(splits[1])
            except:
                pass

    return {'percent_used':return_value, 'one_k_blocks':one_k_blocks}

# ----------------------------
def get_highest_inode_usage():
# ----------------------------
    pass_string, fails = do_one_command('df -ih')

    _ = """
Filesystem     Inodes IUsed IFree IUse% Mounted on
/dev/root        912K   68K  844K    8% /
devtmpfs          83K   431   82K    1% /dev
tmpfs            163K    78  163K    1% /dev/shm
tmpfs            163K   639  163K    1% /run
tmpfs            163K     3  163K    1% /run/lock
tmpfs            163K    15  163K    1% /sys/fs/cgroup
/dev/mmcblk0p1      0     0     0     - /boot
tmpfs            163K    11  163K    1% /run/user/1002
tmpfs            163K    11  163K    1% /run/user/1001
    """

    return_value = 0
    lines = pass_string.split('\n')
    for line in lines:
        splits = line.split()
        if len(splits) > 4:
            try:
                use_value = int(splits[4].replace('%',''))
                if use_value > return_value:
                    return_value = use_value
            except:
                pass

    return return_value

# ----------------------------
def uptime():
# ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0

# ----------------------------
def loadavg():
# ----------------------------
    try:
        with open('/proc/loadavg', 'r') as f:
            return (f.readline().replace(' ','_').replace('\n',''))
    except:
        return ''

# ----------------------------
def build_boot_config():
# ----------------------------
    content = ""

    # Original defaults
    content += "dtparam=audio=on" + "\n"

    # ones we opened up
    content += "disable_overscan=1" + "\n"
    content += "hdmi_force_hotplug=1" + "\n"
    content += "" + "\n"

    # dynamically set items
    output_file = "/cardinal/localhtml/screen_resolution"
    try:
        resolution = get_my_screen_resolution()
        # resolution = name group mode valuesCommaSeparated
        # resolution = 1280x720r60 2 87 1280,720,60
        # resolution = VGA 1 1

        if resolution:
            group = resolution.split()[1]
            mode = resolution.split()[2]
            try:
                values = resolution.split()[3].replace(',',' ')
            except:
                values = ''

            content += "hdmi_group=" + group + "\n"
            content += "hdmi_mode=" + mode + "\n"
            if values:
                content += "hdmi_cvt " + values + "\n"
    except:
        pass

    # sectioned items after here
    content += "[pi4]" + "\n"
    content += "dtoverlay=vc4-fkms-v3d" + "\n"
    content += "max_framebuffers=2" + "\n"

    try:
        with open ('/boot/config.txt' , 'w') as f:
            f.write(content)
    except:
        pass

# ----------------------------
def do_privoxy_summary():
# ----------------------------

    """
Privoxy log:
/var/log/privoxy/logfile

(It will get shorter at times, when it rolls)

pi@cah-rp-10000000e3669edf:~ $ sudo ls -l /var/log/privoxy
total 80
-rw-r----- 1 <USER> <GROUP>     52635 Jun 21 14:50 logfile
-rw-r--r-- 1 <USER> <GROUP> 23719 Jun 19 17:44 logfile.1.gz
pi@cah-rp-10000000e3669edf:~ $


2021-06-21 14:46:08.852 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:47:11.861 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:47:11.863 b62ff460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:14.862 b62ff460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:14.862 b6c89460 Crunch: Blocked: www.google.com:443
2021-06-21 14:48:35.883 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:36.757 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:38.569 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443
2021-06-21 14:48:42.324 b4eff460 Crunch: Blocked: content-autofill.googleapis.com:443

with debug 1 set, you can also get:
2021-06-21 14:48:34.684 b6c89460 Request: mex03-dms.cardinalhealth.net:443/
2021-06-21 14:48:35.070 b62ff460 Request: cdn.jsdelivr.net:443/
2021-06-21 14:48:35.082 b58ff460 Request: cdn.datatables.net:443/
2021-06-21 14:48:35.087 b44ff460 Request: code.jquery.com:443/
2021-06-21 14:48:35.088 b3cfe460 Request: cdn.jsdelivr.net:443/
2021-06-21 14:48:35.089 b4eff460 Request: cdnjs.cloudflare.com:443/
2021-06-21 14:48:35.090 b30ff460 Request: cdn.datatables.net:443/
2021-06-21 14:48:35.091 b28fe460 Request: cdnjs.cloudflare.com:443/

cat /dev/shm/pi_runner_privoxy_summary.txt

"""

    lines = []
    try:
        with open ('/var/log/privoxy/logfile', 'r') as f:
            lines = f.readlines()
    except:
        pass

    to_ignores = []
    to_ignores.append('www.google.com')
    to_ignores.append('content-autofill.googleapis.com')
    to_ignores.append('update.googleapis.com')
    to_ignores.append('safebrowsing.googleapis.com')
    to_ignores.append('fonts.googleapis.com')
    to_ignores.append('www.gstatic.com')
    to_ignores.append('accounts.google.com')
    to_ignores.append('support.google.com')
    to_ignores.append('redirector.gvt1.com') # https://www.bleepingcomputer.com/news/security/what-are-these-suspicious-google-gvt1com-urls/
    to_ignores.append('ssl.gstatic.com') # Google static content
    # to_ignores.append('maxcdn.bootstrapcdn.com')

    report = {}
    for line_read in lines:
        if 'Blocked' in line_read:
            site = line_read.split()[-1]

            should_ignore = False

            # formatting like http://tfnkklroxbragen/
            if not '.' in site:
                should_ignore = True

            # explicit list
            for to_ignore in to_ignores:
                if to_ignore in site:
                    should_ignore = True

            if not should_ignore:
                if not site in report:
                    report[site] = 0
                report[site] += 1

    try:
        with open ('/dev/shm/pi_runner_privoxy_summary.txt', 'w') as f:
            f.write(json.dumps(report, indent=4, separators=(',',':')))
    except:
        pass

# ----------------------------
def screen_size():
# ----------------------------
    screen_width = '1920'
    screen_height = '1080'
    try:
        pass_string, fails = do_one_command('fbset -s')
        # pass_string = '\nmode "1280x800"\n    geometry 1280 800 1280 800 16\n    timings 0 0 0 0 0 0 0\n    accel true\n    rgba 5/11,6/5,5/0,0/0\nendmode\n\n'

        splits = pass_string.split('\n')
        for item in splits:
            #print ('item', item)
            parts = item.strip().split()
            #print ('parts', parts)
            if parts:
                if parts[0] == 'geometry':
                    screen_width = int(parts[1])
                    screen_height = int(parts[2])
                    #print (screen_width, screen_height)
    except:
        pass

    return screen_width, screen_height

# ----------------------------
def get_my_screen_resolution():
# ----------------------------
    input_file = "/cardinal/localhtml/screen_resolution"

    name_to_use = ''
    try:
        with open(input_file, 'r') as f:
            names_full = json.loads(f.read())
        serial = get_serial()
        if serial in names_full:
            name_to_use = copy.deepcopy(names_full[serial])
    except:
        pass

    return name_to_use

# ----------------------------
def get_my_special_menu():
# ----------------------------
    input_file = "/cardinal/localhtml/special_menu"

    name_to_use = ''
    try:
        with open(input_file, 'r') as f:
            names_full = json.loads(f.read())
        serial = get_serial()
        if serial in names_full:
            name_to_use = copy.deepcopy(names_full[serial])
    except:
        pass

    return name_to_use

# ----------------------------
def get_my_browser_timeout():
# ----------------------------
    input_file = "/cardinal/localhtml/browser_timeout"
    browser_timeout = '0'
    try:
        with open(input_file, 'r') as f:
            content_full = json.loads(f.read())
        serial = get_serial()
        if serial in content_full:
            browser_timeout = copy.deepcopy(content_full[serial])
    except:
        pass

    return browser_timeout

# ----------------------------
def get_my_bookmarks():
# ----------------------------
    input_file = "/cardinal/localhtml/bookmarks"
    bookmarks = {}
    try:
        with open(input_file, 'r') as f:
            bookmarks_full = json.loads(f.read())
        serial = get_serial()
        if serial in bookmarks_full:
            bookmarks = copy.deepcopy(bookmarks_full[serial])

    except:
        pass

    return bookmarks

# ----------------------------
def check_for_inactivity():
# ----------------------------
    global s_monitored_devices, s_time_last_active, s_activity_since_reset

    browser_timeout_minutes = get_my_browser_timeout()

    try:
        with open('/dev/shm/pir_runner_browser_timeout', 'w') as f:
            f.write(browser_timeout_minutes)
    except:
        pass

    # see if there are new devices to monitor
    try:
        device_dir = '/dev/input/'
        for device_name in os.listdir(device_dir):
            if ('event' in device_name) or ('mouse' in device_name):
                full_device_path = device_dir + device_name
                if not full_device_path in s_monitored_devices:
                    s_monitored_devices[full_device_path] = {'executor':None, 'is_done':True} # Make the arrival of the new device be an activity.

                    #job_id_thread1 = sched.add_job(thread_call_back_to_slicer, 'interval', seconds=30, coalesce=True)
                    # https://thehackerdiary.wordpress.com/tag/devinput-python/
                    # https://stackoverflow.com/questions/39948588/non-blocking-file-read/39948643

        # run through all the monitored devices, and if they are done, start a new executor
        any_active = False
        report_what = ''
        for monitored_device in sorted(s_monitored_devices):
            if s_monitored_devices[monitored_device]['executor']:
                s_monitored_devices[monitored_device]['is_done'] = s_monitored_devices[monitored_device]['executor'].done()

            if s_monitored_devices[monitored_device]['is_done']:
                report_what += monitored_device + ' -> active' + '\n'
                if not s_monitored_devices[monitored_device]['executor'] is None: # ignore the initialization pass
                    any_active = True
                try:
                    del(s_monitored_devices[monitored_device]['executor'])
                except:
                    with open ('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
                        f.write(traceback.format_exc())

                try:
                    s_monitored_devices[monitored_device]['executor'] = s_executor.submit(read_file, monitored_device)
                except:
                    with open ('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
                        f.write(traceback.format_exc())
            else:
                report_what += monitored_device + ' -> inactive' + '\n'

        if any_active:
            s_time_last_active = time.time()
            s_activity_since_reset = True

        delta_time = time.time()-s_time_last_active
        compare_seconds = int(browser_timeout_minutes) * 60

        with open('/dev/shm/pi_runner_active_monitor_time_since', 'w') as f:
            f.write(str(delta_time) + '\n')
            f.write(str(browser_timeout_minutes) + ' minutes\n')
            f.write(str(compare_seconds) + ' compare_seconds\n')
            f.write(str(s_activity_since_reset) + ' s_activity_since_reset\n')
            f.write(report_what + '\n')

        if int(browser_timeout_minutes) > 0:
            if (compare_seconds < delta_time) and s_activity_since_reset:
                # do the reset
                kill_browser()

                # and wait another interval before doing it again
                s_time_last_active = time.time()

                #
                s_activity_since_reset = False

                with open('/dev/shm/pi_runner_active_monitor_time_since', 'w') as f:
                    f.write(str(delta_time) + ' was reset' + '\n')
                    f.write(str(browser_timeout_minutes) + ' minutes\n')
                    f.write(str(s_activity_since_reset) + ' s_activity_since_reset\n')
                    f.write(report_what + '\n')

#  watch -n 1 cat /dev/shm/pi_runner_active_monitor_time_since

    except:
        with open ('/dev/shm/pi_runner_active_monitor_exception', 'w') as f:
            f.write(traceback.format_exc())

# ----------------------------
def check_hosts_for_update():
# ----------------------------
    output_file = '/etc/hosts'

    content = """
127.0.0.1  localhost
::1        localhost ip6-localhost ip6-loopback
ff02::1    ip6-allnodes
ff02::2    ip6-allrouters
*********  """ + make_host_name()

    hosts_to_add = []
    bookmarks = get_my_bookmarks()
    for key in sorted(bookmarks):
        if 'hosts' in bookmarks[key]:
            for host in bookmarks[key]['hosts']:
                if not host in hosts_to_add:
                    hosts_to_add.append(host)
    for host in hosts_to_add:
        content += '\n' + host
    content += '\n'

    write_content_if_different(content, output_file)


    _ = """
Test adding a name resolve, for Troy McDaniel

sudo vi /etc/hosts
(Add line to end)
********** wpec5009scsql10

ping wpec5009scsql10
    """

# ----------------------------
def check_browser_for_restart():
# ----------------------------
    # see if the browser requires a restart
    screen_width, screen_height = screen_size()
    try:
        temp_file = "/cardinal/browserstart"
        start_content_before = ""
        with open(temp_file, 'r') as f:
            start_content_before = f.read()

        start_content_after = build_browser_start_content(get_my_bookmarks(), screen_width, screen_height)

        if start_content_before:
            if start_content_before != start_content_after:
                # do the update, and kill the browser
                with open(temp_file, 'w') as f:
                    f.write(start_content_after)
                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)
                pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
                kill_browser()
    except:
        pass

# ----------------------------
def get_browser_start_count():
# ----------------------------
    _ = """
cd /cardinal
python3 -c 'import pi_runner; print (pi_runner.get_browser_start_count())'
    """

    the_count = '0'
    try:
        with open("/dev/shm/pi_runner_browser_start_count.txt", "r") as f:
            the_count = f.read()
    except:
        pass

    return the_count

# ----------------------------
def increment_browser_start_count():
# ----------------------------
    _ = """
cd /cardinal
python3 -c 'import pi_runner; print (pi_runner.increment_browser_start_count())'
    """
    output_file = "/dev/shm/pi_runner_browser_start_count.txt"
    current_count = get_browser_start_count()
    new_count = str(int(current_count) + 1)

    try:
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, "w") as f:
            f.write(new_count)
    except:
        pass

# ----------------------------
def build_browser_start_content(bookmarks, screen_width, screen_height):
# ----------------------------
    format_to_use = 1
    keys_settings = """
xmodmap -e "keycode 37="
xmodmap -e "keycode 67="
xmodmap -e "keycode 105="
xmodmap -e "keycode 133="
xmodmap -e "keycode 134="
"""
    kiosk_mode_string = "--kiosk "


    for key in sorted(bookmarks):
        if 'format' in bookmarks[key]:
            if bookmarks[key]['format'] == 'tabs':
                format_to_use = 2

        if 'allowkeys' in bookmarks[key]:
            if bookmarks[key]['allowkeys'] == 'all':
                keys_settings = "setxkbmap -layout us" + "\n" # this clears the image setup of blocking ctrl and other keys.

        if 'disablekiosk' in bookmarks[key]:
            if bookmarks[key]['disablekiosk'] == 'yes':
                kiosk_mode_string = ''

    content = keys_settings

    content += """python3 -c "import sys; sys.path.append('/cardinal'); import pi_runner; pi_runner.increment_browser_start_count()\"""" + "\n"

    content += "cp /cardinal/localhtml/chromium_Default_Preferences /home/<USER>/.config/chromium/Default/Preferences" + "\n"

    # https://peter.sh/experiments/chromium-command-line-switches/
    content += "chromium-browser \\" + "\n"
    content += """      --proxy-server="https=127.0.0.1:8118;http=127.0.01:8118" \\""" + "\n"

    bypass_to_add = []
    for key in sorted(bookmarks):
        if 'bypass' in bookmarks[key]:
            for host in bookmarks[key]['bypass']:
                if not host in bypass_to_add:
                    bypass_to_add.append(host)

    if bypass_to_add:
        content += """      --proxy-bypass-list=\"""" + ','.join(bypass_to_add) + """\" \\""" + "\n"

    content += "      --window-size=""" + str(screen_width) + "," + str(screen_height) + " \\" + "\n"
    content += "      --window-position=0,0 \\" + "\n"
    content += "      --start-fullscreen \\" + "\n"
    content += "      --incognito \\" + "\n"
    content += "      --noerrdialogs \\" + "\n"
    content += "      --disable-translate \\" + "\n"
    content += "      --no-first-run \\" + "\n"
    content += "      --fast \\" + "\n"
    content += "      --fast-start \\" + "\n"
    content += "      --disable-infobars \\" + "\n"
    content += "      --disable-features=TranslateUI \\" + "\n"
    content += "      --disable-features=Translate \\" + "\n"
    content += "      --disk-cache-dir=/dev/null \\" + "\n"
    content += "      --overscroll-history-navigation=0 \\" + "\n"
    content += "      --disable-pinch \\" + "\n"
    content += kiosk_mode_string

    if format_to_use == 1:
        content += '"file:///cardinal/localhtml/index.html"'

    if format_to_use == 2:
        for key in sorted(bookmarks):
            content += ' "' + bookmarks[key]['url'] + '" '

    return content


# ----------------------------
def get_my_tab_mode():
# ----------------------------
    input_file = "/cardinal/localhtml/browser_zoom"

    zoom_to_use = '100'
    try:
        with open(input_file, 'r') as f:
            zooms_full = json.loads(f.read())
        serial = get_serial()
        if serial in zooms_full:
            zoom_to_use = copy.deepcopy(zooms_full[serial])
    except:
        pass

    return zoom_to_use

# ----------------------------
def get_my_zoom():
# ----------------------------
    input_file = "/cardinal/localhtml/browser_zoom"

    zoom_to_use = '100'
    try:
        with open(input_file, 'r') as f:
            zooms_full = json.loads(f.read())
        serial = get_serial()
        if serial in zooms_full:
            zoom_to_use = copy.deepcopy(zooms_full[serial])
    except:
        pass

    return zoom_to_use

# ----------------------------
def kill_browser():
# ----------------------------
    try:
        pass_string, fail_string = do_one_command("ps ax") # | fgrep chromium-browser | fgrep cardinal | cut -d ' ' -sf1")

        process_number = ''
        for item in pass_string.split("\n"):
            if 'chromium-browser' in item:
                if 'cardinal' in item:
                    process_number = item.split()[0].strip()

        if process_number:
            # 14373
            pass_string, fail_string = do_one_command("sudo kill " + process_number)
    except:
        print(traceback.format_exc())


# ----------------------------
def set_browser_zoom_level(zoom_level):
# ----------------------------
    notes = """
scale    chrome value
 0.50   -3.****************
 0.75   -1.****************
 0.80   -1.****************
 0.90   -0.****************
1.00    0.0
1.10    0.****************
1.25    1.****************
1.50    2.***************
X       Y

Y ~= 5.4848 ln(X)

# https://community.volumio.org/t/how-to-preset-chromium-browser-scale-zoom-factor/38944/2
Y = log(X, 1.2)

    """

    try:
        scale = float(zoom_level) / 100.0

    #    original_file = "/home/<USER>/.config/chromium/Default/Preferences"
    #    original = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"Discover great apps,\n games,\n extensions and themes for Chromium.","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":"[\"gaia.l.a.r\",\n[]\n]\n"},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":-2.0},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Crashed","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""
    #    trimmed = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":""},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":-2.0},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Crashed","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""

        first_part = """{"account_id_migration_state":2,"account_tracker_service_last_update":"*****************","alternate_error_pages":{"backup":true},"autocomplete":{"retention_policy_last_version":88},"autofill":{"last_version_validated":88,"orphan_rows_removed":true},"browser":{"window_placement":{"bottom":799,"left":0,"maximized":false,"right":1279,"top":0,"work_area_bottom":800,"work_area_left":0,"work_area_right":1280,"work_area_top":0}},"countryid_at_install":18242,"data_reduction":{"daily_original_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"daily_received_length":["0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","3296298","291873","151669","32098","266294","234435","337408","50972","50963","50981","154383","480640","258298","20396","443211","453281"],"last_update_date":"13266568800000000","last_week_services_downstream_background_kb":{},"last_week_services_downstream_foreground_kb":{"112189210":768,"21785164":0,"54845618":770,"6019475":486},"last_week_user_traffic_contenttype_downstream_kb":{},"this_week_number":2682,"this_week_services_downstream_background_kb":{},"this_week_services_downstream_foreground_kb":{"112189210":403,"21785164":0,"54845618":110},"this_week_user_traffic_contenttype_downstream_kb":{}},"default_apps_install_state":3,"domain_diversity":{"last_reporting_timestamp":"13266585162955778"},"download":{"directory_upgrade":true},"extensions":{"alerts":{"initialized":true},"chrome_url_overrides":{},"last_chrome_version":"88.0.4324.187","pinned_extension_migration":true,"pinned_extensions":[],"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":{"active_permissions":{"api":["management","system.display","system.storage","webstorePrivate","system.cpu","system.memory","system.network"],"manifest_permissions":[]},"app_launcher_ordinal":"t","commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050237824","location":5,"manifest":{"app":{"launch":{"web_url":"https://chrome.google.com/webstore"},"urls":["https://chrome.google.com/webstore"]},"description":"","icons":{"128":"webstore_icon_128.png","16":"webstore_icon_16.png"},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB","name":"Web Store","permissions":["webstorePrivate","management","system.cpu","system.display","system.memory","system.network","system.storage"],"version":"0.2"},"needs_sync":true,"page_ordinal":"n","path":"/usr/lib/chromium-browser/resources/web_store","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"kmendfapggjehodndflmmgagdbamhnfd":{"active_permissions":{"api":["cryptotokenPrivate","externally_connectable.all_urls","tabs"],"explicit_host":["http://*/*","https://*/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050340945","location":5,"manifest":{"background":{"persistent":false,"scripts":["util.js","b64.js","cbor.js","sha256.js","timer.js","countdown.js","countdowntimer.js","devicestatuscodes.js","approvedorigins.js","errorcodes.js","webrequest.js","messagetypes.js","factoryregistry.js","requesthelper.js","asn1.js","enroller.js","requestqueue.js","signer.js","origincheck.js","textfetcher.js","appid.js","watchdog.js","logging.js","webrequestsender.js","window-timer.js","cryptotokenorigincheck.js","cryptotokenapprovedorigins.js","inherits.js","individualattest.js","googlecorpindividualattest.js","cryptotokenbackground.js"]},"description":"CryptoToken Component Extension","externally_connectable":{"ids":["fjajfjhkeibgmiggdfehjplbhmfkialk"],"matches":["https://*/*"]},"incognito":"split","key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB","manifest_version":2,"name":"CryptoTokenExtension","permissions":["cryptotokenPrivate","externally_connectable.all_urls","tabs","https://*/*","http://*/*"],"version":"0.9.74"},"path":"/usr/lib/chromium-browser/resources/cryptotoken","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mfehgcgbbipciphmccgaenjidiccnmng":{"active_permissions":{"api":["cloudPrintPrivate"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050234437","location":5,"manifest":{"app":{"launch":{"web_url":"https://www.google.com/cloudprint"},"urls":["https://www.google.com/cloudprint/enable_chrome_connector"]},"description":"Cloud Print","display_in_launcher":false,"icons":{},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDqOhnwk4+HXVfGyaNsAQdU/js1Na56diW08oF1MhZiwzSnJsEaeuMN9od9q9N4ZdK3o1xXOSARrYdE+syV7Dl31nf6qz3A6K+D5NHe6sSB9yvYlIiN37jdWdrfxxE0pRYEVYZNTe3bzq3NkcYJlOdt1UPcpJB+isXpAGUKUvt7EQIDAQAB","name":"Cloud Print","permissions":["cloudPrintPrivate"],"version":"0.1"},"path":"/usr/lib/chromium-browser/resources/cloud_print","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"mhjfbmdgcfjbbpaeojofohoefgiehjai":{"active_permissions":{"api":["contentSettings","fileSystem","fileSystem.write","metricsPrivate","resourcesPrivate"],"explicit_host":["chrome://resources/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":[],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050240908","location":5,"manifest":{"content_security_policy":"script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:; plugin-types application/x-google-chrome-pdf","description":"","incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB","manifest_version":2,"mime_types":["application/pdf"],"mime_types_handler":"index.html","name":"Chromium PDF Viewer","offline_enabled":true,"permissions":["chrome://resources/","contentSettings","metricsPrivate","resourcesPrivate",{"fileSystem":["write"]}],"version":"1"},"path":"/usr/lib/chromium-browser/resources/pdf","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"nkeimhogjdpnpccoofpliimaahmaaome":{"active_permissions":{"api":["desktopCapture","processes","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate","system.cpu","enterprise.hardwarePlatform"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":1,"events":["runtime.onConnectExternal"],"from_bookmark":false,"from_webstore":false,"incognito_content_settings":[],"incognito_preferences":{},"install_time":"13265738050338000","location":5,"manifest":{"background":{"page":"background.html","persistent":false},"externally_connectable":{"matches":["https://*.google.com/*","*://localhost/*"]},"incognito":"split","key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB","manifest_version":2,"name":"Google Hangouts","permissions":["desktopCapture","enterprise.hardwarePlatform","processes","system.cpu","webrtcAudioPrivate","webrtcDesktopCapturePrivate","webrtcLoggingPrivate"],"version":"1.3.15"},"path":"/usr/lib/chromium-browser/resources/hangout_services","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":false,"was_installed_by_oem":false},"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"ack_external":true,"active_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"commands":{},"content_settings":[],"creation_flags":137,"events":["identity.onSignInChanged","runtime.onStartup","runtime.onSuspend","settingsPrivate.onPrefsChanged"],"from_bookmark":false,"from_webstore":true,"granted_permissions":{"api":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","identity","identity.email","management","mediaRouterPrivate","metricsPrivate","mdns","networkingPrivate","processes","settingsPrivate","storage","tabs","tabCapture","webview","system.cpu"],"explicit_host":["http://*/*","https://*.google.com/*","https://hangouts.google.com/*"],"manifest_permissions":[]},"has_declarative_rules":{"declarativeContent":{"onPageChanged":false},"declarativeWebRequest":{"onRequest":false}},"incognito_content_settings":[],"incognito_preferences":{},"install_time":"*****************","lastpingday":"*****************","location":10,"manifest":{"background":{"persistent":false,"scripts":["common.js","mirroring_common.js","background_script.js"]},"content_security_policy":"default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; script-src 'self' https://apis.google.com https://feedback.googleusercontent.com https://www.google.com https://www.gstatic.com; child-src https://accounts.google.com https://content.googleapis.com https://www.google.com; connect-src 'self' http://*:* https://*:*; font-src https://fonts.gstatic.com; object-src 'self';","current_locale":"en_US","default_locale":"en","description":"Provider for discovery and services for mirroring of Chrome Media Router","externally_connectable":{"ids":["idmofbkcelhplfjnmmdolenpigiiiecc","ggedfkijiiammpnbdadhllnehapomdge","njjegkblellcjnakomndbaloifhcoccg"]},"key":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNTWJoPZ9bT32yKxuuVa9LSEYobjPoXCLX3dgsZ9djDrWKNikTECjdRe3/AFXb+v8jkmmtYQPnOgSYn06J/QodDlCIG6l470+gkOoobUM7fOs1AVOse23qYUV4jbuRW3+YZlCvaWCFeczCNbGIUgKEi5B2fyQazy60AL1sLW3utQIDAQAB","manifest_version":2,"minimum_chrome_version":"37","name":"Chrome Media Router","oauth2":{"client_id":"************-55j965o0km033psv3i9qls5mo3qtdrb0.apps.googleusercontent.com","scopes":["https://www.googleapis.com/auth/calendar.readonly","https://www.googleapis.com/auth/hangouts","https://www.googleapis.com/auth/hangouts.readonly","https://www.googleapis.com/auth/meetings","https://www.googleapis.com/auth/userinfo.email"]},"permissions":["alarms","cast","declarativeWebRequest","desktopCapture","gcm","http://*/*","identity","identity.email","management","mdns","mediaRouterPrivate","metricsPrivate","networkingPrivate","processes","storage","system.cpu","settingsPrivate","tabCapture","tabs","webview","https://hangouts.google.com/*","https://*.google.com/cast/chromecast/home/<USER>"],"update_url":"https://clients2.google.com/service/update2/crx","version":"8820.1109.0.1","web_accessible_resources":["cast_sender.js"]},"path":"pkedcjkdefgpdelpbcmbmeomcjbeemfm/8820.1109.0.1_0","preferences":{},"regular_only_preferences":{},"state":1,"was_installed_by_default":true,"was_installed_by_oem":false}}},"gaia_cookie":{"changed_time":**********.126995,"hash":"2jmj7l5rSw0yVb/vlWAYkK/YBwk=","last_list_accounts_data":""},"gcm":{"product_category_for_subtypes":"org.chromium.linux"},"google":{"services":{"signin_scoped_device_id":"e862cbab-48ec-4218-8b24-d06488f5706e"}},"http_original_content_length":"6573200","http_received_content_length":"6573200","invalidation":{"per_sender_topics_to_handler":{"*************":{},"**********":{}}},"media":{"device_id_salt":"241A99BB5A70D044B4C39B5F86F12FDC","engagement":{"schema_version":4}},"media_router":{"receiver_id_hash_token":"keekAhgkhg7bKhrP0xrGrj0yPNG+NNecDMCQtW9TW9E+FkLCIPBHm6FI8+TwwiNIvGSo25lBulsoldcpEvq3YQ=="},"ntp":{"num_personal_suggestions":1},"partition":{"default_zoom_level":{"x":"""
        second_part = """},"per_host_zoom_levels":{"x":{}}},"pinned_tabs":[],"plugins":{"plugins_list":[]},"previews":{"litepage":{"user-needs-notification":false}},"profile":{"avatar_bubble_tutorial_shown":2,"avatar_index":26,"content_settings":{"enable_quiet_permission_ui_enabling_method":{"notifications":1},"exceptions":{"accessibility_events":{},"app_banner":{},"ar":{},"auto_select_certificate":{},"automatic_downloads":{},"autoplay":{},"background_sync":{},"bluetooth_chooser_data":{},"bluetooth_guard":{},"bluetooth_scanning":{},"camera_pan_tilt_zoom":{},"client_hints":{},"clipboard":{},"cookies":{},"durable_storage":{},"file_system_last_picked_directory":{},"file_system_read_guard":{},"file_system_write_guard":{},"font_access":{},"geolocation":{},"hid_chooser_data":{},"hid_guard":{},"idle_detection":{},"images":{},"important_site_info":{},"insecure_private_network":{},"installed_web_app_metadata":{},"intent_picker_auto_display":{},"javascript":{},"legacy_cookie_access":{},"media_engagement":{},"media_stream_camera":{},"media_stream_mic":{},"midi_sysex":{},"mixed_script":{},"nfc":{},"notifications":{},"password_protection":{},"payment_handler":{},"permission_autoblocking_data":{},"permission_autorevocation_data":{},"popups":{},"ppapi_broker":{},"protocol_handler":{},"safe_browsing_url_check_data":{},"sensors":{},"serial_chooser_data":{},"serial_guard":{},"site_engagement":{},"sound":{},"ssl_cert_decisions":{},"storage_access":{},"subresource_filter":{},"subresource_filter_data":{},"usb_chooser_data":{},"usb_guard":{},"vr":{},"window_placement":{}},"pref_version":1},"created_by_version":"88.0.4324.187","creation_time":"*****************","exit_type":"Crashed","exited_cleanly":true,"last_time_obsolete_http_credentials_removed":**********.851376,"managed_user_id":"","name":"Person 1","password_account_storage_exists":true,"password_account_storage_settings":{}},"protection":{"macs":{"browser":{"show_home_button":"904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"},"default_search_provider_data":{"template_url_data":"575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"},"extensions":{"settings":{"ahfgeienlihckogmohjhadlkjgocpleb":"B0AF2B8DD1A304A5AAE38D6D3820E6B0743D9B1A9D88DCC9AB64DB5B458FB2FE","kmendfapggjehodndflmmgagdbamhnfd":"D14ACFB91F14A641742CAA533253BB7A669F3AC7DCE35AB302241C02D1EC3172","mfehgcgbbipciphmccgaenjidiccnmng":"8F5BF73A506B23CB7D56715866744A0D4BB0E8FA9C5D1DE430BC65C92FDF3784","mhjfbmdgcfjbbpaeojofohoefgiehjai":"FD6EB3B1CB8F6131414ED95A118E019F62AEF55F2F2969C24EA5497662E9FEDB","nkeimhogjdpnpccoofpliimaahmaaome":"79139666FBD0DFC00BFBE4B1F507FA120D88B4DDC0C0720EF005C434C4E4F88D","pkedcjkdefgpdelpbcmbmeomcjbeemfm":"A7E44AB63266642CD6258D27AA34491243C27590C2B94DB370136F75F372CC59"}},"google":{"services":{"account_id":"E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486","last_account_id":"6C67156FD15665D53CD24B5098D16B462BA8B8A0EFDD969A317C3235E973A4A3","last_username":"24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}},"homepage":"B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E","homepage_is_newtabpage":"3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119","media":{"storage_id_salt":"E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"},"pinned_tabs":"699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8","prefs":{"preference_reset_time":"95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"},"safebrowsing":{"incidents_sent":"569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"},"search_provider_overrides":"1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD","session":{"restore_on_startup":"F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E","startup_urls":"8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}},"safebrowsing":{"metrics_last_log_time":"13266609794"},"signin":{"DiceMigrationComplete":true,"allowed":true},"spellcheck":{"dictionaries":["en-US"],"dictionary":""},"token_service":{"dice_compatible":true},"unified_consent":{"migration_state":10},"updateclientdata":{"apps":{"pkedcjkdefgpdelpbcmbmeomcjbeemfm":{"cohort":"1::","cohortname":"","dlrc":5250,"pf":"f36089f3-dbf2-4d5c-bf53-77ec1dbe1213"}}},"web_apps":{"last_preinstall_synchronize_version":"88","system_web_app_failure_count":0,"system_web_app_last_attempted_language":"en-US","system_web_app_last_attempted_update":"88.0.4324.187","system_web_app_last_installed_language":"en-US","system_web_app_last_update":"88.0.4324.187"}}"""

        value_string_to_use = str(math.log(float(scale), 1.2))

        content = first_part + value_string_to_use + second_part

        output_file = "/cardinal/localhtml/chromium_Default_Preferences"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo chown -R worker:worker ' + output_file)

    except:
        print(traceback.format_exc())

# ====================================
def rot13(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz"
    trans = chars[13:]+chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ----------------------------
def get_cpu_temperature():
# ----------------------------
    pass_string, fail_string = do_one_command('vcgencmd measure_temp')
    return pass_string.split('=')[1].split("'")[0]

# ----------------------------
def get_my_showgif_md5():
# ----------------------------
    input_file = "/cardinal/localhtml/showgif_md5"

    value_to_use = ''
    try:
        with open(input_file, 'r') as f:
            value_full = json.loads(f.read())
        serial = get_serial()
        if serial in value_full:
            value_to_use = copy.deepcopy(value_full[serial])
    except:
        pass

    return value_to_use

# ----------------------------
def make_showgif_html():
# ----------------------------
    output_file = '/cardinal/localhtml/showgif.html'
    content = """
<!DOCTYPE html>
<html>
<head>
<style>
body {
  background-image: url('showgif.gif');
  background-attachment: fixed;
  background-size: 100% 100%;
}
</style>
</head>
</html>
    """

    write_content_if_different(content, output_file, with_execute=False)

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"] OR ['https://slicer.systems']
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def get_server_file_to_local(file_to_get, file_to_save):
# ----------------------------
    # reach out to Slicer, get the file, then do the work
    _ = """
on test pi:
cd /cardinal
sudo python3
import pi_runner
pi_runner.get_server_file_to_local('slideshow.gif','/cardinal/localhtml/showgif.gif')

sudo vi showgif.html
<!DOCTYPE html>
<html>
<head>
<style>
body {
  background-image: url('showgif.gif');
  background-attachment: fixed;
  background-size: 100% 100%;
}
</style>
</head>
</html>


    """
    try:
        the_site_to_call = call_home_locations()[0]
        the_request_url = the_site_to_call + '/download?' + 'filetodownload=' + file_to_get
        output_file = file_to_save

        response = requests.get(the_request_url, verify=False, timeout=45.0)

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        if response.status_code == 200:
            with open(output_file, 'wb') as f:
                f.write(response.content)
    except:
        print ('download failed')
        print (traceback.format_exc())

# ----------------------------
def get_service(service_name, service_version):
# ----------------------------
    # reach out to Slicer, get the file, then do the work
    try:
        the_site_to_call = call_home_locations()[0]
        the_request_url = the_site_to_call + '/download?' + 'service=' + service_name + ',version=' + service_version

        r = requests.get(the_request_url, verify=False, timeout=15.0)
        url_result = r.text

        service_content = rot13(url_result)

        # make sure it really is a service file
        is_service_file = True
        reason_not_service = ''

        test_strings = ['version =', 'release_notes =', '# ===== begin: start file', '# ===== begin: service file', 'def main():']
        for test_string in test_strings:
            if not test_string in service_content:
                is_service_file = False
                reason_not_service += "no:" + test_string

#        with open('/dev/shm/reason_not_service', 'w') as f:
#            f.write(reason_not_service)

        if is_service_file:
            # for initial debug
            service_code_file = '/cardinal/' + service_name + '.py'
            service_start_file = '/cardinal/' + service_name.replace('_','-')
            service_full_name = service_name.replace('_','-') + '.service'
            service_conf_file = '/lib/systemd/system/' + service_full_name

            start_content = ""
            start_content += "#!/usr/bin/env python3\n"
            start_content += "import " + service_name + "\n"
            start_content += service_name + ".main()\n"

            service_conf = """
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=""" + service_start_file + """
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
            """



            with open(service_code_file, 'w') as f:
                f.write(service_content)

            with open(service_start_file, 'w') as f:
                f.write(start_content)

            do_one_command('sudo chmod +x ' + service_start_file)

            with open(service_conf_file, 'w') as f:
                f.write(service_conf)

            do_one_command('sudo systemctl daemon-reload')
            if service_name == 'pi_runner':
                do_one_command('sudo systemctl restart ' + service_full_name)
            else:
                do_one_command('sudo systemctl stop ' + service_full_name)
                do_one_command('sudo systemctl start ' + service_full_name)
            do_one_command('sudo systemctl enable ' + service_full_name)

    except:
        pass
        with open ('/dev/shm/pi_runner_get_service_exception', 'w') as f:
            f.write(traceback.format_exc())


# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")
    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)

# ----------------------------
def make_host_name():
# ----------------------------
    serial = get_serial()
    host_name = 'cah-rp-' + serial
    return host_name

# ----------------------------
def do_one_time():
# ----------------------------

    set_firewall_rules()

    increment_boot_count()

    # set the hostname
    try:
        host_name = make_host_name()

        do_one_command('sudo hostname ' + host_name)

        file_out = '/etc/hostname'
        with open(file_out, 'w') as f:
            f.write(host_name)
    except:
        pass

    check_hosts_for_update()

    # set timezone to last known
    save_file = '/cardinal/localhtml/timezone'
    try:
        with open(save_file, 'r') as f:
            time_zone = f.read()
            # set it
            # get options from 'timedatectl list-timezones | tee'
            pass_string, fail_string = do_one_command('sudo timedatectl set-timezone ' + time_zone)
    except:
        pass

    set_browser_zoom_level(get_my_zoom())

    make_showgif_html()

# ----------------------------
def set_firewall_rules():
# ----------------------------
    manually = """
# Firewall
# https://www.digitalocean.com/community/tutorials/iptables-essentials-common-firewall-rules-and-commands
# https://www.cyberciti.biz/tips/linux-iptables-4-block-all-incoming-traffic-but-allow-ssh.html
sudo iptables -S

# flush all rules
sudo iptables -F
sudo iptables -X
# Setting default filter policy
sudo iptables -P OUTPUT ACCEPT
sudo iptables -P INPUT DROP
sudo iptables -P FORWARD DROP
# Allow unlimited traffic on loopback
sudo iptables -A INPUT -i lo -j ACCEPT
sudo iptables -A OUTPUT -o lo -j ACCEPT
# Allow ssh
sudo iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW,ESTABLISHED -j ACCEPT
# drop bad packets
sudo iptables -A INPUT -m conntrack --ctstate INVALID -j DROP

sudo iptables -S
    """

    if True:
        commands = []

        # flush all rules
        commands.append('sudo iptables -F')

        # allow all, until the lower section here is correct
        commands.append('sudo iptables -P OUTPUT ACCEPT')
        commands.append('sudo iptables -P INPUT ACCEPT')
        commands.append('sudo iptables -P FORWARD ACCEPT')

    else:
        commands = []
        # flush all rules
        commands.append('sudo iptables -F')
        commands.append('sudo iptables -X')

        # Setting default filter policy
        commands.append('sudo iptables -P OUTPUT ACCEPT')   # need our outbound 443 access to Slicer to work, and to function as a web browser
        commands.append('sudo iptables -P INPUT DROP')
        commands.append('sudo iptables -P FORWARD DROP')

        # drop bad packets
        commands.append('sudo iptables -A INPUT -m conntrack --ctstate INVALID -j DROP')

        # Allow unlimited traffic on loopback
        commands.append('sudo iptables -A INPUT -i lo -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o lo -j ACCEPT')

        # web browser activity from the pi to the outside
        commands.append('sudo iptables -A OUTPUT -o eth0 -p tcp --dport 80 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o eth0 -p tcp --dport 443 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o wlan0 -p tcp --dport 80 -m state --state NEW,ESTABLISHED -j ACCEPT')
        commands.append('sudo iptables -A OUTPUT -o wlan0 -p tcp --dport 443 -m state --state NEW,ESTABLISHED -j ACCEPT')

        # Allow ssh
        commands.append('sudo iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW,ESTABLISHED -j ACCEPT')

        # Network time outbound, and responses?

    try:
        for command in commands:
            pass_string, fails = do_one_command(command)

    except:
        pass

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def macAddress(portName):
# ----------------------------
    return_value = ''
    pass_string, fails = do_one_command('ip -j addr')
    # pass_string = '[{"ifindex":1,"ifname":"lo","flags":["LOOPBACK","UP","LOWER_UP"],"mtu":65536,"qdisc":"noqueue","operstate":"UNKNOWN","group":"default","txqlen":1000,"link_type":"loopback","address":"00:00:00:00:00:00","broadcast":"00:00:00:00:00:00","addr_info":[{"family":"inet","local":"127.0.0.1","prefixlen":8,"scope":"host","label":"lo","valid_life_time":4294967295,"preferred_life_time":4294967295},{"family":"inet6","local":"::1","prefixlen":128,"scope":"host","valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":2,"ifname":"eth0","flags":["BROADCAST","MULTICAST","UP","LOWER_UP"],"mtu":1500,"qdisc":"mq","operstate":"UP","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c2","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691098,"preferred_life_time":691098},{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","secondary":true,"dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691103,"preferred_life_time":604703},{"family":"inet6","local":"fe80::d21:c163:5965:731e","prefixlen":64,"scope":"link","noprefixroute":true,"valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":3,"ifname":"wlan0","flags":["NO-CARRIER","BROADCAST","MULTICAST","UP"],"mtu":1500,"qdisc":"pfifo_fast","operstate":"DOWN","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c4","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[]}]'
    try:
        #print ('pass_string', pass_string)
        the_list = json.loads(pass_string)
        for index in range(0, len(the_list)):
            # the_list[1].keys()
            # [u'addr_info', u'operstate', u'qdisc', u'group', u'mtu', u'broadcast', u'flags', u'address', u'ifindex', u'txqlen', u'ifname', u'link_type']
            #print ("ifname", the_list[index]['ifname'])
            if the_list[index]['ifname'] == portName:
                return_value = the_list[index]['address']
    except:
        pass

    return return_value

# ----------------------------
def get_memory_details():
# ----------------------------
    _ = """
MemTotal:        1911320 kB
MemFree:         1194012 kB
MemAvailable:    1533404 kB
Buffers:           51552 kB
Cached:           442692 kB
SwapCached:            0 kB
Active:           199556 kB
Inactive:         439384 kB
Active(anon):      13020 kB
Inactive(anon):   224548 kB
Active(file):     186536 kB
Inactive(file):   214836 kB
Unevictable:        2784 kB
Mlocked:              16 kB
HighTotal:       1232896 kB
HighFree:         629608 kB
LowTotal:         678424 kB
LowFree:          564404 kB
SwapTotal:        102396 kB
SwapFree:         102396 kB
Dirty:               196 kB
Writeback:             0 kB
AnonPages:        147560 kB
Mapped:           221896 kB
Shmem:             92876 kB
KReclaimable:      22724 kB
Slab:              41424 kB
SReclaimable:      22724 kB
SUnreclaim:        18700 kB
KernelStack:        1936 kB
PageTables:         6724 kB
NFS_Unstable:          0 kB
Bounce:                0 kB
WritebackTmp:          0 kB
CommitLimit:     1058056 kB
Committed_AS:    1299952 kB
VmallocTotal:     245760 kB
VmallocUsed:        5408 kB
VmallocChunk:          0 kB
Percpu:              512 kB
CmaTotal:         262144 kB
CmaFree:          249456 kB
    """

    result = {}
    try:
        with open('/proc/meminfo', 'r') as f:
            content = f.read()

            for line in content.split('\n'):
                if ':' in line:
                    splits = line.split(':')
                    title = splits[0]
                    value_string = splits[1].strip()
                    the_number = value_string.split(' ')[0]
                    the_units = value_string.split(' ')[1]
                    the_bytes = int(the_number)
                    if the_units == 'kB':
                        the_bytes = the_bytes * 1024
                    elif the_units == 'MB':
                        the_bytes = the_bytes * 1024 * 1024
                    result[title] = str(the_bytes)

    except:
        pass

    return result

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_runner_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def get_services_status():
# ----------------------------
    result = {}
    services_running = get_services_running()
    for service_name in services_running:
        status = os.system('systemctl is-active --quiet ' + service_name.replace('_','-') + '.service')

        if str(status) == '0':
            return_value = 'ok'
        else:
            return_value = 'fail'
        result[service_name] = return_value

    return result
# ----------------------------
def get_services_running():
# ----------------------------
    # ------------------------
    # find all of our services that are in place
    services_running = {}
    try:
        service_dir = '/lib/systemd/system/'
        all_services = os.listdir(service_dir)
        for service_name in all_services:
            if len(service_name) > 3:
                if service_name[0:3] == "pi-":
                    #print ('service_name', service_name)
                    version_file = "/dev/shm/" + service_name.replace('-','_').replace('.service','') + '_version.txt'
                    version_string = '(missing)'
                    #print ('version_file', version_file)
                    try:
                        with open(version_file, 'r') as f:
                            version_string = f.read()
                    except:
                        pass
                    services_running[service_name.replace('-','_').replace('.service','')] = version_string
    except:
        do_datadrop_debug(traceback.format_exc())

    return services_running

# ----------------------------
def build_and_do_slicer_report():
# ----------------------------
    global s_previous_report
    serial = get_serial()

    if True:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)
        the_data.append('wlan0mac=' + macAddress('wlan0'))
        the_data.append('eth0mac=' + macAddress('eth0'))

        the_data.append('wlan1mac=' + macAddress('wlan1'))
        the_data.append('eth1mac=' + macAddress('eth1'))
        the_data.append('responsetime=' + "{:.3f}".format(s_previous_report['response_time']))
        the_data.append('responsexce=' + str(s_previous_report['exception_count']))


        the_data.append('uptime=' + str(uptime()))
        the_data.append('loadavg=' + str(loadavg()))
        disk_usage = get_highest_disk_usage()
        the_data.append('disk_use=' + str(disk_usage['percent_used']))
        the_data.append('one_K=' + str(disk_usage['one_k_blocks']))

        the_data.append('inode_use=' + str(get_highest_inode_usage()))

        the_data.append('boot_count=' + str(get_boot_count()))
        the_data.append('screengrab_count=' + str(get_grab_count()))

        screen_width, screen_height = screen_size()
        the_data.append('screen_width=' + str(screen_width))
        the_data.append('screen_height=' + str(screen_height))

        # pick up any shared items (from other processes)
        share_point = '/dev/shm/'
        files_found = os.listdir(share_point)
        for file_found in files_found:
            if 'shared_' in file_found:
                name = file_found.replace('shared_','s_')
                value = ''
                try:
                    with open(share_point + file_found, 'r') as f:
                        value = f.read().split('\n')[0].replace(',','') # limit to first line, and no commas, to get ready to send it
                    the_data.append(name + '=' + str(value))
                except:
                    pass

        local_device_path = '/cardinal/localhtml/'
        files_found = os.listdir(local_device_path)
        for file_found in files_found:
            if 'dev_' == file_found[:4]:
                # these are device settings, from local choices, that need updated in Slicer
                try:
                    raw = json.loads(open(local_device_path + file_found, 'r').read())
                    the_data.append(file_found + '=' + str(raw[serial]))
                except:
                    pass
        # ------------------------
        found_time_zone = "???"
        pass_string, fail_string = do_one_command('timedatectl')
        _ = """
           Local time: Wed 2021-05-19 17:11:32 UTC
       Universal time: Wed 2021-05-19 17:11:32 UTC
             RTC time: n/a
            Time zone: UTC (UTC, +0000)
System clock synchronized: yes
          NTP service: active
      RTC in local TZ: no
        """
        for line in pass_string.split('\n'):
            if 'Time zone:' in line:
                splits = line.split(':')
                found_time_zone=splits[1].strip().split(' ')[0]
        the_data.append('timezone=' + found_time_zone)

        # ------------------------
        image_version = get_image_version()

        the_data.append('image=' + image_version)

        pass_string, fail_string = do_one_command('cat /etc/hostname')
        try:
            the_data.append('hostname=' + pass_string.split('\n')[0])
        except:
            do_datadrop_debug(traceback.format_exc())

        # ------------------------

        # pass_string = "networkuse=(1)(2)(3)"
        try:
            the_data.append('networkuse=' + get_network_utilization_report())
        except:
            do_datadrop_debug(traceback.format_exc())



        # ------------------------

        # pass_string = "temp=44.8'C"
        try:
            the_data.append('temperature=' + get_cpu_temperature())
        except:
            do_datadrop_debug(traceback.format_exc())

        # ------------------------
        # find all of our services that are in place
        services_running = get_services_running()
        for service_name in services_running:
            the_data.append('service:' + service_name + '=' + services_running[service_name])

        services_running = get_services_status()
        for service_name in services_running:
            the_data.append('run_status:' + service_name + '=' + services_running[service_name])

        memory_details = get_memory_details()
        to_send_list = ['MemTotal', 'MemFree', 'MemAvailable', 'Buffers', 'Cached']
        for to_send in to_send_list:
            if to_send in memory_details:
                the_data.append('Memory:' + to_send + '=' + memory_details[to_send])

        # as the very last item, send our time, so that it can be compared to the server time
        the_data.append('devicetime=' + str(time.time()))

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)
            #print ('the_report_url', the_report_url)
            #{"temperature":"45.2","service:pi-monitor":"M.2.2","wlan0mac":"dc:a6:32:96:56:c4","hostname":"cah-pi-10000000e3669edf","service:pi-network":"N.1.0","source":"runner","version":"R.1.0","service:pi-runner":"R.1.0","time":1620917416.788708,"serial":"10000000e3669edf","service:pi-hmi":"H.0.6"}

            # check in with slicer
            time_start = time.time()
            try:
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                s_previous_report['response_time'] = time.time() - time_start
                s_previous_report['exception_count'] = 0
            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'
                s_previous_report['exception_count'] += 1

            if s_previous_report['exception_count'] > 0:
                # try any other set of urls, to see if it is just Slicer, or is the network down
                did_get_any = False
                for test_url in ['http://google.com','http://google.com']:
                    try:
                        r = requests.get(test_url, verify=False, timeout=5.0)
                        did_get_any = True
                        break # exit on the first hit
                    except:
                        pass

                if did_get_any:
                    s_previous_report['exception_count'] = 0

            write_content_if_different(str(s_previous_report['exception_count']), '/dev/shm/pi_runner_slicer_exceptions.txt')

            # try to not let keyboard and mouse sleep:
            # https://forums.linuxmint.com/viewtopic.php?t=273460
            # echo on | sudo tee /sys/bus/usb/devices/*/power/level >/dev/null
            # test: cat  /sys/bus/usb/devices/*/power/level
            file_out = '/dev/shm/pi_runner_usbpoweron.txt'
            command_file = '/dev/shm/pi_runner_usbpoweron'
            try:
                with open(command_file, 'w') as f:
                    f.write('echo on | sudo tee /sys/bus/usb/devices/*/power/level >/dev/null')

                pass_string, fails = do_one_command('sudo chmod +x ' + command_file)
                pass_string, fails = do_one_command('sudo ' + command_file)

                content = 'pass_string:\n' + pass_string + '\nfail_string:\n' + fail_string
            except:
                content = traceback.format_exc()

            try:
                with open(file_out, 'w') as f:
                    f.write(content)
            except:
                pass


    return url_result

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('runner data drop: Start',True)

    serial = get_serial()
    slicer_update_bonus = False

    url_result = build_and_do_slicer_report()

    try:
        try:
            result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'

            # {"services": {"pi_runner": "R.1.0", "pi_hmi": "H.0.7"}}

            # make a new pattern, that if the field is named "info_", then lump process that here
            # These are light weight, non-verified to serial number
            for item in result_json:
                if 'info_' in item:
                    output_file = "/cardinal/localhtml/" + item
                    content = str(result_json[item])

                    write_content_if_different(content, output_file)

            if 'timezone' in result_json:
                time_zone = str(result_json['timezone'])
            else:
                time_zone = 'UTC'

            content = time_zone
            output_file = "/cardinal/localhtml/timezone"

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            try:
                with open(output_file, 'r') as f:
                    existing_content = f.read()
            except:
                existing_content = ''

            if existing_content != content:
                with open(output_file, 'w') as f:
                    f.write(content)

                try:
                    # set it
                    pass_string, fail_string = do_one_command('sudo timedatectl set-timezone ' + time_zone)
                except:
                    pass

                try:
                    do_one_command('sudo systemctl restart ' + 'pi-hmi.service')
                except:
                    pass

            if 'showgif_md5' in result_json:
                md5_before = get_my_showgif_md5()

                output_file = "/cardinal/localhtml/showgif_md5"
                content = json.dumps(result_json['showgif_md5'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

                md5_after = get_my_showgif_md5()

                if md5_after != md5_before:
                    try:
                        get_server_file_to_local('showgif_' + serial + '.gif', '/cardinal/localhtml/showgif.gif')

                        need_to_restart_browser = False
                        bookmarks = get_my_bookmarks()
                        for key in sorted(bookmarks):
                            if 'url' in bookmarks[key]:
                                if 'file:///cardinal/localhtml/showgif.html' == bookmarks[key]['url']:
                                    need_to_restart_browser = True
                                    # do not require that it be autolaunch. If it was manually launched,
                                        # then this will alert the people that there is new content.

                        if need_to_restart_browser:
                            kill_browser()
                    except:
                        # make it so that it retries at the next opportunity
                        os.remove("/cardinal/localhtml/showgif_md5")

            if 'browser_zoom' in result_json:
                zoom_before = get_my_zoom()

                output_file = "/cardinal/localhtml/browser_zoom"
                content = json.dumps(result_json['browser_zoom'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

                zoom_after = get_my_zoom()

                if zoom_after != zoom_before:
                    set_browser_zoom_level(zoom_after)
                    kill_browser()

            if 'clockview_enabled' in result_json:
                output_file = "/cardinal/localhtml/clockview_enabled"
                content = json.dumps(result_json['clockview_enabled'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

            if 'bluetooth_enabled' in result_json:
                output_file = "/cardinal/localhtml/bluetooth_enabled"
                content = json.dumps(result_json['bluetooth_enabled'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

            if 'special_menu' in result_json:
                output_file = "/cardinal/localhtml/special_menu"
                content = json.dumps(result_json['special_menu'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

            if 'screen_resolution' in result_json:
                output_file = "/cardinal/localhtml/screen_resolution"
                content = json.dumps(result_json['screen_resolution'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

                    # also build a new boot configuration, and do the reboot
                    build_boot_config()
                    do_one_command('sudo reboot')


            if 'name_to_use' in result_json:
                output_file = "/cardinal/localhtml/name_to_use"
                content = json.dumps(result_json['name_to_use'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

            if 'browser_timeout' in result_json:
                output_file = "/cardinal/localhtml/browser_timeout"
                content = json.dumps(result_json['browser_timeout'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

            # like "conf_wifi_connect"
            for individual_item in result_json:
                if 'conf_' == individual_item[:5]:
                    output_file = "/cardinal/localhtml/" + individual_item
                    dev_file = "/cardinal/localhtml/" + 'dev_' + individual_item[5:]

                    content = json.dumps(result_json[individual_item])

                    if os.path.isfile(dev_file):
                        try:
                            raw = json.loads(open(dev_file, 'r').read())

                            delete_dev = False
                            if not serial in raw:
                                delete_dev = True # this cleans up any old serial number item
                            else:
                                dev_value = str(raw[serial])
                                if dev_value == result_json[individual_item][serial]:
                                    delete_dev = True # Once they match, remove the device side setting
                            if delete_dev:
                                os.remove(dev_file)
                        except:
                            pass

                    if not os.path.exists(os.path.dirname(output_file)):
                        os.makedirs(os.path.dirname(output_file))

                    try:
                        with open(output_file, 'r') as f:
                            existing_content = f.read()
                    except:
                        existing_content = ''

                    if existing_content != content:
                        with open(output_file, 'w') as f:
                            f.write(content)

            if 'bookmarks' in result_json:
                output_file = "/cardinal/localhtml/bookmarks"
                content = json.dumps(result_json['bookmarks'])

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))

                try:
                    with open(output_file, 'r') as f:
                        existing_content = f.read()
                except:
                    existing_content = ''

                if existing_content != content:
                    with open(output_file, 'w') as f:
                        f.write(content)

                check_browser_for_restart()
                check_hosts_for_update()

            if 'services' in result_json:
                services_running = get_services_running()
                services_to_fetch = {}
                runner_to_fetch = {}

                # for initial debug
                with open('/dev/shm/runner_services_list', 'w') as f:
                    f.write(json.dumps(result_json['services']))
                # {"pi_security": "S.1.1"}

                with open('/dev/shm/runner_services_running', 'w') as f:
                    f.write(json.dumps(services_running))

                # compare whats requested, compared to what I have already running, and pull the difference
                for item in result_json['services']:
                    need_to_pull = True
                    if item in services_running:
                        if result_json['services'][item] == services_running[item]:
                            # it matches what I already have
                            need_to_pull = False

                    if need_to_pull:
                        if item == 'pi_runner':
                            runner_to_fetch[item] = result_json['services'][item]
                        else:
                            services_to_fetch[item] = result_json['services'][item]

                # do the updates
                for service_name in services_to_fetch:
                    get_service(service_name, services_to_fetch[service_name]) # name, version

                # If there is a runner update, do it last, since that is this code running right here
                for service_name in runner_to_fetch:
                    get_service(service_name, runner_to_fetch[service_name]) # name, version

            if 'screengrab_request' in result_json:
                if serial in result_json['screengrab_request']:
                    screengrab_request = result_json['screengrab_request'][serial]

                    # Only check the non-empty ones
                    if screengrab_request:
                        try:
                            with open("/dev/shm/debug_screengrab.txt", 'w') as f:
                                f.write(screengrab_request + '\n')
                        except:
                            pass

                        # Cross check this with my current grab_count, to see if I should act on it
                        debug_string = "1"
                        try:
                            debug_string = "1a"
                            if screengrab_request == get_image_version() + ":" + str(get_grab_count()).zfill(6):
                                debug_string = "2"
                                # do it
                                temp_file = "/dev/shm/screen_grab"
                                image_name = 'screen_' + serial + '_' + get_image_version() + ":" + str(get_grab_count()).zfill(6) + '.png'
                                cmd = """
rm /dev/shm/screen.xxd
xhost +local:pi
export DISPLAY=:0
scrot /dev/shm/""" + image_name + """
"""
                                with open(temp_file, 'w') as f:
                                    f.write(cmd)
                                pass_string, fails = do_one_command('sudo apt-get install --fix-missing -y scrot')
                                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)
                                pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
                                pass_string, fails = do_one_command('sudo runuser -u worker -- /dev/shm/screen_grab')
                                pass_string, fails = do_one_command('sudo rm /dev/shm/screen_grab')

                                # now try to send to slicer
                                # https://stackabuse.com/how-to-upload-files-with-pythons-requests-library/
                                the_site_to_call = call_home_locations()[0]
                                the_post_url = the_site_to_call + '/upload'
                                debug_string = "3"
                                test_file = open("/dev/shm/" + image_name, "rb")
                                debug_string = "4"

                                test_response = requests.post(the_post_url, files = {"form_field_name": test_file})
                                debug_string = "5"
                                test_file.close()
                                debug_string = "6"

                                # mark that we accomplished it
                                increment_grab_count()
                                debug_string = "7"

                                pass_string, fails = do_one_command('sudo rm /dev/shm/' + image_name)

                                slicer_update_bonus = True
                        except:
                            debug_string = "E " + traceback.format_exc()
                            pass

                        try:
                            with open("/dev/shm/debug_screendebug.txt", 'w') as f:
                                f.write(debug_string + '\n')
                        except:
                            pass



            # -----------------------------------
            # Always take the reboot request last,
            #     to let all data writing activities happen before this
            # -----------------------------------
            if 'reboot_request' in result_json:
                if serial in result_json['reboot_request']:
                    reboot_request = result_json['reboot_request'][serial]

                    # Only check the non-empty ones
                    if reboot_request:
                        try:
                            with open("/dev/shm/debug_reboot.txt", 'w') as f:
                                f.write(reboot_request)
                        except:
                            pass

                        # Cross check this with my current boot_count, to see if I should act on it
                        if reboot_request == get_image_version() + ":" + str(get_boot_count()):
                            # do it
                            do_one_command('sudo reboot')

        except:
            do_datadrop_debug(traceback.format_exc())

    except:
        do_datadrop_debug(traceback.format_exc())


    if slicer_update_bonus:
        url_result = build_and_do_slicer_report()

    do_datadrop_debug('runner data drop: End')

# ----------------------------
def do_maintenance():
# ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    try:
        do_privoxy_summary()
    except:
        pass

    do_datadrop()

    check_for_inactivity()

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_runner.cpython-37.pyc", "/cardinal/pi_runner.pyc")

    if os.path.isfile("/cardinal/pi_runner.py"):
        os.remove("/cardinal/pi_runner.py")

    try:
        with open('/dev/shm/pi_runner_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    # on initial boot, maybe the screen has not fully communicated with the pi yet.
    # Give it some time to settle, and then grab the screen size

    time.sleep(15)
    check_browser_for_restart()
    do_one_time()

    wake_count = 0

#    with open('/dev/shm/pi_runner_active_monitor_debug', 'w') as f:
#        f.write('0d\n')

    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_runner_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print ("!!! failed to write wake_count")



