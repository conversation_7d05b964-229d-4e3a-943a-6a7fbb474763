_ = """
sudo vi /cardinal/pi_security.py

sudo systemctl restart pi-security.service

watch -n 1 sudo cat /dev/shm/pi_security_datadrop.txt

cat /cardinal/log/pi_security_lastupdate.txt

"""

service = 'security'
version = 'S.1.2'
description = """
This is a service to perform the audits on the device, and to report
findings.

"""

release_notes = """
2021.09.14
S.1.2
Scan for updates available.
React to "do updates" request from <PERSON><PERSON><PERSON>. (device_osupdates_request_)
On start, do a bluetooth disable (take away the old default bluetooth setup), so that updates are clean.

2021.09.09
S.1.1
Version bump, just to test code load over service pull.

2021.08.10
S.1.0
Do lynis scan, and report up to <PERSON>lice<PERSON>.

"""

_updates = """
https://linuxhint.com/apt_get_upgrade_dist_upgrade/

install just one:
https://askubuntu.com/questions/44122/how-to-upgrade-a-single-package-using-apt-get

Check, but do not install:
https://unix.stackexchange.com/questions/19470/list-available-updates-but-do-not-install-them

Security updates available:
https://askubuntu.com/questions/774805/how-to-get-a-list-of-all-pending-security-updates

sudo apt-get list --upgradable |grep "/$(lsb_release -cs)-security"

OR:

sudo apt update -y
sudo apt-get --no-download -s dist-upgrade -V --fix-missing

0 upgraded, 0 newly installed, 0 to remove and 88 not upgraded.


ALSO:
eeprom update:
https://pimylifeup.com/raspberry-pi-update/


"""

other_content = """
sudo vi /cardinal/pi-security
sudo chmod +x /cardinal/pi-security

# ===== begin: start file
#!/usr/bin/env python3
import pi_security
pi_security.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-security.service
sudo systemctl daemon-reload
sudo systemctl stop pi-security.service
sudo systemctl start pi-security.service
sudo systemctl enable pi-security.service

systemctl status pi-security.service

sudo systemctl restart pi-security.service

# Logging of std out
cat /var/log/syslog | fgrep pi-security

OR

tail -f /var/log/syslog | fgrep pi-security

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-security
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""


import copy
import json
import math
import os
import requests
import shutil
import socket
import subprocess
import sys
import time
import traceback


# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)

# ----------------------------
def do_one_time():
# ----------------------------
    try:
        do_one_command('sudo systemctl disable bluetooth.service')
        do_one_command('sudo systemctl stop bluetooth.service')
    except:
        pass

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_security_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def do_updates_screen():
# ----------------------------
    result = {}
    result['updates_available'] = '-1'

    # make the list be current
    pass_string, fail_string = do_one_command('sudo apt update -y')

    # See what the list has that we do not have
#    pass_string, fail_string = do_one_command('sudo apt-get --no-download -s dist-upgrade -V --fix-missing')
    pass_string, fail_string = do_one_command('sudo apt-get -s dist-upgrade -V --fix-missing')

    _ = """
The following packages will be upgraded:
   bluez (5.50-1.2~deb10u2 => 5.50-1.2~deb10u2+rpt1)
   libbluetooth3 (5.50-1.2~deb10u2 => 5.50-1.2~deb10u2+rpt1)
   libc-bin (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc-dev-bin (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc-l10n (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6 (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6-dbg (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libc6-dev (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   libnftnl11 (1.1.2-2 => 1.1.7-1~bpo10+1~0)
   libraspberrypi-bin (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi-dev (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi-doc (1:1.20210805-1 => 1:1.20210831-1)
   libraspberrypi0 (1:1.20210805-1 => 1:1.20210831-1)
   linux-libc-dev (1:1.20210805-1 => 1:1.20210831-1)
   locales (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   multiarch-support (2.28-10+rpi1 => 2.28-10+rpt2+rpi1)
   raspberrypi-bootloader (1:1.20210805-1 => 1:1.20210831-1)
   raspberrypi-kernel (1:1.20210805-1 => 1:1.20210831-1)
   raspberrypi-sys-mods (20210706 => 20210901)
19 upgraded, 0 newly installed, 0 to remove and 0 not upgraded.
1 not fully installed or removed.
Inst libc6-dbg [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc6-dev [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc-dev-bin [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf]) []
Inst linux-libc-dev [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libc6 [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc6 (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst bluez [5.50-1.2~deb10u2] (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Inst libc-bin [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc-bin (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst libc-l10n [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Inst locales [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Inst libbluetooth3 [5.50-1.2~deb10u2] (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Inst libnftnl11 [1.1.2-2] (1.1.7-1~bpo10+1~0 Raspberry Pi Foundation:testing [armhf])
Inst libraspberrypi-doc [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi-dev [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst raspberrypi-kernel [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi-bin [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst libraspberrypi0 [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf]) []
Inst raspberrypi-bootloader [1:1.20210805-1] (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Inst multiarch-support [2.28-10+rpi1] (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Inst raspberrypi-sys-mods [20210706] (20210901 Raspberry Pi Foundation:testing [armhf])
Conf libc6-dbg (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc6-dev (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf libc-dev-bin (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf linux-libc-dev (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf bluez (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Conf libc-l10n (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Conf locales (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [all])
Conf libbluetooth3 (5.50-1.2~deb10u2+rpt1 Raspberry Pi Foundation:testing [armhf])
Conf libnftnl11 (1.1.7-1~bpo10+1~0 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-doc (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-dev (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-kernel (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi-bin (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf libraspberrypi0 (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-bootloader (1:1.20210831-1 Raspberry Pi Foundation:testing [armhf])
Conf multiarch-support (2.28-10+rpt2+rpi1 Raspberry Pi Foundation:testing [armhf])
Conf raspberrypi-sys-mods (20210901 Raspberry Pi Foundation:testing [armhf])
  """

    count_security = 0
    for line in pass_string.split('\n'):
        if 'upgraded,' in line:
            # line = '0 upgraded, 0 newly installed, 0 to remove and 88 not upgraded.'
            try:
                result['updates_available'] = line.split('upgraded,')[0].strip()
            except:
                pass

            if 'security' in line.lower():
                count_security += 1

    result['updates_security'] = str(count_security)

    return result

# ----------------------------
def do_security_screen():
# ----------------------------
    result = {}
    result['hardening_index'] = '-1'

    # sudo lynis audit system -Q
    pass_string, fail_string = do_one_command('sudo lynis audit system -Q --no-colors')

    _ = """
  Hardening index : 55 [###########         ]
  Tests performed : 213
  Plugins enabled : 1
  """

    for line in pass_string.split('\n'):
        if 'Hardening index' in line:
            result['hardening_index'] = line.split(':')[1].strip().split()[0]

    return result

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('security data drop: Start',True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        do_datadrop_debug('Start security screen...')
        results = do_security_screen()
        for item in results:
            the_data.append(item + '=' + results[item])

        do_datadrop_debug('Start updates screen...')
        results = do_updates_screen()
        for item in results:
            the_data.append(item + '=' + results[item])

        the_report_url = 'https://slicer.cardinalhealth.net/datadrop?' + ','.join(the_data)

        # check in with slicer
        try:
            do_datadrop_debug('Start reporting...:' + the_report_url)
            r = requests.get(the_report_url, verify=False, timeout=15.0)
            url_result = r.text
            do_datadrop_debug('Reporting result: ' + url_result)

            try:
                result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'


                if 'action_request' in result_json:
                    if serial in result_json['action_request']:
                        action_request = result_json['action_request'][serial]

                        if action_request == 'update_at_next_chance':
                            # do it right here
                            do_datadrop_debug('OS Updates starting...')

                            output_file = "/cardinal/log/pi_security_lastupdate.txt"
                            if not os.path.exists(os.path.dirname(output_file)):
                                os.makedirs(os.path.dirname(output_file))
                            with open(output_file, 'w') as f:
                                f.write('')

                            # https://linux.die.net/man/8/apt-get
                            cmd = 'sudo DEBIAN_FRONTEND=noninteractive apt-get -y dist-upgrade -V --fix-missing'
                            pass_string, fail_string = do_one_command(cmd)
                            do_datadrop_debug('cmd: ' + cmd)
                            do_datadrop_debug('pass_string: ' + pass_string)
                            do_datadrop_debug('fail_string: ' + fail_string)

                            try:
                                with open(output_file, 'a') as f:
                                    f.write('cmd:\n' + cmd + '\n')
                                    f.write('pass_string:\n' + pass_string + '\n')
                                    f.write('fail_string:\n' + fail_string + '\n')
                            except:
                                do_datadrop_debug(traceback.format_exc())

                            cmd = 'sudo apt-get clean'
                            pass_string, fail_string = do_one_command(cmd)
                            do_datadrop_debug('cmd: ' + cmd)
                            do_datadrop_debug('pass_string: ' + pass_string)
                            do_datadrop_debug('fail_string: ' + fail_string)

                            try:
                                with open(output_file, 'a') as f:
                                    f.write('cmd:\n' + cmd + '\n')
                                    f.write('pass_string:\n' + pass_string + '\n')
                                    f.write('fail_string:\n' + fail_string + '\n')
                            except:
                                do_datadrop_debug(traceback.format_exc())


                            do_datadrop_debug('OS Updates completed.')
            except:
                do_datadrop_debug(traceback.format_exc())




        except:
            do_datadrop_debug(traceback.format_exc())
            url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())


    do_datadrop_debug('security data drop: End')

# ----------------------------
def do_maintenance():
# ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()



# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_security.cpython-37.pyc", "/cardinal/pi_security.pyc")

    if os.path.isfile("/cardinal/pi_security.py"):
        os.remove("/cardinal/pi_security.py")

    try:
        with open('/dev/shm/pi_security_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    do_one_time()

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_security_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print ("!!! failed to write wake_count")



