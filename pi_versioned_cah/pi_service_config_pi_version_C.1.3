_ = """
sudo vi /cardinal/pi_config.py

sudo systemctl restart pi-config.service

watch -n 1 sudo cat /dev/shm/pi_config_datadrop.txt

cat /cardinal/log/pi_config_lastupdate.txt

"""

service = 'config'
version = 'C.1.3'
description = """
This is a pi service config.

"""

release_notes = """
2024.03.12
C.1.3
Support Raspberry Pi5

2022.10.25
C.1.2
Add more items to be configured:
    wifi ssid and psk
    special menu
    clock
    screen resolution and zoom
    bookmarks

2022.08.31
C.1.1
Handle multiple call_home entries

2022.02.02
C.1.0
Start a project to allow for an http based configuration of local choices

"""

other_content = """
sudo vi /cardinal/pi-config
sudo chmod +x /cardinal/pi-config

# ===== begin: start file
#!/usr/bin/env python3
import pi_config
pi_config.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-config.service
sudo systemctl daemon-reload
sudo systemctl stop pi-config.service
sudo systemctl start pi-config.service
sudo systemctl enable pi-config.service

systemctl status pi-config.service

sudo systemctl restart pi-config.service

# config of std out
cat /var/log/syslog | fgrep pi-config

OR

tail -f /var/log/syslog | fgrep pi-config

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-config
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_config


"""

import copy
import json
import math
import os
try:
    import requests
except:
    pass # for unittest
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

from sys import version as python_version
from cgi import parse_header, parse_multipart

if python_version.startswith('3'):
    from urllib.parse import parse_qs
    from http.server import BaseHTTPRequestHandler,HTTPServer
else:
    from urlparse import parse_qs
    from BaseHTTPServer import BaseHTTPRequestHandler,HTTPServer

# ----------------------------
def get_config_list():
# ----------------------------
    item_count = 0
    items_to_configure = {}

    item_count += 1
    items_to_configure[item_count] = {}
    items_to_configure[item_count]['name'] = 'unlock'
    items_to_configure[item_count]['location'] = '/dev/shm/config_unlock.txt'
    items_to_configure[item_count]['storage_type'] = 'string'
    items_to_configure[item_count]['default_value'] = ''
    items_to_configure[item_count]['help'] = ''

    if get_content_for_form(items_to_configure[item_count]) == 'unlock1234':
        item_count = 0
        items_to_configure = {}

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'call_home'
        items_to_configure[item_count]['location'] = 'call_home_locations.txt'
        items_to_configure[item_count]['storage_type'] = 'json'
        items_to_configure[item_count]['default_value'] = ['']
        items_to_configure[item_count]['help'] = "['https://slicer.XYZ.net']"

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'wifi_ssid_psk'
        items_to_configure[item_count]['location'] = '/cardinal/wifi_config.txt' # point to the existing network expected place
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = 'ssid_name(,psk)'

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'screen_resolution'
        items_to_configure[item_count]['location'] = '/cardinal/config_resolution.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = '1080p(1920x1080) 1 16'

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'screen_zoom'
        items_to_configure[item_count]['location'] = '/cardinal/config_zoom.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = '100 (may require 2 reboots to take full effect)'

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'menu'
        items_to_configure[item_count]['location'] = '/cardinal/config_menu.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = 'Yes or No to show the special menu'

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'clock'
        items_to_configure[item_count]['location'] = '/cardinal/config_clock.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = 'MMDDYYYYhhmmap or hhmmapMMDDYYYY or blank'


        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'bookmarks'
        items_to_configure[item_count]['location'] = 'config_bookmarks_local.txt'
        items_to_configure[item_count]['storage_type'] = 'json'
        items_to_configure[item_count]['default_value'] = {}
        items_to_configure[item_count]['help'] = "{'1': {'url': 'https://xyz.company.net/login', 'allowlist': ['*.company.net'], 'title': 'company'}}"
        items_to_configure[item_count]['input_type'] = 'textarea'



    return items_to_configure

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def build_get_page_body(the_path='/'):
# ----------------------------
    # localhost:7000, gives the_path = '/'
    # localhost:7000/?set=1234, gives the_path = '/?set=1234'

    body = ''
#    body += '<B><a href="JavaScript:window.close()" style="text-decoration:none;color:inherit">Close</a></B>'
    body += '<center>'
    body += '<br>Config page (Alt F4 to return home)<br>'
    body += '<table border="1" cellpadding="5">'

    try:
        items_to_configure = get_config_list()

        row = '<tr><td>Name</td><td>Description</td><td>Current Value</td><td>Edit value</td><td>Click to save</td></tr>'
        body += row

        for item in sorted(items_to_configure.keys()):
            configure_item = items_to_configure[item]

            name = configure_item['name']

            content = get_content_for_form(items_to_configure[item])
            current_value = ''
            help_value = ''
            if configure_item['storage_type'] == 'json':
                current_value = json.dumps(content)
            if configure_item['storage_type'] == 'string':
                current_value = content
            if 'help' in configure_item:
                help_value = configure_item['help']

            row = ''
            row += '<tr>'
            row += '<td>'
            row += name
            row += '</td>'
            row += '<td>'
            row += help_value
            row += '</td>'
            row += '<td>'
            row += current_value.replace('"',"'")
            row += '</td>'

            row += '<form method="post" action="">'
            row += '<td>'

            row += '<select name="the_selection" id="the_selection" hidden>'
            row += '<option value="' + name + '_text_set" selected>' + 'dummy' + '</option>'
            row += '</select>'

            input_type = 'text'
            if 'input_type' in configure_item:
                input_type = configure_item['input_type']

            if input_type == 'text':
                row += """<input type="text" size=""" +  str(len(current_value) + 10) + """ name="device_value_text" value=\"""" + current_value.replace('"',"'") + """\">"""
            elif input_type == 'textarea':
                row += """<textarea rows=5 cols = 40 name="device_value_text" >""" + current_value.replace('"',"'") + """</textarea>"""
            row += '</td>'

            row += '<td>'
            row += '<input type="submit" value="Submit">'
            row += '</td>'
            row += '</form>'

            row += '</tr>'
            body += row
    except:
        body += traceback.format_exc().replace('\n','<br>')

    body += '</table>'
    body += '</center>'

    return body

# ----------------------------
def process_post_data(post_d):
# ----------------------------
    items_to_configure = get_config_list()

    if 'the_selection' in post_d:
        for item in sorted(items_to_configure.keys()):
            name = items_to_configure[item]['name']
            if post_d['the_selection'] == name + '_text_set':
                if 'device_value_text' in post_d:
                    content = post_d['device_value_text'].replace('%2C',',').replace('%7B','{').replace('%7D','}')
                    set_content_from_form(items_to_configure[item], content)

# ----------------------------
def build_and_start_web_server(port_number):
# ----------------------------
    # be a web server
    # https://stackoverflow.com/questions/23264569/python-3-x-basehttpserver-or-http-server

    class MyServer(BaseHTTPRequestHandler):
        def do_GET(self):
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            response = "<html><head><title>" + service + "</title></head><body>"
            response += build_get_page_body(self.path)
            response += "</body></html>"

            self.wfile.write(bytes(response, "utf-8"))

        def do_POST(self):
            # https://stackoverflow.com/questions/4233218/python-how-do-i-get-key-value-pairs-from-the-basehttprequesthandler-http-post-h/13330449
            # https://gist.github.com/mdonkers/63e115cc0c79b4f6b8b3a6b797e485c7
            try:
                content_length = int(self.headers['Content-Length']) # <--- Gets the size of data
                post_data = self.rfile.read(content_length).decode("utf-8")  # <--- Gets the data itself

                replacers = []
                replacers.append(('%5B', '['))
                replacers.append(('%5D', ']'))
                replacers.append(('%27', "'"))
                replacers.append(('%3A', ':'))
                replacers.append(('%2F', '/'))
                replacers.append(('+', ' '))
                replacers.append(('%40', '@'))
                replacers.append(('%24', '$'))
                replacers.append(('%25', '%'))
                replacers.append(('%21', '!'))
                replacers.append(('%23', '#'))

                post_d = {}
                for piece in post_data.split('&'):
                    if '=' in piece:
                        content_found = piece.split('=')[1]
                        for replacer in replacers:
                            content_found = content_found.replace(replacer[0], replacer[1])

                        post_d[piece.split('=')[0]] = content_found

                post_body = ""
                for key in post_d:
                    post_body += '<br>' + key + ' = ' + post_d[key]

                # process the posted data
                process_post_data(post_d)

                self.send_response(200)
                self.send_header("Content-type", "text/html")
                self.end_headers()
                response = "<html><head><title>Config.</title></head><body>"
                response += build_get_page_body(self.path)
                response += "<br>Posted:"

                response += post_body

                response += "</body></html>"
            except:
                response = traceback.format_exc().replace('\n','<br>')

            self.wfile.write(bytes(response, "utf-8"))

    myServer = HTTPServer(("localhost", port_number), MyServer)

    try:
        myServer.serve_forever()
    except KeyboardInterrupt:
        pass

    myServer.server_close()

# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
#    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass

    build_and_start_web_server(7000)


# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_config_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def set_content_from_form(configure_item, content):
# ----------------------------
    try:
        open(configure_item['location'], 'w').write(content)
    except:
        pass

# ----------------------------
def get_content_for_form(configure_item):
# ----------------------------
    return_value = configure_item['default_value']

    try:
        content = open(configure_item['location'], 'r').read()

        if configure_item['storage_type'] == 'json':
            return_value = json.loads(content.replace("'",'"'))
        if configure_item['storage_type'] == 'string':
            return_value = content
    except:
        pass

    return return_value

# ----------------------------
def call_home_locations():
# ----------------------------
    # Used by all the other processes, to know where to call in
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string for " + service + ": " + version)

    # do not copy this, unless this is the last code to run.
    # Look to pi_logging for a threaded alternative
    do_one_time()


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')



# End of file