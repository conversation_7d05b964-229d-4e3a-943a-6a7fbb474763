_ = """
sudo vi /cardinal/pi_logging.py

sudo systemctl restart pi-logging.service

watch -n 1 sudo cat /dev/shm/pi_logging_datadrop.txt


ls -l /cardinal/log/pi_logging/



"""

service = 'logging'
version = 'L.0.3'
description = """
This is a service to manage logging on the device, and to report log status.

"""

release_notes = """
2021.10.06
L.0.3
Keyboard/mouse activity logging, to help with debugging PR005 extra session windows, and MEX09 touchpad deactivation issues.

"""

other_content = """
sudo vi /cardinal/pi-logging
sudo chmod +x /cardinal/pi-logging

# ===== begin: start file
#!/usr/bin/env python3
import pi_logging
pi_logging.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-logging.service
sudo systemctl daemon-reload
sudo systemctl stop pi-logging.service
sudo systemctl start pi-logging.service
sudo systemctl enable pi-logging.service

systemctl status pi-logging.service

sudo systemctl restart pi-logging.service

# Logging of std out
cat /var/log/syslog | fgrep pi-logging

OR

tail -f /var/log/syslog | fgrep pi-logging

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-logging
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""


_ = """
keystroke logging

# https://thehackerdiary.wordpress.com/2017/04/21/exploring-devinput-1/

import struct
f = open( "/dev/input/event0", "rb" ); # Open the file in the read-binary mode
while 1:
  data = f.read(16)
  print (struct.unpack('LLHHI',data))
  ###### PRINT FORMAL = ( Time Stamp_INT , 0 , Time Stamp_DEC , 0 ,
  ######   type , code ( key pressed ) , value (press/release) )

# https://www.kernel.org/doc/html/v4.17/input/event-codes.html
# https://github.com/torvalds/linux/blob/master/include/uapi/linux/input-event-codes.h
# https://thehackerdiary.wordpress.com/2017/04/21/exploring-devinput-1/


"""


import copy
import datetime
import json
import math
import os
import requests
import shutil
import socket
import struct
import subprocess
import sys
import time
import traceback

from concurrent.futures import ThreadPoolExecutor
s_executor = ThreadPoolExecutor(100)
# line up all of the worker threads
s_monitored_devices = {}
s_base_raw_path = "/cardinal/log/pi_logging/"


# ----------------------------
def log_from_input_device(filename):
# ----------------------------
    device_name = filename.split('/')[-1]

    f = open(filename, 'rb')
    while(True):
        data = f.read(16) # blocking if there is no data
        (time_second, time_microsecond, key_type, key_code, key_value) = struct.unpack('LLHHI',data)
        report = str(time_second) + ',' + str(key_type) + ',' + str(key_code) + ',' + str(key_value) + '\n'

        TS = datetime.datetime.now().strftime('%Y%m%d')

        output_file = base_raw_path + TS + "/device_" + device_name + ".txt"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'a') as f:
            f.write('')

    days_to_keep = 14
    days_present = sorted(os.listdir(s_base_raw_path))
    if len(days_present) > days_to_keep:
        for day_to_remove in days_present[:-1 * days_to_keep]:
            try:
                shutil.rmtree(base_raw_path + day_to_remove)
            except:
                pass

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)



# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
#    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_logging_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('logging data drop: Start',True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        the_report_url = 'https://slicer.cardinalhealth.net/datadrop?' + ','.join(the_data)

        # check in with slicer
        try:
            do_datadrop_debug('Start logging...:' + the_report_url)
            r = requests.get(the_report_url, verify=False, timeout=15.0)
            url_result = r.text
            do_datadrop_debug('logging result: ' + url_result)

            try:
                result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'


                if 'action_request' in result_json:
                    pass
            except:
                do_datadrop_debug(traceback.format_exc())

        except:
            do_datadrop_debug(traceback.format_exc())
            url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())


    do_datadrop_debug('logging data drop: End')

# ----------------------------
def do_maintenance():
# ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()

# ----------------------------
def check_for_inputs():
# ----------------------------
    global s_monitored_devices

    # see if there are new devices to monitor
    try:
        device_dir = '/dev/input/'
        for device_name in os.listdir(device_dir):
            if ('event' in device_name) or ('mouse' in device_name):
                full_device_path = device_dir + device_name
                if not full_device_path in s_monitored_devices:
                    s_monitored_devices[full_device_path] = {'executor':None, 'is_done':True} # Make the arrival of the new device be an activity.

                    #job_id_thread1 = sched.add_job(thread_call_back_to_slicer, 'interval', seconds=30, coalesce=True)
                    # https://thehackerdiary.wordpress.com/tag/devinput-python/
                    # https://stackoverflow.com/questions/39948588/non-blocking-file-read/39948643

        # run through all the monitored devices, and if they are done, start a new executor
        for monitored_device in sorted(s_monitored_devices):
            if s_monitored_devices[monitored_device]['executor']:
                s_monitored_devices[monitored_device]['is_done'] = s_monitored_devices[monitored_device]['executor'].done()

            if s_monitored_devices[monitored_device]['is_done']:
                try:
                    del(s_monitored_devices[monitored_device]['executor'])
                except:
                    pass

                try:
                    s_monitored_devices[monitored_device]['executor'] = s_executor.submit(log_from_input_device, monitored_device)
                except:
                    with open ('/dev/shm/pi_logging_active_monitor_exception', 'w') as f:
                        f.write(traceback.format_exc())
    except:
        pass

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_logging.cpython-37.pyc", "/cardinal/pi_logging.pyc")

    if os.path.isfile("/cardinal/pi_logging.py"):
        os.remove("/cardinal/pi_logging.py")

    try:
        with open('/dev/shm/pi_logging_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    do_one_time()

    wake_count = 0
    time_of_last_input_check = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            time_of_check = time.time()
            if abs(time_of_last_input_check - time_of_check) > 60:
                time_of_last_input_check = time_of_check
                check_for_inputs()



            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_logging_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print ("!!! failed to write wake_count")





