_ = """
sudo vi /cardinal/pi_config.py

sudo systemctl restart pi-config.service

watch -n 1 sudo cat /dev/shm/pi_config_datadrop.txt

cat /cardinal/log/pi_config_lastupdate.txt

"""

service = 'config'
version = 'C.1.0'
description = """
This is a pi service config.

"""

release_notes = """
2022.02.02
C.0.1
Start a project to allow for an http based configuration of local choices.

"""

other_content = """
sudo vi /cardinal/pi-config
sudo chmod +x /cardinal/pi-config

# ===== begin: start file
#!/usr/bin/env python3
import pi_config
pi_config.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-config.service
sudo systemctl daemon-reload
sudo systemctl stop pi-config.service
sudo systemctl start pi-config.service
sudo systemctl enable pi-config.service

systemctl status pi-config.service

sudo systemctl restart pi-config.service

# config of std out
cat /var/log/syslog | fgrep pi-config

OR

tail -f /var/log/syslog | fgrep pi-config

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-config
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_config


"""

import copy
import json
import math
import os
try:
    import requests
except:
    pass # for unittest
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

from sys import version as python_version
from cgi import parse_header, parse_multipart

if python_version.startswith('3'):
    from urllib.parse import parse_qs
    from http.server import BaseHTTPRequestHandler,HTTPServer
else:
    from urlparse import parse_qs
    from BaseHTTPServer import BaseHTTPRequestHandler,HTTPServer



# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def build_get_page_body(the_path='/'):
# ----------------------------
    # localhost:7000, gives the_path = '/'
    # localhost:7000/?set=1234, gives the_path = '/?set=1234'

    body = ''
    body += '<br><br>'
    body += '<center>'
    body += '<table border="1" cellpadding="5">'
    body += '<tr>'
    body += '<td>'
    body += 'call_home'
    body += '</td>'
    body += '<td>'
    current_value = json.dumps(call_home_locations())
    body += current_value
    body += '</td>'


    id = get_serial()

    body += '<td>'
    body += '<form method="post" action="">'

    body += '<select name="the_selection" id="the_selection" hidden>'
    body += '<option value="call_home_text_set" selected>' + 'dummy' + '</option>'
    body += '</select>'
    body += '<select name="serial" id="serial" hidden>'
    body += '<option value="' + id + '" selected>' + id + '</option>'
    body += '</select>'

    body += """<input type="text" size=""" +  str(len(current_value) + 10) + """ name="device_name_text" value=\"""" + current_value.replace('"',"'") + """\">"""
    body += '</td>'

    body += '<td>'
    body += '<input type="submit" value="Submit">'
    body += '</td>'
    body += '</form>'


    body += '</tr>'
    body += '</table>'
    body += '</center>'

    return body

# ----------------------------
def process_post_data(post_d):
# ----------------------------
    if 'the_selection' in post_d:
        if post_d['the_selection'] == 'call_home_text_set':
            if 'device_name_text' in post_d:
                try:
                    open('call_home_locations.txt', 'w').write(post_d['device_name_text'])
                except:
                    pass


# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
#    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass

    # be a web server
    # https://stackoverflow.com/questions/23264569/python-3-x-basehttpserver-or-http-server

    class MyServer(BaseHTTPRequestHandler):
        def do_GET(self):
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            response = "<html><head><title>Config.</title></head><body>"
            response += build_get_page_body(self.path)
            response += "</body></html>"

            self.wfile.write(bytes(response, "utf-8"))

        def do_POST(self):
            # https://stackoverflow.com/questions/4233218/python-how-do-i-get-key-value-pairs-from-the-basehttprequesthandler-http-post-h/13330449
            # https://gist.github.com/mdonkers/63e115cc0c79b4f6b8b3a6b797e485c7
            try:
                content_length = int(self.headers['Content-Length']) # <--- Gets the size of data
                post_data = self.rfile.read(content_length).decode("utf-8")  # <--- Gets the data itself

                replacers = []
                replacers.append(('%5B', '['))
                replacers.append(('%5D', ']'))
                replacers.append(('%27', "'"))
                replacers.append(('%3A', ':'))
                replacers.append(('%2F', '/'))
                replacers.append(('+', ' '))

                post_d = {}
                for piece in post_data.split('&'):
                    if '=' in piece:
                        content_found = piece.split('=')[1]
                        for replacer in replacers:
                            content_found = content_found.replace(replacer[0], replacer[1])

                        post_d[piece.split('=')[0]] = content_found

                post_body = ""
                for key in post_d:
                    post_body += '<br>' + key + ' = ' + post_d[key]

                # process the posted data
                process_post_data(post_d)

                self.send_response(200)
                self.send_header("Content-type", "text/html")
                self.end_headers()
                response = "<html><head><title>Config.</title></head><body>"
                response += build_get_page_body(self.path)
                response += "<br><br>Posted<br>"

                response += post_body

                response += "</body></html>"
            except:
                response = traceback.format_exc().replace('\n','<br>')

            self.wfile.write(bytes(response, "utf-8"))

    myServer = HTTPServer(("localhost", 7000), MyServer)

    try:
        myServer.serve_forever()
    except KeyboardInterrupt:
        pass

    myServer.server_close()




# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_config_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_config.cpython-37.pyc", "/cardinal/pi_config.pyc")

    if os.path.isfile("/cardinal/pi_config.py"):
        os.remove("/cardinal/pi_config.py")

    try:
        with open('/dev/shm/pi_config_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    do_one_time()


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')
