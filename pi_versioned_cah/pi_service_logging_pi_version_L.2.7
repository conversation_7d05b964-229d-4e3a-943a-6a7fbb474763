_ = """
sudo vi /cardinal/pi_logging.py

sudo systemctl restart pi-logging.service

sudo systemctl status pi-logging.service

watch -n 1 sudo cat /dev/shm/pi_logging_datadrop.txt


ls -l /cardinal/log/pi_logging/

cat /cardinal/log/pi_logging/20211007/device_event0.txt

cat /cardinal/log/pi_logging/20211007/lsusb_change.txt



"""

service = 'logging'
version = 'L.2.7'
description = """
This is a service to manage logging on the device, and to report log status.

"""

release_notes = """
2022.05.09
L.2.7
Also keep /var/log/audit clean of old logs.

2022.05.06
L.2.6
Eliminate all print (reduce logging to minimum)
Try to keep /var/log directory clean of old logs

2022.04.06
L.2.5
Improved the writing to the log file.

2022.02.14
L.2.4
Convert the ELAN touch screen and the USB events into HID reporting (PR005 devices)

2022.01.31
L.2.3
Add support for looking up the call_home_locations

2022.01.20
L.2.2
Report total network transmit and receive errors.

2022.01.19
L.2.1
Report wifi statistics to be shared back through the runner: bitrate, frequency, level, noise

2022.01.14
L.2.0
Fix bug where logging would not startup on a clean install, with no user events yet logged.

2021.12.20
L.1.9
Log and send the device event counts.

2021.10.18
L.1.8
Add monitoring of lsusb, looking for any device changes.

2021.10.14
L.1.4
fix an issue where the normal logging was disabled

2021.10.11
L.1.3
udevadm logging

2021.10.06
L.1.1
Make an entry in the log when starting up, to know where the previous last item was.

2021.10.06
L.1.0
Keyboard/mouse activity logging, to help with debugging PR005 extra session windows,
and MEX09 touchpad deactivation issues.

"""

congestion_notes = """
sysctl net.ipv4.tcp_available_congestion_control

sudo modprobe tcp_bbr
sudo echo "tcp_bbr" > /etc/modules-load.d/bbr.conf
(failed)

sysctl net.ipv4.tcp_available_congestion_control

sudo vi /etc/sysctl.conf

----- start -----
net.core.default_qdisc = fq    # BBR must be used with fq qdisc, see note below
net.ipv4.tcp_congestion_control = bbr
-----  end  -----

sysctl net.ipv4.tcp_congestion_control
sudo sysctl -p
sysctl net.ipv4.tcp_congestion_control

sudo apt-get install -y iperf3

iperf3 -C bbr -c slicer.cardinalhealth.net

https://www.tecmint.com/ss-command-examples-in-linux/
https://www.howtogeek.com/681468/how-to-use-the-ss-command-on-linux/
https://www.maketecheasier.com/ss-command-monitor-network-connections-linux/

ss -tin

ss -s

Another source:
cat /sys/class/net/eth0/statistics/*

------------------
yet another:
https://phoenixnap.com/kb/linux-network-speed-test

sudo apt install -y speedtest-cli
speedtest --simple

Works well, but will do a lot of data upload/download, potentially making the congestion on the network worse.
Maybe reserve this for an 'on demand' speedtest, and keep results like the screen grabs.

------------------

sudo apt install -y cbm



------------------
sudo apt install -y nload

------------------
Speed of wlan interface:

iwconfig

cat /sys/class/net/wlan0/wireless/link


------------------
------------------

"""

other_content = """
sudo vi /cardinal/pi-logging
sudo chmod +x /cardinal/pi-logging

# ===== begin: start file
#!/usr/bin/env python3
import pi_logging
pi_logging.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-logging.service
sudo systemctl daemon-reload
sudo systemctl stop pi-logging.service
sudo systemctl start pi-logging.service
sudo systemctl enable pi-logging.service

systemctl status pi-logging.service

sudo systemctl restart pi-logging.service

# Logging of std out
cat /var/log/syslog | fgrep pi-logging

OR

tail -f /var/log/syslog | fgrep pi-logging

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-logging
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""


_ = """
keystroke logging

# https://thehackerdiary.wordpress.com/2017/04/21/exploring-devinput-1/

import struct
f = open( "/dev/input/event0", "rb" ); # Open the file in the read-binary mode
while 1:
  data = f.read(16)
  print (struct.unpack('LLHHI',data))
  ###### PRINT FORMAL = ( Time Stamp_INT , 0 , Time Stamp_DEC , 0 ,
  ######   type , code ( key pressed ) , value (press/release) )

# https://www.kernel.org/doc/html/v4.17/input/event-codes.html
# https://github.com/torvalds/linux/blob/master/include/uapi/linux/input-event-codes.h
# https://thehackerdiary.wordpress.com/2017/04/21/exploring-devinput-1/


"""

_20211011 = """
udevadm monitor

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_logging


"""


import copy
import datetime
import json
import math
import os
try:
    import requests
except:
    pass # for unittest
import shutil
import socket
import struct
import subprocess
import sys
import time
import traceback
import unittest

from concurrent.futures import ThreadPoolExecutor
s_executor = ThreadPoolExecutor(100)
# line up all of the worker threads
s_monitored_devices = {}

s_days_to_keep = 14
s_base_raw_path = "/cardinal/log/pi_logging/"
s_temp_base_path_counts = "/dev/shm/logging_counts/"

# ----------------------------
def build_iwconfig_counts (pass_string):
# ----------------------------
    the_counts = {'wifi_bitrate':'', 'wifi_frequency':''}

    for item_line in pass_string.split('\n'):
        if 'Bit Rate=' in item_line:
            the_counts['wifi_bitrate'] = item_line.split('Bit Rate=')[1].split('Tx-Power')[0].strip()

        if 'Frequency:' in item_line:
            the_counts['wifi_frequency'] = item_line.split('Frequency:')[1].split('Access Point:')[0].strip()

    return the_counts

# ----------------------------
def build_wireless_counts (pass_string):
# ----------------------------
    the_counts = {'wifi_level':'', 'wifi_noise':''}

    for item_line in pass_string.split('\n'):
        if 'wlan' in item_line:
            try:
                the_counts['wifi_level'] = item_line.strip().split()[3]
                the_counts['wifi_noise'] = item_line.strip().split()[4]
            except:
                pass

    return the_counts

# ----------------------------
def build_network_counts(network_stats):
# ----------------------------
    the_counts = {'net_tx_errors':'0', 'net_rx_errors':'0', 'net_tx_dropped':'0', 'net_rx_dropped':'0'}

    for key in network_stats:
        try:
            value = int(network_stats[key].split('\n')[0])

            if 'errors' in key:
                if 'tx_' in key:
                    the_counts['net_tx_errors'] = str(int(the_counts['net_tx_errors']) + value)

                if 'rx_' in key:
                    the_counts['net_rx_errors'] = str(int(the_counts['net_rx_errors']) + value)

            if 'dropped' in key:
                if 'tx_' in key:
                    the_counts['net_tx_dropped'] = str(int(the_counts['net_tx_dropped']) + value)

                if 'rx_' in key:
                    the_counts['net_rx_dropped'] = str(int(the_counts['net_rx_dropped']) + value)

        except:
            #print (traceback.format_exc())
            pass

    _others_not_yet_used = """
['rx_nohandler',
 'collisions',
 'tx_packets',
 'rx_compressed',
 'tx_bytes',
 'multicast',
 'rx_packets',
 'rx_bytes',
 'tx_compressed']
    """

    return the_counts

# ----------------------------
def get_network_statistics():
# ----------------------------
    # go through all in the dir /sys/class/net, and find all files and contents in /statistics/*

    result = {}
    net_base_path = '/sys/class/net/'
    try:
        interfaces = os.listdir(net_base_path) # like ['wlan0', 'lo', 'eth0']

        for interface in interfaces:
            if interface != 'lo':
                all_files = os.listdir(net_base_path + interface + '/statistics/')
                for all_file in all_files:
                    name_to_use = interface + '_' + all_file
                    result[name_to_use] = open(net_base_path + interface + '/statistics/' + all_file, 'r').read()
    except:
        pass

    return result

# ----------------------------
def get_device_event_linkage():
# ----------------------------
    try:
        content = open('/proc/bus/input/devices', 'r').read()
    except:
        content = ''

    return parse_device_event_linkage(content)

# ----------------------------
def parse_device_event_linkage(content):
# ----------------------------
    linkage = {}
    if content:
        current_name = ''
        for line in content.split('\n'):
            if line[:2] == 'N:':
                try:
                    current_name = line.split('"')[1].split()[0]

                    current_name = current_name.replace('ELAN', 'HID').replace('USB', 'HID')
                except:
                    current_name = ''
            if line[:2] == 'H:' and current_name:
                try:
                    current_handlers = line.split('=')[1].split()
                    if current_name:
                        if not current_name in linkage:
                            linkage[current_name] = []
                        for item in current_handlers:
                            if not item in linkage[current_name]:
                                linkage[current_name].append(item)
                except:
                    current_handlers = ''
    # linkage = {'HID': ['sysrq', 'kbd', 'leds', 'event0', 'event1', 'event2', 'mouse0', 'event3'], 'RS6000': ['sysrq', 'kbd', 'leds', 'event4']}
    return linkage

# ----------------------------
def log_cleanup():
# ----------------------------
    days_present = sorted(os.listdir(s_base_raw_path))
    if len(days_present) > s_days_to_keep:
        for day_to_remove in days_present[:-1 * s_days_to_keep]:
            try:
                shutil.rmtree(s_base_raw_path + day_to_remove)
            except:
                pass

# ----------------------------
def execute(cmd):
# ----------------------------
    popen = subprocess.Popen(cmd, stdout=subprocess.PIPE, universal_newlines=True)
    for stdout_line in iter(popen.stdout.readline, ""):
        yield stdout_line
    popen.stdout.close()
    return_code = popen.wait()
    if return_code:
        raise subprocess.CalledProcessError(return_code, cmd)

# ----------------------------
def fnc_log_lsusb():
# ----------------------------
    bus_devices = {}

    TS = datetime.datetime.now().strftime('%Y%m%d')
    output_file = s_base_raw_path + TS + "/lsusb_change" + ".txt"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    TSfast = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    report = str(TSfast) + ',' + 'Start logging' + '\n'

    try:
        with open(output_file, 'a') as f:
                f.write(report)
    except:
        pass

    while(True):

        pass_string, fail = do_one_command('lsusb')
        try:
            with open('/dev/shm/pi_logging_lsusb.txt', 'w') as f:
                f.write(pass_string)
        except:
            pass


        _ = """
Bus 002 Device 004: ID 0424:5744 Standard Microsystems Corp. Hub
Bus 002 Device 001: ID 1d6b:0003 Linux Foundation 3.0 root hub
Bus 001 Device 010: ID 258a:0131
Bus 001 Device 008: ID 0424:2744 Standard Microsystems Corp. Hub
Bus 001 Device 002: ID 2109:3431 VIA Labs, Inc. Hub
Bus 001 Device 001: ID 1d6b:0002 Linux Foundation 2.0 root hub
        """

        for line_found in pass_string.split('\n'):
            report = ''

            splits = line_found.split()
            if len(splits) > 3:
                bus = splits[1]
                device = splits[3].replace(':','')
                bus_device = bus + ':' + device

                if bus_device in bus_devices:
                    if bus_devices[bus_device] != line_found:
                        report = 'change -> ' + bus_device + ' -> ' + line_found
                        bus_devices[bus_device] = line_found
                else:
                    report = '   new -> ' + bus_device + ' -> ' + line_found
                    bus_devices[bus_device] = line_found


                if report:
                    now_time = datetime.datetime.now()
                    TS = now_time.strftime('%Y%m%d')
                    output_file = s_base_raw_path + TS + "/lsusb_change" + ".txt"
                    if not os.path.exists(os.path.dirname(output_file)):
                        os.makedirs(os.path.dirname(output_file))

                    TSfast = now_time.strftime('%Y%m%d%H%M%S%f')
                    report = str(TSfast) + ' '+ report + '\n'

                    try:
                        with open(output_file, 'a') as f:
                                f.write(report)
                    except:
                        pass

        time.sleep(15)

# ----------------------------
def fnc_log_udevadm():
# ----------------------------
    """
cat  /cardinal/log/pi_logging/20211011/udevadm.txt

    """

    TS = datetime.datetime.now().strftime('%Y%m%d')
    output_file = s_base_raw_path + TS + "/udevadm" + ".txt"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    TSfast = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    report = str(TSfast) + ',' + 'Start logging' + '\n'

    with open(output_file, 'a') as f:
            f.write(report)

    for line_read in execute(["udevadm", "monitor"]):
        TS = datetime.datetime.now().strftime('%Y%m%d')
        output_file = s_base_raw_path + TS + "/udevadm" + ".txt"

        TSfast = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
        report = str(TSfast) + ',' + line_read.replace('\n','') + '\n'

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'a') as f:
            f.write(report)

        log_cleanup()

# ----------------------------
def do_atomic_write_if_different(output_file, content):
# ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        shutil.move(temp_name, output_file)

# ----------------------------
def log_from_input_device(filename):
# ----------------------------
    device_name = filename.split('/')[-1]
    counts_file = s_temp_base_path_counts + device_name + '.txt'

    TS = datetime.datetime.now().strftime('%Y%m%d')
    output_file = s_base_raw_path + TS + "/device_" + device_name + ".txt"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'a') as f:
        f.write(str(int(time.time())) + ",-1,-1,-1" + "\n")

    count_events = 0
    event_f = open(filename, 'rb')
    while(True):
        data = event_f.read(16) # blocking if there is no data
        count_events += 1
        do_atomic_write_if_different(counts_file, str(count_events))
        (time_second, time_microsecond, key_type, key_code, key_value) = struct.unpack('LLHHI',data)
        report = str(time_second) + ',' + str(key_type) + ',' + str(key_code) + ',' + str(key_value) + '\n'

        TS = datetime.datetime.now().strftime('%Y%m%d')
        output_file = s_base_raw_path + TS + "/device_" + device_name + ".txt"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'a') as f:
            f.write(report)

        log_cleanup()

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)



# ----------------------------
def do_one_time():
# ----------------------------
    list_of_cmds = []
#    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
# ----------------------------
    the_file = '/dev/shm/pi_logging_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')

# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def do_datadrop():
# ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('logging data drop: Start',True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in
            try:
                do_datadrop_debug('Start logging...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug('logging result: ' + url_result)

                try:
                    result_json = json.loads(url_result) # This will throw exception if the previous block passed 'exception'

                    if 'action_request' in result_json:
                        pass
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())


    do_datadrop_debug('logging data drop: End')


# ----------------------------
def do_clean_logs():
# ----------------------------
    _ = """
syslog and daemon.log is where all the print statements from our services end up.

ls -l /var/log
drwxr-xr-x 2 <USER>        <GROUP>      4096 May  6 14:51 apt
drwxr-x--- 2 <USER>        <GROUP>       4096 May  4 05:25 audit
-rw-r----- 1 <USER>        <GROUP>  132219556 May  6 15:00 auth.log
-rw-rw---- 1 <USER>        <GROUP>         0 Apr 30 20:00 btmp
drwxr-xr-x 2 <USER>        <GROUP>      4096 May  5 01:00 cups
-rw-r----- 1 <USER>        <GROUP>  311291252 May  6 15:00 daemon.log
-rw-r----- 1 <USER>        <GROUP>       1802 May  2 19:30 debug
-rw-r--r-- 1 <USER>        <GROUP>      8286 May  6 14:47 dpkg.log
drwxr-s--- 2 Debian-exim adm       4096 May  5 17:16 exim4
-rw-r----- 1 <USER>        <GROUP>       1470 May  2 19:30 fail2ban.log
-rw-r----- 1 <USER>        <GROUP>     827598 May  6 14:59 kern.log
-rw-rw-r-- 1 <USER>        <GROUP>    292876 May  5 07:44 lastlog
-rw-r----- 1 <USER>        <GROUP>    456317 May  6 14:51 lynis.log
-rw-r----- 1 <USER>        <GROUP>     47825 May  6 14:51 lynis-report.dat
-rw-r----- 1 <USER>        <GROUP>          0 May  6 13:46 mail.err
-rw-r----- 1 <USER>        <GROUP>          0 May  6 13:46 mail.info
-rw-r----- 1 <USER>        <GROUP>          0 May  6 13:46 mail.log
-rw-r----- 1 <USER>        <GROUP>          0 May  6 13:46 mail.warn
-rw-r----- 1 <USER>        <GROUP>    1296436 May  6 14:59 messages
drwx------ 2 <USER>        <GROUP>      4096 Mar  4  2021 private
drwxr-x--- 2 <USER>     <GROUP>       4096 Apr 30 20:00 privoxy
-rw-r----- 1 <USER>        <GROUP>   22524015 May  6 15:00 syslog
drwxr-xr-x 2 <USER>        <GROUP>      4096 Nov 29 10:37 sysstat
-rw-r----- 1 <USER>        <GROUP>     683446 May  6 14:57 user.log
-rw-rw-r-- 1 <USER>        <GROUP>   3841536 May  6 14:57 wtmp

https://access.redhat.com/solutions/2316

sudo lsof | egrep "deleted|COMMAND"
(shows rsyslogd and fail2ban own the deleted files, and still have them open)

sudo service rsyslog restart



    """

    match_list = ['syslog', '.log','.1', '.2', '.3', '.4', '.5', '.6', '.7', '.8', '.9']
    to_remove = {}
    try:
        for base_directory in ['/var/log/', '/var/log/audit/']:
            contents = os.listdir(base_directory)
            for file_name in contents:
                for matcher in match_list:
                    if matcher in file_name:
                        to_remove[base_directory + file_name] = True
    except:
        pass

    for full_name in to_remove:
        try:
            os.remove(full_name)
        except:
            pass

    try:
        pass_string, fail = do_one_command('sudo service rsyslog restart')
    except:
        pass

    try:
        pass
        #os.remove('/usr/lib/chromium-browser/chromium-browser-v7')
    except:
        pass

# ----------------------------
def do_maintenance():
# ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()
    do_clean_logs()

# ----------------------------
def get_event_counts():
# ----------------------------
    the_event_counts = {}

    try:
        count_files = os.listdir(s_temp_base_path_counts)
    except:
        count_files = []

    for count_file in count_files:
        if ('.txt' in count_file) and (not '.tmp' in count_file):
            try:
                counts = int(open(s_temp_base_path_counts + count_file, 'r').read())
            except:
                counts = 0
            the_event = count_file.replace('.txt','')
            the_event_counts[the_event] = counts

    return the_event_counts

# ----------------------------
def build_device_counts(linkage, old_counts, new_counts):
# ----------------------------
    # linkage = {'HID': ['sysrq', 'kbd', 'leds', 'event0', 'event1', 'event2', 'mouse0', 'event3'], 'RS6000': ['sysrq', 'kbd', 'leds', 'event4']}
    the_device_counts = {}

    for event in new_counts:
        previous = 0
        if event in old_counts:
            previous = old_counts[event]
        diff = new_counts[event] - previous
        if diff < 0:
            diff = 0
        for device in linkage:
            if event in linkage[device]:
                if not device in the_device_counts:
                    the_device_counts[device] = 0
                the_device_counts[device] += diff

    return the_device_counts

# ----------------------------
def build_shared_device_counts(the_device_counts):
# ----------------------------
    # do reporting
    devices_to_report = ['HID', 'RS6000'] # These are the minimum basic requirements
    for device in the_device_counts:
        if not device in devices_to_report:
            devices_to_report.append(device)

    the_counts = {}
    for device in devices_to_report:
        diff = 0
        if device in the_device_counts:
            diff = the_device_counts[device]

        the_counts[device] = str(diff)

    return the_counts

# ----------------------------
def save_shared_counts(the_counts):
# ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_logging_' + the_count
            temp_name = file_name.replace('shared_', 'temp_')
            with open (temp_name, 'w') as f:
                f.write(str(the_counts[the_count]))
            shutil.move(temp_name, file_name)
        except:
            pass

# ----------------------------
def check_for_inputs():
# ----------------------------
    global s_monitored_devices

    report_what = ''

    # see if there are new devices to monitor
    try:
        device_dir = '/dev/input/'
        for device_name in os.listdir(device_dir):
            if ('event' in device_name) or ('mouse' in device_name):
                full_device_path = device_dir + device_name
                if not full_device_path in s_monitored_devices:
                    s_monitored_devices[full_device_path] = {'executor':None, 'is_done':True} # Make the arrival of the new device be an activity.

                    #job_id_thread1 = sched.add_job(thread_call_back_to_slicer, 'interval', seconds=30, coalesce=True)
                    # https://thehackerdiary.wordpress.com/tag/devinput-python/
                    # https://stackoverflow.com/questions/39948588/non-blocking-file-read/39948643

        # run through all the monitored devices, and if they are done, start a new executor
        for monitored_device in sorted(s_monitored_devices):
            report_what += '-----------------------------------' + '\n'
            report_what += 'device: ' + monitored_device + '\n'

            if s_monitored_devices[monitored_device]['executor']:
                s_monitored_devices[monitored_device]['is_done'] = s_monitored_devices[monitored_device]['executor'].done()

            if s_monitored_devices[monitored_device]['is_done']:
                report_what += 'is done' + '\n'
                try:
                    del(s_monitored_devices[monitored_device]['executor'])
                except:
                    pass

                try:
                    report_what += 'assigning executor...' + '\n'
                    s_monitored_devices[monitored_device]['executor'] = s_executor.submit(log_from_input_device, monitored_device)
                    report_what += 'assigned executor' + '\n'
                except:
                    report_what += 'exception assigning executor' + '\n'
                    with open ('/dev/shm/pi_logging_inputs_exception', 'w') as f:
                        f.write(traceback.format_exc())
            else:
                report_what += 'is not done' + '\n'
    except:
        with open ('/dev/shm/pi_logging_inputs_exception', 'w') as f:
            f.write(traceback.format_exc())

    with open('/dev/shm/pi_logging_inputs_report_what', 'w') as f:
        f.write(report_what + '\n')


# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_logging.cpython-37.pyc", "/cardinal/pi_logging.pyc")

    if os.path.isfile("/cardinal/pi_logging.py"):
        os.remove("/cardinal/pi_logging.py")

    try:
        with open('/dev/shm/pi_logging_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    do_one_time()

    # launch monitor(s) that just do their thing
    log_udevadm = s_executor.submit(fnc_log_udevadm)
    #log_urb = s_executor.submit(read_urb_data)

    log_libusb = s_executor.submit(fnc_log_lsusb)

    wake_count = 0
    time_of_last_input_check = 0

    minutes_for_outer_loop = 30
    seconds_for_inner_loop = 60

    old_counts = {}
    while True:
        # do system maintenance (once every minutes_for_outer_loop minutes
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * minutes_for_outer_loop):
            time_of_check = time.time()
            time_diff_of_check = abs(time_of_last_input_check - time_of_check)

            do_atomic_write_if_different('/dev/shm/pi_logging_timediff', str(time_diff_of_check))
            if time_diff_of_check > seconds_for_inner_loop:
                time_of_last_input_check = time_of_check

                # ==============================================================
                # Do the gathering and reporting to the /dev/shm/shared_ files
                # ==============================================================
                # HID and RS6000
                check_for_inputs()
                linkage = get_device_event_linkage()
                new_counts = get_event_counts()
                the_device_counts = build_device_counts(linkage, old_counts, new_counts)
                the_counts = build_shared_device_counts(the_device_counts)
                save_shared_counts(the_counts)
                old_counts = copy.deepcopy(new_counts)

                # Wifi Network statistics
                pass_string, fail = do_one_command('iwconfig')
                the_counts = build_iwconfig_counts(pass_string)
                save_shared_counts(the_counts)

                pass_string, fail = do_one_command('cat /proc/net/wireless')
                the_counts = build_wireless_counts(pass_string)
                save_shared_counts(the_counts)

                the_counts = build_network_counts(get_network_statistics())
                save_shared_counts(the_counts)


            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_logging_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                #print ("!!! failed to write wake_count")
                pass

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_device_event_counts(self):
        """
        (fill in here)
        """
        linkage = {'HID': ['sysrq', 'kbd', 'leds', 'event0', 'event1', 'event2', 'mouse0', 'event3'], 'RS6000': ['sysrq', 'kbd', 'leds', 'event4']}

        old_counts = {}
        new_counts = {'event0':1}
        expected_result = {'HID':1}
        actual_result = build_device_counts(linkage, old_counts, new_counts)
        self.assertEqual(actual_result, expected_result)

        old_counts = {'event0':1}
        new_counts = {'event0':1}
        expected_result = {'HID':0}
        actual_result = build_device_counts(linkage, old_counts, new_counts)
        self.assertEqual(actual_result, expected_result)

        old_counts = {'event0':1}
        new_counts = {'event0':4,'mouse0':2}
        expected_result = {'HID':5}
        actual_result = build_device_counts(linkage, old_counts, new_counts)
        self.assertEqual(actual_result, expected_result)

        old_counts = {'event0':1}
        new_counts = {'event0':4,'mouse0':2, 'event4':2}
        expected_result = {'HID':5, 'RS6000':2}
        actual_result = build_device_counts(linkage, old_counts, new_counts)
        self.assertEqual(actual_result, expected_result)

        # handle a roll over as a zero
        old_counts = {'event0':1}
        new_counts = {'event0':0}
        expected_result = {'HID':0}
        actual_result = build_device_counts(linkage, old_counts, new_counts)
        self.assertEqual(actual_result, expected_result)


    def test_build_iwconfig_counts(self):
        pass_string = """
lo        no wireless extensions.

eth0      no wireless extensions.

wlan0     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=31 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on
        """
        expected_result = {'wifi_bitrate':'', 'wifi_frequency':''}

        actual_result = build_iwconfig_counts(pass_string)
        self.assertEqual(actual_result, expected_result)

        pass_string = """
lo        no wireless extensions.

wlan0     IEEE 802.11  ESSID:"corp"
          Mode:Managed  Frequency:5.24 GHz  Access Point: 20:A6:CD:D4:6B:91
          Bit Rate=54 Mb/s   Tx-Power=31 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on
          Link Quality=46/70  Signal level=-64 dBm
          Rx invalid nwid:0  Rx invalid crypt:0  Rx invalid frag:0
          Tx excessive retries:483  Invalid misc:0   Missed beacon:0

eth0      no wireless extensions."""
        expected_result = {'wifi_bitrate':'54 Mb/s', 'wifi_frequency':'5.24 GHz'}

        actual_result = build_iwconfig_counts(pass_string)
        self.assertEqual(actual_result, expected_result)


        pass_string = """
lo        no wireless extensions.

eth0      no wireless extensions.

wlan0     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=31 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on

wlan1     IEEE 802.11  ESSID:"corp"
          Mode:Managed  Frequency:5.2 GHz  Access Point: E8:26:89:8E:72:B1
          Bit Rate=48 Mb/s   Tx-Power=20 dBm
          Retry short  long limit:2   RTS thr:off   Fragment thr:off
          Power Management:off
          Link Quality=28/70  Signal level=-82 dBm
          Rx invalid nwid:0  Rx invalid crypt:0  Rx invalid frag:0
          Tx excessive retries:0  Invalid misc:0   Missed beacon:0
"""
        expected_result = {'wifi_bitrate':'48 Mb/s', 'wifi_frequency':'5.2 GHz'}

        actual_result = build_iwconfig_counts(pass_string)
        self.assertEqual(actual_result, expected_result)

    def test_build_wireless_counts(self):
        pass_string = ''
        expected_result = {'wifi_level':'', 'wifi_noise':''}

        actual_result = build_wireless_counts(pass_string)
        self.assertEqual(actual_result, expected_result)


        pass_string = """
Inter-| sta-|   Quality        |   Discarded packets               | Missed | WE
 face | tus | link level noise |  nwid  crypt   frag  retry   misc | beacon | 22
  wlan1: 0000   30.  -80.  -256        0      0      0      0      0        0
"""
        expected_result = {'wifi_level':'-80.', 'wifi_noise':'-256'}

        actual_result = build_wireless_counts(pass_string)
        self.assertEqual(actual_result, expected_result)


    def test_build_network_counts(self):
        network_stats = {'tx_fifo_errors': '0\n'}
        expected_result = {'net_tx_errors':'0', 'net_rx_errors':'0', 'net_tx_dropped':'0', 'net_rx_dropped':'0'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)

        network_stats = {'tx_fifo_errors': '1\n'}
        expected_result = {'net_tx_errors':'1', 'net_rx_errors':'0', 'net_tx_dropped':'0', 'net_rx_dropped':'0'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)

        network_stats = {'tx_fifo_errors': '1\n', 'tx_frame_errors': '2\n'}
        expected_result = {'net_tx_errors':'3', 'net_rx_errors':'0', 'net_tx_dropped':'0', 'net_rx_dropped':'0'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)

        network_stats = {'tx_fifo_errors': '1\n', 'rx_frame_errors': '2\n'}
        expected_result = {'net_tx_errors':'1', 'net_rx_errors':'2', 'net_tx_dropped':'0', 'net_rx_dropped':'0'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)

        network_stats = {'tx_fifo_errors': '1\n', 'rx_frame_errors': '2\n', 'tx_dropped':'7\n'}
        expected_result = {'net_tx_errors':'1', 'net_rx_errors':'2', 'net_tx_dropped':'7', 'net_rx_dropped':'0'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)


        network_stats = {'tx_fifo_errors': '1\n', 'rx_frame_errors': '2\n', 'tx_dropped':'7\n', 'rx_dropped':'9\n'}
        expected_result = {'net_tx_errors':'1', 'net_rx_errors':'2', 'net_tx_dropped':'7', 'net_rx_dropped':'9'}

        actual_result = build_network_counts(network_stats)
        self.assertEqual(actual_result, expected_result)

    def test_event_linkage(self):
        # 10000000d86aa7e9
        content = """
I: Bus=0003 Vendor=060b Product=0540 Version=0111
N: Name="USB Keyboard"
P: Phys=usb-0000:01:00.0-1.3/input0
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3:1.0/0003:060B:0540.0002/input/input4
U: Uniq=
H: Handlers=sysrq kbd leds event3
B: PROP=0
B: EV=120013
B: KEY=10000 7 ff800000 7ff febeffdf f3cfffff ffffffff fffffffe
B: MSC=10
B: LED=7

I: Bus=0003 Vendor=060b Product=0540 Version=0111
N: Name="USB Keyboard"
P: Phys=usb-0000:01:00.0-1.3/input1
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3:1.1/0003:060B:0540.0003/input/input5
U: Uniq=
H: Handlers=mouse1 event4
B: PROP=0
B: EV=17
B: KEY=1f0000 0 0 0 0 0 0 0 0
B: REL=903
B: MSC=10

I: Bus=0003 Vendor=04f3 Product=249f Version=0110
N: Name="ELAN Touchscreen"
P: Phys=usb-0000:01:00.0-1.2/input0
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.2/1-1.2:1.0/0003:04F3:249F.0001/input/input6
U: Uniq=
H: Handlers=mouse0 event0
B: PROP=2
B: EV=1b
B: KEY=400 0 0 0 0 0 0 0 0 0 0
B: ABS=32738000 3
B: MSC=20

I: Bus=0003 Vendor=04f3 Product=249f Version=0110
N: Name="ELAN Touchscreen UNKNOWN"
P: Phys=usb-0000:01:00.0-1.2/input0
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.2/1-1.2:1.0/0003:04F3:249F.0001/input/input7
U: Uniq=
H: Handlers=event1
B: PROP=0
B: EV=9
B: ABS=100 0

I: Bus=0003 Vendor=04f3 Product=249f Version=0110
N: Name="ELAN Touchscreen UNKNOWN"
P: Phys=usb-0000:01:00.0-1.2/input0
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.2/1-1.2:1.0/0003:04F3:249F.0001/input/input8
U: Uniq=
H: Handlers=event2
B: PROP=0
B: EV=100001

        """

        actual_result = parse_device_event_linkage(content)
        expected_result = {'HID': ['sysrq','kbd','leds','event3','mouse1','event4','mouse0','event0','event1','event2']}

        self.assertEqual(actual_result, expected_result)

        content = """
I: Bus=0003 Vendor=04d9 Product=a088 Version=0111
N: Name="HID 04d9:a088"
P: Phys=usb-0000:01:00.0-1.3.4/input0
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3.4/1-1.3.4:1.0/0003:04D9:A088.0001/input/input0
U: Uniq=
H: Handlers=sysrq kbd leds event0
B: PROP=0
B: EV=120013
B: KEY=10000 7 ff800000 7ff febeffdf f3cfffff ffffffff fffffffe
B: MSC=10
B: LED=7

I: Bus=0003 Vendor=04d9 Product=a088 Version=0111
N: Name="HID 04d9:a088 System Control"
P: Phys=usb-0000:01:00.0-1.3.4/input1
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3.4/1-1.3.4:1.1/0003:04D9:A088.0002/input/input1
U: Uniq=
H: Handlers=kbd event1
B: PROP=0
B: EV=13
B: KEY=c000 100000 0 0 0
B: MSC=10

I: Bus=0003 Vendor=04d9 Product=a088 Version=0111
N: Name="HID 04d9:a088 Consumer Control"
P: Phys=usb-0000:01:00.0-1.3.4/input1
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3.4/1-1.3.4:1.1/0003:04D9:A088.0002/input/input2
U: Uniq=
H: Handlers=kbd event2
B: PROP=0
B: EV=13
B: KEY=10000 2000000 39fa d9411001 e0000 0 0 0
B: MSC=10

I: Bus=0003 Vendor=04d9 Product=a088 Version=0111
N: Name="HID 04d9:a088 Mouse"
P: Phys=usb-0000:01:00.0-1.3.4/input1
S: Sysfs=/devices/platform/scb/fd500000.pcie/pci0000:00/0000:00:00.0/0000:01:00.0/usb1/1-1/1-1.3/1-1.3.4/1-1.3.4:1.1/0003:04D9:A088.0002/input/input3
U: Uniq=
H: Handlers=mouse0 event3
B: PROP=0
B: EV=17
B: KEY=70000 0 0 0 0 0 0 0 0
B: REL=3
B: MSC=10

I: Bus=0005 Vendor=01f1 Product=000c Version=0201
N: Name="RS6000 19114523022499   (RS507 compatible)"
P: Phys=dc:a6:32:96:56:c6
S: Sysfs=/devices/platform/soc/fe201000.serial/tty/ttyAMA0/hci0/hci0:12/0005:01F1:000C.0003/input/input4
U: Uniq=80:6f:b0:96:16:08
H: Handlers=sysrq kbd leds event4
B: PROP=0
B: EV=120013
B: KEY=10000 7 ff9f207a c14057ff febeffdf ffefffff ffffffff fffffffe
B: MSC=10
B: LED=1f
    """

        actual_result = parse_device_event_linkage(content)
        expected_result = {'HID': ['sysrq', 'kbd', 'leds', 'event0', 'event1', 'event2', 'mouse0', 'event3'], 'RS6000': ['sysrq', 'kbd', 'leds', 'event4']}

        self.assertEqual(actual_result, expected_result)





