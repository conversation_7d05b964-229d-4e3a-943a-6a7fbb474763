_ = """
sudo vi /cardinal/pi_hmi.py

sudo systemctl restart pi-hmi.service

"""

service = 'hmi'
version = 'H.3.4'

_ = """
ppt notes:
Maybe try libreoffice commandline tools to make PDF, then show pdf
https://askubuntu.com/questions/11130/how-can-i-convert-a-ppt-to-a-pdf-from-the-command-line
https://forums.raspberrypi.com/viewtopic.php?t=98249

"""

release_notes = """
2022.01.06
H.3.4
Show the ring level below the image version on the main page.
On ID page, show the wifi network we are configured for, if not on wired.
Show the image build number on the main screen, in the upper left box.
On the main page, if the network communications are off line, then the address box
will first turn yellow, and then turn red, in order to draw attention to the fact that
it is not able to call home to Slicer.
Fix an issue with the whitelist service.

2021.11.29
H.3.1
Add link to open CUPS configuration page on ID page.

2021.11.24
H.3.0
If there are ring updates available, show an asterisk on the ID prompt, and highlight that cell blue.
On ID screen, display my ring level and update count.
If the update count is not zero, then show an asterisk after the value.
If the count is not zero, then highlight the value in blue.

2021.11.08
H.2.5
Support showing debug content on ID screen.

2021.11.03
H.2.4
On ID details page, for wifi, report which adapter is providing the connection, like wlan0.
Show that on the main page as well, after the address.
When on wired connection, show which eth port is being used.

2021.10.29
H.2.2
Add display of mac address for second wifi, and possible second wired ethernet.

2021.10.14
H.2.1
Change the id screen title for mac addresses, to match what is being proposed for Service Now entry.

2021.08.14
H.2.0
Show alternate home screen, with MAC addresses, as index2.html
If not in kiosk mode, then show the F11 key to exit full screen.
Give a delay before grabbing the screen resolution from the hardware.


2021.07.22
H.1.9
Build the logo options

2021.07.20
H.1.8
Experiment with running index page as content inside an iframe.
# If the index was accessed at reload-2 to reload+2 second, then do not change it yet.
If all keyboard access is allowed, show ctrl key hints in the tabbed homepage view.
Change write_content_if_different to use same physical device as the destination,
so that mv (move file) can be atomic (not vulnerable to read while partially written).

2021.07.12
H.1.7
Always build the equivalent set of bookmarks as an iframe view. (Not showing yet)
Use the bookmarks format setting to control how the page is built.
Add a shutdown / reboot / menu screen

2021.07.03
H.1.6
Change the formatting, to better fit on smaller screens.

2021.06.24
H.1.5
Fixed the issue where 12 noon was still shown as AM.

2021.06.22
H.1.4
Add support for clock view on the visual interface.
Show the screen zoom number after the resolution numbers.

2021.06.15
H.1.3
Add support for bluetooth pairing visual interface.
Build local page once each second, but only save it on change from last saved.

2021.05.25
H.1.2
Pickup and show the name to use for the device.

2021.05.19
H.1.1
Have the boomark option of "autolaunch" cause the browser to autolaunch that bookmark
when a timer runs out.

2021.05.17
H.1.0
Put the QR code generation in a try block, so that the install can do its work,
and then only load the QR library when it is need, and should be there by that time.
Bypass the one-time installs, and put them back into the image building.

2021.05.17
H.0.9
In the one_time task, install all the tools we need; that way this service
can be loaded onto old images, before 2.0.0, to provide the browser certificate.

2021.05.15
H.0.8
Make the bookmarks smaller also, when the list is longer

2020.05.14
H.0.7
Version bump, to test updates

2021.05.10
H.0.6
Make clean outputs for the logo and the root certificate (fix the xxd)

2021.05.05
H.0.5
Found issue with writing the whitelist file

2021.05.05
H.0.4
Put the file system expand back in.

2021.05.05
H.0.3
Implement the handling of whitelist for privoxy.
Remove the expanding of filesystem (this is now handled in the packaging process)

2021.05.03
H.0.2
Make the index.html file more robust, by writing to temp file, then do a move.

2021.04.27
H.0.1
Pull the landing page creation from the hmi service.


"""

_ = """
tabs and iframe:
https://jsfiddle.net/tovic/eHMBP/
https://www.w3schools.com/HTML/html_iframe.asp

"""

_ = """

Human Machine Interface service

Point to a default page, that should have been created by a new process, that manages the browser needs, and is the way that the browser got started. If the browser is closed, can we detect that, and re-launch?

Use javascript to show a count down timer, until the pre-selected page is loaded; the initial setup should have a blank field, and no jump. See if we can use this interface to let the site type in what they want for the jump page; if not, it will have to be managed some other way.

New service: (manages the interaction with the screen, keyboard, and mouse)

Manage the screen size settings. Support portrait / landscape choice (build my own page in Slicer, to autoload on a local pi, to be my system dashboard.)

Potentially take a screen grab of the current contents, to show in restricted Slicer access, instead of full remote desktop. Initially as an 'on demand', but could be turned into a once per X minute logging task, with content sent back to Slicer.

Manage the whitelist of sites, and the "bookmarks" links.

Use this to be the way that the id overlay is done, the browser is configured, the keyboard might want to be intercepted (inactivated), hide the mouse pointer, remove the menu bar from the screen, ...

"""

screen_catpure = """

sudo apt-get install -y scrot

sudo su worker
xhost +local:pi; export DISPLAY=:0; scrot /dev/shm/screen.png
xxd -p /dev/shm/screen.png

(copy content, paste to text file on mac ~/Downloads/screens/test1.xxd)

cat ~/Downloads/screens/test1.xxd | xxd -p -r >~/Downloads/screens/test1.png

cat ~/Downloads/screens/test4.xxd | xxd -p -r >~/Downloads/screens/test4.png



"""

ttd = """

- whitelist

privoxy


sudo vi /etc/privoxy/config

set enforce-blocks 1


(save and exit)

sudo vi /etc/privoxy/user.action

(at end)
{ +block } /

(save and exit)


sudo tail -n 100 -f  /var/log/privoxy/logfile

sudo systemctl restart privoxy

sudo systemctl status privoxy

cat /etc/privoxy/user.action

# make browser use this as proxy (see image-building.txt)




"""

keyboard_shortcuts = """
https://support.google.com/chrome/answer/157179?hl=en&co=GENIE.Platform%3DDesktop#zippy=%2Ctab-and-window-shortcuts

"""


other_content = """
sudo vi /cardinal/pi-hmi
sudo chmod +x /cardinal/pi-hmi

# ===== begin: start file
#!/usr/bin/env python3
import pi_hmi
pi_hmi.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-hmi.service
sudo systemctl daemon-reload
sudo systemctl stop pi-hmi.service
sudo systemctl start pi-hmi.service
sudo systemctl enable pi-hmi.service

sudo systemctl restart pi-hmi.service

systemctl status pi-hmi.service

# Logging of std out
cat /var/log/syslog | fgrep pi-hmi

tail -n 1000 /var/log/syslog | fgrep pi-hmi

OR

tail -f /var/log/syslog | fgrep pi-hmi

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-hmi
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

cups_printing = """
https://www.circuitbasics.com/how-to-print-in-raspberry-pi-using-cups/
https://www.cups.org/doc/admin.html

sudo apt-get update

sudo dpkg --configure -a

sudo apt -y full-upgrade

sudo apt-get clean

sudo apt-get -y autoremove

sudo apt-get update && sudo apt-get upgrade -y

sudo dpkg --configure -a

sudo apt-get install -f

sudo apt remove -y apt-listbugs

sudo apt-get install -y cups
(This takes a long time to install)

curl http://127.0.0.1:631/

/etc/cups/printers.conf

lpinfo
sudo lpinfo -v

sudo lpadmin -p networkPrinter -E -v ipp://*************/ipp/print -m everywhere

lpadmin -p networkPrinter -E -v socket://************* -m everywhere
OR
lpadmin -p networkPrinter -E -v socket://************* -m everywhere


lpstat -s
sudo lpinfo -v

From Slicer server login:
nmap -sT *************

21/tcp   open  ftp
23/tcp   open  telnet
80/tcp   open  http
139/tcp  open  netbios-ssn
514/tcp  open  shell
515/tcp  open  printer
631/tcp  open  ipp
7443/tcp open  oracleas-https
8080/tcp open  http-proxy
9100/tcp open  jetdirect




"""

import copy
import datetime
import json
import os
import shutil
import socket
import stat
import subprocess
import sys
import time
import traceback

# BarCode
from PIL import Image
from PIL import ImageDraw

color_red_warning = "(255, 0, 0, 0.3)"
color_yellow_caution = "(255, 255, 100, 0.3)"
color_green = "(0, 255, 0, 0.3)"
color_clear = "(0, 0, 0, 0.0)"
color_purple = "(255, 0, 255, 0.3)"

last_whitelist_content = "(initialize to something, so that we write it out the first time)"

G_thread_manage_data = {}
G_thread_manage_data['states'] = {}
G_thread_manage_data['states']['menu'] = {'filename':"/dev/shm/pi_hmi_special.html", 'last':0, 'enabled':False}
G_thread_manage_data['states']['reboot'] = {'filename':"/dev/shm/pi_hmi_reboot.html", 'last':0, 'enabled':False}
G_thread_manage_data['states']['shutdown'] = {'filename':"/dev/shm/pi_hmi_shutdown.html", 'last':0, 'enabled':False}
G_thread_manage_data['states']['index2'] = {'filename':"/dev/shm/pi_hmi_index2.html", 'last':0, 'enabled':False}
G_thread_manage_data['states']['wifi_corp'] = {'filename':"/dev/shm/pi_hmi_wifi_corp.html", 'last':0, 'enabled':False}
G_thread_manage_data['states']['wifi_iot'] = {'filename':"/dev/shm/pi_hmi_wifi_iot.html", 'last':0, 'enabled':False}

# ^^^ Add a new link here, then add it as an href on a page, and then be sure to handle it somewhere
# The enabled is a toggle, so once you handle the action, and want to clear it, then do that (like search for wifi_corp)

# ----------------------------
def do_one_command(command):
# ----------------------------
    command_splits = command.split(" ")
    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)

# Copied from http://en.wikipedia.org/wiki/Code_128
# Value Weights 128A    128B    128C
CODE128_CHART = """
0       212222  space   space   00
1       222122  !       !       01
2       222221  "       "       02
3       121223  #       #       03
4       121322  $       $       04
5       131222  %       %       05
6       122213  &       &       06
7       122312  '       '       07
8       132212  (       (       08
9       221213  )       )       09
10      221312  *       *       10
11      231212  +       +       11
12      112232  ,       ,       12
13      122132  -       -       13
14      122231  .       .       14
15      113222  /       /       15
16      123122  0       0       16
17      123221  1       1       17
18      223211  2       2       18
19      221132  3       3       19
20      221231  4       4       20
21      213212  5       5       21
22      223112  6       6       22
23      312131  7       7       23
24      311222  8       8       24
25      321122  9       9       25
26      321221  :       :       26
27      312212  ;       ;       27
28      322112  <       <       28
29      322211  =       =       29
30      212123  >       >       30
31      212321  ?       ?       31
32      232121  @       @       32
33      111323  A       A       33
34      131123  B       B       34
35      131321  C       C       35
36      112313  D       D       36
37      132113  E       E       37
38      132311  F       F       38
39      211313  G       G       39
40      231113  H       H       40
41      231311  I       I       41
42      112133  J       J       42
43      112331  K       K       43
44      132131  L       L       44
45      113123  M       M       45
46      113321  N       N       46
47      133121  O       O       47
48      313121  P       P       48
49      211331  Q       Q       49
50      231131  R       R       50
51      213113  S       S       51
52      213311  T       T       52
53      213131  U       U       53
54      311123  V       V       54
55      311321  W       W       55
56      331121  X       X       56
57      312113  Y       Y       57
58      312311  Z       Z       58
59      332111  [       [       59
60      314111  \       \       60
61      221411  ]       ]       61
62      431111  ^       ^       62
63      111224  _       _       63
64      111422  NUL     `       64
65      121124  SOH     a       65
66      121421  STX     b       66
67      141122  ETX     c       67
68      141221  EOT     d       68
69      112214  ENQ     e       69
70      112412  ACK     f       70
71      122114  BEL     g       71
72      122411  BS      h       72
73      142112  HT      i       73
74      142211  LF      j       74
75      241211  VT      k       75
76      221114  FF      l       76
77      413111  CR      m       77
78      241112  SO      n       78
79      134111  SI      o       79
80      111242  DLE     p       80
81      121142  DC1     q       81
82      121241  DC2     r       82
83      114212  DC3     s       83
84      124112  DC4     t       84
85      124211  NAK     u       85
86      411212  SYN     v       86
87      421112  ETB     w       87
88      421211  CAN     x       88
89      212141  EM      y       89
90      214121  SUB     z       90
91      412121  ESC     {       91
92      111143  FS      |       92
93      111341  GS      }       93
94      131141  RS      ~       94
95      114113  US      DEL     95
96      114311  FNC3    FNC3    96
97      411113  FNC2    FNC2    97
98      411311  ShiftB  ShiftA  98
99      113141  CodeC   CodeC   99
100     114131  CodeB   FNC4    CodeB
101     311141  FNC4    CodeA   CodeA
102     411131  FNC1    FNC1    FNC1
103     211412  StartA  StartA  StartA
104     211214  StartB  StartB  StartB
105     211232  StartC  StartC  StartC
106     2331112 Stop    Stop    Stop
""".split()

VALUES   = [int(value) for value in CODE128_CHART[0::5]]
WEIGHTS  = dict(zip(VALUES, CODE128_CHART[1::5]))
CODE128B = dict(zip(CODE128_CHART[3::5], VALUES))


# ----------------------------
def code128_format_link(text_to_encode, isBluetoothAddressLink):
# ----------------------------
    """
    Generate a link barcode from bluetooth address
    """
    if isBluetoothAddressLink:
        text     = "B" + str(text_to_encode.replace(':',''))
    else:
        text     = str(text_to_encode)

    pos      = 0
    length   = len(text)
    charset = CODE128B
    codes   = [charset['StartB']]

    if isBluetoothAddressLink:
        # FNC3
        codes.append(96)

    # Data
    while pos < length:
        # Encode Code B one character at a time
        codes.append(charset[text[pos]])
        pos += 1
    # Checksum
    checksum = 0
    for weight, code in enumerate(codes):
        checksum += max(weight, 1) * code
    codes.append(checksum % 103)
    # Stop Code
    codes.append(charset['Stop'])
    return codes

# ----------------------------
def code128_format_image(data, height=100, thickness=3, quiet_zone=True):
# ----------------------------
    barcode_widths = []
    for code in data:
        for weight in WEIGHTS[code]:
            barcode_widths.append(int(weight) * thickness)
    width = sum(barcode_widths)
    x = 0
    if quiet_zone:
        width += 20 * thickness
        x = 10 * thickness
    # Monochrome Image
    img  = Image.new('1', (width, height), 1)
    draw = ImageDraw.Draw(img)
    draw_bar = True
    for width in barcode_widths:
        if draw_bar:
            draw.rectangle(((x, 0), (x + width - 1, height)), fill=0)
        draw_bar = not draw_bar
        x += width
    return img

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def my_best_address():
# ----------------------------
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        my_best = s.getsockname()[0]
        s.close()
    except:
        my_best = ''
    return my_best

# ----------------------------
def macAddress(portName):
# ----------------------------
    return_value = ''
    if True:
        notes = """
        pi@raspberrypi:~ $ cat /sys/class/net/eth0/address
        dc:a6:32:96:56:c2
        pi@raspberrypi:~ $ cat /sys/class/net/wlan0/address
        dc:a6:32:96:56:c4
        """
        pass_string, fails = do_one_command('cat /sys/class/net/' + portName + '/address')
        return_value = pass_string
    else:
        pass_string, fails = do_one_command('ip -j addr')
        # pass_string = '[{"ifindex":1,"ifname":"lo","flags":["LOOPBACK","UP","LOWER_UP"],"mtu":65536,"qdisc":"noqueue","operstate":"UNKNOWN","group":"default","txqlen":1000,"link_type":"loopback","address":"00:00:00:00:00:00","broadcast":"00:00:00:00:00:00","addr_info":[{"family":"inet","local":"127.0.0.1","prefixlen":8,"scope":"host","label":"lo","valid_life_time":4294967295,"preferred_life_time":4294967295},{"family":"inet6","local":"::1","prefixlen":128,"scope":"host","valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":2,"ifname":"eth0","flags":["BROADCAST","MULTICAST","UP","LOWER_UP"],"mtu":1500,"qdisc":"mq","operstate":"UP","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c2","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691098,"preferred_life_time":691098},{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","secondary":true,"dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691103,"preferred_life_time":604703},{"family":"inet6","local":"fe80::d21:c163:5965:731e","prefixlen":64,"scope":"link","noprefixroute":true,"valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":3,"ifname":"wlan0","flags":["NO-CARRIER","BROADCAST","MULTICAST","UP"],"mtu":1500,"qdisc":"pfifo_fast","operstate":"DOWN","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c4","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[]}]'
        try:
            the_list = json.loads(pass_string)
            for index in range(0, len(the_list)):
                # the_list[1].keys()
                # [u'addr_info', u'operstate', u'qdisc', u'group', u'mtu', u'broadcast', u'flags', u'address', u'ifindex', u'txqlen', u'ifname', u'link_type']
                if the_list[index]['ifname'] == portName:
                    return_value = the_list[index]['address']
        except:
            pass

    return return_value

# ----------------------------
def serialr(serial):
# ----------------------------
    # make an alternate serial number formatting
    try:
        decimal = int(serial, 16)
        string = str(decimal)[::-1]
        build = ""
        count = -1
        for item in string:
            count += 1
            if count > 0:
                if count % 4 == 0:
                    build += '-'
            build += item
    except:
        build = 'error'
    return build

# ----------------------------
def get_my_zoom():
# ----------------------------
    input_file = "/cardinal/localhtml/browser_zoom"

    zoom_to_use = '100'
    try:
        with open(input_file, 'r') as f:
            zooms_full = json.loads(f.read())
        serial = get_serial()
        if serial in zooms_full:
            zoom_to_use = copy.deepcopy(zooms_full[serial])
    except:
        pass

    return zoom_to_use


# ----------------------------
def screen_size():
# ----------------------------
    screen_width = '1920'
    screen_height = '1080'
    try:
        pass_string, fails = do_one_command('fbset -s')
        # pass_string = '\nmode "1280x800"\n    geometry 1280 800 1280 800 16\n    timings 0 0 0 0 0 0 0\n    accel true\n    rgba 5/11,6/5,5/0,0/0\nendmode\n\n'

        splits = pass_string.split('\n')
        for item in splits:
            #print ('item', item)
            parts = item.strip().split()
            #print ('parts', parts)
            if parts:
                if parts[0] == 'geometry':
                    screen_width = int(parts[1])
                    screen_height = int(parts[2])
                    #print (screen_width, screen_height)
    except:
        pass

    return screen_width, screen_height

# ----------------------------
def file_system_expand():
# ----------------------------
    if os.path.isfile("/cardinal/needsexpand"):
        content = """
sudo rm /cardinal/needsexpand
sudo raspi-config --expand-rootfs
sudo rm /cardinal/pi_hmi_tempscript
sudo reboot
        """

        output_file = "/cardinal/pi_hmi_tempscript"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_hmi_tempscript')
        pass_string, fails = do_one_command('sudo /cardinal/pi_hmi_tempscript')

# ----------------------------
def make_qrcode(content, savefile):
# ----------------------------
    try:
        from MyQR import myqr as mq

        # https://pypi.org/project/MyQR/
        # https://www.topcoder.com/thrive/articles/make-a-qr-code-with-python
        """
    from MyQR import myqr as mq
    #mq.run('https://slicer.cardinalhealth.net', save_name = 'cah.png')
    mq.run('https://slicer.cardinalhealth.net', save_name = '/cardinal/cardinal.gif')


    sudo python3
    from MyQR import myqr as mq
    content = 'https://slicer.cardinalhealth.net'
    savefile = '/cardinal/qr.gif'
    mq.run(content, save_name = savefile, version=2)

        """
        mq.run(content, save_name = savefile)
    except:
        pass
# ----------------------------
def make_cordis_logo():
# ----------------------------
    # downloaded : https://www.mycardinalhealth.net/media/1748/brand-guidelines-2020.pdf
    # then did a screen capture of just the area of the logo, pasted into Word,
    # right click on image: "save as picture" to gif, then did "xxd" of that file.

    # cd /Users/<USER>/OneDrive\ -\ Cardinal\ Health/MarioToDave
    # made with "xxd -p Cordis.png"
    content = """89504e470d0a1a0a0000000d4948445200000258000000c80802000000bb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"""

    output_file = "/cardinal/cordis.xxd"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    # make back into image file
    content = """
#!/usr/bin/env sh
cat /cardinal/cordis.xxd | xxd -p -r >/cardinal/cordis.gif
sudo rm /cardinal/cordis.xxd
    """
    output_file = "/cardinal/pi_hmi_tempscript"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    report = ''

    pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_hmi_tempscript')
    report += "pass1\n" + pass_string + "fail1\n" + fails

    pass_string, fails = do_one_command('sudo /cardinal/pi_hmi_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    # clean up
    pass_string, fails = do_one_command('sudo rm /cardinal/pi_hmi_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    with open ("/dev/shm/hmi_c_report.txt", "w") as f:
        f.write(report)


# ----------------------------
def make_cardinal_logo():
# ----------------------------
    # downloaded : https://www.mycardinalhealth.net/media/1748/brand-guidelines-2020.pdf
    # then did a screen capture of just the area of the logo, pasted into Word,
    # right click on image: "save as picture" to gif, then did "xxd" of that file.

    # made with "xxd -p cardinal.gif"
    content = """47494638376118017a00e600000000001a1919201e1e2423232827272c2b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"""

    output_file = "/cardinal/cardinal.xxd"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    # make back into image file
    content = """
#!/usr/bin/env sh
cat /cardinal/cardinal.xxd | xxd -p -r >/cardinal/cardinal.gif
sudo rm /cardinal/cardinal.xxd
    """
    output_file = "/cardinal/pi_hmi_tempscript"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    report = ''

    pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_hmi_tempscript')
    report += "pass1\n" + pass_string + "fail1\n" + fails

    pass_string, fails = do_one_command('sudo /cardinal/pi_hmi_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    # clean up
    pass_string, fails = do_one_command('sudo rm /cardinal/pi_hmi_tempscript')
    report += "pass2\n" + pass_string + "fail2\n" + fails

    with open ("/dev/shm/hmi_report.txt", "w") as f:
        f.write(report)

# ----------------------------
def get_communication_exceptions():
# ----------------------------
    result = 0

    try:
        result = int(open('/dev/shm/pi_runner_slicer_exceptions.txt', 'r').read())
    except:
        pass

    return result

# ----------------------------
def get_image_version():
# ----------------------------
    image_version = "(missing)"
    try:
        with open('/cardinal/image_ver.txt', 'r') as f:
            content = f.read()
            # "Image version: 2.0.4"
        image_version = content.split(":")[1].strip()
    except:
        #do_datadrop_debug(traceback.format_exc())
        pass

    return image_version

# ----------------------------
def configure_ntp():
# ----------------------------
    content = """
#  This file is part of systemd.
#
#  systemd is free software; you can redistribute it and/or modify it
#  under the terms of the GNU Lesser General Public License as published by
#  the Free Software Foundation; either version 2.1 of the License, or
#  (at your option) any later version.
#
# Entries in this file show the compile time defaults.
# You can change settings by editing this file.
# Defaults can be restored by simply deleting this file.
#
# See timesyncd.conf(5) for details.

[Time]
#NTP=
FallbackNTP=gpstime.cardinalhealth.net gpstime02.cardinalhealth.net 0.debian.pool.ntp.org 1.debian.pool.ntp.org 2.debian.pool.ntp.org 3.debian.pool.ntp.org
#RootDistanceMaxSec=5
#PollIntervalMinSec=32
#PollIntervalMaxSec=2048
    """

    output_file = "/etc/systemd/timesyncd.conf"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    pass_string, fails = do_one_command('sudo timedatectl set-ntp false')
    pass_string, fails = do_one_command('sudo timedatectl set-ntp true')


# ----------------------------
def set_whitelist (whitelist):
# ----------------------------
    global last_whitelist_content

    output_file = "/etc/privoxy/user.action"


    content = """
{{alias}}
+crunch-all-cookies = +crunch-incoming-cookies +crunch-outgoing-cookies
-crunch-all-cookies = -crunch-incoming-cookies -crunch-outgoing-cookies
 allow-all-cookies  = -crunch-all-cookies -session-cookies-only -filter{content-cookies}
 allow-popups       = -filter{all-popups} -filter{unsolicited-popups}
+block-as-image     = +block{Blocked image request.} +handle-as-image
-block-as-image     = -block

fragile     = -block -crunch-all-cookies -filter -fast-redirects -hide-referer -prevent-compression
shop        = -crunch-all-cookies allow-popups

myfilters   = +filter{html-annoyances} +filter{js-annoyances} +filter{all-popups}\
              +filter{webbugs} +filter{banners-by-size}

allow-ads   = -block -filter{banners-by-size} -filter{banners-by-link}
{ allow-all-cookies }
{ -filter{all-popups} }
{ -filter }
{ +block{Nasty ads.} }
{ +block-as-image }
{ fragile }
{ allow-ads }
{ +set-image-blocker{blank} }
"""

    whitelist_content = ""
    if not '*' in whitelist:
        content += """{ +block }
/
"""
        content += """{ -block -filter allow-all-cookies}""" + "\n"

        for item in whitelist:
            whitelist_content += item + "\n"

    if whitelist_content != last_whitelist_content:
        last_whitelist_content = whitelist_content

        content += whitelist_content

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo systemctl restart privoxy')

# ----------------------------
def get_my_name():
# ----------------------------
    input_file = "/cardinal/localhtml/name_to_use"

    name_to_use = ''
    try:
        with open(input_file, 'r') as f:
            names_full = json.loads(f.read())
        serial = get_serial()
        if serial in names_full:
            name_to_use = copy.deepcopy(names_full[serial])
    except:
        pass

    return name_to_use

# ----------------------------
def get_my_info(item):
# ----------------------------
    info = ''

    try:
        with open('/cardinal/localhtml/info_' + item, 'r') as f:
            info = f.read()
    except:
        pass

    return info

# ----------------------------
def get_my_bookmarks():
# ----------------------------
    input_file = "/cardinal/localhtml/bookmarks"
    test_bookmarks = """

sudo vi /cardinal/localhtml/bookmarks
{"10000000e3669edf":{"1": {"title": "EDHR", "url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login",
"whitelist":["edhr-na-jz.cardinalhealth.net"]
}}}


            """

    bookmarks = {}
    try:
        with open(input_file, 'r') as f:
            bookmarks_full = json.loads(f.read())
        serial = get_serial()
        if serial in bookmarks_full:
            bookmarks = copy.deepcopy(bookmarks_full[serial])

    except:
        pass

    return bookmarks

# ----------------------------
def get_my_clockview_enable(serial):
# ----------------------------
    input_file = "/cardinal/localhtml/clockview_enabled"

    return_value = ''
    try:
        with open(input_file, 'r') as f:
            json_full = json.loads(f.read())
        if serial in json_full:
            return_value = copy.deepcopy(json_full[serial])
    except:
        pass

    return return_value

# ----------------------------
def set_my_wifi_setting(the_setting):
# ----------------------------
    input_file = "/cardinal/localhtml/dev_wifi_connect"
    serial = get_serial()

    try:
        with open(input_file, 'w') as f:
            f.write(json.dumps({serial:the_setting}))
    except:
        pass

# ----------------------------
def get_my_wifi_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_connect"
    serial = get_serial()

    setting = 'corp'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        pass

    input_file = "/cardinal/localhtml/dev_wifi_connect"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        pass

    return setting

# ----------------------------
def get_bluetooth_enable():
# ----------------------------
    return_value = 'No'

    try:
        with open('/dev/shm/pi_bluetooth_enabled.txt', 'r') as f:
            return_value = f.read()
    except:
        pass

    return return_value

# ----------------------------
def get_cpu_temperature():
# ----------------------------
    pass_string, fail_string = do_one_command('vcgencmd measure_temp')
    return pass_string.split('=')[1].split("'")[0]

# ----------------------------
def get_address_and_network_and_lan_status():
# ----------------------------
    address = my_best_address()

    lan_status = {"ssid": "(not_on_wifi)", "chan": "", "signal": ""} # The default anyway
    try:
        with open('/dev/shm/pi_network_scan.txt', 'r') as f:
            lan_status = json.loads(f.read())
    except:
        pass

    # fix the old network result:
    if not 'lan' in lan_status:
        lan_status['lan'] = 'wlan0'

    if lan_status['ssid'] != '(not_on_wifi)':
        # fix the old network result:
        if not 'lan' in lan_status:
            lan_status['lan'] = 'wlan0'

        network = lan_status['lan'] + ' -> ' + lan_status['ssid'] + '-' + lan_status['chan'] + '-' + lan_status['signal']
    else:
        if not 'lan' in lan_status:
            lan_status['lan'] = 'eth0'

        network = 'Wire ' + lan_status['lan']

    if not address:
        network = 'no lan'

    return address, network, lan_status


# ----------------------------
def build_local_html_content2():
# ----------------------------
    image_version = get_image_version()

    content = ''

    address, network, lan_status = get_address_and_network_and_lan_status()

    cah_iot = 'No' # the default
    if 'cah-iot' in lan_status:
        cah_iot = lan_status['cah-iot']

    ring_updates = get_my_info('ring_updates')
    ring_updates_color = ""
    if ring_updates:
        if ring_updates != '0':
            ring_updates_color = "(100, 100, 255, 0.3)"
            ring_updates += ' *'

    title_vs_content = []
    title_vs_content.append(['<B><a href="file:///dev/shm/pi_hmi_index2.html" style="text-decoration:none;color:inherit">ID</a></B>',get_serial(),'<img src="file:///dev/shm/serial.png" alt="(ID)" width="400" height="50">'])
    if image_version >= "2.3.2":
        title_vs_content.append(['Printer', '<B><a href="http://127.0.0.1:631/" style="text-decoration:none;color:inherit">configure</a></B>','(Use Alt F4 to return)'])

    # The following did not work. Likely the file needs to be pulled up to their server, and processed, which we have no way to serve it up to there.
#    title_vs_content.append(['Downloads', '<B><a href="file:///cardinal/localhtml/downloads.html" style="text-decoration:none;color:inherit">downloads</a></B>','(Use Alt F4 to return)'])

    my_setting = get_my_wifi_setting()
    if my_setting == 'corp':
        wifi_setting = 'If not wired, use: ' + my_setting + ' (change to <B><a href="file:///dev/shm/pi_hmi_wifi_iot.html" style="text-decoration:none;color:inherit">iot</a></B>)'
    else:
        wifi_setting = 'If not wired, use: ' + my_setting + ' (change to <B><a href="file:///dev/shm/pi_hmi_wifi_corp.html" style="text-decoration:none;color:inherit">corp</a></B>)'
    title_vs_content.append(['WiFi (wlan0mac)',macAddress('wlan0'),wifi_setting])
    title_vs_content.append(['WiFi (wlan1mac)',macAddress('wlan1'),wifi_setting])
    title_vs_content.append(['Wired (eth0mac)',macAddress('eth0'),''])
    title_vs_content.append(['Wired (eth1mac)',macAddress('eth1'),''])
    title_vs_content.append(['cah-iot seen',cah_iot,''])
    title_vs_content.append(['Network active',network,''])


    prompt_content = ''
    slicer_exceptions = get_communication_exceptions()
    if slicer_exceptions > 3:
        prompt_content = '!!! Network appears to be offline'
    elif slicer_exceptions > 1:
        prompt_content = '!!! Network appears to be failing'

    title_vs_content.append(['Address',address, prompt_content])
    title_vs_content.append(['CPU Temp',get_cpu_temperature(), 'C'])
    title_vs_content.append(['Ring Level',get_my_info('ring_level'), ''])
    title_vs_content.append(['Ring Updates',ring_updates, '', ring_updates_color])

    for filename in sorted(os.listdir('/dev/shm')):
        if 'debug' in filename:
            fullPath = '/dev/shm/' + filename
            try:
                with open(fullPath, 'r') as f:
                    debug_content = f.read()
                title_vs_content.append([filename, '', str(debug_content).replace('\n','<br>')])
            except:
                pass

    # format it
    content += '<head>'

    refresh_time = 30

    content += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

    content += '</head>'

    content += '<body>'
    content += '<center><table border="1" cellpadding="10">'

    for item in title_vs_content:
        color = ""
        if len(item) > 3:
            color = item[3]

        content += '<tr>'
        content += '<td>'
        content += item[0]
        content += '</td>'
        if color:
            content += '<td style="background-color:rgba' + color + '">'
        else:
            content += '<td>'
        content += item[1]
        content += '</td>'
        content += '<td>'
        content += item[2]
        content += '</td>'
        content += '<tr>'

    content += '<td id="timertoshow" style="text-align: center;">' + '<center>'
    content += '0 seconds'
    content += '</center></td>'
    content += '<td>'
    content += 'click ID to return Home'
    content += '</td>'
    content += '<td>'
    content += '</td>'
    content += '</tr>'

    content += '</tr>'
    content += '</table>'
    content += '</center>'

    content += '</body>'

    content += """

<script>
var seconds = 0;
var el = document.getElementById('timertoshow');


function incrementSeconds() {
    seconds += 1;
    el.innerText = "" + seconds + " seconds";
}
var cancel = setInterval(incrementSeconds, 1000);

</script>


            """

    return content

# ----------------------------
def build_local_html_content_downloads():
# ----------------------------
    # build content
    title_vs_content = []
    title_vs_content.append(['File','Date (YYYY.MM.DD)',''])

    downloads_files = []
    try:
        base_dir = '/home/<USER>/Downloads/'
        list_dir = os.listdir(base_dir)
        for item in list_dir:
            file_name = base_dir + item
            if '.ppt' in item.lower():
                fileStatsObj = os.stat (file_name)
                last_modified_time = fileStatsObj [ stat.ST_MTIME ]
                last_modified_string = datetime.datetime.fromtimestamp(last_modified_time, tz=datetime.timezone.utc).strftime('%Y.%m.%d')


                #raw ppt in browser: http://www.learningaboutelectronics.com/Articles/How-to-embed-a-powerpoint-file-on-a-web-page-HTML.php

                downloads_files.append({'filename':item, 'modified':last_modified_string, 'render':"<iframe src='https://view.officeapps.live.com/op/embed.aspx?src=file://" + file_name + "' width='100%' height='565px' frameborder='0'> </iframe>"})
    except:
        downloads_files = [{'filename':'exception', 'modified':traceback.format_exc()}]

    for downloads_file in downloads_files:
        title_vs_content.append([downloads_file['filename'],downloads_file['modified'],downloads_file['render']])

    # Render content
    content = ''

    content += '<head>'

    refresh_time = 30

    content += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

    content += '</head>'

    content += '<body>'
    content += '<center><table border="1" cellpadding="10">'

    for item in title_vs_content:
        color = ""
        if len(item) > 3:
            color = item[3]

        content += '<tr>'
        content += '<td>'
        content += item[0]
        content += '</td>'
        if color:
            content += '<td style="background-color:rgba' + color + '">'
        else:
            content += '<td>'
        content += item[1]
        content += '</td>'
        content += '<td>'
        content += item[2]
        content += '</td>'
        content += '<tr>'

    content += '<td id="timertoshow" style="text-align: center;">' + '<center>'
    content += '0 seconds'
    content += '</center></td>'
    content += '<td>'
    content += 'alt F4 to return to top'
    content += '</td>'
    content += '<td>'
    content += '</td>'
    content += '</tr>'

    content += '</tr>'
    content += '</table>'
    content += '</center>'

    content += '</body>'

    content += """
<script>
var seconds = 0;
var el = document.getElementById('timertoshow');


function incrementSeconds() {
    seconds += 1;
    el.innerText = "" + seconds + " seconds";
}
var cancel = setInterval(incrementSeconds, 1000);

</script>
            """

    return content

# ----------------------------
def build_local_html_content1(bookmarks):
# ----------------------------
    global G_thread_manage_data
    image_version = get_image_version()

    if G_thread_manage_data['states']['index2']['enabled'] or G_thread_manage_data['states']['wifi_corp']['enabled'] or G_thread_manage_data['states']['wifi_iot']['enabled']:
        if G_thread_manage_data['states']['wifi_corp']['enabled']:
            set_my_wifi_setting('corp')
            G_thread_manage_data['states']['wifi_corp']['enabled'] = False

        if G_thread_manage_data['states']['wifi_iot']['enabled']:
            set_my_wifi_setting('iot')
            G_thread_manage_data['states']['wifi_iot']['enabled'] = False

        return build_local_html_content2()

    include_reload = True

    name_to_use = get_my_name()
    bluetooth_enabled = (get_bluetooth_enable() == 'Yes')
    bluetooth_status = ''

    menu_setting = get_my_special_menu()


    try:
        with open('/dev/shm/pi_bluetooth_status.txt', 'r') as f:
            bluetooth_status = f.read()
    except:
        pass

    # build the required whitelist
    whitelist = []
#    whitelist.append('view.officeapps.live.com') # for viewing ppt files
#    whitelist.append('r.office.microsoft.com') # for viewing ppt files
#    whitelist.append('*') # for viewing ppt files

    count_of_bookmarks = 0
    logo = ''
    for key in bookmarks:
        count_of_bookmarks += 1
        if 'whitelist' in bookmarks[key]:
            for item in bookmarks[key]['whitelist']:
                if not item in whitelist:
                    whitelist.append(item)

        if 'logo' in bookmarks[key]:
            logo = bookmarks[key]['logo']

    #print ('whitelist', whitelist)
    set_whitelist(whitelist)
    # the first time the browser hits a new white list, it will fail.
    # Do a refresh, and you should be good to go.
    # I can not find a way to avoid this. A reboot does do it, but that seems extreme. (It only affects 'PR005 F 8 tab', that I know of)

    td_style_centered = '<td style="font-size:40px"><center>'
    td_style_centered_rs2 = '<td rowspan="2" style="font-size:40px"><center>'
    td_style_centered_rs3 = '<td rowspan="3" style="font-size:40px"><center>'
    td_format = '<td style="font-size:30px">'
    if count_of_bookmarks > 2:
        td_style_centered = '<td style="font-size:30px"><center>'
        td_style_centered_rs2 = '<td rowspan="2" style="font-size:30px"><center>'
        td_format = '<td style="font-size:25px">'
    if count_of_bookmarks > 4:
        td_style_centered = '<td style="font-size:25px"><center>'
        td_style_centered_rs2 = '<td rowspan="2" style="font-size:25px"><center>'
        td_format = '<td style="font-size:20px">'

    content = ''

    if True:
        try:
            serial = get_serial()

            clockview_setting = get_my_clockview_enable(serial)

            content += '<head>'

            if bluetooth_enabled:
                refresh_time = 5
            else:
                refresh_time = 30

            if include_reload:
                content += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

            content += '</head>'

            content += '<body>'
            content += '<center><table border="1" cellpadding="10">'

            content += '<tr>'

            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                if G_thread_manage_data['states']['shutdown']['enabled']:
                    content += '<a href="file:///dev/shm/pi_hmi_shutdown.html">Shutdown</a>'
                else:
                    content += '<a href="file:///dev/shm/pi_hmi_shutdown.html">shutdown</a>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'

            ring_level = get_my_info('ring_level')
            if ring_level:
                content += td_format + '<center>' + image_version + '<br>' + 'Ring ' + ring_level
            else:
                content += td_format + '<center>' + image_version
            if menu_setting == 'Yes':
                if G_thread_manage_data['states']['menu']['enabled']:
                    content += '<a href="file:///dev/shm/pi_hmi_special.html"><br>Hide<br>Menu</a>'
                else:
                    content += '<a href="file:///dev/shm/pi_hmi_special.html"><br>Menu</a>'

            content += '</center></td>'
            content += td_style_centered
            # png is 3.89 x 1.69
            # pick height = 100, calc width = 100 * (3.89/1.69)
            logo_to_use = 'cardinal.gif'
            if logo == 'cordis':
                logo_to_use = 'cordis.gif'
            content += '<img src="file:///cardinal/' + logo_to_use + '" alt="' + logo_to_use + '" width="230" height="100">'
            content += '</center></td>'

            content += td_style_centered

            try:
                TS = datetime.datetime.now().strftime('%Y%m%d%H%M')
                # 202104062018
                YYYY = TS[0:4]
                MM = str(int(TS[4:6]))
                DD = str(int(TS[6:8]))
                hh = TS[8:10]
                mm = TS[10:12]

                if int(hh) >= 12:
                    ap = "PM"
                    if int(hh) > 12:
                        hh = str(int(hh)-12)
                else:
                    ap = "AM"
                    if int(hh) == 0:
                        hh = '12'
                    else:
                        hh = str(int(hh))

                if clockview_setting == 'MMDDYYYYhhmmap':
                    content += MM + "/" + DD + "/" + YYYY
                    content += "<br>"
                    content += hh + ":" + mm + " " + ap

                if clockview_setting == 'hhmmapMMDDYYYY':
                    content += hh + ":" + mm + " " + ap
                    content += "<br>"
                    content += MM + "/" + DD + "/" + YYYY
            except:
                pass

            content += '</center></td>'

            content += '</tr>'

            content += '<tr>'
            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                if G_thread_manage_data['states']['reboot']['enabled']:
                    content += '<a href="file:///dev/shm/pi_hmi_reboot.html">Reboot</a>'
                else:
                    content += '<a href="file:///dev/shm/pi_hmi_reboot.html">reboot</a>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'


            prompt_to_show = 'ID'
            prompt_color = '(0,0,0,0.0)'
            updates = get_my_info('ring_updates')
            if updates:
                if updates != '0':
                    prompt_to_show += ' *'
                    prompt_color = "(100, 100, 255, 0.3)"
            content += td_format.replace('style="', 'style="background-color:rgba'+ prompt_color + ';') + '<center>'
            content += '<B><a href="file:///dev/shm/pi_hmi_index2.html" style="text-decoration:none;color:inherit">' + prompt_to_show + '</a></B>'
            content += '</center></td>'
            content += td_style_centered
            content += serial
            content += '</center></td>'

            if name_to_use:
                content += td_style_centered_rs2
                content += name_to_use
                content += '</center></td>'

            content += '</tr>'

            content += '<tr>'
            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'
            content += td_format + '<center>'
            content += 'IDr'
            content += '</center></td>'
            content += td_style_centered
            content += serialr(serial)
            content += '</center></td>'
            content += '</tr>'

            content += '<tr>'
            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'

            prompt_color = color_clear
            slicer_exceptions = get_communication_exceptions()
            if slicer_exceptions > 3:
                prompt_color = color_red_warning
            elif slicer_exceptions > 1:
                prompt_color = color_yellow_caution

            content += td_format.replace('style="', 'style="background-color:rgba'+ prompt_color + ';') + '<center>'
            content += 'address'
            content += '</center></td>'
            content += td_style_centered

            address, network, lan_status = get_address_and_network_and_lan_status()

            content += address + ' <- ' + network
            content += '</center></td>'

            if bluetooth_enabled:
                content += td_style_centered_rs3
                content += bluetooth_status
                content += '</center></td>'

            content += '</tr>'

            w, h = screen_size()
            zoom = get_my_zoom()

            content += '<tr>'
            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'
            content += td_format + '<center>'

            content += 'screen'

            content += '</center></td>'
            content += td_style_centered
            content += str(w) + " x " + str(h) + " z " + zoom
            content += '</center></td>'

            content += '</tr>'

            content += '<tr>'
            if G_thread_manage_data['states']['menu']['enabled']:
                content += td_format + '<center>'
                content += '</center></td>'
                content += td_format + '<center>'
                content += '</center></td>'
            content += td_format + '<center>'
            content += 'reset'
            content += '</center></td>'
            content += td_style_centered
            content += 'Alt F4'
            content += '</center></td>'
            content += '</tr>'

            # -----------------------------
            # The Bookmarks area
            # -----------------------------
            format_to_use = 1
            keys_settings = ''
            kiosk_mode = True
            for key in sorted(bookmarks):
                if 'format' in bookmarks[key]:
                    if bookmarks[key]['format'] == 'tabs':
                        format_to_use = 2

                if 'allowkeys' in bookmarks[key]:
                    if bookmarks[key]['allowkeys'] == 'all':
                        keys_settings = 'all'

                if 'disablekiosk' in bookmarks[key]:
                    if bookmarks[key]['disablekiosk'] == 'yes':
                        kiosk_mode = False

            content += '</table></center>'

            if format_to_use == 1:
                content += '<br>'
                content += '<center><table border="1" cellpadding="10">'
                if count_of_bookmarks < 1:
                    content += '<tr>'
                    content += td_format + '<center>'
                    content += 'No bookmarks'
                    content += '</center></td>'
                    content += '</tr>'

                # look for autolaunch, and set it up:
                # https://stackoverflow.com/questions/2774253/javascript-to-auto-click-on-hyperlink/2774310

                any_auto = False
                for key in sorted(bookmarks):
                    if 'autolaunch' in bookmarks[key]:
                        if bookmarks[key]['autolaunch']:
                            any_auto = True

                for key in sorted(bookmarks):
                    link_id = 'link_' + key

                    autolaunch_text = ''
                    if 'autolaunch' in bookmarks[key]:
                        if bookmarks[key]['autolaunch']:
                            content += """<script type="text/javascript">"""
                            content += """setTimeout(function() {"""
                            content += """    var linkUrl = document.getElementById('""" + link_id + """').href;"""
                            content += """    location.href = linkUrl;"""
                            content += """}, 25 * 1000);"""
                            content += """</script>"""
                            autolaunch_text = 'auto'

                    content += '<tr>'
                    content += td_style_centered
                    content += '<a id="' + link_id + '" href="' + bookmarks[key]['url'] + '" >' + bookmarks[key]['title'] + '</a>'
                    content += '</td>'

                    if any_auto:
                        content += td_style_centered
                        content += autolaunch_text
                        content += '</td>'
                    content += '</tr>'

                content += '<tr>'
                content += '<td id="timertoshow" style="text-align: center;">' + '<center>'
                content += '0 seconds'
                content += '</center></td>'
                content += '</tr>'

                content += '</table></center>'

            if format_to_use == 2:
                content += '<br>'
                content += '<center><table border="1" cellpadding="10">'

                alt_strs = []
                titles = []
                for key in sorted(bookmarks):
                    titles.append(bookmarks[key]['title'])
                while len(titles) < 10:
                    titles.append('')
                for x in range (0,10):
                    if titles[x]:
                        alt_strs.append('Alt ' + str(x+1)[0])
                    else:
                        alt_strs.append('')

                if kiosk_mode:
                    keys_content = [
                                    ['ctrl tab -> next tab',''],
                                    ['crtl r -> refresh page',''],
                                    ['',''],
                                    ['',''],
                                    ['esc -> exit a mode',''],
                                    ]
                else:
                    keys_content = [
                                    ['ctrl tab -> next tab',''],
                                    ['ctrl r -> refresh page',''],
                                    ['',''],
                                    ['F11 -> exit full screen',''],
                                    ['esc -> exit a mode',''],
                                    ]

                for index in range (0, 5):
                    content += '<tr>'

                    if keys_settings == 'all':
                        content += td_format + '<center>'
                        content += keys_content[index][0]
                        content += '</center></td>'
                        content += td_format + '<center>'
                        content += keys_content[index][1]
                        content += '</center></td>'

                        content += td_format + '<center>'
                        content += '</center></td>'


                    content += td_format + '<center>'
                    content += alt_strs[index]
                    content += '</center></td>'
                    content += td_format + '<center>'
                    content += '<B>' + titles[index] + '</B>'
                    content += '</center></td>'

                    content += td_format + '<center>'
                    content += '</center></td>'

                    content += td_format + '<center>'
                    content += alt_strs[index + 5]
                    content += '</center></td>'
                    content += td_format + '<center>'
                    content += '<B>' + titles[index+5] + '</B>'
                    content += '</center></td>'

                    content += '</tr>'

                content += '<tr>'
                content += '<td id="timertoshow" style="text-align: center;">' + '<center>'
                content += '0 seconds'
                content += '</center></td>'
                content += '</tr>'

                content += '</table></center>'
            # Close out the page
            content += '</body>'

            content += """

<script>
var seconds = 0;
var el = document.getElementById('timertoshow');


function incrementSeconds() {
    seconds += 1;
    el.innerText = "" + seconds + " seconds";
}
var cancel = setInterval(incrementSeconds, 1000);

</script>


            """

        except:
            print ("exception")

    return content

# ----------------------------
def load_certs():
# ----------------------------
    try:
        _ = """
    ############################
    # load CAH root certificate for browser
    ############################
    # make xxd
    on Daves Mac:
    cd /Users/<USER>/Downloads/cs_sp_pi_imageCreator/bootstrap_image/certs
    xxd -p CAH-Root-CA-PR1.cer
        """

        content = """308205463082032ea00302010202101a4c4b2db06fb38e4122cccaacb5f6
ff300d06092a864886f70d01010b0500303431183016060355040a130f43
617264696e616c204865616c7468311830160603550403130f4341482d52
6f6f742d43412d505231301e170d3139303731323139333035345a170d34
34303731323139333035345a303431183016060355040a130f4361726469
6e616c204865616c7468311830160603550403130f4341482d526f6f742d
43412d50523130820222300d06092a864886f70d01010105000382020f00
3082020a0282020100c1c306b503e6ff5b49018175a8405b692cae7c1ec2
1ed7d234ee16536e531fbf62442d58bc923e1042a314dbb7aa6fee2c3c50
a302349d2228c4b34f4e5c4d02d3efbe65a318f48a1b8e6fadd2d80afee6
0a0ab217c688e44b0a3e8e3f9596a7d83202a6338083ca1cd566f0136bfe
e0affdcc567e0049a74ec24f9715a631f6efc02d671064685448a8c6cde8
1e2007deece5d89ac88163114cff788d7109efd014a8d8b150cbdd0703da
6f39171bf66c7948be1732c452115fab0c0410a329f187acd6081481b7a2
b31aa35a906c35e595806810e4dabc24227566ed5d5e0976efd419c60140
7041008f185b76485f09ca182fe591b3a4a7c9ac7ac187c38779f4335550
5c8df542df1eb1120193d14c349fdd89bcb7182f8ae9d5df822e1bd2b8a0
a0dfe765c7dc2230616ab241eb1c595febe89986555a93de5687d2196bfc
54dbfe03e4b49510cb6685c9b5377d197e50e8601274b434e935c72ca16e
49083ac524fbcd381087e68bd8b53a18a2634b49df16303a580869633433
6d488820b180de8612934c6541c109ebc30fda32184b8b18954078fdfd15
ce9256bd5a1a7b347180ab592c4a26c7d84558dc6efaae9f690fcc191d09
a5311940d667df94eae6bb76e4f499746235e96bd0609e1a931b70c6d085
198c4fa54af93ccade8742ec3e9fdaaaf1c1eea6e0bd4fcfc27f15cd2263
a7e5597dbc8aca2e92222b0203010001a3543052300e0603551d0f0101ff
040403020106300f0603551d130101ff040530030101ff301d0603551d0e
041604148deb04e5396d80b7464dc1215c5185436c5bc153301006092b06
010401823715010403020100300d06092a864886f70d01010b0500038202
0100704328446949836f0dded58aac8fa34e41b56fb683f01addf9a46e2c
7d12ea98ca2f45feda09a25a9ce08475773535582c860801e9d524eebd4d
95fa1b23f5bafd2439e9afb13b7fbd92cc737e348698a8e1875d3661ef88
aac1872c144ecb9fefc974bc04dec67e9c38ed3e8d4bc89001dfeaac660e
3adc3b9768357456b3c7005f21b5293ddaa0b0266b0efdb2849437f0b026
6943de09c44e50c5ddbea2297d7f4380b78cff4da784ae8c64c47fbd9f50
2a07f35216e93a2ccbed04e01154bc954c186198ebf7a436e5bc0d8ed0f8
da23bbafc5f23b01b3bdf4948cfbf5dd6e5e009f96aba68a4840b00b5eab
a847569c3e073eb449c1bbc7b14026ebfe1fef291765cac569e0b3897d23
9917bcc72690a998ad06c0edf60e4e6a1d2589bc582a6606ad0605803c8e
b95e640eb26c1357085ab72481e64cab3520c133d67b6b4ffa78fa2e83b7
bff1a31c1aa8437b897e8fac26e76a4a3de69ff3a5ea5ffa46fcffa9230e
b4245e46e528056c2aae8d6568101223a0357cd62e8b13f507f27de6ed8e
d0e095f9c38b781ec4555af9f158fccf1da137d85278ab44b2ac66d29c26
6a5bd2f216d680347df49d4e59baea2fb4357f8eb786c5384ddaf0a95e2f
b1635fd077ee54f911a3ad0c8423ed21313e7b7b716fe4da4a1cf32503f9
18fc6f3e7ed18bdf502b124c076e2a078c42e35fb48f53822925782f3243
b018d4fb"""


        need_one_time_installs = False
        try:
            pass_string, fails = do_one_command('certutil -d sql:/home/<USER>/.pki/nssdb -L')
        except:
            need_one_time_installs = True

        # skip for now
        need_one_time_installs = False

        if need_one_time_installs:
            content = """
#!/usr/bin/env sh
sudo apt-get update
sudo apt-get install -y privoxy
sudo apt-get install -y python3-pip
sudo pip3 install MyQR
sudo apt-get install libopenjp2-7
sudo apt-get install -y --no-install-recommends xserver-xorg-video-all xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils chromium-browser unclutter
sudo apt-get install -y libnss3-tools
            """
            output_file = "/cardinal/pi_hmi_tempscript"
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            with open(output_file, 'w') as f:
                f.write(content)

            pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_hmi_tempscript')
            print (pass_string, fails)
            pass_string, fails = do_one_command('sudo /cardinal/pi_hmi_tempscript')
            print (pass_string, fails)

        # look to see if the certificate needs installed
        need_to_do_it = False
        pass_string, fails = do_one_command('certutil -d sql:/home/<USER>/.pki/nssdb -L')
        print (pass_string, fails)

        #pass_string = '\nCertificate Nickname                                         Trust Attributes\n                                                             SSL,S/MIME,JAR/XPI\n\nCAH-Root-CA-PR1                                              C,,  \n'
        if not 'CAH-Root-CA-PR1' in pass_string:
            need_to_do_it = True

        if need_to_do_it:
            # make script in local file
            output_file = "/usr/local/share/ca-certificates/CAH-Root-CA-PR1.xxd"
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            with open(output_file, 'w') as f:
                f.write(content)

            # make back into cer file
            content = """
#!/usr/bin/env sh
cat /usr/local/share/ca-certificates/CAH-Root-CA-PR1.xxd | xxd -p -r >/usr/local/share/ca-certificates/CAH-Root-CA-PR1.cer
sudo openssl x509 -inform DER -in /usr/local/share/ca-certificates/CAH-Root-CA-PR1.cer -out /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
sudo update-ca-certificates
sudo certutil -d sql:/home/<USER>/.pki/nssdb -L
sudo certutil -d sql:/home/<USER>/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
            """
            output_file = "/cardinal/pi_hmi_tempscript"
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            with open(output_file, 'w') as f:
                f.write(content)

            pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_hmi_tempscript')
            print (pass_string, fails)
            pass_string, fails = do_one_command('sudo /cardinal/pi_hmi_tempscript')
            print (pass_string, fails)

            # clean up
            pass_string, fails = do_one_command('sudo rm /cardinal/pi_hmi_tempscript')
            print (pass_string, fails)
    except:
        with open ('/dev/shm/pi_hmi_load_certs_exception', 'w') as f:
            f.write(traceback.format_exc())

# ----------------------------
def power_cycle_USB():
# ----------------------------
    _ = """
https://raspberrypi.stackexchange.com/questions/5407/how-can-i-cut-power-coming-out-of-the-pis-usb-ports

sudo uhubctl -l 1-1 -p 2 -a 0
sudo uhubctl -l 1-1 -p 2 -a 1

For 4B:
sudo uhubctl -l 2 -a 0
sudo uhubctl -l 2 -a 1

# cycle all levels (deep ones do nothing if no devices found)
sudo uhubctl -l 1 -a 2
sudo uhubctl -l 2 -a 2
sudo uhubctl -l 3 -a 2
sudo uhubctl -l 4 -a 2


sudo tee /sys/bus/usb/drivers/usb/unbind
sudo tee /sys/bus/usb/drivers/usb/bind

echo '1-1' |sudo tee /sys/bus/usb/drivers/usb/unbind
echo '1-1' |sudo tee /sys/bus/usb/drivers/usb/bind

    """

    pass_string, fails = do_one_command('sudo rm /cardinal/pi_hmi_tempscript')

# ----------------------------
def do_html_update():
# ----------------------------
    global G_thread_manage_data

    # ===================
    # Handle the touch event(s)
    # ===================
    # I know it is there at this point, so put the touch test here
    for key in G_thread_manage_data['states']:
        file_name = G_thread_manage_data['states'][key]['filename']
        fileStatsObj = os.stat (file_name)
        #accessTime = time.ctime ( fileStatsObj [ stat.ST_ATIME ] ) # Tue Jul 20 11:37:59 2021
        accessTime = fileStatsObj [ stat.ST_ATIME ] # 1626781079

        if G_thread_manage_data['states'][key]['last'] == 0:
            G_thread_manage_data['states'][key]['last'] = str(accessTime)
        if str(accessTime) != G_thread_manage_data['states'][key]['last']:
            G_thread_manage_data['states'][key]['last'] = str(accessTime)
            # do the toggle
            G_thread_manage_data['states'][key]['enabled'] = not G_thread_manage_data['states'][key]['enabled']


    if G_thread_manage_data['states']['reboot']['enabled']:
        pass_string, fails = do_one_command('sudo reboot')

    if G_thread_manage_data['states']['shutdown']['enabled']:
        pass_string, fails = do_one_command('sudo shutdown -h now')

    # ===================
    # Build the content
    # ===================
    bookmarks = get_my_bookmarks()

    content = build_local_html_content1(bookmarks)
    write_content_if_different(content, "/cardinal/localhtml/index.html")

# This is now served by rebuilding the index1 content, as index2
#    content = build_local_html_content2()
#    write_content_if_different(content, "/cardinal/localhtml/index2.html")

    content = build_local_html_content_downloads()
    write_content_if_different(content, "/cardinal/localhtml/downloads.html")

# ----------------------------
def do_one_time():
# ----------------------------
    file_system_expand()

    load_certs()

    make_cordis_logo()
    make_cardinal_logo()
    make_qrcode('https://slicer.cardinalhealth.net', '/cardinal/qr.gif')

    code128_data = code128_format_link(get_serial(), False)
    code128_image = code128_format_image(code128_data)
    code128_image.save("/dev/shm/serial.png", "PNG")

    redirect = """
<script>
window.onload = function() {
    setTimeout( function ()
    {
    window.location.href = "/cardinal/localhtml/index.html";
    }, 1500);
}
</script>
    """
    for key in G_thread_manage_data['states']:
        file_name = G_thread_manage_data['states'][key]['filename']
        write_content_if_different(redirect, file_name)

    do_html_update()

    configure_ntp()


# ----------------------------
def write_content_if_different(content, output_file, with_execute=False):
# ----------------------------
    # This does a temp file, then atomic move to final
    if content:
        last_content_written = ''
        if os.path.isfile(output_file):
            with open(output_file, 'r') as f:
                last_content_written = f.read()

        if content != last_content_written:
            temp_file = "/cardinal/localhtml/hmi_temp_file" # must be on the same physical device as the destination, to be atomic on mv
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            with open(temp_file, 'w') as f:
                f.write(content)


            print ("updating file: " + output_file)
            if with_execute:
                pass_string, fails = do_one_command('sudo chmod +x ' + temp_file)

            pass_string, fails = do_one_command('sudo chown -R worker:worker ' + temp_file)
            pass_string, fails = do_one_command('sudo mv ' + temp_file + ' ' + output_file)


# ----------------------------
def get_my_special_menu():
# ----------------------------
    input_file = "/cardinal/localhtml/special_menu"

    name_to_use = ''
    try:
        with open(input_file, 'r') as f:
            names_full = json.loads(f.read())
        serial = get_serial()
        if serial in names_full:
            name_to_use = copy.deepcopy(names_full[serial])
    except:
        pass

    return name_to_use

# ----------------------------
def do_maintenance():
# ----------------------------
    do_html_update()

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_hmi.cpython-37.pyc", "/cardinal/pi_hmi.pyc")

    if os.path.isfile("/cardinal/pi_hmi.py"):
        os.remove("/cardinal/pi_hmi.py")

    try:
        with open('/dev/shm/pi_hmi_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    # on initial boot, maybe the screen has not fully communicated with the pi yet.
    # Give it some time to settle, and then grab the screen size
    time.sleep(15)

    do_one_time()

    wake_count = 0
    do_maintenance_count = 0
    while True:
        # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
        time.sleep(1)
        do_maintenance()

        wake_count += 1
        try:
            with open('/dev/shm/pi_hmi_wake.txt', 'w') as f:
                f.write(str(wake_count))
        except:
            print ("!!! failed to write wake_count")














