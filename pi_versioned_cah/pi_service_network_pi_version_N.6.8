_ = """
sudo vi /cardinal/pi_network.py

sudo systemctl restart pi-network.service

sudo systemctl restart pi-network.service; exit

sudo systemctl status pi-network.service

"""

service = 'network'
version = 'N.6.8'

release_notes = """
2022.10.26
N.6.8
Parse the wifi config file for preshare key.
Allow saved changes to wifi config take effect immediately (instead of requiring a reboot).
#Move the cert load out of here... just manage here. (keep existing until all converted)

2022.10.10
N.6.7
Handle getting on corp with invalid (expired) certificate, and also a valid certificate available.
Undo the startup wipe of wireless connections (introduced in N.5.2)

2022.10.06
N.6.6
Report the radio resets count back to Slicer.

2022.10.03
N.6.5
On external radio start, do a usb reset on it first, to get a clean starting point each time.

2022.09.27
N.6.4
Refresh NetworkManager often, to keep network connection reports as fresh as possible.

2022.09.20
N.6.3
Allow for consistency on the wifi connection.

2022.07.15
N.5.5
Allow for wifi configuration to come from a data file: /cardinal/wifi_config.txt

2022.04.25
N.5.4
Network setting to ignore carrier loss when hopping WiFi access point (coaster).

2022.04.20
N.5.3
Wifi hopper option (jump to better signal access point)

2022.03.03
N.5.2
On first start up, always remove all wireless connections, to be sure that we are using the latest certificate.

2022.03.02
N.5.1
Support old image updates, to detect eth0 connections.

2022.03.01
N.5.0
Update the wifi certificate.

2022.02.28
N.4.2
Add support for showing wifi data on a served up webpage.

2022.01.31
N.4.1
Add support for looking up the call_home_locations

2021.12.29
N.4.0
Allow for manual configuration of IOT, from Slicer side, and from pi side.

2021.12.29
N.3.9
Completely eliminate IOT references.

2021.12.21
N.3.8
Disable IOT connection attempt.

2021.12.07
N.3.7
If the identified lan changes (wlan0, wlan1, eth0), then send new status.

2021.11.16
N.3.6
Set to connect to IOT as priority over corp.
Clean up a corp single config situation, and go dual for corp and iot.
When changing wifi configuration, restart network manager also, in order to take effect immediately.

2021.11.09
N.3.1
Fix the reporting, to correctly show wlanX, when connected.
Add support to allow wlan1 (external wifi adapter) to be configured

2021.11.03
N.2.1
Add to the network report: the adapter providing the access (wlan0, wlan1, eth0, ...).

2021.07.23
N.2.0
Add support for cah-iot wifi connection, in addition to corp wifi connection.

2021.05.15
N.1.2
Move the bookmarks update out of here, and into runner.

2021.05.14
N.1.1
If wired, delete all wireless connections.
If not wired, add corp connection.

2021.05.11
N.1.0
Get ready to be managed that we can be connected to corp, depending on not being on wired,
But not active yet. Save this as a 'corp still manually managed' version.

2021.05.10
N.0.6
Correct the corp certificate creation. (The ascii was emitting slash character which
was acting as an escape character, and causing incorrect results)

2021.05.10
N.0.5
Make the timeout be 15 seconds, instead of 3, for talking to Slicer.
The Slicer response is now calculated, instead of a direct lookup.
Put the manage_network_connections into play

2021.05.06
N.0.4
Add the parsing of the datadrop response, looking for data that we need to deal with.
Bookmarks

2021.04.20
N.0.1
Borrow from the monitor 1.1 file, and adapt to be a network monitor, reporter, and
perhaps even a controller of the network settings / connectivity.

"""

_thoughts = """
2022.09.14
Working on a thought... if there is a way that I can do a non-permanent change of the mac address in the external radio,
then someone unplugs it, and takes it, the mac address would be the default original mac address of that device,
and not match the one we have in our list.... That could meet everyones needs.

https://askubuntu.com/questions/1121523/how-do-i-get-networkmanager-to-assign-a-fixed-mac-address-to-eth0

sudo nmcli con modify Default 802-3-ethernet.cloned-mac-address 00:12:34:56:78:9a

daves test device

wlan0
dc:a6:32:96:56:c4

wlan1 (Panda)
9c:ef:d5:fc:d0:f6

sudo nmcli con modify Default 802-3-ethernet.cloned-mac-address 00:12:34:56:78:9a

sudo nmcli connection add type wifi con-name "cah-iot1" ifname wlan1 ssid "cah-iot" 802-11-wireless.cloned-mac-address dc:a6:32:96:56:c6


sudo nmcli connection modify --temporary "corp1" 802-11-wireless.cloned-mac-address dc:a6:32:96:56:c4
sudo nmcli con down corp1 && sudo nmcli con up corp1
ip addr

#sudo nmcli connection modify "corp1" 802-11-wireless.assigned-mac-address dc:a6:32:96:56:c4


https://udger.com/resources/mac-address-vendor-detail?name=raspberry_pi_foundation

raspberry pi
28:CD:C1:xx:xx:xx
B8:27:EB:xx:xx:xx
DC:A6:32:xx:xx:xx
E4:5F:01:xx:xx:xx

2022.09.19
47/63 in PR005 have internal as wlan0 (75%)
16/63 in PR005 have internal as wlan1 (25%)


Tests to run:
    On wlan1, upgrade from 5.4 to 6+ and see that it connects. (corp)

    plug into wired, and unplug wired, to see that wifi connects ok (both corp and iot)

    Leave on external IOT for a long time, and make sure it stays responsive
        even with the NetworkManager restart in place, ok so long as I only do "up" and not a "down"
        before that, and then restart network manager.


Passing Tests:
    Lab device, with wlan1 as internal, and wlan0 as external... remove external, and see that internal
    connects. This currently fails on N.6.2, fixed in N.6.3

    With internal as wlan1, what happens when unplugging external radio?
        Does internal move to wlan0, or stay at wlan1?
            Stays at wlan1, and replugging external re-shows at wlan0.


"""

_usb_reset_logic = """
sudo python3
dev_path = '/dev/bus/usb/001/008'
import os
import fcntl
USBDEVFS_RESET = 21780
f = open(dev_path, 'w', os.O_WRONLY)
fcntl.ioctl(f, USBDEVFS_RESET, 0)
f.close()


sudo usbreset 001/008

"""

_multiple_valid_corp_wifi_certs = """
Make network use old corp cert, see if it throws any message on connect attempt, using the nee page 2 view.

Be able to have pi pull many certs, keep them all, and rotate through a list to find one that works (ping *******? If no error message to be used)

Report inventory of certs, and which one we are currently using.

Write a non-volatile of which one worked last, and start there on next attempt.

Naming scheme to show date of expiration, and also a marker of somekind in case there are two different ones for the same day.
20231010a
20231010b


"""

other_content = """
sudo vi /cardinal/pi-network
sudo chmod +x /cardinal/pi-network

# ===== begin: start file
#!/usr/bin/env python3
import pi_network
pi_network.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-network.service
sudo systemctl daemon-reload
sudo systemctl stop pi-network.service
sudo systemctl start pi-network.service
sudo systemctl enable pi-network.service
systemctl status pi-network.service

sudo systemctl restart pi-network.service

systemctl status pi-network.service

# Logging of std out
cat /var/log/syslog | fgrep pi-network

OR

tail -n 1000 -f /var/log/syslog | fgrep pi-network

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-network
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_network


"""

_Transmit_power_notes = """
https://www.digitalcitizen.life/double-wifi-speed-windows-intel-network-cards/


https://raspberrypi.stackexchange.com/questions/90932/restrict-wifi-range-of-pi-3b

sudo iw dev wlan0 info

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 31.00 dBm

sudo iw dev wlan0 set txpower fixed 1000

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 10.00 dBm


sudo iw dev wlan0 set txpower fixed 500

Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 5.00 dBm


DELL:
https://www.digitalcitizen.life/double-wifi-speed-windows-intel-network-cards/
2: Medium-Low

The power setting does not survive a reboot.


wlan0, set to 100 sets to 1.0 dBm. Set to anything less gets 0 dBm (? no limit to request on top end)
wlan1, set to less than 200 gets error "command failed: Operation not supported (-95)".  (? no limit to request on top end)

Can not set wlan0 above 31 dBm
Can not set wlan1 to any value (fixed at 20)

if wlan1 not plugged in, get "command failed: No such device (-19)" (to both info and set power)

Power level discussion:
https://community.element14.com/products/raspberry-pi/f/forum/20444/rt3070-wireless-adapter-adjust-tx-power

Power management:
https://www.lifewire.com/usb-wifi-adapter-raspberry-pi-4058093

iwconfig
lo        no wireless extensions.

eth0      no wireless extensions.

wlan0     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=31 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on

wlan1     IEEE 802.11  ESSID:off/any
          Mode:Managed  Access Point: Not-Associated   Tx-Power=20 dBm
          Retry short  long limit:2   RTS thr:off   Fragment thr:off
          Power Management:off


https://askubuntu.com/questions/85214/how-can-i-prevent-iwconfig-power-management-from-being-turned-on
https://thepihut.com/blogs/raspberry-pi-tutorials/disable-wifi-power-management
https://unix.stackexchange.com/questions/269661/how-to-turn-off-wireless-power-management-permanently

Q: is the internal radio getting power managed to the off position in 1.4.8 images, and
    the external radio seems to have power management off, and so that's the reason the
    external radio helped them operate? Also, external PAU09 is 2.4 and 5 GHz. (Internal is also)
    What is the 20 dBm limit on wlan1, combined with the antenna, yields a LOWER total transmit
    power, thus justifying using the internal radio, with the power turned down?
    wlan0 = 31 dBm with antenna ?
    wlan1 = 20 dBm with 5dBi antenna = 25 dBm when aligned?

sudo /sbin/iwconfig wlan0 power off
(survive reboot = NO)
sudo iw wlan0 get power_save


wlan0: on AP104 at signal quality 50
South: in 3rd floor default conference room, not under an access point
31dBm -> 63  mW/m^2 on upload test  at 11MBps
20 dBm -> 3.5   at 11MBps (Still get 125 to 250 mW/m^2 bursts for 1 second)
10dBm -> 0.5 at 11MBps
 5dBm -> 0.2 at 11MBps
 3dBm -> 0.125 at 9 to 11MBps
 1dBm -> 0.100 at 9 to 11MBps

walk to kitchen, still on AP104 at 40, at 1dbm, get 1 MPBs, set to 2dBm, get 2.5 MBps, set back to 1, get 2.5, at 5 get 3.6
at 10 get 7, at 31 get 12, at 10 get 7

restart network, connected to 108 at 50, set to 1 get 11, set to 5 get 12, and walk more

Every 20 seconds, get burst of 40 mW/m^2 no matter the power setting. Is this bluetooth? (stop that service, and burst still happens)
Our rescan is every 4 seconds?
stopping network service makes the every 20 second burst stop.
Burst is from "sudo nmcli -c no device wifi list --rescan yes"
This does not do it all the time, just when its been a while since it did a full scan: "sudo nmcli -c no device wifi list"

Try disabling the scan, and turning the power down, and then walk around.


"""

_channel_connection = """
https://developer-old.gnome.org/NetworkManager/stable/nm-settings-nmcli.html

(Search for channel, look to the result near the bottom of the page)

sudo nmcli connection modify corp0  802-11-wireless.band a  802-11-wireless.channel 52

Try...
sudo nmcli connection modify corp0 802-11-wireless.bssid 34:FC:B9:75:C0:92


sudo wpa_cli status



This works...
https://forums.raspberrypi.com/viewtopic.php?t=224243

sudo wpa_cli -i wlan0 status

# Set the "preferred bssid"
# Chan 052
sudo wpa_cli -i wlan0 bssid 0 34:FC:B9:75:C0:92; sudo wpa_cli -i wlan0 reassociate


# Chan 104
sudo wpa_cli -i wlan0 bssid 0 34:FC:B9:75:BA:32; sudo wpa_cli -i wlan0 reassociate

# Chan 108
sudo wpa_cli -i wlan0 bssid 0 44:48:C1:81:A1:31; sudo wpa_cli -i wlan0 reassociate

# dis-allow list
https://gist.github.com/penguinpowernz/1d36a38af4fac4553562410e0bd8d6cf

# Chan 104
sudo wpa_cli -i wlan0 blacklist 34:FC:B9:75:BA:32; sudo wpa_cli -i wlan0 reassociate

# view blacklist
sudo wpa_cli -i wlan0 blacklist

#clear list
sudo wpa_cli -i wlan0 blacklist clear; sudo wpa_cli -i wlan0 reassociate
blacklist clear

!!! Cahnges are not persisting... something is resetting these choices



"""

# from Scott Wolke, for PR005 (which includes PR010, because they share a controller)
bssid_to_AP_raw = """
00:4e:35:fc:26:51  corp     N/A   10.216.237.73   a      ap    161/12.0/29.3     0       NW-PR005-AP-1B-047  0        17d:2h:6m:42s    1500  -          2    T          A                              no
00:4e:35:fc:26:52  cah-iot  N/A   10.216.237.73   a      ap    161/12.0/29.3     0       NW-PR005-AP-1B-047  0        17d:2h:6m:48s    1500  -          164  T          A                              no
20:a6:cd:d4:25:31  corp     N/A   10.216.201.45   a      ap    40/12.0/24.0      0       NW-PR010-AP-1C-033  0        72d:3h:37m:43s   1500  -          2    T          A                              no
20:a6:cd:d4:25:32  cah-iot  N/A   10.216.201.45   a    ap    40/12.0/24.0      1       NW-PR010-AP-1C-033  0        72d:3h:37m:35s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6b:71  corp     N/A   10.216.201.38   a      ap    40/12.0/24.0      2       NW-PR010-AP-1D-034  0        72d:3h:37m:45s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6b:72  cah-iot  N/A   10.216.201.38   a    ap    40/12.0/24.0      2       NW-PR010-AP-1D-034  0        72d:3h:37m:36s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6b:91  corp     N/A   10.216.237.84   a      ap    48/12.0/23.5      3       NW-PR005-AP-2A-009  0        72d:3h:37m:42s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6b:92  cah-iot  N/A   10.216.237.84   a    ap    48/12.0/23.5      4       NW-PR005-AP-2A-009  0        72d:3h:37m:26s   1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6b:d0  corp     N/A   10.216.201.20   a-VHT  ap    144-/15.0/29.5    1       NW-PR010-AP-2B-017  0        72d:3h:37m:44s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6b:d1  byod     N/A   10.216.201.20   a      ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:33s   1500  -          2    T          A                              no
20:a6:cd:d4:6b:d2  guest    N/A   10.216.201.20   a    ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:26s   1500  -          166  T          A                              no
20:a6:cd:d4:6b:d3  cah-iot  N/A   10.216.201.20   a      ap    144/15.0/29.5     0       NW-PR010-AP-2B-017  0        72d:3h:37m:29s   1500  -          164  T          A                              no
20:a6:cd:d4:6c:31  corp     N/A   10.216.201.24   a      ap    40/12.0/24.0      8       NW-PR010-AP-1A-039  0        72d:3h:37m:45s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6c:32  cah-iot  N/A   10.216.201.24   a    ap    40/12.0/24.0      10      NW-PR010-AP-1A-039  0        72d:3h:37m:36s   1500  -          164  T          A sU                           no
20:a6:cd:d4:6c:f1  corp     N/A   10.216.201.42   a      ap    36/12.0/24.0      0       NW-PR010-AP-1C-014  0        31d:11h:38m:1s   1500  -          2    T          A                              no
20:a6:cd:d4:6c:f2  cah-iot  N/A   10.216.201.42   a    ap    36/12.0/24.0      0       NW-PR010-AP-1C-014  0        31d:11h:37m:52s  1500  -          164  T          A                              no
20:a6:cd:d4:6d:10  corp     N/A   10.216.201.31   a-VHT  ap    112-/14.0/30.0    4       NW-PR010-AP-2B-008  0        31d:11h:40m:1s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6d:12  guest    N/A   10.216.201.31   a    ap    112/14.0/30.0     1       NW-PR010-AP-2B-008  0        31d:11h:39m:52s  1500  -          166  T          sA U  (AAC=10.216.254.223)     no
20:a6:cd:d4:6d:30  corp     N/A   10.216.237.36   a-VHT  ap    153-/14.0/33.8    2       NW-PR005-AP-2B-053  0        72d:3h:37m:44s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:6d:31  byod     N/A   10.216.237.36   a      ap    153/14.0/33.8     1       NW-PR005-AP-2B-053  0        72d:3h:37m:44s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6d:32  guest    N/A   10.216.237.36   a    ap    153/14.0/33.8     0       NW-PR005-AP-2B-053  0        72d:3h:37m:35s   1500  -          166  T          A                              no
20:a6:cd:d4:6d:33  cah-iot  N/A   10.216.237.36   a      ap    153/14.0/33.8     0       NW-PR005-AP-2B-053  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:71  corp     N/A   10.216.201.39   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-032  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d4:6d:72  cah-iot  N/A   10.216.201.39   a    ap    157/12.0/27.8     0       NW-PR010-AP-1E-032  0        72d:3h:37m:35s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:d1  corp     N/A   10.216.201.54   a      ap    48/12.0/23.5      0       NW-PR010-AP-1D-035  0        72d:3h:37m:45s   1500  -          2    T          A                              no
20:a6:cd:d4:6d:d2  cah-iot  N/A   10.216.201.54   a    ap    48/12.0/23.5      0       NW-PR010-AP-1D-035  0        72d:3h:37m:36s   1500  -          164  T          A                              no
20:a6:cd:d4:6d:f1  corp     N/A   10.216.237.38   a      ap    40/12.0/24.0      2       NW-PR005-AP-2A-012  0        31d:11h:38m:1s   1500  -          2    T          A sU                           no
20:a6:cd:d4:6d:f2  cah-iot  N/A   10.216.237.38   a    ap    40/12.0/24.0      0       NW-PR005-AP-2A-012  0        31d:11h:37m:52s  1500  -          164  T          A                              no
20:a6:cd:d4:6e:71  corp     N/A   10.216.201.29   a      ap    149/12.0/27.8     0       NW-PR010-AP-2B-036  0        72d:3h:37m:45s   1500  -          2    T          A                              no
20:a6:cd:d4:6e:72  cah-iot  N/A   10.216.201.29   a    ap    149/12.0/27.8     4       NW-PR010-AP-2B-036  0        72d:3h:37m:36s   1500  -          164  T          A U sU                         no
20:a6:cd:d4:6e:b1  corp     N/A   10.216.201.28   a      ap    161/12.0/27.8     4       NW-PR010-AP-2B-037  0        72d:3h:37m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:6e:f1  corp     N/A   10.216.201.25   a      ap    149/12.0/27.8     6       NW-PR010-AP-1A-038  0        10d:15h:10m:31s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:70:31  corp     N/A   10.216.237.42   a      ap    161/12.0/27.8     2       NW-PR005-AP-1F-014  0        42d:22h:23m:28s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:70:b2  guest    N/A   10.216.237.29   a    ap    124/14.0/30.0     3       NW-PR005-AP-1B-016  0        17d:2h:8m:5s     1500  -          166  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:7b:30  corp     N/A   10.216.237.113  a-VHT  ap    136-/13.0/30.0    3       NW-PR005-AP-2G-058  0        43d:1h:45m:59s   1500  -          2    T          A U sU                         no
20:a6:cd:d4:7b:31  byod     N/A   10.216.237.113  a      ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:59s   1500  -          2    T          A                              no
20:a6:cd:d4:7b:32  guest    N/A   10.216.237.113  a    ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:50s   1500  -          166  T          A                              no
20:a6:cd:d4:7b:33  cah-iot  N/A   10.216.237.113  a      ap    136/13.0/30.0     0       NW-PR005-AP-2G-058  0        43d:1h:45m:53s   1500  -          164  T          A                              no
20:a6:cd:d4:e6:52  cah-iot  N/A   10.216.201.33   a      ap    48/12.0/23.5      2       NW-PR010-AP-1D-022  0        72d:3h:37m:29s   1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d4:ed:70  corp     N/A   10.216.201.41   a      ap    157/12.0/27.8     0       NW-PR010-AP-1C-027  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d4:ed:72  cah-iot  N/A   10.216.201.41   a      ap    157/12.0/27.8     0       NW-PR010-AP-1C-027  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:14:50  corp     N/A   10.216.201.36   a      ap    40/12.0/24.0      0       NW-PR010-AP-1E-016  0        23h:41m:58s      1500  -          2    T          A                              no
20:a6:cd:d6:14:52  cah-iot  N/A   10.216.201.36   a      ap    40/12.0/24.0      0       NW-PR010-AP-1E-016  0        23h:41m:54s      1500  -          164  T          A                              no
20:a6:cd:d6:15:70  corp     N/A   10.216.201.47   a      ap    161/12.0/27.8     0       NW-PR010-AP-1C-005  0        72d:3h:37m:43s   1500  -          2    T          A                              no
20:a6:cd:d6:15:72  cah-iot  N/A   10.216.201.47   a      ap    161/12.0/27.8     0       NW-PR010-AP-1C-005  0        72d:3h:37m:39s   1500  -          164  T          A                              no
20:a6:cd:d6:1b:31  corp     N/A   10.216.201.35   a      ap    36/12.0/24.0      1       NW-PR010-AP-1D-030  0        72d:3h:37m:45s   1500  -          2    T          A sU                           no
20:a6:cd:d6:1b:32  cah-iot  N/A   10.216.201.35   a      ap    36/12.0/24.0      3       NW-PR010-AP-1D-030  0        72d:3h:37m:39s   1500  -          164  T          A sU                           no
20:a6:cd:d6:1b:d1  corp     N/A   10.216.201.49   a      ap    161/12.0/27.8     0       NW-PR010-AP-1D-028  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:1b:d2  cah-iot  N/A   10.216.201.49   a      ap    161/12.0/27.8     0       NW-PR010-AP-1D-028  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:1c:b1  corp     N/A   10.216.201.44   a      ap    161/15.0/27.8     2       NW-PR010-AP-1C-021  0        72d:3h:37m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d6:1c:d1  corp     N/A   10.216.201.50   a      ap    149/12.0/27.8     0       NW-PR010-AP-1D-024  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:1c:d2  cah-iot  N/A   10.216.201.50   a      ap    149/12.0/27.8     0       NW-PR010-AP-1D-024  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:d6:23:52  cah-iot  N/A   10.216.201.32   a      ap    161/12.0/27.8     1       NW-PR010-AP-1D-010  0        72d:3h:37m:39s   1500  -          164  T          sA U  (AAC=10.216.254.223)     no
20:a6:cd:d6:25:50  corp     N/A   10.216.201.30   a-VHT  ap    52+/13.0/29.0     9       NW-PR010-AP-2B-007  0        72d:3h:37m:43s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
20:a6:cd:d6:25:71  corp     N/A   10.216.201.43   a      ap    149/12.0/27.8     0       NW-PR010-AP-1C-009  0        72d:3h:37m:44s   1500  -          2    T          A                              no
20:a6:cd:d6:25:72  cah-iot  N/A   10.216.201.43   a      ap    149/12.0/27.8     0       NW-PR010-AP-1C-009  0        72d:3h:37m:38s   1500  -          164  T          A                              no
20:a6:cd:df:c5:f2  cah-iot  N/A   10.216.201.59   a    ap    36/12.0/24.0      2       NW-PR010-AP-1E-019  0        10d:15h:10m:22s  1500  -          164  T          sA U sU  (AAC=10.216.254.223)  no
24:f2:7f:a5:4c:51  corp     N/A   10.216.201.51   a    ap    157/12.0/27.8     0       NW-PR010-AP-1D-012  0        72d:3h:37m:36s   1500  -          2    T          A                              no
24:f2:7f:a5:4c:52  cah-iot  N/A   10.216.201.51   a      ap    157/12.0/27.8     0       NW-PR010-AP-1D-012  0        72d:3h:37m:45s   1500  -          164  T          A                              no
24:f2:7f:a5:4e:31  corp     N/A   10.216.201.27   a    ap    48/12.0/23.5      5       NW-PR010-AP-1A-042  0        10d:15h:12m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
24:f2:7f:a5:4e:32  cah-iot  N/A   10.216.201.27   a      ap    48/12.0/23.5      1       NW-PR010-AP-1A-042  0        10d:15h:12m:31s  1500  -          164  T          sA U  (AAC=10.216.254.223)     no
24:f2:7f:a5:4e:51  corp     N/A   10.216.201.34   a    ap    40/12.0/24.0      1       NW-PR010-AP-1D-018  0        72d:3h:37m:35s   1500  -          2    T          A sU                           no
24:f2:7f:a5:4e:52  cah-iot  N/A   10.216.201.34   a      ap    40/12.0/24.0      1       NW-PR010-AP-1D-018  0        72d:3h:37m:44s   1500  -          164  T          A U                            no
7c:57:3c:1b:32:f1  corp     N/A   10.216.237.23   a      ap    48/12.0/23.5      7       NW-PR005-AP-1B-022  0        17d:2h:8m:10s    1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
7c:57:3c:1b:35:51  corp     N/A   10.216.237.22   a      ap    44/12.0/24.0      3       NW-PR005-AP-1F-019  0        42d:22h:23m:16s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
a8:bd:27:84:9d:91  corp     N/A   10.216.201.37   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-011  0        72d:3h:37m:39s   1500  -          2    T          A                              no
a8:bd:27:84:9d:92  cah-iot  N/A   10.216.201.37   a      ap    157/12.0/27.8     0       NW-PR010-AP-1E-011  0        72d:3h:37m:43s   1500  -          164  T          A                              no
b0:b8:67:cf:2a:b0  corp     N/A   10.216.237.32   a-VHT  ap    56-/14.0/29.0     3       NW-PR005-AP-2A-052  0        72d:3h:37m:39s   1500  -          2    T          A U sU                         no
b0:b8:67:cf:2a:b1  byod     N/A   10.216.237.32   a      ap    56/14.0/29.0      0       NW-PR005-AP-2A-052  0        72d:3h:37m:39s   1500  -          2    T          A                              no
b0:b8:67:cf:2a:b3  cah-iot  N/A   10.216.237.32   a      ap    56/14.0/29.0      0       NW-PR005-AP-2A-052  0        72d:3h:37m:43s   1500  -          164  T          A                              no
e8:26:89:8e:30:31  corp     N/A   10.216.237.111  a      ap    40/12.0/24.0      1       NW-PR005-AP-2G-036  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8e:30:32  cah-iot  N/A   10.216.237.111  a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-036  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:32:91  corp     N/A   10.216.237.103  a      ap    48/12.0/23.5      4       NW-PR005-AP-2G-033  0        31d:11h:41m:51s  1500  -          2    T          A U sU                         no
e8:26:89:8e:32:92  cah-iot  N/A   10.216.237.103  a      ap    48/12.0/23.5      3       NW-PR005-AP-2G-033  0        31d:11h:41m:55s  1500  -          164  T          A sU                           no
e8:26:89:8e:39:31  corp     N/A   10.216.237.30   a      ap    149/12.0/27.8     0       NW-PR005-AP-1F-006  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8e:39:32  cah-iot  N/A   10.216.237.30   a      ap    149/12.0/27.8     0       NW-PR005-AP-1F-006  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:60:d1  corp     N/A   10.216.237.89   a    ap    36/12.0/24.0      1       NW-PR005-AP-1F-011  0        42d:22h:23m:25s  1500  -          2    T          A U                            no
e8:26:89:8e:60:d2  cah-iot  N/A   10.216.237.89   a      ap    36/12.0/24.0      0       NW-PR005-AP-1F-011  0        42d:22h:23m:32s  1500  -          164  T          A                              no
e8:26:89:8e:61:11  corp     N/A   10.216.237.105  a    ap    149/12.0/27.8     0       NW-PR005-AP-2G-038  0        43d:1h:45m:41s   1500  -          2    T          A                              no
e8:26:89:8e:61:12  cah-iot  N/A   10.216.237.105  a      ap    149/12.0/27.8     0       NW-PR005-AP-2G-038  0        43d:1h:45m:48s   1500  -          164  T          A                              no
e8:26:89:8e:71:11  corp     N/A   10.216.237.119  a      ap    36/12.0/24.0      1       NW-PR005-AP-2G-037  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8e:71:12  cah-iot  N/A   10.216.237.119  a      ap    36/12.0/24.0      0       NW-PR005-AP-2G-037  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8e:72:b1  corp     N/A   10.216.237.96   a      ap    40/12.0/24.0      3       NW-PR005-AP-2G-032  0        10d:15h:12m:25s  1500  -          2    T          A U sU                         no
e8:26:89:8e:72:b2  cah-iot  N/A   10.216.237.96   a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-032  0        10d:15h:12m:29s  1500  -          164  T          A                              no
e8:26:89:8e:73:90  corp     N/A   10.216.237.79   a-VHT  ap    100+/13.0/30.0    4       NW-PR005-AP-2B-054  0        31d:11h:41m:51s  1500  -          2    T          A U sU                         no
e8:26:89:8e:73:91  byod     N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8e:73:92  guest    N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:55s  1500  -          166  T          A                              no
e8:26:89:8e:73:93  cah-iot  N/A   10.216.237.79   a      ap    100/13.0/30.0     0       NW-PR005-AP-2B-054  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:9b:b1  corp     N/A   10.216.237.95   a    ap    48/12.0/23.5      3       NW-PR005-AP-1F-005  0        10d:15h:6m:38s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8e:b4:71  corp     N/A   10.216.237.68   a    ap    40/12.0/24.0      2       NW-PR005-AP-1F-025  0        31d:11h:39m:52s  1500  -          2    T          sA U  (AAC=10.216.254.223)     no
e8:26:89:8e:cd:b0  corp     N/A   10.216.237.55   a-VHT  ap    120-/13.0/30.0    4       NW-PR005-AP-2G-056  0        10d:15h:10m:29s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8e:d8:f0  corp     N/A   10.216.237.34   a-VHT  ap    140+/14.0/29.5    1       NW-PR005-AP-2A-051  0        31d:11h:41m:55s  1500  -          2    T          A sU                           no
e8:26:89:8e:d8:f1  byod     N/A   10.216.237.34   a    ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8e:d8:f2  guest    N/A   10.216.237.34   a      ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:57s  1500  -          166  T          A                              no
e8:26:89:8e:d8:f3  cah-iot  N/A   10.216.237.34   a      ap    140/14.0/29.5     0       NW-PR005-AP-2A-051  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:dd:31  corp     N/A   10.216.237.69   a    ap    157/12.0/27.8     0       NW-PR005-AP-1F-018  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8e:dd:32  cah-iot  N/A   10.216.237.69   a      ap    157/12.0/27.8     0       NW-PR005-AP-1F-018  0        31d:11h:41m:57s  1500  -          164  T          A                              no
e8:26:89:8e:f9:31  corp     N/A   10.216.237.109  a    ap    40/12.0/24.0      0       NW-PR005-AP-2G-043  0        43d:1h:45m:45s   1500  -          2    T          A                              no
e8:26:89:8e:f9:32  cah-iot  N/A   10.216.237.109  a      ap    40/12.0/24.0      0       NW-PR005-AP-2G-043  0        43d:1h:45m:54s   1500  -          164  T          A                              no
e8:26:89:8f:1e:70  corp     N/A   10.216.237.66   a-VHT  ap    124+/15.0/30.0    4       NW-PR005-AP-2B-055  0        10d:15h:12m:31s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:25:31  corp     N/A   10.216.237.21   a    ap    44/15.0/24.0      0       NW-PR005-AP-1F-010  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:25:32  cah-iot  N/A   10.216.237.21   a      ap    44/15.0/24.0      0       NW-PR005-AP-1F-010  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:3d:f1  corp     N/A   10.216.237.99   a    ap    48/12.0/23.5      0       NW-PR005-AP-2G-040  0        43d:1h:45m:43s   1500  -          2    T          A                              no
e8:26:89:8f:3d:f2  cah-iot  N/A   10.216.237.99   a      ap    48/12.0/23.5      0       NW-PR005-AP-2G-040  0        43d:1h:45m:52s   1500  -          164  T          A                              no
e8:26:89:8f:48:b1  corp     N/A   10.216.237.112  a    ap    157/12.0/27.8     3       NW-PR005-AP-2G-029  0        10d:15h:10m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:57:d1  corp     N/A   10.216.237.67   a    ap    161/12.0/27.8     0       NW-PR005-AP-1F-008  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:57:d2  cah-iot  N/A   10.216.237.67   a      ap    161/12.0/27.8     0       NW-PR005-AP-1F-008  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:5b:b1  corp     N/A   10.216.237.118  a    ap    36/12.0/24.0      2       NW-PR005-AP-2G-031  0        10d:15h:10m:22s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:65:91  corp     N/A   10.216.237.20   a    ap    157/12.0/27.8     0       NW-PR005-AP-1F-007  0        31d:11h:41m:48s  1500  -          2    T          A                              no
e8:26:89:8f:65:92  cah-iot  N/A   10.216.237.20   a      ap    157/12.0/27.8     0       NW-PR005-AP-1F-007  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:6e:d1  corp     N/A   10.216.237.77   a    ap    36/12.0/24.0      2       NW-PR005-AP-1E-001  0        31d:11h:41m:48s  1500  -          2    T          A sU                           no
e8:26:89:8f:6e:d2  cah-iot  N/A   10.216.237.77   a      ap    36/12.0/24.0      0       NW-PR005-AP-1E-001  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:8f:f1  corp     N/A   10.216.237.104  a      ap    44/12.0/24.0      1       NW-PR005-AP-2G-034  0        31d:11h:41m:51s  1500  -          2    T          A sU                           no
e8:26:89:8f:8f:f2  cah-iot  N/A   10.216.237.104  a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-034  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:9e:71  corp     N/A   10.216.237.33   a      ap    36/12.0/24.0      5       NW-PR005-AP-2A-004  0        10d:15h:12m:27s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:ae:51  corp     N/A   10.216.237.101  a      ap    36/12.0/24.0      6       NW-PR005-AP-2G-027  0        10d:15h:12m:25s  1500  -          2    T          A U sU                         no
e8:26:89:8f:ae:52  cah-iot  N/A   10.216.237.101  a      ap    36/12.0/24.0      2       NW-PR005-AP-2G-027  0        10d:15h:12m:29s  1500  -          164  T          A sU                           no
e8:26:89:8f:b2:31  corp     N/A   10.216.237.102  a      ap    36/12.0/24.0      1       NW-PR005-AP-2G-039  0        43d:1h:45m:52s   1500  -          2    T          A U                            no
e8:26:89:8f:b2:32  cah-iot  N/A   10.216.237.102  a      ap    36/12.0/24.0      0       NW-PR005-AP-2G-039  0        43d:1h:45m:56s   1500  -          164  T          A                              no
e8:26:89:8f:b8:b1  corp     N/A   10.216.237.106  a      ap    161/12.0/27.8     3       NW-PR005-AP-2G-030  0        10d:15h:10m:25s  1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:b9:31  corp     N/A   10.216.237.110  a      ap    44/12.0/24.0      1       NW-PR005-AP-2G-044  0        43d:1h:45m:44s   1500  -          2    T          A sU                           no
e8:26:89:8f:b9:32  cah-iot  N/A   10.216.237.110  a      ap    44/12.0/24.0      5       NW-PR005-AP-2G-044  0        43d:1h:45m:48s   1500  -          164  T          A U sU                         no
e8:26:89:8f:c1:31  corp     N/A   10.216.237.85   a      ap    157/12.0/27.8     0       NW-PR005-AP-2G-035  0        31d:11h:41m:51s  1500  -          2    T          A                              no
e8:26:89:8f:c1:32  cah-iot  N/A   10.216.237.85   a      ap    157/12.0/27.8     0       NW-PR005-AP-2G-035  0        31d:11h:41m:55s  1500  -          164  T          A                              no
e8:26:89:8f:c2:f0  corp     N/A   10.216.237.90   a-VHT  ap    140+/13.0/29.5    8       NW-PR005-AP-1A-023  0        17d:1h:33m:44s   1500  -          2    T          sA U sU  (AAC=10.216.254.223)  no
e8:26:89:8f:c2:f2  guest    N/A   10.216.237.90   a      ap    140/13.0/29.5     1       NW-PR005-AP-1A-023  0        17d:1h:33m:48s   1500  -          166  T          sA U  (AAC=10.216.254.223)     no
e8:26:89:8f:d0:51  corp     N/A   10.216.237.98   a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-028  0        10d:15h:12m:25s  1500  -          2    T          A                              no
e8:26:89:8f:d0:52  cah-iot  N/A   10.216.237.98   a      ap    44/12.0/24.0      0       NW-PR005-AP-2G-028  0        10d:15h:12m:29s  1500  -          164  T          A                              no
"""

# ----------------------------
def uptime():
# ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0

# ----------------------------
def get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list):
# ----------------------------
    return_value = ''

    if current_cert in corp_wifi_certs_list:
        return_value = corp_wifi_certs_list[(1 + corp_wifi_certs_list.index(current_cert)) % len(corp_wifi_certs_list)]
    else:
        return_value = corp_wifi_certs_list[0]

    return return_value

# ----------------------------
def find_external_radio_from_usbreset(content):
# ----------------------------
    return_value = ''

    for line in content.split('\n'):
        if ('802' in line) and ('WLAN' in line):
            splits = line.strip().split()
            if len(splits) > 1:
                return_value = splits[1]
    return return_value

# ----------------------------
def find_external_radio_from_lsusb_dash_v(lsusb_t):
# ----------------------------
    in_what_we_want = False

    return_value = ''

    bus_number = ''
    for line in lsusb_t.split('\n'):
        splits = line.split()
        if len(splits) > 2:
            if splits[1] == 'Bus':
                bus_number = splits[2].split('.')[0]

        if 'rt2800usb' in line:

            device_number = ''
            if ': Dev' in line:
                device_number = line.split(': Dev')[1].split(',')[0]
            if bus_number and device_number:
                return_value = '{:03d}'.format(int(bus_number)) + '/' + '{:03d}'.format(int(device_number))

    return return_value

# ----------------------------
def is_connection_active(line):
# ----------------------------
    if len(line.split(':')) > 3:
        if line.split(':')[3]:
            return True
        else:
            return False
    else:
        return False


# ----------------------------
def wireless_to_configure_from_wlanX(device):
# ----------------------------
    return device[-1:]


# ----------------------------
def get_wifi_mapping(devices_report):
# ----------------------------
    internal = ''
    external = ''

    in_wlan0 = False
    in_wlan1 = False

    wlan0_present = False
    wlan1_present = False

    for line in devices_report.split('\n'):
        try:
            if line[0:6] == 'wlan0:':
                in_wlan0 = True
                in_wlan1 = False
                wlan0_present = True
            if line[0:6] == 'wlan1:':
                in_wlan0 = False
                in_wlan1 = True
                wlan1_present = True
        except:
            pass

        if "brcmfmac" in line:
            if in_wlan0:
                internal = 'wlan0'
            if in_wlan1:
                internal = 'wlan1'

    if wlan1_present:
        if internal == 'wlan0':
            external = 'wlan1'

    if wlan0_present:
        if internal == 'wlan1':
            external = 'wlan0'

    return_value = {'internal_wifi':internal, 'external_wifi':external}

    return return_value

# ----------------------------
def make_bssid_to_ap():
# ----------------------------
    return_value = {}

    for item in bssid_to_AP_raw.split('\n'):
        splits = item.split()
        if len(splits) > 8:
            full_ap = splits[8].split('-')[1] + ':' + splits[8].split('-')[-1]
            return_value[splits[0].upper()] = full_ap

    return return_value

import copy
import json
import os
try:
    import requests
except:
    # for unittest
    pass

import shutil
import socket
import subprocess
import sys
import threading
import time
import traceback
import unittest

from sys import version as python_version
from cgi import parse_header, parse_multipart

if python_version.startswith('3'):
    from urllib.parse import parse_qs
    from http.server import BaseHTTPRequestHandler,HTTPServer
else:
    from urlparse import parse_qs
    from BaseHTTPServer import BaseHTTPRequestHandler,HTTPServer

#from apscheduler.schedulers.background import BackgroundScheduler

# Globals
s_allow_wifi_config_previous = {}
s_allow_wifi_config_connection = {}
s_corp_wifi_certs_path = "/cardinal/corp_certs/"
s_corp_wifi_cert_last_used_path = "/cardinal/"

s_corp_wifi_certs_2_path = "/cardinal/wifi_certs/" # provided by pi_organization.py

s_to_use_corp_wifi_certs_path = ''

s_corp_wifi_cert_in_use = ''
s_corp_wifi_cert_just_loaded_in_use = False
s_corp_wifi_cert_rotation_count = 0
s_corp_wifi_cert_retry_count = 0
s_corp_connect_attempts = []

s_crypt_cahiot_preshare = 'g9nHZ3QvYZ'
s_pass_count = 0
s_http_page_count = 0
s_http_previous_path = ''

s_wifi_X_removed_count = 0
s_force_rescan = True
s_run_speed_tests = False
s_pop_to_top_signal_channel = False

s_time_of_last_pop = 0
s_is_engineering_device = False

s_forced_rescan_count = 0
s_bssid_to_ap = make_bssid_to_ap()
s_bssid_last = ''
s_bssid_change_count = 0

s_items_deleted_count = 0
s_passes_not_connected = 0
s_seconds_per_manage_pass = 4

s_lan_detail_counts = 0
s_lan_worst_restarts = 0
s_lan_detail_string = '(none yet)'

s_usb_reset_radio_count = 0

# ----------------------------
def macAddress(portName):
# ----------------------------
    return_value = ''
    pass_string, fails = do_one_command('ip -j addr')
    # pass_string = '[{"ifindex":1,"ifname":"lo","flags":["LOOPBACK","UP","LOWER_UP"],"mtu":65536,"qdisc":"noqueue","operstate":"UNKNOWN","group":"default","txqlen":1000,"link_type":"loopback","address":"00:00:00:00:00:00","broadcast":"00:00:00:00:00:00","addr_info":[{"family":"inet","local":"127.0.0.1","prefixlen":8,"scope":"host","label":"lo","valid_life_time":4294967295,"preferred_life_time":4294967295},{"family":"inet6","local":"::1","prefixlen":128,"scope":"host","valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":2,"ifname":"eth0","flags":["BROADCAST","MULTICAST","UP","LOWER_UP"],"mtu":1500,"qdisc":"mq","operstate":"UP","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c2","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691098,"preferred_life_time":691098},{"family":"inet","local":"**************","prefixlen":27,"broadcast":"**************","scope":"global","secondary":true,"dynamic":true,"noprefixroute":true,"label":"eth0","valid_life_time":691103,"preferred_life_time":604703},{"family":"inet6","local":"fe80::d21:c163:5965:731e","prefixlen":64,"scope":"link","noprefixroute":true,"valid_life_time":4294967295,"preferred_life_time":4294967295}]},{"ifindex":3,"ifname":"wlan0","flags":["NO-CARRIER","BROADCAST","MULTICAST","UP"],"mtu":1500,"qdisc":"pfifo_fast","operstate":"DOWN","group":"default","txqlen":1000,"link_type":"ether","address":"dc:a6:32:96:56:c4","broadcast":"ff:ff:ff:ff:ff:ff","addr_info":[]}]'
    try:
        #print ('pass_string', pass_string)
        the_list = json.loads(pass_string)
        for index in range(0, len(the_list)):
            # the_list[1].keys()
            # [u'addr_info', u'operstate', u'qdisc', u'group', u'mtu', u'broadcast', u'flags', u'address', u'ifindex', u'txqlen', u'ifname', u'link_type']
            #print ("ifname", the_list[index]['ifname'])
            if the_list[index]['ifname'] == portName:
                return_value = the_list[index]['address']
    except:
        pass

    return return_value

# ----------------------------
def wifi_config_from_content(content):
# ----------------------------
    return_value = {}

    if content:
        splits = content.split(",")
        return_value['ssid'] = splits[0]

        if len(splits) > 1:
            return_value['psk'] = splits[1]

    return return_value

# ----------------------------
def save_shared_counts(the_counts):
# ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_network_' + the_count
            do_atomic_write_if_different(file_name, str(the_counts[the_count]))
        except:
            pass

# ----------------------------
def get_ping_from_result(input_string):
# ----------------------------
    return_value = ''

    for line in input_string.split('\n'):
        if 'time=' in line:
            return_value = line.split('time=')[1]

    return return_value


# ----------------------------
def get_speed_from_upload_result(input_string):
# ----------------------------
    return_value = ''

    splits = input_string.split()
    for index_count in range(1, len(splits)):
        if 'MBps' in splits[index_count]:
            return_value = splits[index_count-1] + ' MB/s'

    return return_value

# ----------------------------
def get_parens_items(input_string):
# ----------------------------
    return_value = []

    if ('(' in input_string):
        location = input_string.find('(')
        for first_split in input_string[location:].split('('):
            if ')' in first_split:
                return_value.append(first_split.split(')')[0])

    return return_value

# ----------------------------
def get_speed_from_download_result(fail_string):
# ----------------------------
    return_value = '(no download speed)'

    for the_option in get_parens_items(fail_string):
        if ' ' in the_option:
            return_value = the_option

    return return_value

# ----------------------------
def make_future_path_string(current_path, key, value):
# ----------------------------
    future_path = copy.deepcopy(current_path)
    future_path[key] = value
    path_string = ''
    for item in future_path:
        if path_string:
            path_string += ','
        path_string += item + '=' + future_path[item]
    return path_string

# ----------------------------
def set_wlan_tx_power(wlan, the_string_value):
# ----------------------------
    content, fails = do_one_command('iw dev ' + wlan + ' set txpower fixed ' + the_string_value)

# ----------------------------
def get_wifi_info(wlan):
# ----------------------------
    content, fails = do_one_command('iw dev ' + wlan + ' info')

    return content

# ----------------------------
def get_wifi_tx_power(content):
# ----------------------------
    return_value = ''

    for line in content.replace('\t','').split('\n'):
        if 'txpower' in line:
            try:
                return_value = line.split()[1] + ' ' + line.split()[2]
            except:
                pass

    return return_value

# ----------------------------
def get_wifi_results(wifi_list):
# ----------------------------
    return_value = {}

    # sudo nmcli -c no -t -f ssid,bssid,signal,in-use dev wifi
    list_to_use = wifi_list.replace('\:','<colon>')

    for line_item in list_to_use.split('\n'):
        splits = line_item.split(':')
        if len(splits) > 0:
            ssid = splits[0].replace('<colon>',':')
            if ssid:
                if not ssid in return_value:
                    return_value[ssid] = {}

                if len(splits) > 5:
                    bssid = splits[1].replace('<colon>',':')
                    in_use = splits[2].replace('<colon>',':')
                    channel = splits[3].replace('<colon>',':')
                    freq = splits[4].replace('<colon>',':')
                    signal = splits[5].replace('<colon>',':')
                    rate = splits[6].replace('<colon>',':')

                    if bssid in return_value[ssid]:
                        if '*' in return_value[ssid][bssid]['in_use']:
                            pass
                            # let it ride
                        else:
                            return_value[ssid][bssid] = {'in_use':in_use, 'channel':channel, 'freq':freq, 'signal':signal, 'rate':rate}
                    else:
                        return_value[ssid][bssid] = {'in_use':in_use, 'channel':channel, 'freq':freq, 'signal':signal, 'rate':rate}

    return return_value

# ----------------------------
def get_access_points_for_ssid(wifi_state, ssid):
# ----------------------------
    return_value = []
    if ssid in wifi_state:
        for bssid in wifi_state[ssid]:
            return_value.append((wifi_state[ssid][bssid]['in_use'], wifi_state[ssid][bssid]['channel'], wifi_state[ssid][bssid]['signal']))

    return return_value

# ----------------------------
def get_my_wifi_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_connect"
    serial = get_serial()

    setting = 'corp'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_connect"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting

# ----------------------------
def get_my_coaster_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_coaster"
    serial = get_serial()

    setting = '0'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_coaster"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting


# ----------------------------
def get_my_hopper_setting():
# ----------------------------
    input_file = "/cardinal/localhtml/conf_wifi_hopper"
    serial = get_serial()

    setting = 'no-hop'
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    input_file = "/cardinal/localhtml/dev_wifi_hopper"
    try:
        with open(input_file, 'r') as f:
            settings_full = json.loads(f.read())
        if serial in settings_full:
            setting = copy.deepcopy(settings_full[serial])
    except:
        print(traceback.format_exc())

    return setting

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")
    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)

# ----------------------------
def do_atomic_write_if_different(output_file, content):
# ----------------------------
    did_write = False

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        # flush all to disk
#        os.sync()

        shutil.move(temp_name, output_file)

        did_write = True

    return did_write

# ----------------------------
def write_lan_detail(content):
# ----------------------------
    do_atomic_write_if_different('/dev/shm/pi_network_detail.txt', content)


# ----------------------------
def restart_network_manager():
# ----------------------------
    global s_lan_worst_restarts

    is_working = True


    count_how_many = 0
    while is_working:
        count_how_many += 1
        set_wifi_command = 'sudo systemctl restart NetworkManager'
        pass_string, fails = do_one_command(set_wifi_command)

        print ('pass:' + pass_string)
        print ('other:' + fails)

        if fails:
            is_working = True
        else:
            is_working = False
            time.sleep(0.10)

    if count_how_many > s_lan_worst_restarts:
        s_lan_worst_restarts = count_how_many

    print ('s_lan_worst_restarts', s_lan_worst_restarts)

# ----------------------------
def manage_network_connections():
# ----------------------------
    global s_wifi_X_removed_count, s_bssid_last, s_bssid_change_count
    global s_lan_detail_counts, s_items_deleted_count, s_passes_not_connected
    global s_usb_reset_radio_count, s_lan_detail_string
    global s_corp_wifi_cert_in_use, s_corp_wifi_cert_rotation_count, s_corp_wifi_cert_just_loaded_in_use
    global s_corp_wifi_cert_retry_count
    global s_corp_connect_attempts
    global s_allow_wifi_config_previous, s_allow_wifi_config_connection
    global s_pass_count, s_time_of_last_pop
    global s_to_use_corp_wifi_certs_path

    current_uptime = uptime()

    try:
        content = open('/cardinal/wifi_config.txt').read()
        s_allow_wifi_config_connection = wifi_config_from_content(content)
    except:
        s_allow_wifi_config_connection = {}

    if s_allow_wifi_config_connection != s_allow_wifi_config_previous:
        s_allow_wifi_config_previous = copy.copy(s_allow_wifi_config_connection)
        # make it drop all wifi, and start again
        s_pass_count = 0
        current_uptime = 1

    time_start = time.time()

    debug_string = ''

    the_counts = {'signal':0}
    _ = """
wireless:
nmcli -t c s
level2-ng:7d687a6d-51b5-4660-9fb1-c69d2f2eebdd:802-11-wireless:wlan0
corp0:b0fe05cf-8250-48e5-a4cc-d9868a75d759:802-11-wireless:
Wired connection 1:8898c2db-ba58-36ac-9f64-7b9e4b6c142e:802-3-ethernet:

wired:
nmcli -t c s
Wired connection 1:467d59c0-89e2-3f98-9b93-6f8c24941aca:802-3-ethernet:eth0
corp0:5bafe10a-d110-41aa-af52-1ec174cab11e:802-11-wireless:

    """

    try:
        coaster = get_my_coaster_setting()

        content = """[main]
plugins=ifupdown,keyfile

[ifupdown]
managed=false"""

        if coaster != '0':
            content = """[main]
ignore-carrier=""" + coaster + """
plugins=ifupdown,keyfile

[ifupdown]
managed=false"""

        did_change_content = do_atomic_write_if_different('/etc/NetworkManager/NetworkManager.conf', content)

        if did_change_content:
            restart_network_manager()

    except:
        pass

    try:
        s_pass_count += 1
        my_signal = 0

        time_start = time.time()

        devices_report, fails = do_one_command('nmcli -t')

        #debug_string += devices_report

        # new as of 2022.09.19
        wifi_mac_address_external = ''
        wifi_mapping = get_wifi_mapping(devices_report)
        if wifi_mapping['external_wifi']:
            wireless_to_configure = wireless_to_configure_from_wlanX(wifi_mapping['external_wifi'])
            wireless_lan_external_number = wireless_to_configure_from_wlanX(wifi_mapping['external_wifi'])
            wifi_mac_address_external = macAddress('wlan' + wireless_lan_external_number)
        else:
            wireless_to_configure = wireless_to_configure_from_wlanX(wifi_mapping['internal_wifi'])
        wireless_lan_internal_number = wireless_to_configure_from_wlanX(wifi_mapping['internal_wifi'])
        wifi_mac_address_to_use = macAddress('wlan' + wireless_lan_internal_number)

        #debug_string += " ?? " + json.dumps(wifi_mapping)

        connection_list, fails = do_one_command('nmcli -t c s')
        print ("scan:", connection_list, fails)

        #debug_string += " ?? " + connection_list

        list_to_remove = []
        is_wired = False
        is_wifi_configured = False
        is_iot_selected = (get_my_wifi_setting() == 'iot')
        is_hopper_enabled = (get_my_hopper_setting() == 'hop')

        if is_iot_selected:
            ssid_name = 'cah-iot'
        else:
            ssid_name = 'corp'

        splits = connection_list.split('\n')

        for item in splits:
            parts = item.split(':')
            if len(parts) > 3:
                connection_name = parts[0]
                connection_type = parts[2]
                connection_port = parts[3]

                if ('Wired' in connection_name) or ('ethernet' in connection_type):
                    if connection_port:
                        is_wired = True
                        print ("Is Wired")
                    else:
                        # no wire plugged in, remove this open item
                        result, fails = do_one_command("sudo nmcli con delete \"" + connection_name + "\"")
                        print ("delete connection:", connection_name, result, fails)


        found_wifi_config = False

        for item in splits:
            parts = item.split(':')
            if len(parts) > 3:
                connection_name = parts[0]
                connection_type = parts[2]
                connection_port = parts[3]

                if 'wireless' in connection_type:
                    is_early_since_boot = False
                    if current_uptime:
                        if current_uptime < 120:
                            is_early_since_boot = True
                    if is_wired or (is_early_since_boot and (s_pass_count == 1)) : # on first pass, start from wireless scratch (to let a new cert apply, specifically)
                        # remove all wireless
                        list_to_remove.append(connection_name)
                        s_items_deleted_count += 1
                    else:
                        # remove all wireless but corp and cah-iot
                        remove_it = True

                        if is_iot_selected:
                            if 'cah-iot' + wireless_to_configure in connection_name:
                                remove_it = False
                        else:
                            if 'corp' + wireless_to_configure in connection_name:
                                remove_it = False

                        # be able to transition to the other adapter
                        if connection_port:
                            if not 'wlan' + wireless_to_configure in connection_port:
                                remove_it = True
                                s_wifi_X_removed_count += 1

                        # check that it got unplugged and replugged (external)
                        if not connection_port:
                            s_passes_not_connected += 1
                            if s_passes_not_connected > 20 / s_seconds_per_manage_pass:
                                remove_it = True

                        # check that external has correct mac
                        if ssid_name == 'cah-iot':
                            if 'wlan' + wireless_to_configure in connection_port:
                                if wifi_mac_address_external:
                                    if wifi_mac_address_external != wifi_mac_address_to_use:
                                        remove_it = True

                        if remove_it:
                            s_passes_not_connected = 0
                            list_to_remove.append(connection_name)
                            s_items_deleted_count += 1
                        else:
                            is_wifi_configured = True
                            print ("Is wifi Configured")

        for connection_name in list_to_remove:
            result, fails = do_one_command("sudo nmcli con delete \"" + connection_name + "\"")
            print ("delete connection:", connection_name, result, fails)

        wifi_list = get_wifi_list()
        do_atomic_write_if_different('/dev/shm/pi_network_wifi_list', wifi_list)

        # if not on wired, then add the allowed wifi networks
        if is_wired:
            pass

        else:
            if s_allow_wifi_config_connection:
                ssid_name = s_allow_wifi_config_connection['ssid']

            if not is_wifi_configured:
                wifi_note = ''

                set_wifi_command = ''
                set_wifi_command_followup = ''

                if wireless_to_configure != wireless_lan_internal_number:
                    # is on external
                    # try to reset it, to get a clean start each time
                    content, fails = do_one_command('usbreset')
                    device_to_reset = find_external_radio_from_usbreset(content)
                    if device_to_reset:
                        content, fails = do_one_command('usbreset ' + device_to_reset)
                        s_usb_reset_radio_count += 1
                        save_shared_counts({'ext_radio_resets':s_usb_reset_radio_count})

                if ssid_name == 'cah-iot':
                    s_corp_wifi_cert_in_use = ''
                    s_corp_wifi_cert_just_loaded_in_use = False
                    preshare = rot13c(s_crypt_cahiot_preshare)
                    set_wifi_command = 'sudo nmcli connection add type wifi con-name "cah-iot' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "' + preshare + '"'
                    if wifi_mac_address_to_use and wireless_to_configure != wireless_lan_internal_number:
                        # is on external, clone the mac from the internal
                        set_wifi_command_followup = 'sudo nmcli connection modify --temporary "cah-iot' + wireless_to_configure + '"  802-11-wireless.cloned-mac-address ' + wifi_mac_address_to_use + ' && sudo nmcli con up cah-iot' + wireless_to_configure
                    else:
                        set_wifi_command_followup = 'sudo nmcli con up cah-iot' + wireless_to_configure

                # check for organizational provided certs
                try:
                    certs_in_2 = os.listdir(s_corp_wifi_certs_2_path)
                except:
                    certs_in_2 = []

                if certs_in_2:
                    # the provided certs
                    s_to_use_corp_wifi_certs_path = s_corp_wifi_certs_2_path
                else:
                    # my own certs
                    s_to_use_corp_wifi_certs_path = s_corp_wifi_certs_path

                if ssid_name == 'corp':
                    corp_wifi_certs_list = sorted(os.listdir(s_to_use_corp_wifi_certs_path), reverse=True)
                    use_new_cert = False

                    if s_corp_wifi_cert_in_use:
                        if not s_corp_wifi_cert_in_use in corp_wifi_certs_list:
                            use_new_cert = True
                    else:
                        use_new_cert = True

                    if s_corp_wifi_cert_just_loaded_in_use:
                        pass
                        # wipe the saved setting, and try the list from the 'start',
                        #    in an effort to use the one with the most life in it
                        s_corp_wifi_cert_in_use = ''
                        use_new_cert = True
                        wifi_note = 'scratch'
                    else:
                        # on low number of disconnects, retry existing cert (make it sticky)
                        if (s_corp_wifi_cert_retry_count < 1) and (s_corp_wifi_cert_in_use):
                            s_corp_wifi_cert_retry_count += 1
                            wifi_note = 'bounce'
                        else:
                            s_corp_wifi_cert_retry_count = 0
                            use_new_cert = True
                            wifi_note = 'start ' + str(s_corp_wifi_cert_in_use)

                    if use_new_cert:
                        # get the next cert
                        s_corp_wifi_cert_in_use = get_next_corp_wifi_to_try(s_corp_wifi_cert_in_use, corp_wifi_certs_list)
                        s_corp_connect_attempts.append(s_corp_wifi_cert_in_use + ' ' + str(s_corp_wifi_cert_rotation_count))
                        s_corp_wifi_cert_just_loaded_in_use = False
                        s_corp_wifi_cert_rotation_count += 1

                    password = open(s_to_use_corp_wifi_certs_path + s_corp_wifi_cert_in_use + '/TempCert.pass','r').read()
                    cert_file = s_to_use_corp_wifi_certs_path + s_corp_wifi_cert_in_use + '/TempCert.p12'

                    set_wifi_command = """sudo nmcli connection add type wifi con-name "corp""" + wireless_to_configure + """\" ifname wlan""" + wireless_to_configure + ' ssid "' + ssid_name + '" -- wifi-sec.key-mgmt wpa-eap 802-1x.eap tls 802-11-wireless.hidden yes 802-1x.identity "<EMAIL>" 802-1x.private-key-password "' + password + '" 802-1x.private-key ' + cert_file

#                    if True:
#                        set_wifi_command = """sudo nmcli connection add type wifi con-name "corp""" + wireless_to_configure + """\" ifname wlan""" + wireless_to_configure + ' ssid "' + ssid_name + '1" -- wifi-sec.key-mgmt wpa-eap 802-1x.eap tls 802-11-wireless.hidden yes 802-1x.identity "<EMAIL>" 802-1x.private-key-password "' + password + '" 802-1x.private-key ' + cert_file

                    set_wifi_command_followup = 'sudo nmcli con up corp' + wireless_to_configure

                if s_allow_wifi_config_connection: # for testing on other networks, but still name the connection 'corp'
                    s_corp_wifi_cert_in_use = ''
                    s_corp_wifi_cert_just_loaded_in_use = False
                    ssid_name == s_allow_wifi_config_connection['ssid']
                    if 'psk' in s_allow_wifi_config_connection:
                        if s_allow_wifi_config_connection['psk']:
                            preshare = s_allow_wifi_config_connection['psk']
                            set_wifi_command = 'sudo nmcli connection add type wifi con-name "corp' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "' + preshare + '"'
                    else:
                        set_wifi_command = 'sudo nmcli connection add type wifi con-name "corp' + wireless_to_configure + '" ifname wlan' + wireless_to_configure + ' ssid "' + ssid_name + '"'
                    set_wifi_command_followup = 'sudo nmcli con up corp' + wireless_to_configure

                do_atomic_write_if_different(s_corp_wifi_cert_last_used_path + 's_corp_wifi_cert_in_use', s_corp_wifi_cert_in_use)

                if set_wifi_command:

                    pass_string, fails = do_one_command(set_wifi_command)
                    s_lan_detail_counts += 1
                    s_lan_detail_string = ''
                    s_lan_detail_string += set_wifi_command
                    s_lan_detail_string += ' lan_connect(' + wifi_note + '): ' + str(s_lan_worst_restarts) + ' -> ' + str(s_lan_detail_counts) + ' pass: ' + pass_string + ', other: ' + fails

#                    write_lan_detail(s_lan_detail_string)
                    if set_wifi_command_followup:
                        #time.sleep(10)  # experiment with no sleep
                        for cmdline in set_wifi_command_followup.split('&&'):
                            pass_string, fails = do_one_command(cmdline)

                    restart_network_manager()
                    do_one_command('/sbin/iwconfig wlan0 power off') # turn off the power save feature, so that it does not disable
                    do_one_command('/sbin/iwconfig wlan1 power off') # turn off the power save feature, so that it does not disable

                    _ = """
sudo ip link set wlan0 down
sudo ip link set wlan0 up

sudo ip link set wlan1 down
sudo ip link set wlan1 up

RTNETLINK answers: No such device



                    """

            # do wifi watching
            results_d = get_wifi_results(wifi_list)
            if ssid_name in results_d:
                mine = ''
                my_signal = 0
                strongest_bssid = ''
                max_strength = 0
                for bssid in results_d[ssid_name]:
                    try:
                        if int(results_d[ssid_name][bssid]['signal']) > max_strength:
                            max_strength = int(results_d[ssid_name][bssid]['signal'])
                            strongest_bssid = bssid
                    except:
                        pass

                    if results_d[ssid_name][bssid]['in_use'] == '*':
                        mine = bssid

                        if mine != s_bssid_last:
                            s_bssid_last = mine
                            s_bssid_change_count += 1
                        try:
                            my_signal = int(results_d[ssid_name][bssid]['signal'])
                            the_counts['signal'] = my_signal
                        except:
                            pass

                # seconds, delta required
                pop_schedule = [[0,100],[10,50],[20,25],[30,15]]
                pop_if_mine_below =  45
                time_now = time.time()
                time_since = time_now - s_time_of_last_pop
                signal_delta_needed = 100
                for pair in pop_schedule:
                    if time_since > pair[0]:
                        signal_delta_needed = pair[1]

                changed_to_channel = ''
                if is_hopper_enabled:
                    do_the_pop = False
                    if max_strength > my_signal + signal_delta_needed:
                        do_the_pop = True
                    if my_signal < pop_if_mine_below:
                        do_the_pop = True

                    if do_the_pop:
                        command = 'wpa_cli -i wlan' + wireless_to_configure + ' bssid 0 ' + strongest_bssid
                        pass_string, fail_string = do_one_command(command)

                        command = 'wpa_cli -i wlan' + wireless_to_configure + ' reassociate'
                        pass_string2, fail_string2 = do_one_command(command)

                        s_time_of_last_pop = time_now
                        changed_to_channel = results_d[ssid_name][strongest_bssid]['channel']

                time_to_here = time.time() - time_start

        debug_string += s_lan_detail_string
        debug_string += " ?? deleted " + str(s_items_deleted_count)
        debug_string += " ?? not connected " + str(s_passes_not_connected)
        debug_string += " ?? radio resets " + str(s_usb_reset_radio_count)
        debug_string += " ?? cert " + str(s_corp_wifi_cert_in_use)
        debug_string += " ?? cert rotation " + str(s_corp_wifi_cert_rotation_count)
        debug_string += " ?? cert retry " + str(s_corp_wifi_cert_retry_count)
        debug_string += " ?? attempts " + str(s_corp_connect_attempts)
        debug_string += " ?? current_uptime " + str(current_uptime)


        if len(s_corp_connect_attempts) > 10:
            s_corp_connect_attempts.pop(0)

        time_end = time.time()
        debug_string += " ?? process time " + str(time_end - time_start)

        write_lan_detail(debug_string)

        the_counts['signal'] = my_signal
        the_counts['corp_cert'] = s_corp_wifi_cert_in_use

        save_shared_counts(the_counts)

    except:
        trace_back_string = traceback.format_exc()
        do_atomic_write_if_different('/dev/shm/network_notes_wpa_exception.txt', trace_back_string)
        write_lan_detail(trace_back_string)


    # Check priority:
    # https://askubuntu.com/questions/165679/how-to-manage-available-wireless-network-priority
    # nmcli -f autoconnect-priority,name c

# ----------------------------
def write_debug_string(debug_string):
# ----------------------------
    debug_name = '/dev/shm/pi_network_manage_debug.txt' # leaving this on will take up space on the second screen on the pi
    debug_name = '/dev/shm/pi_network_manage_notes.txt'

    if True:
        try:
            do_atomic_write_if_different(debug_name, debug_string)
        except:
            pass

# ----------------------------
def get_wifi_scan():
# ----------------------------
    global s_forced_rescan_count
    results = {}

    # if we are wired, then there is no wifi to report
    results['ssid'] = '(not_on_wifi)'
    results['chan'] = ''
    results['signal'] = ''
    results['cah-iot'] = 'No'
    results['lan'] = 'dne'
    results['nic'] = 'int'
    results['corp_cert'] = s_corp_wifi_cert_in_use

    connection_list, fails = do_one_command('nmcli -t c s')
    #corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0
    #Wired connection 1:1acbc619-206d-3148-9ca9-50e49677f612:802-3-ethernet:
    splits = connection_list.split('\n')
    for item in splits:
        parts = item.split(':')
        if len(parts) > 3:
            if parts[3]:
                results['lan'] = parts[3]
                devices_report, fails = do_one_command('nmcli -t')
                wifi_mapping = get_wifi_mapping(devices_report)
                if results['lan'] == wifi_mapping['external_wifi']:
                    results['nic'] = 'ext'

    # do a scan, and force it to actually do the scan, not just return from cache
    if s_force_rescan:
        s_forced_rescan_count += 1
        result, fails = do_one_command('nmcli -c no device wifi list --rescan yes') # This is what gives it the chance to change channels (aggressive mode)
    else:
        result, fails = do_one_command('nmcli -c no device wifi list') # rescan causes a high power burst... lets avoid that

    for line in result.split('\n'):
        if line:
            if 'cah-iot' in line:
                results['cah-iot'] = 'Yes'
            is_the_inuse = False
            if line[0] == '*':
                is_the_inuse = True
                line = ' ' + line[1:]

            splits = line.split()
            #print (splits)

            if is_the_inuse:
                if len(splits) > 2:
                    results['ssid'] = splits[0]
                    results['chan'] = splits[2]
                if len(splits) > 5:
                    results['signal'] = splits[5]

    return results

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def load_wifi_cert():
# ----------------------------
    global s_corp_wifi_cert_in_use, s_corp_wifi_cert_just_loaded_in_use
    _ = """
2022.10.17

Windows: (Fails to make private key exportable... something about policies?)
Make a new (fresh) cert:
Remote desktop to DW6888665558828
FA-OH085.NPSIpad
GrsROYIW3WLVAWb

te-CAH-RPI-User-TEMP-DO-NOT-USE-KF-93b44b28-44cd-4dea-aeb0-95cf12524ff5
or
OH085, NPSIpad

Start menu, control panel, search, hidden, File Explorer Options, Hidden Files and Folders, Show.

Explore, OH085, NPSIpad/AppData/Local/Microsoft/Credentials
right click, general, uncheck Read-Only, apply



Start menu, certmgr.msc, (do not open as control panel, use Microsoft Console Document)
Certificates, Personal, Certificates, "OH085, NPSIpad"
Right click, All Tasks, Advanced Operations, Renew This Certificate with the Same Key...
Next, Enroll, (Status: Succeeded), Finish
Right click (again), All Tasks, Export...
Next, (no private) Next, DER encoded, Next, save to
downloads/20221017a.cer
Finish

Open Teams, new message, attach the cer file.

Receive the file, and save in downloads folder on Mac.
Double click to open the file in keychain.
Find the cert in the list, right click, export,



Linux direct:
https://geekdudes.wordpress.com/2020/02/14/request-ssl-certificate-for-linux-machine-from-microsoft-certification-authority/









2022.01.26
back documenting the current wifi cert:

Account name: FA-OH085.NPSIpad

From the command that makes the wifi connection:
802-1x.identity "<EMAIL>"
802-1x.private-key-password "NgsA7cR3fqf?qnpA"
802-1x.private-key /cardinal/TempCert.p12

To see contents:
openssl pkcs12 -info  -in TempCert.p12
(enter the private key password from the connection line)
[It shows some details, then asks for "Enter PEM pass phrase:",
use the one from dwf rawNotes.docx file, search for "tag:dwf20220126a"]
(enter password)

    # made with "xxd -p TempCert2.p12" 2022.03.01 account name "FA-OH085.NPSIpad"

    # made by Bryan Runnels on March 1, 2022, expires...
    # cd "/Users/<USER>/OneDrive - Cardinal Health/RaspberryPi/certAsOf20220301"
    # rename pfx to p12
#    # openssl pkcs12 -in raspberrycert.pfx -nocerts -out private.key -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb
#    # openssl pkcs12 -export -out raspberrycert.p12 -inkey private.key -in certificate.cer -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb

check contents:
openssl pkcs12 -in /cardinal/TempCert.p12

Check expiration:
openssl pkcs12 -in /cardinal/TempCert.p12 -nokeys -passin pass:GrsROYIW3WLVAWb | openssl x509 -noout -enddate

then paste the one password for input and output
made with: xxd -p OH085NPSIpadFullchain.p12

Can we remove the passphrase requirement?
https://blog.armbruster-it.de/2010/03/remove-the-passphrase-from-a-pkcs12-certificate/

cd /cardinal/
sudo openssl pkcs12 -in /cardinal/TempCert.p12 -nodes -out temp.pem
sudo openssl pkcs12 -export -in temp.pem -out junk.p12
rm temp.pem
sudo chmod +r junk.p12

openssl pkcs12 -in /cardinal/junk.p12 -nokeys -passin pass: | openssl x509 -noout -enddate

    """

    certs_path = s_corp_wifi_certs_path

    cert_contents = {}

    cert_contents_not_to_use = {}

    # old, expired cert, as a test
    cert_contents_not_to_use['20220401a'] = {
        'password':'NgsA7cR3fqf?qnpA',
        'content':"""30821cec02010330821ca806092a864886f70d010701a0821c9904821c95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"""}

    cert_contents['20230401a'] = {
        'password':'GrsROYIW3WLVAWb',
        'content':"""30821eda02010330821e8606092a864886f70d010701a0821e7704821e73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"""
}

    try:
        s_corp_wifi_cert_in_use = open(s_corp_wifi_cert_last_used_path + 's_corp_wifi_cert_in_use', 'r').read()
        s_corp_wifi_cert_just_loaded_in_use = True
    except:
        pass

    # start clean each time
    pass_string, fails = do_one_command('sudo rm -rf ' + certs_path)

    for cert_name in cert_contents.keys():
        output_file = certs_path + cert_name + "/TempCert.pass"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(cert_contents[cert_name]['password'])


        output_file = certs_path + cert_name + "/TempCert.xxd"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(cert_contents[cert_name]['content'])

        # make back into p12 file
        content = """
    #!/usr/bin/env sh
    cat """ + output_file + """ | xxd -p -r >""" + output_file.replace('.xxd', '.p12') + """
    sudo rm """ + output_file + """
        """
        output_file = "/cardinal/pi_network_tempscript"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_network_tempscript')
        pass_string, fails = do_one_command('sudo /cardinal/pi_network_tempscript')

        # clean up
        pass_string, fails = do_one_command('sudo rm /cardinal/pi_network_tempscript')







    # old code:
    if False:
        output_file = "/cardinal/TempCert.xxd"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        # make back into p12 file
        content = """
    #!/usr/bin/env sh
    cat /cardinal/TempCert.xxd | xxd -p -r >/cardinal/TempCert.p12
    sudo rm /cardinal/TempCert.xxd
        """
        output_file = "/cardinal/pi_network_tempscript"
        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))
        with open(output_file, 'w') as f:
            f.write(content)

        pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_network_tempscript')
        pass_string, fails = do_one_command('sudo /cardinal/pi_network_tempscript')

        # clean up
        pass_string, fails = do_one_command('sudo rm /cardinal/pi_network_tempscript')

# ====================================
def rot13b(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz9876543210ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    trans = chars[13:]+chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ====================================
def rot13c(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz9876543210ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    trans = chars[int(len(chars)/2):]+chars[:int(len(chars)/2)]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ----------------------------
def do_one_time():
# ----------------------------
    load_wifi_cert()


# ----------------------------
def call_home_locations():
# ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'",'"'))
    except:
        pass

    return response

# ----------------------------
def build_get_page_body(the_path='/', results_d={}, wlan0_info='', wlan_active='wlan0'):
# ----------------------------
    # localhost:7040, gives the_path = '/'
    # localhost:7040/?set=1234, gives the_path = '/?set=1234'
    global s_http_page_count, s_http_previous_path
    s_http_page_count += 1

    split_path = {}
    for item in the_path.replace('/','').replace('?','').split(','):
        try:
            split_path[item.split('=')[0]] = item.split('=')[1]
        except:
            pass

    lines_of_data = {}
    signal_thresholds = [95,90,85,80,75,70,65,60,55,50,45,40,35,30,25,20,15,10]

    for item in sorted(results_d):
        lines_of_data[item] = {}
        for signal_threshold in signal_thresholds:
            lines_of_data[item][signal_threshold] = ''

        for bssid in results_d[item]:
            signal = results_d[item][bssid]['signal']
            ap_name = ''
            if bssid.upper() in s_bssid_to_ap:
                ap_name = s_bssid_to_ap[bssid]

            try:
                column = signal_thresholds[-1]
                for threshold_to_test in sorted(signal_thresholds):
                    if int(signal) >= threshold_to_test:
                        column = threshold_to_test

                if lines_of_data[item][column]:
                    lines_of_data[item][column] += '<br>'

                # test
                #ap_name = 'test:001'

                if ap_name:
                    # show ap name
                    ap_name_to_show = ap_name
                    if ':' in ap_name:
                        ap_name_to_show = ap_name.split(':')[1]
                    lines_of_data[item][column] += '<B>' + ap_name_to_show + results_d[item][bssid]['in_use'].replace(' ','&nbsp') + '</B>'
                    lines_of_data[item][column] += '<br>'

                # show channel number
                if int(results_d[item][bssid]['channel']) < 32: # 2.4 GHz band
                    lines_of_data[item][column] += '<i>' + '{:03d}'.format(int(results_d[item][bssid]['channel'])) + results_d[item][bssid]['in_use'].replace(' ','&nbsp') + '</i>'
                else:
                    lines_of_data[item][column] += '{:03d}'.format(int(results_d[item][bssid]['channel'])) + results_d[item][bssid]['in_use'].replace(' ','&nbsp')
            except:
                pass

    body = ''

    refresh_time = 1
#    if s_run_speed_tests:
#        refresh_time = 5

    body += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

    # the link home does not work... because it is a file that get tagged as 'accessed', not actually loaded?
#    body += '<B><a href="file:///cardinal/localhtml/index.html" style="text-decoration:none;color:inherit">Home</a></B>'

    # only works if you don't click on any of the internal links, and "open another window in the current context"
#    body += '<B><a href="JavaScript:window.close()" style="text-decoration:none;color:inherit">Close</a></B>'

#    body += '<B><a href="#" onclick="close_window();return false;">close</a></B>'

#    body += """<a href="blablabla" onclick="setTimeout(function(){var ww = window.open(window.location, '_self'); ww.close(); }, 1000);">If you click on this the window will be closed after 1000ms</a>"""


    body += '<br>'

    body += '<center>'
    body += '<table border="1" cellpadding="5">'
    body += '<tr>'
    body += '<td>'
    body += '<B>wlan0Tx</B>'
    body += '</td>'
    body += '<td>'
    body += get_wifi_tx_power(wlan0_info)
    body += '</td>'

    if s_is_engineering_device:
        for power_level in ['31.00', '20.00', '10.00', '5.00', '4.00', '3.00', '2.00', '1.00']:
            body += '<td>'
            power_string = power_level.replace('.','')
            path_string = make_future_path_string(split_path, 'wlan0power', power_string)
            body += ' ' + '<a href="?' + path_string + '">' + power_level + '</a>'
        body += '</td>'
    body += '</tr>'


    body += '<tr>'
    body += '<td>'
    body += '<B>force rescan on</B>'
    body += '</td>'
    body += '<td>'
    body += str(s_force_rescan)
    body += '</td>'

    if s_is_engineering_device:
        body += '<td>'
        if s_force_rescan:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'force_rescan', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'

        body += '<td>'
        if s_force_rescan:
            path_string = make_future_path_string(split_path, 'force_rescan', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'

    body += '<td>'
    body += str(s_forced_rescan_count)
    body += '</td>'

    body += '</tr>'



    is_hopper_enabled = (get_my_hopper_setting() == 'hop')

    body += '<tr>'
    body += '<td>'
    body += '<B>hopper on</B>'
    body += '</td>'
    body += '<td>'
    body += str(is_hopper_enabled)
    body += '</td>'

    if s_is_engineering_device:
        body += '<td>'
        if is_hopper_enabled:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'pop_to_top_signal_channel', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if is_hopper_enabled:
            path_string = make_future_path_string(split_path, 'pop_to_top_signal_channel', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'
    body += '<td>'
    body += '<B>Coaster</B>'
    body += '</td>'
    body += '<td>'
    body += str(get_my_coaster_setting())
    body += '</td>'

    body += '</tr>'



    if s_is_engineering_device:
        body += '<tr>'
        body += '<td>'
        body += '<B>ferg</B>'
        body += '</td>'
        body += '<td>'
        body += str(s_allow_wifi_config_connection)
        body += '</td>'

        body += '<td>'
        if s_allow_wifi_config_connection:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'allow_ferg_connection', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if s_allow_wifi_config_connection:
            path_string = make_future_path_string(split_path, 'allow_ferg_connection', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'
        body += '</tr>'

    if True:
        body += '<tr>'
        body += '<td>'
        body += '<B>run speed test</B>'
        body += '</td>'
        body += '<td>'
        body += str(s_run_speed_tests)
        body += '</td>'

        body += '<td>'
        if s_run_speed_tests:
            body += ' ' + 'True'
        else:
            path_string = make_future_path_string(split_path, 'speed_test', 'true')
            body += ' ' + '<a href="/?' + path_string + '">' + 'True' + '</a>'
        body += '</td>'
        body += '<td>'
        if s_run_speed_tests:
            path_string = make_future_path_string(split_path, 'speed_test', 'false')
            body += ' ' + '<a href="/?' + path_string + '">' + 'False' + '</a>'
        else:
            body += ' ' + 'False'
        body += '</td>'
        body += '</tr>'

    body += '<td>'
    body += str(s_http_page_count)
    body += '</td>'
    body += '</tr>'
    body += '</table>'

    body += '<br>'

    if False:
        body += 'prev: ' + s_http_previous_path
        body += '<br>'
        body += 'path: ' + the_path
        body += '<br>'
        s_http_previous_path = the_path
    body += '</center>'


    show_options = []
    show_options.append({'title':'corp_and_iot', 'list':['corp','cah-iot']})
    show_options.append({'title':'corp', 'list':['corp']})
    show_options.append({'title':'iot', 'list':['cah-iot']})
    show_options.append({'title':'all', 'list':[]})

    current_show = show_options[0]['title'] # take the first one as the default
    if 'show=' in the_path:
        current_show = the_path.split('show=')[1].split(',')[0]

    body += '<center>'
    body += 'WiFi Scan results ' + wlan_active + ' for ' + '<B>' + current_show + '</B>' + ': '

    show_list = []
    for show_option in show_options:
        option = show_option['title']
        if current_show == option:
            show_list = show_option['list']
        if option == current_show:
            body += '....[' + '' + option + '' + ']'
        else:
            path_string = make_future_path_string(split_path, 'show', option)
            body += '....[' + '<a href="/?' + path_string + '">' + option + '</a>' + ']'

    body += '<br><br>'
#    body += 'show_list: ' + str(show_list)
#    body += '<br><br>'

    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td title="channels less than 32 are 2.4GHz (italics); above that is 5GHz; in bold indicates the access point name">'
    body += '<B>ssid</B>'
    body += '</td>'
    for signal in signal_thresholds:
        body += '<td>'
        body += '{:03d}'.format(signal)
        body += '</td>'

    body += '</tr>'

    for item in sorted(results_d):
        if show_list:
            use_it = item in show_list
        else:
            use_it = True # empty list means to allow all

        if use_it:
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            color = '(0,255,0,0.25)'

            for signal in signal_thresholds:
                text = '<br>'.join(sorted(lines_of_data[item][signal].split('<br>')))
                if not text:
                    text = '&nbsp' * 4

                if '*' in text:
                    body += '<td style="background-color:rgba' + color + '">'
                else:
                    body += '<td>'
                body += text
                body += '</td>'
            body += '</tr>'
    body += '</table>'
    body += '</center>'

    body += '<center>'
    body += '<br>'

    try:
        # this next line fails on unit test on mac
        new_content = 'wlan0 ' + macAddress('wlan0') + '     wlan1 ' + macAddress('wlan1')
        body += new_content
    except:
        pass

    body += '<br>'

    try:
        body += open('/dev/shm/network_notes_wpa.txt', 'r').read().replace('\n','<br>')
    except:
        body += traceback.format_exc()

    if s_is_engineering_device:
        try:
            body += open('/dev/shm/pi_network_manage_notes.txt', 'r').read().replace('\n','<br>')
        except:
            #body += traceback.format_exc()
            pass

    body += '</center>'


    return body

# ----------------------------
def get_wifi_list():
# ----------------------------
    wifi_list, fail = do_one_command('sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi')
    return wifi_list

# ----------------------------
def build_and_start_manage_network_connections(dummy_arg):
# ----------------------------
    while True:
        try:
            manage_network_connections()
        except:
            open('/dev/shm/pi_network_daemon2_exception', 'w').write(traceback.format_exc())

        time_of_start_of_scan = time.time()
        while (abs(time_of_start_of_scan - time.time()) < s_seconds_per_manage_pass):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)

# ----------------------------
def build_and_start_web_server(port_number):
# ----------------------------
    # be a web server
    # https://stackoverflow.com/questions/23264569/python-3-x-basehttpserver-or-http-server

    class MyServer(BaseHTTPRequestHandler):
        def do_GET(self):
            global s_force_rescan, s_run_speed_tests, s_pop_to_top_signal_channel, s_allow_wifi_config_connection

            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            response = "<html><head><title>" + str(s_bssid_change_count) + ' ' + service + "</title></head><body>"

            if self.path == '/favicon.ico':
                pass
            else:
                try:
                    wifi_list = open('/dev/shm/pi_network_wifi_list', 'r').read()
                except:
                    wifi_list = ''
                results_d = get_wifi_results(wifi_list)
                wlan0_info = get_wifi_info('wlan0')

                path_splits = self.path.replace('/','').replace('?','').split(',')
                #self.wfile.write(bytes(str(path_splits) + '<br>', "utf-8"))
                for item in path_splits:
                    if 'force_rescan=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_force_rescan = False
                            if new_value == 'true':
                                s_force_rescan = True

                    if 'speed_test=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_run_speed_tests = False
                            if new_value == 'true':
                                s_run_speed_tests = True


#                    if 'allow_ferg_connection=' in item:
#                            new_value = item.split('=')[1]
#                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
#                            if new_value == 'false':
#                                s_allow_wifi_config_connection = False
#                            if new_value == 'true':
#                                s_allow_wifi_config_connection = True


                    if 'pop_to_top_signal_channel=' in item:
                            new_value = item.split('=')[1]
                            #self.wfile.write(bytes(item + ', ' + new_value, "utf-8"))
                            if new_value == 'false':
                                s_pop_to_top_signal_channel = False
                            if new_value == 'true':
                                s_pop_to_top_signal_channel = True

                    if 'wlan0power=' in item:
                        try:
                            old_power = get_wifi_tx_power(wlan0_info).replace('.','').split()[0]
                            new_power = item.split('=')[1]
                            if old_power != new_power:
                                set_wlan_tx_power('wlan0', new_power)
                                #self.wfile.write(bytes(old_power + ', ' + new_power, "utf-8"))
                                wlan0_info = get_wifi_info('wlan0')
                        except:
                            pass

                wlan_active = 'wlan0'
                devices_report, fails = do_one_command('nmcli -t')
                if 'wlan1' in devices_report:
                    wlan_active = 'wlan1'


                response += build_get_page_body(self.path, results_d, wlan0_info,wlan_active)
                self.wfile.write(bytes(response, "utf-8"))
                response = ''

                if s_run_speed_tests:
                    self.wfile.write(bytes("<br><center>running speed tests...</center>", "utf-8"))

                    content, fails = do_one_command('ping slicer.cardinalhealth.net -c 1')
                    self.wfile.write(bytes("<center> ping= " + get_ping_from_result(content) + "</center>", "utf-8"))

                    if False:
                        content, fails = do_one_command('wget slicer.cardinalhealth.net/speedtest?size=10000000 --timeout 25.0 -O testfile1')
                        download_speed = get_speed_from_download_result(fails)
                        self.wfile.write(bytes("<center> download= " + download_speed + "</center>", "utf-8"))

                        content, fails = do_one_command('curl -v -F filename=testfile --max-time 25 -F upload=@testfile1 https://slicer.cardinalhealth.net/speedtest')
                        upload_speed = get_speed_from_upload_result(content)
                        self.wfile.write(bytes("<center> upload= " + upload_speed + "</center>", "utf-8"))

            response += "</body></html>"

            self.wfile.write(bytes(response, "utf-8"))

    myServer = HTTPServer(("localhost", port_number), MyServer)

    try:
        myServer.serve_forever()
    except KeyboardInterrupt:
        pass

    myServer.server_close()

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_network.cpython-37.pyc", "/cardinal/pi_network.pyc")
    save_shared_counts({'ext_radio_resets':s_usb_reset_radio_count})

    print ('version=' + version)

    if os.path.isfile("/cardinal/pi_network.py"):
        os.remove("/cardinal/pi_network.py")

    do_one_time()

    daemon = True

    time_of_last_send = 0

    try:
        with open('/dev/shm/pi_network_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    last_channel = ''
    last_lan = ''
    serial = get_serial()

    daemon = threading.Thread(name='daemon_server',
                          target=build_and_start_web_server,
                          args=[7040])
    daemon.setDaemon(True) # Set as a daemon so it will be killed once the main thread is dead.
    daemon.start()

    daemon2 = threading.Thread(name='daemon_manage_network_connections',
                          target=build_and_start_manage_network_connections,
                          args=['1234'])
    daemon2.setDaemon(True) # Set as a daemon so it will be killed once the main thread is dead.
    daemon2.start()

    while True:
        time_of_start_of_scan = time.time()
        need_to_send = False

        if True:
            # this performs the side effect of forcing an actual rescan of the ssid/channel choice.
            results = get_wifi_scan()

            the_counts = {}
            the_counts['nic'] = results['nic']
            save_shared_counts(the_counts)

            # write to local store, for others to use (hmi)
            do_atomic_write_if_different('/dev/shm/pi_network_scan.txt', json.dumps(results))

            time_now = time.time()
            if abs(time_now - time_of_last_send) > 60 * 30: # 1800 seconds
                need_to_send = True

            if abs(time_now - time_of_last_send) > 60:
                if 'chan' in results:
                    if last_channel != results['chan']:
                        last_channel = results['chan']
                        need_to_send = True
                if 'lan' in results:
                    if last_lan != results['lan']:
                        last_lan = results['lan']
                        need_to_send = True

            the_data = []
            the_data.append('source=' + service)
            the_data.append('serial=' + serial)
            the_data.append('version=' + version)

            for key in results:
                the_data.append(key + "=" + str(results[key]))

            for call_home_location in call_home_locations():
                the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

                if need_to_send:
                    time_of_last_send = time_now


                    # check in with slicer
                    try:
                        r = requests.get(the_report_url, verify=False, timeout=15.0)
                        url_result = r.text

                    except:
                        url_result = 'exception'


                    # url_result = '{"bookmarks":{"10000000e3669edf":{"1": {"title": "EDHR", "url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login","whitelist":["edhr-na-jz.cardinalhealth.net"]}}}}'
                    try:
                        result_json = json.loads(url_result)

                    except:
                        print ("url_result was not a valid json")

                # save locally, for testing
                try:
                    with open('/dev/shm/pi_network_datadrop.txt', 'w') as f:
                        f.write(the_report_url)
                        f.write('\n\n' + str(url_result))
                    print (the_report_url)
                except:
                    print ("!!! failed to write datadrop string")

                    _ = """
look with:
watch cat /dev/shm/pi_network_datadrop.txt
                    """

            if False:
                try:
                    manage_network_connections()
                except:
                    pass



        if not (daemon and daemon2):
            break

        while (abs(time_of_start_of_scan - time.time()) < 4):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_wifi_list_parsing(self):
        """
        (fill in here)
        """

        wifi_list = ''
        self.assertEqual(get_wifi_results(wifi_list), {})

        # sudo nmcli -c no -t -f ssid,bssid,in-use,chan,freq,signal,rate dev wifi
        wifi_list = """cah-iot:34\:FC\:B9\:71\:0A\:D0: :52:5260 MHz:55:54 Mbit/s
byod:34\:FC\:B9\:71\:0A\:D2: :52:5260 MHz:55:54 Mbit/s
guest:34\:FC\:B9\:71\:0A\:D3: :52:5260 MHz:55:54 Mbit/s
corp:34\:FC\:B9\:71\:12\:31: :36:5180 MHz:52:540 Mbit/s
cah-iot:34\:FC\:B9\:71\:12\:30: :36:5180 MHz:52:54 Mbit/s
byod:34\:FC\:B9\:71\:12\:32: :36:5180 MHz:52:54 Mbit/s
guest:34\:FC\:B9\:71\:12\:33: :36:5180 MHz:52:54 Mbit/s
corp:34\:FC\:B9\:71\:0A\:D1:*:52:5260 MHz:52:540 Mbit/s
corp:34\:FC\:B9\:71\:0A\:D1: :52:5260 MHz:52:540 Mbit/s
corp:34\:FC\:B9\:71\:3C\:B1: :60:5300 MHz:30:540 Mbit/s
cah-iot:34\:FC\:B9\:71\:3C\:B0: :60:5300 MHz:30:54 Mbit/s
byod:34\:FC\:B9\:71\:3C\:B2: :60:5300 MHz:30:54 Mbit/s
guest:34\:FC\:B9\:71\:3C\:B3: :60:5300 MHz:29:54 Mbit/s
corp:34\:FC\:B9\:71\:03\:D1: :132:5660 MHz:25:540 Mbit/s
Hotspot10EE:22\:AD\:56\:24\:10\:EE: :6:2437 MHz:24:65 Mbit/s
corp:34\:FC\:B9\:71\:13\:51: :44:5220 MHz:24:540 Mbit/s
guest:34\:FC\:B9\:71\:13\:53: :44:5220 MHz:24:54 Mbit/s
cah-iot:34\:FC\:B9\:71\:13\:50: :44:5220 MHz:24:54 Mbit/s
byod:34\:FC\:B9\:71\:13\:52: :44:5220 MHz:24:54 Mbit/s
byod:34\:FC\:B9\:71\:03\:D2: :132:5660 MHz:24:54 Mbit/s
guest:34\:FC\:B9\:71\:03\:D3: :132:5660 MHz:24:54 Mbit/s
cah-iot:34\:FC\:B9\:71\:03\:D0: :132:5660 MHz:24:54 Mbit/s
"""

        expected_result = {
                              'cah-iot': {
                                '34:FC:B9:71:0A:D0': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:12:30': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:3C:B0': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:13:50': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:03:D0': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'byod': {
                                  '34:FC:B9:71:0A:D2': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:32': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B2': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:52': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D2': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'guest': {
                                  '34:FC:B9:71:0A:D3': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:33': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B3': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '29', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:53': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D3': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'corp': {
                                  '34:FC:B9:71:12:31': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:0A:D1': {'in_use': '*', 'channel': '52', 'freq': '5260 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:3C:B1': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:03:D1': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '25', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:13:51': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '540 Mbit/s'}
                                  },
                              'Hotspot10EE': {
                                  '22:AD:56:24:10:EE': {'in_use': ' ', 'channel': '6', 'freq': '2437 MHz', 'signal': '24', 'rate': '65 Mbit/s'}
                                  }
                          }

        self.assertEqual(get_wifi_results(wifi_list), expected_result)

        expected_result2 = [(' ','36','52'),('*','52','52'),(' ','60','30'),(' ','132','25'),(' ','44','24')]

        #self.assertEqual(get_access_points_for_ssid(expected_result, 'corp'), expected_result2)



    def test_make_content(self):
        results_d = {
                              'cah-iot': {
                                '34:FC:B9:71:0A:D0': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:12:30': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:3C:B0': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:13:50': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                '34:FC:B9:71:03:D0': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'byod': {
                                  '34:FC:B9:71:0A:D2': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:32': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B2': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:52': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D2': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'guest': {
                                  '34:FC:B9:71:0A:D3': {'in_use': ' ', 'channel': '52', 'freq': '5260 MHz', 'signal': '55', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:12:33': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:3C:B3': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '29', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:13:53': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '54 Mbit/s'},
                                  '34:FC:B9:71:03:D3': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '24', 'rate': '54 Mbit/s'}
                                  },
                              'corp': {
                                  '34:FC:B9:71:12:31': {'in_use': ' ', 'channel': '36', 'freq': '5180 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:0A:D1': {'in_use': '*', 'channel': '52', 'freq': '5260 MHz', 'signal': '52', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:3C:B1': {'in_use': ' ', 'channel': '60', 'freq': '5300 MHz', 'signal': '30', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:03:D1': {'in_use': ' ', 'channel': '132', 'freq': '5660 MHz', 'signal': '25', 'rate': '540 Mbit/s'},
                                  '34:FC:B9:71:13:51': {'in_use': ' ', 'channel': '44', 'freq': '5220 MHz', 'signal': '24', 'rate': '540 Mbit/s'}
                                  },
                              'Hotspot10EE': {
                                  '22:AD:56:24:10:EE': {'in_use': ' ', 'channel': '6', 'freq': '2437 MHz', 'signal': '24', 'rate': '65 Mbit/s'}
                                  }
                          }
        build_get_page_body('/',results_d)


    def test_get_tx_power_from_info(self):

        content = """
Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 31.00 dBm
"""
        expected_result = '31.00 dBm'
        self.assertEqual(get_wifi_tx_power(content), expected_result)

        content = """
Interface wlan0
	ifindex 3
	wdev 0x1
	addr dc:a6:32:07:e2:d1
	ssid corp
	type managed
	wiphy 0
	channel 136 (5680 MHz), width: 40 MHz, center1: 5670 MHz
	txpower 20.00 dBm
"""
        expected_result = '20.00 dBm'
        self.assertEqual(get_wifi_tx_power(content), expected_result)

    def test_get_items_in_parensthesis(self):
        input_string = '1234'
        expected_result = []
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234'
        expected_result = []
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234)'
        expected_result = ['1234']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234)(456)'
        expected_result = ['1234', '456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '(1234) junk here (456)'
        expected_result = ['1234', '456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '1234) junk here (456)'
        expected_result = ['456']
        self.assertEqual(get_parens_items(input_string), expected_result)

        input_string = '1234) junk here (456) junk after'
        expected_result = ['456']
        self.assertEqual(get_parens_items(input_string), expected_result)


    def test_get_download_speed_from_fail_string(self):
        fail_string = '--2022-04-01 12:10:06--  http://slicer.cardinalhealth.net/speedtest?size=10000000\nResolving slicer.cardinalhealth.net (slicer.cardinalhealth.net)... 10.50.40.47\nConnecting to slicer.cardinalhealth.net (slicer.cardinalhealth.net)|10.50.40.47|:80... connected.\nHTTP request sent, awaiting response... 301 Moved Permanently\nLocation: https://slicer.cardinalhealth.net/speedtest?size=10000000 [following]\n--2022-04-01 12:10:06--  https://slicer.cardinalhealth.net/speedtest?size=10000000\nConnecting to slicer.cardinalhealth.net (slicer.cardinalhealth.net)|10.50.40.47|:443... connected.\nHTTP request sent, awaiting response... 200 OK\nLength: unspecified [text/html]\nSaving to: testfile1\n\n     0K .......... .......... .......... .......... ..........  526K\n    50K .......... .......... .......... .......... ..........  957K\n   100K .......... .......... .......... .......... .......... 1019K\n   150K .......... .......... .......... .......... .......... 8.70M\n   200K .......... .......... .......... .......... .......... 1024K\n   250K .......... .......... .......... .......... .......... 1.16M\n   300K .......... .......... .......... .......... .......... 2.91M\n   350K .......... .......... .......... .......... .......... 1.42M\n   400K .......... .......... .......... .......... .......... 1.08M\n   450K .......... .......... .......... .......... .......... 1.48M\n   500K .......... .......... .......... .......... .......... 3.54M\n   550K .......... .......... .......... .......... .......... 1.03M\n   600K .......... .......... .......... .......... .......... 5.71M\n   650K .......... .......... .......... .......... .......... 1.23M\n   700K .......... .......... .......... .......... .......... 1.22M\n   750K .......... .......... .......... .......... .......... 3.29M\n   800K .......... .......... .......... .......... .......... 1.17M\n   850K .......... .......... .......... .......... .......... 1.60M\n   900K .......... .......... .......... .......... .......... 2.12M\n   950K .......... .......... .......... .......... .......... 1.71M\n  1000K .......... .......... .......... .......... .......... 2.55M\n  1050K .......... .......... .......... .......... .......... 1.58M\n  1100K .......... .......... .......... .......... .......... 3.55M\n  1150K .......... .......... .......... .......... .......... 1.48M\n  1200K .......... .......... .......... .......... .......... 1.17M\n  1250K .......... .......... .......... .......... .......... 2.59M\n  1300K .......... .......... .......... .......... .......... 1.81M\n  1350K .......... .......... .......... .......... .......... 2.15M\n  1400K .......... .......... .......... .......... .......... 2.82M\n  1450K .......... .......... .......... .......... .......... 1.71M\n  1500K .......... .......... .......... .......... .......... 4.41M\n  1550K .......... .......... .......... .......... .......... 2.37M\n  1600K .......... .......... .......... .......... .......... 1.34M\n  1650K .......... .......... .......... .......... .......... 1.42M\n  1700K .......... .......... .......... .......... .......... 6.84M\n  1750K .......... .......... .......... .......... .......... 2.90M\n  1800K .......... .......... .......... .......... .......... 1.71M\n  1850K .......... .......... .......... .......... .......... 2.65M\n  1900K .......... .......... .......... .......... .......... 1.95M\n  1950K .......... .......... .......... .......... .......... 3.41M\n  2000K .......... .......... .......... .......... .......... 1.53M\n  2050K .......... .......... .......... .......... .......... 4.86M\n  2100K .......... .......... .......... .......... .......... 2.49M\n  2150K .......... .......... .......... .......... .......... 2.00M\n  2200K .......... .......... .......... .......... .......... 3.48M\n  2250K .......... .......... .......... .......... .......... 1.75M\n  2300K .......... .......... .......... .......... .......... 4.79M\n  2350K .......... .......... .......... .......... .......... 3.31M\n  2400K .......... .......... .......... .......... .......... 1.45M\n  2450K .......... .......... .......... .......... .......... 6.95M\n  2500K .......... .......... .......... .......... .......... 1.50M\n  2550K .......... .......... .......... .......... .......... 3.80M\n  2600K .......... .......... .......... .......... .......... 5.05M\n  2650K .......... .......... .......... .......... .......... 1.53M\n  2700K .......... .......... .......... .......... .......... 5.52M\n  2750K .......... .......... .......... .......... .......... 5.44M\n  2800K .......... .......... .......... .......... .......... 1.46M\n  2850K .......... .......... .......... .......... .......... 7.36M\n  2900K .......... .......... .......... .......... .......... 9.96M\n  2950K .......... .......... .......... .......... .......... 1.52M\n  3000K .......... .......... .......... .......... .......... 8.93M\n  3050K .......... .......... .......... .......... .......... 4.69M\n  3100K .......... .......... .......... .......... .......... 1.64M\n  3150K .......... .......... .......... .......... .......... 5.27M\n  3200K .......... .......... .......... .......... .......... 1.34M\n  3250K .......... .......... .......... .......... .......... 10.2M\n  3300K .......... .......... .......... .......... .......... 6.12M\n  3350K .......... .......... .......... .......... .......... 5.35M\n  3400K .......... .......... .......... .......... .......... 1.71M\n  3450K .......... .......... .......... .......... .......... 5.51M\n  3500K .......... .......... .......... .......... .......... 5.52M\n  3550K .......... .......... .......... .......... .......... 1.78M\n  3600K .......... .......... .......... .......... .......... 4.41M\n  3650K .......... .......... .......... .......... .......... 5.44M\n  3700K .......... .......... .......... .......... .......... 1.54M\n  3750K .......... .......... .......... .......... .......... 10.2M\n  3800K .......... .......... .......... .......... .......... 4.90M\n  3850K .......... .......... .......... .......... .......... 2.20M\n  3900K .......... .......... .......... .......... .......... 3.63M\n  3950K .......... .......... .......... .......... .......... 4.52M\n  4000K .......... .......... .......... .......... .......... 3.00M\n  4050K .......... .......... .......... .......... .......... 2.46M\n  4100K .......... .......... .......... .......... .......... 6.27M\n  4150K .......... .......... .......... .......... .......... 6.57M\n  4200K .......... .......... .......... .......... .......... 2.36M\n  4250K .......... .......... .......... .......... .......... 3.18M\n  4300K .......... .......... .......... .......... .......... 8.98M\n  4350K .......... .......... .......... .......... .......... 4.70M\n  4400K .......... .......... .......... .......... .......... 1.70M\n  4450K .......... .......... .......... .......... .......... 10.1M\n  4500K .......... .......... .......... .......... .......... 3.90M\n  4550K .......... .......... .......... .......... .......... 4.04M\n  4600K .......... .......... .......... .......... .......... 2.12M\n  4650K .......... .......... .......... .......... .......... 9.73M\n  4700K .......... .......... .......... .......... .......... 7.05M\n  4750K .......... .......... .......... .......... .......... 2.79M\n  4800K .......... .......... .......... .......... .......... 2.54M\n  4850K .......... .......... .......... .......... .......... 10.1M\n  4900K .......... .......... .......... .......... .......... 2.16M\n  4950K .......... .......... .......... .......... .......... 9.88M\n  5000K .......... .......... .......... .......... .......... 3.60M\n  5050K .......... .......... .......... .......... .......... 10.2M\n  5100K .......... .......... .......... .......... .......... 5.04M\n  5150K .......... .......... .......... .......... .......... 2.12M\n  5200K .......... .......... .......... .......... .......... 6.89M\n  5250K .......... .......... .......... .......... .......... 7.58M\n  5300K .......... .......... .......... .......... .......... 7.06M\n  5350K .......... .......... .......... .......... .......... 2.01M\n  5400K .......... .......... .......... .......... .......... 4.66M\n  5450K .......... .......... .......... .......... .......... 5.63M\n  5500K .......... .......... .......... .......... .......... 6.15M\n  5550K .......... .......... .......... .......... .......... 2.41M\n  5600K .......... .......... .......... .......... .......... 2.85M\n  5650K .......... .......... .......... .......... .......... 5.58M\n  5700K .......... .......... .......... .......... .......... 3.99M\n  5750K .......... .......... .......... .......... .......... 5.72M\n  5800K .......... .......... .......... .......... .......... 5.09M\n  5850K .......... .......... .......... .......... .......... 5.62M\n  5900K .......... .......... .......... .......... .......... 5.16M\n  5950K .......... .......... .......... .......... .......... 9.74M\n  6000K .......... .......... .......... .......... .......... 2.46M\n  6050K .......... .......... .......... .......... .......... 5.58M\n  6100K .......... .......... .......... .......... .......... 3.58M\n  6150K .......... .......... .......... .......... .......... 8.75M\n  6200K .......... .......... .......... .......... .......... 9.51M\n  6250K .......... .......... .......... .......... .......... 2.98M\n  6300K .......... .......... .......... .......... .......... 7.90M\n  6350K .......... .......... .......... .......... .......... 3.99M\n  6400K .......... .......... .......... .......... .......... 7.81M\n  6450K .......... .......... .......... .......... .......... 10.1M\n  6500K .......... .......... .......... .......... .......... 2.81M\n  6550K .......... .......... .......... .......... .......... 10.3M\n  6600K .......... .......... .......... .......... .......... 4.27M\n  6650K .......... .......... .......... .......... .......... 10.1M\n  6700K .......... .......... .......... .......... .......... 10.4M\n  6750K .......... .......... .......... .......... .......... 2.26M\n  6800K .......... .......... .......... .......... .......... 7.50M\n  6850K .......... .......... .......... .......... .......... 6.67M\n  6900K .......... .......... .......... .......... .......... 5.80M\n  6950K .......... .......... .......... .......... .......... 10.2M\n  7000K .......... .......... .......... .......... .......... 2.00M\n  7050K .......... .......... .......... .......... .......... 5.36M\n  7100K .......... .......... .......... .......... .......... 10.2M\n  7150K .......... .......... .......... .......... .......... 9.14M\n  7200K .......... .......... .......... .......... .......... 3.02M\n  7250K .......... .......... .......... .......... .......... 4.36M\n  7300K .......... .......... .......... .......... .......... 5.27M\n  7350K .......... .......... .......... .......... .......... 10.2M\n  7400K .......... .......... .......... .......... .......... 9.40M\n  7450K .......... .......... .......... .......... .......... 1.99M\n  7500K .......... .......... .......... .......... .......... 3.79M\n  7550K .......... .......... .......... .......... .......... 7.76M\n  7600K .......... .......... .......... .......... .......... 8.25M\n  7650K .......... .......... .......... .......... .......... 10.4M\n  7700K .......... .......... .......... .......... .......... 2.99M\n  7750K .......... .......... .......... .......... .......... 6.74M\n  7800K .......... .......... .......... .......... .......... 4.95M\n  7850K .......... .......... .......... .......... .......... 10.2M\n  7900K .......... .......... .......... .......... .......... 10.1M\n  7950K .......... .......... .......... .......... .......... 10.4M\n  8000K .......... .......... .......... .......... .......... 2.14M\n  8050K .......... .......... .......... .......... .......... 8.35M\n  8100K .......... .......... .......... .......... .......... 7.89M\n  8150K .......... .......... .......... .......... .......... 10.2M\n  8200K .......... .......... .......... .......... .......... 9.33M\n  8250K .......... .......... .......... .......... .......... 10.4M\n  8300K .......... .......... .......... .......... .......... 2.95M\n  8350K .......... .......... .......... .......... .......... 5.30M\n  8400K .......... .......... .......... .......... .......... 4.90M\n  8450K .......... .......... .......... .......... .......... 10.1M\n  8500K .......... .......... .......... .......... .......... 10.2M\n  8550K .......... .......... .......... .......... .......... 10.4M\n  8600K .......... .......... .......... .......... .......... 3.32M\n  8650K .......... .......... .......... .......... .......... 4.00M\n  8700K .......... .......... .......... .......... .......... 5.33M\n  8750K .......... .......... .......... .......... .......... 10.3M\n  8800K .......... .......... .......... .......... .......... 8.40M\n  8850K .......... .......... .......... .......... .......... 10.4M\n  8900K .......... .......... .......... .......... .......... 5.34M\n  8950K .......... .......... .......... .......... .......... 4.80M\n  9000K .......... .......... .......... .......... .......... 4.02M\n  9050K .......... .......... .......... .......... .......... 9.66M\n  9100K .......... .......... .......... .......... .......... 2.88M\n  9150K .......... .......... .......... .......... .......... 10.3M\n  9200K .......... .......... .......... .......... .......... 8.41M\n  9250K .......... .......... .......... .......... .......... 10.2M\n  9300K .......... .......... .......... .......... .......... 10.4M\n  9350K .......... .......... .......... .......... .......... 4.06M\n  9400K .......... .......... .......... .......... .......... 3.34M\n  9450K .......... .......... .......... .......... .......... 11.4M\n  9500K .......... .......... .......... .......... .......... 18.8M\n  9550K .......... .......... .......... .......... .......... 19.0M\n  9600K .......... .......... .......... .......... .......... 3.42M\n  9650K .......... .......... .......... .......... .......... 3.81M\n  9700K .......... .......... .......... .......... .......... 3.65M\n  9750K .......... .....                                       16.2M=3.0s\n\n2022-04-01 12:10:17 (3.15 MB/s) - testfile1 saved [10000030]\n\n'
        expected_result = '3.15 MB/s'
        self.assertEqual(get_speed_from_download_result(fail_string), expected_result)

        fail_string = ''
        expected_result = '(no download speed)'
        self.assertEqual(get_speed_from_download_result(fail_string), expected_result)

    def test_get_upload_speed_from_pass_string(self):
        pass_string = '<html>\n<body>\nsize of file uploaded is 10000030 in time of 9.46198511124 seconds: which gives rate of 1056863 Bps or 1056.863 KBps or 1.056863 MBps</body>\n</html>\n'
        expected_result = '1.056863 MB/s'
        self.assertEqual(get_speed_from_upload_result(pass_string), expected_result)

    def test_ping_value_from_pass_string(self):
        pass_string = 'PING slicer.cardinalhealth.net (10.50.40.47) 56(84) bytes of data.\n64 bytes from slicer.cardinalhealth.net (10.50.40.47): icmp_seq=1 ttl=57 time=27.4 ms\n\n--- slicer.cardinalhealth.net ping statistics ---\n1 packets transmitted, 1 received, 0% packet loss, time 0ms\nrtt min/avg/max/mdev = 27.437/27.437/27.437/0.000 ms\n'
        expected_result = '27.4 ms'
        self.assertEqual(get_ping_from_result(pass_string), expected_result)

    def test_make_bssid_to_ap(self):
        bssid_to_ap = make_bssid_to_ap()
        self.assertEqual(bssid_to_ap['E8:26:89:8F:D0:52'], 'PR005:028')
        self.assertEqual(bssid_to_ap['20:A6:CD:D4:E6:52'], 'PR010:022')

    def test_wifi_config_from_content(self):
        content = ""
        expected = {}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)

        content = "test123"
        expected = {'ssid':'test123'}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)

        content = "test123,psk5678"
        expected = {'ssid':'test123', 'psk':'psk5678'}
        actual = wifi_config_from_content(content)
        self.assertEqual(actual, expected)


    def test_get_wifi_mapping_internal_wlan0(self):
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan1: connected to corp1
	"Ralink RT5572"
	wifi (rt2800usb), 9C:EF:D5:FA:B4:0A, hw, mtu 1500
	ip4 default
	inet4 ************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::cf8f:142f:f3d8:3e04/64
	route6 fe80::/64

wlan0: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan1

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan0', 'external_wifi':'wlan1'}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)

    def test_get_wifi_mapping_internal_wlan0_only(self):
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan0: connected to corp1
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500
	ip4 default
	inet4 **************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::3f53:64e5:8637:974d/64
	route6 fe80::/64

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan0

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan0', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)


    def test_get_wifi_mapping_internal_wlan1_only(self):
        # cah-rp-10000000b7578519
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """eth0: connected to eth0
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500
	inet4 10.211.6.199/24
	route4 10.211.6.0/24
	route4 0.0.0.0/0
	inet6 fe80::a12a:3000:9219:7b2d/64
	route6 fe80::/64

wlan1: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan1', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)

    def test_get_wifi_mapping_internal_wlan1(self):
        # cah-rp-10000000b7578519
        # devices_report, fails = do_one_command('nmcli -t')
        devices_report = """wlan1: connected to corp1
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:07:E2:D1, hw, mtu 1500
	ip4 default
	inet4 **************/20
	route4 0.0.0.0/0
	route4 ************/20
	inet6 fe80::3f53:64e5:8637:974d/64
	route6 fe80::/64

wlan0: disconnected
	"Ralink RT5572"
	wifi (rt2800usb), 9C:EF:D5:FA:B4:0A, hw, mtu 1500

eth0: unavailable
	"eth0"
	ethernet (bcmgenet), DC:A6:32:07:E2:D0, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

DNS configuration:
	servers: ************** ************** ************** *************** *************** *********** *********
	domains: unregistered.cardinalhealth.net
	interface: wlan1

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""
        expected = {'internal_wifi':'wlan1', 'external_wifi':'wlan0'}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)



    def test_get_wifi_mapping_internal_wlan0_on_wire(self):
        devices_report = """eth0: connected to eth0
	"eth0"
	ethernet (bcmgenet), DC:A6:32:96:56:C2, hw, mtu 1500
	inet4 10.211.6.227/24
	route4 0.0.0.0/0
	route4 10.211.6.0/24

wlan0: disconnected
	"Broadcom BCM43438 combo and Bluetooth Low Energy"
	wifi (brcmfmac), DC:A6:32:96:56:C4, hw, mtu 1500

lo: unmanaged
	"lo"
	loopback (unknown), 00:00:00:00:00:00, sw, mtu 65536

Use "nmcli device show" to get complete information about known devices and
"nmcli connection show" to get an overview on active connection profiles.

Consult nmcli(1) and nmcli-examples(5) manual pages for complete usage details."""


        expected = {'internal_wifi':'wlan0', 'external_wifi':''}
        actual = get_wifi_mapping(devices_report)
        self.assertEqual(expected, actual)


    def test_wireless_to_configure_from_wlanX(self):
        device = 'wlan0'
        expected = '0'
        actual = wireless_to_configure_from_wlanX(device)
        self.assertEqual(expected, actual)

        device = 'wlan1'
        expected = '1'
        actual = wireless_to_configure_from_wlanX(device)
        self.assertEqual(expected, actual)

    def test_is_connection_active(self):
        line = 'corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0'
        expected = True
        actual = is_connection_active(line)
        self.assertEqual(expected, actual)

        line = 'corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:'
        expected = False
        actual = is_connection_active(line)
        self.assertEqual(expected, actual)

    def test_find_external_radio_from_lsusb_dash_v(self):
        content = """/:  Bus 02.Port 1: Dev 1, Class=root_hub, Driver=xhci_hcd/4p, 5000M
/:  Bus 01.Port 1: Dev 1, Class=root_hub, Driver=xhci_hcd/1p, 480M
    |__ Port 1: Dev 2, If 0, Class=Hub, Driver=hub/4p, 480M
        |__ Port 1: Dev 7, If 0, Class=Vendor Specific Class, Driver=rt2800usb, 480M
        |__ Port 2: Dev 4, If 0, Class=Human Interface Device, Driver=usbhid, 1.5M
        |__ Port 2: Dev 4, If 1, Class=Human Interface Device, Driver=usbhid, 1.5M
        |__ Port 3: Dev 5, If 0, Class=Human Interface Device, Driver=usbhid, 12M
        |__ Port 4: Dev 6, If 0, Class=Human Interface Device, Driver=usbhid, 1.5M
"""

        # /dev/bus/usb/
        expected = "001/007"
        actual = find_external_radio_from_lsusb_dash_v(content)
        self.assertEqual(expected, actual)

    def test_find_external_radio_from_usbreset(self):
        content = """Usage:
  usbreset PPPP:VVVV - reset by product and vendor id
  usbreset BBB/DDD   - reset by bus and device number
  usbreset "Product" - reset by product name

Devices:
  Number 001/008  ID 148f:5572  802.11 n WLAN
  Number 001/003  ID 062a:4101  2.4G Wireless Mouse
  Number 001/002  ID 2109:3431  USB2.0 Hub
  Number 001/006  ID 046d:c52b  USB Receiver
  Number 001/004  ID 4037:2804  2.4G Composite Devic"""
        expected = "001/008"
        actual = find_external_radio_from_usbreset(content)
        self.assertEqual(expected, actual)

    def test_get_next_corp_wifi_to_try(self):
        corp_wifi_certs_list = ['20220401a','20230401a']
        current_cert = ''
        expected = '20220401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

        current_cert = '20220401a'
        expected = '20230401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

        current_cert = '20230401a'
        expected = '20220401a'
        actual = get_next_corp_wifi_to_try(current_cert, corp_wifi_certs_list)
        self.assertEqual(expected, actual)

    def test_s_lan_detail_string(self):
        # ok, or on expired cert... eventually the connection will drop, and we move on to the next.
        s_lan_detail_string_pass = "Connection 'corp0' (81ed4954-6347-4234-bb6b-5747e28d6f54) successfully added."
        s_lan_detail_string_fails = ""

        # when cert is has a mismatched password
        s_lan_detail_string_pass = ""
        s_lan_detail_string_fails = "Error: failed to modify 802-1x.private-key: 802-1x.private-key: Couldn't verify PKCS#12 file: The Message Authentication Code verification failed.."

        # when cert is junk content
        s_lan_detail_string_pass = ""
        s_lan_detail_string_fails = "Error: failed to modify 802-1x.private-key: 802-1x.private-key: not a valid private key."

    def test_should_we_rotate_corp_wifi(self):
        time_since = 0



























# End of source file