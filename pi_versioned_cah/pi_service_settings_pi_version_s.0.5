service = 'settings'
version = 's.0.5'
description = """
This is a pi service for setting organization specific content.

logo for use by hmi

let this be where psk and ssid get set into the config file
(instead of image building instructions doing it?).

"""

release_notes = """
2022.12.07
s.0.5
Make it so that organization pulls the settings

"""

# globals
s_corp_wifi_certs_path = "/cardinal/wifi_certs/"
s_corp_wifi_cert_last_used_path = "/cardinal/"

# ====================================
def get():
# ====================================
    return_value = {}

    return_value['added_users'] = ['opc']

    return_value['added_sshd'] = ['PVJZLSLC01.corp.cordis.com', '***********']

    return_value['logo_xxd'] = logo_xxd()

    return_value['wifi_cert'] = get_wifi_certs()

    return return_value

# ----------------------------
def get_wifi_certs():
# ----------------------------
    global s_corp_wifi_cert_in_use, s_corp_wifi_cert_just_loaded_in_use
    _ = """
2022.10.17

Windows: (Fails to make private key exportable... something about policies?)
Make a new (fresh) cert:
Remote desktop to DW6888665558828
FA-OH085.NPSIpad
GrsROYIW3WLVAWb

te-CAH-RPI-User-TEMP-DO-NOT-USE-KF-93b44b28-44cd-4dea-aeb0-95cf12524ff5
or
OH085, NPSIpad

Start menu, control panel, search, hidden, File Explorer Options, Hidden Files and Folders, Show.

Explore, OH085, NPSIpad/AppData/Local/Microsoft/Credentials
right click, general, uncheck Read-Only, apply



Start menu, certmgr.msc, (do not open as control panel, use Microsoft Console Document)
Certificates, Personal, Certificates, "OH085, NPSIpad"
Right click, All Tasks, Advanced Operations, Renew This Certificate with the Same Key...
Next, Enroll, (Status: Succeeded), Finish
Right click (again), All Tasks, Export...
Next, (no private) Next, DER encoded, Next, save to
downloads/20221017a.cer
Finish

Open Teams, new message, attach the cer file.

Receive the file, and save in downloads folder on Mac.
Double click to open the file in keychain.
Find the cert in the list, right click, export,



Linux direct:
https://geekdudes.wordpress.com/2020/02/14/request-ssl-certificate-for-linux-machine-from-microsoft-certification-authority/


2022.01.26
back documenting the current wifi cert:

Account name: FA-OH085.NPSIpad

From the command that makes the wifi connection:
802-1x.identity "<EMAIL>"
802-1x.private-key-password "NgsA7cR3fqf?qnpA"
802-1x.private-key /cardinal/TempCert.p12

To see contents:
openssl pkcs12 -info  -in TempCert.p12
(enter the private key password from the connection line)
[It shows some details, then asks for "Enter PEM pass phrase:",
use the one from dwf rawNotes.docx file, search for "tag:dwf20220126a"]
(enter password)

    # made with "xxd -p TempCert2.p12" 2022.03.01 account name "FA-OH085.NPSIpad"

    # made by Bryan Runnels on March 1, 2022, expires...
    # cd "/Users/<USER>/OneDrive - Cardinal Health/RaspberryPi/certAsOf20220301"
    # rename pfx to p12
#    # openssl pkcs12 -in raspberrycert.pfx -nocerts -out private.key -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb
#    # openssl pkcs12 -export -out raspberrycert.p12 -inkey private.key -in certificate.cer -passin pass:GrsROYIW3WLVAWb -passout pass:GrsROYIW3WLVAWb

check contents:
openssl pkcs12 -in /cardinal/TempCert.p12

Check expiration:
openssl pkcs12 -in /cardinal/TempCert.p12 -nokeys -passin pass:GrsROYIW3WLVAWb | openssl x509 -noout -enddate

then paste the one password for input and output
made with: xxd -p OH085NPSIpadFullchain.p12

Can we remove the passphrase requirement?
https://blog.armbruster-it.de/2010/03/remove-the-passphrase-from-a-pkcs12-certificate/

cd /cardinal/
sudo openssl pkcs12 -in /cardinal/TempCert.p12 -nodes -out temp.pem
sudo openssl pkcs12 -export -in temp.pem -out junk.p12
rm temp.pem
sudo chmod +r junk.p12

openssl pkcs12 -in /cardinal/junk.p12 -nokeys -passin pass: | openssl x509 -noout -enddate

    """

    cert_contents = {}

    cert_contents_not_to_use = {}

    # old, expired cert, as a test
    cert_contents_not_to_use['20220401a'] = {
        'password':'NgsA7cR3fqf?qnpA',
        'content':"""30821cec02010330821ca806092a864886f70d010701a0821c9904821c95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"""}

    cert_contents['20230401a2'] = {
        'password':'GrsROYIW3WLVAWb',
        'content':"""30821eda02010330821e8606092a864886f70d010701a0821e7704821e73
30821e6f3082069506092a864886f70d010701a082068604820682308206
7e3082067a060b2a864886f70d010c0a0102a08205413082053d30570609
2a864886f70d01050d304a302906092a864886f70d01050c301c040887ab
99817a88d911020207d0300c06082a864886f70d02090500301d06096086
4801650304012a0410692717d76126f6b517e6ff135003def5048204e03b
8ad280e36966cf6bcf15098416dc77148731b9cd3751c58dd49864b28efa
172443ee71eefa2460145cbf4e876a7ce78c09fedddf73492f8a6f165e38
f0f94be13d19f1a08795cfa544e7c2f15b7715be3c6d25b752c4725e2aca
d53668cd30604113a2ba203b4fc2e6aed2a3e4a3806a6573433457091e1c
e11ffea024842b85844df721ab81f4a158993eb4c29389f175847cf2b11a
702d80e94f21cdb5c482098a8f59dc3834f1efc3c8e387e5223a114f2066
bcf6859d2bcb0dd055b31bc63cced34cf8ca60c1b3fd166f0e772b085819
4c3a565d983c51e2ffc9c2e836ffa0d170551107e75d3db3b70773a3eb7d
20b26740e6e594192dab7b6eda88e1d9e7f80e5a00627beb5f051a91438d
bc723bf193b780281517da85e72922c7d9800ae351ecc01cde62e2c7829a
b92b25b5f1a78daf8f153721e65aba2d838ef8da4f4121a678384624a47e
61b20883a91d36553336838915e6ab80683a634f490f83bb230c0d69c0f9
83cb47ce5237f099c618a0f1b4b52d73058a335585e8406a59528759a26a
b668478eee82bd35cefd336d6e93e6bbb2279b40e512f8ffdd939be62d6d
76295faccf32f02ec2364e0ee3067af33ccf3af7cddaf555e02d081625e1
9616faa4085d6e409b4ad14f126d55c5196122b155731ba8c74eff105a12
0fb201c214e2b412a0c015751e34a529c2b5fbef969bbe0b8ffb19285bdc
1bc8e333d692228ccbbfb840ce38cd98677bf2f4d8a95180e26e7ff1f6a0
58812a92fe2a2b22ebfc92334cebb0ce5f3fc060fe5706921fc7e46a12e6
deaeb03ef566825b31f33765875d9266f6e974a631088102bb700c2c778c
3aa4e7cb0b5f86766fdac08e3619a4afa91237b39002d8cd628ccef75126
cf33a87247e944f43928eb762b669973121a2a9f3e120d325ee854adbe52
6061dcfd4e88ff20ddfeb789c04d540f451ea55fb90bdfe2b318ab908f36
40e29531b81052f1d63a546c581dacd2bcfbef2babaecbc25c0842968149
413cd58faf55b54d37cc5febccab83ab1488cd5dc9c800179e188c8a2778
bac3389cfd45608d1019023f1bdedf0284178cca2ea9f76954f2fddb184c
be4971f36d3839b1820d5d5e282cbfb2deb81a41c9bfb965faa4048b2030
196f42432c638c679500652cc97d72aaaf029fcde9a45f7b77f75a124f07
c9eac7344d2c2a38c4d3c5e7681342942c6ba0251eafa792a61dcfdfb660
bea13cc09acef6c6c39d1bd7fbbbf54d23a5731f1ec93bb61cf0aa31953e
da321a5394ba37a533626534b804fce2db4e8612344f9e9860b40becd23c
ceb8582a16e62c479059315d40ed089b75889cfc77bf57fc488c5dcc6c7f
8df574985c37f46cf069b5f5e0e1debe7149351eee6990b466b4bed9ad5e
1c1010c4a1fd6699f379ca384c595fadc91681f6d17b7cbf5921c2de2731
9ae466dd15cefb5bcafee25022e703ad32bdaa44292edd85470f81468611
159c0f2df56bfea9142c167db691347056232aec3e27c9cdcdc530d4057f
b1691b2e41a849c1101e9d6313af36c573310cf062c648f38eef4a38db03
5d908416062e847c962b32e5c633bba791ec5309cd58fb13d249f13d973d
c43f657af0f4695f843b355702d671173535dd76e7175042f32cdac9b25c
965cf0fc233a478c61ef9afd1abd8d0362da4df8738b1f45af5d4e834938
91b01896d0a46c18cf15bc03d7cd050d1fce66d1908acc87cf825db4e4ef
c3adf81c219b33bd773a92d722e9ea36ea31820124301306092a864886f7
0d0109153106040401000000306b06092b0601040182371101315e1e5c00
4d006900630072006f0073006f0066007400200045006e00680061006e00
6300650064002000430072007900700074006f0067007200610070006800
690063002000500072006f00760069006400650072002000760031002e00
3030819f06092a864886f70d0109143181911e818e00740065002d004300
410048002d005200500069002d0055007300650072002d00540045004d00
50002d0044004f002d004e004f0054002d005500530045002d004b004600
2d00630062003600310037006100380064002d0063006100660030002d00
34006200630062002d0061003400320037002d0032003600380031006600
66003100300064003600660031308217d206092a864886f70d010706a082
17c3308217bf020100308217b806092a864886f70d010701305706092a86
4886f70d01050d304a302906092a864886f70d01050c301c0408bb85ce6f
4cd82f70020207d0300c06082a864886f70d02090500301d060960864801
650304012a04106fef7e641d8653b286f42fb8b05df87f80821750439d9d
748b29434f07ed8563c24c7aa97431d1f1a131164992e1a490ded215036b
4718dd4a9840cac05e237c989f58716476f4047e53b6844aa4d473f1c759
9914a6c0296670a3b6fb56ea7349dc8a08035bd2671af1e935b7316e5597
f3aa218f0b417082b1afe0cd351db236491713cb4fa732933f6b1a1cfc9e
f56c2fc346ed8b2adf09d503b8489f8cdfbaa8fe8c01901ff547d7969ebe
321ceaa42aa19b527d1b05d39eab99847f134c8143d3c637eaeb107f7e77
1af64a4d19d9983911990e04da7e43efa1d3f5a97c19d7115ec0e688db07
8f6c1e285341e366c6bc3675c70c11145378a3af1e9d73481f17210e882d
b6a3d66e2f15954520cdaed5e720a881924f7f56a945bba8f0942c4f9c3d
a4ae2f627732b21fce3fe4f9604672e3039a2f9fa974fd90398e4203114f
b6075d60880845d59fc5f31e8fb869b968f1c36accfc77774a3bd3bb8dac
a822579cb5780cded13cab4896db5b8ee19af19afa4c084f4ee93a6c53f7
f86277f23ca5daa09d2fd357143e212db3d1cd27070260e359aba8e2b9af
21b39e1905708f31111a6d11565c18de603ca21be29f8f9c7a07bef53dca
61c07ed1c02a29065fbf93559a8a966c928f86437ac9ed914b380067f968
637535249f52f0034903a91d6bb8900a6aa979c59eee054df1aae68f3eb6
a6e47d4988f792a0791e58e835c00d595bdf9037dbd4a468a6815087429a
cc742c355b1a0b4932e20f795c1984c063a791732764324a3d34a7d85ae6
2623276dca6b5d806ae0a7e8b9ac706f37f7a14249332eb95f98da9521f9
98433b55556f105cbdc8a997726339658f90506c8b0298e33a594dd3bb12
b1f9e194e7fb7522d9f71bd12afbd8e16d535e0c3b812c4100754e643123
adf8522318c7d3229bee4109e553db7e0c39e92861c558749d7dc4570192
6c06d471566cc301a7b811ebc3feb377ce499865a48dfdf0b855b471770f
e0751d70109616110c09faaee5b147928c94b26108f7ce4638b569b7ff23
68640e8b3a4397ba44c4f55a8722fdb246267ce7c79f18d064044e3ca74d
a4fc5ae50f8eea52ee9eb5afc210bfccc8617ce82a408e0de00af70e6fb0
8aeea8345773a491ec29cea5a3ee90af48a91b68029f24219c7d0f639097
a8aa351f4a7d4c4b8408d6214ec97b85dbe823c5c6003bebff71272fdb9d
05513643a756f4e340db63d5489a9fcc9e490f48e98af12581c3165370bd
e1c0a6527fec9bc9d24dd54e73800761929813abe478a18318a7230cf3c7
bc99bba4274bee685211e5c56b948265859cbdcc10e1ad0084b6eb5386ba
9c7882a648cbfcc79034c47e53b04eaeaf759e1fbbcf6e75820c26c6eba6
2790820e288f27e811ba6331dae34a32da8327679c10a453067946ab53f6
dc5558f0a2292ff624ae9cf851f27a1d1785671cdf1fadc197bcbcbd8995
b9a540e6a623e82f76777933838fdb50fc16c65ccad9aa3b6dba639be18f
68d039a6ec44d4f287b30f1e2b72584fe901c7323e7ed8f74a41491728b4
5b4be8355391cafd6885183f8e2336a00fa7ff481c68be4ad5b7d2b5643f
a594a6b695ddb1d63566b80e67b4dcdcf4bc1c173762c6295270b430c035
781c61cc0fa701d351737cdd5225da49948cfdc669d1cda3a8c573c53bb4
2e2a6b7f302ffcdda96703c179cee554ca2ea813c7965e29d35edb5a0038
226a891ca8e0bc62a2e61757b3456b4c2afce3f38bde9130571e28d67d65
9c1faa8d64d1427b38dabd03b84f944c6dcef0f1b779719fbce9bd05ae0d
c8268e2cfc4838b786f732ef5ef11f88a86110241fbec7afd18764d5cf20
04e9bd1aa07eaefd82e12e7e99942b3b76f3888c9ea6860d0c0064bc5cd3
a0e1823a0ea71c0f26d2b49fbbf56648a6c11c34f8ce24469bbfb0d61036
baebd24924bbc798fbbd5a61f8b901dc809c45b98bfdcf37e9a96bc656f8
1b8c499ffa36296a888d80ff16db4f991272285a1d48f0d6723ddb2827dd
9c37f7b027be735e9ba596ef7274a2951f0b1a9e6783beb702c28e5e09d5
8b3bfe96366558e2454b83f87d76a6940b9ba380d64878e0882a77f91ffe
0755b2c35a4c367a1f7275debe2c0b3163d5934fc11cd5b7ea3420b934a7
d5fcba9584541e1e754a8f1d1b464585db867a987b64bdeb044b4f82926c
d57f6b698d046fd486ae3baee6f457827471a262cb0722b5871533033c63
24574bbb4bbf71da3cce9b91b359d627556d661234ba3cb5dc5b47ea5e5f
a89a0a80e4b47a8dda35fd940d03bca75de20fe39d3e9b80e1755853c9b1
17deb58a228e80f4db3166410f8b0b9fec4effe81791df60833053aed2cd
8a4e19beab70c85aa0685bbcf69d2450490c5355224718856b4d3971c9ed
f6931338df3f66c9179f3b3ea802db66c8f9b444539e8652b5aaff5b6331
6859265318dbed1dc02d5f81f971f0d66703f7cccfe3b9755c2c9e497ae5
15253cc50e395eb4ec4682bcab93f842aa3277ab683b3b28a3a673e1df3b
ba22d6e626a95026bb52ed6ad74aa4ec691e36e3416218f7a1a84a104f1f
7e695f2ab19b3e60a6deb9a422cc6c94ae15139e5540c160b0ad677171dc
151db918cff0701e2eba31ce4aebea318903758a6fe7e0ad19b656054e56
c51d3f5f3fc5ca072cbe25974416a1d993f96c0d7945f2eb63d4888357a1
026f30c3afa9e78fbfa1f6cb21bcc1ee9322fd59652f64dad0dc352c17a6
fe682631e834d0fc55c27e97eea460b2fc3d83a7d3e77cbbc9f4b277d5a3
65862174f01082449978356058313312f7cc59ad35d45e65005bbdb67d5a
4079448f4d6438d54a08a243cdc9b1836835d94ca715fa919d4e001bb808
b6d7856e01a0a8a8f5b8b8624f841f0840150baaf52863fa1187f00472eb
7e3ee086a44e48d8a3834920944013690a3e4f7480368b7e311c7ab50c68
8a7963afcb0ab530e290391c806a09465df4a05a491ffbfc9e28e0e8ae31
112a8c45c0e6417acbb41d068dbf180c7c804037e2219d23e786385054ea
e9d04000496381e8074207972dd80c6c50c6cf1d18ee15fc5bdc486bde22
c17761349765baf1afb6c74ae059a59e5d3a5f7ce885a6d2476e4d9e5702
b4036e890dad8ba9e8439d8c7c4539bd0da44517010d928be6ccfaa64956
b7adc0da2438d8d1bca2254c66000afc9b600e4218e26bfb243da0ffdd96
068c1c7c5851830c3d8d84dd500b81a702f71025f23e437beed2b31bbf39
82b30d55b6fd11c3d510054a0cd02817794afcd19b7924c4363ae4ec5e39
1d309f1677a2eaa33af3afd21b5a10fe42686ec054ba0094308837dc01f4
fb3fd424a4ca77ca64725c927c5b7a7021daadf7ab3a5dd53021c5eb0358
4502dc2e3067b5e9239600d18741eb141f91243c7820187bab3e8c657b2f
cf35d0df7b43d0619581f8ba674645ed4b8de72e9d9ba2e6493213282cf7
365b6f8f6a9c16ad9a5d13c13e3586c3b31c5ce88bb15e1b10d50c7e409d
6c40982e6aa9caea95b16107162365350f1990fadce1e75c2ef96778eedf
4782c0e7c5a397aaa9d8c0d61a067db8252c600d0c1893b6667f8bd14522
830b8995c8ad7ef1bd1f91c1ecaf367d9cd4093c3b51d8a7d374b23418a8
726baf31250a9c2724ca3d748e1841d65a8814ba88acfd72770708d65976
44035dd782034c1a3537c7ac8686775e1dd64d082c34a96971d23c8a41ab
2a517d63b2ca5f8af63030ed9a6a82636c1a45e1fb4a6b4b613fb9dac3d6
1d869d8f850ac6630738f32608673ddd8f9e5318cd3165c1246c2c1eea68
ce8128054d9778098307948d95377ee9d4324837cc1f425936993c5b06b2
01e32e469bd37e35d228b5a11a17c4c8138195e5ec172f8d8d565ef98934
a3a69ce9614e8f5224984bde531a19f6571232465a5cc24fca229dee1755
585bae112b5f0a4fa651624cbb7ad1b2ddc8b337fb147e71e0cd01aa6165
7f82fd02e3ee904891ee12405ce4b4e1b65fe2304482e682d703fffc47ae
f5359b4953242b2a1334dbe699ad17ed6e2ac3b9b0705ca432c50b0e388d
86d157411dd37e24f395b0a8be637d0241cbe8e418b8b082566fbb73b9ab
3187d121ce2dc50ab2a846bd7b33ade48ab0452a6708f80c37942dcccf58
6bad9ab0994c6ec8a6d9618665a3eecd318b1256c55e0346e45409a97bed
6aa1fc114f4d8420bfaba1bb3beb7238ae49fc3f8f75d48b56fa7c612558
2b28d692b8644f6ac8f676d3103eae5719249740b11c3a6f4baad336a50a
07d5201cbf9ddd452175fd0bb447e62aa642ab34184bb5d5fd4549badceb
a52c695875f0ecc5a28ec383cd1f99a52191e7684ad24bf32b30ca158ba5
5796ab5723682946412bd7b916ae44fb55f95e3b2e105170aaa0d45d9efe
d50e460034b744bf2c20f87fb0f0ef9975a5c1d4ab38a3fff0079d26f18f
754d5453e1bb9f7315d67bf429abb4be0e736accb13526236ae21258a041
8e4a8f0c3c9f27d2bbfd59167a5ae11dd4732cbf23b418b5f1a67a7038c9
9e4684191772d0bc61301083f9719a1b7ef1d52b58407d84171f497a10d7
82fa6b24206575509a157636d26dc88ef54c445e6359f8d061f09f86b5d3
906b4b38d742502a408e7d3edb872a5e9c99967098f7b515e44ff6bda2c9
60a7f69a15cb822b830fdb6634ba0e9447ac8b6a57738b63a1c87639f4ad
1a15ade2f2579d99625a5d7caded60ccd2c230e591ed8ad6465ee64884b2
e7e278643e12ab4434fa03ca9c2eb6faa77c17f00eef7c28c9fe72734231
1ad354fec7005eca899a450a0b7fff6d9b1f6e49cbee530c2fce901e4a10
9fcf20461113c39440ab1a0168d7d55a9bde33bd139e74cf5e978f34dd05
1d7105c01d9fdc97b39dda3c01e6a3d587c67e98b02c65b996dbb5d75d21
8ebb20dea1e10b5299a9812a4238b38b23bd1744f1badbb8bf29ae1987d0
d3fb3034be2b856cf5e7554806a8b3bca400cd727911cd42a466ed40c81e
015e5d8ff1b0d1c6f9af7eb4c6d8b070289f5e3ee661152d76efdf9fd794
8ca5c25da25a73ad20f3cd803f1e8cfc4158d0c371f39fbfbcbfb602b8fe
adf6f0e19d0c5d7c2ce92232e0e14ccc00febc216968ce1eecef9ad6df2b
7b0259677b523002f79e894960bb299b4779c4b611febc67e2f77427d2dc
98ef285c9c24c78b98a49928f971a978d3bb31f7a9939fc7f0b8ba6c3a70
bc1eab84cb4fb7f7c097b4c09506a06e30af4a3ec4c711b2931c789e3bdc
708ec5466379a428787cc497fe10fbae1c26f21b8fedf94b718652da7752
e00d3b2261dfe661926da7f028e1b6fb464cb92ed23e1080980e93ab3877
6a36258bab28df44fad15de9b88eea7b8d5f4bb08be7c3e470b8596bd9ab
d979e47d11c12d8dd431359fdd12f11382d7121d53d3a1038941aa70dd0e
81194b08e7fc1376f14cdfc9246597c98a9ef651f8597d8996027d193328
26ea8f6f7dbca369ae15057f820d10fe1bfcc0cd5df90c6ce509991229e2
a4280bb099a565359f008ee334aad2f8524076c2f24050c04b59657eed12
4c48145c1945c1282ce336715e61dd8a5799e9d930be89a8a231afd4aa43
50f07930f56feab9578c03af883e9a749bff12ad790dd9ae8c803bc5da12
2715cb1bb65753c91bf8df73e7a127d35d12c43b41f04e00bec202fdba04
6a15870137ef14f438214021c9113103357ec55d1ea2e1ed0e0a94610516
cb3334768b19e7e067077e306856dd28c2eb00c0ae4c2698157ce8d9d082
4846518f9477d306c850088682f93ed7c4f64323df4f3cf04d4f8535e2a8
62574a23aae22abdb470a14d4911c3bab9d2fe7b198ecca43fa21ecca3a3
e6cd957fa124f8164751dd41680c9dfc75f6d142d4f556ff8d7b57ab52a4
153d1457262f941d8178916cf6962b27db4b9d0644459f278db0354fddb0
7158f127b59b066936c612612b49fe374de2731fd7fc15c01b0bd1fc6d88
cd73c283cd818f6ac30e5b033a621e6d4ba7dae1c2a9837feb3d854f8b56
67a2c20b4091d1ab08fc1c5628c9cf28e5775e9e65b611cb9c35f60428e5
2a2cab8dcd0031b5bc635c230392a333973a1a3622a33cb75a9d4e6577f7
c606f9e397fca47429425ddfeb07962c5cf5aca4270839ae237de950a356
3096d0e091fcf4014a2365faac81da065045ed279017e5af8ff05d4a1dc3
64e1fac16592775123c2f44c821b5d434a5085cf13d6bcb36b0ce16d823f
214821421dc6102f7b82f424ec08aede629ba803e7937d3fe7e308da4161
4a4d1ac709684ff588fce09fcb7ceed7e27173c64546fe41866575ff97b2
ccb12baed2645bc69069e6e9b67ae02b83275a4db50714b6d1df1812de60
d2aa85d47830b8559ca4443f304dbd0313804e2cbd0dc0fcea76f829669a
744f3bb9a98890ce1c793e56e6040b1835754ffffe39c77618b3cf66f292
72abd269118828bf8e524eacc00e91998bf51ca6d2645ec105d0cd8112b9
1ca204d84e89fd22fa587d46139b2edf6b8de069b0164562c5d63aa7a2e8
fccfbb7cbcf90d86400ee8058d464d9d2b1aa344e7d3594be915a056f115
da0e78c3cc66b87884cee2dc3d1e5bc911bf69b07b51fde1dcb14a6fc0b4
948030a28f1661804e1e73e5acfc5a3286a6c629a74b75eb283cece7b110
dbe5ffa6742f1dddb68d7f1ee2dfc408ff70a89107a6e99c2cde99610222
735809096a906a22d6ae394a3dfd0dc7e102f328b86e8a412158387b9e2c
4f85232fd19dbf35c74155422fd2d833317ca6c367d5c1c7159fffabee2d
9fe39e041beb353d3fb8e2913bdb81a29b2be8ac9d1046d09b2b0818ac44
b01b677872fc835f66f992e93bab42009939faff656d0f85bf6bab8edbaf
fd447c442f351c7096dad15e46472810c40b1e274c7f439b68a4e7681309
4a7b682d6c8d4be140d974a79df52bfb8491e868611b73f0ddc6e9f7d3cd
a03622a17f24f2fad46221fb140290b777fdf64836799f0959946fbe5f03
b9a29db444446bc7a9ab2ae8addf2c64c5ac57f6e8c8b1c3b54771d0ee50
5bbf56139c7de996aac1a7d96fbd32e187cae3518decc016338d9e91219c
71e7b83e16efff357d1b511d275ff8d1fb875c29ccdceb1acfa2a4f23a9b
b3a19478461639963694e2cc882ca25d38b03e42433b92514d0961733156
823cbb6131d66d64b2c9d0180a284c9bae03c4c0c11dc493eba807272bae
0b06fb65791162b38814b36a696be75b1d94b617cba1471fd391e3aa1e77
647d331d9285427577fa17bc4fe711f92153c2812e0087771edaff2ff15d
dc4857b8361620b09053eae671657b79f01921b16a94a210f7efdea1de8e
5cbb40cda97e1c044df31ea3010ed7e1bebe7f8ea1e6279becd0ebf8daa8
e488341f8b38e64ec8e4dad5025312bdc8746455a8d676b613308268d744
c7b6e9118a43321d780f6c980e0d1dd69f065059f615efefcb311e2eb809
3539ef62ea6a3348c3c3793cfbdd252f637007e82e9a6c70025a9e550d26
d3347a8cc978e095df2801730063ff72911116c9f71f70c1c8940790ea17
1753e9ad62cc854522f8d2c080c2d080a91a66b0a2eecb5249f1a56be27e
0697841d08db9c8e2a22a889a83ec862c0812c0d0689004f19d84465b493
19a6e9136ab9d5f00e4e00b768cdbc740e3758fc24641e012603d726f289
dfc2492144725f6e30986debb7f5b8cd5c7a8db76a38fbe1e8490825ef97
99a893e685e75355c8ba97422801764d9f4a559727f1ff374ed43165051f
1e3f9cf986dc0ae7ba952784db74a15a151400add37c344476181f427e2b
de5dad6c371c055325209afb4db85c97c5dd83d9c3010a47ae456b416979
dfdbf651382d1e3cbf78413d096de7778b26a75394ce60d66b3cc81e7b98
5383f0856fd3d37d258b445465282f44a6dc4d89a653e09ef75db38af8a5
05e30b461d0d1197f7e92180c03c277f16928f00bc74381380ff8e6bbc09
6267a8ded9fb8a0ad7cfe2c247321f97060a7cce3d73a64a2048455222bd
d462bbe9fdd23653ce6bb92d3046b67a4646e0221bde6740a22bdcd0bbc4
00e3db34d61f37b9d29383bd1344db684ee12d27905e0d5085f5237179f0
706578e77445afc249760fe6d41e774ce05b4de11610c8a06f8ed48cbeba
0336e1de3307a78e36fabc385d9e0171cdc6174f2bff93718fecfa270485
b3b7fa84fc4ebc0c6a1c1fa34eac95b5a0435a4f88e0057e9e9cc5c8bc3c
4bae5beefe950fb12b5ee2983383948baf0b0331f258fb65b3e582d44941
74e753293c544ac3e7fd96d427dc3387d6444853731470d4409ec5b70b01
9c246b3d2ab56765bf46d2a779113282ba06d03620454939ab1b8d71644b
e5d28f8a10ee871c40bae8900f5166dc782bbbb863b832704d1ccf4384ab
f8f356f5481bf63de56d7a132f444b0c991511895e619a8b5c0c074f603f
075d74799267ad8f82eab2fba4795d0663d0e0fa0d2b09db79304b302f30
0b06096086480165030402010420ff06b7f034fd8b4eac2fab2573b07968
a29f953f391bebc1181dbcbe2ebe053f0414498899057c6a06669d38364c
7b26fbbc4beee89e020207d0"""
}

    return cert_contents

# ----------------------------
def logo_xxd():
# ----------------------------
    # downloaded : https://www.mycardinalhealth.net/media/1748/brand-guidelines-2020.pdf
    # then did a screen capture of just the area of the logo, pasted into Word,
    # right click on image: "save as picture" to gif, then did "xxd" of that file.

    # made with "xxd -p cardinal.gif"
    content = """47494638376118017a00e600000000001a1919201e1e2423232827272c2b
2b302e2e3332323837373b3b3bdc3b42403f3fdd4147434242dd454b4847
47de494f4b4a4adf4d54df5057df5259535353e0555c585757e0585e5c5b
5be15b62605f5fe26167646363e2646a686767e3696e6c6b6be36d73706f
6fe47076747373e5747b787777e5787e7c7b7be67d82807f7f828181e781
86e7858ae8878de8898f8d8c8ce88d92e99095969595ea959aeb9a9e9c9c
9bec9da2eda1a5a5a4a4eda5a9a8a7a7eeaaadefadb1aeaeaeb0afafefb1
b4f0b3b7b5b5b5f0b5b8b8b7b7f1b9bdbdbdbdf1bdc1c0bfbff2c1c4c4c4
c4f3c5c8c8c7c7f4cacdccccccf5ced0f6d3d5d4d4d4f7d7d8f7d9dadada
daf8dcdef8dfe0dfe0dfe3e3e3f9e3e4fae7e8fbecededededfceff0f4f4
f4fdf4f4fef7f8ffffff0000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000
0000000000000021f90409000063002c0000000018017a000007ff806282
838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0
a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbe
bfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1cc565cd2d6d79e5c514e4a
5160d8e0e1625c5654568f5c463d3b4cdfe2efd060dc4846de8d51382e35
4eeef0fecc547ce0b021e4dca22d3d4498e8a1e59fc3585aa84499d82f92
951e2a50e4e0a7884b0f0f1a6c507948d21598284688f83042a5a2232735
3488c81145d1951a122ce46858b2e72a793966c8e8e1a4da232d3b3c3868
61c42521231e149870e2b3ea2a2b415a7850d1c35e232d382c486d8ac809
09051a88585d9b4a8b901612ff3cd45062749113150a1cc83068288a0905
127c84614bd814951c5135ec735a48cb0c070a442041144504e020a4ac4c
615c982417217f19982052f710981d1200e7283dc88906b44c3a6d5122c4
0813d6883877bec6c54754a993151179cd4006cf423e202840813b524019
354833d2c244899328c777fbb3220332841953142179ada005df415c5ce4
1522894a0f132270d454a4c5088e1c3da0e8d60e0f8c0ff21af4d09c20e3
29a0800ae709e2040507ee27483a2e4820820f038a210f0e26a8b0c37c91
44e4207fc938f197812d70688810a929e0026b60b480567684f466190814
e606930716cce0c4608e70e184104118615d85203aa3850c0c18a881ff10
8c818103640ee4c0e320423020c148838061846512d800237a41fca5010e
0922e2630f30b40043571f16190d173828a700043634b7055e0a58401521
51482041435ad40099084a20a2c54773ce8065225c3081e10422d8c08417
9680414e145358d1a69bb99c96a202307c2906130c2af042455a5860010c
06a260227abe196842a1896c61c40b62798003149180a1451448f470df0e
3df8a0041544721a8ca706aaf8251832186801875cb818d9ab048e38010e
155a81c36b4b21b1a9185a2881030a123810a912a22adb0c18394066e00b
b83191227b3d40a681063e1472939c24d07ac8143680db426c5f29610309
9059e04241eec2c3c50ccd326083ff4b5c8060606a1260c6850783287196
02c59529481435941ae087516094a20633d01531495a8c08587084902701
6bc3190801b7875c1196810ebc806d2151e420429280b96044b233bfa344
a9521905065ee4994065a912ece0947fbf29e001938968d982bc0a7080c3
a251af0506c5443765d90c623801d9ace441e0b52150b4c034032e1c2d88
1744d82cf60eedb66d5514626d0c81c0567cca400d2ef9479e0213ec30a0
9686638043e28ab305867a066270c52039346b018c5c08daac074624c284
dfcd4a3083e0a16b6784bc0e2871c56f1a88c5a116ac364bc29e86588153
b3cb099c7bc45450dd2c0ee4e66469b4cda2109e21600811b6021868fe7c
ffe81f57bc28150e28d5ac096c0fc21dda07223fbee26068dcac0a8318f1
a90847336159ed409b5feeb8f01a0be0492a51b01f5a9c47a5cb892d7602
7c1e183820956fd4807944db01f77af029a9c82f82a13b0bf506413a03a9
00373ee8a00ada0742c5d940011014040e30d8834220a1712634590ba3c6
0507208f0bff538002e74305054a85853b24df20a690220a50857416681d
f334f0c1240a5009f252413f82d82c07f4cb8a3b34828118c01e42680183
27046318a5d53e277490816a9c9f130c2402d6608f79708c63eeb4001917
34e672d37a81d8f418c1a8d4c0866873c137c02016241272663b50c00807
f142a2a9a5350ab0c12307e880495ae8ff801a28930a34b0c9d059419359
52200a18b305068cab94fc21e0f414514558ce4c96068aa12d770886dff8
7097601cd995806945ec494087c47c1e123686cc64920f4a8e7466ee5060
a05a4a33777354c025af594805c8809b209c23c8c0194115380074e48c18
1818f0455e542103f00c0121aa20857a7ea114598067063a408821644007
8878670944b1840cdca01137c8c0111021851494200925c8821462800c2b
8cb3115da8c2119650852ea8420a0108e9010851809006a008a500694807
40081a0480050c0d400244f183000c94112908004011b10480fe330637bd
4617689000939a9400414da949473a88928614a5a4506900583a0897c2f4
1020ff9d69286a9ad444e474a787e8a918529002a06263084e35aa490580
0aa93255106985ea28a44a55415835a65a0505571bf1d544f4b40b316041
1246700d97aa55ad0668eb52095181063876094a5d694b5f8a579adab410
34a88021faba887b8ac1b3d128825a1b10832148610831d84003142b5257
d075b2573544562ddbd5906e56a73ef9c2018c4a83436481b50178eb2a5e
5b55ca6255a6b42d846d0bc1d992dcc0a81485c417b200da464cd7b79e75
2b25bae05146703711c4b5ab71658b5c4474a1ba89e8ee21f64a88e512a2
b9eaf5c76e458a5ef30e210545156909aa6088071ce000fc1c427ed57b84
0c08200002f8c012b43b880afcf7004f1844ff0c1edc842fe820022b4d41
7c0521851b7420ad11b8017ac32b86bb1e37af83a842080810d208809510
43c570482bf06241b077c2f37d706fc7aad32584600021cd8014e0b104a3
1eb4112c3e6c486bdc809036400746edae61d59ad3d60e22bf0180ac7897
dc64b526a0ba4750724811f0db4190d8c4e445b1189e006400cb589e302e
e90022d0812ef39310ecbd41052a30e33def34a719086e084a30df858a23
06462d3323f23b800620c0a802e0ef20645c00209bd4a345366a5a177b65
936ab9c426b5f461772c882798b40011107500324088330720055f88b5ac
636d6a14eb76bc4f28e98bbf3000119bb9a82f66ef20dc3b882a47571088
2e407da5ffd10193aa5911110841844b2de3cb4e5aad05d84007ee790193
46e0b7f934aa70b1fce9290700013128c208442ddc2a1480068ad681a8a7
2d06578bd9d9848032ab0911e6051442d183184200563b08610b82d882c8
29c109d1641e8883cf337e04c00581855313a2daaf264416123d882fa475
dc9e2eee4ae3cd71417c61c3a00ee9b15d7d803dbbdce54d463186876088
a20e59115f0829680d2e0684f398d4c54dc1c34d7a814b743900eaadf6c2
0501e58813a2049c1604b9451e803b9bdca8585884a9430ae77a871ab695
154417747edb232b02c8eae5b9cf73eaf04240b9abd680f8aa2721d1252c
610197be7648cd9e70931edbc65117c3d4b76cedff619b74e226cfc21396
c0039376ddd5b12dc46c05d184e01e02ca5d3f44acd15ef0c21f3c00b7ad
b118de2e8e4087f4019038c28fc59c74931a7a10cd5e7221b66ef94e87b4
dc26adede1357e038c1bf5f15f2f6ee45b5d5e3108bc003748bef26f1082
0044a01055c8ec7cf30e78dd87de10a40f4795a7ba6c437c41ee2135c001
44ddfa90be5e10b10fc00f241ff8c1a7dcf33ddfbd2096b0e901fcd7f166
0ebe78879fffbc0afcde03f7742bb5001950025087749d677dcc855b6e07
7fd6f07fe6d70872170247e059d5567e01707e62907eeb4708b40772b747
75ba175265260596360034707355807f1ca67fa0c67f2d98576176004350
833668839f06ff753458089c577dca057a0b287ad9070ebc66529ab5088d
c7758570817a978185708001c077621066562675214778231800652677e7
b7824ad88292257c61b76601b8084536003747083d28066a0784efc580f9
e680d6700246a58186907e69280878175218a88153d655885685827785ef
9785bf357621e56f1ec8825e1786fb37866357575e25876bd886d7d78070
670d59206a06908786607a0190871b477d62506d1a487b6af600ed47887f
f883242806a3588683f05c5fd888530576274608aa48738a90537f2708a3
9876f0b77670380843180e81b85231206962d0053a706750f8772c10654d
6887d3d781a3276e84e07eadd85ebb9773269586b756ff8b903786a0f665
8a8068fb4608d388803ee88d97188799780de0b752e367654060541d7004
3a508f7d6808533600214003a02888dc987baea88582008a05c0024bc002
581600c0e7882f688e5d50527097865b978799a690ef380840c68c7d2784
72780d5f00854a5657be1752a8267fa5e87ade7774465502965600dbc88a
08e98daf688b6a058a56578eb948084b006409e09043406842077b014000
21f0033a60608e1500ccc873cd96003ff00437d75cc65892d8b00411a956
839005f5f86ded188ad578085d508f19308e9278902165888370049b4659
4ef57c60788b6218941ef8686a05745d907e4e96051b1000f4c673529056
d1a5954cc795e090b1053dc5022b7003472005e8f5043a10033f20695970
049ca95e4fc09947807229f603a595756b069a42099aea8505a0e989a059
8184f00547400334109aa5c6999f269bb84908ac2992b1b95101359b5729
9a52f003343004922605b6098c9369085d300434a00392a65188b7999e98
4ed8999ddab99ddcd99ddef99de0199ee2399ee4599ee6799ee8999eeab9
9eecd99eeef99ef0199ff2399ff4599ff6799ff8999ffab99ffcd99ffef9
9f001a0b8100003b"""

    return content


other_content_to_convince_runner_we_are_a_service = """
# ===== begin: service file
# ===== begin: start file
"""

import os
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_settings.cpython-37.pyc", "/cardinal/pi_settings.pyc")

    if os.path.isfile("/cardinal/pi_settings.py"):
        os.remove("/cardinal/pi_settings.py")

    try:
        with open('/dev/shm/pi_settings_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    wake_count = 0
    while True:
        # do system maintenance

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_settings_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print ("!!! failed to write wake_count")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))

    def test_settings(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)

