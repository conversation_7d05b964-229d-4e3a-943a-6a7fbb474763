_ = """
sudo vi /cardinal/pi_network.py

sudo systemctl restart pi-network.service

"""

service = 'network'
version = 'N.3.1'

release_notes = """
2021.11.09
N.3.1
Fix the reporting, to correctly show wlanX, when connected.
Add support to allow wlan1 (external wifi adapter) to be configured

2021.11.03
N.2.1
Add to the network report: the adapter providing the access (wlan0, wlan1, eth0, ...).

2021.07.23
N.2.0
Add support for cah-iot wifi connection, in addition to corp wifi connection.

2021.05.15
N.1.2
Move the bookmarks update out of here, and into runner.

2021.05.14
N.1.1
If wired, delete all wireless connections.
If not wired, add corp connection.

2021.05.11
N.1.0
Get ready to be managed that we can be connected to corp, depending on not being on wired,
But not active yet. Save this as a 'corp still manually managed' version.

2021.05.10
N.0.6
Correct the corp certificate creation. (The ascii was emitting slash character which
was acting as an escape character, and causing incorrect results)

2021.05.10
N.0.5
Make the timeout be 15 seconds, instead of 3, for talking to <PERSON>licer.
The Slicer response is now calculated, instead of a direct lookup.
Put the manage_network_connections into play

2021.05.06
N.0.4
Add the parsing of the datadrop response, looking for data that we need to deal with.
Bookmarks

2021.04.20
N.0.1
Borrow from the monitor 1.1 file, and adapt to be a network monitor, reporter, and
perhaps even a controller of the network settings / connectivity.

"""

other_content = """
sudo vi /cardinal/pi-network
sudo chmod +x /cardinal/pi-network

# ===== begin: start file
#!/usr/bin/env python3
import pi_network
pi_network.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-network.service
sudo systemctl daemon-reload
sudo systemctl stop pi-network.service
sudo systemctl start pi-network.service
sudo systemctl enable pi-network.service
systemctl status pi-network.service

sudo systemctl restart pi-network.service

systemctl status pi-network.service

# Logging of std out
cat /var/log/syslog | fgrep pi-network

OR

tail -n 1000 -f /var/log/syslog | fgrep pi-network

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-network
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

import json
import os
import requests
import shutil
import socket
import subprocess
import sys
import time

crypt_cahiot_preshare = 'OiVzHo1dGH'
pass_count = 0

# ----------------------------
def do_one_command(command):
# ----------------------------
    import shlex
    command_splits = shlex.split(command)
    #command_splits = command.split(" ")

    doit = subprocess.Popen (command_splits, universal_newlines=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def manage_network_connections():
# ----------------------------
    _ = """
wireless:
nmcli -t c s
level2-ng:7d687a6d-51b5-4660-9fb1-c69d2f2eebdd:802-11-wireless:wlan0
corp0:b0fe05cf-8250-48e5-a4cc-d9868a75d759:802-11-wireless:
Wired connection 1:8898c2db-ba58-36ac-9f64-7b9e4b6c142e:802-3-ethernet:

wired:
nmcli -t c s
Wired connection 1:467d59c0-89e2-3f98-9b93-6f8c24941aca:802-3-ethernet:eth0
corp0:5bafe10a-d110-41aa-af52-1ec174cab11e:802-11-wireless:

    """

    global pass_count
    pass_count += 1

    debug_string = str(pass_count) + "\n"

    devices_report, fails = do_one_command('nmcli -t')

    debug_string += 'start' + '\n'
    write_debug_string(debug_string)

    if 'wlan1' in devices_report:
        notes = """
        Make this just '1' or '0', then we can look for 'corp'+'1', and remove it, when configured for '0'
        """
        wireless_to_configure = '1'
    else:
        wireless_to_configure = '0'

    debug_string += 'wireless_to_configure = ' + wireless_to_configure + '\n'
    write_debug_string(debug_string)

    connection_list, fails = do_one_command('nmcli -t c s')
    print ("scan:", connection_list, fails)

    debug_string += connection_list + '\n'
    write_debug_string(debug_string)

    list_to_remove = []
    is_wired = False
    is_wifi_configured = False

    splits = connection_list.split('\n')

    for item in splits:
        parts = item.split(':')
        if len(parts) > 3:
            connection_name = parts[0]
            connection_type = parts[2]
            connection_port = parts[3]

            if 'Wired' in connection_name:
                if connection_port:
                    is_wired = True
                    print ("Is Wired")

    for item in splits:
        parts = item.split(':')
        if len(parts) > 3:
            connection_name = parts[0]
            connection_type = parts[2]
            connection_port = parts[3]

            if 'wireless' in connection_type:
                if is_wired:
                    # remove all wireless
                    list_to_remove.append(connection_name)
                else:
                    # remove all wireless but corp and cah-iot
                    remove_it = True
                    if 'corp' in connection_name:
                        if 'corp' + wireless_to_configure in connection_name:
                            remove_it = False

#                    if 'cah-iot' in connection_name:
#                        remove_it = False

                    # be able to transition to the other adapter
                    if connection_port:
                        if not 'wlan' + wireless_to_configure in connection_port:
                            remove_it = True

                    if remove_it:
                        list_to_remove.append(connection_name)
                    else:
                        is_wifi_configured = True
                        print ("Is wifi Configured")

    for connection_name in list_to_remove:
        result, fails = do_one_command("sudo nmcli con delete \"" + connection_name + "\"")
        print ("delete connection:", connection_name, result, fails)

    debug_string += 'is_wired = ' + str(is_wired) + '\n'
    write_debug_string(debug_string)
    debug_string += 'is_wifi_configured = ' + str(is_wifi_configured) + '\n'
    write_debug_string(debug_string)


    # if not on wired, then add the allowed networks
    if not is_wired and not is_wifi_configured:
#        print ("Try to add cah-iot")
#        set_wifi_command = """sudo nmcli connection add type wifi con-name "cah-iot" ifname wlan""" + wireless_to_configure + """ ssid "cah-iot" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "password" """

#        preshare = rot13b(crypt_cahiot_preshare)
#        set_wifi_command = 'sudo nmcli connection add type wifi con-name "cah-iot" ifname wlan0 ssid "cah-iot" -- wifi-sec.key-mgmt wpa-psk wifi-sec.psk "' + preshare + '" '
#        pass_string, fails = do_one_command(set_wifi_command)

        debug_string += 'Trying to add corp...'
        write_debug_string(debug_string)

        set_wifi_command = """sudo nmcli connection add type wifi con-name "corp""" + wireless_to_configure + """\" ifname wlan""" + wireless_to_configure + """ ssid "corp" -- wifi-sec.key-mgmt wpa-eap 802-1x.eap tls 802-11-wireless.hidden yes 802-1x.identity "<EMAIL>" 802-1x.private-key-password "NgsA7cR3fqf?qnpA" 802-1x.private-key /cardinal/TempCert.p12"""
        pass_string, fails = do_one_command(set_wifi_command)

        debug_string += ' done' + '\n'
        write_debug_string(debug_string)

        _  = """
        print ("Set priorities")
        set_wifi_command = 'nmcli connection modify "cah-iot" connection.autoconnect-priority 10'
        pass_string, fails = do_one_command(set_wifi_command)
        set_wifi_command = 'nmcli connection modify "corp0" connection.autoconnect-priority -10'
        pass_string, fails = do_one_command(set_wifi_command)
        """

    # Check priority:
    # https://askubuntu.com/questions/165679/how-to-manage-available-wireless-network-priority
    # nmcli -f autoconnect-priority,name c

# ----------------------------
def write_debug_string(debug_string):
# ----------------------------
    try:
        with open('/dev/shm/pi_network_manage_debug.txt', 'w') as f:
            f.write(debug_string)
    except:
        pass


# ----------------------------
def get_wifiscan():
# ----------------------------
    results = {}

    # if we are wired, then there is no wifi to report
    results['ssid'] = '(not_on_wifi)'
    results['chan'] = ''
    results['signal'] = ''
    results['cah-iot'] = 'No'
    results['lan'] = 'dne'


    connection_list, fails = do_one_command('nmcli -t c s')
    #corp0:1669a28e-9c4c-42ff-aa9f-7ff89bc956fd:802-11-wireless:wlan0
    #Wired connection 1:1acbc619-206d-3148-9ca9-50e49677f612:802-3-ethernet:
    splits = connection_list.split('\n')
    for item in splits:
        parts = item.split(':')
        if len(parts) > 3:
            if parts[3]:
                results['lan'] = parts[3]

    # do a scan, and force it to actually do the scan, not just return from cache
    result, fails = do_one_command('nmcli -c no device wifi list --rescan yes')

    for line in result.split('\n'):
        if line:
            if 'cah-iot' in line:
                results['cah-iot'] = 'Yes'
            is_the_inuse = False
            if line[0] == '*':
                is_the_inuse = True
                line = ' ' + line[1:]

            splits = line.split()
            #print (splits)

            if is_the_inuse:
                if len(splits) > 2:
                    results['ssid'] = splits[0]
                    results['chan'] = splits[2]
                if len(splits) > 5:
                    results['signal'] = splits[5]

    return results

# ----------------------------
def get_serial():
# ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial

# ----------------------------
def load_wifi_cert():
# ----------------------------
    # made with "xxd -p TempCert2.p12"
    content = """30821cec02010330821ca806092a864886f70d010701a0821c9904821c95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"""


    output_file = "/cardinal/TempCert.xxd"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    # make back into p12 file
    content = """
#!/usr/bin/env sh
cat /cardinal/TempCert.xxd | xxd -p -r >/cardinal/TempCert.p12
sudo rm /cardinal/TempCert.xxd
    """
    output_file = "/cardinal/pi_network_tempscript"
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))
    with open(output_file, 'w') as f:
        f.write(content)

    pass_string, fails = do_one_command('sudo chmod +x /cardinal/pi_network_tempscript')
    pass_string, fails = do_one_command('sudo /cardinal/pi_network_tempscript')

    # clean up
    pass_string, fails = do_one_command('sudo rm /cardinal/pi_network_tempscript')

# ====================================
def rot13b(s):
# ====================================
    chars = "abcdefghijklmnopqrstuvwxyz9876543210ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    trans = chars[13:]+chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c)>-1 else c
    return ''.join( rot_char(c) for c in s )

# ----------------------------
def do_one_time():
# ----------------------------
    load_wifi_cert()


# ----------------------------
def main():
# ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_network.cpython-37.pyc", "/cardinal/pi_network.pyc")

    print ('version=' + version)

    if os.path.isfile("/cardinal/pi_network.py"):
        os.remove("/cardinal/pi_network.py")

    do_one_time()

    daemon = True

    time_of_last_send = 0

    try:
        with open('/dev/shm/pi_network_version.txt', 'w') as f:
            f.write(version)
    except:
        print ("!!! failed to write version string")

    last_channel = ''
    serial = get_serial()

    while True:
        time_of_start_of_scan = time.time()
        need_to_send = False

        if True:
            # this performs the side effect of forcing an actual rescan of the ssid choice.
            results = get_wifiscan()

            # write to local store, for others to use (hmi)
            try:
                with open('/dev/shm/pi_network_scan.txt', 'w') as f:
                    f.write(json.dumps(results))
            except:
                pass

            time_now = time.time()
            if abs(time_now - time_of_last_send) > 60 * 30: # 1800 seconds
                need_to_send = True

            if abs(time_now - time_of_last_send) > 60:
                if 'chan' in results:
                    if last_channel != results['chan']:
                        last_channel = results['chan']
                        need_to_send = True

            the_data = []
            the_data.append('source=' + service)
            the_data.append('serial=' + serial)
            the_data.append('version=' + version)

            for key in results:
                the_data.append(key + "=" + str(results[key]))

            the_report_url = 'https://slicer.cardinalhealth.net/datadrop?' + ','.join(the_data)

            if need_to_send:
                time_of_last_send = time_now


                # check in with slicer
                try:
                    r = requests.get(the_report_url, verify=False, timeout=15.0)
                    url_result = r.text

                except:
                    url_result = 'exception'


                # url_result = '{"bookmarks":{"10000000e3669edf":{"1": {"title": "EDHR", "url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login","whitelist":["edhr-na-jz.cardinalhealth.net"]}}}}'
                try:
                    result_json = json.loads(url_result)

                except:
                    print ("url_result was not a valid json")

            # save locally, for testing
            try:
                with open('/dev/shm/pi_network_datadrop.txt', 'w') as f:
                    f.write(the_report_url)
                    f.write('\n\n' + str(url_result))
                print (the_report_url)
            except:
                print ("!!! failed to write datadrop string")

                _ = """
look with:
watch cat /dev/shm/pi_network_datadrop.txt


                """

            try:
                manage_network_connections()
            except:
                pass



        if not daemon:
            break

        while (abs(time_of_start_of_scan - time.time()) < 4):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)



