# A deviceupload for slicer page services

service = 'deviceupload'
version = service + '.0.2'

_ = """
This file gets loaded to:
/var/www/html/deviceupload.py

using:
sudo vi /var/www/html/deviceupload.py

It also requires:

sudo vi /etc/httpd/conf.d/python-deviceupload.conf
----- start copy -----
WSGIScriptAlias /deviceupload /var/www/html/deviceupload.py
----- end copy -----

sudo chown apache:apache /var/www/html/deviceupload.py

sudo systemctl restart httpd


test on Slicer server with:
cd /var/www/html
sudo python -c "import deviceupload; print(deviceupload.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/deviceupload



"""

import cgi
import copy
import datetime
import hashlib
import json
import os
import shutil
import stat
import sys
import time
import traceback
import unittest

import shlex
import subprocess

from tempfile import TemporaryFile

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_deviceupload_path = service_config['base_deviceupload_path']
    checkin_file_root = service_config['checkin_file_root']


except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import address2location
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def does_device_have_multimedia_profile(device_id, data_store_content):
    # ----------------------------
    return_value = False

    key = 'device_profile_' + device_id
    if key in data_store_content:
        if '_multimedia_' in data_store_content[key]:
            return_value = True

    return return_value


# ----------------------------
def do_atomic_write_if_different(output_file, content, binary=False):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content, binary=False):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'

    if binary:
        with open(temp_name, 'wb') as f:
            f.write(content)
    else:
        try:
            with open(temp_name, 'w') as f:
                f.write(content)
        except:
            with open(temp_name, 'wb') as f:
                f.write(content)

    shutil.move(temp_name, output_file)  # this is where the atomic activity occurs


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if environ['REQUEST_METHOD'] == 'POST':
        body, other = make_body_POST(environ)
        permissions.log_page_allowed(environ, service, other)

    if permissions.permission_prefix_allowed(environ, 'deviceupload_'):
        try:
            if environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
                permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_device_content_md5_filename(single_id):
    # ====================================
    return 'showgif_' + single_id + '.gif'


# ====================================
def get_device_content_md5(single_id):
    # ====================================
    return_value = ''

    file_name = 'showgif_' + single_id + '.gif'
    output_file = base_deviceupload_path + file_name
    md5_file = output_file + '.md5'

    try:
        return_value = open(md5_file, 'r').read()
    except:
        pass

    return return_value


# ====================================
def save_device_content(single_id, source_original_name, file_content):
    # ====================================
    file_name = 'showgif_' + single_id + '.gif'
    output_file = base_deviceupload_path + '/' + file_name

    do_atomic_write_if_different(output_file, file_content, binary=True)

    source_name_file = output_file + '.source.txt'
    do_atomic_write_if_different(source_name_file, source_original_name)

    # build the md5sum
    command = 'md5sum ' + output_file
    md5_file = output_file + '.md5'
    mem_string, fails = do_one_command(command)

    # md5 file is the trigger to the device, that new content is here for them to grab
    do_atomic_write_if_different(md5_file, mem_string.split()[0])


# ====================================
def make_body_POST(environ):
    # ====================================
    # https://stackoverflow.com/questions/14355409/getting-the-deviceupload-file-content-to-wsgi
    # https://stackoverflow.com/questions/14544696/python-simple-wsgi-file-deviceupload-script-what-is-wrong/14590585

    body = ''

    body += 'method = POST<br>'

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)
    # body += str(field_storage) # FieldStorage(None, None, [FieldStorage('file', 'ChromeZoom.xlsx', 'PK\x03\x...
    # FieldStorage(None, None, [MiniFieldStorage('file_download_allowed', 'testfile1.txt'), MiniFieldStorage('file_download_allowed_allowed', 'Yes')])

    try:
        file_name = field_storage.getvalue('file_download_allowed')
        allow_to_allow = field_storage.getvalue('file_download_allowed_allowed')
        datastore.set_value('download_permission_' + file_name, allow_to_allow)
    except:
        pass

    try:
        file_name = field_storage.getvalue('file_delete_allowed')
        allow_to_allow = field_storage.getvalue('file_delete_allowed_key')
        if 'delete_it_now' == allow_to_allow:
            try:
                os.remove(base_deviceupload_path + file_name)
            except:
                pass
            try:
                os.remove(base_deviceupload_path + file_name + '.md5')
            except:
                pass
    except:
        pass

    try:
        if len(field_storage.list):
            for item in field_storage.list:
                if item.filename:
                    body += 'filename, ' + str(item.filename)

                    if item.name:  # This is what we made the name, no matter what the original file name was
                        single_id = str(item.name)
                        source_original_name = str(item.filename)
                        file_content = item.file.read()  # binary, so don't .decode('utf-8')

                        save_device_content(single_id, source_original_name, file_content)

        #                    body += ', ' + str(len(file_content))
        #                    body += ', ' + str(len(output_file)) + '<br>'

        else:
            return str(field_storage)
    except:
        open('/dev/shm/running_exceptions_' + service, 'w').write('pass_count : ' + str(pass_count) + ' -> ' + str(
            traceback.format_exc().replace("\n", "<br>").replace("\"",
                                                                 "'")))  # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))
        return traceback.format_exc().replace("\"", "'")

    #    return body + '<br>' + make_get_content(environ)
    return make_get_content(environ)


# ====================================
def make_body_GET(environ):
    # ====================================
    return make_get_content(environ)


# ====================================
def get_deviceupload_files_and_base(filter=''):
    # ====================================
    return_value = []

    if filter:
        found = os.listdir(base_deviceupload_path)
        for item in found:
            if filter in item:
                if not '.md5' in item:
                    return_value.append(item)
    else:
        return_value = os.listdir(base_deviceupload_path)

    return sorted(return_value), base_deviceupload_path


# ====================================
def has_correct_permissions(environ, singleID):
    # ====================================
    query = environ['QUERY_STRING']

    query_items = {}
    query_items_order = []
    for item in query.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]
            query_items_order.append(parms[0])

    single_id = ''
    if 'serial' in query_items:
        single_id = query_items['serial']

    base_path = checkin_file_root
    checkin_file = base_path + '/' + single_id
    try:
        with open(checkin_file, 'r') as f:
            checkin = json.loads(f.read())
        site = address2location.location(checkin['address'])
    except:
        site = ''

    return permissions.permission_allowed(environ, 'device_edit') and (
                permissions.permission_site_allowed(environ, site) or permissions.permission_site_allowed(environ,
                                                                                                          '(all)'))


# ====================================
def make_get_content(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    query = environ['QUERY_STRING']

    query_items = {}
    query_items_order = []
    for item in query.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]
            query_items_order.append(parms[0])

    single_id = ''
    if 'serial' in query_items:
        single_id = query_items['serial']

    if has_correct_permissions(environ, single_id):
        #    if permissions.permission_allowed(environ, 'device_edit') and (permissions.permission_site_allowed(environ, site) or permissions.permission_site_allowed(environ, '(all)')):
        body = ''

        if not single_id:
            body += """
    <center>
    Page must call for one serial number device.
    </center>

            """

        else:
            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            name_to_show = "Home"
            url_to_use = make_home_url_from_environ(environ)
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            try:
                body += "<br><br>"

                create_permission = permissions.permission_allowed(environ, 'deviceupload_create')
                read_permission = permissions.permission_allowed(environ, 'deviceupload_read')
                update_permission = permissions.permission_allowed(environ, 'deviceupload_update')
                delete_permission = permissions.permission_allowed(environ, 'deviceupload_delete')

                body += '<center>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'

                if create_permission or update_permission:
                    body += '<td>'
                    body += 'upload'
                    body += '</td>'

                body += '<td>'
                body += 'file'
                body += '</td>'
                body += '<td>'
                body += 'size'
                body += '</td>'
                if read_permission:
                    body += '<td>'
                    body += 'view'
                    body += '</td>'
                    body += '<td>'
                    body += 'download'
                    body += '</td>'
                #            body += '<td>'
                #            body += 'Available for download<br>on the downloads page'
                #            body += '</td>'
                if delete_permission:
                    body += '<td>'
                    body += 'Delete option<br>(must not be available for download)'
                    body += '</td>'
                body += '<td>'
                body += 'md5'
                body += '</td>'
                body += '<td>'
                body += 'Modified'
                body += '</td>'
                body += '<td>'
                body += 'Days Old'
                body += '</td>'

                body += '</tr>'

                base_to_use = base_deviceupload_path
                #                files = ['showgif_' + single_id + '.gif']
                single_ids = [single_id]
                data_store_content = datastore.all_datastore()

                for single_id in single_ids:
                    file_name = 'showgif_' + single_id + '.gif'
                    if not '.md5' in file_name:
                        body += '<tr>'

                        if create_permission or update_permission:
                            body += '<td>'
                            if does_device_have_multimedia_profile(single_id, data_store_content):
                                body += '(Media uploads are managed in multimedia section, based on the profile)'
                            else:
                                body += """
                        <form id="deviceupload" name="deviceupload" method=post enctype=multipart/form-data>
                        <input type=file name=""" + single_id + """>"""
                                body += """
                        <input type=submit value=upload>
                        </form>
                        """
                            body += '</td>'

                        body += '<td>'
                        # body += file_name
                        source_file_name = base_deviceupload_path + file_name + '.source.txt'
                        try:
                            source_file_content = open(source_file_name, 'r').read()
                            # body += '<br>
                            body += source_file_content
                        except:
                            pass
                        body += '</td>'

                        body += '<td>'
                        body += make_file_size_human_readable(base_to_use + file_name)
                        body += '</td>'

                        is_allowed = False
                        result = datastore.get_value('download_permission_' + file_name)
                        if result == 'Yes':
                            is_allowed = True

                        if is_allowed:
                            the_string = "Yes"
                            next_Value = "No"
                            color = "(0, 255, 0, 0.3)"
                        else:
                            the_string = "No"
                            next_Value = "Yes"
                            color = "(255, 0, 0, 0.3)"

                        if read_permission:
                            body += '<td>'
                            file_extensions = ['.txt', '.png', '.gif']
                            if file_name[-4:] in file_extensions:
                                # the target="_blank" makes the click open a new tab for the result
                                body += '<a href="' + url_to_use + '/download' + '?filetoview=' + file_name + '" target="_blank"> ' + 'view' + '</a>'
                            body += '</td>'
                            body += '<td>'
                            body += '<a href="' + url_to_use + '/download' + '?filetodownload=' + file_name + '"> ' + 'download' + '</a>'
                            body += '</td>'

                        _ = """
                        body += '<td style="background-color:rgba' + color + '">'
                        if permissions.permission_allowed(environ, 'deviceupload_edit'):
                            body += '<form method="post" action="">'
                            body += '<select name="file_download_allowed" id="file_download_allowed" hidden>'
                            body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                            body += '</select>'

                            body += '<select name="file_download_allowed_allowed" id="file_download_allowed_allowed" hidden>'
                            body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                            body += '</select>'
                            body += '<center>'
                            body += '<input type="submit" value="' + the_string + '">'
                            body += '</center>'
                            body += '</form>'
                        body += '</td>'
                        """

                        if delete_permission:
                            body += '<td>'
                            if not is_allowed:
                                next_Value = 'delete_it_now'
                                body += '<form method="post" action="">'
                                body += '<select name="file_delete_allowed" id="file_delete_allowed" hidden>'
                                body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                                body += '</select>'

                                body += '<select name="file_delete_allowed_key" id="file_delete_allowed_key" hidden>'
                                body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                                body += '</select>'
                                body += '<center>'
                                body += '<input type="submit" value="' + next_Value + '">'
                                body += '</center>'
                                body += '</form>'
                            body += '</td>'

                        body += '<td>'
                        md5_file = base_to_use + file_name + '.md5'
                        md5_value = ''
                        if os.path.isfile(md5_file):
                            with open(md5_file, 'r') as f:
                                md5_value = f.read()
                        body += md5_value
                        body += '</td>'

                        body += '<td>'
                        days_old = -1
                        try:
                            # file_to_use = '/mnt/disks/SSD/var/log/slicer/deviceupload/files/slideshow.gif'
                            file_to_use = base_to_use + file_name
                            fileStatsObj = os.stat(file_to_use)
                            last_modified_time = fileStatsObj[stat.ST_MTIME]
                            days_old = int((time.time() - last_modified_time) / 60.0 / 60.0 / 24.0)
                            body += datetime.datetime.fromtimestamp(last_modified_time).strftime('%Y.%m.%d')

                        except:
                            body += file_to_use

                        body += '</td>'

                        body += '<td>'
                        body += str(days_old)
                        body += '</td>'

                        body += '</tr>'

                body += '</table>'
                body += '</center>'

            except Exception as e:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    else:
        body = 'You do not have permissions for this device'

    return body, other


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n<body>\n'

    try:
        body, other = make_body(environ)
        html += body
    except Exception as e:
        html += 'exception: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_does_device_have_multimedia_profile(self):
        data_store_content = {'device_profile_10000000e3669edf': 'profile_VPN1_nonmultimedia_demo'}
        device_id = '10000000e3669edf'
        expected = False
        actual = does_device_have_multimedia_profile(device_id, data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'device_profile_10000000e3669edf': 'profile_VPN1_multimedia_demo'}
        device_id = '10000000e3669edf'
        expected = True
        actual = does_device_have_multimedia_profile(device_id, data_store_content)
        self.assertEqual(expected, actual)

    def test_make_device_content_md5_filename(self):
        single_id = '10000000e3669edf'
        expected = 'showgif_10000000e3669edf.gif'
        actual = make_device_content_md5_filename(single_id)
        self.assertEqual(expected, actual)

# End of file content
