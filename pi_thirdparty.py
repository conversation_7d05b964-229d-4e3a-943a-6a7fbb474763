_ = """
sudo vi /cardinal/pi_thirdparty.py

sudo systemctl restart pi-thirdparty.service

watch -n 1 sudo cat /dev/shm/pi_thirdparty_datadrop.txt

cat /cardinal/log/pi_thirdparty_lastupdate.txt

"""

service = 'thirdparty'
version = 'P.0.1'
description = """
This is a pi service thirdparty.

"""

release_notes = """
2023.05.10
P.0.1

Build a service to run on the pi, that can pull down thirdparty apps,
install them on the pi, and report version and status of the app.

This is an optional application, that has no effect if there are no
third party apps targeted to the device.


"""

other_content = """
sudo vi /cardinal/pi-thirdparty
sudo chmod +x /cardinal/pi-thirdparty

# ===== begin: start file
#!/usr/bin/env python3
import pi_thirdparty
pi_thirdparty.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-thirdparty.service
sudo systemctl daemon-reload
sudo systemctl stop pi-thirdparty.service
sudo systemctl start pi-thirdparty.service
sudo systemctl enable pi-thirdparty.service

systemctl status pi-thirdparty.service

sudo systemctl restart pi-thirdparty.service

# thirdparty of std out
cat /var/log/syslog | fgrep pi-thirdparty

OR

tail -f /var/log/syslog | fgrep pi-thirdparty

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-thirdparty
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_thirdparty


"""

import copy
import json
import math
import os

try:
    import requests
except:
    pass  # for unittest
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest

s_minutes_between_datadrops = 5


# ----------------------------
def save_shared_counts(the_counts):
    # ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_' + service + '_' + the_count
            do_atomic_write_if_different(file_name, str(the_counts[the_count]))
        except:
            pass


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    did_write = False

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        # flush all to disk
        #        os.sync()

        shutil.move(temp_name, output_file)

        did_write = True

    return did_write


# ----------------------------
def get_log_request_from_do_datadrop_response(result_json):
    # ----------------------------

    return ''


# ----------------------------
def do_one_command(command):
    # ----------------------------
    import shlex
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def do_one_time():
    # ----------------------------
    list_of_cmds = []
    #    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass


# ----------------------------
def get_serial():
    # ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial


# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
    # ----------------------------
    the_file = '/dev/shm/pi_' + service + '_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')


# ----------------------------
def call_home_locations():
    # ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'", '"'))
    except:
        pass

    return response


# ----------------------------
def thirdparty_apps_found_to_report(cardinal_thirdparty_dir_dict):
    # ----------------------------
    return_value = ''

    for key in cardinal_thirdparty_dir_dict.keys():
        if return_value:
            return_value += '|'
        return_value += key + '_' + cardinal_thirdparty_dir_dict[key]['status'].replace(' ', '').replace('\n',
                                                                                                         '') + '_' + \
                        cardinal_thirdparty_dir_dict[key]['version'].replace(' ', '').replace('\n', '')

    return return_value


# ----------------------------
def do_datadrop():
    # ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug(service + ' data drop: Start', True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        the_thirdparty_report_value = ''
        try:
            cardinal_thirdparty_dir_list = os.listdir('/cardinal/thirdparty')
            cardinal_thirdparty_dir_dict = {}
            for item in cardinal_thirdparty_dir_list:
                try:
                    status_found, fail = do_one_command('/cardinal/thirdparty/' + item + ' --status')
                    version_found, fail = do_one_command('/cardinal/thirdparty/' + item + ' --version')
                    cardinal_thirdparty_dir_dict[item] = {'status': status_found, 'version': version_found}
                except:
                    pass
            the_thirdparty_report_value = thirdparty_apps_found_to_report(cardinal_thirdparty_dir_dict)
            the_data.append('thirdparty=' + the_thirdparty_report_value)
        except:
            pass

        save_shared_counts({'report': the_thirdparty_report_value})

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in with slicer
            try:
                do_datadrop_debug('Start ' + service + '...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug(service + ' result: ' + url_result)

                try:
                    result_json = json.loads(
                        url_result)  # This will throw exception if the previous block passed 'exception'

                    if 'thirdparty_list' in result_json:
                        pass
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())

    do_datadrop_debug(service + ' data drop: End')


# ----------------------------
def do_maintenance():
    # ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()


# ----------------------------
def main():
    # ----------------------------
    if False:
        run_original_main_thread()
    else:
        from apscheduler.schedulers.background import BackgroundScheduler
        sched = BackgroundScheduler(daemon=True)  # Set as a daemon so it will be killed once the main thread is dead.
        job_id_thread1 = sched.add_job(run_original_main_thread, 'interval', seconds=1, coalesce=True)
        job_id_thread2 = sched.add_job(run_extra_main_thread, 'interval', seconds=1, coalesce=True)
        sched.start()
        while True:
            time.sleep(1)
        sched.shutdown()


# ----------------------------
def run_extra_main_thread():
    # ----------------------------
    try:

        # Do one cycle worth, and return; we will get called again
        pass

    except:
        # don't die on something silly
        pass


# ----------------------------
def run_original_main_thread():
    # ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string for " + service + ": " + version)

    do_one_time()

    # For two different working examples of having many worker threads:
    #    look to pi_bluetooth, in this same spot (apscheduler)
    #    look to pi_network, in this same spot (built in threading)

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * s_minutes_between_datadrops):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_' + service + '_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print("!!! failed to write wake_count")


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_myself(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_thirdparty_apps_found_to_report(self):
        cardinal_thirdparty_dir_dict = {'testapp_1.0.2': {'status': 'hello', 'version': '1.0.0'}}
        expected = 'testapp_1.0.2_hello_1.0.0'
        actual = thirdparty_apps_found_to_report(cardinal_thirdparty_dir_dict)
        self.assertEqual(expected, actual)

        cardinal_thirdparty_dir_dict = {'testapp_1.0.2': {'status': 'hello', 'version': '1.0.0'},
                                        'testplay_2.0.2': {'status': 'hello again\n', 'version': '1. 0. 2\n'}}
        expected = 'testapp_1.0.2_hello_1.0.0|testplay_2.0.2_helloagain_1.0.2'
        actual = thirdparty_apps_found_to_report(cardinal_thirdparty_dir_dict)
        self.assertEqual(expected, actual)

    def test_get_log_request_from_do_datadrop_response(self):
        result_json = {}
        expected = ''
        actual = get_log_request_from_do_datadrop_response(result_json)
        self.assertEqual(expected, actual)

        result_json = {"thirdparty_log": {"testapp": "cat /var/log/testapp.log"}}
        expected = ''
        actual = get_log_request_from_do_datadrop_response(result_json)
        self.assertEqual(expected, actual)

        _notes = """
Do not actually use this yet.

Instead, use a collections device command, and then look for the results in the
upload list. Treat it like a debug request, until we figure out exactly what
logs are needed, and then hard code for that later.


        """

# end of file
