Slicer filesystem usage/layout:

/var/www/html
    This is the location of all of the WSGI apps

/var/www/html/pi_services/(app)/(version)/(app).py
    This is the location of the versioned files, to be pulled by pi_runner, when needed.
    These are placed here manually to begin with.
    These are pulled by the pi using api calls to the slicer app "download".

/var/www/htmlfiles
    This is the location of any statically served content
    (none as of 2021.05.21)
    This was created, so that Apache could be configured away from
    /var/www/html as the point of serving files

/var/log/slicer/(app)
    Each slicer app (WSGI started python module) should use its own directory here
    to save whatever it needs.

    checkin:
        /json
        /raw

    datadrop:
        /json/id
            /(id)
                /(service)
                    (content of the most recently received json from this id)
                        like: sudo cat /var/log/slicer/datadrop/json/id/10000000bd6f7a19/runner
                        gives: {"service:pi_monitor": "M.2.2", "temperature": "61.3", "Memory:Cached": "680194048", "service:pi_runner": "R.1.4", "wlan0mac": "dc:a6:32:39:54:a3", "image": "2.0.8", "hostname": "cah-rp-10000000bd6f7a19", "Memory:MemFree": "2920873984", "Memory:MemTotal": "4013187072", "service:pi_hmi": "H.1.2", "source": "runner", "Memory:MemAvailable": "3252744192", "version": "R.1.4", "service:pi_network": "N.1.2", "time": 1624295415.814557, "timezone": "America/Chihuahua", "serial": "10000000bd6f7a19", "eth0mac": "dc:a6:32:39:54:a2", "Memory:Buffers": "53133312"}

        /raw/(YYYYMMDD)/(HH)/(YYYYMMDDHHMMSSmmmuuu)_(id).txt
            like: /var/log/slicer/datadrop/raw/20210526/08/20210526085941378807_100000004e61fc41.txt

            and for rollups:
            like: /mnt/disks/SSD/var/log/slicer/datadrop/raw/20220728/00/rollup_20000000cf60766f

            statistical reports (built in datadrop.build_all_statistics_serial):
            like: /mnt/disks/SSD/var/log/slicer/datadrop/stats/id/10000000fc5317fa


    datastore: Key value pairs
        (files, one per 'key', with the contents of the file being the json dumped 'value'.)

    login:
        /keep
        /throw
        /touch
        /user

    tasks:
        /status_summary
        /(directory datetime stamp)
            /request (a text file with the dumped json of the request content)
                Created by the reports module, dropping the request here, to kick off the process
            /status (a text file with the dumped json of the tasks runner work content)
            /complete (a text file that defines being complete simply by existing)


    uploads:
        (nothing yet)
