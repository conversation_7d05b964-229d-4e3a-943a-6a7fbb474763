_ = """

cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/
python3 offline_analyze.py slicer_wsgi_permissions


Test:
python3 -m unittest offline_analyze


"""

import json
import sys
import time
import traceback

# -------------------------------
def build_analysis_from_content(content):
    # -------------------------------
    return_value = {}
    last_def = ''
    for line in content.split('\n'):
#        print(line)
        if 'TestAllMethods' in line:
            break

        def_line = False
        splits = line.split()
        if len(splits) > 1:
            if 'def' == splits[0]:
                last_def = splits[1].split('(')[0]
                return_value[last_def] = {}
                def_line = True

        if not def_line:
            if last_def:
                split_paren = line.split('(')
                if len(split_paren) > 1:
                    found_func_splits = split_paren[0].split()
                    if found_func_splits:
                        called_func = found_func_splits[-1]
                        use_it = True
                        for item in ["[",".keys",".read",".write",".split",".append"]:
                            if item in called_func:
                                use_it = False
                        for item in ["sorted","str","if","int","len"]:
                            if item == called_func:
                                use_it = False
                        if use_it:
                            if not called_func in return_value[last_def]:
                                return_value[last_def][called_func] = 0
                            return_value[last_def][called_func] += 1

    return return_value

# -------------------------------
def make_reverse(the_analysis):
    # -------------------------------
    return_value = ''

    # build the reverse lookup
    all_funcs = {}
    for func in the_analysis.keys():
        for sub_func in the_analysis[func].keys():
            if not sub_func in all_funcs:
                all_funcs[sub_func] = {}
            if not func in all_funcs[sub_func]:
                all_funcs[sub_func][func] = 0
            all_funcs[sub_func][func] += the_analysis[func][sub_func]

    return all_funcs

# -------------------------------
def make_report(the_analysis, the_reverse_analysis):
    # -------------------------------
    return_value = ''
    return_value += '\n-------------------------------------------\n'
    return_value += json.dumps(the_analysis, indent=4, separators=(',', ':'))
    return_value += '\n-------------------------------------------\n'
    return_value += json.dumps(the_reverse_analysis, indent=4, separators=(',', ':'))
    return_value += '\n-------------------------------------------\n'

    for func in sorted(the_analysis.keys()):
        return_value += func + '\n'


    return return_value

# -------------------------------
def get_report(the_module):
    # -------------------------------
    return_value = 'error reading module'

    try:
        content = open(the_module + '.py','r').read()
        the_analysis = build_analysis_from_content(content)
        the_reverse_analysis = make_reverse(the_analysis)
        return_value = make_report(the_analysis, the_reverse_analysis)
    except:
        print("exception", traceback.format_exc())

    return return_value

# -------------------------------
def main():
    # -------------------------------

    if len(sys.argv) > 1:
        print(sys.argv[1])
        result = get_report(sys.argv[1])
        print(result)
    else:
        print('python3 offline_analyze.py (package)')
        print('like:')
        print('python3 offline_analyze.py slicer_wsgi_permissions')


if __name__ == '__main__':
    main()

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_build_analysis_from_content(self):
        # split the content 'de f' line, so that it does not show as a test itself
        content = """
    de""" + """f func():
        pass
        """
        expected = {'func':{}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        a = [('junk')]
        """
        expected = {'func':{}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        # ones to ignore
        content = """
    de""" + """f func():
        # Subordinate ones
        p = a.keys()
        p = a.read()
        p = a.write()
        p = a.split()
        p = a.append()

        # exact ones
        p = sorted(a)
        p = str(a)
        p = if(True)
        p = int(1.0)
        p = len([])
        """
        expected = {'func':{}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        pass
    de""" + """f func2():
        pass
        """
        expected = {'func':{},'func2':{}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        pass
    de""" + """f func2():
        p = func()
        """
        expected = {'func':{}, 'func2':{'func':1}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        pass
    de""" + """f func2():
        print('test')
        p = func()
        """
        expected = {'func':{}, 'func2':{'func':1, 'print':1}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        pass
    de""" + """f func2():
        print('test')
        print('test2')
        p = func()
        """
        expected = {'func':{}, 'func2':{'func':1, 'print':2}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

        content = """
    de""" + """f func():
        pass
    de""" + """f func2():
        print('test')
        print('test2')
        (fill in here)
        p = func()
        """
        expected = {'func':{}, 'func2':{'func':1, 'print':2}}
        actual = build_analysis_from_content(content)
        self.assertEqual(expected, actual)

    def test_make_reverse(self):
        the_analysis = {'func':{}, 'func2':{'func':1, 'print':2}}
        expected = {'print':{'func2':2},'func':{'func2':1}}
        actual = make_reverse(the_analysis)
        self.assertEqual(expected, actual)

# FixMe: The reverse does not make a list of all, because some are not called. (main)


# End of File
