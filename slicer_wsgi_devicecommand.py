# A devicecommand for slicer page services

service = "devicecommand"
version = service + '.0.1'

release_notes = """

"""

_ = """
This file gets loaded to:
/var/www/html/devicecommand.py

using:
sudo vi /var/www/html/devicecommand.py

It also requires:

sudo vi /etc/httpd/conf.d/python-devicecommand.conf
----- start copy -----
WSGIScriptAlias /devicecommand /var/www/html/devicecommand.py
----- end copy -----

sudo chown apache:apache /var/www/html/devicecommand.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/devicecommand-runner
sudo chmod +x /var/www/html/devicecommand-runner

# ===== begin: start file
#!/usr/bin/env python
import devicecommand
devicecommand.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/devicecommand-runner.service
sudo systemctl daemon-reload
sudo systemctl stop devicecommand-runner.service
sudo systemctl start devicecommand-runner.service
sudo systemctl enable devicecommand-runner.service

systemctl status devicecommand-runner.service

sudo systemctl restart devicecommand-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep devicecommand-runner

OR

tail -f /var/log/syslog | fgrep devicecommand-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/devicecommand-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import devicecommand; print(devicecommand.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/devicecommand

https://slicer.cardinalhealth.net/devicecommand?siteid=PR005

https://slicer.cardinalhealth.net/devicecommand?serial=100000002a5da842

https://slicer.cardinalhealth.net/devicecommand?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_devicecommand


"""

_troubleshooting = """

----
test build
----
Are you on slicer01? STOP. I did not make it work there.

----
test that sample result is not already on the server
----
login to slicer
https://slicer.cardinalhealth.net/login

(user permission for upload_read required)
browse to https://slicer.cardinalhealth.net/upload

<<more here>>


----
test that a device can upload a result:
----
on slicer terminal:
ssh cah-pi-su@(ip_address)
like:
ssh cah-pi-su@**************

<<more here>>

----
test
----

<<more here>>


"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_devicecommand_path = ''
    base_devicecommand_path_commands = ''
    base_devicecommand_path_devices = ''
    if 'base_devicecommand_path' in service_config:
        base_devicecommand_path = service_config['base_devicecommand_path']
        base_devicecommand_path_commands = base_devicecommand_path + 'commands/'
        base_devicecommand_path_devices = base_devicecommand_path + 'devices/'

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import investigate
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import upload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ----------------------------
def make_formatted_text(content):
    # ----------------------------
    return_value = content

    try:
        # If if looks and feels like a json, then format it as a nice looking one
        return_value = json.dumps(json.loads(content), indent=4, separators=(',', ':'))
    except:
        if '\t' in content:
            return_value = '<table>'
            for line in content.split('\n'):
                return_value += '<tr><td>' + '</td><td>'.join(line.split('\t')) + '</td></tr>'
            return_value +='</table>'

    # and, in any case, make all spaces hold their position
    return_value = return_value.replace('\n', '<br>').replace(' ','&nbsp;')

    return return_value


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_most_recent_command_number():
    # ----------------------------
    try:
        command_number = int(datastore.get_value('device_command_last_number'))
    except:
        command_number = 0

    return command_number


# ----------------------------
def command_retrieve(command_id):
    # ----------------------------
    return_value = ''
    try:
        command_number = int(str(command_id).replace('do_command_', ''))
        do_command_filename = 'do_command_' + "{:06d}".format(command_number)
        output_filename = base_devicecommand_path_commands + do_command_filename
        return_value = open(output_filename, 'r').read()
    except:
        pass

    return return_value


# ----------------------------
def command_save(command_entered, command_timeout, device_ids):
    # ----------------------------
    datastore.set_value('device_command_last_number', str(get_most_recent_command_number() + 1))

    command_number = get_most_recent_command_number()

    # save the command to a file
    if base_devicecommand_path_commands:

        output_content = ''
        output_content += "{:06d}".format(command_number) + ',' + str(command_timeout) + '\n'
        output_content += command_entered + '\n'

        do_command_filename = 'do_command_' + "{:06d}".format(command_number)

        # save the content the devices need
        for id in device_ids:
            output_filename = base_devicecommand_path_devices + id + '/' + do_command_filename
            if not os.path.exists(os.path.dirname(output_filename)):
                os.makedirs(os.path.dirname(output_filename))
            open(output_filename, 'w').write(output_content)

        # keep the original details too
        output_content += str(device_ids) + '\n'
        output_filename = base_devicecommand_path_commands + do_command_filename
        if not os.path.exists(os.path.dirname(output_filename)):
            os.makedirs(os.path.dirname(output_filename))
        open(output_filename, 'w').write(output_content)

    return command_number


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(devicecommand status)'

    status = os.system('systemctl is-active --quiet devicecommand-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "devicecommand-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # later python 3
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ----------------------------
def next_command_for_id_and_remove(id):
    # ----------------------------
    # mark it as 'consumed', by removing that command for this id
    command_d = _get_all_command_info()
    command_id = _get_next_device_command_for_id(id, command_d)
    _remove_command_for_id(command_id, id)

    return command_id


# ----------------------------
def _get_next_device_command_for_id(id, command_d):
    # ----------------------------
    return_value = ''

    for command_number in sorted(command_d.keys()):
        if 'devices_that_still_have_it' in command_d[command_number]:
            if id in command_d[command_number]['devices_that_still_have_it']:
                return_value = command_number
                break  # Consume them from the earliest to latest

    return return_value


# ----------------------------
def _remove_command_for_id(command_id, id):
    # ----------------------------
    try:
        command_number = int(str(command_id).replace('do_command_', ''))
        do_command_filename = 'do_command_' + "{:06d}".format(command_number)
        os.remove(base_devicecommand_path_devices + id + '/' + do_command_filename)
    except:
        pass


# ====================================
def _get_all_command_info(option=''):
    # ====================================
    command_d = {}

    blank_entry = {'content': '', 'devices_that_still_have_it': [], 'results': {}}

    do_command_files = []
    try:
        do_command_files = os.listdir(base_devicecommand_path_commands)
        for item in do_command_files:
            command_d[item] = copy.deepcopy(blank_entry)
            try:
                command_d[item]['content'] = open(base_devicecommand_path_commands + item, 'r').read()
            except:
                pass
    except:
        pass


    devices = []
    try:
        devices = os.listdir(base_devicecommand_path_devices)
        for device in devices:
            try:
                remaining_commands = os.listdir(base_devicecommand_path_devices + device)
                for remaining_command in remaining_commands:
                    if not remaining_command in command_d:
                        command_d[remaining_command] = copy.deepcopy(blank_entry)
                    command_d[remaining_command]['devices_that_still_have_it'].append(device)
            except:
                pass
    except:
        pass


    if 'skip_responses' in option:
        pass
    else:
        # look for responses
        try:
            results_list = upload.get_upload_files_and_base(filter='zzz_done_command_')
            for item in results_list[0]:
                # item = zzz_done_command_10000000e3669edf_000003.txt
                try:
                    id = item.replace('zzz_done_command_', '').replace('.txt', '').split('_')[0]
                    command_number_str = item.replace('zzz_done_command_', '').replace('.txt', '').split('_')[1]

                    remaining_command = 'do_command_' + command_number_str
                    if not remaining_command in command_d:
                        command_d[remaining_command] = copy.deepcopy(blank_entry)
                    command_d[remaining_command]['results'][id] = {'content': upload.get_upload_file_content(item)}
                except:
                    pass
        except:
            pass


    _prod = """
6 10000000f2cfcf20 2.031 runner 0.357 0.043 1.631 0.000 0.000
{'version': '1', 'time_to_1': '0.149', 'time_to_2': '0.097', 'time_to_3': '1.385'}


6 100000009f7eeab2 2.127 runner 0.417 0.001 1.708 0.000 0.000
{'version': '1', 'time_to_1': '0.081', 'time_to_2': '0.242', 'time_to_3': '1.382'}


7 10000000ee46ab7c 7.965 runner 0.883 0.099 6.983 0.000 0.000
{'version': '1', 'time_to_1': '0.375', 'time_to_2': '0.787', 'time_to_3': '5.821'}

Look at having the main thread do the lookups, for all ID, and make a folder of ID/next_command.
Do it in the device command side, when the command is created.

    """


    return command_d


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    # https://slicer.cardinalhealth.net/devicecommand?cmd=28_33,style=side
    # https://slicer.cardinalhealth.net/devicecommand?cmd=28_33,style=side,col=pass_fail_exception
    # https://slicer.cardinalhealth.net/devicecommand?style=side,cmd=28_33_34,col=pass
    commands_to_show = []
    command_d = _get_all_command_info()
    if 'cmd' in query_items:
        commands_to_show = []
        splits = query_items['cmd'].split('_')
        for item in splits:
            key_built = 'do_command_' + item.zfill(6)
            if key_built in command_d:
                commands_to_show.append(key_built)
    if not commands_to_show:
        commands_to_show = sorted(command_d.keys(), reverse=True)

    style = 'list'
    if 'style' in query_items:
        style = query_items['style']

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    try:
        body += '<center>'
        body += base_devicecommand_path_commands
        body += '<br><br>'
        body += '(If there is no content showing, refresh this page in a minute)'
        body += '<br><br>'


        if style == 'side':
            columns_to_show = ['pass'] # pass, fail, exception
            if 'col' in query_items:
                columns_to_show = query_items['col'].split('_')

            ids_to_show = []
            for item in commands_to_show:
                try:
                    devices_in_request = json.loads(command_d[item]['content'].split('\n')[2].replace("'",'"'))
                except:
                    devices_in_request = []
                for device_id in devices_in_request:
                    if not device_id in ids_to_show:
                        ids_to_show.append(device_id)


            body += '<table border="1" cellpadding="5">'

            # ========
            # Header
            # ========
            body += '<tr>'
            body += '<td>'
            body += 'id'
            body += '</td>'

            for item in commands_to_show:
                for column_to_show in columns_to_show:
                    body += '<td>'
                    body += item.replace('do_command_','') + ' ' + column_to_show # "000085 pass"
                    body += '<br>'
                    try:
                        body += command_d[item]['content'].split('\n')[1] # .replace('\n', '<br>')
                    except:
                        body += command_d[item]['content'].replace('\n', '<br>')


                        body += '</td>'
            body += '</tr>'

            # ========
            # Data
            # ========
            for id_to_show in ids_to_show:
                body += '<tr>'
                body += '<td>'
                body += id_to_show
                body += '</td>'
                for item in commands_to_show:
                    try:
                        splits = command_d[item]['results'][id_to_show]['content'].split('--> ') # (empty), command:, done_pass:, done_fail:, done_exception:
                    except:
                        if id_to_show in command_d[item]['devices_that_still_have_it']:
                            splits = ['','','','','']
                        else:
                            splits = ['','','','','']

                    for column_to_show in columns_to_show:
                        body += '<td ><tt>'

                        content = ''
                        if column_to_show == 'pass':
                            content = splits[2].replace('done_pass:','')
                        elif column_to_show == 'investigate':
                            content = investigate.investigate_content(splits[2])
                        elif column_to_show == 'fail':
                            content = splits[3].replace('done_fail:','')
                        elif column_to_show == 'exception':
                            content = splits[4].replace('done_exception:','')

                        content = make_formatted_text(content)

                        body += content

                        body += '</tt></td>'
                body += '</tr>'

            # ========
            # Done
            # ========
            body += '</table>'

        else:
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            body += 'id'
            body += '</td>'
            body += '<td>'
            body += 'command'
            body += '</td>'
            body += '<td>'
            body += 'results'
            body += '</td>'
            body += '</tr>'

            for item in commands_to_show:
                body += '<tr>'
                body += '<td>'

                body += '<a href="?cmd=' + str(int(item.split('_')[-1])) + ',style=side,col=pass_fail_exception" style="text-decoration:none;color:inherit">'
                body += item
                body += '</a>'
                body += '</td>'
                body += '<td>'
                try:
                    body += command_d[item]['content'].split('\n')[1] # .replace('\n', '<br>')
                except:
                    body += command_d[item]['content'].replace('\n', '<br>')

                try:
                    devices_in_request = json.loads(command_d[item]['content'].split('\n')[2].replace("'",'"'))
                except:
                    devices_in_request = []
                for device_id in devices_in_request:
                    if not device_id in command_d[item]['results']:
                        command_d[item]['results'][device_id] = ''

                body += '</td>'

    #            body += '<td>'
    #            body += '\n'.join(sorted(command_d[item]['devices_that_still_have_it'])).replace('\n', '<br>')
    #            body += '</td>'

                body += '<td>'
                body += '<table border="1" cellpadding="5">'
                for result_id in sorted(command_d[item]['results'].keys()):
                    body += '<tr>'
                    body += '<td>'
                    body += result_id
                    body += '</td>'

                    try:
                        splits = command_d[item]['results'][result_id]['content'].split('--> ') # (empty), command:, done_pass:, done_fail:, done_exception:
                    except:
                        if result_id in command_d[item]['devices_that_still_have_it']:
                            splits = ['','','','','(not picked up)']
                        else:
                            splits = ['','','','','(no response)']
                    body += '<td>'
                    body += splits[2].replace('done_pass:','')
                    body += '</td>'
                    body += '<td>'
                    body += splits[3].replace('done_fail:','')
                    body += '</td>'
                    body += '<td>'
                    body += splits[4].replace('done_exception:','')
                    body += '</td>'
                    body += '</tr>'

                body += '</table>'

                body += '</td>'

                body += '</tr>'
            body += '</table>'

        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, service + '_') or permissions.permission_prefix_allowed(
            environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:

        # Look for a do_command download request
        query_items = {}
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]

        if 'do_command' in query_items:
            file_content = command_retrieve(query_items['do_command'])
            body = file_content  # no calculate_rot13
            status = '200 OK'
            response_header = [('Content-type', 'text/html')]

            # allow non wrapped response
            start_response(status, response_header)
            return [body.encode()]

        else:

            body, other = make_body(environ)
            status = other['status']
            response_header = other['response_header']
            #        response_header.append(set_cookie_header('name_test', str(value_test)))
            #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))
            if other['add_wrapper']:
                html += '<html>\n' \
                        '<body>\n'
            html += body

            if other['add_wrapper']:
                html += '</body>\n' \
                        '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_devicecommand(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_get_next_command_id_for_device(self):
        command_d = {'000007': {'content': 'empty', 'devices_that_still_have_it': ['device_001']}}
        expected = '000007'
        actual = _get_next_device_command_for_id('device_001', command_d)
        self.assertEqual(expected, actual)

        command_d = {'000006': {'content': 'empty', 'devices_that_still_have_it': ['device_001']}}
        expected = '000006'
        actual = _get_next_device_command_for_id('device_001', command_d)
        self.assertEqual(expected, actual)

        command_d = {'000006': {'content': 'empty', 'devices_that_still_have_it': ['device_001']},
                     '000007': {'content': 'empty', 'devices_that_still_have_it': ['device_001', 'device_002']}}
        expected = '000006'
        actual = _get_next_device_command_for_id('device_001', command_d)
        self.assertEqual(expected, actual)

        command_d = {'000006': {'content': 'empty', 'devices_that_still_have_it': ['device_001']},
                     '000007': {'content': 'empty', 'devices_that_still_have_it': ['device_001', 'device_002']}}
        expected = '000007'
        actual = _get_next_device_command_for_id('device_002', command_d)
        self.assertEqual(expected, actual)

        command_d = {'000006': {'content': 'empty', 'devices_that_still_have_it': ['device_001']},
                     '000007': {'content': 'empty', 'devices_that_still_have_it': ['device_001', 'device_002']}}
        expected = ''
        actual = _get_next_device_command_for_id('device_003', command_d)
        self.assertEqual(expected, actual)

    def test_make_formatted_text(self):
        content = 'test'
        expected = 'test'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'test\n'
        expected = 'test<br>'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'test  line'
        expected = 'test&nbsp;&nbsp;line'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = '{"test":"line"}'
        expected = '{<br>&nbsp;&nbsp;&nbsp;&nbsp;"test":"line"<br>}'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = '{"test":"line","test2":"line2"}'
        expected = '{<br>&nbsp;&nbsp;&nbsp;&nbsp;"test":"line",<br>&nbsp;&nbsp;&nbsp;&nbsp;"test2":"line2"<br>}'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'test1\ttest2'
        expected = '<table><tr><td>test1</td><td>test2</td></tr></table>'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'test1\ttest2\tcol3'
        expected = '<table><tr><td>test1</td><td>test2</td><td>col3</td></tr></table>'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'test1\ttest2\tcol3\nline2c1\tline2c2\tline2c3'
        expected = '<table><tr><td>test1</td><td>test2</td><td>col3</td></tr><tr><td>line2c1</td><td>line2c2</td><td>line2c3</td></tr></table>'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)





# End of source file
