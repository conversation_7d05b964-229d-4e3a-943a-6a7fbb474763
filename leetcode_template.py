# A template service for leetcode.com
#

import time

# https://leetcode.com/problems/container-with-most-water/
class Solution(object):
    def test_function(self):
        return None


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print ('%s: %.3f' % (self.id(), time_spent))


    def test_solution(self):
        """
        (fill in here)
        """
        test_solution = Solution()
        expected = None
        actual = test_solution.test_function()
        self.assertEqual(expected, actual)

# End of source file