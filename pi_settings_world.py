service = 'settings'
version = 's.0.5'
description = """
This is a pi service for setting organization specific content.

logo for use by hmi

let this be where psk and ssid get set into the config file
(instead of image building instructions doing it?).

"""

release_notes = """
2022.12.07
O.0.5
Make for world

2022.11.30
O.0.4

Version bump for testing.

2022.10.31
O.0.3

Cordis: put the company logo image into place.


"""


# ====================================
def get():
    # ====================================
    return_value = {}

    return_value['added_users'] = []

    return_value['added_sshd'] = ['************']

    return_value['logo_xxd'] = logo_xxd()

    return return_value


# ----------------------------
def logo_xxd():
    # ----------------------------
    # made with "xxd -p world.gif"
    content = """474946383761bd00b600e6000000000000000901001306003d050633110f
2710141a0516000818001c18180419271119270b1f4420210015222b1423
390d2430222528222a201e2b352c2b3b242c13292c00172d4e2230002530
3317335e38333226353f2b352e2137542b38072c393a323c23243d4c283f
56363f1a383f452743653d43002f444f35443a4744432c456035460c3f49
174c4a56304c6a354d553a4d46414f3737506043503135537130546b5555
5647563b4d570e4d5b1e405c6d425d67605e6a40627f4363775363356664
64456a8a466a80596a28486e8961712859725d4b76927a78774d7a97677a
37697a2e6a7a204e7c9950809e81807f6f82315484a386858572864c5987
a95688a75a8dac798d36798f4c7e93386395b67f96509b9b9b689cbc869d
406fa3c48ba4508da64272a7c973a9ccabaaaf96b049bcbcbfa2bf4fa5c2
50a8c64fc9c9c9aecd57b3d359d7d7d7e6e6e6eeeeeef7f7f7ffffff0000
000000000000000000000000000000000000000000000000000000000000
0000000000000021f90409000073002c00000000bd00b6000007ff807182
838485868788898a8b8c8d8e8f909192927095969798999a9b9c9d9e9fa0
a1a2a3a4a5a6a7a8a9aaabacadae6fb0b1b2b3b4b5b6b7b8b9babbbcbdbe
bfc0c0aec3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9a893dc
dddedfe0e1e2e3e4e5886f6e6b675d53494941f041ee50535d656b6e6fe6
fbfcfdfe826fd67449b20180021136862071f2448ac32b57ac3841326484
888b1002240832e58c9b7f20438a34243088010809a58021c392cc989730
c1409c79458a159a5790fc10a100808a296b460a1d0a6ecd940d024614b9
e2c50b989760a2c27419b52aceab57b6ac7479658888002aba7c244ab6ec
a13341008840e285e598aa70ffc75cf150a1c5821a5bc854dd8275a6d6a8
2e5f7aa9f90342842963cd2a16f9668a811952c6b48409b5ea982746d0b4
d92ca6458daa83fb5e01bc65458d2a7af942b4624341822efa16cb36b7e6
860827925f92b9e2c3068f1d358454c94b664b88366c92b3d98ca5c65b99
7dfffad0a1264d141451afb21670e3ccecefdfca94f8d1f6ed4b24209898
d9bc398d182c343ae840a3bcbe1a147ac1a8c63a664b0e346a6cc6c45f7d
212102005d80a7e0235da08044654f0911c2179bd5971c7b155a78610857
3c27da53bcad40441a4078289a173f0030456c0bb64848172248c1d2534f
6d918116196aa8a38e6818f0d77e5749d55f0d3f58f5e15b00009004ff8b
2e825706074f5035830e1c44954080166288dc8e599a31427ea2fd25a491
7dad84821a44bc10c0144d7eb7460933d038460d62b421860f42e058df66
5fe810420b1472b9e712453c075d5f4ec175284e5b243ae7176c3c00468a
09b659d61b374c809779fd256086193e4cb0e572683011015e51bd80c5a8
5cb641036a500189d516b422aae84a3e68f1c5056f89b04150960ad58502
33dc18814b4f5ca197070fd4b02aa92d3c80c44a7bb5c0aaa051e0008312
6fe164936834257aeb4a23a82146046c25d943b021b9b1c15a21a8a1c605
64ecd0c40463c000c41281da59006e8646f8aca0cab19742b73349a1c4c2
365921c5134fdce497650157751cff1b58544983160678c72e3f53003084
172868d6860e5e3c814269016ea699162bd0a8e8181c5c4bf072384405a4
130bf7ecb314335926850f7955d65f0866a421c118458cf8c1ba1f93f346
0915001183124e8840c212206c3586a8914a30020e42545c151922a87173
c19bc560e24c4ff8ec73b25965970211681801430d4a6c0193172f88d021
194244d1061305001bf5376700100217cb89c103e112d00044086d31d006
1af4ea65b6545678a0e7cd686011450bdc46151adc722f1cf4532854889c
b958c820845e81ada4c4aa7622b8b83750089001e417ee6af8663090e1c3
f112104dd5b863fca0c3dacb11319ccc6442d43ad075eb67c4b5ec69f1c2
ff737e8bb8651b693cfdbb246fa820c20461b09ac67a6d7c811d66a37e41
c205518efbd403f4b999ab9e4219b8ecc70a737b1d193c10401dd9e9015e
2882113a10a83d1981024c5a9f22dc108119144106a3424e07401805e790
a15c216c831974f0800edd8a0c74b2d99ed8d0822214a737c1a982cc68e2
b39938650c55d0810c0b868610d4803d0efc820012a34144ac01000ee240
03d3b0041e78a1036d20804bbc400235d087556d504303c4559517106188
1742830450b3851600480d66d0020e1ea49f99f48c6e7f19c303d2a0a16b
a181072f20980a01e0b1261aa20c00a042085ed0af3610a1063ea8811049
d09409a081012b00810ed653300b9091ff0c2f309ca0ea270017566160cb
69c3093ab4ba9ebd6e0c52106296bec0c70bbd0a84824c4305ca60c84220
12085440430528848625c0e0023a98df72f023020079c0255bb880199463
8615e40730581ca51820f89c31ec400b2d439f049cc24325706f344ff141
9d2c84861a00615469c0422dd7f6814af5b20b89a4421ad0170209048001
26a0013c358084334ae02f64b84000db1005a2190d97828a801702360625
8820043a88420c5849938740444cd9dc9316aaf0ce19526f392cb0a706bb
50012a04f37c6940831934235226b0410d2ff09caa46a5062c1021059131
d328b150b65b49c60bb4820ab87e58830a16ec0b5538e3497794d22696a1
a52fffa59e0aa3400227148703eb5c0e0460404ce744c8a959020219fd07
86d5d90a0c17d3101a7c20d5a9f6f103bcfcdd1910e0529a0a70730c6814
19ac50817d16ec01ca8bc2170a65b121b6410670712bb8b032d127306188
3520eb72ecaa210414925d4fec2b1af7642a3370483223f06bf16a400624
8c202e4ab8ac03dda6bac94ed6295e08c13e31949cb982a10638f8029638
4b2a0030b14d6f10804b3999cb164472a688bd8c1b59a5060b0c4e51303c
5e8eda40db45d91622896a8a54b6200222f8540634580f4e735305114c93
b8cb31830032e8a2080093b93b7219132620a329b5c0062e29ce0846c41c
0dec404c6d45a8104040032284950dc9aba3ff68ac20311f3e65a2d8ddc2
f5bc3081e5749770a8e46c1bb4408160dd80065488df28cd10ad69bd6530
790103128223930750a80518aeedc404b3050f30214062302b56acf0049e
b96ec73cd0011dfda7bc3a95283bb284ef85980035174da1a55c18ad0a0b
20a38a91410a32c0c217b4a06432880ab170a1ecd98af002121c343b3489
5beb6852156be1e073ba89e19361d840292f47072a05cf1a0ee0520142d6
6cb01422869a10110358c15061b20c199af21620b56e614ea0f34a566086
3b55aca219b84220db3003c9d880066af3737d30a0b8efbc2102a225181a
8a3aae02cc73395a9816b5747ca27195f3d2154ed413c000422dd0ba5a34
a06b0cdaff4206128c96b36988c0826e70df671f0e9d33134288db108215
b4400723f01b9c6f0b9a5fb78e6e10692b0eb0f00321ace7c970218309be
0082170841083e58c1129e6dd7fa5559366568418af9cd6d045b26761652
83008e27cca0caeabb1091f3a5c3054acdd80009eb31c3b1e7244a997e4a
b5aade930ef2ba9837b4340b5ad5810b6f25019b3177332470e1c35733e1
4b2f0cdd5a21c30c5678851da8cd91e884e18343be3609d097283770e9ad
650d811c5b8603d4cbc073247b0523a3dbdc73b6b07e6a20a3189c0f052f
79c1e888be363bfd9b2c6710387e0568060908017b274cf52873704defca
0ddd08b4b912ae1e97b780703351b89d10864e76ff01eae0b344e90015b2
dc6f8c0101065a19c3f208ce0413608f2646d6bbcd2b8c6dcbec800840e0
81b2c620d0c2db550dd22ecb1480b9f493021e354de5f785b050caa9274c
f37ae7bcc16b044d8bf6d9f465c7029b88f28644aa58ca1c284e94b59a86
163ce16d57c0bdcdcf095ea3ce80085110c37081effa0a1cfd1f41100115
fc3ce2db2121c45a2541f6322ffd3b06e9567f97bd881d2843159e1d246b
100107d6de6f26286b05bfa75544b017339177edd733998613e3b20512d0
7af925402d6306755530511005f9c502c7f50f376003252565f563024220
5b65873e1772303a837507c828d07305ce76336680567db40426f04e3850
05fbf654c251ff7a72e50223917fcd247fada20584d747ecf10531a00175
d206eac76b5577803ec379644419a0f45eadb20435a0833a022ac5a15130
844a5ae80330f867ade60f2a300413487ecff6825a400433a04305f045c6
067d0ae38437a76955f1044890874230010e8844c9110242808516022ae7
512863e00115e42a8d622d3cc283f8a72961c87dcb31005750056d010611
e0576d6082121611745887af131520208462508a72c73662a02bbbc56d42
708605930242021323c05c6a7050006833273386fb70033fc04092e84058
f0254f1143056306ac356ed1f78909986e3ac33b7e68215a900242f00442
f002e9851cd2f83d7d14053a34065e00492b0103a916ff4620e005191080
6c8006f7570e6ea00048d072bfe84046e0372784052d7321396005271871
ca688770854668f000dc32151c174628a06d2964273e501c348006620088
c4886b4fb005ae885219480e49b00343b07cf1583044d01f06c01b1e6000
99b11966303ee366804e182e805103e8b81cf3683684f33d683003ea841c
f5e3608e340224904a6d800522981c6a006033708a17220649d00fc95504
3530761d79212504048ca61b52e0011f400416f068c8f8894a4013625205
91d806405077f1167b4d003834e5015b306fcf984a59220364f004e8b71c
15d00f0d82041ee080c0e787ec3101d7745496086933c17eeda780557183
7d74049f23150235ff032e71264ad8162b703c8244045a11577b12052457
0e0630044f1052f1e8480280232e5301bb772b96f68938e1285ed7254df0
038bb9121c90064b9602e9684d64a0041930847b2206b753943aa2062566
0e67000145a0041cc97d3f793b22200646600117206e6c858c2ad97ecb58
7dc3483feca103a7f114c5015e33f2143f700262724576329087c87f5962
8550e74018680e37b00348e00373697a6d907c6d358dbb369d924598d277
75dd131535100340a003400002cad2563e00044c100540c9869631335b20
5031671e1310894464587df405c3370ec93504734185cac906ab44319125
2bb5e22d5c09a051480655500390648d446004dac91ebeb9ff987271462d
5005e6e10324a09765977ae35006223004605000bf881c4cf0005ae96b10
579d07c8799df7142310056820533e998539f53c3353052da0063a8050c6
018459a20317d90df1c9162db897661001b0021a4d815410a73d5cd99586
09182bc09b0e44030c101c45231581f18d2b73362300a482240681f60d02
00a21449a6d4c3041e803d330771758a352a281548109465c7067164041c
e03c4f30027831196773267ee688e1b0061060a455e09421e748b7536e73
8a1350ea84d437a5f02862e1d3021db02a2be4014280043cca1213eaa87b
d202df27093062434880a1f025966232a9df3587957aa94ff104a2848619
12469dc6041270011c68ffa802140588d70d2ab003320286c63a4a44804e
d21a110f63640cd11052e09f7408a065f99244a725eb3a881dfa0d01f003
7e137bf6c904cf27590e5364954a87d6da1f1df89464d70625100eed5824
92e7aaaa76327eb3330bcb95d7d9795e2003109b85fdda061f100e446aa4
636005da18b130a31771d6b15ca97ba0619b239b2521a0af95a98b920005
33a06b5b909ccfca064680928b22b35c79a747aa9e10fb939839552396a8
92301e4ea033422b625f90013cba8f3581b474487d7934017a3ab2065091
6b6306eb18090af003faa81f570bb5461033b667475ebb929a360679d2af
fb1a050530b659521f02f00d6e00013fc014512188589b53b20aff11f65a
b797f6b1528102372b2868f00210354a40806a177200c9da08296bb8f922
486db9ad61bab85720718e8b7bd497285760b6939b1c20e0034c3b285520
013fc7023cfb0853e04175332746d99b0e2a06a9542afd02047fe156a89b
ba9a679815f7baf9853a0f9b5f5f3091ced6064620b58f10043bd0959137
7843643b39310209204f24500542a0685110543451abcadb3ad7f9236ffb
baf553033320aed6ab2ceaaa0550e00de79a2c91a704185b7e9ee38d42a0
142ce12c0a89308cdbbeffc99278ebb793cb6df8334a11f01cc781b6de50
023b20314f2105514003db1854923675151046d674b40c8c7b9c0718f5eb
bc4138637a3abf6fc1676c80ffaa9330011bcc8c5760040220a2d5b35633
c3406a70c20598c22aac693af7bb2e5c1f609702f52706912a155560382a
e00d0ab00316b605461002db0659d32979c2cb010a9c8c466c7386e94d4a
bcc4d5b30537d9471d702b76d60d6f70c5130306348005d1cb5dd3991d97
d5aefd41b7653c71770b9c6a9c8535e0058cb827444551227032ddb00674
1c34a0c40401902531b0c74fb17346c0510b1cc859f7515201037a2b8903
c46921840622f0692ea9039dab0867b0b6c1d65623b002a8f4582f342e2b
60035cebc9eeeb955581b8857c94ce61b342d901403c0648200666ca0d65
b0aa7cb7125e50c20553baa0310240fc3c45cccb72e3cbfa8198c19cff25
edaac538d9022ba728536c04b9cb085db0aa90bb122f10563f993ae46204
b0e93ffb91bcda6c985ee0badf0c0428c03b6270c2fe33a3e5ea08eb3c04
4a20a5aa93c8e9288c92a71e66458057a1cd9fcc8c60e0c4dffc80e8a305
5dc35663400246b09990d0cc08fdb1a3a13ca37332ca5205dfa3053e2098
5741ad14bd3050b81236a0b3fd3c025dc958fe63014420d28ff0ca089dd0
2a7888158406133017fb1405f2ec5d803cd3a008cafac1cf85fc816070be
38e63f5ea00387c70d15eb4afabc00e3b8421462bd088615f84cd1edfcc0
a31c8f3e85236ae53f3bdcd593f0d5511d347201c25ab2c66d35c6b707d5
3dc4cd6330032097d117c21e3480ff67a784bbdc30c73f9040be2c171910
055ac0a038c9d12910020fd217800dd9a1080620a0a1866dc986422b52a1
045f800167ca08c9b503ec07a091071d5b5003b2c51e68b002de38649d0d
d675dc561d20daa32d94111d1aa72d0675d9d800b0bd7263d23fd4924159
b9a9a1dbbbcda252618e853d4a38bdaf373a6eac1b0508e00dc98d04c076
15e24238398b1ced047d3891d6bcacd0a02102a4a96568c200109cb14c10
54ab93471d7000e0bd034560af528a9dc5e100ea2502d724adf50ad5261d
170b06045ab059877d53a522385e700239a29cb0c8b5e824170990c11ed4
b801de3dfd91891aab28723a64ec9dc2b0dd775ea00420608f9c4a043210
ff0336b9b59741416d9d5f62b0b5de651937e00d498002433d7db6221715
908e1e0021a031a944a6cdb73aa504f4a228e050b91117606002d96a9fdd
2559acbbbfdd00238fad794e10e2917705f102c51b3e2e274eab29eeb801
7ecc31319d64f0b4fbdaaed903ca63e004d8eb08aaeada0d7c1591b70520
70a546c0ab11ade63347648debb54f9e158d02c46d15a78a12b4394e5a4b
e6d4a3113de9dc08ed289f0708a001fa02dae9d271aee6a291e0eddbe873
6a19ea5a788ed4d4930a0622b0da8e1000febde8db63ad20c0490c35013e
a0120454e5a64bab0c0cea738a501e007c3b5a77e06200e0b0011af989f8
5a157b74216c20064cd002208002dc8eff02356005cc2e1ab8beb0aa6e5b
789add2755be0143753421a4dd10e4614e87ef7bda63c7afdcaa03b89d66
e29ec2e5fe21d7aaa9cfaa034a4096e0520449f90d30e2e79f487d5531c5
04a7058e6940fb9ec24ef02d1d5564efb7120f90c6aea705c71859e06203
402d0990ece949fb4abb237b2aa4b8128d15d37da7b004f0fd16024eaaa2
400301b4fe08b6fedf33fb4ab22b7f23e6011305d313ddd94a3b06a0495c
955b3133e74a01200e1b3003f17eaf0a8402e29a256260011e302dd7c4ee
6d9ec22b7c362f80eed27b6cc8c83a0b530441200e532002ca2dedaf84d1
50ab424cd0002b90a01abe1a5fafbccfdcf032af55e9bbee2e8f69222f0e
25cffff354cf8c63000225ab42e0a6ef289ecf9ffd1474ee7ad4dcf2d98c
35383f0eb6fe03e37e6eaf0402afda1c441ff965cc9267d3c85206021543
7532edece3100433f0f64e88475221f77ee6255dff5d7bbfb0dc7cfbfd7a
9264e9d473e8043e70f0e2509cb79ef8e272b9cfca0497ce8413afbcbf1f
1554dd2a066b360f87692250d0de60003b30f5edc73de1c5d0cf1a02b1b9
e658f1f93dffd90e4f5cdc06e94ecd334e00fb1839fbe26dabd8d9ba6ddd
06020008555b60648563608889605e578d8e8d4a91929394959691568f89
634a586d6ca0a1a2a3a26a1c648a8a5b8f57524a454171b2b3b4b5b6b7b2
6b103b43979799575e606348519fa4c8c9a0ff66463a2d150f2f4e8487a9
60ab8faebedbbec08d9b485fc7cac9692ba89bd560ac574e2267b8f1f2b4
113b3bdc94c0c2633ee2e4ffa2da081468068b0e113eb61852c4e811be87
93bc5dd954c30c40646dccf8a836668b071bd5b0395262609ec9785d44ec
28e204a2be61152fca1c35100d161ad2c6a85be7e8094488ac129199306e
269b3658901c1ae385061b2383aeb1fa91e4a4d55a6f14ecf8f1f3e59898
46c38212a886898022e8a436b2f2f36150445b74149d99c60122324f46a4
691325aac84610dc5c1d2c2bc88c1d485ae20336688c102d73c5cefca4a3
465a876db9bd750c596c1b125b0e7930a246608a908f9ea8204c58d756c5
dcbc21f2ffd222b2e4c93aac24c29cd9d75b322bd47826a2e41008349f90
0a41ede81d6bc225ec2179f8c41122c7626cdf06d8468b5244bc7b5772b2
798476723a7ca0aad2e4589b108a1e5989f09cf019954360fb22df28ea18
18db49d6c609132d129e789354e7887f1c9c87510b48a4d5c13168bc804e
438dd850467d8465209d5bd681c7848301d284450dcc5dc116829548e41f
0a248ea2060a9711915c145524f257491c0e56067efa5d22c58277551463
896d8861d96e3db158092b5101776428688c80ce1823a031d6694c66d845
8f1d7ec80d7f8d0883480a53061801438f04e92494d7d5105685e85431e2
514c2c07972356f008e6553fbe868f82df0c5385ff8d2592d3c6174b82c7
a7939490d9df754268c99d40586c34cc0cc221a5de6c8f8cb0e19f834587
989b2d6a0293278926d386114ea8931aa409c2696876ff7447c2042f2842
83406258d8652bf4913a986b5c8118e2182b1891a6516d7c10955a90d02a
c9908f98094615c6286a8609c3a4d5541b6a6410df23ce193bd80d8715e1
92aa5f75d66a40159cdb884fd662c28ab657dca94c1b445cb1d3303f9c90
81c07b3a82c46aea0ee68656f7bcbb2c07d93dcb5dbda08e946f24ecf0db
adab628830f075da1af8880082353cd8144042846d9987d4200316a5cdfb
6a8118aeb871c7bb7d4c4a921e5823b4a38ed83085caac7978aacb41ad67
83cf0126a9a9ff48f8eebcef6efe22d3c1d042b3f244b1481fab55b24cab
4ac80bfe245ae521226d2c09cf7021fa3311558cdc352b0aac11366b49a8
e46e576fad2357ab68884caddb1c5f7d5d6d3f4721445a5c635874557b27
2d1daa97d80a4605163f2805db6b21ae043bd382c178286da0d1829eb36d
e1faebecf40476e5c70a70394492163806049da3fe490b6811ad8ddb125d
517a6d3511e1c11517c6ee7c230be84d3b6b5dec123144840643480d6943
6b9311122871d9bda2bf1c22c12118c1440b22d43decf3ac8cf0e5f4cfdd
8082a012177ac5e0d032e101121351874830e7a4ec15ea3aeb00a04e3206
3f760ce106f4e350040e931fdcc16b01cfea8e8506f6ff17d125ee11a543
a08e1a183b27cc2e8284714300ecc192b2698f0c590a8b0346f697aa21ce
7c07bcdbf3a4f0841e4a01185640190a397406dbade4279ae047f772a505
4d254272d5f2e0e8d82134287acd170280c71039d4058825863a21dac2e9
b8a383d2c5ce86a23360999ef8972b5ec2091098df163994042f12701244
1a8307cc70a43648a01a565491146b4542760ccf124f100114e6f8a72058
ef8bdc20d430ae30c67f99c14a266385ce06190935c2cf0a774c642c18f9
a71b3cf28e1f9c08193c802b72d4c54c67e4a42570183b50467204a32465
291f39284778a1231670d0ab8a432d8dc9f28d3fd4241a2f91975cea7297
62da862680b34451ff9841072fa8063b8ed91654264804ce7c662321d642
5f10ea975b08264604c03c06928f9b6e73823b28274e75256185bcd08c75
5669299ab4605a6d84a7e82070b47aaa6c0a00380c573047a8d014632e02
614211b4392b815acb094810801c0ddab03200e07e88f18d2fc17080a224
a9023578222b2c6aad270c218b1cdddb1a0c30011632f3118ef1191a18f0
cbf72d93a5dd7c820d2290b29886ed0d3730e20fa693aafe90e101690885
198445346302b5372d8140388d1a368482d45dfa91d43548708c3450158a
3fbdeaa08610808d72b5726b4880f596aa1ff391c107776ac3049a7720b5
e2439e22d84051df4abf2424d41ebdc0e382c87001c8b4a1ff0505e249e8
fcfa1027b814000525ec10cf20577bac449e4ac8de2f2fe0093130001df2
a1ec5f91008108484fb373f4aa67ff2611425c401c5f1096e454bb1f27d8
00006e85ed16dd90d4c3b030778728001f19858a8af236413f104010de20
dc7aae6103d663210899b200e418e1537da5ec138a0001150cb6bae23c03
763d8b185f766401d9394068447248b5ca93bc2a782d7a637a861228c0b8
3f2842268601860b18210a1395ec15d2cacd96fca0bcfadd2f57d7905495
fce007f9f9251994200200c0d2aaf06c49114420dd084b98b06f98820120
30830b2f35348518d626a5d8128c1661060a08c014a87be2139f21080210
818b87f084c68c8115def7c46a8d5f31841d4c000037d0628fa72c8b1f43
003f43c84ff1ea2b1ed04a02094518c20f66c0010194a00b3ca6b29a6771
8624380002f7bbf0101223056dd4d8cb9690276c5a026631db6306225000
94d1bce642dfc20d5db8010014c08138db43ce45280212264d6925f019cc
61f6f39f450001010cdac4860e752dd6d00528dcc0008bbeb20850808219
1897bd3b70f50c5020020e40400102c84010bab086348bfad75679c31aca
50ea24dc200206483600961d011504210953e84217ce705e605bfbdae284
83b6b7cded6e7bfbdbe00eb7b8c74dee729bfbdce84eb7bad7cdee76bbfb
ddf08eb7bce74def7adbfbdef8ceb7b70301003b"""

    return content


other_content_to_convince_runner_we_are_a_service = """
# ===== begin: service file
# ===== begin: start file
"""

_ = """
sudo vi /cardinal/pi_settings.py

sudo systemctl restart pi-settings.service

sudo systemctl status pi-settings.service

"""

import os
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest


# ----------------------------
def main():
    # ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string for " + service + ": " + version)

    wake_count = 0
    while True:
        # do system maintenance

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_settings_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print("!!! failed to write wake_count")


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_settings(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)
