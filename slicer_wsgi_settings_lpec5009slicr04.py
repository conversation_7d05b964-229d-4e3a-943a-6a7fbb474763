# cah specific items for use on slicer04

service = "settings"
version = service + '.cah04.0.1'


# ====================================
def get():
    # ====================================
    return_value = {}
    #    return_value['home_url'] = 'http://10.50.34.22' # slicer04
    return_value['home_url'] = 'https://slicer.cardinalhealth.net'  # 2024.02.24
    return_value['trust_list'] = ['david.ferguson', 'steven.murphy']
    return_value['trust_list'] = []
    return_value['data_drop_base'] = '/mnt/disks/SSD/var/log/slicer/'
    return_value['ram_disk_path'] = '/dev/shm/'
    return_value['datastore_save_path'] = '/var/log/slicer/'
    return_value['days_to_keep'] = 14
    return_value['sites_to_drop'] = {'MEX09': '(if key exists, then it will be dropped)'}
    return_value['days_to_keep_dashboard_data'] = 120
    return_value['site_title'] = 'Slicer 2.04' # This is the 'static' version... the 'dynamic version' is based on the last uplaoded zip release name.

    return_value['login_authentication'] = {
        'authentication_type': 'ldap-cmdln',
        'admin_AD_group': 'A-APM0028269-Slicer-admins',
        'user_domain': '@cardinalhealth.com',
        'basedn': 'dc=cardinalhealth,dc=net',
        'user_domain_list': ['@cardinalhealth.com', '@cardinalhealth.net', '@cordlogistics.com'],
        'ldap_servers': ['WPIL0219ADIDC' + '02' + '.cardinalhealth.net',
                         'WPIL0219ADIDC' + '25' + '.cardinalhealth.net',
                         'WPIL0219ADIDC' + '26' + '.cardinalhealth.net'],
    }

    # --------------------
    # Apache settings:
    # --------------------
    # https://stackoverflow.com/questions/44335970/apache-invalid-command-sslengine-perhaps-misspelled-or-defined-by-a-module-n

    files_path = '/var/www/htmlfiles'
    return_value['apache_files_path'] = files_path

    content_limts = """LimitRequestBody 0
"""

    content_80 = """<VirtualHost *:80>
	ServerAdmin webmaster@localhost
	DocumentRoot """ + files_path + """
</VirtualHost>
"""

    content_443 = """
<VirtualHost *:443>
ServerName slicer.cardinalhealth.net
DocumentRoot """ + files_path + """
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/slicer_cabundle.pem"
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""

    return_value['apache_config_content'] = content_limts + content_80 + content_443

    return return_value


import time


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

# end of file
