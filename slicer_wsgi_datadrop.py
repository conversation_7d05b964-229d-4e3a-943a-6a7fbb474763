# A receiver for devices to give us data about themselves, and
# to return information that they might need.
# pi_runner calls in here.

service = 'datadrop'
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/datadrop.py

using:
sudo mkdir /var/log/slicer
sudo chown -R apache:apache /var/log/slicer

sudo vi /var/www/html/datadrop.py

Test with:
sudo python /var/www/html/datadrop.py

It also requires:

sudo vi /etc/httpd/conf.d/python-datadrop.conf
----- start copy -----
WSGIScriptAlias /datadrop /var/www/html/datadrop.py
----- end copy -----

sudo chown apache:apache /var/www/html/datadrop.py

sudo systemctl restart httpd

systemctl status datadrop-runner.service

sudo systemctl restart datadrop-runner.service


##############
# notes
##############

The url to hit it is https://slicer.cardinalhealth.net/datadrop

like:
https://slicer.cardinalhealth.net/datadrop?serial=test,source=network

https://slicer.cardinalhealth.net/datadrop?serial=10000000e3669edf,source=network

https://slicer.cardinalhealth.net/datadrop?serial=test,source=runner

https://slicer.cardinalhealth.net/datadrop?serial=10000000e3669edf,source=runner
https://slicer.cardinalhealth.net/datadrop?serial=10000000aaef9969,source=runner


{"name_to_use": {"10000000e3669edf": ""}, "screen_resolution": {"10000000e3669edf": ""}, "browser_zoom": {"10000000e3669edf": "100"}, "clockview_enabled": {"10000000e3669edf": "No"}, "bluetooth_enabled": {"10000000e3669edf": "No"}, "services": {"pi_security": "S.1.1"}, "bookmarks": {"10000000e3669edf": {}}, "special_menu": {"10000000e3669edf": "No"}}

sudo su
vi /var/log/slicer/datadrop/json/id/test/network_response
chown apache:apache /var/log/slicer/datadrop/json/id/test/network_response



It then saves the get request query content to:
/var/log/slicer/datadrop/

The raw received data is organized as YYYYMMDD/HH/ in:
/var/log/slicer/datadrop/raw/YYYYMMDD/HH/<timestamp>-<id>.txt

The recording of each datadrop json of the query in:
/var/log/slicer/datadrop/json/id/<id>

sudo cat /var/log/slicer/datadrop/json/id/10000000e3669edf/runner


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/datadrop-runner
sudo chmod +x /var/www/html/datadrop-runner

# ===== begin: start file
#!/usr/bin/env python
import datadrop
datadrop.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/datadrop-runner.service
sudo systemctl daemon-reload
sudo systemctl stop datadrop-runner.service
sudo systemctl start datadrop-runner.service
sudo systemctl enable datadrop-runner.service

systemctl status datadrop-runner.service

sudo systemctl restart datadrop-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep datadrop-runner

OR

tail -f /var/log/syslog | fgrep datadrop-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/datadrop-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!


"""

_test = """
on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python -m unittest slicer_wsgi_datadrop

"""

import datetime
import json
# pulled in main only import numpy # pulled in function that needs it, to not slow down all tests # that long time of 5.2 seconds does not repeat
import os
import shutil
import sys
import time
import traceback
import unittest

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import address2location
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import algorithms
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import codeupload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import devicecommand
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import rings
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import upload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

# sudo cp -R /var/log/slicer /mnt/disks/SSD/var/log/slicer
# sudo chown -R apache /mnt/disks/SSD

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    days_to_keep = service_config['days_to_keep']
    datadrop_raw_root = service_config['datadrop_raw_root']
    network_utilization_save_path = service_config['network_utilization_save_path']
    datadrop_save_path = service_config['datadrop_save_path']
    statistics_save_path = service_config['statistics_save_path']
    base_upload_path = service_config['base_upload_path']
    checkin_file_root = service_config['checkin_file_root']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

hour_list = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17',
             '18', '19', '20', '21', '22', '23']

# ====================================
def make_new_response_from_selected_data_store_content(response_d, selected_data_store):
# ====================================
    response_string = json.dumps(response_d)

    for key in selected_data_store.keys():
        # replace out the ones that defined the datastore pull of content
        name_to_find = '(datastore_' + key + ')'
        response_string = response_string.replace(name_to_find, selected_data_store[key])

        # get any direct replacements
        name_to_find = '(' + key + ')'
        response_string = response_string.replace(name_to_find, selected_data_store[key])

    return_value = json.loads(response_string)

    return return_value

# ====================================
def make_datastore_pull_list_from_response(response_d):
    # ====================================
    return_value = []
    # get back to string
    profile_string = json.dumps(response_d)
    splits = profile_string.split('(datastore_')
    if len(splits) > 1:
        for item_count in range(1,len(splits)):
            sub_string = splits[item_count].split(')')[0]
            value_found = '' + sub_string
            if not value_found in return_value:
                return_value.append(value_found)

    return sorted(return_value)

# ====================================
def get_current_day_hour():
    # ====================================
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    return TS[0:8] + '.' + TS[8:10]


# ----------------------------
def day_hour_is_in_last_X_hours(other_time, current_time, previous_hours):
    # ----------------------------
    result = True

    current_day = current_time.split('.')[0]
    current_hour = current_time.split('.')[1]

    other_day = other_time.split('.')[0]
    other_hour = other_time.split('.')[1]

    current_clock = datetime.datetime.strptime(current_day + current_hour, '%Y%m%d%H')
    other_clock = datetime.datetime.strptime(other_day + other_hour, '%Y%m%d%H')

    total_seconds = (other_clock - current_clock).total_seconds()

    if total_seconds > 0:
        result = False

    if total_seconds < -1 * 3600 * previous_hours:
        result = False

    return result


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = output_file + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ====================================
def get_device_address(id):
    # ====================================
    if len(id.split('.')) == 4:
        return (id)
    else:
        base_path = checkin_file_root
        checkin_file = base_path + '/' + id
        try:
            with open(checkin_file, 'r') as f:
                checkin = json.loads(f.read())
        except:
            checkin = {}
        return_value = ''
        if 'address' in checkin:
            return_value = checkin['address']
        return str(return_value)


# ====================================
def cleanup_old_raw():
    # ====================================
    days_present = sorted(os.listdir(datadrop_raw_root))

    if len(days_present) > days_to_keep:
        for day_to_remove in days_present[:-1 * days_to_keep]:
            try:
                shutil.rmtree(datadrop_raw_root + day_to_remove)
            except:
                pass


# ====================================
def build_all_rollups():
    # ====================================
    # This gets called by tasks, on a periodic schedule, and also when getting all data for a device

    _ = """
cd /var/www/html
sudo python
pass; import datadrop # put the pass in front, so offline_test does not find it as a real import
datadrop.build_all_rollups()
exit()

sudo chown -R apache:apache /mnt/disks/SSD/var/log/slicer/datadrop/raw/

    """

    ids = {}
    base_path = checkin_file_root
    id_files = os.listdir(base_path)

    count_ok = 0
    count_error = 0
    time_start = time.time()
    for the_serial in sorted(id_files):
        build_all_rollups_serial(the_serial)

        try:
            build_all_statistics_serial(the_serial)
            count_ok += 1
        except:
            count_error += 1
            output_file = statistics_save_path + 'last_exception.txt'
            do_atomic_write(output_file, str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))

    time_end = time.time()
    output_file = statistics_save_path + 'last_run.txt'
    do_atomic_write(output_file, str(time.time()) + '\n' + '\n'.join(id_files) + '\n' + str(count_ok) + ', ' + str(
        count_error) + '\n' + 'seconds: ' + str(int(time_end - time_start)) + '\n')


# ====================================
def get_average_peak_load_in_last_eight_hours(raw_load_data, current_day_hour):
    # ====================================
    import numpy

    result = 0

    accumulator = []
    for d in raw_load_data:
        day_hour = d['day_hour']
        if day_hour_is_in_last_X_hours(day_hour, current_day_hour, 8):
            if d['raw']:
                raw_float = []
                for value in d['raw']:
                    raw_float.append(float(value.split('_')[0]))
                peak_value = numpy.array(raw_float).max()

                accumulator.append(peak_value)

    if accumulator:
        result = numpy.array(accumulator).mean()

    return result


# ====================================
def build_all_statistics_serial(the_serial):
    # ====================================
    results = {}
    # do a central report of things that want to help us locate problem devices
    # these get reported to

    # average load for the last 8 hours
    raw_load_data = get_all_raw_data_for_service('runner', the_serial, 'loadavg', do_the_roll_up=False)
    current_day_hour = get_current_day_hour()
    results['load_report'] = get_average_peak_load_in_last_eight_hours(raw_load_data, current_day_hour)

    # save it
    content = json.dumps(results)
    output_file = statistics_save_path + the_serial
    do_atomic_write_if_different(output_file, content)


# ====================================
def build_all_rollups_serial(the_serial):
    # ====================================
    current_day, current_hour, current_TS = get_current_raw_day_hour_TS()

    current_day_hour = current_day + '.' + current_hour

    all_days_in_raw = os.listdir(datadrop_raw_root)
    for day in sorted(all_days_in_raw):
        hours = os.listdir(datadrop_raw_root + day + "/")

        for hour in hour_list:
            day_hour = day + "." + hour

            raw_dir = datadrop_raw_root + day + "/" + hour + "/"
            serial_rollup_file = raw_dir + 'rollup_' + the_serial

            if False:
                if os.path.isfile(serial_rollup_file):
                    os.remove(serial_rollup_file)
            else:
                if (not day_hour == current_day_hour) and (not os.path.isfile(serial_rollup_file)):
                    if hour in hours:
                        rollup_content = ''
                        to_delete = []

                        all_raw = os.listdir(raw_dir)

                        for each_raw in sorted(all_raw):
                            if the_serial in each_raw:
                                raw_file_name = raw_dir + each_raw
                                with open(raw_file_name, 'r') as f:
                                    content = f.read()
                                    rollup_content += content + '\n'
                                    to_delete.append(raw_file_name)

                        # like /mnt/disks/SSD/var/log/slicer/datadrop/raw/20220728/00/rollup_20000000cf60766f
                        do_atomic_write(serial_rollup_file, rollup_content)
                        _ = """
                        with open(serial_rollup_file, 'w') as f:
                            f.write(rollup_content)
                        """
                        for raw_file_name in to_delete:
                            os.remove(raw_file_name)


# ====================================
def get_all_raw_data_for_service(the_service, the_serial, the_value_raw, do_the_roll_up=True):
    # ====================================
    result = []

    if do_the_roll_up:
        build_all_rollups_serial(the_serial)

    all_days_in_raw = os.listdir(datadrop_raw_root)
    for day in sorted(all_days_in_raw):
        hours = os.listdir(datadrop_raw_root + day + "/")

        for hour in hour_list:
            d = {}
            day_hour = day + "." + hour
            d['day_hour'] = day_hour
            d['raw'] = []

            if hour in hours:
                raw_dir = datadrop_raw_root + day + "/" + hour + "/"
                serial_rollup_file = raw_dir + 'rollup_' + the_serial

                all_content = ''
                if os.path.isfile(serial_rollup_file):
                    try:
                        all_content = open(serial_rollup_file, 'r').read()
                    except:
                        pass
                else:
                    all_raw = os.listdir(raw_dir)
                    for each_raw in sorted(all_raw):
                        if the_serial in each_raw:
                            try:
                                content = open(raw_dir + each_raw, 'r').read()
                                all_content += content + '\n'
                            except:
                                pass
                for content in all_content.split('\n'):
                    try:
                        # source=network,serial=10000000b5ef2cdf,version=N.1.0,ssid=corp,chan=149,signal=64source=runner,serial=10000000972180ef,version=R.1.2,wlan0mac=dc:a6:32:31:a6:a9,eth0mac=dc:a6:32:31:a6:a8,timezone=UTC,image=2.0.7,hostname=cah-rp-10000000972180ef,temperature=54.5,service:pi_network=N.1.2,service:pi_runner=R.1.2,service:pi_hmi=H.1.1,service:pi_monitor=M.2.2,Memory:MemTotal=4013187072,Memory:MemFree=3288465408,Memory:MemAvailable=3568103424,Memory:Buffers=51073024,Memory:Cached=451837952
                        if "source=" + the_service in content:
                            if 'serial=' + the_serial in content:
                                if the_value_raw + '=' in content:
                                    report_value = content.split(the_value_raw + '=')[1].split(',')[0]
                                    d['raw'].append(report_value)
                    except:
                        pass

            result.append(d)
    return result


# ====================================
def get_current_raw_day_hour_TS():
    # ====================================
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    return TS[0:8], TS[8:10], TS


# ====================================
def application(environ, start_response):
    # ====================================
    time_start = time.time()
    time_to_1 = time_start
    time_to_2 = time_start
    time_to_3 = time_start
    time_to_4 = time_start
    time_to_5 = time_start
    debug_string = ''
    notes = ''
    network_response_file = ''
    response_json = None

    try:
        cleanup_old_raw()
    except:
        pass

    try:
        # query = 'source=network,serial=10000000e3669edf,ssid=(not_on_wifi),chan=,signal='
        query = environ['QUERY_STRING']
        if ('serial=' in query) and ('source=' in query):
            day, hour, TS = get_current_raw_day_hour_TS()

            datadrop = {}
            datadrop['time'] = time.time()

            splits = query.split(',')
            notes += '<br>' + 'splits'

            for item in splits:
                parts = item.split('=')
                if len(parts) > 1:
                    datadrop[parts[0]] = parts[1] # one of these is 'source'

            try:
                id = datadrop['serial']
            except:
                id = '(id)'

            device_first_date_ = datastore.get_value('device_first_date_' + id)
            if not device_first_date_:
                datastore.set_value('device_first_date_' + id, day + '_' + hour, who='slicer.datadrop')

            if 'ssid' in datadrop: # from the "network" update
                if datadrop['ssid'] == 'cah-iot':
                    device_first_iot_date_ = datastore.get_value('device_first_iot_date_' + id)
                    if not device_first_iot_date_:
                        datastore.set_value('device_first_iot_date_' + id, day + '_' + hour, who='slicer.datadrop')

            # I want to clean up based on time of arrival, so store based on time
            output_file = datadrop_raw_root + day + '/' + hour + '/' + TS + "_" + id + '.txt'
            notes += '<br>' + output_file

            # if datadrop['serial'] == '10000000aaef9969':
            #    time.sleep(20)

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            # piggyback here, and calc time diff to server, and push it back into the data
            try:
                datadrop['timediff'] = float(datadrop['devicetime']) - time.time()
                query += ',timediff=' + str(datadrop['timediff'])
            except:
                pass

            notes += '<br>' + 'save'
            do_atomic_write(output_file, query)
            _ = """
            with open(output_file, 'w') as f:
                f.write(query)
            """
            notes += '<br>' + 'done'

            datadrop_file = datadrop_save_path + datadrop['serial'] + '/' + datadrop['source']
            if not os.path.exists(os.path.dirname(datadrop_file)):
                os.makedirs(os.path.dirname(datadrop_file))
            try:
                do_atomic_write(datadrop_file, json.dumps(datadrop))
                _ = """
                with open(datadrop_file, 'w') as f:
                    f.write(json.dumps(datadrop))
                """
            except:
                pass

            # -------------------------------------
            if datadrop['source'] == 'runner':
                # -------------------------------------
                _ = """
{
    "temperature":"45.2",
    "service:pi-monitor":"M.2.2",
    "wlan0mac":"dc:a6:32:96:56:c4",
    "hostname":"cah-pi-10000000e3669edf",
    "service:pi-network":"N.1.0",
    "source":"runner",
    "version":"R.1.0",
    "service:pi-runner":"R.1.0",
    "time":1620917416.788708,
    "serial":"10000000e3669edf",
    "service:pi-hmi":"H.0.6"
}

2021.09.28
{
    "service:pi_monitor":"M.2.2",
    "service:pi_runner":"R.3.8",
    "image":"2.2.8",
    "Memory:MemAvailable":"3383091200",
    "service:pi_hmi":"H.2.0",
    "timezone":"America/Chihuahua",
    "serial":"10000000e3669edf",
    "eth0mac":"dc:a6:32:96:56:c2",
    "uptime":"1091562",
    "service:pi_bluetooth":"B.2.2",
    "temperature":"47.2",
    "Memory:Cached":"1598885888",
    "hostname":"cah-rp-10000000e3669edf",
    "screengrab_count":"4",
    "source":"runner",
    "version":"R.3.8",
    "service:pi_network":"N.2.0",
    "Memory:Buffers":"55775232",
    "screen_width":"1280",
    "boot_count":"136",
    "service:pi_security":"S.1.3",
    "wlan0mac":"dc:a6:32:96:56:c4",
    "Memory:MemFree":"2074701824",
    "networkuse":"(8509)(2886)(60)",
    "time":1632833048.543771,
    "Memory:MemTotal":"4025200640",
    "screen_height":"800"
}

                """

                # save off the network utilization info
                try:
                    network_utilization = {'time': '', 'usage': ''}
                    for key in datadrop:
                        if 'time' == key:
                            network_utilization['time'] = str(int(datadrop['time']))
                        if 'networkuse' == key:
                            network_utilization['usage'] = datadrop['networkuse']  # (rx)(tx)(sample_interval)
                    if network_utilization['time'] != '':
                        if network_utilization['usage'] != '':
                            time_bucket = network_utilization['time'][:-5]  # 1632833048 to 16328
                            save_folder = network_utilization_save_path.replace('(id)', id)
                            save_file = save_folder + time_bucket
                            output_line = json.dumps(network_utilization) + '\n'

                            if not os.path.exists(os.path.dirname(save_file)):
                                os.makedirs(os.path.dirname(save_file))

                            with open(save_file, 'a') as f:
                                f.write(output_line)

                            # Now, take the time to clean up old files
                            days_to_save = 14
                            time_bucket_to_save = str(int(network_utilization['time']) - days_to_save * 24 * 60 * 60)[
                                                  :-5]  # 1632833048 to 16328
                            for file_found in os.listdir(save_folder):
                                if file_found < time_bucket_to_save:
                                    os.remove(save_folder + file_found)

                except:
                    do_atomic_write('/dev/shm/datadrop_network_utilization.txt',
                                    str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))
                    _ = """
                    with open('/dev/shm/datadrop_network_utilization.txt', 'w') as f:
                        f.write(str(traceback.format_exc().replace("\n","<br>").replace("\"","'")))
                    """
                    pass

                all_files_content = {}
                for file in os.listdir(datadrop_save_path + datadrop['serial'] + '/'):
                    try:
                        content = open(datadrop_save_path + datadrop['serial'] + '/' + file, 'r').read()
                        all_files_content[file] = content
                    except:
                        pass
                service_versions = algorithms.build_versions_summary(all_files_content)
                try:
                    do_atomic_write(datadrop_save_path + datadrop['serial'] + '/' + 'service_versions',
                                    json.dumps(service_versions))
                    _ = """
                    open(datadrop_save_path + datadrop['serial'] + '/' + 'service_versions', 'w').write(json.dumps(service_versions))
                    """
                except:
                    pass

                try:
                    content_of_versions = rings.get_content_of_versions()
                    service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
                    service_pack = rings.calculate_service_pack(service_versions, service_pack_list)
                except:
                    service_pack = 'SP.???'

                # tell this device what versions it should be running
                # build a blank result as the default
                id = datadrop['serial']

                response_d = {}

                # load the full profile as the default starting point
                try:
                    # see if there is a profile to load up
                    profile = datastore.get_value('device_profile_' + id)
                    # profile = datastore.get_value('device_profile_' + '10000000e3669edf')

                    if profile:
                        profile_content = datastore.get_value(profile)
                        if profile_content:
                            response_d = json.loads(profile_content)
                except:
                    pass

                # look for url datastore_ replacements wanted
                datastore_pull_list = make_datastore_pull_list_from_response(response_d)
                if datastore_pull_list:
                    # do the work
                    selected_data_store = {}
                    for item in datastore_pull_list:
                        selected_data_store[item] = datastore.get_value(item)
                    response_d = make_new_response_from_selected_data_store_content(response_d, selected_data_store)

                # look for timezone override
                address = get_device_address(id)
                if address:
                    site = address2location.location(address)
                    timezone_override = datastore.get_value('site_timezone_override_' + site)
                    if timezone_override:
                        timezone_override_to_use = ''
                        if timezone_override != '(profile)':
                            timezone_override_to_use = timezone_override

                        if timezone_override_to_use:
                            response_d['timezone'] = timezone_override_to_use

                # add any info_ items
                response_d['info_service_pack'] = service_pack

                ring_updates = '0'
                try:
                    with open('/dev/shm/rings_report.txt', 'r') as f:
                        rings_report = json.loads(f.read())
                except:
                    rings_report = {}
                if id in rings_report:
                    ring_updates = str(len(rings_report[id]))
                response_d['info_ring_updates'] = ring_updates
                response_d['info_ring_level'] = rings.device_ring_calculated(id)

                time_to_1 = time.time()
                time_to_2 = time_to_1

                # add any additional content that is available
                try:
                    updates_shown = {}
                    if id in rings_report:
                        for item in rings_report[id]:
                            updates_shown[item] = rings_report[id][item]

                    count_items = -1
                    if updates_shown:
                        for item in sorted(updates_shown):
                            count_items += 1
                            response_d['info_ring_service_' + str(count_items)] = item + '|' + updates_shown[item]

                    # terminate the list with an marker
                    count_items += 1
                    response_d['info_ring_service_' + str(count_items)] = '(empty)'


                except:
                    pass


                # add any additional content that is available
                try:
                    services_d = {}
                    # list out the services
                    services_available = codeupload.get_pi_services()
                    # { "pi_runner":{ "R.1.0":"/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py" } }
                    service_names = services_available.keys()

                    # see if there are entries that are not yet picked up, and build an original looking dictionary
                    for service_name in service_names:
                        ready_to_pull = datastore.get_value('device_service_request_' + id + '_pull_' + service_name)
                        if ready_to_pull:
                            services_d[service_name] = ready_to_pull

                            # clear out the entry once we have 'taken it'
                            datastore.set_value('device_service_request_' + id + '_pull_' + service_name, '',
                                                who='slicer.datadrop')

                    # old as of 2021.09.09
                    #                    services_to_use = datastore.get_value('device_service_'+id)
                    #                    services_d = json.loads(services_to_use)
                    if services_d:
                        response_d['services'] = services_d
                except:
                    pass

                # add any additional content that is available
                try:
                    name_to_use, trust_value = datastore.get_value_with_trust('device_name_' + id)
                    if trust_value:
                        response_d['name_to_use'] = {"(id)": name_to_use.replace("|", "<br>")}
                except:
                    pass

                # add any additional content that is available
                try:
                    browser_zoom = datastore.get_value('device_browser_zoom_' + id)
                    if browser_zoom:
                        response_d['browser_zoom'] = {"(id)": browser_zoom}
                except:
                    pass

                # add any additional content that is available
                try:
                    value_to_use, trust_value = datastore.get_value_with_trust('device_screen_resolution_' + id)
                    if trust_value:
                        response_d['screen_resolution'] = {"(id)": value_to_use}
                except:
                    pass


                # add any additional content that is available
                try:
                    value_to_use, trust_value = datastore.get_value_with_trust('device_special_menu_' + id)
                    if trust_value:
                        response_d['special_menu'] = {"(id)": value_to_use}
                except:
                    pass

                # add any additional content that is available
                try:
                    device_clockview_setting = datastore.get_value('device_clockview_' + id)
                    if device_clockview_setting:
                        response_d['clockview_enabled'] = {"(id)": device_clockview_setting}
                except:
                    pass

                try:
                    device_managementID = datastore.get_value('device_managementID_' + id)
                    response_d['conf_device_managementID'] = {
                        "(id)": device_managementID}  # put it out as a conf_ so that the pi_runner builds it for free
                except:
                    pass

                try:
                    conf_extra_boots = datastore.get_value('device_extra_boots_' + id)
                    response_d['conf_extra_boots'] = {
                        "(id)": conf_extra_boots}  # put it out as a conf_ so that the pi_runner builds it for free
                except:
                    pass

                # add any additional content that is available
                try:
                    for config_item, default_setting in [('wifi_connect', 'corp'), ('wifi_hopper', 'no-hop'),
                                                         ('wifi_coaster', '0')]:
                        if 'dev_' + config_item in datadrop:  # look for a device side value that is different, and use it
                            dev_setting = datadrop['dev_' + config_item]
                            conf_setting = datastore.get_value('conf_' + config_item + '_' + id)
                            if not conf_setting == dev_setting:
                                datastore.set_value('conf_' + config_item + '_' + id, dev_setting,
                                                    who='slicer.datadrop')

                        conf_setting = datastore.get_value('conf_' + config_item + '_' + id)
                        if not conf_setting:
                            conf_setting = default_setting  # be sure that a blank setting still gets the default
                        if conf_setting:
                            response_d['conf_' + config_item] = {"(id)": conf_setting}
                except:
                    pass

                # add any additional content that is available
                try:
                    device_bluetooth_setting = datastore.get_value('device_bluetooth_' + id)
                    if device_bluetooth_setting:
                        response_d['bluetooth_enabled'] = {"(id)": device_bluetooth_setting}
                except:
                    pass

                # add any additional content that is available
                try:
                    reset_request = datastore.get_value('device_reset_request_' + id)
                    if reset_request:
                        response_d['reset_request'] = {"(id)": reset_request}

                        # now that we have loaded up the request, clear it from the datastore
                        datastore.set_value('device_reset_request_' + id, '', who='slicer.datadrop')
                except:
                    pass

                # add any additional content that is available
                try:
                    reboot_request = datastore.get_value('device_reboot_request_' + id)
                    if reboot_request:
                        response_d['reboot_request'] = {"(id)": reboot_request}

                        # now that we have loaded up the request, clear it from the datastore
                        datastore.set_value('device_reboot_request_' + id, '', who='slicer.datadrop')
                except:
                    pass

                # add any additional content that is available
                try:
                    screengrab_request = datastore.get_value('device_screengrab_request_' + id)
                    if screengrab_request:
                        response_d['screengrab_request'] = {"(id)": screengrab_request}

                        # now that we have loaded up the request, clear it from the datastore
                        datastore.set_value('device_screengrab_request_' + id, '', who='slicer.datadrop')
                except:
                    pass

                # add any additional content that is available
                try:
                    the_setting = datastore.get_value('showgif' + id)
                    if the_setting:
                        response_d['browser_timeout'] = {"(id)": the_setting}
                    else:
                        response_d['browser_timeout'] = {"(id)": '0'}
                except:
                    pass

                # add any additional content that is available
                try:
                    the_setting = open(base_upload_path + 'showgif_' + id + '.gif.md5').read()
                    if the_setting:
                        response_d['showgif_md5'] = {"(id)": the_setting}
                    else:
                        pass
                except:
                    pass


                # generic file transfer list....
                _ = """
Multimedia:

If we do a generic list of files, then there needs to be an interface (live data table), that
shows what is set to be transfered, and what has been confirmed back as being on the device.

For instance, we want to have three different training videos loaded on the device,
and then have the menu interface show links to each... Titles are in the profile, with file names?

Then, how does the multimedia interface need to change, to accomodate the fact that we have many files per device(s)
to be loaded?
    Make the existing file be the "1" link file.

    Make a title field, to show instead of the profile title.

    Make a new profile, that is "dynamic multimedia", so that title, autolaunch,
        and content get define in the multimedia interface.

    profile_VPN1_multimedia_Show1
        orig:
            - url: file:///cardinal/localhtml/showgif.html
            - format: ''
        new:
            - url: file:///cardinal/localhtml/showmedia.html
            - format: 'dynamic_media'



                """

                # do the do_command lookup
                try:
                    time_to_2 = time.time()
                    time_to_3 = time_to_2
                    time_to_4 = time_to_2
                    time_to_5 = time_to_2

# FixMe: !!!! This comment out of the call is just temporary as a test
                    if True:
                        if False:
                            command_id = devicecommand.next_command_for_id_and_remove(id)
                        else:
                            command_d = devicecommand._get_all_command_info(option='skip_responses')
                            if 'timing' in command_d:
                                debug_string += '\n' + str(command_d['timing'])
                            time_to_3 = time.time()
                            command_id = command_id = devicecommand._get_next_device_command_for_id(id, command_d)
                            time_to_4 = time.time()
                            devicecommand._remove_command_for_id(command_id, id)
                            time_to_5 = time.time()

                        if command_id:
                            response_d['do_command'] = {"(id)": command_id}
                            debug_string += '\n' + command_id
                except:
                    debug_string = traceback.format_exc()

                    # {"services": {"pi_runner": "R.1.0", "pi_hmi": "H.0.7"}}

                response_json = json.dumps(response_d)

                # make it personal to this device
                response_json = response_json.replace("(id)", id)


            # -------------------------------------
            if datadrop['source'] == 'security':
                # -------------------------------------
                # build a blank response as the default
                response_d = {}

                # add any additional content that is available
                try:
                    action_request = datastore.get_value('device_osupdates_request_' + id)
                    if action_request:
                        response_d['action_request'] = {"(id)": action_request}

                        # now that we have loaded up the request, clear it from the datastore
                        datastore.set_value('device_osupdates_request_' + id, '', who='slicer.datadrop')
                except:
                    pass

                response_json = json.dumps(response_d)

                # make it personal to this device
                response_json = response_json.replace("(id)", id)

            # -------------------------------------
            if datadrop['source'] == 'thirdparty':
                # -------------------------------------
                # build a blank response as the default
                response_d = {}

                # add any additional content that is available
                try:
                    thirdparty_list = datastore.get_value('thirdparty_list' + id)
                    thirdparty_list = 'testapp'
                    if thirdparty_list:
                        response_d['thirdparty_list'] = {"(id)": thirdparty_list}
                except:
                    pass
                response_json = json.dumps(response_d)

                # make it personal to this device
                response_json = response_json.replace("(id)", id)

            # -------------------------------------
            if datadrop['source'] == 'network':
                # -------------------------------------
                # build a blank profile as the default
                response_json = """{"bookmarks":{"(id)":{}}}"""

                # start: Augment the version details, but only if there is an active runner checking in
                all_files_content = {}
                for file in os.listdir(datadrop_save_path + datadrop['serial'] + '/'):
                    try:
                        content = open(datadrop_save_path + datadrop['serial'] + '/' + file, 'r').read()
                        all_files_content[file] = content
                    except:
                        pass
                service_versions = algorithms.build_versions_summary(all_files_content)
                try:
                    do_atomic_write(datadrop_save_path + datadrop['serial'] + '/' + 'service_versions',
                                    json.dumps(service_versions))
                    _ = """
                    open(datadrop_save_path + datadrop['serial'] + '/' + 'service_versions', 'w').write(json.dumps(service_versions))
                    """
                except:
                    pass
                # end: Augment the version details

                try:
                    # see if there is a profile to load up
                    profile = datastore.get_value('device_profile_' + id)
                    # profile = datastore.get_value('device_profile_' + '10000000e3669edf')

                    if profile:
                        response_json = datastore.get_value(profile)

                except:
                    response_json = '<br>' + 'exception' + str(
                        traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
                    pass

                # make it personal to this device
                response_json = response_json.replace("(id)", id)

    except:
        notes += '<br>' + 'exception' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    status = '200 OK'

    if response_json:
        # going back to a device
        html = response_json
        response_header = [('Content-type', 'text/html')]

        # allow non wrapped response
        start_response(status, response_header)

        time_end = time.time()
        output_file = '/dev/shm/datadrop_device_response_time.txt'
        # watch -n 0.1 cat /dev/shm/datadrop_device_response_time.txt
        time_diff = time_end - time_start
        if time_diff > 2.0: # most are quick, so just look at the long running ones
            try:
                time_diff_1 = time_to_1 - time_start
                time_diff_2 = time_to_2 - time_to_1
                time_diff_3 = time_to_3 - time_to_2
                time_diff_4 = time_to_4 - time_to_3
                time_diff_5 = time_to_5 - time_to_4
                report = '8' + \
                        ' ' + datadrop['serial'] + \
                        ' ' + '{:.3f}'.format(time_diff) + \
                        ' ' + datadrop['source'] + \
                        ' ' + '{:.3f}'.format(time_diff_1) + \
                        ' ' + '{:.3f}'.format(time_diff_2) + \
                        ' ' + '{:.3f}'.format(time_diff_3) + \
                        ' ' + '{:.3f}'.format(time_diff_4) + \
                        ' ' + '{:.3f}'.format(time_diff_5) + \
                        ' ' + debug_string
            except:
                report = traceback.format_exc()
            do_atomic_write(output_file,report)

        return [html.encode()]
    else:
        html = '<html>\n' \
               '<body>\n' \
               '<div style="width: 100%; font-size: 12px; font-weight: bold; text-align: center;">\n'
        html += str(sys.version_info) + '<br><br>'
        html += str(environ['QUERY_STRING']) + '<br>'
        html += str(notes) + '<br>'
        html += str(network_response_file) + '<br>'

        html += '</div>\n' \
                '</body>\n' \
                '</html>\n'
        response_header = [('Content-type', 'text/html')]

        try:
# take out, to test if production server is more responsive (organization call might be doing the chown -R every data call
#            html = organization.wrap_page_with_session(environ, html)
            start_response(status, response_header)
        except:
            # still on slicer01
            # allow non wrapped response
            start_response(status, response_header)


        return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        if pass_count % 60 == 0:
            try:
                build_all_rollups()
            except:
                pass

        if pass_count % 600 == 1: # do this on the very first pass, and then every 5 minutes after that
            # do the permissions / context cleanup
            pass


        time.sleep(1)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_list_to_info(self):
        """
        (fill in here)
        """
        response_d = {}
        id = '10000000e3669edf'
        rings_report = {}
        rings_report[id] = {}
        rings_report[id] = {'pi_logging': 'L.2.4'}

        updates_shown = {}
        updates_shown['pi_test'] = 'T.0.0'
        updates_shown['pi_test2'] = 'T.0.1'

        count_items = -1
        if updates_shown:
            for item in sorted(updates_shown):
                count_items += 1
                response_d['info_ring_service_' + str(count_items)] = item + '|' + updates_shown[item]

        # terminate the list with an marker
        count_items += 1
        response_d['info_ring_service_' + str(count_items)] = '(empty)'

        self.assertEqual(response_d, {'info_ring_service_2': '(empty)', 'info_ring_service_1': 'pi_test2|T.0.1',
                                      'info_ring_service_0': 'pi_test|T.0.0'})

    def test_day_hour_is_in_last_X_hours(self):
        previous_hours = 8

        current_day_hour = '20220729.13'
        test_day_hour = '20220729.13'
        expected = True
        actual = day_hour_is_in_last_X_hours(test_day_hour, current_day_hour, previous_hours)
        self.assertEqual(actual, expected)

        current_day_hour = '20220729.13'
        test_day_hour = '20220729.14'
        expected = False
        actual = day_hour_is_in_last_X_hours(test_day_hour, current_day_hour, previous_hours)
        self.assertEqual(actual, expected)

        current_day_hour = '20220729.13'
        test_day_hour = '20220729.04'
        expected = False
        actual = day_hour_is_in_last_X_hours(test_day_hour, current_day_hour, previous_hours)
        self.assertEqual(actual, expected)

    def test_load_value_statistics(self):
        raw_load_data = [
            {'raw': ['0.30_0.37_0.37_1/390_30384', '0.50_0.42_0.38_1/390_30879'], 'day_hour': '20220729.12'},
            {'raw': ['0.70_0.48_0.46_1/389_21297', '0.10_0.37_0.42_1/384_22739'], 'day_hour': '20220729.13'},
            {'raw': ['0.90_0.48_0.46_1/389_21297'], 'day_hour': '20220729.23'}]

        current_day_hour = '20220729.13'
        expected = 0.6
        actual = get_average_peak_load_in_last_eight_hours(raw_load_data, current_day_hour)
        self.assertEqual(actual, expected)

        current_day_hour = '20220729.12'
        expected = 0.5
        actual = get_average_peak_load_in_last_eight_hours(raw_load_data, current_day_hour)
        self.assertEqual(actual, expected)

        raw_load_data = [{'raw': [], 'day_hour': '20220729.12'}]

        current_day_hour = '20220729.13'
        expected = 0
        actual = get_average_peak_load_in_last_eight_hours(raw_load_data, current_day_hour)
        self.assertEqual(actual, expected)

    def test_make_new_response_from_selected_data_store_content(self):
        self.maxDiff = None
        # profile_IL002_multimedia_DisplayBoard_Dates
        profile_content = '{"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true, "url": "http://localhost:7010?reload_key=1,safe_day_start=(datastore_user_data_IL002_safe_day_start),days_after_today=(datastore_user_data_IL002_short_date_delay_days),years_after_this_year=(datastore_user_data_IL002_coding_date_delay_years),background_color=(0~0~0~1.0),font_bold=yes,font_color=(255~255~255~1.0),cellpadding=120,font_size=150,row1=Coding_Date|(Coding_Date),row2=Short_Date|(Short_Date),row3=Safe_Days|(Safe_Days)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}'
        response_d = json.loads(profile_content)

        # ---------------------------
        # a simpler one
        profile_content = '{"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true, "url": "http://localhost:7010?reload_key=1,safe_day_start=(datastore_user_data_IL002_safe_day_start)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}'
        response_d = json.loads(profile_content)

        expected = ['user_data_IL002_safe_day_start']
        actual = make_datastore_pull_list_from_response(response_d)
        self.assertEqual(actual, expected)

        # now update the response
        selected_data_store = {'user_data_IL002_safe_day_start':'2024.01.01'}
        expected = {"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": True, "url": "http://localhost:7010?reload_key=1,safe_day_start=2024.01.01", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}
        actual = make_new_response_from_selected_data_store_content(response_d, selected_data_store)
        self.assertEqual(actual, expected)
        # ---------------------------

        # ---------------------------
        # make multiples, expect only one
        profile_content = '{"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true, "url": "http://localhost:7010?reload_key=1,safe_day_start=(datastore_user_data_IL002_safe_day_start),junk=(datastore_user_data_IL002_safe_day_start)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}'
        response_d = json.loads(profile_content)

        expected = ['user_data_IL002_safe_day_start']
        actual = make_datastore_pull_list_from_response(response_d)
        self.assertEqual(actual, expected)

        # ---------------------------
        # make different, expect two
        profile_content = '{"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true, "url": "http://localhost:7010?reload_key=1,safe_day_start=(datastore_user_data_IL002_safe_day_start),junk=(datastore_user_data_IL002_safe_day_start_2)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}'
        response_d = json.loads(profile_content)

        expected = ['user_data_IL002_safe_day_start', 'user_data_IL002_safe_day_start_2']
        actual = make_datastore_pull_list_from_response(response_d)
        self.assertEqual(actual, expected)


    def test_make_new_response_from_selected_data_store_content_parameter_static(self):
        self.maxDiff = None
        profile_content = '{"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true, "url": "http://localhost:7010?reload_key=1,static_coding_date=(datastore_user_data_IL002_parameter_coding_date),row1=Coding_Date|(user_data_IL002_parameter_coding_date)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}'
        response_d = json.loads(profile_content)

        # now update the response
        selected_data_store = {'user_data_IL002_parameter_coding_date':'12/31/2069'}
        expected = {"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": True, "url": "http://localhost:7010?reload_key=1,static_coding_date=12/31/2069,row1=Coding_Date|12/31/2069", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}
        actual = make_new_response_from_selected_data_store_content(response_d, selected_data_store)
        self.assertEqual(actual, expected)


















# end of file
