service = "intake"
version = service + '.0.1'

# An intake for slicer page services
#  Expect sources like:
#     - SNOW data for device(s)
#     - Clearpass data for MAC address IOT listing(s)
#  For now:
#    Simulate those data sources, with hardwired knowledge

_ = """
This file gets loaded to:
/var/www/html/intake.py

using:
sudo vi /var/www/html/intake.py

It also requires:

sudo vi /etc/httpd/conf.d/python-intake.conf
----- start copy -----
WSGIScriptAlias /intake /var/www/html/intake.py
----- end copy -----

sudo chown apache:apache /var/www/html/intake.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/intake-runner
sudo chmod +x /var/www/html/intake-runner

# ===== begin: start file
#!/usr/bin/env python
import intake
intake.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/intake-runner.service
sudo systemctl daemon-reload
sudo systemctl stop intake-runner.service
sudo systemctl start intake-runner.service
sudo systemctl enable intake-runner.service

systemctl status intake-runner.service

sudo systemctl restart intake-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep intake-runner

OR

tail -f /var/log/syslog | fgrep intake-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/intake-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import intake; print(intake.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/intake

https://slicer.cardinalhealth.net/intake?siteid=PR005

https://slicer.cardinalhealth.net/intake?serial=100000002a5da842

https://slicer.cardinalhealth.net/intake?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_intake


"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:  # for unittest to work
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_list_of_mac_in_iot():
    # ----------------------------
    the_content = {
        # daves test device
        'DC-A6-32-96-56-C4': '',
        # IOT-1
        'DC-A6-32-31-A6-EE': '',
        'DC-A6-32-53-96-67': '',
        'DC-A6-32-5C-42-B8': '',
        'DC-A6-32-98-04-A7': '',
        'DC-A6-32-98-0F-21': '',
        'DC-A6-32-A3-A1-1D': '',
        '': '',
    }  # hard code the mac for daves one test unit

    # 2023.02.10 Jim Steiner says that our spreadsheet full was imported
    new_list = ['DC-A6-32-A5-C1-C7', 'E4-5F-01-97-F2-73', 'DC-A6-32-A5-C2-03', 'E4-5F-01-97-DB-9E', 'DC-A6-32-9D-AB-4E',
                'E4-5F-01-97-D7-4B', 'DC-A6-32-39-A8-4F', 'DC-A6-32-C5-E2-4B', 'DC-A6-32-41-82-E6', 'DC-A6-32-7C-28-EA',
                'DC-A6-32-98-66-4E', 'DC-A6-32-98-66-45', 'DC-A6-32-A5-C3-80', 'DC-A6-32-98-66-33', 'E4-5F-01-97-D7-06',
                'DC-A6-32-53-97-B7', 'DC-A6-32-41-81-60', 'DC-A6-32-A5-C1-AC', 'DC-A6-32-17-18-70', 'DC-A6-32-A5-C2-24',
                'DC-A6-32-53-97-4B', 'DC-A6-32-B7-7B-41', 'DC-A6-32-53-96-19', 'DC-A6-32-53-96-52', 'E4-5F-01-97-D7-60',
                'DC-A6-32-98-60-18', 'DC-A6-32-7C-2B-54', 'DC-A6-32-53-98-83', 'DC-A6-32-A5-C3-8C', 'DC-A6-32-A5-C0-B9',
                'DC-A6-32-39-A9-30', 'DC-A6-32-C5-E2-7E', 'DC-A6-32-E1-E0-3E', 'DC-A6-32-53-97-63', 'DC-A6-32-A5-BF-7E',
                'DC-A6-32-41-4C-11', 'DC-A6-32-A5-C0-EF', 'E4-5F-01-97-F2-54', 'DC-A6-32-B7-5C-9C', 'DC-A6-32-E2-06-72',
                'DC-A6-32-98-66-5A', 'DC-A6-32-C5-E2-5A', 'E4-5F-01-98-20-2F', 'DC-A6-32-7C-15-13', 'DC-A6-32-98-66-5D',
                'DC-A6-32-C5-E2-5D', 'DC-A6-32-C5-E2-66', 'DC-A6-32-98-66-3B', 'DC-A6-32-A5-C1-22', 'DC-A6-32-A5-C1-D3',
                'DC-A6-32-A5-C2-6C', 'DC-A6-32-C5-E0-47', 'DC-A6-32-A5-C3-86', 'DC-A6-32-A5-C2-84', 'DC-A6-32-98-66-3F',
                'DC-A6-32-C5-E2-27', 'DC-A6-32-C5-E2-9C', 'DC-A6-32-98-66-48', 'DC-A6-32-E1-F8-A7', 'DC-A6-32-C5-E0-A4',
                'DC-A6-32-B7-6C-2F', 'DC-A6-32-53-95-E3', 'DC-A6-32-90-E9-75', 'DC-A6-32-A5-C2-EE', 'DC-A6-32-A5-C1-CA',
                'E4-5F-01-97-DB-8F', 'DC-A6-32-41-82-20', 'DC-A6-32-B7-61-E8', 'DC-A6-32-C5-94-12', 'DC-A6-32-A5-BE-A3',
                'DC-A6-32-9D-B0-5C', 'DC-A6-32-B7-7B-3B', 'DC-A6-32-A5-C2-1E', 'DC-A6-32-7C-06-7C', 'DC-A6-32-A5-C2-96',
                'DC-A6-32-7C-2B-C6', 'DC-A6-32-41-82-FB', 'DC-A6-32-B7-7B-74', 'DC-A6-32-9D-AF-F9', 'DC-A6-32-53-97-FC',
                'DC-A6-32-B7-7B-3E', 'DC-A6-32-A5-C2-F6', 'DC-A6-32-A5-C3-BC', 'DC-A6-32-39-6A-DE', 'DC-A6-32-39-69-2B',
                'DC-A6-32-39-5F-41', 'DC-A6-32-39-6B-5C', 'DC-A6-32-39-62-56', 'DC-A6-32-39-6C-F2', 'DC-A6-32-39-68-6B',
                'DC-A6-32-39-57-E5', 'DC-A6-32-39-67-C0', 'DC-A6-32-39-57-5D', 'DC-A6-32-39-4F-9C', 'DC-A6-32-39-68-77',
                'DC-A6-32-39-6A-53', 'DC-A6-32-39-54-A3', 'DC-A6-32-39-59-FB', 'DC-A6-32-39-6D-09', 'DC-A6-32-39-6B-8C',
                'DC-A6-32-39-68-53', 'DC-A6-32-39-4F-24', 'DC-A6-32-39-68-92', 'DC-A6-32-39-6A-BA', 'DC-A6-32-39-6D-4A',
                'DC-A6-32-39-69-D0', 'DC-A6-32-39-03-90', 'DC-A6-32-39-68-B9', 'DC-A6-32-39-4F-87', 'DC-A6-32-39-63-F7',
                'DC-A6-32-39-6B-EC', 'DC-A6-32-39-6B-9E', 'DC-A6-32-39-65-85', 'DC-A6-32-39-5C-E9', 'DC-A6-32-39-6D-CC',
                'DC-A6-32-39-6A-ED', 'DC-A6-32-39-6D-F3', 'DC-A6-32-39-6B-74', 'DC-A6-32-39-61-2D', 'DC-A6-32-39-6D-D2',
                'DC-A6-32-39-68-C5', 'DC-A6-32-39-4F-93', 'DC-A6-32-39-69-E8', 'DC-A6-32-39-64-72', 'DC-A6-32-39-6C-1C',
                'DC-A6-32-39-4F-DE', 'DC-A6-32-39-6A-21', 'DC-A6-32-39-67-9F', 'DC-A6-32-39-6C-F7', 'DC-A6-32-39-50-3E',
                'DC-A6-32-39-68-C2', 'DC-A6-32-39-67-FF', 'DC-A6-32-69-66-B5', 'DC-A6-32-98-32-73', 'E4-5F-01-2E-98-70',
                'DC-A6-32-B2-CC-C9', 'DC-A6-32-72-37-1F', 'DC-A6-32-98-32-AF', 'DC-A6-32-98-31-E0', 'DC-A6-32-98-32-04',
                'E4-5F-01-2E-79-FD', 'E4-5F-01-2E-98-61', 'E4-5F-01-2E-83-94', 'DC-A6-32-98-31-B6', 'DC-A6-32-98-31-9E',
                'DC-A6-32-98-32-7F', 'E4-5F-01-2E-92-4E', 'DC-A6-32-98-31-98', 'DC-A6-32-98-31-D7', 'DC-A6-32-98-32-1F',
                'DC-A6-32-98-31-50', 'DC-A6-32-31-A6-01', 'E4-5F-01-2E-98-DD', 'DC-A6-32-31-A6-BE', 'DC-A6-32-98-32-76',
                'DC-A6-32-98-32-BE', 'DC-A6-32-31-A6-C4', 'DC-A6-32-98-31-F5', 'DC-A6-32-98-31-B3', 'DC-A6-32-98-04-A7',
                'DC-A6-32-98-31-A7', 'DC-A6-32-72-33-B9', 'DC-A6-32-31-A6-5B', 'DC-A6-32-31-A7-36', 'DC-A6-32-31-A5-4E',
                'DC-A6-32-98-32-5B', 'DC-A6-32-98-32-01', 'E4-5F-01-2E-90-95', 'DC-A6-32-98-32-88', 'DC-A6-32-98-31-C2',
                'DC-A6-32-31-A5-80', 'DC-A6-32-31-A7-09', 'DC-A6-32-98-31-A1', 'DC-A6-32-98-32-7C', 'DC-A6-32-31-A6-AF',
                'DC-A6-32-98-31-CB', 'DC-A6-32-31-A6-F1', 'DC-A6-32-31-A6-B8', 'E4-5F-01-2E-92-8C', 'DC-A6-32-72-36-0F',
                'DC-A6-32-98-32-94', 'DC-A6-32-98-08-28', 'DC-A6-32-98-31-EC', 'DC-A6-32-98-32-5E', 'DC-A6-32-69-64-C3',
                'E4-5F-01-2E-92-92', 'E4-5F-01-2E-8D-3B', 'DC-A6-32-98-31-D4', 'DC-A6-32-98-0D-62', 'DC-A6-32-98-31-DA',
                'DC-A6-32-72-2B-CA', 'DC-A6-32-31-A5-7A', 'DC-A6-32-98-0F-21', 'DC-A6-32-98-32-AC', 'DC-A6-32-98-31-8C',
                'DC-A6-32-98-32-22', 'DC-A6-32-98-31-77', 'DC-A6-32-69-64-69', 'DC-A6-32-39-A8-EB', 'DC-A6-32-98-31-89',
                'DC-A6-32-98-31-FB', 'DC-A6-32-31-A2-DA', 'DC-A6-32-98-31-EF', 'DC-A6-32-98-31-5C', 'DC-A6-32-65-12-B8',
                'DC-A6-32-31-A7-0F', 'DC-A6-32-31-A5-44', 'DC-A6-32-31-92-F0', 'DC-A6-32-65-12-5C', 'DC-A6-32-31-9C-86',
                'DC-A6-32-31-A5-BF', 'DC-A6-32-76-BC-5D', 'DC-A6-32-31-A6-7F', 'DC-A6-32-31-A6-74', 'DC-A6-32-65-12-11',
                'DC-A6-32-31-A6-3E', 'DC-A6-32-31-A6-6A', 'DC-A6-32-31-A6-79', 'DC-A6-32-31-A7-13', 'DC-A6-32-31-A7-06',
                'DC-A6-32-31-A6-4E', 'DC-A6-32-31-A6-10', 'DC-A6-32-31-A6-EE', 'DC-A6-32-31-A2-EC', 'DC-A6-32-31-A6-75',
                'DC-A6-32-31-A6-07', 'DC-A6-32-31-A6-8B', 'DC-A6-32-31-A6-3F', 'DC-A6-32-31-A5-38', 'DC-A6-32-31-A6-A9',
                'DC-A6-32-31-A6-BB', 'DC-A6-32-31-A6-FD', 'DC-A6-32-98-31-4A', 'DC-A6-32-A3-A1-1D', 'DC-A6-32-31-A5-7D',
                'DC-A6-32-31-9B-FC', 'DC-A6-32-65-12-65', 'DC-A6-32-31-A6-CD', 'DC-A6-32-31-A6-55', 'DC-A6-32-31-8B-5E',
                'DC-A6-32-31-A6-94', 'DC-A6-32-AA-FC-D8', 'DC-A6-32-53-98-62', 'DC-A6-32-79-0B-B3', 'DC-A6-32-53-96-79',
                'DC-A6-32-53-97-68', 'DC-A6-32-5B-EE-B7', 'DC-A6-32-30-13-71', 'DC-A6-32-79-0A-1B', 'DC-A6-32-79-0B-26',
                'DC-A6-32-79-0A-33', 'DC-A6-32-30-13-3E', 'DC-A6-32-5B-C7-7C', 'DC-A6-32-30-14-07', 'DC-A6-32-53-98-5A',
                'DC-A6-32-5B-F2-F0', 'DC-A6-32-79-0C-88', 'DC-A6-32-5C-5B-87', 'DC-A6-32-53-99-49', 'DC-A6-32-53-84-13',
                'DC-A6-32-53-98-0E', 'DC-A6-32-5C-45-8E', 'DC-A6-32-53-95-FA', 'DC-A6-32-5C-5C-EF', 'DC-A6-32-78-D4-93',
                'DC-A6-32-53-98-7D', 'DC-A6-32-79-0C-04', 'DC-A6-32-53-97-CF', 'DC-A6-32-79-09-E8', 'DC-A6-32-5B-E1-EC',
                'DC-A6-32-79-0B-8F', 'DC-A6-32-79-0A-DE', 'DC-A6-32-78-DA-BF', 'DC-A6-32-79-0C-67', 'DC-A6-32-5C-5C-53',
                'DC-A6-32-5C-5B-FF', 'DC-A6-32-79-09-BB', 'DC-A6-32-82-E7-B3', 'DC-A6-32-37-F2-D3', 'DC-A6-32-30-13-77',
                'DC-A6-32-53-87-15', 'DC-A6-32-5C-43-21', 'DC-A6-32-78-DB-68', 'DC-A6-32-5C-E5-B9', 'DC-A6-32-5C-5B-DF',
                'DC-A6-32-79-0A-D5', 'DC-A6-32-30-14-28', 'DC-A6-32-82-DC-2A', 'DC-A6-32-53-62-05', 'DC-A6-32-79-09-D6',
                'DC-A6-32-5C-5B-96', 'DC-A6-32-37-DD-7C', 'DC-A6-32-53-97-03', 'DC-A6-32-5C-43-AF', 'DC-A6-32-53-95-5F',
                'DC-A6-32-5C-5B-84', 'DC-A6-32-79-0C-19', 'DC-A6-32-79-0B-BC', 'DC-A6-32-38-8B-CA', 'DC-A6-32-53-98-5F',
                'DC-A6-32-30-13-B6', 'DC-A6-32-53-95-4A', 'DC-A6-32-79-0A-68', 'DC-A6-32-53-82-90', 'DC-A6-32-30-13-E3',
                'DC-A6-32-5C-44-8C', 'DC-A6-32-79-0D-18', 'DC-A6-32-53-95-A1', 'DC-A6-32-53-97-ED', 'DC-A6-32-30-13-1D',
                'DC-A6-32-53-97-7E', 'DC-A6-32-5C-5B-9C', 'DC-A6-32-5C-5C-29', 'DC-A6-32-5C-42-67', 'DC-A6-32-79-09-7F',
                'DC-A6-32-53-82-9C', 'DC-A6-32-53-85-E7', 'DC-A6-32-79-0C-70', 'DC-A6-32-1F-2E-B4', 'DC-A6-32-53-95-89',
                'DC-A6-32-5C-5C-2F', 'DC-A6-32-5C-44-F7', 'DC-A6-32-53-97-1D', 'DC-A6-32-53-98-BF', 'DC-A6-32-5C-5C-0B',
                'DC-A6-32-79-0A-FF', 'DC-A6-32-79-0C-A3', 'DC-A6-32-5C-42-AF', 'DC-A6-32-5C-5C-5F', 'DC-A6-32-30-13-4A',
                'DC-A6-32-30-0C-51', 'DC-A6-32-5C-5C-7D', 'DC-A6-32-79-0C-40', 'DC-A6-32-1E-F6-02', 'DC-A6-32-79-0C-8F',
                'DC-A6-32-5C-41-FC', 'DC-A6-32-53-97-90', 'DC-A6-32-5C-5B-81', 'DC-A6-32-79-0C-48', 'DC-A6-32-82-EA-B0',
                'DC-A6-32-5C-5B-8E', 'DC-A6-32-79-09-D2', 'DC-A6-32-53-96-F7', 'DC-A6-32-79-0C-01', 'DC-A6-32-5C-42-C9',
                'DC-A6-32-30-13-8C', 'DC-A6-32-53-96-67', 'DC-A6-32-5C-5B-57', 'DC-A6-32-5C-43-99', 'DC-A6-32-5C-42-BB',
                'DC-A6-32-53-96-10', 'DC-A6-32-5B-DE-F8', 'DC-A6-32-79-0A-DB', 'DC-A6-32-5C-5C-A5', 'DC-A6-32-79-0A-42',
                'DC-A6-32-1F-2B-DB', 'DC-A6-32-30-13-C6', 'DC-A6-32-79-09-EE', 'DC-A6-32-5C-1A-5F', 'DC-A6-32-5C-43-B5',
                'DC-A6-32-5C-44-A9', 'DC-A6-32-79-09-D1', 'DC-A6-32-78-D9-16', 'DC-A6-32-5C-42-B8', 'DC-A6-32-5C-25-A5',
                'DC-A6-32-53-97-21', 'DC-A6-32-8F-C4-38', 'DC-A6-32-79-0B-14', 'DC-A6-32-30-14-20', 'DC-A6-32-53-97-06',
                'DC-A6-32-53-69-DF', 'DC-A6-32-53-96-FB', 'DC-A6-32-20-AB-73', 'DC-A6-32-79-0A-45']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.02.23 Additional devices that are now online, that need added
    new_list = ['DC-A6-32-5B-D4-79', 'DC-A6-32-82-E6-74', 'E4-5F-01-97-F4-10', 'E4-5F-01-98-20-11']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.03.29 Emailed these to Aaron
    # <AUTHOR> <EMAIL>
    _ = """
raspberrypi,DC-A6-32-98-31-71,10000000bc455595,MEX04
raspberrypi,DC-A6-32-49-EC-F6,100000000dcb320b,OH085 (really is AZ032)
raspberrypi,DC-A6-32-5C-43-5D,10000000c8697fd3,PR005
raspberrypi,E4-5F-01-97-C3-A0,10000000d95741ab,DOM02
    """
    new_list = ['DC-A6-32-98-31-71', 'DC-A6-32-49-EC-F6', 'DC-A6-32-5C-43-5D', 'E4-5F-01-97-C3-A0']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.04.03, Daves test at East
    _ = """
raspberrypi,DC-A6-32-07-E2-D1,100000000e3c804f,OH085
    """
    new_list = ['DC-A6-32-07-E2-D1']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.05.09 asked for this one to add (just found it online, not listed
    new_list = ['DC-A6-32-65-12-9B']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.05.19 asked for this one to add DOM02 noticed
    new_list = ['DC-A6-32-C5-E2-63']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.05.23 asked for this one to add Raul at PR005 was trying to get it flipped
    # Clear pass freeze for two weeks
    # 2023.05.31 DONE
    new_list = ['DC-A6-32-5C-5B-3F']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.06.07 Cleanup any not yet added
    new_list = ['DC-A6-32-31-A6-31', 'DC-A6-32-38-8B-D6', 'DC-A6-32-53-85-E1', 'DC-A6-32-53-98-29', 'DC-A6-32-5C-41-4A',
                'DC-A6-32-5C-42-DC', 'DC-A6-32-5C-43-78', 'DC-A6-32-5C-5C-68', 'DC-A6-32-5C-5C-C8', 'DC-A6-32-79-0A-48',
                'DC-A6-32-79-0B-A4', 'DC-A6-32-79-0B-C5', 'DC-A6-32-79-79-96', 'DC-A6-32-A5-C2-4D', 'DC-A6-32-C5-E2-36',
                'DC-A6-32-C5-E2-48', 'E4-5F-01-09-1D-A1', 'E4-5F-01-98-1E-EB']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.06.08 requested another add for Raul
    new_list = ['D8-3A-DD-15-99-45']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.06.20, for Raul, PR005 D8-3A-DD-15-99-75
    new_list = ['D8-3A-DD-15-99-75']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.07.13
    new_list = ['E4-5F-01-14-FC-24', 'DC-A6-32-A3-AE-69']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.08.25
    new_list = ['DC-A6-32-39-6C-57']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.09.21
    new_list = ['E4:5F:01:90:E3:65', 'E4:5F:01:90:E3:B9']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.09.24
    _ = """
Carlos, Cristhian <<EMAIL>>

Site: MEX03
ID: 1000000082AC0443
MAC: DC-A6-32-39-6C-19

Site: MEX03
ID: 10000000513d2c7a
MAC: DC-A6-32-39-6A-FF

Site: MEX03
ID: 100000005fd6bf36
MAC: DC-A6-32-39-69-96

    """
    new_list = ['DC-A6-32-39-6C-19', 'DC-A6-32-39-6A-FF', 'DC-A6-32-39-69-96']
    for new_item in new_list:
        the_content[new_item] = ''

    # 2023.10.27 Stephen Sidlowski, at MD014 (Baltimore)
    new_list = []
    new_list.append('DC:A6:32:49:ED:F2')  # (10000000fee5ac78)
    new_list.append('DC:A6:32:49:EE:3A')  # (10000000e52a1839)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """
    2023.10.27 email to Stefan Seifert and Suresh Pal (cc Aaron Perkins)

Site: MD014
ID: 10000000fee5ac78
MAC: DC-A6-32-49-ED-F2

Site: MD014
ID: 10000000e52a1839
MAC: DC-A6-32-49-EE-3A

    """

    # 2023.11.01 Raul Ramos, PR005
    new_list = []
    new_list.append('DC-A6-32-5C-5D-07')  # (1000000023ccd80b)
    new_list.append('DC-A6-32-82-EA-6D')  # (1000000099568b6a)
    new_list.append('DC-A6-32-8F-C0-F0')  # (100000006ceee70d)
    new_list.append('D8-3A-DD-15-99-D2')  # (1000000007b3cb81)
    new_list.append('D8-3A-DD-15-6A-C4')  # (10000000e1b930c6)
    new_list.append('D8-3A-DD-15-99-6C')  # (100000000c7ae298)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """

MAC: DC-A6-32-5C-5D-07
Host: cah-rp-1000000023ccd80b
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: DC-A6-32-82-EA-6D
Host: cah-rp-1000000099568b6a
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: DC-A6-32-8F-C0-F0
Host: cah-rp-100000006ceee70d
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-D2
Host: cah-rp-1000000007b3cb81
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-6A-C4
Host: cah-rp-10000000e1b930c6
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-6C
Host: cah-rp-100000000c7ae298
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005



    """

    # 2023.11.07 Raul Ramos, PR005
    new_list = []
    new_list.append('D8-3A-DD-15-99-B4')  # (1000000015fdab61)
    new_list.append('D8-3A-DD-15-97-6E')  # (100000008a861e4b)
    new_list.append('D8-3A-DD-15-99-82')  # (100000000a5bfca3)
    new_list.append('D8-3A-DD-15-99-AB')  # (100000000cac7e01)
    new_list.append('D8-3A-DD-15-62-D3')  # (1000000090a69601)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """
MAC: D8-3A-DD-15-99-B4
Host: cah-rp-1000000015fdab61
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-97-6E
Host: cah-rp-100000008a861e4b
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-82
Host: cah-rp-100000000a5bfca3
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-AB
Host: cah-rp-100000000cac7e01
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-62-D3
Host: cah-rp-1000000090a69601
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

    """

    # 2023.11.07 Raul Ramos, PR005
    new_list = []
    new_list.append('D8-3A-DD-15-6A-0E')  # (10000000c0fdd0d7)
    new_list.append('D8-3A-DD-15-99-CC')  # (1000000026c06d97)
    new_list.append('D8-3A-DD-15-99-30')  # (100000006419d5ae)
    new_list.append('D8-3A-DD-15-67-32')  # (10000000e331b0f6)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """
MAC: D8-3A-DD-15-6A-0E
Host: cah-rp-10000000c0fdd0d7
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-CC
Host: cah-rp-1000000026c06d97
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-99-30
Host: cah-rp-100000006419d5ae
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005

MAC: D8-3A-DD-15-67-32
Host: cah-rp-10000000e331b0f6
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: PR005
"""

    # 2023.11.28 Flores, Azucena (Susy)
    new_list = []
    new_list.append('DC-A6-32-39-6A-30')  # (100000007a1b190c)
    new_list.append('DC-A6-32-39-6D-66')  # (1000000000f722ee)
    new_list.append('DC-A6-32-39-6A-33')  # (1000000034818611)
    new_list.append('DC-A6-32-39-2F-D6')  # (10000000fed493c5)
    new_list.append('DC-A6-32-39-6A-60')  # (100000001f57bdc8)
    new_list.append('DC-A6-32-39-4F-FF')  # (100000007a5aea52)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """
MAC: DC-A6-32-39-6A-30
Host: cah-rp-100000007a1b190c
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-6D-66
Host: cah-rp-1000000000f722ee
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-6A-33
Host: cah-rp-1000000034818611
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-2F-D6
Host: cah-rp-10000000fed493c5
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-6A-60
Host: cah-rp-100000001f57bdc8
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-4F-FF
Host: cah-rp-100000007a5aea52
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03


"""

    new_list = []
    new_list.append('DC-A6-32-78-F7-61')  # (100000001cf0cd2d)
    for new_item in new_list:
        the_content[new_item] = ''

    _ = """
2024.02.05

MAC: DC-A6-32-78-F7-61
Host: cah-rp-100000001cf0cd2d
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: TX134

wired IP = *************

"""

    _ = """
2024.02.28

Use "Requested Service Not Found" method below

MAC: DC-A6-32-39-55-AE
Host: cah-rp-100000000ecef973
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

REQ3774813





MAC: DC-A6-32-39-64-5A
Host: cah-rp-10000000cee3f658
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

REQ3778212

2024.03.04
Melvin De Jesus


These 4 might already be in?

MAC: DC-A6-32-53-97-4B
Host: cah-rp-10000000f6605bf8
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: DOM02

MAC: DC-A6-32-41-4C-11
Host: cah-rp-10000000fb5ee37b
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: DOM02

MAC: DC-A6-32-41-81-60
Host: cah-rp-1000000055a57d1b
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: DOM02

MAC: DC-A6-32-B7-7B-74
Host: cah-rp-10000000b05b7cad
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: DOM02



    """

    _ = """
2024.03.06

MAC: DC-A6-32-A3-7D-50
Host: cah-rp-1000000054c3472e
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX05
traffic ok on: *************

MAC: DC-A6-32-65-12-91
Host: cah-rp-100000005ca08eda
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX05
traffic ok on: *************


MAC: DC-A6-32-A3-AB-A6
Host: cah-rp-100000009d488f20
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX05

Not allowing traffic: *************

REQ3783264


    """

    _ = """
2024.03.14
[Yesterday 5:44 PM] Flores, Azucena (Susy)
Hi.  David  could you help me add the IOT to connect to the Wifi.
10000000e3112a78
10000000c1c6c299
10000000cda74128

MAC: DC-A6-32-39-69-AC
Host: cah-rp-10000000c1c6c299
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-6C-6E
Host: cah-rp-10000000cda74128
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-6D-6F
Host: cah-rp-10000000e3112a78
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

REQ3794089 Created

"""

    """
2024.04.04
Daves test Pi5

MAC: D8-3A-DD-A6-F3-63
Host: cah-rp-ec885c8aa5f46f0d
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: OH001

REQ3820915 Created

"""

    """
2024.06.18
REQ3912492

MAC: E4-5F-01-2E-8F-CD
Host: cah-rp-100000009f7eeab2
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04
"""

    new_list = []
    new_list.append('E4-5F-01-2E-8F-CD')  # (100000009f7eeab2)
    for new_item in new_list:
        the_content[new_item] = ''



    """
2024.08.13
REQ3986958

MAC: E4-5F-01-57-5B-A1
Host: cah-rp-10000000ebc1838c
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04

MAC: E4-5F-01-2E-98-DE
Host: cah-rp-10000000d8a3d6ba
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04

MAC: E4-5F-01-3D-6D-C1
Host: cah-rp-10000000e589dcae
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04

MAC: E4-5F-01-57-5A-CC
Host: cah-rp-100000006f0859f4
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04

MAC: E4-5F-01-3D-6D-7F
Host: cah-rp-100000006ced4c42
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX04


"""

    new_list = []
    new_list.append('E4-5F-01-57-5B-A1')
    new_list.append('E4-5F-01-2E-98-DE')
    new_list.append('E4-5F-01-3D-6D-C1')
    new_list.append('E4-5F-01-57-5A-CC')
    new_list.append('E4-5F-01-3D-6D-7F')
    for new_item in new_list:
        the_content[new_item] = ''


    """
    2024.11.07
    Azucena (Susy) Flores

REQ4096757
RITM6255169

ID 1000000048AB4B34
ID 10000000db019dda
ID 1000000051A33FA7
ID 100000004C03609E


MAC: DC-A6-32-39-6D-81
Host: cah-rp-1000000048ab4b34
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03


MAC: DC-A6-32-39-6B-EF
Host: cah-rp-10000000db019dda
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03

MAC: DC-A6-32-39-68-5C
Host: cah-rp-1000000051a33fa7
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03


MAC: DC-A6-32-39-6B-F2
Host: cah-rp-100000004c03609e
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: MEX03



    """

    new_list = []
    new_list.append('DC-A6-32-39-6D-81')
    new_list.append('DC-A6-32-39-6B-EF')
    new_list.append('DC-A6-32-39-68-5C')
    new_list.append('DC-A6-32-39-6B-F2')
    for new_item in new_list:
        the_content[new_item] = ''


    _ = """
    IL002
    Lang, Darby
    req: REQ4160397

MAC: DC-A6-32-66-72-D0
Host: cah-rp-10000000e870f866
Group: RASPBERRY_PI
POLICY: CORPORATE
SITE-ID: IL002

    """
    new_list = []
    new_list.append('DC-A6-32-66-72-D0')
    for new_item in new_list:
        the_content[new_item] = ''


    # !!!!!!!!!!!! put new request data above here... Instructions are below here
    # !!!!!!!!!!!! put new request data above here... Instructions are below here
    # !!!!!!!!!!!! put new request data above here... Instructions are below here

    _ = """
NO GOOD.... it is misleading to have to pick policy on this page:
https://cardinal.service-now.com/gith?id=sp_index
(search "iot devices")
Find "Authorize IOT devices in ClearPass"


OR

ServiceNow:
https://cardinal.service-now.com/gith?id=sp_index
(search "iot devices")
at bottom of search results, click "Engage a Team",
Which brings up "Requested Service Not Found"
Keyword searched: iot devices
Why: Was referred to use this form
Who: Network-IOT / Seifert, Stefan <<EMAIL>>
Do you know an IT team: Yes
What do you need: Complete Work
Select an IT group: Network-IOT
Title: Add raspberry pi to ClearPass
Description: (put the MAC:, Host:, ... SiteID: details)
Email for copy: aaron perkins (erin)

Submit Now
"""

    # 2023.XX.XX QCAM test device (points to a different IOT vlan, for NLC?)
    new_list = ['DC-A6-32-96-5C-A2']  # 10000000aaef9969

    # production QCAM devices
    _ = """
100000003dd1585f, d8:3a:dd:06:b1:85
100000003497a4cb, e4:5f:01:09:f7:66
    """

    the_content_cleaned = {}
    for item in the_content.keys():
        the_content_cleaned[item.replace(':', '-')] = ''

    return the_content_cleaned


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(intake status)'

    status = os.system('systemctl is-active --quiet intake-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "intake-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content

    # then return what GET would have done
    return make_body_GET(environ), other


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += '<center>'
        body += 'method = GET'
        body += '</center>'

        body += '<center>'
        body += 'environ'

        body += '<table border="1" cellpadding="5">'
        for item in environ:
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            body += '<td>'
            try:
                body += str(environ[item])
            except:
                body += '(na)'
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += 'result'
        body += '<br><br>'
        body += str(login.get_current_user(environ))
        body += '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'test1'
        body += '</td>'
        body += '<td>'
        body += 'test2'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, 'intake_') or permissions.permission_prefix_allowed(environ,
                                                                                                          'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n' \
                    '<body>\n'
        html += body

        if other['add_wrapper']:
            html += '</body>\n' \
                    '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_intake(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

# End of source file
