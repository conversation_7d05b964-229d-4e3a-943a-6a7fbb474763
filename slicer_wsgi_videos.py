# A videos for slicer page services

service = "videos"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/videos.py

using:
sudo vi /var/www/html/videos.py

It also requires:

sudo vi /etc/httpd/conf.d/python-videos.conf
----- start copy -----
WSGIScriptAlias /videos /var/www/html/videos.py
----- end copy -----

sudo chown apache:apache /var/www/html/videos.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import videos; print(videos.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/videos

https://slicer.cardinalhealth.net/videos?test


"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

import copy
import traceback
import json
import os
import sys
import time

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def make_body_POST(environ):
    # ====================================
    return ''


# ====================================
def make_body_GET(environ):
    # ====================================

    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
<head>
  <link href="https://vjs.zencdn.net/7.15.4/video-js.css" rel="stylesheet" />
</head>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        the_videos = []
        the_videos.append(
            {'title': 'Introduction<br>to<br>Raspberry Pi', 'video': 'Video_RaspberryPi_Introduction.mp4'})
        #        the_videos.append({'title':'Raspberry Pi<br>unboxing<br>and setup', 'video':''})
        the_videos.append(
            {'title': 'Raspberry Pi<br>configure<br>using Slicer', 'video': 'SlicerConfigurationOfPi_720p.mov'})
        #        the_videos.append({'title':'Raspberry Pi<br>monitoring<br>using Slicer', 'video':''})
        #        the_videos.append({'title':'Raspberry Pi<br>ring based updates<br>using Slicer', 'video':''})
        the_videos.append({'title': 'Raspberry Pi<br>connect to bluetooth scanner', 'video': '2.1.4_Scanner_Demo.MOV'})

        _get_files_into_place = """
sudo cp /mnt/disks/SSD/var/log/slicer/upload/files/Video_RaspberryPi_Introduction.mp4 /var/www/htmlfiles/Video_RaspberryPi_Introduction.mp4
sudo cp /mnt/disks/SSD/var/log/slicer/upload/files/SlicerConfigurationOfPi_720p.mov /var/www/htmlfiles/SlicerConfigurationOfPi_720p.mov
sudo cp /mnt/disks/SSD/var/log/slicer/upload/files/2.1.4_Scanner_Demo.MOV /var/www/htmlfiles/2.1.4_Scanner_Demo.MOV

sudo chown -R apache:apache /var/www/htmlfiles/

            """

        body += '<center>'
        body += 'Videos'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'

        for the_video in the_videos:
            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += the_video['title']
            body += '</center>'
            body += '</td>'
            body += '<td>'
            # https://videojs.com/getting-started

            video_file = the_video['video']

            if video_file:
                body += """
          <video
            id="my-video"
            class="video-js"
            controls
            preload="auto"
            width="640"
            height="264"
            data-setup="{}"
          >"""
                body += '<source src="' + video_file + '" type="video/mp4" />'
                body += """
            <p class="vjs-no-js">
              To view this video please enable JavaScript, and consider upgrading to a
              web browser that
              <a href="https://videojs.com/html5-video-support/" target="_blank"
                >supports HTML5 video</a
              >
            </p>
          </video>
                """
            else:
                body += """Coming soon"""

            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    # permission_to_use = permissions.permission_prefix_allowed(environ, 'videos_')
    permission_to_use = True

    if permission_to_use:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other

# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
