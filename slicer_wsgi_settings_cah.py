# cah specific items for use on slicer01

service = "settings"
version = service + '.cah.0.3'

import os


# ====================================
def get():
    # ====================================
    return_value = {}
    return_value['home_url'] = 'https://slicer.cardinalhealth.net'
    return_value['trust_list'] = ['david.ferguson', 'steven.murphy', 'mark.warren']
    return_value['trust_list'] = []
    return_value['data_drop_base'] = '/mnt/disks/SSD/var/log/slicer/'
    return_value['ram_disk_path'] = '/dev/shm/'
    return_value['datastore_save_path'] = '/var/log/slicer/'
    return_value['days_to_keep'] = 14
    return_value['sites_to_drop'] = {'MEX09': '(if key exists, then it will be dropped)'}
    return_value['days_to_keep_dashboard_data'] = 120

    return_value['login_authentication'] = {
        'authentication_type': 'ldap-cmdln',
        'admin_AD_group': 'A-APM0028269-Slicer-admins',
        'user_domain': '@cardinalhealth.com',
        'basedn': 'dc=cardinalhealth,dc=net',
        'user_domain_list': ['@cardinalhealth.com', '@cordlogistics.com'],
        'ldap_servers': ['WPIL0219ADIDC' + '02' + '.cardinalhealth.net',
                         'WPIL0219ADIDC' + '25' + '.cardinalhealth.net',
                         'WPIL0219ADIDC' + '26' + '.cardinalhealth.net'],
    }

    _ = """ other domains (from John Lee demo Sept14, 2023)
@cardinalhealth.com
@outcomesmtm.com
@cordlogistics.com
@sonexushealth.com
@harvarddruggroup.com
@parmedpharm.com
@kinray.com
@Firstveterinarysupply.com
@assuramed.com
@bayareahospital.org
@cardinalhealth.ca
@coxhealth.com
@edgepark.com
@bayareahospital.org
@advocatehealth.com
@wavemark.com
@major-pharm.com
@telepharm.com
@cardinalhealth.net
    """

    # --------------------
    # Apache settings:
    # --------------------
    # https://stackoverflow.com/questions/44335970/apache-invalid-command-sslengine-perhaps-misspelled-or-defined-by-a-module-n

    # 1GB upload limit
    # https://serverfault.com/questions/1109455/requests-with-lager-than-1gb-data-fail-after-upgrading-to-apache-2-4-54
    # https://github.com/nextcloud/docker/issues/1796
    #

    files_path = '/var/www/htmlfiles'
    return_value['apache_files_path'] = files_path

    content_limts = """LimitRequestBody 0
"""

    content_80 = """<VirtualHost *:80>
	ServerAdmin webmaster@localhost
	DocumentRoot """ + files_path + """
    <Location />
        SSLRenegBufferSize 0
    </Location>

</VirtualHost>
"""

    if os.path.isfile('/etc/pki/tls/private/slicer_cabundle.pem'):
        content_443 = """
<VirtualHost *:443>
ServerName slicer.cardinalhealth.net
DocumentRoot """ + files_path + """
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/slicer_cabundle.pem"
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""
    else:
        content_443 = """
<VirtualHost *:443>
ServerName slicer.cardinalhealth.net
DocumentRoot """ + files_path + """
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""

    return_value['apache_config_content'] = content_limts + content_80 + content_443

    return return_value


import time


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

# end of file
