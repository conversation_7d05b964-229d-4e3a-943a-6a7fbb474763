How to check an md5 value on a downloaded file

-----------
WINDOWS:
-----------
Download the latest version of WinMD5Free. (http://www.winmd5.com/)

Extract the downloaded zip and launch the WinMD5.exe file.

Click on the Browse button, navigate to the file that you want to check and select it.

Just as you select the file, the tool will show you its MD5 checksum.

Copy and paste the original MD5 value provided by the developer or the download page.

Click on Verify button.

-----------
MAC:
-----------
Download the file you want to check and open the download folder in Finder.

Open the Terminal, from the Applications / Utilities folder.

Type md5 followed by a space. Do not press Enter yet.

Drag the downloaded file from the Finder window into the Terminal window.

Press Enter and wait a few moments.

The MD5 hash of the file is displayed in the Terminal.

Open the checksum file provided on the Web page where you downloaded your file from.

The file usually has a .cksum extension.

NOTE: The file should contain the MD5 sum of the download file. For example: md5sum: 25d422cc23b44c3bbd7a66c76d52af46
 Compare the MD5 hash in the checksum file to the one displayed in the Terminal.

If they are exactly the same, your file was downloaded successfully. Otherwise, download your file again.

-----------
LINUX:
-----------
Open a terminal window.

Type the following command: md5sum [type file name with extension here] [path of the file] -- NOTE: You can also drag the file to the terminal window instead of typing the full path.

Hit the Enter key.

You will see the MD5 sum of the file.

Match it against the original value.


