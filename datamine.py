#!/usr/bin/env python3
"""
Mock datamine module for development environment
Provides minimal functionality to support reports module
"""

def build_data_dictionary(seconds_for_current):
    """Mock data dictionary for reports module"""
    return {
        'data_items': {},
        's_all_pi_services': [],
        'sites': {},
        'devices': {},
        'summary': 'Mock data dictionary for development'
    }

def get_site_data(site_id):
    """Mock site data"""
    return {
        'site_id': site_id,
        'status': 'online',
        'devices': [],
        'issues': 0
    }

def get_device_data(device_id):
    """Mock device data"""
    return {
        'device_id': device_id,
        'status': 'active',
        'last_seen': '2025-01-20 12:00:00'
    }
