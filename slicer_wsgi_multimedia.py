# A multimedia service for slicer

service = "multimedia"
version = service + '.0.4'

release_notes = """
2024.05.08
0.4
make the sort work nicely for names that end in mp4 (ignore the digit at the end)

2023.11.07
multimedia.0.3

Starting to build a place for uploading content to be available to the pis.

2023.02.01
multimedia.0.2

add live data table

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/multimedia.py

using:
sudo vi /var/www/html/multimedia.py

It also requires:

sudo vi /etc/httpd/conf.d/python-multimedia.conf
----- start copy -----
WSGIScriptAlias /multimedia /var/www/html/multimedia.py
----- end copy -----

sudo chown apache:apache /var/www/html/multimedia.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/multimedia-runner
sudo chmod +x /var/www/html/multimedia-runner

# ===== begin: start file
#!/usr/bin/env python
import multimedia
multimedia.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/multimedia-runner.service
sudo systemctl daemon-reload
sudo systemctl stop multimedia-runner.service
sudo systemctl start multimedia-runner.service
sudo systemctl enable multimedia-runner.service

systemctl status multimedia-runner.service

sudo systemctl restart multimedia-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep multimedia-runner

OR

tail -f /var/log/syslog | fgrep multimedia-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/multimedia-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import multimedia; print(multimedia.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/multimedia

https://slicer.cardinalhealth.net/multimedia?siteid=PR005

https://slicer.cardinalhealth.net/multimedia?serial=100000002a5da842

https://slicer.cardinalhealth.net/multimedia?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_multimedia


"""

_development_notes = """
new permission
new runner, that checks the schedule, and makes the correct files be in the right place.


ability to upload content to a "profile" schedule.
profiles of the name structured like "_multimedia_
Then it is just any device with that profile is the linkage.
In new module, call out how many have the given profile, and maybe show a table per profile,
    with ID and name, and allow a click to get you to the device settings.

profile_(siteID)_multimedia_(purpose)


Other considerations:
Keep log of who/when new content is uploaded to Slicer.
Maybe manage the write permission based on each profile (purpose), so that one person can
    be responsible for one aspect, and another person can manage a different purpose content.

We need a permission, that allows a local RCS person,
    to assign multimedia permissions to others, at the sites they are allowed to manage.

"""

_mp4 = """
https://www.pexels.com/search/videos/4k/

https://www.freecodecamp.org/news/how-to-convert-video-files-to-gif-in-python/)

# ??? pure python (or at least common libraries)
https://www.blog.pythonlibrary.org/2021/06/29/converting-mp4-to-animated-gifs-with-python/

# Online tools (do it yourself)
https://ezgif.com/video-to-gif
https://new.express.adobe.com/tools/convert-to-gif

"""

_ = """
on pi

Cristhian Carlos asking:

Can the pi play videos (for HR use) from a network linked file?

Maybe have it be an upload file, and launch a custom application that can loop play video in python... https://forums.raspberrypi.com/viewtopic.php?t=220065

omxplayer is deprecated... going to VLC

https://forums.raspberrypi.com/viewtopic.php?t=328518



sudo apt-get install vlc -y

vlc http://10.0.2.23:3001

vlc somevideo.mp4

vlc -vvv https://www.youtube.com/watch?v=ulbNpIAUrgM



export DISPLAY=:0

vlc -vvv https://www.youtube.com/watch?v=ulbNpIAUrgM



Audio:

https://support.thepihut.com/hc/en-us/articles/360010336738-No-sound-output-with-my-Raspberry-Pi-4

Loop:

https://community.adobe.com/t5/dreamweaver-discussions/video-set-to-loop-doesn-t-loop-when-uploaded-to-server/m-p/10530852



In browser:

https://stackoverflow.com/questions/10377453/play-infinitely-looping-video-on-load-in-html5


"""

_ = """

slicer04

!!! failed !!!
https://computingforgeeks.com/install-use-ffmpeg-on-rocky-alma-9/
sudo dnf install epel-release
sudo dnf config-manager --set-enabled crb
sudo dnf install --nogpgcheck https://mirrors.rpmfusion.org/free/el/rpmfusion-free-release-$(rpm -E %rhel).noarch.rpm -y
sudo dnf install --nogpgcheck https://mirrors.rpmfusion.org/nonfree/el/rpmfusion-nonfree-release-$(rpm -E %rhel).noarch.rpm -y
sudo dnf install ffmpeg ffmpeg-devel
!!!

!!! failed !!!
https://www.golinuxcloud.com/install-ffmpeg-rocky-linux-9/
sudo dnf -y install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm
sudo dnf -y install https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
sudo dnf config-manager --enable epel,epel-next,crb
sudo dnf install -y https://download1.rpmfusion.org/free/el/rpmfusion-free-release-9.noarch.rpm
sudo dnf install -y https://download1.rpmfusion.org/nonfree/el/rpmfusion-nonfree-release-9.noarch.rpm
sudo dnf -y install ffmpeg
rpm -qa ffmpeg
!!!

!!! failed !!!
https://computingforgeeks.com/install-use-ffmpeg-on-rocky-alma-9z
From Source:
sudo dnf install epel-release -y
sudo dnf install wget nano tar lbzip2 gcc yasm -y
wget https://ffmpeg.org/releases/ffmpeg-snapshot.tar.bz2
tar -xf ffmpeg-snapshot.tar.bz2
!!!

======================================
mp4 Read with python:
https://www.kaggle.com/code/humananalog/examine-mp4-files-with-python-only
http://xhelmboyx.tripod.com/formats/mp4-layout.txt
https://github.com/OpenAnsible/rust-mp4/raw/master/docs/ISO_IEC_14496-14_2003-11-15.pdf
https://developer.apple.com/library/archive/documentation/QuickTime/QTFF/QTFFPreface/qtffPreface.html

sudo python3
import os, sys
import struct
import numpy as np

def find_boxes(f, start_offset=0, end_offset=float("inf")):
    #Returns a dictionary of all the data boxes and their absolute starting
    #and ending offsets inside the mp4 file.
    #Specify a start_offset and end_offset to read sub-boxes.
    s = struct.Struct("> I 4s")
    boxes = {}
    offset = start_offset
    f.seek(offset, 0)
    while offset < end_offset:
        data = f.read(8)               # read box header
        if data == b"": break          # EOF
        length, text = s.unpack(data)
        f.seek(length - 8, 1)          # skip to next box
        boxes[text] = (offset, offset + length)
        offset += length
    return boxes

def scan_mvhd(f, offset):
    f.seek(offset, 0)
    f.seek(8, 1)            # skip box header
    data = f.read(1)        # read version number
    version = int.from_bytes(data, "big")
    word_size = 8 if version == 1 else 4
    f.seek(3, 1)            # skip flags
    f.seek(word_size*2, 1)  # skip dates
    timescale = int.from_bytes(f.read(4), "big")
    if timescale == 0: timescale = 600
    duration = int.from_bytes(f.read(word_size), "big")
    print("Duration (sec):", duration / timescale)

def examine_mp4(filename):
    print("Examining:", filename)
    with open(filename, "rb") as f:
        boxes = find_boxes(f)
        print(boxes)
        # Sanity check that this really is a movie file.
        correct = boxes[b"ftyp"][0] == 0
        assert(correct)
        moov_boxes = find_boxes(f, boxes[b"moov"][0] + 8, boxes[b"moov"][1])
        print(moov_boxes)
        trak_boxes = find_boxes(f, moov_boxes[b"trak"][0] + 8, moov_boxes[b"trak"][1])
        print(trak_boxes)
        udta_boxes = find_boxes(f, moov_boxes[b"udta"][0] + 8, moov_boxes[b"udta"][1])
        print(udta_boxes)
        scan_mvhd(f, moov_boxes[b"mvhd"][0])

the_file = '/mnt/disks/SSD/var/log/slicer/multimedia/files/profile_VPN1_multimedia_test_(active)_20240307222725_(orig_file)_Juarez_Presource.mp4'
examine_mp4(the_file)




======================================

Mac PC convert to h.265
https://www.macxdvd.com/mac-video-converter-pro/transcode-convert-h264-to-h265-x265.htm
Download, drag to Applications alias, run as "MacX Video Converter Pro"
Open file, select output category "Mac Ceneral Video", select MP4 HEVC (H.265)
Click RUN


convert to h.265
https://stackoverflow.com/questions/58742765/convert-videos-from-264-to-265-hevc-with-ffmpeg


"""

import cgi
import copy
import datetime
import hashlib
import json
import os
import shlex
import shutil
import subprocess
import sys
from tempfile import TemporaryFile
import time
import traceback
import unittest

startup_exceptions = ''
new_content_update_flag_file_name = 'new_content.txt'

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import deviceupload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0

# ====================================
def hex_to_rgb(value):
    # ====================================

    value = value.lstrip('#')
    lv = len(value)
    return tuple(int(value[i:i + lv // 3], 16) for i in range(0, lv, lv // 3))

# ====================================
def rgba_to_hex(color):
    # ====================================
    try:
        r = int(color.replace('(','').split(',')[0])
        g = int(color.replace('(','').split(',')[1])
        b = int(color.replace('(','').split(',')[2])
    except:
        r = g = b = 255

    return "#{:02X}{:02X}{:02X}".format(r, g, b)

# ====================================
def rgba_to_alpha(color):
    # ====================================
    try:
        return_value = str(color.replace('(','').replace(')','').split(',')[3])
    except:
        return_value = '1.0'

    return return_value

# ====================================
def extract_digits_at_end(the_string):
    # ====================================
    all_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']

    return_value = ''

    for index in range(len(the_string) - 1, -1, -1):
        if the_string[index] in all_digits:
            return_value = the_string[index] + return_value
        else:
            break

    if len(the_string) > 2:
        if the_string[-3:] == 'mp4':  # ends in a digit, but "mp4" is not meant as a sequence number
            return_value = ''

    return return_value


# ====================================
def extract_profile_names(data_store_content):
    # ====================================
    return_value = []

    for key in data_store_content.keys():
        if ('profile_' in key) and (not '_permission_' in key):
            return_value.append(key)

    return sorted(return_value)


# ====================================
def extract_profile_families_from_names(profile_names):
    # ====================================
    return_value = []

    family_names = {}

    for key in profile_names:
        end_digits = extract_digits_at_end(key)
        if end_digits:
            family_name = key[0:-len(end_digits)]

            if family_name[-1] == '_':
                family_name = family_name[:-1]
        else:
            family_name = key
        if not family_name in family_names:
            family_names[family_name] = 1
            return_value.append(family_name)

    return sorted(return_value)


# ====================================
def sort_by_last_digits(names):
    # ====================================
    # must only concern with the ending digits;
    #   someone else manages that they are all the same family
    return_value = []
    sort_d = {}

    for name in names:
        digits = extract_digits_at_end(name)
        if digits:
            the_value = int(digits)
        else:
            the_value = -1
        sort_d[the_value] = name

    for key in sorted(sort_d.keys()):
        return_value.append(sort_d[key])

    return return_value


# ====================================
def convert_text_to_from_url(text, reverse=False):
    # ====================================
    if reverse:
        return text.replace('%2C', ',')
    else:
        return text.replace(',', '%2C')


# ====================================
def extract_originalfilename_from_media_name(media_name, keep_extension=False):
    # ====================================
    extension = ''
    if len(media_name.split('.')) > 1:
        extension = '.' + media_name.split('.')[-1]

    if extension:
        return_value = media_name[:-len(extension)]
    else:
        return_value = media_name

    if '(orig_file)_' in media_name:
        return_value = media_name.split('(orig_file)_')[1].split('_(')[0]
        if extension:
            if return_value[-len(extension):] == extension:
                return_value = return_value[:-len(extension)]

    if keep_extension:
        return_value += extension

    return return_value


# ====================================
def extract_destination_from_media_name(media_name):
    # ====================================
    return_value = 'showgif.gif'  # default of existing (original) data file intent

    if '(dest)_' in media_name:
        return_value = media_name.split('(dest)')[1].split('_')[1]

    return return_value


# ====================================
def make_file_extension_lower(filename):
    # ====================================
    splits = filename.split('.')

    if len(splits) > 1:
        return_value = splits[0] + '.' + splits[1].lower()

        return_value = '.'.join(splits[0:-1]) + '.' + splits[-1].lower()
    else:
        return_value = filename

    return return_value

# ====================================
def get_file_size(file_name):
    # ====================================
    size = 0
    if os.path.isfile(file_name):
        size = os.path.getsize(file_name)
    return size

# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        fileSizeStr = make_size_human_readable(size)
    return fileSizeStr


# ====================================
def make_size_human_readable(size):
    # ====================================
    fileSizeStr = '0'
    if size > 0:
        fileSizeStr = str(int(size / 1.0)) + " B"
    if size > 1024:
        fileSizeStr = str(int(size / 1024)) + " KB"
    if size > 1024 ** 2:
        fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
    if size > 1024 ** 3:
        fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
    if size > 1024 ** 4:
        fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def select_devices_with_profile(data_store_content, the_profile):
    # ====================================
    return_value = []

    for key in data_store_content.keys():
        if 'device_profile_' in key:
            if the_profile == data_store_content[key]:
                return_value.append(key.split('_')[2])

    return return_value


# ====================================
def get_current_time_stamp():
    # ====================================
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    return TS[0:14]  # 20210406201829 (just the %Y%m%d%H%M%S part)


# ----------------------------
def select_media_for_active_from_time_and_available(current_time, available):
    # ----------------------------
    return_value = ''
    for key in sorted(available.keys()):
        if key <= current_time:
            return_value = available[key]
    return return_value


# ----------------------------
def extract_profile_content_options_from_filelist(filelist, profile_name):
    # ----------------------------
    return_value = {}
    for file_name in filelist:
        if profile_name in file_name:
            active_time = file_name.split('_(active)_')[1].split('_')[0]
            if not '.md5' in file_name:
                return_value[active_time] = file_name
    return return_value


# ----------------------------
def get_all_available(service_config_to_use):
    # ----------------------------
    return_value = os.listdir(service_config['base_upload_path'])
    return return_value


# ----------------------------
def build_save_filename(service_config_to_use, original_file_name, profile_name, active_time):
    # ----------------------------
    return_value = (service_config_to_use['base_upload_path']
                    + '/' + profile_name + '_(active)_' + active_time + '_(orig_file)_'
                    + original_file_name.replace(' ', '_').replace('#', ''))
    return_value = return_value.replace('//', '/')
    return return_value


# ----------------------------
def extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name):
    # ----------------------------
    return_value = []

    site_ok = False
    site_id = profile_name.split('_')[1]

    key_to_find = 'user_site_(user_name)_(site_id)'.replace('(user_name)', user_name).replace('(site_id)', site_id)
    if key_to_find in data_store_content:
        if data_store_content[key_to_find] == 'Yes':
            site_ok = True

    site_id = '(all)'
    key_to_find = 'user_site_(user_name)_(site_id)'.replace('(user_name)', user_name).replace('(site_id)', site_id)
    if key_to_find in data_store_content:
        if data_store_content[key_to_find] == 'Yes':
            site_ok = True

    if site_ok:
        user_permission_sub_key_to_find = 'user_permission_(user_name)_multimedia_'.replace('(user_name)', user_name)
        for key in data_store_content.keys():
            if user_permission_sub_key_to_find in key:
                if data_store_content[key] == 'Yes':
                    return_value.append(key.replace(user_permission_sub_key_to_find, ''))

    return sorted(return_value)

# ----------------------------
def extract_multimedia_needs_from_profile(profile_name, data_store_content):
    # ----------------------------
    return_value = {}

    try:
        the_profile_d = json.loads(data_store_content[profile_name])
        for bookmark_number in the_profile_d['bookmarks']['(id)'].keys():
            the_url = the_profile_d['bookmarks']['(id)'][bookmark_number]['url']

            if 'showgif.html' in the_url:
                return_value['showgif'] = 'gif_mp4'

            if '?' in the_url:
                splits = the_url.split('?')[1].split(',')
                for item in splits:
                    if '=' in item:
                        value = item.split('=')[1]
                        if '(datastore_' in value:
                            if not 'datastore' in return_value:
                                return_value['datastore'] = {}
                            return_value['datastore'][item.split('=')[0]] = item.split('=')[1].replace('(','').replace(')','').replace('datastore_','')

    except:
        return_value['exception'] = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return return_value

# ----------------------------
def extract_multimedia_profiles_from_datastore_sorted(data_store_content):
    # ----------------------------
    sorted_return_value = []

    profile_names = extract_profile_names(data_store_content)
    families = extract_profile_families_from_names(profile_names)

    for family in families:
        names_in_family = []
        if ('_multimedia_' in family):
            for name in profile_names:
                if family == name:
                    names_in_family.append(name)
                else:
                    if extract_digits_at_end(name):
                        if family in name:
                            names_in_family.append(name)

        for sort_result_name in sort_by_last_digits(names_in_family):
            sorted_return_value.append(sort_result_name)

    return sorted_return_value


# ----------------------------
def do_atomic_write_if_different(output_file, content, binary=False):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    if binary:
        try:
            with open(output_file, 'rb') as f:
                existing_content = f.read()
        except:
            existing_content = ''
    else:
        try:
            with open(output_file, 'r') as f:
                existing_content = f.read()
        except:
            existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content, binary)


# ----------------------------
def old_do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content, binary=False):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    if binary:
        with open(temp_name, 'wb') as f:
            f.write(content)
    else:
        with open(temp_name, 'w') as f:
            f.write(content)

    shutil.move(temp_name, output_file)  # this is where the atomic activity occurs


# ----------------------------
def old_do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0

    do_file_checks_time = 0

    while True:
        pass_count += 1

        new_content_filename = service_config['base_upload_path'] + '/' + new_content_update_flag_file_name
        if os.path.exists(new_content_filename):
            do_file_checks_time = 0  # trigger a check
            try:
                os.remove(new_content_filename)
            except:
                pass

        if abs(time.time() - do_file_checks_time) > 60:
            # do the checks
            current_time = get_current_time_stamp()
            data_store_content = datastore.all_datastore()
            filelist = get_all_available(service_config)
            the_profiles = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
            for the_profile in the_profiles:
                available = extract_profile_content_options_from_filelist(filelist, the_profile)
                current_media = select_media_for_active_from_time_and_available(current_time, available)
                current_media_md5 = ''
                try:
                    current_media_md5 = open(service_config['base_upload_path'] + '/' + current_media + '.md5',
                                             'r').read()
                except:
                    pass

                devices_with_this_profile = select_devices_with_profile(data_store_content, the_profile)
                for device_id in devices_with_this_profile:
                    device_content_md5 = deviceupload.get_device_content_md5(device_id)

                    if current_media_md5:
                        if not current_media_md5 == device_content_md5:
                            current_media_content = open(service_config['base_upload_path'] + '/' + current_media,
                                                         'rb').read()

                            # source_original_name = current_media.split('_(orig_file)_')[1].split('.')[0]
                            source_original_name = extract_originalfilename_from_media_name(current_media).split('.')[0]

                            try:
                                deviceupload.save_device_content(device_id, source_original_name, current_media_content)
                            except:
                                open('/dev/shm/running_exceptions_' + service, 'w').write(
                                    'pass_count : ' + str(pass_count) + ' -> ' + str(
                                        traceback.format_exc().replace("\n", "<br>").replace("\"",
                                                                                             "'")))  # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))

            # hold off checking for a while (from the time that we finished the work
            do_file_checks_time = time.time()

        time.sleep(2)


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ, start_response):
    # ====================================
    body = 'debug content: '
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
    action_report = ''

    #    the_who = login.get_current_user(environ)

    # do work on content

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)
#    if True:
    if False:
        body += '<br>environ: ' + str(environ)
        body += '<br>body_of_form: ' + str(body_of_form)
        body += '<br>field_storage:' + str(field_storage)
        if len(field_storage.list):
            for item in field_storage.list:
                body += '<br> field_storage.list item = ' + str(item)
                body += '<br> field_storage.list dir(item) = ' + str(dir(item))
                body += '<br> field_storage.list item.name = ' + str(item.name)
                body += '<br> field_storage.list item.value = ' + str(item.value)

        return body, other

    try:
        file_name = field_storage.getvalue('file_delete_allowed')
        full_path_file_name = service_config['base_upload_path'] + '/' + file_name
        allow_to_allow = field_storage.getvalue('file_delete_allowed_key')
        if 'delete_it_now' == allow_to_allow:
            try:
                os.remove(full_path_file_name)

                if action_report:
                    action_report += '\n'
                action_report += 'deleted: ' + '"' + full_path_file_name + '"'

            except:
                pass
            try:
                os.remove(full_path_file_name + '.md5')
            except:
                pass

            # Trigger a rescan soon, to get current as quickly as possible
            do_atomic_write(service_config['base_upload_path'] + '/' + new_content_update_flag_file_name,
                            'old content was deleted')

    except:
        #        open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"","'"))
        pass

    if len(field_storage.list):
        named_items = {}
        for item in field_storage.list:
            if item.name:
                named_items[item.name] = item.value

        for item in field_storage.list:
            #            body += '<br>item:' + str(item)
            if item.name:
                item_name = item.name
                value_to_use = item.value

                if ('_color' in item_name) and ('datastore_' in item_name):
                    # pull two pieces together here, and build one result
                    # (it will get built twice, since each piece will trigger this code)
                    item_name = 'datastore_' + item_name.split('datastore_')[1]
                    # rgb_ = '#00c800'
                    # a_ = '0.5'
                    rgb = hex_to_rgb(named_items['rgb_' + item_name])
                    value_to_use = '(' + str(rgb[0]) + '~' + str(rgb[1]) + '~' + str(rgb[2]) + '~' + named_items['a_' + item_name] + ')'

                if 'datastore_' in item_name:
                    data_store_value_name = item_name.replace('datastore_','')
                    previous_value = datastore.get_value(data_store_value_name)
                    datastore.set_value(data_store_value_name, value_to_use)

                    if previous_value != value_to_use:
                        if action_report:
                            action_report += '\n'
                        action_report += 'setting ' + data_store_value_name + ' from ' + '"' + previous_value + '"' + ' to ' + '"' + value_to_use + '"'


            if item.filename:
                file_content = item.file.read()  # binary, so don't .decode('utf-8')
                profile_name = item.name
                current_time = get_current_time_stamp()
                uploaded_full_file_name = item.filename

                file_name_to_use = extract_originalfilename_from_media_name(uploaded_full_file_name,
                                                                            keep_extension=True)
                output_file = make_file_extension_lower(
                    build_save_filename(service_config, file_name_to_use, profile_name, current_time))

                if False:
                    body += '<br>' + 'filename, ' + str(uploaded_full_file_name)
                    body += '<br>' + 'file_name_to_use, ' + str(file_name_to_use)
                    body += '<br>' + 'output_file, ' + str(output_file)
                    return body, other

                do_atomic_write_if_different(output_file, file_content, binary=True)

                if action_report:
                    action_report += '\n'
                action_report += 'setting multimedia content for profile:' + profile_name + '\n'
                action_report +=  '    with file: ' + '"' + uploaded_full_file_name + '"' + '\n'
                action_report +=  '    to file: ' + '"' + output_file + '"' + '\n'

                # build the md5sum
                command = 'md5sum ' + output_file
                md5_file = output_file + '.md5'
                mem_string, fails = do_one_command(command)

                # md5 file is the trigger to the device, that new content is here for them to grab
                do_atomic_write_if_different(md5_file, mem_string.split()[0])

                do_atomic_write(service_config['base_upload_path'] + '/' + new_content_update_flag_file_name,
                                'new content was written')

    # then return what GET would have done
    body, other = make_body_GET(environ, start_response)
    other['action_report'] = action_report
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_data(environ):
    # ====================================

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
    the_type = ''
    if 'type' in query_items:
        the_type = query_items['type']

    if the_type == 'issues':
        live_data = get_live_data()

        the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

        the_data['headers'] = live_data['headers']

        for item in live_data['data']:
            row_content = []
            row_links = []
            row_colors = []
            for header_name in the_data['headers']:
                try:
                    item_content = item[header_name]
                except:
                    item_content = ''

                try:
                    item_link = item[header_name + '_link']
                except:
                    item_link = ''

                try:
                    item_color = item[header_name + '_color']
                except:
                    item_color = ''

                row_content.append(item_content)
                row_links.append(item_link)
                row_colors.append(item_color)

            the_data['rows'].append(row_content)
            the_data['links'].append(row_links)
            the_data['color'].append(row_colors)
    else:
        # echo it back out, so that we can see it
        for key in query_items.keys():
            the_data['headers'].append(key)
            the_data['rows'].append([query_items[key]])

    return the_data

# ====================================
def make_multimedia_needs_for_profile(multimedia_needs, data_store_content, show_all_details=False):
    # ====================================
    body = ''
    body += '<form method="post" action="">'
    body += '<table border="1" cellpadding="5">'
    background_color = '(255,255,255,1.0)'
    font_color = '(0,0,0,1.0)'
    font_bold = 'no'

    reverse_lookup = {}
    for datastore_key in sorted(multimedia_needs['datastore'].keys()):
        datastore_lookup = multimedia_needs['datastore'][datastore_key]
        useit = False
        if show_all_details:
            useit = True
        else:
            if '_parameter_' in datastore_lookup:
                useit = True
        if useit:
            reverse_lookup[datastore_lookup] = datastore_key

    for datastore_lookup in sorted(reverse_lookup.keys()):
        datastore_key = reverse_lookup[datastore_lookup]
#        datastore_lookup = multimedia_needs['datastore'][datastore_key]
        datastore_value = datastore.get_value_stored(data_store_content, datastore_lookup, default_if_not_exist='')

        body += '<tr>'
        body += '<td>'
        body += datastore_key
        body += '</td>'

        color = ''
        if '_color' in datastore_key:
            try:
                color = datastore_value.replace('~',',')
                if 'background_color' == datastore_key:
                    background_color = color
                if 'font_color' == datastore_key:
                    font_color = color
            except:
                pass

            body += '<td>'
            body += '<input type="color" id="rgb_datastore_' + datastore_lookup + '" name="rgb_datastore_' + datastore_lookup + '" value="' + rgba_to_hex(color) + '">'
            body += '<input type="range" id="a_datastore_' + datastore_lookup + '" name="a_datastore_' + datastore_lookup + '" value="' + rgba_to_alpha(color) + '" min="0" max="1" step="0.1">'
            body += '</td>'


        else:
            body += '<td>'
            body += '<input type="text" size=35 id="datastore_' + datastore_lookup + '" name="datastore_' + datastore_lookup + '" value=\"' + datastore_value + """\">"""
            body += '</td>'

        if 'font_bold' == datastore_key:
            font_bold = datastore_value

        if show_all_details:
            body += '<td>'
            body += '(datastore_' + datastore_lookup + ')'
            body += '</td>'

        body += '</tr>'

    body += '<tr>'
    body += '<td>'
    body += '</td>'
    body += '<td><center>'
    body += '<input type="submit" value="Submit">'
    body += '</center></td>'

    if show_all_details:
        body += '<td style="background-color:rgba' + background_color + ';color:rgba' + font_color + '">'
        if font_bold == 'yes':
            body += '<B>'
            body += 'Bold on Background'
            body += '</B>'
        else:
            body += 'Font on Background'
        body += '</td>'

    body += '</tr>'
    body += '</table>'
    body += '</form>'

    return body

# ====================================
def make_body_get_profile(environ, the_profile):
    # ====================================
    data_store_content = datastore.all_datastore()
    user_name = str(login.get_current_user(environ))
    current_time = get_current_time_stamp()
    filelist = get_all_available(service_config)

    user_multimedia_permissions = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name,
                                                                                      the_profile)

    available = extract_profile_content_options_from_filelist(filelist, the_profile)
    current_media = select_media_for_active_from_time_and_available(current_time, available)
    devices_with_this_profile = select_devices_with_profile(data_store_content, the_profile)
    multimedia_needs = extract_multimedia_needs_from_profile(the_profile, data_store_content)

    if 'REQUEST_URI' in environ:
        REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
    else:
        REQUEST_URI = ''

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)

    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    body += '<center>'
    body += version
    body += '</center>'

    body += '<center>'
    body += '<br><br>'

    body += '<B>' + 'Profile details for: ' + the_profile + '</B>' + '<br><br>'

    if 'create' in user_multimedia_permissions:
        if 'datastore' in multimedia_needs:
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'

            body += make_multimedia_needs_for_profile(multimedia_needs, data_store_content, show_all_details=True)

            body += '</td>'
            body += '</tr>'


            body += '<tr>'
            body += '<td>'
#            body += '<br>' + 'Colors are (red~green~blue~alpha), where red, green, blue are 0 to 255, and alpha is 0.0 to 1.0; the tilde character is used instead of a comma.'
            body += '<br>' + 'safe_day_start is yyyy.mm.dd, where this is the year, month, day of the zero.'
            body += '<br>' + 'font_bold is yes or no'
            body += '<br><br>'
            body += '</td>'
            body += '</tr>'


            body += '</table>'

        if 'showgif' in multimedia_needs:
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            file_name = 'uploaded_file'
            file_name = the_profile
            body += """
            <form id="multimedia" name="multimedia" method=post enctype=multipart/form-data>
            <input type=file name=""" + file_name + """>"""
            body += """
            <input type=submit value=upload>
            </form>
            """
            body += '</td>'
            body += '</tr>'
            body += '</table>'
            body += '<br><br>'

    if 'showgif' in multimedia_needs:

        body += '<table border="1" cellpadding="5">'

        body += '<tr>'
        if 'delete' in user_multimedia_permissions:
            body += '<td>'
            body += '<B>Delete</B>'
            body += '</td>'
        body += '<td>'
        body += '<B>Timestamp</B><br>(most recent at top)'
        body += '</td>'
        body += '<td>'
        body += '<B>Media</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>Size</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>Download</B>'
        body += '</td>'
        body += '<td>'
        body += 'View in new tab'
        body += '</td>'
        body += '</tr>'

        for item in sorted(available.keys(), reverse=True):
            body += '<tr>'
            if 'delete' in user_multimedia_permissions:
                body += '<td>'
                next_Value = 'delete_it_now'
                body += '<form method="post" action="">'
                body += '<select name="file_delete_allowed" id="file_delete_allowed" hidden>'
                body += '<option value="' + available[item] + '" selected>' + available[item] + '</option>'
                body += '</select>'

                body += '<select name="file_delete_allowed_key" id="file_delete_allowed_key" hidden>'
                body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                body += '</select>'
                body += '<center>'
                body += '<input type="submit" value="' + next_Value + '">'
                body += '</center>'
                body += '</form>'
                body += '</td>'
            body += '<td>'
            if current_media == available[item]:
                body += '<B>' + item + '</B>'
                body += '<br>' + '<B>(currently active)</B>'
            else:
                body += item
            body += '</td>'
            body += '<td>'
            body += available[item].split('_(orig_file)_')[1]
            body += '</td>'

            body += '<td>'
            body += make_file_size_human_readable(service_config['base_upload_path'] + '/' + available[item])
            body += '</td>'

            body += '<td>'
            full_url = url_to_use + REQUEST_URI + '?filetodownload=' + convert_text_to_from_url(available[item])
            body += '<a href="' + full_url + '" download="' + available[item] + '"> ' + 'click here to download' + '</a>'
            body += '</td>'

            full_file_path = available[item]
            body += '<td>'
            if (full_file_path[-4:] == '.txt') or (full_file_path[-4:] == '.png') or (full_file_path[-4:] == '.gif') or (full_file_path[-4:] == '.pdf'):
                # the target="_blank" makes the click open a new tab for the result
                full_url = url_to_use + REQUEST_URI + '?filetoview=' + convert_text_to_from_url(available[item])
                body += '<a href="' + full_url + '" target="_blank"> ' + 'view' + '</a>'
            body += '</td>'

            body += '</tr>'

        body += '</table>'

    #            body += str(service_config)
    body += '<br><br> current time stamp = ' + current_time
    #            body += '<br> media path = ' + service_config['base_upload_path']
    # media path = /mnt/disks/SSD/var/log/slicer/multimedia/files/
    body += '</center>'

    return body, other


# ====================================
def make_body_get_summary(environ):
    # ====================================
    data_store_content = datastore.all_datastore()
    user_name = str(login.get_current_user(environ))
    current_time = get_current_time_stamp()
    filelist = get_all_available(service_config)

    if 'REQUEST_URI' in environ:
        REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
    else:
        REQUEST_URI = ''

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    body += '<center>'
    body += version
    body += '</center>'

    body += '<center>'
    body += '<br><br>'
    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td>'
    body += '<B>Profile</B> (click on a name below, to show details)'
    body += '</td>'
    body += '<td>'
    body += '<B>Devices</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>provide content</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>current file</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>size</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>old content</B>'
    body += '</td>'
    body += '</tr>'

    the_profiles = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
    for the_profile in the_profiles:

        user_multimedia_permissions = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name,
                                                                                          the_profile)

        available = extract_profile_content_options_from_filelist(filelist, the_profile)
        current_media = select_media_for_active_from_time_and_available(current_time, available)
        devices_with_this_profile = select_devices_with_profile(data_store_content, the_profile)
        multimedia_needs = extract_multimedia_needs_from_profile(the_profile, data_store_content)

        if user_multimedia_permissions:
            body += '<tr>'

            # Profile
            body += '<td>'

            url_to_use_here = url_to_use + REQUEST_URI + '?' + 'profile=' + the_profile
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + the_profile + """</a>"""

            body += '</td>'

            # Devices
            body += '<td>'
            for device_id in sorted(devices_with_this_profile):
                key_for_name = 'device_name_' + device_id
                device_name = ''
                if key_for_name in data_store_content:
                    device_name = data_store_content[key_for_name]

                if device_name:
                    body += str(device_id) + ' (' + device_name + ')' + '<br>'
                else:
                    body += str(device_id) + '<br>'
            body += '</td>'

            # provide content
            body += '<td>'
            if 'create' in user_multimedia_permissions:
                if 'datastore' in multimedia_needs:
                    body += make_multimedia_needs_for_profile(multimedia_needs, data_store_content)

                if 'showgif' in multimedia_needs:
                    file_name = 'uploaded_file'
                    file_name = the_profile
                    body += """
                    <form id="multimedia" name="multimedia" method=post enctype=multipart/form-data>
                    <input type=file name=""" + file_name + """>"""
                    if 'mp4' in the_profile:
                        body += """
                        <input type=submit value="upload .gif or .mp4" >
                        </form>
                        """
                    else:
                        body += """
                        <input type=submit value="upload .gif or .mp4" >
                        </form>
                        """
            body += '</td>'

            # current file
            body += '<td>'
            if 'showgif' in multimedia_needs:
                body += current_media.replace(the_profile + '_', '')
            body += '</td>'

            # size
            body += '<td>'
            size_of_current = get_file_size(service_config['base_upload_path'] + '/' + current_media)
            if 'showgif' in multimedia_needs:
                body += make_size_human_readable(size_of_current)
            body += '</td>'

            # size of old files
            body += '<td>'
            size_of_all = 0
            count_of_all = 0
            for item_in_available in sorted(available.keys(), reverse=True):
                count_of_all += 1
                item_file_name = service_config['base_upload_path'] + '/' + available[item_in_available]
                size_of_all += get_file_size(item_file_name)
            if 'showgif' in multimedia_needs:
                if count_of_all > 1:
                    body += make_size_human_readable(size_of_all - size_of_current) + ' (' + str(count_of_all-1) + ')'
            body += '</td>'

            if False:
                body += '<td>'
                body += json.dumps(multimedia_needs)
                body += '</td>'

            body += '</tr>'

    body += '</table>'

    #            body += str(service_config)
    body += '<br><br> current time stamp = ' + current_time
    #            body += '<br> media path = ' + service_config['base_upload_path']
    # media path = /mnt/disks/SSD/var/log/slicer/multimedia/files/
    body += '</center>'

    body += '<br><br><br>'
    body += '<center>'
    body += '<B>Tips for creating multimedia files</B><br><br>'

    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td>'
    body += 'Use Power Point to make a GIF'
    body += '</td>'
    body += '<td>'
    body += 'Make slides in the normal way in Power point.<br>Go to menu File -> Export... -> File Format: Animated GIF (Set options as desired) -> Export.'
    body += '</td>'
    body += '</tr>'

    body += '<tr>'
    body += '<td>'
    body += 'Use Power Point to make an mp4'
    body += '</td>'
    body += '<td>'
    body += 'Make slides in the normal way in Power point, with timing and audio.<br>Go to menu File -> Export... -> File Format: MP4 (H.264, and set other options as desired) -> Export.'
    body += '</td>'
    body += '</tr>'

    if True:
        body += '<tr>'
        body += '<td>'
        body += 'MP4 playback notes'
        body += '</td>'
        body += '<td>'
        body += 'In order for the device to play mp4 content, the pi_runner must be at version R.9.1 or later.<br>'
        body += 'The encoding H.264 is supported.<br>'
        body += 'Size 1280x720 plays well.<br>'
        body += 'Sizes larger than 1280x720 play, but will potentially have stutter issues.<br>'
        body += 'It may help if the color profile is HD (1-1-1); we continue to investigate performance options, and will provide more recommendation here, when avilable.<br>'
        body += '<br>'
        body += '<br>'
        body += 'The encoding H.265 is not yet supported.<br>'
        body += '<br>'
        body += 'The new image 2.4.2, that supports Pi4/Pi5, does not correctly play mp4 audio on a Pi4 as of this time.<br>For Pi4, please continue to use image 2.4.1 to make mp4 with audio work correctly.<br>'
        body += '<br>'
        body += '</td>'
        body += '</tr>'

    if False:
        body += '<tr>'
        body += '<td>'
        body += 'MP4 to GIF'
        body += '</td>'
        body += '<td>'
        body += 'Google search for "MP4 to GIF" to find online convertors:<br>like: https://ezgif.com/video-to-gif'
        body += '</td>'
        body += '</tr>'

    body += '</table>'
    body += '</center>'

    return body, other


# ====================================
def make_body_GET(environ, start_response):
    # ====================================
    global s_get_count

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'profile' in query_items:
        profile = query_items['profile']
    else:
        profile = ''

    try:
        if profile:
            return make_body_get_profile(environ, profile)
        else:
            # main page
            return make_body_get_summary(environ)


    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ, start_response):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ,
                                             service + '_'):  # or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ, start_response)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ, start_response)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================
    action_report = ''

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        query_items = {}
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]

        if 'filetoview' in query_items:
            response_header = [('Content-type', 'text/html')]
            status = '200 OK'
            do_encode = True
            try:
                file_to_get = convert_text_to_from_url(query_items['filetoview'], reverse=True)
                zOutFilename = service_config['base_upload_path'] + file_to_get
                body = '(empty)'

                if file_to_get[-4:] == '.txt':
                    body = open(zOutFilename, 'rb').read().decode().replace('\n', '<br>')
                if file_to_get[-4:] == '.png':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/png')]
                    do_encode = False
                if file_to_get[-4:] == '.jpg':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/jpg')]
                    do_encode = False
                if file_to_get[-4:] == '.gif':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/gif')]
                    do_encode = False
                if file_to_get[-4:] == '.pdf':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'application/pdf')]
                    do_encode = False

                action_report += 'viewed a file: ' + zOutFilename
                other = {'action_report':action_report, 'add_wrapper': True} # add_wrapper tells the permissions to log the action
                permissions.log_page_allowed(environ, service, other)
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))


            # allow non wrapped response
            start_response(status, response_header)
            if do_encode:
                return [body.encode()]
            else:
                return [body]


        elif 'filetodownload' in query_items:
            try:
                file_to_get = convert_text_to_from_url(query_items['filetodownload'], reverse=True)
                zOutFilename = service_config['base_upload_path'] + '/' + file_to_get
                block_size = 1024
                size = os.path.getsize(zOutFilename)

                if ".zip" in zOutFilename:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'application/zip'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'rb')
                elif ".mov" in zOutFilename:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'video/quicktime'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'rb')
                elif ".mp4" in zOutFilename:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'video/mp4'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'rb')
                else:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'text/html'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'r')

                action_report += 'downloaded a file: ' + zOutFilename
                other = {'action_report':action_report, 'add_wrapper': True} # add_wrapper tells the permissions to log the action
                permissions.log_page_allowed(environ, service, other)

                if 'wsgi.file_wrapper' in environ:
                    return environ['wsgi.file_wrapper'](filelike, block_size)
                else:
                    return iter(lambda: filelike.read(block_size), '')
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            status = '200 OK'
            response_header = [('Content-type', 'text/html')]
            # allow non wrapped response
            start_response(status, response_header)
            return [body.encode()]
        else:
            body, other = make_body(environ, start_response) # this does -> permissions.log_page_allowed(environ, service, other)
            status = other['status']
            head = ''
            if 'head' in other:
                head = other['head']
            response_header = other['response_header']
            if other['add_wrapper']:
                html += '<html>\n'
                if head:
                    html += '<head>\n'
                    html += head
                    html += '</head>\n'
                html += '<body>\n'
            html += body
            if other['add_wrapper']:
                html += '</body>\n'
                html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_multimedia(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_extract_digits_at_end(self):
        the_name = 'test'
        expected = ''
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        the_name = 'test1'
        expected = '1'
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        the_name = 'test12'
        expected = '12'
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        the_name = '3test12'
        expected = '12'
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        the_name = '3test'
        expected = ''
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        the_name = 'te3st'
        expected = ''
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        # ends in a digit, but "mp4" is not meant as a sequence number
        the_name = 'test_mp4'
        expected = ''
        actual = extract_digits_at_end(the_name)
        self.assertEqual(expected, actual)

        _ = """
get all profile names
get all profile families
for each family (sorted), get the entries (sorted by ending digits)
for each such entry, is it a multimedia profile

"""

    def test_extract_profile_names(self):
        data_store_content = {'profile_VPN2_multimedia_demo12': {},
                              'profile_VPN2_multimedia_demo11': {},
                              'profile_VPN2_multimedia_demo2': {},
                              'profile_VPN2_multimedia_demo1': {},
                              'profile_VPN1_multimedia_demo1': {},
                              'misc': {},
                              '_permission_': True,
                              }
        expected = ['profile_VPN1_multimedia_demo1',
                    'profile_VPN2_multimedia_demo1',
                    'profile_VPN2_multimedia_demo11',
                    'profile_VPN2_multimedia_demo12',
                    'profile_VPN2_multimedia_demo2',
                    ]
        actual = extract_profile_names(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_profile_names_trailing_revision(self):
        data_store_content = {'profile_IL002_multimedia_DisplayBoard_Dates': {},
                              'profile_IL002_multimedia_DisplayBoard_Dates_2': {},
                              '_permission_': True,
                              }
        expected = ['profile_IL002_multimedia_DisplayBoard_Dates',
                    'profile_IL002_multimedia_DisplayBoard_Dates_2',
                    ]
        actual = extract_profile_names(data_store_content)
        self.assertEqual(expected, actual)


    def test_extract_profile_families_from_names(self):
        profile_names = ['profile_VPN1_multimedia_demo',
                         'profile_VPN2_multimedia_demo1',
                         'profile_VPN2_multimedia_demo11',
                         'profile_VPN2_multimedia_demo12',
                         'profile_VPN2_multimedia_demo2',
                         ]

        expected = ['profile_VPN1_multimedia_demo',
                    'profile_VPN2_multimedia_demo'
                    ]
        actual = extract_profile_families_from_names(profile_names)
        self.assertEqual(expected, actual)

        # strange case
        profile_names = ['profile_VPN1_multimedia_mp4_private',
                         'profile_VPN1_multimedia_mp4',
                         ]

        expected = ['profile_VPN1_multimedia_mp4',
                    'profile_VPN1_multimedia_mp4_private'
                    ]
        actual = extract_profile_families_from_names(profile_names)
        self.assertEqual(expected, actual)

    def test_extract_profile_families_from_names_trailing_revision(self):
        profile_names = ['profile_IL002_multimedia_DisplayBoard_Dates',
                         'profile_IL002_multimedia_DisplayBoard_Dates_2'
                         ]

        expected = ['profile_IL002_multimedia_DisplayBoard_Dates',
                    ]
        actual = extract_profile_families_from_names(profile_names)
        self.assertEqual(expected, actual)


    def test_sort_by_last_digits(self):
        names = ['profile_VPN2_multimedia_demo1',
                 'profile_VPN2_multimedia_demo11',
                 'profile_VPN2_multimedia_demo12',
                 'profile_VPN2_multimedia_demo',
                 'profile_VPN2_multimedia_demo0',
                 'profile_VPN2_multimedia_demo2',
                 ]
        expected = [
            'profile_VPN2_multimedia_demo',
            'profile_VPN2_multimedia_demo0',
            'profile_VPN2_multimedia_demo1',
            'profile_VPN2_multimedia_demo2',
            'profile_VPN2_multimedia_demo11',
            'profile_VPN2_multimedia_demo12',
        ]
        actual = sort_by_last_digits(names)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted_sorted_digits(self):
        data_store_content = {'profile_VPN2_multimedia_demo12': {},
                              'profile_VPN2_multimedia_demo11': {},
                              'profile_VPN2_multimedia_demo2': {},
                              'profile_VPN2_multimedia_demo1': {},
                              }
        expected = ['profile_VPN2_multimedia_demo1',
                    'profile_VPN2_multimedia_demo2',
                    'profile_VPN2_multimedia_demo11',
                    'profile_VPN2_multimedia_demo12'
                    ]
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted_sorted_digits_mixed(self):
        data_store_content = {'profile_VPN2_multimedia_demo12': {},
                              'profile_VPN2_multimedia_demo11': {},
                              'profile_VPN2_multimedia_demo2': {},
                              'profile_VPN2_multimedia_demo1': {},
                              'profile_VPN1_multimedia_demo': {},
                              }
        expected = ['profile_VPN1_multimedia_demo',
                    'profile_VPN2_multimedia_demo1',
                    'profile_VPN2_multimedia_demo2',
                    'profile_VPN2_multimedia_demo11',
                    'profile_VPN2_multimedia_demo12'
                    ]
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted_sorted_extended_family(self):
        # ends in a digit, but "mp4" is not meant as a sequence number
        data_store_content = {'profile_VPN1_multimedia_mp4_private': {},
                              'profile_VPN1_multimedia_mp4': {},
                              }
        expected = ['profile_VPN1_multimedia_mp4',
                    'profile_VPN1_multimedia_mp4_private'
                    ]
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted(self):
        data_store_content = {'profile_VPN1_multimedia_demo': {"bookmarks": {"(id)": {
            "01": {"url": "file:///cardinal/localhtml/showgif.html", "autolaunch": True,
                   "title": "VPN1_multimedia_demo"}}}}}
        expected = ['profile_VPN1_multimedia_demo']
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'override_permission_multimedia_create': 'No', 'profile_VPN1_multimedia_demo': {
            "bookmarks": {"(id)": {"01": {"url": "file:///cardinal/localhtml/showgif.html", "autolaunch": True,
                                          "title": "VPN1_multimedia_demo"}}}}}
        expected = ['profile_VPN1_multimedia_demo']
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted_not_user_permissions(self):
        data_store_content = {'user_permission_david.ferguson_multimedia_read': 'Yes', 'profile_VPN1_multimedia_demo': """{
            "bookmarks": {"(id)": {"01": {"url": "file:///cardinal/localhtml/showgif.html", "autolaunch": True,
                                          "title": "VPN1_multimedia_demo"}}}}"""}
        expected = ['profile_VPN1_multimedia_demo']
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)

    def test_extract_multimedia_profiles_from_datastore_sorted_sorted(self):
        data_store_content = {'profile_VPN2_multimedia_demo': '{}',
                              'profile_VPN1_multimedia_demo': '{}'}
        expected = ['profile_VPN1_multimedia_demo',
                    'profile_VPN2_multimedia_demo']
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)


    def test_extract_multimedia_profiles_from_datastore_trailing_revision(self):
        data_store_content = {'profile_IL002_multimedia_DisplayBoard_Dates': '{}',
                              'profile_IL002_multimedia_DisplayBoard_Dates_2': '{}'}
        expected = ['profile_IL002_multimedia_DisplayBoard_Dates',
                    'profile_IL002_multimedia_DisplayBoard_Dates_2']
        actual = extract_multimedia_profiles_from_datastore_sorted(data_store_content)
        self.assertEqual(expected, actual)



    def test_extract_multimedia_needs_from_profile(self):
        self.maxDiff = None # show all content in the exception case

        data_store_content = {'user_permission_david.ferguson_multimedia_read': 'Yes', 'profile_VPN1_multimedia_demo': '{"bookmarks": {"(id)": {"01": {"url": "file:///cardinal/localhtml/showgif.html", "autolaunch": true,"title": "VPN1_multimedia_demo"}}}}'}
        expected = {'showgif':'gif_mp4'}
        actual = extract_multimedia_needs_from_profile('profile_VPN1_multimedia_demo', data_store_content)
        self.assertEqual(expected, actual)


        data_store_content = {'profile_IL002_multimedia_DisplayBoard_Dates':"""
            {"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true,
            "url": "http://localhost:7010?reload_key=1,safe_day_start=2024.11.01,background_color=(0~0~0~1.0),font_bold=yes,font_color=(255~255~255~1.0),cellpadding=120,font_size=150,row1=Coding_Date|2029.01.01,row2=Short_Date|2025.04.06,row3=Safe_Days|(Safe_Days)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}"""}
        expected = {}
        actual = extract_multimedia_needs_from_profile('profile_IL002_multimedia_DisplayBoard_Dates', data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'profile_IL002_multimedia_DisplayBoard_Dates':"""
            {"bookmarks": {"(id)": {"01": {"title": "DisplayBoard_Dates_b", "autolaunch": true,
            "url": "http://localhost:7010?reload_key=1,safe_day_start=(datastore_user_data_IL002_safe_day_start),background_color=(0~0~0~1.0),font_bold=yes,font_color=(255~255~255~1.0),cellpadding=120,font_size=150,row1=Coding_Date|2029.01.01,row2=Short_Date|2025.04.06,row3=Safe_Days|(Safe_Days)", "disablekiosk": "yes"}}}, "preliminary": "test for layouts", "description": "Show Coding Date and Short Date"}"""}
        expected = {'datastore':{'safe_day_start':'user_data_IL002_safe_day_start'}}
        actual = extract_multimedia_needs_from_profile('profile_IL002_multimedia_DisplayBoard_Dates', data_store_content)
        self.assertEqual(expected, actual)


    def test_extract_user_permissions_on_profile_from_data_store(self):
        data_store_content = {'user_site_david.ferguson_VPN1': 'Yes'}
        user_name = 'david.ferguson'
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = []
        actual = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name)
        self.assertEqual(expected, actual)

        data_store_content = {'user_site_david.ferguson_VPN1': 'Yes',
                              'user_permission_david.ferguson_multimedia_read': 'Yes'}
        user_name = 'david.ferguson'
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = ['read']
        actual = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name)
        self.assertEqual(expected, actual)

        data_store_content = {'user_site_david.ferguson_VPN1': 'Yes',
                              'user_permission_david.ferguson_multimedia_read': 'Yes',
                              'user_permission_david.ferguson_multimedia_create': 'Yes'}
        user_name = 'david.ferguson'
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = ['create', 'read']
        actual = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name)
        self.assertEqual(expected, actual)

    def test_extract_user_permissions_on_profile_from_data_store_location_all(self):
        data_store_content = {'user_site_david.ferguson_(all)': 'Yes',
                              'user_permission_david.ferguson_multimedia_read': 'Yes',
                              'user_permission_david.ferguson_multimedia_create': 'Yes'}
        user_name = 'david.ferguson'
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = ['create', 'read']
        actual = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name)
        self.assertEqual(expected, actual)

    def test_extract_user_permissions_on_profile_from_data_store_location_with_no(self):
        data_store_content = {'user_site_david.ferguson_(all)': 'Yes',
                              'user_permission_david.ferguson_multimedia_read': 'Yes',
                              'user_permission_david.ferguson_multimedia_create': 'No'}
        user_name = 'david.ferguson'
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = ['read']
        actual = extract_user_permissions_on_profile_from_data_store(data_store_content, user_name, profile_name)
        self.assertEqual(expected, actual)

    def test_build_save_filename(self):
        service_config = {'base_upload_path': '/testpath'}  # don't include the trailing slash

        profile_name = 'profile_VPN1_multimedia_demo'
        active_time = '20231129131008'

        original_file_name = 'testfile.gif'
        expected = '/testpath/profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_testfile.gif'
        actual = build_save_filename(service_config, original_file_name, profile_name, active_time)
        self.assertEqual(expected, actual)

        original_file_name = 'test file.gif'
        expected = '/testpath/profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test_file.gif'
        actual = build_save_filename(service_config, original_file_name, profile_name, active_time)
        self.assertEqual(expected, actual)

        service_config = {'base_upload_path': '/testpath/'}  # do include the trailing slash

        original_file_name = 'testfile.gif'
        expected = '/testpath/profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_testfile.gif'
        actual = build_save_filename(service_config, original_file_name, profile_name, active_time)
        self.assertEqual(expected, actual)

        # be sure to handle mp4
        original_file_name = 'testfile.mp4'
        expected = '/testpath/profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_testfile.mp4'
        actual = build_save_filename(service_config, original_file_name, profile_name, active_time)
        self.assertEqual(expected, actual)

    def test_extract_profile_content_options_from_filelist(self):
        filelist = []
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = {}
        actual = extract_profile_content_options_from_filelist(filelist, profile_name)
        self.assertEqual(expected, actual)

        filelist = ['profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234']
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234'}
        actual = extract_profile_content_options_from_filelist(filelist, profile_name)
        self.assertEqual(expected, actual)

        filelist = ['profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234',
                    'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234']
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234',
                    '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234'}
        actual = extract_profile_content_options_from_filelist(filelist, profile_name)
        self.assertEqual(expected, actual)

        # make sure an md5 does not get reported
        filelist = [
            'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
            'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif.md5',
            'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif',
            'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif.md5',
        ]
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
                    '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'}
        actual = extract_profile_content_options_from_filelist(filelist, profile_name)
        self.assertEqual(expected, actual)

        # be sure to handle mp4
        filelist = [
            'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
            'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif.md5',
            'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.mp4',
            'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.mp4.md5',
        ]
        profile_name = 'profile_VPN1_multimedia_demo'
        expected = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
                    '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.mp4'}
        actual = extract_profile_content_options_from_filelist(filelist, profile_name)
        self.assertEqual(expected, actual)

    def test_select_media_for_active_from_time_and_available(self):
        current_time = ''
        available = {}
        expected = ''
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129131009'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'}
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129131008'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'}
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129131007'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'}
        expected = ''
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129131008'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
                     '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'}
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129141007'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
                     '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'}
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        current_time = '20231129141008'
        available = {'20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
                     '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'}
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        # put the available out of order, to see that it still returns the closest timed profile. (Basically, test that the keys are sorted before selection is made)
        current_time = '20231129141008'
        available = {
            '20231129141008': 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif',
            '20231129131008': 'profile_VPN1_multimedia_demo_(active)_20231129131008_(orig_file)_test1234.gif',
        }
        expected = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'
        actual = select_media_for_active_from_time_and_available(current_time, available)
        self.assertEqual(expected, actual)

        # be sure to handle mp4

    def test_extract_destination_from_media_name(self):
        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234.gif'
        expected = 'showgif.gif'
        actual = extract_destination_from_media_name(media_name)
        self.assertEqual(expected, actual)

        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234_(dest)_video1.mp4'
        expected = 'video1.mp4'
        actual = extract_destination_from_media_name(media_name)
        self.assertEqual(expected, actual)

    def test_extract_originalfilename_from_media_name(self):
        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test5555'
        expected = 'test5555'
        actual = extract_originalfilename_from_media_name(media_name)
        self.assertEqual(expected, actual)

        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234_(dest)_video1.mp4'
        expected = 'test1234'
        actual = extract_originalfilename_from_media_name(media_name)
        self.assertEqual(expected, actual)

        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234_5678_(dest)_video1.mp4'
        expected = 'test1234_5678'
        actual = extract_originalfilename_from_media_name(media_name)
        self.assertEqual(expected, actual)

        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test1234_5678_(dest)_video1.mp4'
        expected = 'test1234_5678.mp4'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

        media_name = 'test1234.mp4'
        expected = 'test1234.mp4'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

        media_name = 'profile_VPN1_multimedia_demo_(active)_20231129141008_(orig_file)_test5555.gif'
        expected = 'test5555.gif'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

        media_name = 'Bloque_2.0_presentacion.gif'
        expected = 'Bloque_2.0_presentacion.gif'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

        media_name = 'Bloque_2.0_presentacion'
        expected = 'Bloque_2.0_presentacion'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

        media_name = 'profile_MEX03_multimedia_Show2_(active)_20240208190242_(orig_file)_Bloque_2.0_presentacion.gif'
        expected = 'Bloque_2.0_presentacion.gif'
        actual = extract_originalfilename_from_media_name(media_name, keep_extension=True)
        self.assertEqual(expected, actual)

    def test_select_devices_with_profile(self):
        data_store_content = {'device_profile_10000000e3669edf': 'profile_VPN1_multimedia_demo',
                              }
        the_profile = ''
        expected = []
        actual = select_devices_with_profile(data_store_content, the_profile)
        self.assertEqual(expected, actual)

        data_store_content = {'device_profile_10000000e3669edf': 'profile_VPN1_multimedia_demo',
                              }
        the_profile = 'profile_VPN1_multimedia_demo'
        expected = ['10000000e3669edf']
        actual = select_devices_with_profile(data_store_content, the_profile)
        self.assertEqual(expected, actual)

    def test_make_file_extension_lower(self):
        filename = 'test.gif'
        expected = 'test.gif'
        actual = make_file_extension_lower(filename)
        self.assertEqual(expected, actual)

        filename = 'test.GIF'
        expected = 'test.gif'
        actual = make_file_extension_lower(filename)
        self.assertEqual(expected, actual)

        filename = 'test.middle.GIF'
        expected = 'test.middle.gif'
        actual = make_file_extension_lower(filename)
        self.assertEqual(expected, actual)

        filename = 'test'
        expected = 'test'
        actual = make_file_extension_lower(filename)
        self.assertEqual(expected, actual)

    def test_convert_text_to_from_url(self):
        text = 'test'
        expected = 'test'
        actual = convert_text_to_from_url(text)
        self.assertEqual(expected, actual)

        text = 'test,123'
        expected = 'test%2C123'
        actual = convert_text_to_from_url(text)
        self.assertEqual(expected, actual)

        text = 'test%2C123'
        expected = 'test,123'
        actual = convert_text_to_from_url(text, reverse=True)
        self.assertEqual(expected, actual)

    def test_rgba_to_hex(self):
        rgb = '(0,0,0,1.0)'
        expected = '#000000'
        actual = rgba_to_hex(rgb)
        self.assertEqual(expected, actual)

        rgb = '(255,255,255,1.0)'
        expected = '#FFFFFF'
        actual = rgba_to_hex(rgb)
        self.assertEqual(expected, actual)

        rgb = ''
        expected = '#FFFFFF'
        actual = rgba_to_hex(rgb)
        self.assertEqual(expected, actual)

    def test_rgba_to_alpha(self):
        rgb = '(255,255,255,1.0)'
        expected = '1.0'
        actual = rgba_to_alpha(rgb)
        self.assertEqual(expected, actual)

        rgb = ''
        expected = '1.0'
        actual = rgba_to_alpha(rgb)
        self.assertEqual(expected, actual)

    def test_hex_to_rgb(self):
        hex_value = '#FFFFFF'
        expected = (255, 255, 255)
        actual = hex_to_rgb(hex_value)
        self.assertEqual(expected, actual)


# End of source file
