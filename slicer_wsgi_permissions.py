# A permissions for slicer page services
#
# Yes, you have to manually add new modules into this file, to be able to get them setup.
#     (module)_create
#     (module)_read
#     (module)_update
#     (module)_delete

service = "permissions"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/permissions.py

using:
sudo vi /var/www/html/permissions.py

It also requires:

sudo vi /etc/httpd/conf.d/python-permissions.conf
----- start copy -----
WSGIScriptAlias /permissions /var/www/html/permissions.py
----- end copy -----

sudo chown apache:apache /var/www/html/permissions.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import permissions; print(permissions.make_body())"



!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/permissions-runner
sudo chmod +x /var/www/html/permissions-runner

# ===== begin: start file
#!/usr/bin/env python
import permissions
permissions.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/permissions-runner.service
sudo systemctl daemon-reload
sudo systemctl stop permissions-runner.service
sudo systemctl start permissions-runner.service
sudo systemctl enable permissions-runner.service

# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/permissions-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/permissions


"""

_ = """
(module)_(type)

types:
create
read
update
delete


CRUD
Create, Read, Update, Delete


Initially, all permissions are "assigned" required, and can always be set (in users page).

Have the ["public","login"] setting come from datastore, per (module)_(type)


"""

s_permission_types = {}
s_permission_types['public'] = {'description': 'all are allowed to have this permission, without even being logged in'}
s_permission_types['login'] = {'description': 'any validly logged in user can access this'}
s_permission_types['assigned'] = {'description': 'must be assigned (in the user view)'}

_ = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

import copy
import datetime
import traceback
import json
import os
import shutil
import sys
import time
import traceback
import unittest.mock

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")
try:
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    trust_list = service_config['trust_list']
    base_raw_path = service_config['base_raw_path']

except:
    trust_list = []
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

s_permission_list = []

section_id = '0-Permissions'
block_id = 'explicit'
s_permission_list.append(['permissions_explicit',
                          'Hide the public menus from this user, and only show the explicitly assigned menu permissions',
                          block_id, section_id])

section_id = '1-Multimedia'
block_id = 'media'
s_permission_list.append(
    ['multimedia_create', 'Files that are tied to a profile for multimedia displays', block_id, section_id])
s_permission_list.append(
    ['multimedia_read', 'Files that are tied to a profile for multimedia displays', block_id, section_id])
s_permission_list.append(
    ['multimedia_update', 'Files that are tied to a profile for multimedia displays', block_id, section_id])
s_permission_list.append(
    ['multimedia_delete', 'Files that are tied to a profile for multimedia displays', block_id, section_id])

section_id = '2-Standard'
# Most RCS people get
block_id = 'RCS'
s_permission_list.append(['users_view', 'This allows users to see this users page', block_id, section_id])
s_permission_list.append(
    ['profiles_view', 'This allows users to view the contents of the profiles', block_id, section_id])
s_permission_list.append(['download_view', 'This allows users to see the downloads page', block_id, section_id])

# Only a few per site, so that they are 'in charge' of those devices
block_id = 'RCS config'
s_permission_list.append(['device_configuration',
                          'Caution: This allows users to make device configuration changes. See the site id list for the sites they are allowed to configure.',
                          block_id, section_id])
s_permission_list.append(['datamine_edit', 'local RCS for reviewing datalogs', block_id, section_id])
s_permission_list.append(
    ['device_edit', 'local RCS for setting up the pulled service updates of individual devices', block_id, section_id])
s_permission_list.append(['device_collections_edit',
                          '!!! Requires device_edit !!!local RCS for setting up the pulled service updates for collections of many devices',
                          block_id, section_id])
s_permission_list.append(
    ['rings_view', 'Device maintenance: permission to see the ring version assignments', block_id, section_id])

# GIF content management (Might be a marketing person, instead of RCS)
block_id = 'Display Content'
s_permission_list.append(['deviceupload_create', 'Files meant to be loaded out to devices', block_id, section_id])
s_permission_list.append(['deviceupload_read', 'Files meant to be loaded out to devices', block_id, section_id])
s_permission_list.append(['deviceupload_update', 'Files meant to be loaded out to devices', block_id, section_id])

# Direct download content, like the video files
block_id = 'html files (like video content)'
s_permission_list.append(['htmlfiles_create', 'Files meant to be directly served', block_id, section_id])
s_permission_list.append(['htmlfiles_read', 'Files meant to be directly served', block_id, section_id])
s_permission_list.append(['htmlfiles_update', 'Files meant to be directly served', block_id, section_id])
s_permission_list.append(['htmlfiles_delete', 'Files meant to be directly served', block_id, section_id])

# For super access
block_id = 'RCS supervisor'
s_permission_list.append(
    ['tasks_edit', 'local RCS super user, to allow for push of critical services', block_id, section_id])
s_permission_list.append(['sites_update', 'Update site details, like timezone override.', block_id, section_id])

section_id = '3-Advanced'
# Developer people, For maintaining Slicer and the content that it serves up
block_id = 'Manage Slicer'
s_permission_list.append(
    ['rings_edit', 'Slicer maintenance: permission to modify ring version assignemnts', block_id, section_id])
s_permission_list.append(
    ['codeupload_edit', 'Slicer maintenance: permission to update pi service version files', block_id, section_id])

s_permission_list.append(['upload_edit', 'Slicer maintenance: permission to upload content', block_id, section_id])
s_permission_list.append(
    ['upload_delete', 'Slicer maintenance: permission to remove previous uploads', block_id, section_id])
s_permission_list.append(['datastore_edit',
                          'Slicer maintenance: permission to re-instate datastore trust after a long outage, or restore from backup',
                          block_id, section_id])

block_id = 'Manage Slicer Supervisor'
s_permission_list.append(
    ['codeupload_delete', 'Slicer maintenance: permission to remove previous pi version files', block_id, section_id])

# EUDE manager(s)
block_id = 'Manage Users Supervisor'
s_permission_list.append(
    ['users_edit', 'This is what allows all of these permissions to be modified', block_id, section_id])

# Super-Super developer user (direct editing of json content)
block_id = 'Dev Support'
s_permission_list.append(
    ['profiles_edit', 'Slicer maintenance: permission to update and create profile content', block_id, section_id])

# Developer: This is how to run development in production, without others seeing the interface for it yet
block_id = 'Dev'
s_permission_list.append(
    ['development_read', 'Slicer maintenance: developer view of unreleased features, while in testing', block_id,
     section_id])

# not yet utilized
section_id = '4-Administrator'
block_id = 'Administrator'
s_permission_list.append(['deviceupload_delete', 'Files meant to be loaded out to devices', block_id, section_id])

s_permission_list.append(
    ['address2location_create', 'Slicer lookup of pi address, to determine location', block_id, section_id])
s_permission_list.append(
    ['address2location_read', 'Slicer lookup of pi address, to determine location', block_id, section_id])
s_permission_list.append(
    ['address2location_update', 'Slicer lookup of pi address, to determine location', block_id, section_id])
s_permission_list.append(
    ['address2location_delete', 'Slicer lookup of pi address, to determine location', block_id, section_id])

s_permission_list.append(['scan_edit', 'Slicer scans: ', block_id, section_id])

s_permission_list.append(['device_command', 'Allow sending free-form commands. ', block_id, section_id])

s_permission_list.append(['device_wipe', 'Allow the clearing of all settings. ', block_id, section_id])

s_permission_list.append(['certificates_create', 'Slicer certificates and pi certificates', block_id, section_id])
s_permission_list.append(['certificates_read', 'Slicer certificates and pi certificates', block_id, section_id])
s_permission_list.append(['certificates_delete', 'Slicer certificates and pi certificates', block_id, section_id])

s_permission_list.append(['watchdog_read', 'Server monitoring', block_id, section_id])
s_permission_list.append(['timezones_read', '', block_id, section_id])
s_permission_list.append(['videos_read', '', block_id, section_id])
s_permission_list.append(['ldapsupport_read', '', block_id, section_id])

section_id = '5-Setup'
block_id = 'Setup'
s_permission_list.append(['dataport_create', 'Datastore import of data', block_id, section_id])

_ = """
s_permission_list.append(['template_create', '', block_id, section_id])
s_permission_list.append(['template_read', '', block_id, section_id])
s_permission_list.append(['template_update', '', block_id, section_id])
s_permission_list.append(['template_delete', '', block_id, section_id])

"""

# obsolete?
section_id = '6-Obsolete'
block_id = 'Obsolete'
s_permission_list.append(
    ['reports_detail_view', 'This allows users to see more details in the device reporting table.', block_id,
     section_id])

try:
    #    s_organization_live_permissions_file = service_config['permissions_save_path'] + 'live.txt'
    s_organization_live_permissions_file = '/dev/shm/permissions_live.txt'
except:
    s_organization_live_permissions_file = ''

# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# get from outside: login
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ----------------------------
def log_page_allowed(environ, service_making_the_call, other):
    # ----------------------------
    trace_back_string = ''

    ip_addr = '(no IPaddr)'
    unique_id = '(no_unique)'
    if 'REMOTE_ADDR' in environ:
        ip_addr = environ['REMOTE_ADDR']
    if 'HTTP_COOKIE' in environ: # like: login_time=20250221154014216887
        unique_id = environ['HTTP_COOKIE']

    if other['add_wrapper']:
        # This was a user call, not a data pull from javascript, so log it.
        try:
            action_report = ''

            user = login.get_current_user(environ, refresh_timeout=True)
            if not user:
                if ip_addr != environ['SERVER_ADDR']: # don't log calls from other services on the server
                    user = ip_addr

            if user:
                method = environ['REQUEST_METHOD'] # 'POST', 'GET', ...

#                action_report += '---- ' + ip_addr + ' - ' + '"' + unique_id + '"' + '\n'
# this was getting way to annoying, like "dtCookie=v_4_srv_12_sn_692A50036A7540814CB58E93B7AD3EB5_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1_rcs-3Acss_0; login_time=20250327114852248176"
# and it was on every single entry... take it away for now
                action_report += '---- ' + ip_addr + '\n'

                action_report += method + ' ' + service_making_the_call

                if method == 'GET':
                    if 'QUERY_STRING' in environ:
                        query = environ['QUERY_STRING']
                        if query:
                            action_report += '?' + query
                    if 'action_report' in other:
                        action_report += '\n    ' + '\n    '.join(other['action_report'].split('\n'))

                if method == 'POST':
                    if 'action_report' in other:
                        action_report += '\n    ' + '\n    '.join(other['action_report'].split('\n'))

                TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
                # 20210406201829131704

                output_file = base_raw_path + user + '/' + TS
                content = service_making_the_call + ',' + method + '\n' + action_report

                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))
                open(output_file, 'w').write(content)

                # 2025.02.14 Testing how to save for POST
                # do this, as a way to collect examples of the environ, to be able to log more details about POST and GET
                output_file = '/dev/shm/permissions_' + method + '.txt'
                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))
                open(output_file, 'w').write(str(environ))

        except:
            trace_back_string = traceback.format_exc().replace("\"", "'")

    try:
        open('/dev/shm/permissions.log_page_allowed', 'w').write(str(environ) + '\n\n' + str(service_making_the_call) + '\n\n' + trace_back_string)
    except:
        pass

# ----------------------------
def get_user_allowed_logs():
    # ----------------------------
    return_value = {}

    try:
        for user in os.listdir(base_raw_path):
            return_value[user] = {}
            user_log_path = base_raw_path + user +'/'
            for filename in os.listdir(user_log_path):
                # FixMe: Do we want to prune here, based on age over 90 days?
                return_value[user][filename] = open(user_log_path + filename, 'r').read()
    except:
        pass

    return return_value

# ----------------------------
def get_is_admin(environ):
    # ----------------------------
    user = login.get_current_user(environ)
    return login.get_user_is_admin(user)

# ====================================
def get_module_permissions_for_environ(environ):
    # ====================================
    user = login.get_current_user(environ)
    return get_module_permissions_for_user(user)


# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# get from outside: (organization, through filesystem)
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ----------------------------
def get_live_permissions():
    # ----------------------------
    return_value = {}
    try:
        content = open(s_organization_live_permissions_file, 'r').read()
        return_value = json.loads(content)
    except:
        pass
    return return_value

# ====================================
def get_all_modules():
    # ====================================
    live_permissions = get_live_permissions()
    return make_all_modules(live_permissions)

# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# get from outside: (filesystem)
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ----------------------------
def get_all_py_content():
    # ----------------------------
    return_value = {}

    try:
        for file_found in os.listdir('/var/www/html/'):
            try:
                if '.py' == file_found[-3:]:
                    return_value[file_found] = open(file_found, 'r').read()
            except:
                pass
    except:
        pass

    return return_value


# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# set to outside: datastore
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ====================================
def set_permission_allowed(user, page_name, value_to_set):
    # ====================================
    datastore.set_value('user_permission_' + user + '_' + page_name, value_to_set)

# ====================================
def set_permission_override(page_name, value_to_set):
    # ====================================
    datastore.set_value('override_permission_' + page_name, value_to_set)

# ====================================
def set_site_allowed(user, siteid, value_to_set):
    # ====================================
    datastore.set_value('user_site_' + user + '_' + siteid, value_to_set)

# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# set to outside: (filesystem)
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    did_write = False

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        # flush all to disk
        #        os.sync()

        shutil.move(temp_name, output_file)

        did_write = True

    return did_write

# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# make methods, that should be fully unit tested
# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ====================================
def make_all_modules(live_permissions):
    # ====================================
    return_value = []
    for module_found in live_permissions:
        return_value.append(module_found)
    # some static ones:
    return_value.append('server-status')
    return return_value

# ----------------------------
def make_permissions_from_file_contents(file_contents):
    # ----------------------------
    return_value = {}

    for file_name in file_contents.keys():
        name_to_use = file_name.replace('.py', '')

        if not name_to_use in return_value:
            return_value[name_to_use] = {}
        in_permissions = False
        for line in file_contents[file_name].split('\n'):
            if 'start_' + 'permissions' in line:
                # the text is in two pieces, so that this line of code does not trigger the processing
                in_permissions = True

            if in_permissions:
                try:
                    splits = line.split(':')
                    return_value[name_to_use][splits[0]] = splits[1]
                except:
                    pass

            if 'end_' + 'permissions' in line:
                in_permissions = False
                break

    return return_value

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url

# ====================================
def make_permission_types_from_modules_permissions(modules_permissions):
    # ====================================
    return_value = {}
    for module in modules_permissions.keys():
        for permission_type in modules_permissions[module]:
            return_value[permission_type] = None
    return sorted(return_value.keys())

# ====================================
def make_modules_permissions(modules, all_permissions_list):
    # ====================================
    return_value = {}
    # get them all from what modules we find
    for module in sorted(modules):
        if not module in return_value:
            return_value[module] = []
        if module == 'server-status':
            return_value[module].append('read')
    # get them all from what permissions have ever been saved
    for permission in all_permissions_list:
        permission_name = permission[0]

        module = permission_name.split('_')[0]
        if not module in return_value:
            return_value[module] = []
        return_value[module].append(permission_name.split('_')[1])
    return return_value

# <><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><><>
# ====================================
def make_permissions_cache_from_datastore(data_store_content):
    # ====================================
    return_value = {}

    for key_found in data_store_content.keys():
        splits = key_found.split('_')
        value_found = data_store_content[key_found]

        if len(splits) > 3:
            if splits[0] == 'override':
                if splits[1] == 'permission':
                    module_found = splits[2]
                    permission_found = splits[3]

                    if value_found != 'None':
                        if not 'overrides' in return_value:
                            return_value['overrides'] = {}
                        if not module_found in return_value['overrides']:
                            return_value['overrides'][module_found] = {}
                        return_value['overrides'][module_found][permission_found] = value_found

        if splits[0] == 'user':
            if len(splits) > 4:
                if splits[1] == 'permission':
                    user_found = splits[2]
                    module_found = splits[3]
                    permission_found = splits[4]

                    if value_found == 'Yes':
                        # stash it to the user
                        if not 'users' in return_value:
                            return_value['users'] = {}
                        if not user_found in return_value['users']:
                            return_value['users'][user_found] = {}
                        if not 'modules' in return_value['users'][user_found]:
                            return_value['users'][user_found]['modules'] = {}
                        if not module_found in return_value['users'][user_found]['modules']:
                            return_value['users'][user_found]['modules'][module_found] = []
                        return_value['users'][user_found]['modules'][module_found].append(permission_found)

            if len(splits) > 3:
                if splits[1] == 'site':
                    user_found = splits[2]
                    site_found = splits[3]

                    if value_found == 'Yes':
                        # stash it to the user
                        if not 'users' in return_value:
                            return_value['users'] = {}
                        if not user_found in return_value['users']:
                            return_value['users'][user_found] = {}
                        if not 'sites' in return_value['users'][user_found]:
                            return_value['users'][user_found]['sites'] = []
                        return_value['users'][user_found]['sites'].append(site_found)

                if splits[1] == 'login':
                    user_found = splits[2]
                    attribute_found = splits[3]

                    if attribute_found == 'admin':
                        if value_found == 'Yes':
                            # stash it to the user
                            if not 'users' in return_value:
                                return_value['users'] = {}
                            if not user_found in return_value['users']:
                                return_value['users'][user_found] = {}
                            if not 'login' in return_value['users'][user_found]:
                                return_value['users'][user_found]['login'] = {}

                            # mark as admin
                            return_value['users'][user_found]['login']['admin'] = True

                            # assign  the extra permissions here
                            if not 'modules' in return_value['users'][user_found]:
                                return_value['users'][user_found]['modules'] = {}

                            if not 'datastore' in return_value['users'][user_found]['modules']:
                                return_value['users'][user_found]['modules']['datastore'] = []
                            if not 'edit' in return_value['users'][user_found]['modules']['datastore']:
                                return_value['users'][user_found]['modules']['datastore'].append('edit')

                            if not 'users' in return_value['users'][user_found]['modules']:
                                return_value['users'][user_found]['modules']['users'] = []
                            if not 'edit' in return_value['users'][user_found]['modules']['users']:
                                return_value['users'][user_found]['modules']['users'].append('edit')

                    if attribute_found == 'details':
                        # stash it to the user
                        if not 'users_groups' in return_value:
                            return_value['users_groups'] = {}
                        if not user_found in return_value['users_groups']:
                            return_value['users_groups'][user_found] = {}
                        return_value['users_groups'][user_found] = value_found.split(',')

    return return_value

# ====================================
def make_user_permissions_from_cache(user, permissions_cache):
    # ====================================
    return_value = {}

    if 'users' in permissions_cache:
        if user in permissions_cache['users']:
            if 'modules' in permissions_cache['users'][user]:
                return_value = permissions_cache['users'][user]['modules']

    return return_value









# ====================================
def get_all_complete_permissions():
    # ====================================
    return_value = []

    for item in s_permission_list:
        return_value.append(item)

    section_id = '7-Loaded'
    live_permissions = get_live_permissions()
    for module_found in live_permissions:
        block_id = module_found
        for permission_found in sorted(live_permissions[module_found].keys()):
            return_value.append([module_found + '_' + permission_found, '', block_id, section_id])

    # some static ones:
    module_found = 'server-status'
    permission_found = 'read'
    block_id = module_found
    return_value.append([module_found + '_' + permission_found, '', block_id, section_id])

    return return_value


# ====================================
def get_all_permissions():
    # ====================================
    # Create
    # Read
    # Update (edit)
    # Delete

    return_value = []
    for item in get_all_complete_permissions():
        return_value.append(item[0])  # Just the name of the permission

    return return_value


# ====================================
def permission_description(the_permission):
    # ====================================
    # Create
    # Read
    # Update (edit)
    # Delete

    return_value = ''

    for item in get_all_complete_permissions():
        if the_permission == item[0]:
            if len(item) > 1:
                return_value = item[1]
                break

    return return_value


# ====================================
def permission_block(the_permission):
    # ====================================
    # Create
    # Read
    # Update (edit)
    # Delete

    return_value = ''

    for item in get_all_complete_permissions():
        if the_permission == item[0]:
            if len(item) > 2:
                return_value = item[2]
                break

    return return_value


# ====================================
def permission_section(the_permission):
    # ====================================
    # Create
    # Read
    # Update (edit)
    # Delete

    return_value = ''

    for item in get_all_complete_permissions():
        if the_permission == item[0]:
            if len(item) > 3:
                return_value = item[3]
                break

    return return_value


# ====================================
def permission_site_allowed(environ, siteid):
    # ====================================
    is_allowed = False
    user = login.get_current_user(environ)

    return site_allowed_user(user, siteid)


# ====================================
def site_allowed_user(user, siteid, data_store_content=None):
    # ====================================
    is_allowed = False

    if data_store_content:
        result = datastore.get_value_stored(data_store_content, 'user_site_' + user + '_' + siteid)
    else:
        result = datastore.get_value('user_site_' + user + '_' + siteid)
    if result == 'Yes':
        is_allowed = True

    return is_allowed



# ====================================
def get_modules_with_overrides(override_type='Public'):
    # ====================================
    return_value = {}

    try:
        permission_overrides = datastore.all_datastore('override_permission_')

        for permission_override in permission_overrides.keys():
            if permission_overrides[permission_override] == override_type:
                module = permission_override.replace('override_permission_', '').split('_')[0]
                return_value[module] = {}
    except:
        pass

    return return_value


# ====================================
def get_pages_with_overrides(override_type='Public', data_store_content=None):
    # ====================================
    return_value = {}

    try:
        permission_overrides = datastore.all_datastore('override_permission_', data_store_content=data_store_content)

        for permission_override in permission_overrides.keys():
            if permission_overrides[permission_override] == override_type:
                page_name = permission_override.replace('override_permission_', '')
                return_value[page_name] = {}
    except:
        pass

    return return_value


# ====================================
def get_permission_override(page_name):
    # ====================================
    the_full_key = 'override_permission_' + page_name

    return datastore.get_value(the_key=the_full_key, default_if_not_exist='None')



# ====================================
def permission_allowed_user(user, page_name, data_store_content=None, allow_overrides=True):
    # ====================================
    is_allowed = False

    user_is_admin = False
    key = 'user_login_' + user + '_admin'
    if data_store_content:
        user_admin_value = datastore.get_value_stored(data_store_content, key)
    else:
        user_admin_value = datastore.get_value(key)

    if user_admin_value == 'Yes':
        user_is_admin = True
    if user in trust_list:
        # Hardwired
        user_is_admin = True

    if user_is_admin:
        if page_name == 'users_edit':
            is_allowed = True

        # This next one is needed due to the reboot non trust of data store, which
        #   was preventing the permissions from being accessible, which did not let
        #   me begin trust.
        if page_name == 'datastore_edit':
            is_allowed = True

    if data_store_content:
        result = datastore.get_value_stored(data_store_content, 'user_permission_' + user + '_' + page_name)
    else:
        result = datastore.get_value('user_permission_' + user + '_' + page_name)

    if result == 'Yes':
        is_allowed = True
    else:
        if allow_overrides:
            pages_with_overrides = get_pages_with_overrides(override_type='Public',
                                                            data_store_content=data_store_content)
            if page_name in pages_with_overrides:
                is_allowed = True
                pass

            if user:
                pages_with_overrides = get_pages_with_overrides(override_type='Login',
                                                                data_store_content=data_store_content)
                if page_name in pages_with_overrides:
                    is_allowed = True
                    pass

    return is_allowed


# ====================================
def permission_prefix_allowed(environ, prefix_name):
    # ====================================
    user = login.get_current_user(environ)

    return permission_prefix_allowed_user(user, prefix_name)

# ====================================
def permission_prefix_allowed_user(user, prefix_name):
    # ====================================

    is_allowed = False

    for permission_name in get_all_permissions():
        if prefix_name in permission_name:
            if permission_allowed_user(user, permission_name):
                is_allowed = True

    if not is_allowed:
        modules_with_overrides = get_modules_with_overrides(override_type='Login')
        if prefix_name.split('_')[0] in modules_with_overrides:
            is_allowed = True

    return is_allowed


# ====================================
def permission_allowed(environ, page_name):
    # ====================================
    user = login.get_current_user(environ)

    return permission_allowed_user(user, page_name)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    #    if permission_prefix_allowed(environ, 'permissions_') or permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other

# ====================================
def make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache,do_not_use=[],modules_found_file_content=''):
    # ====================================
    return_value = {'to_show_above_login':[],
                    'to_show_for_login':[],
                    'to_show_below_login':[],
                    'current_user':the_user}

    is_permissions_explicit_set = False
    if 'users' in permissions_cache:
        if the_user in permissions_cache['users']:
            if 'modules' in permissions_cache['users'][the_user]:
                if 'permissions' in permissions_cache['users'][the_user]['modules']:
                    if 'explicit' in permissions_cache['users'][the_user]['modules']['permissions']:
                        is_permissions_explicit_set = True

    if modules_found_file_content:
        modules_found = json.loads(modules_found_file_content)
    else:
        modules_found = [unittest.mock.ANY]

    # make the assignments

    # ----------
    # above
    # ----------
    if not is_permissions_explicit_set:
        if 'overrides' in permissions_cache:
            for key in sorted(permissions_cache['overrides'].keys()):
                if (key not in do_not_use) and (key + '.py' in modules_found):
                    is_public = False
                    for sub_feature in permissions_cache['overrides'][key].keys():
                        if permissions_cache['overrides'][key][sub_feature] == 'Public':
                            is_public = True
                    if is_public:
                        if key != 'login':
                            return_value['to_show_above_login'].append(key)

    # ----------
    # login
    # ----------
    if 'overrides' in permissions_cache:
        if 'login' in sorted(permissions_cache['overrides'].keys()):
            return_value['to_show_for_login'].append('login')

    # ----------
    # below
    # ----------
    if 'users' in permissions_cache:
        if the_user in permissions_cache['users']:
            if 'modules' in permissions_cache['users'][the_user]:
                for key in sorted(permissions_cache['users'][the_user]['modules'].keys()):
                    if (key not in do_not_use) and (key + '.py' in modules_found):
                        value = permissions_cache['users'][the_user]['modules'][key]
                        if not ((key == 'permissions') and (value == ['explicit'])):
                            if not key in return_value['to_show_above_login']: # do not repeat
                                if key != 'login':
                                    return_value['to_show_below_login'].append(key)

    if not is_permissions_explicit_set:
        if 'overrides' in permissions_cache:
            for key in sorted(permissions_cache['overrides'].keys()):
                if (key not in do_not_use) and (key + '.py' in modules_found):
                    is_login = False
                    for sub_feature in permissions_cache['overrides'][key].keys():
                        if permissions_cache['overrides'][key][sub_feature] == 'Login':
                            is_login = True
                    if is_login:
                        if key != 'login':
                            return_value['to_show_below_login'].append(key)
        # get them back in order, in case we added any to the end
        return_value['to_show_below_login'] = sorted(return_value['to_show_below_login'])

    return return_value

# ====================================
def get_module_permissions_for_user(user):
    # ====================================

    to_show_above_login = []
    to_show_for_login = []
    to_show_below_login = []

    all_modules_found = get_all_modules()

    # look for any 'exclusive' permissions, which means to hide any public items
    is_permissions_explicit_set = False
    module_permission_prefix_valid = permission_prefix_allowed_user(user, 'permissions_explicit')
    if module_permission_prefix_valid:
        is_permissions_explicit_set = True

    # do the "public" list at the top
    modules_with_overrides_public = get_modules_with_overrides(override_type='Public')
    if not is_permissions_explicit_set:
        for module in sorted(all_modules_found):
            if module != 'login':
                if module in modules_with_overrides_public:
                    to_show_above_login.append(module)

    # if login is in the list, then show it here
    for module in sorted(all_modules_found):
        if module == 'login':
            to_show_for_login.append(module)

    # Show items the current login user can see
    modules_with_overrides_login = get_modules_with_overrides(override_type='Login')
    for module in sorted(all_modules_found):
        if module != 'login':
            module_permission_prefix_valid = permission_prefix_allowed_user(user, module + '_')
            if (module in modules_with_overrides_login) or module_permission_prefix_valid:
                if not module in to_show_above_login:
                    to_show_below_login.append(module)

    d = {}
    d['to_show_above_login'] = to_show_above_login
    d['to_show_for_login'] = to_show_for_login
    d['to_show_below_login'] = to_show_below_login
    d['current_user'] = user

    d['modules_with_overrides_login'] = modules_with_overrides_login
    d['user'] = user
    d['is_permissions_explicit_set'] = is_permissions_explicit_set

    return d


# ====================================
def make_body_GET(environ):
    # ====================================
    return build_current_view(environ)


# ====================================
def build_current_view(environ):
    # ====================================

    global s_get_count
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    modules = get_all_modules()
    all_permissions_list = get_all_complete_permissions()
    modules_permissions = make_modules_permissions(modules, all_permissions_list)
    permission_types = make_permission_types_from_modules_permissions(modules_permissions)

    body += '<center>'
    body += '<br><br>'
    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td>'
    body += 'module<br>permission<br>override'
    body += '</td>'

    for permission_type in permission_types:
        body += '<td>'
        body += permission_type
        body += '</td>'

    body += '</tr>'

    for module in sorted(modules_permissions.keys()):
        body += '<tr>'
        body += '<td>'
        body += module
        body += '</td>'

        for permission_type in permission_types:
            if permission_type in modules_permissions[module]:
                full_name = module + '_' + permission_type
                per_value = get_permission_override(full_name)
                if per_value == 'None':
                    the_string = "None"
                    next_Value = "Login"
                    color = "(255, 0, 0, 0.3)"
                elif per_value == 'Login':
                    the_string = "Login"
                    next_Value = "Public"
                    color = "(0, 255, 0, 0.3)"
                else:
                    the_string = "Public"
                    next_Value = "None"
                    color = "(0, 255, 0, 0.3)"

                body += '<td title="' + full_name + '" style="background-color:rgba' + color + '">'
                body += '<form method="post" action="">'
                body += '<select name="permission_allowed_kind" id="permission_allowed_kind" hidden>'
                body += '<option value="' + full_name + '" selected>' + full_name + '</option>'
                body += '</select>'

                body += '<select name="permission_allowed_kind_allowed" id="permission_allowed_kind_allowed" hidden>'
                body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                body += '</select>'
                if permission_allowed(environ, 'users_edit'):
                    body += '<center>'
                    body += '<input type="submit" value="' + the_string + '">'
                    body += '</center>'
                body += '</form>'
                body += '</td>'
            else:
                body += '<td>'
                body += '</td>'
        body += '</tr>'
    body += '</table>'
    body += '</center>'

    return body, other


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    if 'permission_allowed_kind_allowed' in d:
        set_permission_override(d['permission_allowed_kind'][0],
                                d['permission_allowed_kind_allowed'][0])

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        try:
            all_py_content = get_all_py_content()
            module_permissions = json.dumps(make_permissions_from_file_contents(all_py_content))

            if s_organization_live_permissions_file:
                do_atomic_write_if_different(s_organization_live_permissions_file, module_permissions)

        except:
            startup_exceptions = traceback.format_exc().replace("\"", "'")
            try:
                open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
            except:
                pass

        time.sleep(5)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_make_all_modules(self):
        live_permissions = ["test"]
        expected = ["test","server-status"]
        actual = make_all_modules(live_permissions)
        self.assertEqual(expected, actual)

    def test_make_permissions_from_file_contents(self):
        file_contents = {}
        file_contents['test1.py'] = """
start_permissions
create:login
read:all
update:assigned
delete:temporary
end_permissions

# only the first ones should count
start_permissions
create:junk
read:all
update:assigned
delete:temporary
end_permissions

"""
        expected = {'test1': {'create': 'login', 'read': 'all', 'update': 'assigned', 'delete': 'temporary'}}
        actual = make_permissions_from_file_contents(file_contents)
        self.assertEqual(expected, actual)

    def test_current_user_has_any_permission(self):
        user_permissions = {}

    def test_make_modules_permissions(self):
        modules = ['test1']
        all_permissions_list = [('test1_read', '', '', '')]
        expected = {'test1': ['read']}
        actual = make_modules_permissions(modules, all_permissions_list)
        self.assertEqual(expected, actual)

    def test_make_modules_permissions2(self):
        modules = ['test1']
        all_permissions_list = [('test1_read', '', '', ''), ('test2_delete', '', '', '')]
        expected = {'test1': ['read'], 'test2': ['delete']}
        actual = make_modules_permissions(modules, all_permissions_list)
        self.assertEqual(expected, actual)

    def test_make_modules_permissions3(self):
        modules = ['test3']
        all_permissions_list = [('test1_read', '', '', ''), ('test2_delete', '', '', '')]
        expected = {'test3': [], 'test1': ['read'], 'test2': ['delete']}
        actual = make_modules_permissions(modules, all_permissions_list)
        self.assertEqual(expected, actual)

    def test_make_permission_types_from_modules_permissions(self):
        modules_permissions = {'test3': [], 'test1': ['read'], 'test2': ['delete']}
        expected = ['delete', 'read']
        actual = make_permission_types_from_modules_permissions(modules_permissions)
        self.assertEqual(expected, actual)

    def test_make_home_url_from_environ(self):
        environ = {'REQUEST_SCHEME': 'http', 'HTTP_HOST': 'slicer.cardinalhealth.com'}
        expected = 'http://slicer.cardinalhealth.com'
        actual = make_home_url_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_make_permissions_cache_from_datastore(self):
        data_store_content = {}
        data_store_content['override_permission_testpage'] = 'login'
        data_store_content['user_site_david.ferguson_MEX03'] = 'Yes'
        data_store_content['user_login_david.ferguson_admin'] = 'Yes'
        data_store_content['user_permission_david.ferguson_address2location_create'] = 'Yes'

        data_store_content = {}
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_permission_david.ferguson_address2location_create'] = 'No'
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_permission_david.ferguson_address2location_create'] = 'Yes'
        expected = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_permission_david.ferguson_permissions_explicit'] = 'Yes'
        expected = {'users':{'david.ferguson':{'modules':{'permissions':['explicit']}}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_site_david.ferguson_MEX03'] = 'No'
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_site_david.ferguson_MEX03'] = 'Yes'
        expected = {'users':{'david.ferguson':{'sites':['MEX03']}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_login_david.ferguson_admin'] = 'No'
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_site_david.ferguson_MEX03'] = 'No'
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        # try clean admin
        data_store_content = {}
        data_store_content['user_login_david.ferguson_admin'] = 'Yes'
        expected = {'users':{'david.ferguson':{'login':{'admin':True},'modules':{'datastore':['edit'],'users':['edit']}}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        # try negated admin permission listed later, to be sure it does not undo the admin rights
        data_store_content = {}
        data_store_content['user_login_david.ferguson_admin'] = 'Yes'
        data_store_content['user_permission_david.ferguson_users_edit'] = 'No'
        expected = {'users':{'david.ferguson':{'login':{'admin':True},'modules':{'datastore':['edit'],'users':['edit']}}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_login_david.ferguson_details'] = 'A-AMA-test'
        expected = {'users_groups':{'david.ferguson':['A-AMA-test']}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {}
        data_store_content['user_login_david.ferguson_details'] = 'A-AMA-test,A-AMA-test2'
        expected = {'users_groups':{'david.ferguson':['A-AMA-test','A-AMA-test2']}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

    def test_make_permissions_cache_from_datastore_overrides(self):
        data_store_content = {'override_permission_dashboard_read':'None'}
        expected = {}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'override_permission_dashboard_read':'Public'}
        expected = {'overrides':{'dashboard':{'read':'Public'}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'override_permission_dashboard_read':'Login'}
        expected = {'overrides':{'dashboard':{'read':'Login'}}}
        actual = make_permissions_cache_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

    def test_make_user_permissions_from_cache(self):
        permissions_cache = {}
        the_user = ''
        expected = {}
        actual = make_user_permissions_from_cache(the_user,permissions_cache)
        self.assertEqual(expected, actual)

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}}}
        the_user = 'david.ferguson'
        expected = {'address2location':['create']}
        actual = make_user_permissions_from_cache(the_user,permissions_cache)
        self.assertEqual(expected, actual)

    def test_make_module_permissions_for_user_from_permissions_cache(self):
        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'dashboard':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':['dashboard'],
                    'to_show_for_login':[],
                    'to_show_below_login':['address2location'],
                    'current_user':'david.ferguson'}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])
        self.assertEqual(expected['current_user'], actual['current_user'])

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create'],
                                                        'permissions':['explicit']}}},
                             'overrides':{'dashboard':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':[],
                    'to_show_below_login':['address2location']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create'],
                                                        'permissions':['explicit']}}},
                             'overrides':{'dashboard':{'read':'Login'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':[],
                    'to_show_below_login':['address2location']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])



        # make public, but also assigned, and explicit/not
        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'dashboard':{'read':'Public'},
                                          'address2location':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':['address2location','dashboard'],
                    'to_show_for_login':[],
                    'to_show_below_login':[]}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create'],
                                                                   'permissions':['explicit']}}},
                             'overrides':{'dashboard':{'read':'Public'},
                                          'address2location':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':[],
                    'to_show_below_login':['address2location']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        # add login as public (start with empty user)
        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'}}}
        the_user = ''
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':[]}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':['address2location']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'},'users':{'view':'Login'}}
                             }
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':['address2location','users']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        # work to eliminate any that are on the 'do not use' list, above
        do_not_use = ['dashboard']

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'dashboard':{'read':'Public'}}}
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':[],
                    'to_show_below_login':['address2location'],
                    'current_user':'david.ferguson'}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache,do_not_use)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])
        self.assertEqual(expected['current_user'], actual['current_user'])

        # work to eliminate any that are on the 'do not use' list, below based on user permission
        do_not_use = ['address2location']

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'},'users':{'view':'Login'}}
                             }
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':['users']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache,do_not_use)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        # work to eliminate any that are on the 'do not use' list, below based also on override permission
        do_not_use = ['address2location', 'template']

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'},'users':{'view':'Login'},'template':{'view':'Login'}}
                             }
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':['users']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache,do_not_use)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])

        # see if the modules are in the file system as a last cross check
        modules_found_file_content = '["address2location.py","login.py","users.py"]'
        do_not_use = ['address2location']

        permissions_cache = {'users':{'david.ferguson':{'modules':{'address2location':['create']}}},
                             'overrides':{'login':{'read':'Public'},'users':{'view':'Login'},'template':{'view':'Login'}}
                             }
        the_user = 'david.ferguson'
        expected = {'to_show_above_login':[],
                    'to_show_for_login':['login'],
                    'to_show_below_login':['users']}
        actual = make_module_permissions_for_user_from_permissions_cache(the_user,permissions_cache,do_not_use,modules_found_file_content)
        self.assertEqual(expected['to_show_above_login'], actual['to_show_above_login'])
        self.assertEqual(expected['to_show_for_login'], actual['to_show_for_login'])
        self.assertEqual(expected['to_show_below_login'], actual['to_show_below_login'])


    def test_make_permissions_cache_from_datastore_production(self):
        data_store_content = {}

        file_content = open('datastore_snapshot.txt', 'r').read()
        for line_read in file_content.split('\n'):
            splits = line_read.split('\t')
            if len(splits) == 2:
                data_store_content[splits[0]] = splits[1]
        # just test that there are no crashes on 'real' data
        cache = make_permissions_cache_from_datastore(data_store_content)













# End of file
