<a name="readme-top"></a>

<!-- PROJECT LOGO -->
<br />
<div align="center">
  <!-- <a href="https://github.com/jvbalcita/rcs-rams">
    <img src="public/assets/img/cardinal-logo-sm.png" alt="Logo" width="160" height="80">
  </a> -->

<h1 align="center">Slicer</h1>
  <p align="center">
    Slicer is a web application, to provide management of the Raspberry Pis.
  </p>
</div>

<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#system-overview">System Overview</a>
      <ul>
        <li><a href="#built-with">Built With</a></li>
        <li><a href="#architecture">Architecture</a></li>
        <li><a href="#disaster-recovery-plan">Disaster Recovery Plan</a></li>
        <li><a href="#sop">SOP</a></li>
        <li><a href="#backup-and-recovery">Backup and Recovery</a></li>
        <li><a href="#communication-from-the-pi">Communication from the Pi</a></li>
        <li><a href="#runtime-layout">Runtime Layout</a></li>
        <li><a href="#pi-services">Pi Services</a></li>
        <li>
          <a href="#slicer-gcp-services">Slicer (GCP) Services</a>
          <ul>
            <li><a href="#configuration-data-and-log-data">Configuration Data and Log Data</a></li>
          </ul>
        </li>
        <li><a href="#pi-services-versioning-on-slicer">Pi Services Versioning on Slicer</a></li>
      </ul>
    </li>
    <li><a href="#authors">Author(s)</a></li>
    <li>
      <a href="#acknowledgements">Acknowledgements</a>
      <ul>
        <li><a href="#markdown-notes">Markdown Notes</a></li>
        <li><a href="#live-edit-of-markdown-content">Live Edit of Markdown Content</a></li>
      </ul>
    </li>
  </ol>
</details>

# System Overview

## Built With

* [![Python][Python.org]][Python-url]
* [![RaspberryPi][Raspberrypi.org]][Raspberrypi-url]

<!-- ARCHITECTURE -->
## Architecture

|Item| ||
|:---:|:---:|:---:|
|Application Name|Slicer|
|Application Status|Standard|
|Criticality[^A1]|Operational|
|RTO[^A2]|Tier 4 (120+ Hours)|
|RPO[^A3]|Data Recovery Not Required|
|Landscape[^A4]|GCP|
|Primary[^A5]|us-central1-c|
|Alternate[^A6]|(none yet)|
|Project|`mac-mgmt-pr`|
|Primary Server|`lpec5009slicr01`|
|Boot disk|31 GB, standard|lpec5009slicr01-boot|
|Data Disk|60 GB, SSD|lpec5009slicr01-disk-lpec5009slicr01-d|
|DNS|`slicer.cardinalhealth.net`|
|IP address|`***********`|
|Certificate|???|

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Disaster Recovery Plan
In case of GCP disk loss:
1. Use the latest one hour disk snapshot to set up a new instance.

## SOP
There is no particular maintenance action(s) required, in order to provide day-to-day operation.

## Backup and Recovery
Backups are set up in GCP, for one hour snapshots.
Recovery would be to rebuild from those snapshots.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Communication from the Pi
|Interface|User |pi_runner|
|:---:|:---:|:---:|
|**Method**|Chromium Browser|requests lib|
|**Options**|Bookmark List|---|
|**Restriction**|Whitelist Proxy|---|
|***Corp Network***|---|---|
|**Destination**|(any whitelisted<br>url)|`https://slicer.cardinalhealth.net`|

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Runtime Layout

|  | Server<br>(Slicer)| Client |
|:---:|:---:|:---:|
|**data location**|/mnt/disks/SSD/var/log/slicer<br>/var/log/slicer|/cardinal/|
|**code location**|/var/www/html/|/cardinal/|
|**deployed file**|*.py|pi_*.py|
|**repo file**|slicer_wsgi_*.py|pi_*.py|
|**language**| python|python3
|**runtime**|WSGI|(custom wrapper)|
|**static location**|/var/www/htmlfiles/|---|
|**application**|Apache|shell|
|**startup**|SystemD|SystemD|
|**OS**|CentOS|Rasbian|
|**Location**|GCP|Raspberry Pi|

Example: The file "slicer_wsgi_reports.py" is loaded to the GCP instance as "/var/www/html/reports.py", and is then available at the url https://slicer.cardinalhealth.net/reports

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Pi Services

The information needed to build a new full image is in "image-building.txt".

Files named as "pi_\*.py" are files that fully describe a service to be run on the remote
pi device.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Slicer (GCP) Services
This is the code that runs on a central server, that manages remote devices.
Set up on a Production GCP server.

See the file "gcp_servers.txt" file

There are slices (activities and data) of:
1. Remote data collection from the devices (current IP address)
2. Reporting of device status (in a table, for humans to use)
3. Providing configuration to the devices (bookmarks and whitelist)
4. Providing pi service(s) updates

Files
(beginning with the string "slicer_wsgi_\*.py", these files are loaded onto the
server as "/var/www/html/\*.py", and get a file "/etc/httpd/conf.d/python-\*.conf"
file created.

    slicer_wsgi_checkin.py  (For the devices in the field to call home to)
    slicer_wsgi_reports.py  (For reporting on the checkin data)

Access the site at: https://slicer.cardinalhealth.net

### Configuration Data and Log Data:

Look to the two files:

    Slicer_filesystem.txt

    Slicer_datastore.txt

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Pi Services Versioning on Slicer

The pi_(service) files get loaded onto the Slicer server, for serving out to the pis.

Storage looks like:
/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py
/var/www/html/pi_services/pi_runner/R.1.1/pi_runner.py

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- AUTHORS -->
## Author(s)

* **David Ferguson** - *Initial work* -
* **Jack Vincent Balcita** - *Contributor* -
<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- ACKNOWLEDGMENTS -->
## Acknowledgments

### Markdown Notes

* [Markdown Guide] https://www.markdownguide.org

* [Basic Writing and Formatting Syntax] https://docs.github.com/en/github/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax

* [Mastering Markdown] https://guides.github.com/features/mastering-markdown/

### Live Edit of Markdown Content

https://stackedit.io/app#

[^A1]:What is the criticality of this business process, application or infrastructure component?<br>
**Essential**: Business has defined these processes, systems, and data as crucial for operating the business. Cardinal Health will likely cease to exist or experience major operational impacts if these first-tier processes, systems and data are unavailable or compromised. (These are the “crown jewels” of Cardinal Health). These systems must be recovered within the first 24 Hours of a disaster, cyber event, or outage. These are our highest priority processes, systems and data.  These systems are currently approved by the OC.  If you are not identified as Essential, at this time, please do not select this option.
**Mission**: Business has defined these processes, systems, or data as mission critical to support the business. There will be significant, but not existential harm to employee productivity, Cardinal Health reputation, revenue, and customer impact if these second-tier systems are unavailable or compromised. These processes, systems, and data must be recovered within the first 24 - 48 hours of a disaster or outage.
**Business**:  Business has defined these processes, systems or data as critical to support the business. These systems are critical to day-to-day operations but will not cause existential harm to Cardinal Health, patients, customers, or employees if they are unavailable for 48 hours. These systems do need to be recovered within 48 – 120 hours following a disaster.
**Operational**:  Processes, systems or data defined as required for day-to-day operations of the business but not critical in the event of a disaster or other major system outage/event. There will be a reduction in organizational efficiency, but otherwise limited impact on Cardinal Health if these fourth-tier processes, systems or data are unavailable or compromised. These applications/systems would not impact essential or mission critical business operations (customer facing, order channel, patient systems, etc.).

[^A2]: What is the Recovery Time Objective (RTO) for this application/data/process?  This is the timeframe in which the business expects this application/data/process to be  recovered following a disaster (this is the timeframe before a financial, operational, and/or legal impacts is expected).<br>
**Tier 1** (0 - 24 Hours)
**Tier 2** (24 - 48 Hours)
**Tier 3** (48 - 120 Hours)
**Tier 4** (120+ Hours)
**DR Not Rerquired**
**Needs Assessed**
**Incubate Status**

[^A3]:What is the maximum timeframe amount of data that can be lost due to unplanned solution outage?<br>
**The Recovery Point Objective (RPO)** is the maximum allowable amount of data loss the business can tolerate following an outage, business interruption, or disaster.  (RPO) describes a point in time to which data must be restored in order to be acceptable to the owner(s) of the processes supported by that data.  This is often thought of as the time between the last available backup and the time a disruption could potentially occur. The RPO is established based on tolerance for loss of data or reentering of data.

[^A4]: Where is your application located? Ie. **GCP**, **AWS**, **Dublin**, **Colo**, **SaaS**, **Azure**, **Cardinal Site** (Manufacturing/Distribution), **Other**.  (If Other, please note in document in Notes section (bottom)).

[^A5]: What is your Primary Location? (GCP Zone)

[^A6]: What is your Alternate Location? (GCP Zone)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[Python.org]: https://img.shields.io/badge/python-3670A0?style=for-the-badge&logo=python&logoColor=ffdd54
[Python-url]: https://python.org
[Raspberrypi.org]: https://img.shields.io/badge/raspberry-pi?style=for-the-badge&logo=raspberry-pi&logoColor=ffdd54&color=%23A22846
[Raspberrypi-url]: https://www.raspberrypi.org/
[Bootstrap.com]: https://img.shields.io/badge/Bootstrap-563D7C?style=for-the-badge&logo=bootstrap&logoColor=white
[Bootstrap-url]: https://getbootstrap.com
[JQuery.com]: https://img.shields.io/badge/jQuery-0769AD?style=for-the-badge&logo=jquery&logoColor=white
[JQuery-url]: https://jquery.com 