# A users interface

service = "users"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/users.py

using:
sudo vi /var/www/html/users.py

It also requires:

sudo vi /etc/httpd/conf.d/python-users.conf
----- start copy -----
WSGIScriptAlias /users /var/www/html/users.py
----- end copy -----

sudo chown apache:apache /var/www/html/users.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import users; print(users.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/users

"""

_permissions = """
start_permissions
view:
end_permissions
"""

import copy
import traceback
import json
import os
import sys
import time

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

startup_exceptions = ''

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import address2location
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import management
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

color_red_warning = "(255, 0, 0, 0.3)"
color_yellow_caution = "(255, 255, 100, 0.3)"
color_green = "(0, 255, 0, 0.3)"
color_clear = "(0, 0, 0, 0.0)"
color_purple = "(255, 0, 255, 0.3)"

# ----------------------------
def make_users_from_datastore(data_store_content):
    # ----------------------------
    return_value = []

    for key in data_store_content.keys():
        splits = key.split('_')
        if splits[0] == 'user':
            user_name = splits[2]
            if not user_name in return_value:
                return_value.append(user_name)

    return sorted(return_value)

# ----------------------------
def user_sort(list_names):
    # ----------------------------
    last_names = {}

    for full_name in list_names:
        splits = full_name.split('.')

        last_name = ''
        first_name = splits[0]
        if len(splits) > 1:
            last_name = splits[1].lower()

        if not last_name in last_names:
            last_names[last_name] = []
        last_names[last_name].append(full_name)

    try:
        # slicer03
        return_value = []
        for last_name in sorted(last_names.keys()):
            for full_name in sorted(last_names[last_name], key=str.casefold):
                return_value.append(full_name)
    except:
        # slicer01
        return_value = []
        for last_name in sorted(last_names.keys()):
            for full_name in sorted(last_names[last_name]):
                return_value.append(full_name)

    return return_value


# ----------------------------
def user_sort_orig(list_names):
    # ----------------------------
    last_names = {}

    for full_name in list_names:
        splits = full_name.split('.')

        last_name = ''
        first_name = splits[0]
        if len(splits) > 1:
            last_name = splits[1]

        if not last_name in last_names:
            last_names[last_name] = []
        last_names[last_name].append(first_name)

    return_value = []

    for last_name in sorted(last_names.keys()):
        for first_name in sorted(last_names[last_name], key=str.casefold):
            return_value.append(first_name + '.' + last_name)

    return return_value


# ----------------------------
def get_all_users(data_store_content, list_names):
    # ----------------------------
    for key in data_store_content.keys():
        if 'user_permission_' in key:
            user = key.replace('user_permission_', '').split('_')[0]
            if not user in list_names:
                list_names.append(user)

        if 'user_site_' in key:
            user = key.replace('user_site_', '').split('_')[0]
            if not user in list_names:
                list_names.append(user)

    return user_sort(list_names)


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def convert_seconds_to_human(time_in_seconds):
    # ====================================
    the_report = ''

    time_to_use = int(time_in_seconds)

    if time_to_use < 0:
        the_report += '-'
        time_to_use = abs(time_to_use)

    days = int(time_to_use / (3600 * 24))
    time_to_use -= days * 3600 * 24

    hours = int(time_to_use / 3600)
    time_to_use -= hours * 3600

    minutes = int(time_to_use / 60)
    time_to_use -= minutes * 60

    seconds = time_to_use % 60

    if days > 0:
        if the_report:
            the_report += ' '
        the_report += str(days) + 'd'

    if hours > 0:
        if the_report:
            the_report += ' '
        the_report += str(hours) + 'h'

    if minutes > 0:
        if the_report:
            the_report += ' '
        the_report += str(minutes) + 'm'

    if (not the_report) or (seconds > 0):
        if the_report:
            the_report += ' '
        the_report += str(seconds) + 's'

    return the_report

# ====================================
def make_current_view_details(url_to_use, REQUEST_URI, query_items, allow_edit, data_store_content, available_permissions,
                                list_users, available_siteid):
    # ====================================

    active_filter = ''
    if 'active' in query_items:
        active_filter = query_items['active']

    data_store_user_content = {}
    for key in data_store_content.keys():
        if 'user_' in key:
            data_store_user_content[key] = data_store_content[key]
        if 'override_' in key:
            data_store_user_content[key] = data_store_content[key]


    sections = {}
    for permission in available_permissions:
        permission_section = permissions.permission_section(permission)
        if permission_section:
            if not permission_section in sections:
                sections[permission_section] = {}
            sections[permission_section][permission] = 1

    if sections:
        all_views = []
        for section in sorted(sections.keys()):
            all_views.append('permissions:' + section)
        all_views.append('manage')
    else:
        all_views = ['permissions', 'manage']


    column_to_show = ''
    list_names = list(list_users.keys())

    if 'user' in query_items:
        list_names = [query_items['user']]

    if 'column' in query_items:
        column_to_show = query_items['column']

    if 'view' in query_items:
        all_views = query_items['view'].split('|')

    try:
        active_users = login.get_active_users()
    except:
        active_users = {}

    all_permissions = []
    all_siteid = []
    if column_to_show:
        if 'permission_' in column_to_show:
            for permission in available_permissions:
                if column_to_show.replace('permission_', '') == permission:
                    all_permissions.append(permission)
        elif 'siteid_' in column_to_show:
            for siteid in available_siteid:
                if column_to_show.replace('siteid_', '') == siteid:
                    all_siteid.append(siteid)
    else:
        for the_view in all_views:
            if 'permissions:' in the_view:
                permission_section = the_view.split(':')[1]
                for permission in sections[permission_section].keys():
                    all_permissions.append(permission)
            elif 'permissions' in the_view:
                all_permissions = available_permissions
        if 'manage' in all_views:
            all_siteid = available_siteid


    permission_blocks = {}
    permission_descriptions = {}
    for permission in all_permissions:
        permission_block = permissions.permission_block(permission)
        permission_blocks[permission] = permission_block
        permission_description = permissions.permission_description(permission)
        permission_descriptions[permission] = permission_description

    # +++++++++++++++++++++++++++
    # users
    # +++++++++++++++++++++++++++

    url_user_base = url_to_use + REQUEST_URI

    users_content = {}
    for user in list_names:
        users_content[user] = {'login': {'time_remaining': '', 'url_to_use': ''},
                               'permissions': {},
                               'manage': {},
                               'any_active': False,
                               'is_admin': False,
                               'lastvalidlogin': ''}

        users_content[user]['lastvalidlogin'] = ''
        users_content[user]['is_admin'] = False
        if user in list_users:
            users_content[user]['lastvalidlogin'] = list_users[user]['last_valid']
            if list_users[user]['admin'] == 'Yes':
                users_content[user]['is_admin'] = True

        if user in active_users:
            for seconds_remaining in sorted(active_users[user]):
                users_content[user]['login']['time_remaining'] = convert_seconds_to_human(seconds_remaining)


        url_to_use_user = url_user_base + "?user=" + user
        users_content[user]['login']['url_to_use'] = url_to_use_user

        for permission in all_permissions:
            try:
                # slicer03
                per_value = permissions.permission_allowed_user(user, permission,
                                                                data_store_content=data_store_user_content,
                                                                allow_overrides=False)
                per_value_override = permissions.permission_allowed_user(user, permission,
                                                                         data_store_content=data_store_user_content,
                                                                         allow_overrides=True)
            except:
                # slicer01
                per_value = permissions.permission_allowed_user(user, permission,
                                                                data_store_content=data_store_user_content)
                per_value_override = permissions.permission_allowed_user(user, permission,
                                                                         data_store_content=data_store_user_content)
            users_content[user]['permissions'][permission] = {'per_value': per_value,
                                                              'per_value_override': per_value_override}
            if per_value:
                users_content[user]['any_active'] = True


        for siteid in all_siteid:
            try:
                # slicer03
                per_value = permissions.site_allowed_user(user, siteid, data_store_content=data_store_user_content)
            except:
                # slicer01
                per_value = permissions.site_allowed_user(user, siteid)
            users_content[user]['manage'][siteid] = {'per_value': per_value}
            if per_value:
                users_content[user]['any_active'] = True


    # Filter here, based on given criteria, if any
    if active_filter:
        if active_filter == 'Yes':
            for user in list_names:
                if not users_content[user]['any_active']:
                    del users_content[user]



    index_for_user = ''
    groups_for_user = ''
    if (len(list_names) == 1) and allow_edit:
        try:
            user = list_names[0]
            import index
            permissions_cache = permissions.make_permissions_cache_from_datastore(data_store_content)
            module_permissions = permissions.make_module_permissions_for_user_from_permissions_cache(user,permissions_cache)
            index_for_user = index.make_body_GET_content(module_permissions)

            permissions_cache = permissions.make_permissions_cache_from_datastore(data_store_content)
            if 'users_groups' in permissions_cache:
                if user in permissions_cache['users_groups']:
                    for item in sorted(permissions_cache['users_groups'][user]):
                        groups_for_user += item + '<br>'

        except:
            pass


    view_details = {}
    view_details['available_siteid'] = available_siteid
    view_details['active_users'] = active_users
    view_details['list_names'] = user_sort(list_names)
    view_details['the_views'] = copy.copy(all_views)
    view_details['sections'] = sections
    view_details['active_filter'] = active_filter
    view_details['all_permissions'] = all_permissions
    view_details['all_siteid'] = all_siteid
    view_details['all_views'] = all_views
    view_details['data_store_user_content'] = data_store_user_content
    view_details['permission_blocks'] = permission_blocks
    view_details['permission_descriptions'] = permission_descriptions
    view_details['users_content'] = users_content
    view_details['allow_edit'] = allow_edit
    view_details['index_for_user'] = index_for_user
    view_details['groups_for_user'] = groups_for_user
    view_details['url_to_use'] = url_to_use

    return view_details

# ====================================
def build_current_view(environ):
    # ====================================
    allow_edit = permissions.permission_allowed(environ, 'users_edit')
    url_to_use = make_home_url_from_environ(environ)
    REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]
    data_store_content = datastore.all_datastore()

    return build_current_view_details(allow_edit, url_to_use, REQUEST_URI, query_items, data_store_content)

# ====================================
def build_current_view_details(allow_edit, url_to_use, REQUEST_URI, query_items, data_store_content):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    try:

        list_names = login.get_all_login_valid_users()

        users_from_datastore = make_users_from_datastore(data_store_content)
        for user in users_from_datastore:
            if not user in list_names:
                list_names.append(user)

        list_users = {}
        for user in list_names:
            list_users[user] = {}
            key = 'user_login_' + user + '_lastvalidlogin'
            list_users[user]['last_valid'] = datastore.get_value_stored(data_store_content, key)

            key = 'user_login_' + user + '_admin'
            list_users[user]['admin'] = datastore.get_value_stored(data_store_content, key)


        available_permissions = permissions.get_all_permissions()  # do not sort, let the permissions list define the display order
        all_locations = address2location.get_all_locations()

        management_id_d = {}
        for siteid in management.get_management_block_list(data_store_content):
            management_id_d[siteid] = True
        management_id_d['(all)'] = True

        for siteid in all_locations:
            management_id_d[siteid] = True

        available_siteid = sorted(management_id_d.keys())

        # build content
        view_details = make_current_view_details(url_to_use, REQUEST_URI, query_items, allow_edit,
                                                data_store_content,
                                                available_permissions,
                                                list_users,
                                                available_siteid
                                                        )

        # pull out content
        users_content = view_details['users_content']
        allow_edit = view_details['allow_edit']
        url_to_use = view_details['url_to_use']

        # build page

        # --------------------------------
        # Show it
        # --------------------------------
        body = ''

        body += """
    <script>
    function URLjump(jumpLocation) {
        location.href = jumpLocation;
    }
    </script>
        """

        body += '<div id="testdiv">'

        name_to_show = "Home"
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

        # ---------------------------
        # Show the top level filter
        # ---------------------------
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        for view in view_details['all_views']:
            color_to_use = ''
            if view in view_details['the_views']:
                color_to_use = color_green

            body += '<td style="background-color:rgba' + color_to_use + '">'
            url_to_use_jump = url_to_use + REQUEST_URI
            url_to_use_jump += "?view=" + view
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_jump + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + view + """</a>"""
            body += '</td>'
        body += '</tr>'
        body += '</table>'

        # ---------------------------
        # Show what has been selected
        # ---------------------------
        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'

        # Block IDs
        # +++++++++++++++++++++++++++
        # headers: Groups
        # +++++++++++++++++++++++++++
        body += '<tr>'
        body += '<th>'
        body += '</th>'
        previous = ''
        count_same = 1
        for permission in view_details['all_permissions']:
            permission_block = view_details['permission_blocks'][permission]
            if previous != permission_block:
                if previous:
                    body += '<th colspan="' + str(count_same) + '">'
                    body += '<center>'
                    body += previous
                    body += '</center>'
                    body += '</th>'
                    count_same = 1
            else:
                count_same += 1
            previous = permission_block
        # clean up the last set
        if count_same > 0:
            body += '<th colspan="' + str(count_same) + '">'
            body += '<center>'
            body += previous
            body += '</center>'
            body += '</th>'
        body += '</tr>'

        # +++++++++++++++++++++++++++
        # headers: detail
        # +++++++++++++++++++++++++++
        body += '<tr>'
        body += '<td>'
        name_to_show = 'user<br>* = admin on login'
        url_to_use_jump = url_to_use + REQUEST_URI
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use_jump + """');return false\""""
        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
        body += '</td>'

        for permission in view_details['all_permissions']:
            permission_description = view_details['permission_descriptions'][permission]

            if permission_description:
                body += '<td title="' + permission_description + '">'
            else:
                body += '<td>'

            body += '<center>'
            #            body += permission.replace('_','<br>')

            name_to_show = permission.replace('_', '<br>')
            url_to_use_jump = url_to_use + REQUEST_URI
            url_to_use_jump += "?column=" + 'permission_' + permission
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_jump + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
            body += '</center>'
            body += '</td>'

        body += '<td>'
        body += '</td>'

        for siteid in view_details['all_siteid']:
            body += '<td>'
            #            body += 'site<br>' + siteid
            name_to_show = 'allow<br>' + siteid
            url_to_use_jump = url_to_use + REQUEST_URI
            url_to_use_jump += "?column=" + 'siteid_' + siteid
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_jump + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '</td>'
        body += '</tr>'



        # +++++++++++++++++++++++++++
        # Show the data here
        # +++++++++++++++++++++++++++

        for user in view_details['list_names']:
            user_line_body = ''
            times_to_show = users_content[user]['login']['time_remaining']
            if times_to_show:
                times_to_show = '<br>' + times_to_show

            user_line_body += '<tr>'
            user_line_body += '<td>'
            name_to_show = user + times_to_show

            onclick = """\"""" + 'URLjump' + """('""" + users_content[user]['login'][
                'url_to_use'] + """');return false\""""
            user_line_body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            if users_content[user]['lastvalidlogin']:
                user_line_body += '<br>' + users_content[user]['lastvalidlogin']

            if users_content[user]['is_admin']:
                user_line_body += ' *'

            user_line_body += '</td>'

            for permission in view_details['all_permissions']:
                loop_content = ''
                per_value = users_content[user]['permissions'][permission]['per_value']
                per_value_override = users_content[user]['permissions'][permission]['per_value_override']

                if per_value:
                    the_string = "Yes"
                    next_Value = "No"

                    if per_value_override == per_value:
                        color = "(0, 255, 0, 0.3)"
                    else:
                        color = "(255, 255, 0, 0.3)"
                else:
                    the_string = "No"
                    next_Value = "Yes"
                    if per_value_override == per_value:
                        color = "(255, 0, 0, 0.3)"
                    else:
                        color = "(255, 255, 0, 0.3)"

                loop_content += '<td title="' + user + '" style="background-color:rgba' + color + '">'
                loop_content += '<form method="post" action="">'
                loop_content += '<select name="permission_allowed_user_user" id="permission_allowed_user_user" hidden>'
                loop_content += '<option value="' + user + '" selected>' + user + '</option>'
                loop_content += '</select>'

                loop_content += '<select name="permission_allowed_user_page" id="permission_allowed_user_page" hidden>'
                loop_content += '<option value="' + permission + '" selected>' + permission + '</option>'
                loop_content += '</select>'

                loop_content += '<select name="permission_allowed_user_allowed" id="permission_allowed_user_allowed" hidden>'
                loop_content += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                loop_content += '</select>'
                if allow_edit:
                    loop_content += '<center>'
                    loop_content += '<input type="submit" value="' + the_string + '">'
                    loop_content += '</center>'
                loop_content += '</form>'
                loop_content += '</td>'

                user_line_body += loop_content

            user_line_body += '<td>'
            user_line_body += '</td>'

            for siteid in view_details['all_siteid']:
                loop_content = ''
                per_value = users_content[user]['manage'][siteid]['per_value']

                if per_value:
                    the_string = "Yes"
                    next_Value = "No"
                    color = "(0, 255, 0, 0.3)"
                else:
                    the_string = "No"
                    next_Value = "Yes"
                    color = "(255, 0, 0, 0.3)"

                loop_content += '<td title="' + user + '" style="background-color:rgba' + color + '">'
                loop_content += '<form method="post" action="">'
                loop_content += '<select name="site_allowed_user_user" id="site_allowed_user_user" hidden>'
                loop_content += '<option value="' + user + '" selected>' + user + '</option>'
                loop_content += '</select>'

                loop_content += '<select name="site_allowed_user_site" id="site_allowed_user_site" hidden>'
                loop_content += '<option value="' + siteid + '" selected>' + siteid + '</option>'
                loop_content += '</select>'

                loop_content += '<select name="site_allowed_user_allowed" id="site_allowed_user_allowed" hidden>'
                loop_content += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                loop_content += '</select>'
                if allow_edit:
                    loop_content += '<center>'
                    loop_content += '<input type="submit" value="' + the_string + '">'
                    loop_content += '</center>'
                loop_content += '</form>'
                loop_content += '</td>'

                user_line_body += loop_content

            body += user_line_body
            user_line_body = None

            body += '</tr>'


        body += '</table>'
        body += '</center>'

        # ---------------------------
        # ---------------------------

        body += '<br><br>'

        if view_details['index_for_user']:
            body += '<br>------------------------------------------------------------<br>'
            body += '<br><br>what the index page will look like:<br><br>'
            body += view_details['index_for_user']

            body += '<br>------------------------------------------------------------<br>'
            body += 'Groups:' + '<br><br>'
            body += view_details['groups_for_user']


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))



    body += '</div>'


    return body, other


# ====================================
def make_body_POST(environ):
    # ====================================
    body = ""
    error_body = ""
    action_report = ''

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except ValueError:
        request_body_size = 0

    request_body = environ['wsgi.input'].read(request_body_size)

    d = parse_qs(request_body.decode('utf-8'))

    # body += json.dumps(d)
    # {"permission_allowed_user_user": ["david.ferguson"], "permission_allowed_user_page": ["datastore"], "permission_allowed_user_allowed": ["No"]}

    if 'site_allowed_user_site' in d:
        permissions.set_site_allowed(d['site_allowed_user_user'][0],
                                     d['site_allowed_user_site'][0],
                                     d['site_allowed_user_allowed'][0])
        if action_report:
            action_report += '\n'
        action_report += 'setting ' + 'site_allowed_user_site' + ' with ' + '"' + \
                d['site_allowed_user_user'][0] + '", "' + \
                d['site_allowed_user_site'][0] + '", "' + \
                d['site_allowed_user_allowed'][0] + '"'


    if 'permission_allowed_user_page' in d:
        permissions.set_permission_allowed(d['permission_allowed_user_user'][0],
                                           d['permission_allowed_user_page'][0],
                                           d['permission_allowed_user_allowed'][0])
        if action_report:
            action_report += '\n'
        action_report += 'setting ' + 'permission_allowed_user_page' + ' with ' + '"' + \
                d['permission_allowed_user_user'][0] + '", "' + \
                d['permission_allowed_user_page'][0] + '", "' + \
                d['permission_allowed_user_allowed'][0] + '"'

    # then return what GET would have done
    body, other = make_body_GET(environ)
    other['action_report'] = action_report
    return body, other


# ====================================
def make_body_GET(environ):
    # ====================================
    return build_current_view(environ)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, 'users_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            pass
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_user_sort(self):
        list_names = ['david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

        list_names = ['david.ferguson01', 'david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'david.ferguson01', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

        list_names = ['David.ferguson01', 'david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'David.ferguson01', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

        list_names = ['fred.ferguson', 'david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'fred.ferguson', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

        list_names = ['Fred.ferguson', 'david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'Fred.ferguson', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

        list_names = ['Fred.Ferguson', 'david.ferguson', 'brandon.jones01']
        expected = ['david.ferguson', 'Fred.Ferguson', 'brandon.jones01']
        actual = user_sort(list_names)
        self.assertEqual(expected, actual)

    def test_make_current_view_details(self):
        list_users = {'david.ferguson':{'last_valid':'2024.09.20', 'admin':''}}
        environ = {'QUERY_STRING':'', 'REQUEST_URI':'', 'REQUEST_SCHEME':'http','HTTP_HOST':'lpec5009slicr05.cardinalhealth.net'}
        url_to_use = make_home_url_from_environ(environ)
        REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
        query_items = {}

        view_details = make_current_view_details(url_to_use, REQUEST_URI, query_items,True,{},[],list_users,[])

        expected = 'http://lpec5009slicr05.cardinalhealth.net'
        actual = view_details['url_to_use']
        self.assertEqual(expected, actual)

        expected = '2024.09.20'
        actual = view_details['users_content']['david.ferguson']['lastvalidlogin']
        self.assertEqual(expected, actual)

        expected = '2024.09.20'
        actual = view_details['url_to_use']
#        self.assertEqual(expected, actual)



    def test_build_current_view_details(self):
        allow_edit = True
        data_store_content = {}

        list_users = {'david.ferguson':{'last_valid':''}}
        environ = {'QUERY_STRING':'', 'REQUEST_URI':'', 'REQUEST_SCHEME':'http','HTTP_HOST':'lpec5009slicr05.cardinalhealth.net'}
        url_to_use = make_home_url_from_environ(environ)
        REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
        query_items = {}

        # run this call, just to see that it does not throw exception
        body = build_current_view_details(allow_edit, url_to_use, REQUEST_URI, query_items, data_store_content)

#        expected = 'http://lpec5009slicr05.cardinalhealth.net'
#        actual = view_details['url_to_use']
#        self.assertEqual(expected, actual)

    def test_make_users_from_datastore(self):
        data_store_content = {'user_junk_david.ferguson_trash':'does not matter'}
        expected = ['david.ferguson']
        actual = make_users_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'user_junk_david.ferguson_trash':'does not matter',
                              'user_junk2_david.ferguson_trash2':'does not matter'}
        expected = ['david.ferguson']
        actual = make_users_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'user_junk_david2.ferguson_trash':'does not matter',
                              'user_junk2_david.ferguson_trash2':'does not matter',
                              'user_junk3_david.ferguson_trash3':'does not matter'}
        expected = ['david.ferguson', 'david2.ferguson']
        actual = make_users_from_datastore(data_store_content)
        self.assertEqual(expected, actual)











# end of file
