# A dsu for slicer page services

service = "dsu"
version = service + '.0.1'

_ = """
Additions:

- a check mark, to show that that person has been called on (or any other visual cue)


--fails- Put the url for each person filter into the knowledge, and then when a name is clicked on,
    change the url for an embedded iframe to be that link to the Jira board, filtered for their name
    - iframe it:
        https://www.tutorialrepublic.com/html-tutorial/html-iframes.php
        https://jira.cardinalhealth.com/secure/RapidBoard.jspa?rapidView=2302


- Show who is facilitating (the current person, and then order down from there)

- Could also add a checklist of topics to cover

- Include section for Sprint planning notes
- Section for Sprint review, and retro
- glossary?


"""

_ = """
This file gets loaded to:
/var/www/html/dsu.py

using:
sudo vi /var/www/html/dsu.py

It also requires:

sudo vi /etc/httpd/conf.d/python-dsu.conf
----- start copy -----
WSGIScriptAlias /dsu /var/www/html/dsu.py
----- end copy -----

sudo chown apache:apache /var/www/html/dsu.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/dsu-runner
sudo chmod +x /var/www/html/dsu-runner

# ===== begin: start file
#!/usr/bin/env python
import dsu
dsu.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/dsu-runner.service
sudo systemctl daemon-reload
sudo systemctl stop dsu-runner.service
sudo systemctl start dsu-runner.service
sudo systemctl enable dsu-runner.service

systemctl status dsu-runner.service

sudo systemctl restart dsu-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep dsu-runner

OR

tail -f /var/log/syslog | fgrep dsu-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/dsu-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import dsu; print(dsu.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/dsu

https://slicer.cardinalhealth.net/dsu?siteid=PR005

https://slicer.cardinalhealth.net/dsu?serial=100000002a5da842

https://slicer.cardinalhealth.net/dsu?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_dsu


"""

import copy
import traceback
import json
import os
import random
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

s_pre_names = ['Monday: Time Sheets', 'Monday: Test On-Call', 'No Estimate', 'To Be Picked Up']
s_CLSY = ['Ben', 'Brian', 'Dave E.', 'Dave F.', 'Donna', 'Jeff', 'Marco', 'Mark', 'Matt', 'Steve']
s_post_names = ['Review oldest EUDER', 'Alyx', 'Aaron', 'Review the cards for points.', 'Any squad meetings today?']


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_shuffled_names(the_names):
    # ----------------------------
    copy_of_names = list(the_names)
    random.shuffle(copy_of_names)

    return copy_of_names


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(dsu status)'

    status = os.system('systemctl is-active --quiet dsu-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "dsu-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content

    # then return what GET would have done
    return make_body_GET(environ), other


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    shuffled_CLSY = get_shuffled_names(s_CLSY)

    shuffled = []
    for item in s_pre_names:
        shuffled.append(item)

    for item in shuffled_CLSY:
        shuffled.append(item)

    for item in s_post_names:
        shuffled.append(item)

    try:
        body += '<center>'
        body += '<br>'
        body += '<table border="1" cellpadding="5">'

        body += '<tr>'
        body += '<td>'
        body += '<B>Callout Order</B>'
        body += '</td>'
        body += '</tr>'

        for index in range(0, len(shuffled)):
            body += '<tr>'
            body += '<td>'
            body += shuffled[index]
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        # body += '<iframe src="https://jira.cardinalhealth.com/secure/RapidBoard.jspa?rapidView=2302" style="border: none;"></iframe>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    if True:
        # if permissions.permission_prefix_allowed(environ, 'dsu_') or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                return make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                return make_body_GET(environ)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n' \
                    '<body>\n'
        html += body

        if other['add_wrapper']:
            html += '</body>\n' \
                    '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


#    return [bytes(html, encoding= 'utf-8')]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_dsu(self):
        """
        (fill in here)
        """
        all_names = ['Dave F.']
        expected = ['Dave F.']
        actual = get_shuffled_names(all_names)
        self.assertEqual(expected, actual)

# End of source file
