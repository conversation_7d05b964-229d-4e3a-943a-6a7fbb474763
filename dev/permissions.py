# Local development permissions module
# Provides simplified permission checking for local development

from . import datastore
from . import login

def permission_allowed(environ, page_name):
    """Check if current user has permission for a page"""
    user = login.get_current_user(environ)
    return permission_allowed_user(user, page_name)

def permission_allowed_user(user, page_name, data_store_content=None, allow_overrides=True):
    """Check if a specific user has permission for a page"""
    if not user:
        return False

    # Get user settings
    try:
        from . import local_dev_settings as settings
        user_list = settings.get().get('user_list', {})
        trust_list = settings.get().get('trust_list', [])
    except:
        user_list = {}
        trust_list = ['developer', 'admin']

    # Check if user is in trust list (always has permissions)
    if user in trust_list:
        return True

    # Check if user is admin
    admin_key = f'user_login_{user}_admin'
    is_admin = datastore.get_value(admin_key, 'No') == 'Yes'

    if is_admin:
        return True

    # Check user-specific permissions from settings
    if user in user_list:
        user_permissions = user_list[user].get('permissions', [])
        for perm in user_permissions:
            if page_name.startswith(perm):
                return True

    # Check datastore permissions
    permission_key = f'permissions_{user}_{page_name}'
    permission_value = datastore.get_value(permission_key, 'deny')

    return permission_value == 'allow'

def permission_prefix_allowed(environ, prefix_name):
    """Check if current user has permission for a prefix"""
    user = login.get_current_user(environ)
    return permission_prefix_allowed_user(user, prefix_name)

def permission_prefix_allowed_user(user, prefix_name):
    """Check if a specific user has permission for a prefix"""
    if not user:
        return False

    # Get user settings
    try:
        from . import local_dev_settings as settings
        user_list = settings.get().get('user_list', {})
        trust_list = settings.get().get('trust_list', [])
    except:
        user_list = {}
        trust_list = ['developer', 'admin']

    # Check if user is in trust list (always has permissions)
    if user in trust_list:
        return True

    # Check if user is admin
    admin_key = f'user_login_{user}_admin'
    is_admin = datastore.get_value(admin_key, 'No') == 'Yes'

    if is_admin:
        return True

    # Check user-specific permissions from settings
    if user in user_list:
        user_permissions = user_list[user].get('permissions', [])
        for perm in user_permissions:
            if prefix_name.startswith(perm) or perm.startswith(prefix_name):
                return True

    return False

def set_permission_allowed(user, page_name, value_to_set):
    """Set permission for a user and page"""
    permission_key = f'permissions_{user}_{page_name}'
    datastore.set_value(permission_key, value_to_set)

def set_permission_override(page_name, value_to_set):
    """Set permission override for a page"""
    override_key = f'override_permission_{page_name}'
    datastore.set_value(override_key, value_to_set)

def get_permission_override(page_name):
    """Get permission override for a page"""
    override_key = f'override_permission_{page_name}'
    return datastore.get_value(override_key, 'None')

def get_all_permissions():
    """Get all permission keys"""
    all_keys = datastore.get_all_keys()
    permission_keys = []

    for key in all_keys:
        if key.startswith('permissions_'):
            # Extract permission name from key like 'permissions_user_page_name'
            parts = key.split('_', 2)
            if len(parts) >= 3:
                permission_keys.append(parts[2])

    return permission_keys

def log_page_allowed(environ, service, other):
    """Log page access (simplified for development)"""
    user = login.get_current_user(environ)
    print(f"[PERMISSIONS] User '{user}' accessed service '{service}' - Status: {other.get('status', 'Unknown')}")

def get_modules_with_overrides(override_type='Login'):
    """Get modules with permission overrides"""
    # For development, return empty list
    return []

# Development helper functions
def grant_all_permissions(user):
    """Grant all permissions to a user (development helper)"""
    common_permissions = [
        'development_', 'admin_', 'user_', 'dashboard_', 'datastore_',
        'permissions_', 'login_', 'index_', 'organization_'
    ]

    for perm in common_permissions:
        set_permission_allowed(user, perm, 'allow')

    # Set admin status
    admin_key = f'user_login_{user}_admin'
    datastore.set_value(admin_key, 'Yes')

# Initialize default permissions for development users
def _init_default_permissions():
    """Initialize default permissions for development"""
    grant_all_permissions('developer')
    grant_all_permissions('admin')

    # Basic permissions for test_user
    set_permission_allowed('test_user', 'user_', 'allow')
    set_permission_allowed('test_user', 'dashboard_', 'allow')
    set_permission_allowed('test_user', 'datastore_', 'allow')

def make_permissions_cache_from_datastore(data_store_content):
    """
    Create permissions cache from datastore content
    This function parses the datastore and creates a structured permissions cache
    """
    return_value = {}

    for key_found in data_store_content.keys():
        splits = key_found.split('_')
        value_found = data_store_content[key_found]

        if len(splits) > 3:
            # Handle override permissions: override_permission_module_action
            if splits[0] == 'override':
                if splits[1] == 'permission':
                    module_found = splits[2]
                    permission_found = splits[3]

                    if value_found != 'None':
                        if not 'overrides' in return_value:
                            return_value['overrides'] = {}
                        if not module_found in return_value['overrides']:
                            return_value['overrides'][module_found] = {}
                        return_value['overrides'][module_found][permission_found] = value_found

            # Handle user permissions: user_permission_username_module_action
            elif splits[0] == 'user':
                if splits[1] == 'permission':
                    user_found = splits[2]
                    module_found = splits[3]

                    if len(splits) > 4:
                        permission_found = splits[4]

                        if value_found == 'Yes':
                            if not 'users' in return_value:
                                return_value['users'] = {}
                            if not user_found in return_value['users']:
                                return_value['users'][user_found] = {}
                            if not 'modules' in return_value['users'][user_found]:
                                return_value['users'][user_found]['modules'] = {}
                            if not module_found in return_value['users'][user_found]['modules']:
                                return_value['users'][user_found]['modules'][module_found] = []

                            if permission_found not in return_value['users'][user_found]['modules'][module_found]:
                                return_value['users'][user_found]['modules'][module_found].append(permission_found)

                # Handle user sites: user_site_username_sitename
                elif splits[1] == 'site':
                    user_found = splits[2]
                    site_found = splits[3]

                    if value_found == 'Yes':
                        if not 'users' in return_value:
                            return_value['users'] = {}
                        if not user_found in return_value['users']:
                            return_value['users'][user_found] = {}
                        if not 'sites' in return_value['users'][user_found]:
                            return_value['users'][user_found]['sites'] = []

                        if site_found not in return_value['users'][user_found]['sites']:
                            return_value['users'][user_found]['sites'].append(site_found)

                # Handle user login attributes: user_login_username_attribute
                elif splits[1] == 'login':
                    user_found = splits[2]
                    attribute_found = splits[3]

                    if attribute_found == 'admin':
                        if value_found == 'Yes':
                            if not 'users' in return_value:
                                return_value['users'] = {}
                            if not user_found in return_value['users']:
                                return_value['users'][user_found] = {}
                            if not 'login' in return_value['users'][user_found]:
                                return_value['users'][user_found]['login'] = {}

                            return_value['users'][user_found]['login']['admin'] = True

                            # Admin users get default permissions
                            if not 'modules' in return_value['users'][user_found]:
                                return_value['users'][user_found]['modules'] = {}
                            return_value['users'][user_found]['modules']['datastore'] = ['edit']
                            return_value['users'][user_found]['modules']['users'] = ['edit']

                    elif attribute_found == 'details':
                        # Store user group details
                        if not 'users_groups' in return_value:
                            return_value['users_groups'] = {}
                        if not user_found in return_value['users_groups']:
                            return_value['users_groups'][user_found] = {}
                        return_value['users_groups'][user_found] = value_found.split(',')

    return return_value

def make_module_permissions_for_user_from_permissions_cache(the_user, permissions_cache, do_not_use=[], modules_found_file_content=''):
    """
    Create module permissions for a specific user from the permissions cache
    """
    return_value = {
        'to_show_above_login': [],
        'to_show_for_login': [],
        'to_show_below_login': [],
        'current_user': the_user if the_user else ''
    }

    # Check if user has explicit permissions set
    is_permissions_explicit_set = False
    if 'users' in permissions_cache:
        if the_user in permissions_cache['users']:
            if 'modules' in permissions_cache['users'][the_user]:
                if 'permissions' in permissions_cache['users'][the_user]['modules']:
                    if 'explicit' in permissions_cache['users'][the_user]['modules']['permissions']:
                        is_permissions_explicit_set = True

    # Public modules (shown above login - visible to everyone including non-logged-in users)
    return_value['to_show_above_login'] = [
        'dashboard', 'reports', 'timezones', 'videos'
    ]

    # Login modules (shown for login)
    return_value['to_show_for_login'] = ['login']

    # User-specific modules (shown below login when logged in)
    if the_user:
        if the_user in ['developer', 'admin']:
            return_value['to_show_below_login'] = [
                'datastore', 'permissions', 'users', 'organization',
                'management', 'debug', 'loader', 'tasks', 'sites',
                'profiles', 'certificates', 'monitor', 'upload', 'download',
                'datadrop', 'datamine', 'investigate', 'multimedia',
                'checkin', 'scan', 'documentation', 'chart', 'view',
                'codeupload', 'watchdog', 'intake', 'rings', 'dsu',
                'devicecommand', 'dataport', 'template', 'thirdparty',
                'address2location', 'jamf', 'ldapsupport', 'speedtest',
                'deviceupload', 'htmlfiles', 'certificates'
            ]
        elif the_user == 'test_user':
            return_value['to_show_below_login'] = [
                'datastore', 'monitor'
            ]
        else:
            # Default user permissions
            return_value['to_show_below_login'] = [
                'datastore'
            ]

    return return_value

def get_module_permissions_for_environ(environ):
    """
    Get module permissions for the current environment/user
    """
    user = login.get_current_user(environ)
    data_store_content = datastore.all_datastore()
    permissions_cache = make_permissions_cache_from_datastore(data_store_content)

    # Always return permissions structure, even for non-logged-in users
    # This ensures public modules are always visible
    return make_module_permissions_for_user_from_permissions_cache(user, permissions_cache)

# Initialize on import
_init_default_permissions()
