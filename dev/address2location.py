#!/usr/bin/env python3
"""
Mock address2location module for development environment
This provides stub functions to prevent import errors in dashboard
"""

def get_location_from_address(address):
    """Mock function to get location from address"""
    return {
        'latitude': 0.0,
        'longitude': 0.0,
        'address': address,
        'status': 'mock_data'
    }

def get_address_from_location(latitude, longitude):
    """Mock function to get address from coordinates"""
    return {
        'address': f'Mock Address at {latitude}, {longitude}',
        'latitude': latitude,
        'longitude': longitude,
        'status': 'mock_data'
    }

# Mock any other functions that might be expected
def initialize():
    """Mock initialization function"""
    pass

def cleanup():
    """Mock cleanup function"""
    pass
