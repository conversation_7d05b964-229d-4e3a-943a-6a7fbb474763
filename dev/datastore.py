# Local development datastore module
# Provides a simple file-based datastore for local development

import os
import json
import datetime
import time

# Local development datastore
_datastore = {}
_datastore_file = None

def _init_datastore():
    """Initialize the local datastore"""
    global _datastore_file
    if _datastore_file is None:
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Go up from dev/ to root
        dev_data_dir = os.path.join(current_dir, 'dev', 'dev_data')
        os.makedirs(dev_data_dir, exist_ok=True)
        _datastore_file = os.path.join(dev_data_dir, 'local_datastore.json')

        # Load existing datastore if it exists
        if os.path.exists(_datastore_file):
            try:
                with open(_datastore_file, 'r') as f:
                    _datastore.update(json.load(f))
            except:
                pass

        # Set some default values for development
        _datastore.update({
            'trust_status': 'trusted',
            'service_loader_index_allow_runner': 'yes',
            'service_loader_login_allow_runner': 'yes',
            'service_loader_datastore_allow_runner': 'yes',
            'service_loader_permissions_allow_runner': 'yes',
            'user_login_developer_admin': 'Yes',
            'user_login_admin_admin': 'Yes',
            'user_login_test_user_admin': 'No',
            'permissions_developer_development_': 'allow',
            'permissions_developer_admin_': 'allow',
            'permissions_developer_user_': 'allow',
            'permissions_admin_admin_': 'allow',
            'permissions_admin_user_': 'allow',
            'permissions_test_user_user_': 'allow',
        })

def _save_datastore():
    """Save the datastore to file"""
    try:
        with open(_datastore_file, 'w') as f:
            json.dump(_datastore, f, indent=2)
    except:
        pass

def trust():
    """Always return True for local development"""
    return True

def set_value(the_key, the_value, who='(local_dev)'):
    """Set a value in the datastore"""
    _init_datastore()
    _datastore[the_key] = str(the_value)
    _save_datastore()

    # Log the operation for debugging
    print(f"[DATASTORE] set_value({the_key}, {the_value}, who={who})")

def get_value(the_key, default_if_not_exist=''):
    """Get a value from the datastore"""
    _init_datastore()
    result = _datastore.get(the_key, default_if_not_exist)
    print(f"[DATASTORE] get_value({the_key}) -> {result}")
    return result

def get_value_stored(data_store_content, the_key, default_if_not_exist=''):
    """Get a value from provided datastore content"""
    return data_store_content.get(the_key, default_if_not_exist)

def get_value_with_trust(the_key, default_if_not_exist=''):
    """Get a value with trust check (always trusted in dev)"""
    return get_value(the_key, default_if_not_exist)

def all_datastore():
    """Return all datastore content"""
    _init_datastore()
    return _datastore.copy()

def get_all_keys():
    """Get all keys in the datastore"""
    _init_datastore()
    return list(_datastore.keys())

def delete_key(the_key):
    """Delete a key from the datastore"""
    _init_datastore()
    if the_key in _datastore:
        del _datastore[the_key]
        _save_datastore()
        print(f"[DATASTORE] deleted key: {the_key}")

def clear_datastore():
    """Clear all data from the datastore"""
    global _datastore
    _datastore = {}
    _save_datastore()
    print("[DATASTORE] cleared all data")

# Initialize on import
_init_datastore()
