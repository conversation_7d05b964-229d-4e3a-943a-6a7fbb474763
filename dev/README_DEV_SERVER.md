# Slicer Local Development Server

This local development server allows you to run and test all the slicer_wsgi_*.py modules on your local machine for frontend redesign work.

## Features

- ✅ **Serves all 45 slicer_wsgi_*.py modules locally**
- ✅ **Complete dashboard functionality with JSON data endpoints**
- ✅ **Clean, maintainable development environment**
- ✅ **Consolidated dev_bridge system for all dependencies**
- ✅ Preserves existing functionality and routing
- ✅ Handles authentication and permissions
- ✅ Provides session management
- ✅ Creates local file structure for development
- ✅ Easy start/stop functionality
- ✅ No modifications to existing files

## Quick Start

### Option 1: Using the Manager Script (Recommended)

1. **Test the setup:**
   ```bash
   python3 dev_server_manager.py test
   ```

2. **Start the server:**
   ```bash
   python3 dev_server_manager.py start
   ```

3. **Access the application:**
   - Open your browser to: <http://localhost:8000>
   - Login with: `developer` / `dev123`
   - **Dashboard is fully functional at: <http://localhost:8000/dashboard>**

### Option 2: Direct Start

1. **Start the server:**
   ```bash
   python3 start_dev_server.py
   ```

   Or directly:
   ```bash
   python3 local_dev_server.py
   ```

### Login and Use

1. **Login with development credentials:**
   - **developer** / **dev123** (admin user)
   - **admin** / **admin123** (admin user)
   - **test_user** / **test123** (regular user)

2. **Browse available modules:**
   - After login, you'll see a grid of all available slicer modules
   - Click on any module to access it (e.g., `/dashboard`, `/datastore`, `/permissions`)

## File Structure (Clean & Organized)

The development server creates these new files (no existing files are modified):

```text
cs_sp_pi_slicer/
├── local_dev_server.py          # Main WSGI development server
├── dev_server_manager.py        # Server management script
├── dev_bridge.py                # Single bridge for all dev module dependencies
└── dev/                         # All development modules and data
    ├── comprehensive_test.py    # Single test file for all dev testing
    ├── login.py                 # Development login system
    ├── datastore.py             # Development datastore
    ├── permissions.py           # Development permissions
    ├── organization.py          # Complete service configurations
    ├── address2location.py      # Development location services
    ├── reports.py               # Development reporting
    ├── dev_data/                # Mock data files
    │   ├── local_datastore.json # Local datastore file
    │   ├── local_sessions.json  # Session storage
    │   └── log/slicer/          # Dashboard data files
    │       └── dashboard_stash_report # Mock dashboard data
    └── dev_summary/             # All documentation and summaries
        ├── DASHBOARD_COMPLETE_FIX.md
        ├── DASHBOARD_FIX_SUMMARY.md
        └── *.md                 # Other summary files
```

### Key Improvements

- **Clean Root Directory**: No scattered dev files in root
- **Single Bridge**: `dev_bridge.py` handles all WSGI module dependencies
- **Single Test File**: `dev/comprehensive_test.py` for all testing
- **Complete Dashboard**: Fully functional with JSON data endpoints
- **Organized Documentation**: All summaries in `dev_summary/`

## Dashboard Functionality

The dashboard is now **fully functional** with complete JSON data endpoints:

### Dashboard Features

- ✅ **Main Dashboard Page**: <http://localhost:8000/dashboard>
- ✅ **JSON Data Endpoint**: <http://localhost:8000/dashboard?get=data&type=issues>
- ✅ **Mock Data**: Sample site statuses and issue counts
- ✅ **Environment Variables**: All required WSGI variables provided
- ✅ **Module Dependencies**: All required modules (permissions, address2location, reports) available
- ✅ **Error-Free**: No more tracebacks or exceptions

### Dashboard Data

The dashboard uses mock data from `dev/dev_data/log/slicer/dashboard_stash_report`:

```json
{
  "headers": ["Site ID", "Status", "Issues", "Last Updated", "Location"],
  "rows": [
    ["DEV-001", "Online", "0", "2025-01-20 12:00:00", "Development Lab"],
    ["DEV-002", "Online", "1", "2025-01-20 11:45:00", "Test Environment"],
    ["DEV-003", "Warning", "2", "2025-01-20 11:30:00", "Staging Server"],
    ["DEV-004", "Offline", "5", "2025-01-20 10:15:00", "Legacy System"]
  ],
  "color": ["green", "yellow", "orange", "red"]
}
```

## Testing

Run the comprehensive test suite:

```bash
python3 dev/comprehensive_test.py
```

This tests:

- Server startup (45 WSGI modules loaded)
- Environment variable handling
- Navigation structure for different user types
- Common page loading (dashboard, permissions, datastore, etc.)

## Troubleshooting

If you encounter the error: `SyntaxError: Unexpected token '<', "<html>"... is not valid JSON`

This means the dashboard data endpoint returned HTML instead of JSON. This is now fixed with:

1. **Mock data file**: Dashboard data is pre-populated
2. **Environment variables**: All required WSGI variables provided
3. **Module dependencies**: All required modules available via `dev_bridge.py`

## How It Works

### WSGI Dispatcher
The `SlicerWSGIDispatcher` class:
- Automatically discovers all `slicer_wsgi_*.py` files
- Routes requests like `/dashboard` to `slicer_wsgi_dashboard.py`
- Handles authentication and session management
- Provides error handling and debugging

### Modular Architecture
The development server uses a clean modular architecture:

**Bridge Modules (Root Directory):**
- **settings.py**: Bridge to `dev/local_dev_settings.py`
- **organization.py**: Bridge to `dev/organization.py`
- **permissions.py**: Bridge to `dev/permissions.py`
- **datastore.py**: Bridge to `dev/datastore.py`
- **login.py**: Bridge to `dev/login.py`

These bridge modules allow the existing `slicer_wsgi_*.py` files to import modules exactly as they do in production, without any modifications.

**Development Support Modules (dev/ Directory):**
- **dev/organization.py**: Provides `get_config()` and session management
- **dev/permissions.py**: Handles user permissions and access control (includes `make_permissions_cache_from_datastore()`)
- **dev/datastore.py**: Simple file-based key-value storage
- **dev/login.py**: Session-based authentication system
- **dev/local_dev_settings.py**: Development-specific configuration

### Settings System
- **dev/local_dev_settings.py**: Provides development-specific configuration
- Creates local directory structure in `dev/dev_data/`
- Uses simplified authentication (no LDAP required)
- Accessible via the bridge `settings.py` module

## Server Management Commands

The `dev_server_manager.py` script provides easy server management:

```bash
# Test the setup
python3 dev_server_manager.py test

# Start the server
python3 dev_server_manager.py start

# Stop the server
python3 dev_server_manager.py stop

# Restart the server
python3 dev_server_manager.py restart

# Check server status
python3 dev_server_manager.py status

# Clean development data
python3 dev_server_manager.py clean

# Show help
python3 dev_server_manager.py help
```

## Available Routes

Once the server is running, you can access:

- **/** - Home page with module listing
- **/login** - Login page
- **/logout** - Logout and clear session
- **/dashboard** - Dashboard module
- **/datastore** - Datastore management
- **/permissions** - Permission management
- **/index** - Index module
- **/{module_name}** - Any other slicer_wsgi module

## Development Users

The system comes with three pre-configured users:

| Username | Password | Role | Permissions |
|----------|----------|------|-------------|
| developer | dev123 | Admin | Full access to all modules |
| admin | admin123 | Admin | Full access to all modules |
| test_user | test123 | User | Limited access for testing |

## Configuration

### Changing Port or Host
Edit `local_dev_server.py` and modify:
```python
host = 'localhost'  # Change to '0.0.0.0' for external access
port = 8000         # Change to desired port
```

### Adding Users
Edit `local_dev_settings.py` and add to the `user_list`:
```python
'new_user': {
    'password': 'password123',
    'admin': False,
    'permissions': ['user_', 'dashboard_']
}
```

### Modifying Settings
All development settings are in `local_dev_settings.py`. You can modify:
- File paths
- Authentication settings
- User permissions
- Site title and URLs

## Troubleshooting

### Server Won't Start
- Ensure you're in the correct directory (cs_sp_pi_slicer)
- Check that Python 3 is installed
- Verify all required files are present

### Module Loading Errors
- Check the console output for specific error messages
- Some modules may have dependencies that need to be installed
- Missing modules will be skipped automatically

### Permission Issues
- Use the 'developer' account for full access
- Check the permissions system in `/permissions`
- Grant permissions using the datastore interface

### Port Already in Use
- Change the port in `local_dev_server.py`
- Or kill the process using port 8000:
  ```bash
  lsof -ti:8000 | xargs kill -9
  ```

## Development Workflow

1. **Start the server** with `python start_dev_server.py`
2. **Login** with development credentials
3. **Navigate** to the module you want to work on
4. **Make changes** to the slicer_wsgi_*.py files
5. **Restart the server** to see changes (Ctrl+C then restart)
6. **Test** your changes in the browser

## Stopping the Server

Press `Ctrl+C` in the terminal where the server is running.

## Notes

- The server automatically creates necessary directories
- Session data persists between server restarts
- All data is stored locally in the `dev_data/` directory
- No production data or systems are affected
- The server runs in development mode with detailed error messages
