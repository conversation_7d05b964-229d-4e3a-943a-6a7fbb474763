# Local development organization module
# Provides configuration and session management for local development

import os
import time
import json
import platform

# Import local development settings
try:
    from . import local_dev_settings as settings
except ImportError:
    print("Warning: Could not import local_dev_settings")
    settings = None

def get_config(service):
    """Get configuration for a specific service"""
    if not settings:
        return {}

    all_settings = settings.get()

    # Base configuration for all services
    return_value = {
        'home_url': all_settings.get('home_url', 'http://localhost:8000'),
        'sites_to_drop': all_settings.get('sites_to_drop', {}),
        'site_title': all_settings.get('site_title', 'Slicer Local Development'),
        'build': 'local_dev',
        'apache_user_name': 'developer',
        'trust_list': all_settings.get('trust_list', ['developer']),
    }

    # Service-specific configurations
    data_drop_base = all_settings.get('data_drop_base', './dev_data/www/slicer/log/slicer/')
    datastore_save_path = all_settings.get('datastore_save_path', './dev_data/log/slicer/')
    ram_disk_path = all_settings.get('ram_disk_path', './dev_data/shm/')

    # Ensure directories exist
    os.makedirs(data_drop_base, exist_ok=True)
    os.makedirs(datastore_save_path, exist_ok=True)
    os.makedirs(ram_disk_path, exist_ok=True)

    if service == 'permissions':
        return_value.update({
            'trust_list': all_settings.get('trust_list', ['developer']),
            'base_raw_path': data_drop_base + 'permissions/raw/',
        })

    elif service == 'login':
        return_value.update({
            'login_authentication': all_settings.get('login_authentication', {}),
            'base_log_path': './dev_data/www/slicer/login/',
            'time_to_remain_valid': 2 * 60 * 60,  # 2 hours
            'user_list': all_settings.get('user_list', {}),
        })

    elif service == 'datastore':
        return_value.update({
            'datadrop_save_path': data_drop_base + 'datadrop/json/id/',
            'base_datastore_path': datastore_save_path + 'datastore/',
            'base_datastorelog_path': datastore_save_path + 'datastorelog/',
            'base_datastorelog_cache_file': datastore_save_path + 'datastorelog_cache.json',
            'do_trust_path': datastore_save_path + 'do_trust',
            'time_of_last_tasks_run_trust_path': datastore_save_path + 'tasks_run_trust',
            'datastore_first_trust_diff_path': datastore_save_path + 'datastore_first_trust_diff',
        })

    elif service == 'datadrop':
        return_value.update({
            'checkin_file_root': data_drop_base + 'checkin/json/id/',
            'datadrop_save_path': data_drop_base + 'datadrop/json/id/',
            'datadrop_raw_root': data_drop_base + 'datadrop/raw/',
            'network_utilization_save_path': data_drop_base + 'network/id/(id)/',
            'statistics_save_path': data_drop_base + 'datadrop/stats/id/',
            'base_upload_path': data_drop_base + 'upload/files/',
            'days_to_keep': all_settings.get('days_to_keep', 4),
        })

    elif service == 'dashboard':
        return_value.update({
            'days_to_keep_dashboard_data': all_settings.get('days_to_keep_dashboard_data', 120),
            's_stash_report_file': datastore_save_path + 'dashboard_stash_report',
            'dashboard_raw_root': data_drop_base + 'dashboard/raw/',
            'sites_to_drop': all_settings.get('sites_to_drop', {}),
        })

    elif service == 'timezones':
        return_value.update({
            's_file_for_timezone_details': ram_disk_path + 'timezone_details.txt',
            'timezone_cache_path': datastore_save_path + 'timezone_cache/',
        })

    elif service == 'reports':
        return_value.update({
            'reports_cache_path': datastore_save_path + 'reports_cache/',
            'reports_data_path': data_drop_base + 'reports/data/',
        })

    # Add common paths for all services
    return_value.update({
        'time_of_last_tasks_run_trust_path': datastore_save_path + 'tasks_run_trust',
        'tasks_request_path': datastore_save_path + 'tasks/',
    })

    return return_value

def make_all_dirs(service_config):
    """Create all directories specified in service config"""
    for key, value in service_config.items():
        if key.endswith('_path') and isinstance(value, str):
            if value.endswith('/'):
                # It's a directory path
                os.makedirs(value, exist_ok=True)
            else:
                # It's a file path, create parent directory
                parent_dir = os.path.dirname(value)
                if parent_dir:
                    os.makedirs(parent_dir, exist_ok=True)

def wrap_page_with_session(environ, html):
    """Wrap page with session management and navigation"""
    from . import login

    user = login.get_current_user(environ)

    # Simple navigation header
    nav_html = f'''
    <div style="background: #007cba; color: white; padding: 10px; margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <strong>Slicer Local Development</strong>
                <span style="margin-left: 20px;">
                    <a href="/" style="color: white; text-decoration: none;">Home</a> |
                    <a href="/dashboard" style="color: white; text-decoration: none;">Dashboard</a> |
                    <a href="/datastore" style="color: white; text-decoration: none;">Datastore</a> |
                    <a href="/permissions" style="color: white; text-decoration: none;">Permissions</a>
                </span>
            </div>
            <div>
                {f"User: {user} | <a href='/logout' style='color: white;'>Logout</a>" if user else "<a href='/login' style='color: white;'>Login</a>"}
            </div>
        </div>
    </div>
    '''

    # Wrap the HTML
    if '<html>' in html.lower():
        # Insert navigation after <body> tag
        html = html.replace('<body>', f'<body>{nav_html}', 1)
    else:
        # Add full HTML structure
        html = f'''
        <html>
        <head>
            <title>Slicer Local Development</title>
            <meta charset="utf-8">
        </head>
        <body>
            {nav_html}
            {html}
        </body>
        </html>
        '''

    return html

def get_apache_user_name():
    """Get apache user name (simplified for development)"""
    return 'developer'

def get_current_linked_folder():
    """Get current linked folder (simplified for development)"""
    return 'local_dev'
