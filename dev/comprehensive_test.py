#!/usr/bin/env python3
"""
Comprehensive test suite for Slicer local development environment
This is the single test file for all dev-related testing
"""

import sys
import os

# Add parent directory to path to access local_dev_server
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_server_startup():
    """Test that the development server starts up correctly"""
    print("=== Testing Server Startup ===")

    try:
        import local_dev_server

        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()
        print(f"✅ Server loaded {len(dispatcher.wsgi_modules)} WSGI modules")

        return True

    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_navigation_structure():
    """Test that navigation works for different user types"""
    print("\n=== Testing Navigation Structure ===")

    try:
        import local_dev_server
        import login

        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()

        # Test scenarios
        test_cases = [
            {
                'name': 'Non-logged-in user',
                'user': None,
                'expected_public': ['dashboard', 'reports', 'timezones', 'videos'],
                'expected_private': []
            },
            {
                'name': 'Developer user',
                'user': 'developer',
                'expected_public': ['dashboard', 'reports', 'timezones', 'videos'],
                'expected_private': ['datastore', 'permissions', 'users']
            },
            {
                'name': 'Test user',
                'user': 'test_user',
                'expected_public': ['dashboard', 'reports', 'timezones', 'videos'],
                'expected_private': ['datastore']
            }
        ]

        passed = 0
        total = len(test_cases)

        for case in test_cases:
            print(f"\n--- Testing: {case['name']} ---")

            # Create environment
            environ = {
                'REQUEST_METHOD': 'GET',
                'PATH_INFO': '/',
                'REMOTE_ADDR': '127.0.0.1',
                'HTTP_COOKIE': '',
                'QUERY_STRING': '',
                'SERVER_NAME': 'localhost',
                'SERVER_PORT': '8000'
            }

            # Add session if user specified
            if case['user']:
                session_id = login.create_session(case['user'])
                environ['HTTP_COOKIE'] = f'slicer_session={session_id}'

            try:
                responses = []
                def start_response(status, headers):
                    responses.append((status, headers))

                result = dispatcher(environ, start_response)

                if responses and responses[0][0].startswith('200'):
                    body = b''.join(result).decode('utf-8')

                    # Check for expected public modules
                    public_found = all(module in body for module in case['expected_public'])
                    private_found = all(module in body for module in case['expected_private'])

                    if public_found and private_found:
                        print(f"✅ {case['name']} navigation correct")
                        passed += 1
                    else:
                        print(f"❌ {case['name']} navigation incorrect")
                        if not public_found:
                            print(f"   Missing public modules: {case['expected_public']}")
                        if not private_found:
                            print(f"   Missing private modules: {case['expected_private']}")
                else:
                    print(f"❌ {case['name']} failed to load")

            except Exception as e:
                print(f"❌ {case['name']} test failed: {e}")

        print(f"\nNavigation tests: {passed}/{total} passed")
        return passed == total

    except Exception as e:
        print(f"❌ Navigation test setup failed: {e}")
        return False

def test_common_pages():
    """Test that common pages load without errors"""
    print("\n=== Testing Common Pages ===")

    try:
        import local_dev_server
        import login

        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()

        # Create session for developer user
        session_id = login.create_session('developer')

        # Pages to test
        pages = [
            'index', 'dashboard', 'permissions', 'datastore',
            'reports', 'timezones', 'videos', 'users'
        ]

        passed = 0
        total = len(pages)

        for page in pages:
            print(f"\n--- Testing: {page} ---")

            environ = {
                'REQUEST_METHOD': 'GET',
                'PATH_INFO': f'/{page}',
                'REMOTE_ADDR': '127.0.0.1',
                'HTTP_COOKIE': f'slicer_session={session_id}',
                'QUERY_STRING': '',
                'SERVER_NAME': 'localhost',
                'SERVER_PORT': '8000'
            }

            try:
                responses = []
                def start_response(status, headers):
                    responses.append((status, headers))

                result = dispatcher(environ, start_response)

                if responses:
                    status, headers = responses[0]

                    if status.startswith('200'):
                        body = b''.join(result).decode('utf-8')

                        if 'Traceback' in body:
                            print(f"⚠️  {page} loads but contains errors")
                            # Extract error for debugging
                            if 'NameError' in body:
                                print(f"   NameError detected in {page}")
                            elif 'KeyError' in body:
                                print(f"   KeyError detected in {page}")
                        else:
                            print(f"✅ {page} loads successfully")
                            passed += 1
                    elif status.startswith('302'):
                        print(f"✅ {page} redirects (likely to login)")
                        passed += 1
                    else:
                        print(f"❌ {page} failed with status: {status}")
                else:
                    print(f"❌ {page} - no response")

            except Exception as e:
                print(f"❌ {page} test failed: {e}")

        print(f"\nPage tests: {passed}/{total} passed")
        return passed == total

    except Exception as e:
        print(f"❌ Page test setup failed: {e}")
        return False

def test_environment_variables():
    """Test that environment variables are properly handled"""
    print("\n=== Testing Environment Variables ===")

    try:
        import local_dev_server

        # Create dispatcher
        dispatcher = local_dev_server.SlicerWSGIDispatcher()

        # Test with minimal environment
        minimal_environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/dashboard',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000'
        }

        # The dispatcher should add missing variables
        dispatcher._add_missing_environ_vars(minimal_environ)

        # Check required variables
        required_vars = [
            'REQUEST_SCHEME', 'HTTP_HOST', 'wsgi.url_scheme',
            'SCRIPT_NAME', 'CONTENT_TYPE', 'CONTENT_LENGTH'
        ]

        missing = [var for var in required_vars if var not in minimal_environ]

        if not missing:
            print("✅ All required environment variables added")
            return True
        else:
            print(f"❌ Missing variables: {missing}")
            return False

    except Exception as e:
        print(f"❌ Environment variable test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Slicer Development Environment - Comprehensive Test Suite")
    print("=" * 60)

    tests = [
        test_server_startup,
        test_environment_variables,
        test_navigation_structure,
        test_common_pages
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")

    print("\n" + "=" * 60)
    print(f"Overall Results: {passed}/{total} test suites passed")

    if passed == total:
        print("🎉 All tests passed! Development environment is ready.")
    else:
        print("❌ Some tests failed. Check the output above for details.")
        print("\nCommon issues to check:")
        print("- Missing module imports")
        print("- Environment variable configuration")
        print("- Permission system setup")

    return 0 if passed == total else 1

if __name__ == '__main__':
    sys.exit(main())
