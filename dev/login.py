# Local development login module
# Provides simplified authentication for local development

import time
import os
import json
import hashlib
from urllib.parse import parse_qs

# Local session storage
_sessions = {}
_session_file = None

def _init_sessions():
    """Initialize session storage"""
    global _session_file
    if _session_file is None:
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Go up from dev/ to root
        dev_data_dir = os.path.join(current_dir, 'dev', 'dev_data')
        os.makedirs(dev_data_dir, exist_ok=True)
        _session_file = os.path.join(dev_data_dir, 'local_sessions.json')

        # Load existing sessions if they exist
        if os.path.exists(_session_file):
            try:
                with open(_session_file, 'r') as f:
                    _sessions.update(json.load(f))
            except:
                pass

def _save_sessions():
    """Save sessions to file"""
    try:
        with open(_session_file, 'w') as f:
            json.dump(_sessions, f, indent=2)
    except:
        pass

def get_cookie_contents_from_environ(environ):
    """Extract cookie contents from WSGI environ"""
    cookie_header = environ.get('HTTP_COOKIE', '')
    cookies = {}

    if cookie_header:
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                cookies[key] = value

    return cookies

def get_current_user(environ, refresh_timeout=False):
    """Get the current user from the session"""
    _init_sessions()

    # Check for session cookie
    cookies = get_cookie_contents_from_environ(environ)
    session_id = cookies.get('slicer_session', '')

    if session_id and session_id in _sessions:
        session = _sessions[session_id]

        # Check if session is still valid (2 hours timeout)
        if time.time() - session.get('last_access', 0) < 7200:
            # Update last access time
            session['last_access'] = time.time()
            _save_sessions()
            return session.get('user', '')
        else:
            # Session expired
            del _sessions[session_id]
            _save_sessions()

    return ''

def create_session(user):
    """Create a new session for a user"""
    _init_sessions()

    # Generate session ID
    session_id = hashlib.md5(f"{user}_{time.time()}".encode()).hexdigest()

    # Store session
    _sessions[session_id] = {
        'user': user,
        'created': time.time(),
        'last_access': time.time()
    }

    _save_sessions()
    return session_id

def validate_login(user, password):
    """Validate user login credentials"""
    # Import settings to get user list
    try:
        from . import local_dev_settings as settings
        user_list = settings.get().get('user_list', {})

        if user in user_list:
            if user_list[user].get('password') == password:
                return True
    except:
        pass

    # Default users for development
    default_users = {
        'developer': 'dev123',
        'admin': 'admin123',
        'test_user': 'test123'
    }

    return default_users.get(user) == password

def logout_user(session_id):
    """Logout a user by removing their session"""
    _init_sessions()
    if session_id in _sessions:
        del _sessions[session_id]
        _save_sessions()

def is_user_logged_in(environ):
    """Check if a user is logged in"""
    return get_current_user(environ) != ''

def get_login_form_html():
    """Return HTML for login form"""
    return '''
    <div style="max-width: 400px; margin: 100px auto; padding: 20px; border: 1px solid #ccc; border-radius: 5px;">
        <h2>Slicer Local Development Login</h2>
        <form method="post" action="/login">
            <div style="margin-bottom: 15px;">
                <label for="username">Username:</label><br>
                <input type="text" id="username" name="username" style="width: 100%; padding: 5px;" required>
            </div>
            <div style="margin-bottom: 15px;">
                <label for="password">Password:</label><br>
                <input type="password" id="password" name="password" style="width: 100%; padding: 5px;" required>
            </div>
            <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px;">Login</button>
        </form>
        <div style="margin-top: 20px; font-size: 12px; color: #666;">
            <strong>Development Users:</strong><br>
            developer / dev123 (admin)<br>
            admin / admin123 (admin)<br>
            test_user / test123 (user)
        </div>
    </div>
    '''

# Initialize on import
_init_sessions()
