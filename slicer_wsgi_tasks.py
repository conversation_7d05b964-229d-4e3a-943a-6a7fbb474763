# A tasks for slicer page services

service = "tasks"
version = service + ".0.5"

release_notes = """
0.5
2022.11.29
Add running_exception


"""

_ = """
This file gets loaded to:
/var/www/html/tasks.py

using:
sudo vi /var/www/html/tasks.py

sudo systemctl restart httpd

sudo systemctl restart tasks-runner.service


It also requires:

sudo vi /etc/httpd/conf.d/python-tasks.conf
----- start copy -----
WSGIScriptAlias /tasks /var/www/html/tasks.py
----- end copy -----

sudo chown apache:apache /var/www/html/tasks.py

sudo systemctl restart httpd

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/tasks-runner
sudo chmod +x /var/www/html/tasks-runner

# ===== begin: start file
#!/usr/bin/env python
import tasks
tasks.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/tasks-runner.service
sudo systemctl daemon-reload
sudo systemctl stop tasks-runner.service
sudo systemctl start tasks-runner.service
sudo systemctl enable tasks-runner.service

systemctl status tasks-runner.service

sudo systemctl restart tasks-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep tasks-runner

OR

tail -f /var/log/syslog | fgrep tasks-runner

Rocky9:

sudo tail -f /var/log/messages | fgrep tasks-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/tasks-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!





test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import tasks; print(tasks.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/tasks

https://slicer.cardinalhealth.net/tasks


"""

import copy
import datetime
import traceback
import json
import os
import shlex
import subprocess
import sys
import time

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)


try:
    import organization

    service_config = organization.get_config('tasks')
    organization.make_all_dirs(service_config)

    home_url = service_config['home_url']
    checkin_file_root = service_config['checkin_file_root']
    task_status_file = service_config['task_status_file']
    time_of_last_tasks_run_trust_path = service_config['time_of_last_tasks_run_trust_path']
    tasks_request_path = service_config['tasks_request_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import requests

    import codeupload
    import datadrop
    import datastore
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

pass_pair_d = {}
pass_pair_d['minusr'] = {'user': 'cv', 'pass': 'enfcorel', 'count': 0}
pass_pair_d['defP'] = {'user': 'cv', 'pass': 'enfcoreel', 'count': 0}
pass_pair_d['newpi'] = {'user': 'cv', 'pass': 'abgenfcoreel', 'count': 0}
pass_pair_d['cahpi'] = {'user': 'pnu-cv-fh', 'pass': 'abgenfcoreel', 'count': 0}


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(tasks status)'

    status = os.system('systemctl is-active --quiet tasks-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# ====================================
def do_one_address_python3_script(id, pass_pair, script, sudo=False):
    # ====================================
    address = get_device_address(id)

    script_filename = '/dev/shm/' + str(time.time()).replace('.', '')
    script += '\n'
    script += 'import os' + '\n'
    script += 'os.remove("' + script_filename + '")\n'

    with open(script_filename, 'w') as f:
        f.write(script)
    source = script_filename
    destination = script_filename
    result, fails = do_the_scp(source, destination, address, pass_pair)
    os.remove(script_filename)

    if sudo:
        do_the_call('sudo python3 ' + script_filename, address, pass_pair)
    else:
        do_the_call('python3 ' + script_filename, address, pass_pair)


# ====================================
def do_one_address_list(id, pass_pair, list_of_commands):
    # ====================================
    script = """
import subprocess
import shlex
import os
def do_command(command):
    command_splits = shlex.split(command)
    doit = subprocess.Popen (command_splits, universal_newlines=True, encoding="utf-8",
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    print ("pass:", mem_string)
    print ("fail:", fails)
    try:
        doit.terminate()
    except:
        pass
"""

    for key_to_pull in sorted(list_of_commands):
        script += 'print ("' + 'sectionStart:' + key_to_pull + '?' + '")\n'
        if 'fileExist:' in list_of_commands[key_to_pull]:
            script += "import os" + "\n"
            script += "filename='" + list_of_commands[key_to_pull].split(':')[1] + "'" + "\n"
            script += "print(os.path.isfile(filename))" + "\n"
        else:
            script += "do_command('" + list_of_commands[key_to_pull] + "')\n"
        script += 'print ("' + 'sectionEnd:' + key_to_pull + '?' + '")\n'

    do_one_address_python3_script(id, pass_pair, script)


# ====================================
def do_the_call(commands, address, pass_pair, timeout_to_use='10'):
    # ====================================
    command = "timeout " + timeout_to_use + " sshpass -p " + calculate_rot13(pass_pair[
                                                                                 'pass']) + " ssh -o ConnectTimeout=" + timeout_to_use + " -o StrictHostKeyChecking=no " + calculate_rot13(
        pass_pair['user']) + "@" + address + " " + commands
    command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    try:
        doit.terminate()
    except:
        pass


# ====================================
def do_the_scp(source, destination, address, pass_pair, timeout_to_use='10'):
    # ====================================
    command = "timeout " + timeout_to_use
    command += " sshpass -p " + calculate_rot13(pass_pair['pass'])
    command += " scp -o ConnectTimeout=" + timeout_to_use + " -o StrictHostKeyChecking=no "
    command += source + " " + calculate_rot13(pass_pair['user']) + "@" + address + ":" + destination
    command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    try:
        doit.terminate()
    except:
        pass
    return mem_string, fails


# ====================================
def push_named_service(id, pass_pair, the_service_name, the_version):
    # ====================================
    # take the time here, and do best effort to gain remote access to the device,
    #     install the service, enable it, and start it

    user_xfer_dir = '/home/' + calculate_rot13(pass_pair['user']) + '/xfer'
    report_given = 'push_service_' + the_service_name + ':'
    list_of_cmds = {}
    list_of_cmds['00-make_cardinal'] = 'sudo mkdir /cardinal'
    list_of_cmds['01-remove_xfer'] = 'sudo rm -rf ' + user_xfer_dir
    list_of_cmds['02-make_xfer'] = 'mkdir ' + user_xfer_dir
    do_one_address_list(id, pass_pair, list_of_cmds)
    report_given += ' ' + '1'

    source = codeupload.get_pi_service_version_filename(the_service_name, the_version)
    destination = user_xfer_dir + "/" + the_service_name + '.py'
    do_the_scp(source, destination, get_device_address(id), pass_pair)
    report_given += ' ' + '2'

    script = """
with open('/cardinal/""" + the_service_name.replace('_', '-') + """', 'w') as f:
    f.write("#!/usr/bin/env python3\\n")
    f.write("import """ + the_service_name + """\\n")
    f.write(\"""" + the_service_name + """.main()\\n")
    f.write("")
with open('/lib/systemd/system/""" + the_service_name.replace('_', '-') + """.service', 'w') as f:
    f.write("[Unit]\\n")
    f.write("Description=""" + the_service_name + """ daemon\\n")
    f.write("After=network.target\\n")
    f.write("\\n")
    f.write("[Service]\\n")
    f.write("ExecStart=/cardinal/""" + the_service_name.replace('_', '-') + """\\n")
    f.write("WorkingDirectory=/cardinal\\n")
    f.write("StandardOutput=inherit\\n")
    f.write("StandardError=inherit\\n")
    f.write("Restart=always\\n")
    f.write("User=root\\n")
    f.write("\\n")
    f.write("[Install]\\n")
    f.write("WantedBy=multi-user.target\\n")
    f.write("")
    """

    do_one_address_python3_script(id, pass_pair, script, sudo=True)

    list_of_cmds = {}
    list_of_cmds[
        '03'] = 'sudo mv -f ' + user_xfer_dir + '/' + the_service_name + '.py /cardinal/' + the_service_name + '.py'
    list_of_cmds['10'] = 'sudo chmod +x /cardinal/' + the_service_name.replace('_', '-')

    list_of_cmds['94'] = 'sudo systemctl daemon-reload'
    list_of_cmds['95'] = 'sudo systemctl stop ' + the_service_name.replace('_', '-') + '.service'
    list_of_cmds['96'] = 'sudo systemctl start ' + the_service_name.replace('_', '-') + '.service'
    list_of_cmds['97'] = 'sudo systemctl enable ' + the_service_name.replace('_', '-') + '.service'
    do_one_address_list(id, pass_pair, list_of_cmds)
    report_given += ' ' + '3'

    return report_given


# ====================================
def get_device_address(id):
    # ====================================
    if len(id.split('.')) == 4:
        return (id)
    else:
        base_path = checkin_file_root
        checkin_file = base_path + '/' + id
        try:
            with open(checkin_file, 'r') as f:
                checkin = json.loads(f.read())
        except:
            checkin = {}
        return_value = ''
        if 'address' in checkin:
            return_value = checkin['address']
        return str(return_value)


# ====================================
def remote_command(pass_pair, address, commands, timeout_to_use='10'):
    # ====================================
    command = "timeout " + timeout_to_use + " sshpass -p " + calculate_rot13(pass_pair[
                                                                                 'pass']) + " ssh -o ConnectTimeout=" + timeout_to_use + " -o StrictHostKeyChecking=no " + calculate_rot13(
        pass_pair['user']) + "@" + address + " " + commands
    print('command', command)
    command_splits = command.split(" ")
    print('command_splits', command_splits)
    # doit = subprocess.Popen (command_splits, universal_newlines=True, encoding="utf-8", stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    doit = subprocess.Popen(command_splits, universal_newlines=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return doit.communicate()


# ====================================
def test_login_correct(pass_pair, address):
    # ====================================
    commands = 'pwd'
    (mem_string, fails) = remote_command(pass_pair, address, commands)
    print('pass :', mem_string)
    print('fails: ', fails)
    return_value = True
    if 'Permission denied' in fails:
        return_value = False
    if (not fails) and (not mem_string):  # this is the timeout result, both empty
        return_value = False
    return return_value

    _ = """
pass : /home/<USER>
fails:  Warning: Permanently added '**************' (ECDSA) to the list of known hosts.

pass : /home/<USER>
fails:

pass :
fails:  Permission denied, please try again.

    """


# ====================================
def discover_pass_pair(id):
    # ====================================
    keys_files = '/root/.ssh/known_hosts'
    try:
        os.remove(keys_files)
    except:
        pass

    # go through the options, and see which one works right now
    pass_pair = {}
    address = get_device_address(id)
    if address:
        for pass_option in pass_pair_d:
            if test_login_correct(pass_pair_d[pass_option], address):
                pass_pair = pass_pair_d[pass_option]
                break
    return pass_pair


# ====================================
def push_service(id, the_service, the_version):
    # ====================================
    result = ''

    result = id + '_' + the_service + '_' + the_version

    pass_pair = discover_pass_pair(id)

    #    allowed_services = ['pi_runner', 'pi_monitor']
    if pass_pair:
        #        if the_service in allowed_services:
        result = push_named_service(id, pass_pair, the_service, the_version)
    #        else:
    #            result = 'service not allowed'
    else:
        result = 'no login possible'

    return result


# Main is the loop for the "task-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = -1
    send_data_failed_count = 0
    while True:
        pass_count += 1

        # do a favor for datastore, and keep a file current with the last time we ran through here
        try:
            if datastore.trust():  # If this is just after a boot up, then this call right here will get the auto_trust active
                with open(time_of_last_tasks_run_trust_path, 'w') as f:
                    the_time = str(time.time())
                    f.write(the_time)
                running_exception = 'success time: ' + the_time
            else:
                running_exception = 'no trust yet'

            open('/dev/shm/running_exceptions_' + service, 'w').write(running_exception)
        except:
            running_exception = traceback.format_exc().replace("\"", "'")
            open('/dev/shm/running_exceptions_' + service, 'w').write(running_exception)
            pass

        # list out the items on my to-do list
        requests_to_work_on = []
        try:
            requests_list = os.listdir(tasks_request_path)
        except:
            requests_list = []
        for request_id in sorted(requests_list):
            # check to see if I think it needs work
            complete_file = tasks_request_path + request_id + "/" + "complete"

            if not os.path.isfile(complete_file):
                requests_to_work_on.append(request_id)

        count_worked_on = 0
        for request_id in sorted(requests_to_work_on):
            complete_file = tasks_request_path + request_id + "/" + "complete"
            status_file = tasks_request_path + request_id + "/" + "status"
            exception_file = tasks_request_path + request_id + "/" + "exception"
            count_worked_on += 1

            # if count_worked_on == 1:
            # only do this one right now
            # provide a status update
            if True:
                the_request = {}
                try:
                    with open(tasks_request_path + request_id + '/request', 'r') as f:
                        the_request = json.loads(f.read())
                except:
                    pass
                the_data = []
                the_data.append('source=' + service)
                the_data.append('routing=' + 'task_status')
                the_data.append('task_id=' + request_id)

                my_status = 'know'

                if 'serial' in the_request:
                    my_status += '_serial'
                    if 'task' in the_request:
                        my_status += '_task'
                        if the_request['task'] == 'push':
                            my_status += '_push'
                            if 'what' in the_request:
                                my_status += '_what'
                                # the_request['what'] =  {"pi_runner": "R.3.0"}

                                try:
                                    my_status = 'push'
                                    for service_to_send in the_request['what']:
                                        my_status += '_' + push_service(the_request['serial'], service_to_send,
                                                                        the_request['what'][service_to_send]).replace(
                                            ' ', '_')
                                except:
                                    with open(exception_file, 'w') as f:
                                        f.write(str(traceback.format_exc()))
                                    my_status += '_exception:' + exception_file

                    the_data.append('status=' + my_status)
                else:
                    the_data.append('status=' + 'no_what')

                try:
                    with open(complete_file, 'w') as f:
                        f.write('complete')
                except:
                    pass

                the_report_url = home_url + '/tasks?' + ','.join(the_data)
                try:
                    r = requests.get(the_report_url, verify=False, timeout=2.0)
                    url_result = r.text
                except:
                    pass

        the_data = []
        the_data.append('source=' + service)
        the_data.append('routing=' + 'status_summary')
        the_data.append('pass_count=' + str(pass_count))

        # FixMe: need to figure out how else to handle this home_url
        the_report_url = home_url + '/tasks?' + ','.join(the_data)

        # https://slicer.cardinalhealth.net/tasks?source=slicer_tasks,routing=status_summary,pass_count=dave
        # https://slicer.cardinalhealth.net/tasks?source=slicer_tasks,routing=task_status,task_id=dave,status=1234

        try:
            r = requests.get(the_report_url, verify=False, timeout=2.0)
            url_result = r.text
            send_data_failed_count = 0
        except:
            send_data_failed_count += 1
            open('/dev/shm/running_exceptions_' + service, 'w').write(
                'pass_count : ' + str(pass_count) + ' -> ' + 'send data failed count = ' + str(
                    send_data_failed_count))  # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))

        for count_seconds in range(0, 60):
            time.sleep(1)  # short sleeps, so that shutdown can be quick


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def build_one_request(the_request_d):
    # ----------------------------

    request_signature = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    try:
        the_file = tasks_request_path + request_signature + "/request"
        if not os.path.exists(os.path.dirname(the_file)):
            os.makedirs(os.path.dirname(the_file))
        with open(the_file, 'w') as f:
            f.write(json.dumps(the_request_d))
    except:
        request_signature = "(failed)"

    return request_signature


# ====================================
def make_body_POST(environ):
    # ====================================
    # Initial requests will be made in the reports module, for a device push of pi_runner
    return ""


# ====================================
def make_body_GET(environ):
    # ====================================
    status_file = task_status_file

    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    requests_list = os.listdir(tasks_request_path)

    try:

        if 'routing' in query_items:
            routing = query_items['routing']

            file_out = tasks_request_path + routing
            if not os.path.exists(os.path.dirname(file_out)):
                os.makedirs(os.path.dirname(file_out))
            with open(file_out, 'w') as f:
                f.write(json.dumps(query_items))

            if routing == 'status_summary':
                try:
                    if not os.path.exists(os.path.dirname(status_file)):
                        os.makedirs(os.path.dirname(status_file))
                    with open(status_file, 'w') as f:
                        f.write(json.dumps(query_items))
                except:
                    pass

            elif routing == 'task_status':
                if 'task_id' in query_items:
                    task_id = query_items['task_id']

                    if task_id in requests_list:
                        # ok to use it, since it refers to an active task
                        if 'status' in query_items:
                            status = query_items['status']
                            file_out = tasks_request_path + task_id + "/" + 'status'

                            try:
                                if not os.path.exists(os.path.dirname(file_out)):
                                    os.makedirs(os.path.dirname(file_out))
                                with open(file_out, 'w') as f:
                                    f.write(json.dumps(query_items))
                            except:
                                pass
            else:
                pass

        else:
            # for humans
            if permissions.permission_allowed(environ, 'tasks_edit'):
                body += ''

                d = {}
                try:
                    with open(status_file, 'r') as f:
                        content = f.read()
                        d = json.loads(content)
                except:
                    pass

                counts = '(no counts in status_file)'
                if 'pass_count' in d:
                    counts = str(d['pass_count'])

                #        body += "<br><br>Task-runner pass counts: " + counts

                body += '<center>'
                body += 'Task Status'
                body += '</center>'

                body += '<center>'
                body += '<br><br>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<td>'
                body += '<B>Passes</B>'
                body += '</td>'
                body += '<td>'
                body += counts
                body += '</td>'
                body += '</tr>'
                body += '</table>'
                body += '</center>'

                # as a test, later this will happen in the reports (login protected)
                # request_string = build_one_request({"task":"push_pi_runner","serial":"10000000e3669edf"})

                body += '<center>'
                body += '<br><br>'
                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += '<B>Request</B><br>(most recent at the top)'
                body += '</td>'
                body += '<td>'
                body += '<B>Status</B>'
                body += '</td>'
                body += '</tr>'

                for task_id in sorted(requests_list, reverse=True):
                    request_content = ""
                    file_in = tasks_request_path + task_id + "/" + 'request'
                    try:
                        with open(file_in, 'r') as f:
                            request_content = f.read()
                    except:
                        pass

                    status = ""
                    file_in = tasks_request_path + task_id + "/" + 'status'

                    try:
                        with open(file_in, 'r') as f:
                            status_d = json.loads(f.read())
                            status = str(status_d['status'])
                    except:
                        pass

                    if request_content:
                        body += '<tr>'
                        body += '<td>'
                        body += task_id
                        body += '</td>'
                        body += '<td>'
                        body += str(request_content)
                        body += '</td>'
                        body += '<td>'
                        body += status
                        body += '</td>'
                        body += '</tr>'
                body += '</table>'
                body += '</center>'

            else:
                body = ""
                body += "<br><br><br><br><br>"
                body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
                return body

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    try:
        if environ['REQUEST_METHOD'] == 'POST':
            body, other = make_body_POST(environ)
        elif environ['REQUEST_METHOD'] == 'GET':
            body, other = make_body_GET(environ)
        permissions.log_page_allowed(environ, service, other)
    except:
        pass

    return body,other

# ====================================
def calculate_rot13(s):
    # ====================================
    chars = "abcdefghijklmnopqrstuvwxyz"
    trans = chars[13:] + chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c) > -1 else c
    return ''.join(rot_char(c) for c in s)


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)
        html += '<br>' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
