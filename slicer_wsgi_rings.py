# A rings for slicer page services, and also a runner service, which delivers result to web service with a requests.get call

service = 'rings'
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/rings.py

using:
sudo vi /var/www/html/rings.py

It also requires:

sudo vi /etc/httpd/conf.d/python-rings.conf
----- start copy -----
WSGIScriptAlias /rings /var/www/html/rings.py
----- end copy -----

sudo chown apache:apache /var/www/html/rings.py

sudo systemctl restart httpd

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/rings-runner
sudo chmod +x /var/www/html/rings-runner

# ===== begin: start file
#!/usr/bin/env python
import rings
rings.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/rings-runner.service
sudo systemctl daemon-reload
sudo systemctl stop rings-runner.service
sudo systemctl start rings-runner.service
sudo systemctl enable rings-runner.service

systemctl status rings-runner.service

sudo systemctl restart rings-runner.service

sudo systemctl status rings-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep rings-runner

OR

tail -f /var/log/syslog | fgrep rings-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/rings-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import rings; print(rings.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/rings


"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_rings


"""

startup_exceptions = ''

import copy
import traceback
import json
import os

try:  # for unittest to work
    import requests
except:
    pass
import shlex
import subprocess
import sys
import time
import unittest

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    home_url = service_config['home_url']

    checkin_file_root = service_config['checkin_file_root']
    datadrop_save_path = service_config['datadrop_save_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:  # for unittest to work
    import codeupload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import config
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import upload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

rings = ['0', '1', '2', '3']
# rings_request_path = '/var/log/slicer/rings/'
status_file = '/var/log/slicer/rings/status_summary'

# ----------------------------
def is_version_less_than_another_version(version_1, version_2):
    # ----------------------------
    return_value = False

    sub_items = []
    for sub_item in version_1.split('.'):
        sub_items.append(sub_item.zfill(10))
    key_1 = '.'.join(sub_items)

    sub_items = []
    for sub_item in version_2.split('.'):
        sub_items.append(sub_item.zfill(10))
    key_2 = '.'.join(sub_items)


    if key_1 < key_2:
        return_value = True
    return return_value

# ----------------------------
def sort_service_versions(versions):
    # ----------------------------
    # lifted from reports
    return_value = []

    expanded_versions = {}
    for item in versions:
        sub_items = []
        for sub_item in item.split('.'):
            sub_items.append(sub_item.zfill(10))
        key = '.'.join(sub_items)
        expanded_versions[key] = item

    for key in sorted(expanded_versions.keys(), reverse=True):
        return_value.append(expanded_versions[key])

    return return_value

# ----------------------------
def get_services_from_list_of_sp(sp_list, service_pack_list):
    # ----------------------------
    return_value = {}

    for sp_raw in sp_list:
        if type(sp_raw) is tuple:
            sp = sp_raw[0]
        else:
            sp = sp_raw

        if sp in service_pack_list:
            services_d = service_pack_list[sp]
            for service in services_d.keys():
                if not service in return_value:
                    return_value[service] = {}
                the_version = services_d[service]
                return_value[service][the_version] = ''

    return return_value


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def extract_service_pack_target(content_of_versions, sp_type='Target SP'):
    # ====================================
    return_value = ''

    try:
        for line in content_of_versions.split('\n'):
            if line:
                splits = line.split('=')
                if sp_type in splits[0]:
                    sp_target_number = splits[1].split('.')[1]
                    if len(sp_target_number) < 3:
                        return_value = 'SP.' + ('0' * (3 - len(sp_target_number))) + sp_target_number
                    else:
                        return_value = 'SP.' + sp_target_number
    except:
        pass

    return return_value


# ====================================
def extract_service_pack_release_notes(content_of_versions):
    # ====================================
    return_value = {}

    last_date = ''
    in_service_pack = ''

    for line in content_of_versions.split('\n'):
        if line:
            splits = line.split()

            if splits[0].count('.') == 2:
                if len(splits[0].split('.')[0]) == 4:
                    last_date = splits[0]
                    in_service_pack = ''

            if last_date:
                if '(SP.' in line:
                    in_service_pack = 'SP.' + '{:03d}'.format(int(line.split('(SP.')[1].split(')')[0]))

                    if len(splits) > 1:
                        image = splits[0]
                    else:
                        image = ''

                    if not in_service_pack in return_value:
                        return_value[in_service_pack] = {'date': last_date, 'image': image, 'note': ''}
                else:
                    if in_service_pack:
                        if 'Includes following' in line:
                            in_service_pack = ''
                        else:
                            if return_value[in_service_pack]['note']:
                                return_value[in_service_pack]['note'] += '\n'
                            return_value[in_service_pack]['note'] += line

    return return_value


# ====================================
def get_all_rings():
    # ====================================
    return rings


# ====================================
def extract_service_pack_build_list(versions_content):
    # ====================================
    return_value = {}

    in_service_pack = ''

    for line in versions_content.split('\n'):
        if '(SP.' in line:
            in_service_pack = 'SP.' + '{:03d}'.format(int(line.split('(SP.')[1].split(')')[0]))
            if not in_service_pack in return_value:
                return_value[in_service_pack] = {}

        if in_service_pack:
            if ' - pi_' in line:
                service_found = 'pi_' + line.split(' - pi_')[1].split()[0]
                version_found = line.split(' - pi_')[0].strip()
                return_value[in_service_pack][service_found] = version_found

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(tasks status)'

    status = os.system('systemctl is-active --quiet rings-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# ====================================
def compare_service_set_to_standard(set_to_test, set_as_standard):
    # ====================================

    any_less = False
    any_more = False
    any_extra = False
    any_missing = False
    for standard_service in set_as_standard:
        if standard_service in set_to_test:
            version_to_test = set_to_test[standard_service].replace(' *', '')
            if version_to_test < set_as_standard[standard_service]:
                any_less = True
            elif version_to_test > set_as_standard[standard_service]:
                any_more = True
        else:
            any_missing = True

    for test_service in set_to_test:
        if not test_service in set_as_standard:
            any_extra = True

    return_value = '='
    if any_less or any_missing:
        return_value = '<'
    else:
        if any_more or any_extra:
            return_value = '>'

    return return_value


# ====================================
def calculate_service_pack(services, service_packs):
    # ====================================
    if services:
        return_value = 'SP.000'
    else:
        return_value = ''

    for service_pack in sorted(service_packs):
        compare_result = compare_service_set_to_standard(services, service_packs[service_pack])
        if compare_result == '>':
            return_value = service_pack + '+'

        if compare_result == '=':
            return_value = service_pack

    return return_value


# ====================================
def device_ring_calculated(id, device_ring='(empty)'):
    # ====================================
    if device_ring == '(empty)':
        device_ring = str(datastore.get_value('device_ring_assignment_' + id))

    versions_available = {}
    try:
        # what has this device reported it has running
        datadrop_directory = datadrop_save_path + id + '/'
        with open(datadrop_directory + 'service_versions', 'r') as f:
            content = f.read()
            versions_available = json.loads(content)
    except:
        pass

    if 'pi_runner' in versions_available:
        if versions_available['pi_runner'] >= 'R.3.0':
            if not device_ring:
                device_ring = '3'
        else:
            if not device_ring:
                device_ring = '4'
    else:
        if not device_ring:
            device_ring = '4'

    return device_ring


# ====================================
def get_content_of_versions():
    # ====================================
    content = upload.get_upload_file_content('read_release_notes_slicer_pi.txt')

    return content


# ====================================
def make_body_POST(environ):
    # ====================================
    body = ''

    body += 'method = POST'

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    the_who = login.get_current_user(environ)

    body += json.dumps(d) + '<br>'

    if 'the_selection' in d:
        pass

    # return json.dumps(d)
    _ = """
{"version_selected": ["S.1.0"], "ring": ["1"], "the_selection": ["service_version_for_ring"], "service": ["pi_security"]}

{"service_pack_selected": ["SP.025"], "service_pack": ["SP.027"], "the_selection": ["service_pack_for_ring"], "ring": ["0"]}

    """

    try:
        if 'the_selection' in d:
            if 'service_pack_for_ring' == str(d['the_selection'][0]):
                service_pack = str(d['service_pack_selected'][0])
                ring = str(d['ring'][0])

                # do the look up, and set the services accordingly
                content_of_versions = get_content_of_versions()
                service_pack_list = extract_service_pack_build_list(content_of_versions)
                if service_pack in service_pack_list:  # this avoids SP.000 doing anything
                    for service_name in service_pack_list[service_pack]:
                        version_value = service_pack_list[service_pack][service_name]
                        datastore.set_value('device_ringed_service' + ring + '_' + service_name, version_value, the_who)

            if 'service_version_for_ring' == str(d['the_selection'][0]):
                service_name = str(d['service'][0])

                if 'version_selected' in d:
                    version_value = str(d['version_selected'][0])
                else:
                    version_value = ''
                ring = str(d['ring'][0])
                datastore.set_value('device_ringed_service' + ring + '_' + service_name, version_value, the_who)
    except:
        body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        return body

    return make_body_GET(environ)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        body, flags = make_body_content({})
        if flags:
            return_value = True
    except:
        pass

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body, flags = make_body_content(environ)

    return body, other


# ====================================
def make_body_content(environ):
    # ====================================
    body = ''
    flags_list = []

    services_available = codeupload.get_pi_services()
    service_names = sorted(services_available.keys())

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    query_string = ''
    if 'QUERY_STRING' in environ:
        query_string = environ['QUERY_STRING']
    for item in query_string.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:

        if 'routing' in query_items:
            routing = query_items['routing']

            # 2023.01.06.DWF Took this out... was it just left over debugging?
            #            file_out = rings_request_path + routing
            #            if not os.path.exists(os.path.dirname(file_out)):
            #                os.makedirs(os.path.dirname(file_out))
            #            with open (file_out, 'w') as f:
            #                f.write(json.dumps(query_items))

            if routing == 'status_summary':
                try:
                    if not os.path.exists(os.path.dirname(status_file)):
                        os.makedirs(os.path.dirname(status_file))
                    with open(status_file, 'w') as f:
                        f.write(json.dumps(query_items))
                except:
                    pass


        else:
            # Build response for humans
            if True:
                ring_counts = {}
                for ring in rings:
                    ring_counts[ring] = {'total': 0, 'available': 0}

                id_files = find_all_devices()
                for id in id_files:
                    field_service_versions = {}
                    try:
                        # what has this device reported it has running
                        datadrop_directory = datadrop_save_path + id + '/'
                        with open(datadrop_directory + 'service_versions', 'r') as f:
                            content = f.read()
                            field_service_versions = json.loads(content)
                    except:
                        pass

                    device_ring = device_ring_calculated(id)

                    if device_ring in ring_counts:
                        ring_counts[device_ring]['total'] += 1

                        if 'pi_runner' in field_service_versions:
                            if field_service_versions['pi_runner'] >= 'R.3.0':
                                ring_counts[device_ring]['available'] += 1

                d = {}
                try:
                    with open(status_file, 'r') as f:
                        content = f.read()
                        d = json.loads(content)
                except:
                    pass

                counts = '(no counts in status_file)'
                if 'pass_count' in d:
                    counts = str(d['pass_count'])
                items_remain = '(no items)'
                if 'items_remain' in d:
                    items_remain = str(d['items_remain'])

                try:
                    body += '<center>'

                    body += '<table border="1" cellpadding="5">'
                    body += '<tr>'
                    body += '<td>'
                    body += 'Ring M : manual<br>'
                    body += 'Ring 0 : development<br>'
                    body += 'Ring 1 : early adopters<br>'
                    body += 'Ring 2 : pre production<br>'
                    body += 'Ring 3 : production<br>'
                    body += 'Ring 4 : anything left over, that does not yet have runner version R.3.0 or above.<br>'
                    body += '</td>'
                    body += '</tr>'

                    body += '</table>'

                    body += '<br><br>'
                    body += '<table border="1" cellpadding="5">'

                    body += '<tr>'
                    body += '<td>'
                    # body += '<B>Service</B>'
                    body += '</td>'

                    content_of_versions = get_content_of_versions()
                    service_pack_list = extract_service_pack_build_list(content_of_versions)
                    service_pack_dropdown_items = sorted(service_pack_list, reverse=True)

                    service_pack_dropdown_list = []
                    for service_pack_item in service_pack_dropdown_items:
                        the_string = service_pack_item + ' ->'
                        for service in sorted(service_pack_list[service_pack_item]):
                            the_string += ' ' + service_pack_list[service_pack_item][service]
                        service_pack_dropdown_list.append(the_string)

                    if not 'SP.000' in service_pack_dropdown_list:
                        service_pack_dropdown_list.append('SP.000')

                    url_to_use = make_home_url_from_environ(environ)
                    url_base = url_to_use + '/reports?ring=(ring)'
                    for ring in rings:
                        url_to_use = url_base.replace('(ring)', ring)
                        value_to_use = 'Ring ' + ring

                        body += '<td>'
                        body += '<center><B>'

                        if url_to_use:
                            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
                            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + value_to_use + """</a>"""
                        else:
                            body += value_to_use

                        body += '</B></center>'
                        body += '</td>'
                    body += '</tr>'

                    body += '<tr>'
                    body += '<td title="Total number of devices in this ring.">'
                    body += '<B>Total Devices</B>'
                    body += '</td>'
                    for ring in rings:
                        body += '<td>'
                        body += '<center>'
                        body += str(ring_counts[ring]['total'])
                        body += '</center>'
                        body += '</td>'
                    body += '</tr>'

                    """
                    body += '<tr>'
                    body += '<td title="The number of devices in this ring, that have runner version R.3.0 or higher, which means they can actually pull the services.">'
                    body += '<B># that can pull</B>'
                    body += '</td>'
                    for ring in rings:
                        body += '<td>'
                        body += '<center>'
                        body += str(ring_counts[ring]['available'])
                        body += '</center>'
                        body += '</td>'
                    body += '</tr>'
                    """

                    body += '<tr>'
                    body += '<td>'
                    body += '<B>Service Pack</B>'
                    body += '</td>'

                    rings_to_service_pack = lookup_rings_to_service_pack()
                    for ring in rings:
                        calc_service_pack = rings_to_service_pack[ring]

                        body += '<td>'
                        body += '<center>'
                        body += calc_service_pack
                        body += '</center>'

                        if permissions.permission_prefix_allowed(environ, 'rings_edit'):
                            body += '<br>'
                            body += '<form method="post" action="">'
                            body += '<select name="the_selection" id="the_selection" hidden>'
                            body += '<option value="service_pack_for_ring" selected>' + 'service_pack_for_ring' + '</option>'
                            body += '</select>'

                            calc_service_pack_to_use = calc_service_pack.replace('+',
                                                                                 '')  # Truncate to the closest previous
                            body += '<select name="service_pack" id="service_pack" hidden>'
                            body += '<option value="' + calc_service_pack_to_use + '" selected>' + calc_service_pack_to_use + '</option>'
                            body += '</select>'

                            body += '<select name="ring" id="ring" hidden>'
                            body += '<option value="' + ring + '" selected>' + ring + '</option>'
                            body += '</select>'

                            body += '<select name="' + 'service_pack_selected' + '" id="' + 'service_pack_selected' + '">'
                            for key_long_name in service_pack_dropdown_list:
                                key_name = key_long_name.split()[0]
                                if calc_service_pack_to_use == key_name:
                                    body += '<option value="' + key_name + '" selected>' + key_name + '</option>'
                                else:
                                    body += '<option value="' + key_name + '">' + key_name + '</option>'

                            body += '<input type="submit" value="Submit">'
                            body += '</select>'
                            body += '</form>'

                        body += '</td>'
                    body += '</tr>'

                    for service_name in service_names:
                        versions = services_available[service_name].keys()
                        values_to_show = ['']
                        for key_name in sort_service_versions(versions):
                            values_to_show.append(key_name)

                        body += '<tr>'
                        body += '<td>'
                        body += service_name
                        body += '</td>'

                        for ring in rings:
                            ringed_service_version = datastore.get_value(
                                'device_ringed_service' + ring + '_' + service_name)

                            color = ''
                            notation = ''
                            if ring == '0':
                                if len(values_to_show) > 1:
                                    if ringed_service_version != values_to_show[
                                        1]:  # skip over the blank field, and find the most recent version
                                        color = config.color('color_update_highlight')  # "(100, 100, 255, 0.3)"
                                        notation = 'needs update'
                                        flags_list.append(ring + ':' + service_name)
                                    else:
                                        # call it good
                                        color = config.color('color_version_is_matched')
                                        notation = 'matched'
                            else:
                                try:
                                    previous_ring_ringed_version = datastore.get_value(
                                        'device_ringed_service' + str(int(ring) - 1) + '_' + service_name)
                                    if previous_ring_ringed_version == ringed_service_version:
                                        color = config.color('color_version_is_matched')
                                        notation = 'matched'
                                    elif is_version_less_than_another_version(previous_ring_ringed_version, ringed_service_version):
                                        # if we are ahead of the previous ring, then call that out as gray
                                        color = config.color('color_version_is_ahead')
                                        notation = 'ahead of previous ring'
                                    else:
                                        # look through the available versions list, and see how many options back we are
                                        # (if each is even in the list)
                                        index_previous = -1
                                        index_current = -1
                                        index_now = -1
                                        for key_name in sort_service_versions(versions):
                                            index_now += 1
                                            if key_name == previous_ring_ringed_version:
                                                index_previous = index_now
                                            if key_name == ringed_service_version:
                                                index_current = index_now

                                            if index_previous < 0 or index_current < 0:
                                                # unsure, due to one of them not being in the list
                                                # color = config.color('color_version_is_ahead')
                                                notation = 'something missing'
                                            else:
                                                if 1 == index_current - index_previous:
                                                    color = config.color('color_version_is_just_behind_by_one')
                                                    notation = 'behind by one'
                                                else:
                                                    color = config.color(
                                                        'color_version_is_just_behind_by_more_than_one')
                                                    notation = 'behind by more than one'
                                                    flags_list.append(ring + ':' + service_name)
                                        pass
                                except:
                                    pass

                            if color:
                                body += '<td style="background-color:rgba' + color + '">'
                            else:
                                body += '<td>'

                            # body += ring + '_' + service_name + '<br>'
                            if notation:
                                body += ringed_service_version + '<br>(' + notation + ')<br>'
                            else:
                                body += ringed_service_version + '<br>' + '<br>'

                            if permissions.permission_prefix_allowed(environ, 'rings_edit'):
                                body += '<form method="post" action="">'
                                body += '<select name="the_selection" id="the_selection" hidden>'
                                body += '<option value="service_version_for_ring" selected>' + 'service_version_for_ring' + '</option>'
                                body += '</select>'

                                body += '<select name="service" id="service" hidden>'
                                body += '<option value="' + service_name + '" selected>' + service_name + '</option>'
                                body += '</select>'

                                body += '<select name="ring" id="ring" hidden>'
                                body += '<option value="' + ring + '" selected>' + ring + '</option>'
                                body += '</select>'

                                body += '<select name="' + 'version_selected' + '" id="' + 'version_selected' + '">'
                                for key_name in values_to_show:
                                    body += '<option value="' + key_name + '">' + key_name + '</option>'

                                body += '<input type="submit" value="Submit">'
                                body += '</select>'
                                body += '</form>'

                            body += '</td>'

                        body += '<td>'

                        key_names = codeupload.get_pi_service_formatted_release_notes(service_name)

                        body += '<select name="' + 'release_notes' + '" id="' + 'release_notes' + '">'
                        for key_name in key_names:
                            body += '<option>' + key_name + '</option>'
                            # body += '<option hidden>' + key_name + '</option>'
                        body += '</select>'
                        body += '</td>'

                        body += '</tr>'

                    body += '</table>'

                    body += '<br><br>'

                    body += 'Service Pack summary<br>'
                    body += '<select name="' + 'service_pack_selected' + '" id="' + 'service_pack_selected' + '">'
                    body += '<option value="' + '' + '" selected>' + '' + '</option>'
                    for key_name in service_pack_dropdown_list:
                        body += '<option value="' + key_name + '">' + key_name + '</option>'

                    body += '</select>'

                    body += '<br><br>'

                    body += '<table border="1" cellpadding="5">'
                    body += '<tr>'
                    body += '<td style="background-color:rgba' + config.color('color_update_highlight') + '">'
                    body += 'Ring 0 version needs pulled forward'
                    body += '</td>'
                    body += '</tr>'

                    body += '<tr>'
                    body += '<td style="background-color:rgba' + config.color('color_version_is_ahead') + '">'
                    body += 'Version is ahead of previous ring'
                    body += '</td>'
                    body += '</tr>'

                    body += '<tr>'
                    body += '<td style="background-color:rgba' + config.color('color_version_is_matched') + '">'
                    body += 'Version is matched to previous ring'
                    body += '</td>'
                    body += '</tr>'

                    body += '<tr>'
                    body += '<td style="background-color:rgba' + config.color(
                        'color_version_is_just_behind_by_one') + '">'
                    body += 'Version is behind by just one from previous ring'
                    body += '</td>'
                    body += '</tr>'

                    body += '<tr>'
                    body += '<td style="background-color:rgba' + config.color(
                        'color_version_is_just_behind_by_more_than_one') + '">'
                    body += 'Version is behind by more than one from previous ring'
                    body += '</td>'
                    body += '</tr>'

                    body += '</table>'

                    body += '</center>'

                    # report on runner activity
                    body += '<br><br>'
                    body += '<center>'
                    body += 'Rings runner report:'
                    body += '<br><br>'
                    body += 'Pass counts ' + counts

                    body += '<table border="1" cellpadding="5">'
                    body += '<tr>'
                    body += '<td>'
                    body += 'Items remaining to pull update(s)'
                    body += '</td>'
                    body += '<td>'
                    body += items_remain
                    body += '</td>'
                    body += '</tr>'
                    body += '</table>'

                    body += '</center>'


                except Exception as e:
                    body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            if permissions.permission_prefix_allowed(environ, 'rings_'):
                pass  # let the built body ride
            else:
                body = ""
                body += "<br><br><br><br><br>"
                body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    except Exception as e:
        body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        return body, 1

    flags = ','.join(flags_list)

    return body, flags


# ====================================
def make_body(environ):
    # ====================================
    body = ''

    try:
        if environ['REQUEST_METHOD'] == 'POST':
            body, other = make_body_POST(environ)
        elif environ['REQUEST_METHOD'] == 'GET':
            body, other = make_body_GET(environ)
        permissions.log_page_allowed(environ, service, other)
    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ====================================
def find_all_devices():
    # ====================================
    ids = {}
    base_path = checkin_file_root
    id_files = os.listdir(base_path)
    return id_files


# ----------------------------
def do_one_command(command):
    # ----------------------------
    import shlex
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def main():  # this is the rings-runner
    # ====================================
    pass_count = 0
    send_data_failed_count = 0
    while True:
        pass_count += 1

        try:
            items_remain = 0
            debug_print = ''

            # do a look through all rings, all devices, and see what needs marked for new pull
            ids = {}
            id_files = find_all_devices()
            for id in id_files:
                field_service_versions = {}
                try:
                    # what has this device reported it has running
                    datadrop_directory = datadrop_save_path + id + '/'
                    with open(datadrop_directory + 'service_versions', 'r') as f:
                        content = f.read()
                        field_service_versions = json.loads(content)
                except:
                    pass

                device_ring = device_ring_calculated(id)

                ids[id] = {'field_service_versions': field_service_versions, 'device_ring': device_ring}

            debug_print += '\n' + '=====================\n' + 'ids:\n' + json.dumps(ids, indent=4, separators=(',', ':'))

            services_available = codeupload.get_pi_services()
            service_names = sorted(services_available.keys())

            debug_print += '\n' + '=====================\n' + 'service_names:\n' + json.dumps(service_names, indent=4, separators=(',', ':'))
            debug_print += '\n' + '=====================\n' + 'rings:\n' + json.dumps(rings, indent=4, separators=(',', ':'))

            work_to_do = {}
            for service_name in service_names:
                for ring in rings:
                    ring_service_version = datastore.get_value('device_ringed_service' + ring + '_' + service_name)
                    debug_print += '\n' + '=====================\n' + 'ring_service_version:\n' + service_name + ', ' + ring + ', ' + ring_service_version

                    if ring_service_version:
                        for id in ids:
                            if ids[id]['device_ring'] == ring:
                                needs_pull = False
                                if not service_name in ids[id]['field_service_versions']:
                                    needs_pull = True
                                else:
                                    if ring_service_version > ids[id]['field_service_versions'][service_name]:
                                        needs_pull = True

                                fielded_runner_version = ''
                                if 'pi_runner' in ids[id]['field_service_versions']:
                                    fielded_runner_version = ids[id]['field_service_versions']['pi_runner']

                                if fielded_runner_version < 'R.3.0':
                                    # not ready to accept pull requests
                                    needs_pull = False

                                if needs_pull:
                                    items_remain += 1
                                    if not id in work_to_do:
                                        work_to_do[id] = {}
                                    work_to_do[id][service_name] = ring_service_version

            # we now have the full list of devices that need service updates

            # 2021.10.29 : do not actually do the updates here, for now.
            # Leave it up to the users to report which devices could take updates, and let them manage based on that reporting.

            """
            for id in work_to_do:
                for service_name in work_to_do[id]:

                    existing_last = datastore.get_value('device_service_request_' + str(id) + '_last_' + service_name)
                    existing_pull = datastore.get_value('device_service_request_' + str(id) + '_pull_' + service_name)

                    work_to_do[id]['marked_last'] = 0
                    work_to_do[id]['marked_pull'] = 0

                    if existing_last <> work_to_do[id][service_name]:
                        datastore.set_value('device_service_request_' + str(id) + '_last_' + service_name', work_to_do[id][service_name], who='rings')
                        work_to_do[id]['marked_last'] = 1
                    if existing_pull <> work_to_do[id][service_name]:
                        datastore.set_value('device_service_request_' + str(id) + '_pull_' + service_name', work_to_do[id][service_name], who='rings')
                        work_to_do[id]['marked_pull'] = 1
            """

            debug_print += '\n' + '=====================\n' + 'work:\n' + json.dumps(work_to_do, indent=4, separators=(',', ':'))
            try:
                with open('/dev/shm/rings_work.txt', 'w') as f:
                    f.write(debug_print)
            except:
                pass

            final_report = '/dev/shm/rings_report.txt'
            temp_report = final_report + '.tmp'
            try:
                with open(temp_report, 'w') as f:
                    f.write(json.dumps(work_to_do, indent=4, separators=(',', ':')))
                    # do atomic move
                cmd = 'sudo mv -f ' + temp_report + ' ' + final_report
                do_one_command(cmd)

            except:
                pass

            the_data = []
            the_data.append('routing=' + 'status_summary')
            the_data.append('pass_count=' + str(pass_count))
            the_data.append('items_remain=' + str(items_remain))

            # Fixme: home_url
            #        url_to_use = make_home_url_from_environ(environ)
            url_to_use = home_url
            the_report_url = url_to_use + '/rings?' + ','.join(the_data)

            # https://slicer.cardinalhealth.net/tasks?source=slicer_tasks,routing=status_summary,pass_count=dave
            # https://slicer.cardinalhealth.net/tasks?source=slicer_tasks,routing=task_status,task_id=dave,status=1234

            try:
                r = requests.get(the_report_url, verify=False, timeout=2.0)
                url_result = r.text
                send_data_failed_count = 0
            except:
                send_data_failed_count += 1
                open('/dev/shm/running_exceptions_' + service, 'w').write(
                    'pass_count : ' + str(pass_count) + ' -> ' + 'send data failed count = ' + str(
                        send_data_failed_count) + ' on ' + the_report_url)

        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write('pass_count : ' + str(pass_count) + ' -> ' + str(
                traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))
            # + ', ' + str(time_diff) + ', ' + str(time_of_last_poll))

        # time.sleep(60 * 5) # device check in once a minute, so give them a chance to see the pull, do the pull, and report (up to 4 minutes)
        time.sleep(60)  # keep it fairly current


# ====================================
def lookup_rings_to_service_pack():
    # ====================================
    services_available = codeupload.get_pi_services()
    service_names = sorted(services_available.keys())

    content_of_versions = get_content_of_versions()
    service_pack_list = extract_service_pack_build_list(content_of_versions)
    return_value = {}
    for ring in rings:
        set_of_chosen = {}
        for service_name in service_names:
            ringed_service_version = datastore.get_value('device_ringed_service' + ring + '_' + service_name)
            set_of_chosen[service_name] = ringed_service_version
        calc_service_pack = calculate_service_pack(set_of_chosen, service_pack_list)
        return_value[ring] = calc_service_pack
    return return_value


# ====================================
def calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list, lowest_service_pack=''):
    # ====================================
    return_value = []

    sp_values = sorted(service_pack_list, reverse=True)

    for sp_value in sp_values:
        was_used = False

        # -----------------------
        # with a plus sign
        # -----------------------
        if lowest_service_pack:
            if sp_value + '+' < lowest_service_pack:
                test_it = False
            else:
                test_it = True
        else:
            test_it = True

        if test_it:
            if not sp_value == sp_values[0]:
                sp_value_to_test = sp_value + '+'
                for ring_to_test in sorted(rings_to_service_pack):
                    if rings_to_service_pack[ring_to_test] == sp_value_to_test:
                        return_value.append((sp_value_to_test, ring_to_test))
                        # was_used = True # Do not mark this here, because we want the base sp also listed

        # -----------------------
        # without a plus sign
        # -----------------------
        if lowest_service_pack:
            if sp_value < lowest_service_pack:
                test_it = False
            else:
                test_it = True
        else:
            test_it = True

        if test_it:
            sp_value_to_test = sp_value
            for ring_to_test in sorted(rings_to_service_pack):
                if rings_to_service_pack[ring_to_test] == sp_value_to_test:
                    return_value.append((sp_value_to_test, ring_to_test))
                    was_used = True

            if not was_used:
                return_value.append((sp_value_to_test, ''))

    return return_value


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_service_pack_build_list(self):
        """
        (fill in here)
        """

        content_of_versions = """
2022.03.07
2.4.1 (SP.27)
Image: Networking service now starts from zero connections each startup, to get clean onto corp.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth (old note here as a test of cleanly getting just the service name)
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security

2022.03.02
2.4.0 (SP.26)
Image: Networking service now has a new certificate to connect to corp WiFi, that expires March 2, 2023

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.0 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security
"""
        expected_result = {'SP.027': {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        },
            'SP.026': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.5.0',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            }
        }
        actual_result = extract_service_pack_build_list(content_of_versions)
        self.assertEqual(actual_result, expected_result)

    def test_compare_service_sets(self):
        set_standard = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
        }

        set_a = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '=')

        set_a = {
            'pi_bluetooth': 'B.2.3',
            'pi_config': 'C.1.0',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '<')

        set_a = {
            'pi_bluetooth': 'B.2.5',
            'pi_config': 'C.1.0',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '>')

        set_a = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.1',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '>')

        set_a = {
            'pi_bluetooth': 'B.2.3',
            'pi_config': 'C.1.1',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '<')

        set_a = {
            'pi_bluetooth': 'B.2.5',
            'pi_config': 'C.0.9',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '<')

        # extra service
        set_a = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_test': 'T.1.0',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '>')

        # missing service
        set_a = {
            'pi_bluetooth': 'B.2.4',
        }
        self.assertEqual(compare_service_set_to_standard(set_a, set_standard), '<')

    def test_service_pack_calculate_empty(self):
        service_pack_list = {'SP.027': {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        },
            'SP.026': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.5.0',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            }
        }

        test_service_versions = {}
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), '')

    def test_service_pack_calculate(self):
        service_pack_list = {'SP.027': {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        },
            'SP.026': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.5.0',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            }
        }

        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.027')

        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.0',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.026')

        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.1',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.026+')

        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.4.9',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.000')

        # extra service(s)
        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.0',
            'pi_runner': 'R.5.9',
            'pi_test': 'T.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.026+')

        # missing service(s)
        test_service_versions = {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.0',
            'pi_runner': 'R.5.9',
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.000')

    def test_service_pack_plus_rings(self):
        service_pack_list = {'SP.027': {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        },
            'SP.026': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.5.0',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            },
            'SP.025': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.4.9',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            }
        }

        rings_to_service_pack = {
            '0': 'SP.027',
            '1': 'SP.026+',
            '2': 'SP.026+',  # These might be different version of 26+
            '3': 'SP.026',
        }

        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026', '3'), ('SP.025', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list), expected)

        rings_to_service_pack = {
            '0': 'SP.027',
            '2': 'SP.026+',  # These might be different version of 26+
            '1': 'SP.026+',
            '3': 'SP.026',
        }

        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026', '3'), ('SP.025', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list), expected)

        rings_to_service_pack = {
            '0': 'SP.027',
            '2': 'SP.026+',  # These might be different version of 26+
            '1': 'SP.026+',
            '3': 'SP.026+',
        }

        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026+', '3'), ('SP.026', ''),
                    ('SP.025', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list), expected)

        lowest_service_pack = 'SP.024'
        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026+', '3'), ('SP.026', ''),
                    ('SP.025', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list, lowest_service_pack),
                         expected)

        lowest_service_pack = 'SP.025'
        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026+', '3'), ('SP.026', ''),
                    ('SP.025', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list, lowest_service_pack),
                         expected)

        lowest_service_pack = 'SP.026'
        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026+', '3'), ('SP.026', '')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list, lowest_service_pack),
                         expected)

        lowest_service_pack = 'SP.026+'
        expected = [('SP.027', '0'), ('SP.026+', '1'), ('SP.026+', '2'), ('SP.026+', '3')]
        self.assertEqual(calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list, lowest_service_pack),
                         expected)

    def test_service_pack_with_marked_versions(self):
        service_pack_list = {'SP.027': {
            'pi_bluetooth': 'B.2.4',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        },
            'SP.026': {
                'pi_bluetooth': 'B.2.4',
                'pi_config': 'C.1.0',
                'pi_hmi': 'H.3.9',
                'pi_logging': 'L.2.4',
                'pi_monitor': 'M.2.3',
                'pi_network': 'N.5.0',
                'pi_runner': 'R.5.9',
                'pi_security': 'S.1.7'
            }
        }

        test_service_versions = {
            'pi_bluetooth': 'B.2.4 *',
            'pi_config': 'C.1.0',
            'pi_hmi': 'H.3.9',
            'pi_logging': 'L.2.4',
            'pi_monitor': 'M.2.3',
            'pi_network': 'N.5.2',
            'pi_runner': 'R.5.9',
            'pi_security': 'S.1.7'
        }
        self.assertEqual(calculate_service_pack(test_service_versions, service_pack_list), 'SP.027')

    def test_service_pack_release_notes(self):
        """
        (fill in here)
        """
        self.maxDiff = None

        content_of_versions = """
2022.03.11
(SP.28)
Updates on HMI to show Service Pack as SP, instead of showing image version.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.4.0 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.6.1 - pi_runner
    S.1.7 - pi_security

2022.03.07
2.4.1 (SP.27)
Networking service now starts from zero connections each startup, to get clean onto corp.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth (old note here as a test of cleanly getting just the service name)
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security

2022.03.02
2.4.0 (SP.26)
Networking service now has a new certificate to connect to corp WiFi, that expires March 2, 2023
(line2)

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.0 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security
"""
        expected_result = {
            'SP.028': {
                'date': '2022.03.11',
                'image': '',
                'note': 'Updates on HMI to show Service Pack as SP, instead of showing image version.',
            },
            'SP.027': {
                'date': '2022.03.07',
                'image': '2.4.1',
                'note': 'Networking service now starts from zero connections each startup, to get clean onto corp.',
            },
            'SP.026': {
                'date': '2022.03.02',
                'image': '2.4.0',
                'note': """Networking service now has a new certificate to connect to corp WiFi, that expires March 2, 2023
(line2)""",
            }
        }
        actual_result = extract_service_pack_release_notes(content_of_versions)
        self.assertEqual(actual_result, expected_result)

    def test_service_pack_target_and_rollback(self):
        """
        (fill in here)
        """
        self.maxDiff = None

        content_of_versions = """
Target SP = SP.28
Rollback SP = SP.27
2022.03.11
(SP.28)
Updates on HMI to show Service Pack as SP, instead of showing image version.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.4.0 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.6.1 - pi_runner
    S.1.7 - pi_security

2022.03.07
2.4.1 (SP.27)
Networking service now starts from zero connections each startup, to get clean onto corp.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth (old note here as a test of cleanly getting just the service name)
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security
"""

        expected_result = 'SP.028'
        actual_result = extract_service_pack_target(content_of_versions)
        self.assertEqual(actual_result, expected_result)

        expected_result = 'SP.027'
        actual_result = extract_service_pack_target(content_of_versions, sp_type='Rollback SP')
        self.assertEqual(actual_result, expected_result)

    def test_get_services_from_list_of_sp(self):
        service_pack_list = {
            'SP.027': {
                'pi_bluetooth': 'B.2.8',
                'pi_config': 'C.1.4',
            },
            'SP.026': {
                'pi_bluetooth': 'B.2.6',
                'pi_config': 'C.1.2',
            },
            'SP.025': {
                'pi_bluetooth': 'B.2.6',
                'pi_config': 'C.1.0',
            },
            'SP.024': {
                'pi_bluetooth': 'B.2.0',
            },
        }

        expected = {}
        actual = get_services_from_list_of_sp(['SP.028'], service_pack_list)
        self.assertEqual(expected, actual)

        expected = {'pi_bluetooth': {'B.2.0': ''}}
        actual = get_services_from_list_of_sp(['SP.024'], service_pack_list)
        self.assertEqual(expected, actual)

        expected = {
            'pi_bluetooth': {'B.2.8': ''},
            'pi_config': {'C.1.4': ''},
        }
        actual = get_services_from_list_of_sp(['SP.027'], service_pack_list)
        self.assertEqual(expected, actual)

        expected = {
            'pi_bluetooth': {'B.2.8': '', 'B.2.6': ''},
            'pi_config': {'C.1.4': '', 'C.1.2': ''},
        }
        actual = get_services_from_list_of_sp(['SP.027', 'SP.026'], service_pack_list)
        self.assertEqual(expected, actual)

        expected = {
            'pi_bluetooth': {'B.2.8': '', 'B.2.6': ''},
            'pi_config': {'C.1.4': '', 'C.1.2': '', 'C.1.0': ''},
        }
        actual = get_services_from_list_of_sp(['SP.027', 'SP.026', 'SP.025'], service_pack_list)
        self.assertEqual(expected, actual)

        # be able to handle the output of calculate_rings_vs_service_pack
        expected = {
            'pi_bluetooth': {'B.2.8': '', 'B.2.6': ''},
            'pi_config': {'C.1.4': '', 'C.1.2': '', 'C.1.0': ''},
        }
        actual = get_services_from_list_of_sp([('SP.027', ''), ('SP.026', ''), ('SP.025', '')], service_pack_list)
        self.assertEqual(expected, actual)

    def test_extract_service_pack_build_list(self):
        content_of_versions = ''
        expected = {}
        actual = extract_service_pack_build_list(content_of_versions)
        self.assertEqual(expected, actual)

        content_of_versions = """
(SP.58)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Increment screen grab, even if there are errors on sending the image
Logging change to report and clear the profile log
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.7 - pi_logging - Logging change to report and clear the profile log
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.10 - pi_runner
    S.2.2 - pi_security
    s.1.1 - pi_settings
"""
        expected = {'SP.058': {'pi_bluetooth': 'B.2.8',
             'pi_config': 'C.1.3',
             'pi_hmi': 'H.5.2',
             'pi_logging': 'L.3.7',
             'pi_monitor': 'M.2.8',
             'pi_network': 'N.7.9',
             'pi_organization': 'O.1.2',
             'pi_runner': 'R.9.10',
             'pi_security': 'S.2.2',
             'pi_settings': 's.1.1'}}
        actual = extract_service_pack_build_list(content_of_versions)
        self.assertEqual(expected, actual)

    def test_is_version_less_than_another_version(self):
        previous_ring_ringed_version = '1.0.0'
        ringed_service_version = '1.0.0'
        expected = False
        actual = is_version_less_than_another_version(previous_ring_ringed_version, ringed_service_version)
        self.assertEqual(expected, actual)

        previous_ring_ringed_version = '1.0.0'
        ringed_service_version = '1.0.1'
        expected = True
        actual = is_version_less_than_another_version(previous_ring_ringed_version, ringed_service_version)
        self.assertEqual(expected, actual)

        previous_ring_ringed_version = '1.0.0'
        ringed_service_version = '1.0.2'
        expected = True
        actual = is_version_less_than_another_version(previous_ring_ringed_version, ringed_service_version)
        self.assertEqual(expected, actual)

        previous_ring_ringed_version = '1.0.10'
        ringed_service_version = '1.0.2'
        expected = False
        actual = is_version_less_than_another_version(previous_ring_ringed_version, ringed_service_version)
        self.assertEqual(expected, actual)


# End of file
