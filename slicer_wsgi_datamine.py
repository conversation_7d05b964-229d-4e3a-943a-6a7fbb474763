# A datamine for slicer page services

service = "datamine"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/datamine.py

using:
sudo vi /var/www/html/datamine.py

It also requires:

sudo vi /etc/httpd/conf.d/python-datamine.conf
----- start copy -----
WSGIScriptAlias /datamine /var/www/html/datamine.py
----- end copy -----

sudo chown apache:apache /var/www/html/datamine.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import datamine; print(datamine.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/datamine

https://slicer.cardinalhealth.net/datamine?siteid=PR005

https://slicer.cardinalhealth.net/datamine?serial=100000002a5da842

https://slicer.cardinalhealth.net/datamine?monitorNot=M.1.2

2022.06.19
Troubleshoot device at PR005:
https://slicer.cardinalhealth.net/datamine?serial=1000000090d8c2f3,category=landaily


on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python -m unittest slicer_wsgi_datamine



"""

import copy
import datetime
import traceback
import json
import os
import sys
import time

import unittest

# for unittest to work on mac
# sudo pip3 install --upgrade pip
# pip3 install numpy
import numpy

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:  # for unittest to work
    import numpy

    import dashboard
    import datadrop
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# Globals
color_red_warning = "(255, 0, 0, 0.3)"
color_yellow_caution = "(255, 255, 100, 0.3)"
color_green = "(0, 255, 0, 0.3)"
color_clear = "(0, 0, 0, 0.0)"
color_purple = "(255, 0, 255, 0.3)"

color_feature_not_available = color_purple

s_all_pi_services = sorted(
    ['pi_beyondtrust', 'pi_bluetooth', 'pi_config', 'pi_hmi', 'pi_logging', 'pi_monitor', 'pi_network',
     'pi_organization', 'pi_runner', 'pi_security', 'pi_settings', 'pi_thirdparty'])

s_months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec']


# ====================================
def dump_html_data_dictionary():
# ====================================
    body = ''

    data_dictionary = build_data_dictionary()

    keys_to_show = []
#        body += json.dumps(data_dictionary['data_items'][data_item])
    # {"source": "forced", "header": "forced", "title": "forced", "description_short": "", "needs_trust": true, "to_plot_as_number": "", "display": "", "help": "The configuration assigned force of a screen resolution setting.", "filter": "forced=(forced)", "thresholds": "", "sort": ""}
    for data_item in sorted(data_dictionary['data_items'].keys()):
        for key in data_dictionary['data_items'][data_item].keys():
            if not key in keys_to_show:
                keys_to_show.append(key)


    body += '<table border="1" cellpadding="5">'

    body += '<tr>'
    body += '<td>'
    body += '<B>'
    body += 'data_item'
    body += '</B>'
    body += '</td>'
    for key in sorted(keys_to_show):
        body += '<td>'
        body += '<B>'
        body += key
        body += '</B>'
        body += '</td>'
    body += '</tr>'

    for data_item in sorted(data_dictionary['data_items'].keys()):
        body += '<tr>'
        body += '<td>'
        body += data_item
        body += '</td>'
        for key in sorted(keys_to_show):
            body += '<td>'
            if key in data_dictionary['data_items'][data_item]:
                body += str(data_dictionary['data_items'][data_item][key])
            body += '</td>'
        body += '</tr>'
    body += '</table>'

    return body

# ====================================
def convert_from_now_to_human(time_now):
    # ====================================
    the_year = time_now[0:4]
    the_month = int(time_now[4:6])
    the_day = time_now[6:8]
    the_hour = int(time_now[9:11])
    the_half_day = 'am'

    if the_hour > 12:
        the_hour = the_hour - 12
        the_half_day = 'pm'
    elif the_hour == 12:
        the_half_day = 'pm'
    elif the_hour == 0:
        the_hour = 12
        the_half_day = 'am'

    return_value = s_months[the_month - 1] + ' ' + str(int(the_day)) + ', ' + the_year + ' ' + str(
        the_hour) + the_half_day

    return return_value


# ====================================
def convert_from_now_to_just_utc_with_offset(time_to_now, offset_hours):
    # ====================================
    date_and_hour = time_to_now.split('(')[1].replace(')', '')

    if True:
        # python 2 and 3
        date_time = datetime.datetime.strptime(date_and_hour, '%Y%m%d.%H') + datetime.timedelta(hours=offset_hours)
    else:
        # python 3 only
        time_seconds = datetime.datetime.strptime(date_and_hour, '%Y%m%d.%H').timestamp()
        time_seconds += offset_hours * 3600
        date_time = datetime.datetime.fromtimestamp(time_seconds)

    date_return = "{:04d}".format(int(date_time.year)) + "{:02d}".format(int(date_time.month)) + "{:02d}".format(
        int(date_time.day))
    hour_return = "{:02d}".format(int(date_time.hour))

    return_value = date_return + '.' + hour_return

    return return_value


# ====================================
def convert_from_now_to_just_hours(time_to_now):
    # ====================================
    leading_item = time_to_now.split('(')[0].strip()

    if leading_item:
        return_value = leading_item
    else:
        return_value = '0h'

    return return_value


# ====================================
def extract_reportable_from_raw(raw_value, to_plot_as_number):
    # ====================================
    reportable = '(none)'

    if ')(' in raw_value:
        split_values = raw_value.replace('(', '').split(')')

        if to_plot_as_number == 'second_value':
            if len(split_values) > 1:
                raw_number = float(split_values[1])
                if raw_number < 0:
                    raw_number = 0
                reportable = raw_number
        else:
            if len(split_values) > 0:
                raw_number = float(split_values[0])
                if raw_number < 0:
                    raw_number = 0
                reportable = raw_number
    else:
        if to_plot_as_number == 'nic_to_number':
            reportable = 0
            if raw_value == 'int':
                reportable = 1
            if raw_value == 'ext':
                reportable = 2

        elif to_plot_as_number == 'bitrate':
            # like '54%20Mb/s'
            raw_number = 1
            try:
                if raw_value:
                    raw_number = float(raw_value.strip().replace('%20', ' ').split()[0])
                    if 'Kb/s' in raw_value:
                        raw_number *= 1000
                    if 'Mb/s' in raw_value:
                        raw_number *= 1000000
                    if 'Gb/s' in raw_value:
                        raw_number *= 1000000000
            except:
                raw_number = 2
                pass
            reportable = raw_number

            if raw_number > 2:
                reportable = raw_number / 1000000

        elif 'divideby_' in to_plot_as_number:
            try:
                raw_number = float(raw_value.replace('%20', ' ').replace('_', ' ').split()[
                                       0])  # make it work for one minute load value

                raw_number = raw_number / float(to_plot_as_number.replace('divideby_', ''))

                reportable = raw_number
            except:
                pass

        else:
            # default is float of the first value in the string of underscored values
            if raw_value:
                # print ('report_value2', report_value)
                reportable = float(raw_value.replace('%20', ' ').replace('_', ' ').split()[
                                       0])  # make it work for one minute load value

    return reportable


# ====================================
def build_data_dictionary(s_seconds_for_current=60 * 4):
    # ====================================
    # This is all the possible s_ items that come in from the pi_runner
    #     that were /dev/shm/shared_ on the pi

    return_value = {}
    return_value['s_all_pi_services'] = s_all_pi_services

    return_value['data_items'] = {}

    # key: The label to use inside our table definitions, in this code file, to pull this item
    #       This is also the default name for the value to pull from the 'built' content made at tag:20220404a
    # source: use this, instead of the name (key), to look up the value to be shown
    # default: set the initial value, if other than empty is wanted
    # parse: use this to know how to extract a value from the source
    #       loadavg_parse:  Pull just the value in front of the first underscore, like x from 'x_y_z'
    #       time_since:     Use the given data as the time.time(), and calc a difference from current time.time()
    # format:
    #       human_time:     convert seconds into human readable time

    # header: The label to use at the top of a table column, displaying these values (old style was key 'header')
    # help: The float over tooltip help text
    # description: The full (paragraphs) of description of the item
    # Filter: The filter to be applied when the user clicks on the value shown (old filter_to_use)
    #           When adding a filter item, be sure to add code to "tag: Implement Filters here"
    #           Default filter of '(key)' will be set, if no specific filter defined.
    # runner_raw: Get the data from the runner data drop raw item listed
    # thresholds: [color, value, color,...]
    # value_color: {value:color}

    # for plotting:
    # title: The display name to use when showing this in a table to be used for plotting (if blank, do not plot)
    # description_short: To be used for humans to quickly understand the data content (the clickable in datamine section table)

    # scale: The preferred vertical scale to use when plotting this (linear, log)
    # minimum: For plotting, use this as the "minimum"
    # maximum: For plotting, use this as the "maximum"
    # plot_algorithm: any special function that needs applied to the data when plotting
    # to_plot_as_number: options like ['','bitrate','divideby_1000000000']

    # sort: The sorting algorithm to use when organizing this data
    #   bitrate
    #   human_time
    #   integer
    #   first_split_dash_string
    #   third_split_dash_integer
    #   float_empty_to_top
    #   dotted_version
    # display: The algorithm to apply to convert raw data into a displayed result

    # -----------------------------------------------------------------------------
    # make a block of all the networking items, for concentrated cleanup efforts.
    #   Keep them in the display order too, for one to one alignment (sorted on description_short)
    # -----------------------------------------------------------------------------
    return_value['data_items']['network_nic_change_count'] = {
        'source': 'network_nic_change_count',
        'header': 'Nic<br>change',
        'title': 'Network Interface Changes',
        'description_short': 'Network Interface Changes',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_network_nic_change_count',
        'help': 'network Interface change counts',
        'plot_info': {'y_label': 'Network Interface Changes since imaged'},
        'description_long': 'Network Interface change counts, since imaged. After changing between internal and external radio, then this number will increase by one.',
        'filter': 'network_connects=(network_connects)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['network_connects'] = {
        'source': 'network_connects',
        'header': 'Network<br>connects',
        'title': 'Network Connection Changes',
        'description_short': 'Network Connection Changes',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'network_connects',
        'help': 'network connection change counts',
        'plot_info': {'y_label': 'Network Connection Changes since imaged'},
        'description_long': 'Network connection change counts, since imaged. If the network drops, and re-connects, then this number will increase by one. This will also happen if the adapter changes (like from internal to external radio)',
        'filter': 'network_connects=(network_connects)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['network_device_changes'] = {
        'source': 'network_device_changes',
        'header': 'Network<br>device',
        'title': 'Network Device Changes',
        'description_short': 'Network Device Changes',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'network_device_changes',
        'help': 'network device change counts',
        'description_long': 'Network device change counts, since imaged. If the network adapter that is in use changes, then this number will increase by one. If no user was unplugging/re-plugging something, and this number changes, then that means that a cable is loose, and randomly unplugging',
        'filter': 'network_device_changes=(network_device_changes)',
        'thresholds': '',
        'sort': '',
        'plot_info': {'y_label': 'counts since imaged'},
    }

    return_value['data_items']['network_device_number'] = {
        'source': 'network_device_number',
        'header': 'Network<br>number',
        'title': 'Network Device Number',
        'description_short': 'Network Device Number',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'network_device_number',
        'help': 'network device number (0=other, 1=none, 2=eth0, 3=wlan0, 4=wlan1)',
        'description_long':
            """Network device number (0=other, 1=none, 2=eth0, 3=wlan0, 4=wlan1)
            If the network device number changes, then that means that a connection came unplugged, or was replugged.
            If it goes between 3 and 4, that means that the external radio USB cable is coming unplugged/plugged.""",
        'plot_info': {'y_label': '0=other, 1=none, 2=eth0, 3=wlan0, 4=wlan1'},
        'filter': 'network_device_number=(network_device_number)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['network_interface'] = {
        'header': 'nic',
        'title': '',
        'description_short': 'Network Interface active',
        'description_long': 'Network Interface active: 0 = na, 1 = internal, 2 = external',
        'runner_raw': 's_network_nic',
        'to_plot_as_number': 'nic_to_number',
        'plot_info': {'y_label': '0 = na, 1 = internal, 2 = external'},
        #        'filter':'s_network_nic=(s_network_nic)'
    }

    return_value['data_items']['radio_resets'] = {
        'header': 'Radio resets',
        'title': 'Radio resets',
        'description_short': 'Network Radio resets',
        'description_long': 'Network Radio resets since device power on. Resets are used anytime the connection becomes stale, as an attempt to get the radio to reconnect. Seeing lots of resets indicates that the radio was not able to connect easliy, may have been in a dead zone, but then later communicated, and delivered this count data after the fact.',
        'runner_raw': 's_network_ext_radio_resets',
        'filter': '',
        'plot_info': {'y_label': 'counts'},
    }

    return_value['data_items']['net_rx_dropped'] = {
        'header': 'RX dropped',
        'title': 'Network Receive Dropped',
        'description_short': 'Network Receive Dropped',
        'description_long': 'Counts of packet drops (not sure what the time frame is, since these are really rare in the first place)',
        'runner_raw': 's_logging_net_rx_dropped',
        'plot_info': {'y_label': 'Counts in a minute'},
        'filter': ''
    }

    return_value['data_items']['net_rx_errors'] = {
        'header': 'RX errors',
        'title': 'Network Receive Errors',
        'description_short': 'Network Receive Errors',
        'description_long': 'Counts of receive errors (not sure what the time frame is, since these are really rare in the first place)',
        'runner_raw': 's_logging_net_rx_errors',
        'plot_info': {'y_label': 'Counts in a minute'},
        'filter': ''
    }

    return_value['data_items']['responsexce'] = {
        'header': 'Response Exceptions',
        'title': 'Response Exceptions',
        'description_short': 'Network Response Exceptions',
        # leave blank, so that it does not show in the data mining list
        'description_long': 'Response Exceptions as a count of occurences in a one minute interval',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'responsexce',
        'help': 'Response Exceptions',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Counts in a minute'},
    }

    return_value['data_items']['responsetime'] = {
        'source': 'Response Time',
        'header': 'Response Time',
        'title': 'Response Time',
        'description_short': 'Network Slicer Response Time',
        'description_long': 'Response Time from the device to Slicer and back, as a logarthmic plot (so that it shows low just as well as high)',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'responsetime',
        'help': 'Response Time',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_info': {'y_label': 'Seconds'},
    }

    return_value['data_items']['net_tx_dropped'] = {
        'header': 'TX dropped',
        'title': 'Network Transmit Dropped',
        'description_short': 'Network Transmit Dropped',
        'description_long': 'Counts of transmit drops (not sure what the time frame is, since these are really rare in the first place)',
        'runner_raw': 's_logging_net_tx_dropped',
        'plot_info': {'y_label': 'Counts in a minute'},
        'filter': ''
    }

    return_value['data_items']['net_tx_errors'] = {
        'header': 'TX errors',
        'title': 'Network Transmit Errors',
        'description_short': 'Network Transmit Errors',
        'description_long': 'Counts of transmit errors (not sure what the time frame is, since these are really rare in the first place)',
        'runner_raw': 's_logging_net_tx_errors',
        'plot_info': {'y_label': 'Counts in a minute'},
        'filter': ''
    }

    return_value['data_items']['networkuse'] = {
        'header': 'Network Use rx',
        'title': 'Network Use rx',
        'description_short': 'Network Use rx',
        'description_long': 'Network Use on receive into the device, as a logarthmic plot (so that it shows low traffic just as well as high traffic)',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'networkuse',
        'help': 'Network Use rx',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_info': {'y_label': 'Bytes'},
    }

    return_value['data_items']['networkuse'] = {
        'header': 'Network Use rx',
        'title': 'Network Use rx',
        'description_short': 'Network Use rx',
        'description_long': 'Network Use on receive into the device, as a logarthmic plot (so that it shows low traffic just as well as high traffic)',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'networkuse',
        'help': 'Network Use rx',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_info': {'y_label': 'Bytes'},
    }

    return_value['data_items']['networkuse_average'] = {
        'header': 'Network Use rx',
        'title': 'Network Use rx average',
        'description_short': 'Network Use rx average',
        'description_long': 'Network Use on receive into the device, as a logarthmic plot (so that it shows low traffic just as well as high traffic)',
        'needs_trust': False,
        'to_plot_as_number': 'unused_marker_1',
        'display': '',
        'runner_raw': 'networkuse',
        'help': 'Network Use rx average',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_algorithm': 'remove_spurious_zero_plot_only_mean',
        'plot_info': {'y_label': 'Bytes'},
    }

    return_value['data_items']['networkusetx'] = {
        'header': 'Network Use tx',
        'title': 'Network Use rx tx',
        'description_short': 'Network Use tx',
        'description_long': 'Network Use on transmit from the device, as a logarthmic plot (so that it shows low traffic just as well as high traffic)',
        'needs_trust': False,
        'to_plot_as_number': 'second_value',
        'display': '',
        'runner_raw': 'networkuse',
        'help': 'Network Use tx',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_info': {'y_label': 'Bytes'},
    }

    return_value['data_items']['wifibitrate'] = {
        'header': 'Rate Mbps',
        'title': 'WiFi Bit Rate Mbps',
        'description_short': 'Network WiFi Bit Rate Mbps',
        'description_long': 'This is the reporting of what the connection speed is between the device, and the currently connected WiFi access point. It does not indicate that we are actually using it all, or even that we could use it all.',
        'runner_raw': 's_logging_wifi_bitrate',
        'display': 'html2text',
        'scale': 'linear',
        'to_plot_as_number': 'bitrate',
        'sort': 'bitrate',
        'filter': '',
        'plot_info': {'y_label': 'Mbps'},
    }

    return_value['data_items']['wififrequency'] = {
        'header': 'Frequency',
        'title': 'WiFi Frequency',
        'description_short': 'Network WiFi Frequency',
        'description_long': '',
        'runner_raw': 's_logging_wifi_frequency',
        'plot_info': {'y_label': 'WiFi channel frequency'},
        'description_long': 'Reporting of the WiFi connection frequency, which indicates which channel is being used. If the channel is changing a lot, then it is likely that the device is moving around, and using many different access points; normal for a cart, not normal for a desk location.',
        'filter': ''
    }

    return_value['data_items']['wifilevel'] = {
        'header': 'Level',
        'title': 'WiFi Level (dBm)',
        'description_short': 'Network WiFi Level (dBm)',
        'description_long': 'This is similar to the WiFi signal, except that the units here are dBm (less negative is better signal)',
        'runner_raw': 's_logging_wifi_level',
        'plot_info': {'y_label': 'WiFi receive power level'},
        'filter': ''
    }

    return_value['data_items']['wifinoise'] = {
        'header': 'Noise',
        'title': 'WiFi Noise',
        'description_short': 'Network WiFi Noise',
        'description_long': 'Reporting of the radio report of WiFi noise. In practice this does not show useful data, but is present here for completeness of the erporting of what data is listed for the interface.',
        'runner_raw': 's_logging_wifi_noise',
        'plot_info': {'y_label': 'WiFi Noise report'},
        'filter': ''
    }

    return_value['data_items']['wifisignal'] = {
        'header': 'Signal',
        'help': 'The wifi signal report.',
        'title': 'WiFi Signal (0 to 100)',
        'description_short': 'Network WiFi Signal (0 to 100)',
        'description_long': 'The WiFi Signal report. 0 = no signal, 30 = barely enough signal, 100 = full signal.',
        'runner_raw': 's_network_signal',
        'minimum': '0',
        'maximum': '100',
        'plot_algorithm': 'remove_spurious_zero',
        'plot_info': {'y_label': 'WiFi Signal Report'},
        'filter': ''
    }

    return_value['data_items']['wifisignal_minimum'] = {
        'header': 'Signal',
        'help': 'The wifi minimum signal report.',
        'title': 'WiFi Signal (0 to 100)',
        'description_short': 'Network WiFi Signal Minimum (0 to 100)',
        'description_long': 'The WiFi Signal Minimum report. 0 = no signal, 30 = barely enough signal, 100 = full signal.',
        'runner_raw': 's_network_signal',
        'minimum': '0',
        'maximum': '100',
        'plot_algorithm': 'remove_spurious_zero_plot_only_minimum',
        'plot_info': {'y_label': 'WiFi Minimum Signal Report'},
        'to_plot_as_number': 'unused_marker_1',  # to set it apart from the one that plots all three
        'filter': ''
    }

    # -----------------------------------------------------------------------------
    # all else:
    # -----------------------------------------------------------------------------
    return_value['data_items']['id'] = {
        'header': 'ID',
        'title': 'ID',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The unique ID for the device.',
        'filter': 'serial=(id)'
    }

    return_value['data_items']['idr'] = {
        'header': 'IDr',
        'title': 'IDr',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The user friendly ID for the device, derived from the unique ID.',
        'filter': 'serial=(id)'
    }

    return_value['data_items']['site_name'] = {
        'source': 'calc_site',
        'header': 'siteID',
        'title': 'siteID',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The inferred site ID, based on looking up the network address in a list of subnet assignments.',
        'filter': 'siteid=(siteID)'
    }

    return_value['data_items']['management_block'] = {
        'source': 'calc_management_block',
        'header': 'managementID',
        'title': 'mgmnt',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The management block assignment (defaults to siteID).',
        'filter': 'mgmntblock=(mgmntblock)'
    }

    return_value['data_items']['tag'] = {
        'source': 'tag',
        'needs_trust': True,
        'header': 'tag',
        'title': 'tag',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned tag for the device.'
    }

    return_value['data_items']['device_collection'] = {
        'source': 'device_collection',
        'needs_trust': True,
        'header': 'device collection',
        'title': 'device collection',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned collection name for the device.'
    }

    return_value['data_items']['device_extra_boots'] = {
        'source': 'device_extra_boots',
        'needs_trust': True,
        'header': 'extra boots',
        'title': 'extra boots',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned extra boots on a boot.'
    }

    return_value['data_items']['device_retired'] = {
        'source': 'device_retired',
        'needs_trust': True,
        'header': 'retired',
        'title': 'retired',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned retired setting.'
    }

    return_value['data_items']['name'] = {
        'source': 'name',
        'needs_trust': True,
        'header': 'name',
        'title': 'name',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned name for the device.'
    }

    return_value['data_items']['ring'] = {
        'source': 'ring',
        'needs_trust': True,
        'header': 'ring',
        'title': 'ring',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The configuration assigned ring for the device.'
    }

    return_value['data_items']['seconds_since'] = {
        'source': 'calc_seconds',
        'needs_trust': False,
        'header': 'Seconds Since',
        'title': 'Seconds Since',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'The number of seconds since this device last checked in. The monitor checks in every 1800 seconds, the runner every 60.',
        'thresholds': ["(0, 255, 0, 0.3)", 1900.0, "(255, 255, 100, 0.3)", s_seconds_for_current + 10,
                       "(255, 0, 0, 0.3)"],
        'sort': 'integer',
        'filter': ''
    }

    return_value['data_items']['seconds_since_human'] = {
        'source': 'calc_seconds_human',
        'needs_trust': False,
        'header': 'Time Since',
        'title': 'Time Since', 'help': 'The monitor checks in every 30 minutes, the runner every 1 minute.',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'sort': 'human_time',
        'filter': ''
    }

    return_value['data_items']['service_pack'] = {
        'source': 'service_pack',
        'needs_trust': False,
        'header': 'Service<br>Pack',
        'title': 'Service<br>Pack',
        'help': 'The service pack level (This is more meaningful than the Base Image version).',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'filter': 'service_pack=(service_pack)'}

    return_value['data_items']['stuckkeys'] = {
        'header': 'High Key events',
        'title': 'High Key events',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'Devices that potentially have stuck keys, as reported by the event count for each high use keycode.',
        'description_long': '',
        'runner_raw': 's_logging_stuckkeys',
        'plot_info': {'y_label': ''},
        'filter': ''
    }

    return_value['data_items']['stuckkeys_human'] = {
        'header': 'Stuck ?',
        'title': 'Stuck ?',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'Devices that potentially have stuck keys, as reported by the event count for each high use keycode.',
        'source': 'calc_stuckkeys_human',
        'filter': ''
    }

    return_value['data_items']['userdevicelastactive'] = {
        'header': 'UDLA',
        'title': 'User Device Last Active',
        'description_short': '',
        # leave blank, so that it does not show in the data mining list, was 'User Device Last Active'
        'runner_raw': 's_logging_userdevicelastactive',
        'plot_info': {'y_label': ''},
        'filter': ''
    }

    return_value['data_items']['configchanges'] = {
        'header': 'config<br>changes',
        'title': 'configchanges',
        'description_short': 'Configuration Changes',
        'runner_raw': 's_logging_configchanges',
        'plot_info': {'y_label': 'counts since imaged'},
        'filter': ''
    }

    return_value['data_items']['configtime'] = {
        'header': 'config<br>time',
        'title': 'configtime',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'runner_raw': 's_logging_configtime',
        'filter': '',
        'sort': 'integer',
        'plot_info': {'y_label': ''},
        'parse': 'time_since'
    }

    return_value['data_items']['configtime_human'] = {
        'header': 'config<br>time',
        'title': 'configtimehuman',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'runner_raw': 's_logging_configtime',
        'filter': '',
        'parse': 'time_since',
        'format': 'human_time',
    }

    return_value['data_items']['USB'] = {
        'header': 'USB',
        'title': 'USB',
        'description_short': 'USB counts',
        'runner_raw': 's_logging_USB',
        'filter': ''
    }

    return_value['data_items']['memfree'] = {
        'header': 'Mem Free',
        'title': 'MemFree (GB)',
        'description_short': 'MemFree (GB)',
        'to_plot_as_number': 'divideby_1000000000',
        'display': 'divideby_1000000000',
        'runner_raw': 'Memory:MemFree',
        'thresholds': ["(255, 255, 100, 0.3)", 0.5, "(0, 0, 0, 0.0)"],
        'filter': ''
    }

    return_value['data_items']['membuffer'] = {
        'header': 'Mem Buffer',
        'title': 'MemBuffer (GB)',
        'description_short': 'MemBuffer (GB)',
        'to_plot_as_number': 'divideby_1000000000',
        'display': 'divideby_1000000000',
        'runner_raw': 'Memory:Buffers',
        'filter': ''
    }

    return_value['data_items']['memcache'] = {
        'header': 'Mem Cache',
        'title': 'MemCache (GB)',
        'description_short': 'MemCache (GB)',
        'to_plot_as_number': 'divideby_1000000000',
        'display': 'divideby_1000000000',
        'runner_raw': 'Memory:Cached',
        'filter': ''
    }

    return_value['data_items']['memtotal'] = {
        'header': 'Mem Total',
        'title': 'MemTotal (GB)',
        'description_short': 'MemTotal (GB)',
        'to_plot_as_number': 'divideby_1000000000',
        'display': 'divideby_1000000000',
        'runner_raw': 'Memory:MemTotal',
        'filter': ''
    }

    for service_name in s_all_pi_services:
        return_value['data_items']['service_versions_' + service_name + '_version'] = {
            'header': service_name,
            'title': '',
            'description_short': '',  # leave blank, so that it does not show in the data mining list
            'service_versions': service_name}

    _ = """
    return_value['data_items']['UserInactive'] = {
        'header':'User',
        'help':'The user inactivity time.',
        'title':'User Inactivty Seconds',
        'description_short':'User Inactivty Seconds',
        'runner_raw':'s_logging_userinactive',
        'filter':''
        }
"""

    return_value['data_items']['ScreenReport'] = {
        'header': 'Screen Report',
        'title': 'Screen Report',
        'description_short': 'Screen Report',
        'help': 'Screen Report',
        'runner_raw': 's_logging_screenreport',
        'sort': 'integer',
        'scale': '',
        'filter': ''
    }

    return_value['data_items']['UserInActive'] = {
        'header': 'User Inactive',
        'title': 'User Inactive',
        'description_short': 'User Inactive',
        'help': 'User inactive time, seconds',
        'runner_raw': 's_logging_userinactive',
        'sort': 'integer',
        'scale': 'log',
        'filter': ''
    }

    return_value['data_items']['UserActive'] = {
        'header': 'User Active',
        'help': 'The user is active mark.',
        'title': 'User is Active',
        'description_short': 'User is Active',
        'runner_raw': 's_logging_userinactive',
        'plot_algorithm': 'value_less_than_360'
    }

    return_value['data_items']['timelocal'] = {
        'header': 'Local time',
        'title': 'Local time reported from the device',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'runner_raw': 'timelocal',
        'filter': ''
    }

    return_value['data_items']['BrowserRestartWanted'] = {
        'header': 'Browser Wants Reset',
        'title': 'Browser Wants Reset',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'help': 'There has been a configuration change, that is not yet applied. Reset the browser to make it be applied.',
        'runner_raw': 'brRsWd',
        'filter': 'brRsWd=(BrowserRestartWanted)'}

    return_value['data_items']['RunnerReportCount'] = {
        'header': 'Runner Reports',
        'help': 'The number of Runner Reports each hour.',
        'title': 'Runner Reports',
        'description_short': 'Runner Reports',
        'runner_raw': 'uptime', 'plot_algorithm': 'count_occurences',
        'filter': ''
    }

    return_value['data_items']['RunnerActive'] = {
        'header': 'Runner Active',
        'help': 'The runner reported in for this hour.',
        'title': 'Runner Active',
        'description_short': 'Runner Active',
        'runner_raw': 'uptime',
        'plot_algorithm': 'count_occurences_non_zero'
    }

    return_value['data_items']['profile'] = {
        'source': 'profile',
        'header': 'profile',
        'title': 'profile',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned set of bookmarks to be shown on the device. Include whitelist, timezone, key restrictions, and kiosk mode settings.',
        'filter': 'profile=(profile)'
    }

    return_value['data_items']['profile_screen_threshold'] = {
        'source': 'profile_screen_threshold',
        'header': 'profile_screen_threshold',
        'title': 'profile_screen_threshold',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned value of the allowed percentage of black at the bottom of the screen.',
        'filter': 'profile_screen_threshold=(profile_screen_threshold)'
    }

    return_value['data_items']['screen_over_threshold'] = {
        'source': 'screen_over_threshold',
        'header': 'screen_over_threshold',
        'title': 'screen_over_threshold',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': '',
        'thresholds': ["(255, 0, 0, 0.0)", 0, "(255, 0, 0, 0.3)"],
        'filter': 'screen_over_threshold=(screen_over_threshold)'
    }


    return_value['data_items']['first_date'] = {
        'source': 'first_date',
        'header': 'first_date',
        'title': 'first_date',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': '',
        'filter': 'first_date=(first_date)'
    }

    return_value['data_items']['first_iot_date'] = {
        'source': 'first_iot_date',
        'header': 'first_iot_date',
        'title': 'first_iot_date',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': '',
        'filter': 'first_iot_date=(first_iot_date)'
    }

    return_value['data_items']['hopper'] = {
        'source': 'hopper',
        'header': 'hopper',
        'title': 'hopper',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration of the wifi hopper setting.',
        'filter': 'hopper=(hopper)'
    }

    return_value['data_items']['coaster'] = {
        'source': 'coaster',
        'header': 'coaster',
        'title': 'coaster',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration of the wifi coaster setting.',
        'filter': 'coaster=(coaster)'
    }

    return_value['data_items']['lan'] = {
        'source': 'lan',
        'header': 'LAN',
        'title': 'lan',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The network connection being used by the device',
        'filter': 'lan=(lan)'
    }

    return_value['data_items']['ring_updates'] = {
        'source': 'ring_updates',
        'header': 'ring<br>updates<br>available',
        'title': 'ring<br>updates<br>available',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The device has updates available. After a change, it may take 5 to 6 minutes for this field to be refreshed.',
        'filter': 'ring_updates=(ring_updates)',
        'thresholds': ["(255, 255, 255, 0.0)", 0.5, "(100, 100, 255, 0.3)"]
    }

    return_value['data_items']['temperature'] = {
        'runner_raw': 'temperature',
        'header': 'Temp',
        'title': 'Temp',
        'description_short': 'CPU Temperature',
        'description_long': 'CPU Temperature in C; above 80 (176F) may cause a shutdown: 60 to 70 (140F to 158F) is warning, above 70 (140F) is concerning.',
        # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'Cpu termperature in C; above 80 (176F) may cause a shutdown: colors are 60 to 70 (140F to 158F) in yellow, above 70 (140F) in red.',
        'filter': '',
        'plot_info': {'y_label': 'degrees C'},
        'thresholds': ["(0, 255, 0, 0.0)", 60.0, "(255, 255, 100, 0.3)", 70.0, "(255, 0, 0, 0.3)"]
    }

    return_value['data_items']['ssid_chan_sig'] = {
        'source': 'ssid_chan_sig',
        'header': 'ssid_chan_sig',
        'title': 'ssid_chan_sig',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The WiFi network name (ssid), channel number, and signal strength. If the connected access point does not broadcast its SSID, then we show (hidden) here. Channel number tells what access point is making the connection. Signal strength is best at 100, and worst at zero. (Sort is based on signal strength)',
        'filter': 'ssid_chan_sig=(ssid_chan_sig)',
        'thresholds': [],
        'sort': 'third_split_dash_integer'
    }

    return_value['data_items']['ssid'] = {
        'source': 'ssid',
        'header': 'ssid',
        'title': 'ssid',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The WiFi network name (ssid). If the connected access point does not broadcast its SSID, then we show (hidden) here.',
        'filter': 'ssid=(ssid)',
        'thresholds': [],
        'value_color': {'corp': "(255, 255, 100, 0.3)"},
        'sort': 'first_split_dash_string'
    }

    return_value['data_items']['IP'] = {
        'source': 'IP',
        'header': 'IP Address',
        'title': 'IP Address',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The network address for the device.',
        'filter': 'IP=(IP)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['bluetooth'] = {
        'source': 'bluetooth',
        'header': 'bluetooth',
        'title': 'bluetooth',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned bluetooth enable setting.',
        'filter': 'bluetooth=(bluetooth)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['Menu'] = {
        'source': 'Menu',
        'header': 'Menu',
        'title': 'Menu',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned special menu enabled setting.',
        'filter': 'Menu=(Menu)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['forced'] = {
        'source': 'forced',
        'header': 'forced',
        'title': 'forced',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned force of a screen resolution setting.',
        'filter': 'forced=(forced)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['zoom'] = {
        'source': 'zoom',
        'header': 'zoom',
        'title': 'zoom',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': True,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The configuration assigned browser zoom level setting.',
        'filter': 'zoom=(zoom)',
        'thresholds': '',
        'sort': 'integer'
    }

    return_value['data_items']['screen'] = {
        'source': 'screen',
        'header': 'screen',
        'title': 'screen',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'The reported screen resolution from the device.',
        'filter': 'screen=(screen)',
        'thresholds': '',
        'sort': 'screen_resolution'
    }

    return_value['data_items']['outage'] = {
        'source': 'outage',
        'header': 'outage',
        'title': 'outage',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'Some services are not running.',
        'filter': 'outage=(outage)',
        'thresholds': ["(255, 255, 255, 0.0)", 0.5, color_red_warning],
        'sort': 'integer'
    }

    return_value['data_items']['inspect_comms'] = {
        'source': 'inspect_comms',
        'header': 'inspect_comms',
        'title': 'inspect_comms',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'Take a look at the comms, to see if this device reporting makes sense',
        'filter': 'inspect_comms=(inspect_comms)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['loadavg'] = {
        'source': 'loadavg',
        'parse': 'loadavg_parse',
        'header': 'loadavg',
        'title': 'loadavg',
        'description_short': 'CPU load average',
        'runner_raw': 'loadavg',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'Recent (minute based) loading of the device',
        'filter': 'loadavg=(loadavg)',
        'thresholds': '',
        'plot_info': {'y_label': 'CPU load average (4)'},
        'sort': ''
    }

    return_value['data_items']['chromium'] = {
        'source': 'chromium',
        'header': 'chromium',
        'title': '',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'chromium',
        'help': 'chromium version',
        'filter': 'chromium=(chromium)',
        'thresholds': '',
        'sort': 'dotted_version'
    }

    return_value['data_items']['network_device_changes'] = {
        'source': 'network_device_changes',
        'header': 'Network<br>device',
        'title': 'Network Device Changes',
        'description_short': 'Network Device Changes',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'network_device_changes',
        'help': 'network device change counts',
        'description_long': 'Network device change counts, since imaged. If the network adapter that is in use changes, then this number will increase by one. If no user was unplugging/re-plugging something, and this number changes, then that means that a cable is loose, and randomly unplugging',
        'filter': 'network_device_changes=(network_device_changes)',
        'thresholds': '',
        'sort': '',
        'plot_info': {'y_label': 'counts since imaged'},
    }

    return_value['data_items']['kernel'] = {
        'source': 'kernel',
        'header': 'kernel',
        'title': 'kernel',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'kernel',
        'help': 'kernel version',
        'filter': 'kernel=(kernel)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['device'] = {
        'source': 'device',
        'default': 'raspberrypi',
        'header': 'device',
        'title': 'device',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': '',
        'help': 'The device type',
        'filter': 'device=(device)',
        'thresholds': '',
        'sort': ''
    }

    return_value['data_items']['network_corp_cert'] = {
        'header': 'corp cert',
        'title': '',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'runner_raw': 's_network_corp_cert',
        'sort': 'float_empty_to_top',
        #        'filter':'s_network_corp_cert=(s_network_corp_cert)'
    }

    return_value['data_items']['thirdparty_report'] = {
        'header': 'thirdparty_report',
        'title': 'thirdparty_report',
        'description_short': '',  # leave blank, so that it does not show in the data mining list
        'needs_trust': False,
        'runner_raw': 's_thirdparty_report',
    }

    return_value['data_items']['disk'] = {
        'source': '',
        'header': 'disk',
        'title': 'disk',
        'description_short': 'Disk space usage',  # leave blank, so that it does not show in the data mining list
        'description_long': 'disk use on the disk that is closest to 100% space usage (there are multiple disks/partitions in the system)',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'disk_use',
        'help': 'disk use',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'plot_info': {'y_label': 'Percent usage'},
    }

    return_value['data_items']['inode'] = {
        'source': '',
        'header': 'inode_use',
        'title': 'inode_use',
        'description_short': 'Disk inode usage',  # leave blank, so that it does not show in the data mining list
        'description_long': 'inode use on the disk that is closest to 100% space usage (there are multiple disks/partitions in the system)',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'inode_use',
        'help': 'inode_use',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'plot_info': {'y_label': 'Percent usage'},
    }

    return_value['data_items']['s_bluetooth_connected'] = {
        'header': 's_bluetooth_connected',
        'title': 's_bluetooth_connected',
        'description_short': 'Bluetooth Connected',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_bluetooth_connected',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': 'log',
        'plot_info': {'y_label': 'Seconds connected'},
    }

    return_value['data_items']['s_bluetooth_selected'] = {
        'header': 's_bluetooth_selected',
        'title': 's_bluetooth_selected',
        'description_short': 'Bluetooth Selected',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_bluetooth_selected',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Count of times selected'},
    }

    return_value['data_items']['s_bluetooth_specials'] = {
        'header': 's_bluetooth_specials',
        'title': 's_bluetooth_specials',
        'description_short': 'Bluetooth Specials',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_bluetooth_specials',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Count of occurences'},
    }

    return_value['data_items']['s_logging_HID'] = {
        'header': 's_logging_HID',
        'title': 's_logging_HID',
        'description_short': 'Logging HID',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_logging_HID',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Count of occurences'},
    }

    return_value['data_items']['s_logging_RS6000'] = {
        'header': 's_logging_RS6000',
        'title': 's_logging_RS6000',
        'description_short': 'Logging RS6000',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 's_logging_RS6000',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Count of occurences'},
    }

    return_value['data_items']['timediff'] = {
        'header': 'timediff',
        'title': 'timediff',
        'description_short': 'Time Difference',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'timediff',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'Seconds'},
    }

    return_value['data_items']['uptime'] = {
        'header': 'uptime',
        'title': 'uptime',
        'description_short': 'Uptime',  # leave blank, so that it does not show in the data mining list
        'description_long': '',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'runner_raw': 'uptime',
        'help': '',
        'filter': '',
        'thresholds': '',
        'sort': '',
        'scale': '',
        'plot_info': {'y_label': 'seconds'},
    }

    return_value['data_items']['browseruptime'] = {
        'source': 'browseruptime',
        'header': 'browseruptime',
        'title': 'browseruptime',
        'description_short': 'Browser uptime seconds',
        'runner_raw': 'browseruptime',
        'needs_trust': False,
        'to_plot_as_number': '',
        'display': '',
        'help': 'Browser uptime in seconds',
        'filter': 'browseruptime=(browseruptime)',
        'thresholds': '',
        'plot_info': {'y_label': 'seconds'},
        'sort': ''
    }

    # template:
    #    Define content here
    #    Then go down to "built = {}" in reports.do_make_body_GET(), and make the item have a value
    #    Then go to ??? and implement its filter

    _template = """
    return_value['data_items']['template'] = {
        'source':'template',
        'header':'template',
        'title':'template',
        'description_short':'', # leave blank, so that it does not show in the data mining list
        'description_long':'',
        'needs_trust':False,
        'to_plot_as_number':'',
        'display':'divideby_1000000000',
        'runner_raw':'template',
        'help':'template',
        'filter':'template=(template)',
        'thresholds':'',
        'sort':'',
        'scale':'',
        'plot_info':{'y_label':''},
        }
    """

    # ------------------------------------
    # do a default filter, if none given
    # ------------------------------------
    for key in return_value['data_items'].keys():
        if not 'filter' in return_value['data_items'][key]:
            return_value['data_items'][key]['filter'] = key + '=(' + key + ')'

    description_short_keys = {}

    for key in return_value['data_items'].keys():
        if 'description_short' in return_value['data_items'][key]:
            description_short = return_value['data_items'][key]['description_short']
            if description_short:
                description_short_keys[description_short] = key

    description_short_sorted_keys = []
    for item in sorted(description_short_keys.keys()):
        description_short_sorted_keys.append(description_short_keys[item])

    return_value['description_short_sorted_keys'] = description_short_sorted_keys

    return return_value


# ----------------------------
def build_title_from_name_and_serial(name, serial):
    # ----------------------------
    return_value = serial

    if name:
        return_value = serial + ' (' + name + ')'

    return return_value


# ----------------------------
def extract_from_data_dictionary(data_dictionary, item_name, key_name, to_plot_as_number=''):
    # ----------------------------
    return_value = None

    if 'data_items' in data_dictionary:
        for key in data_dictionary['data_items'].keys():
            if 'runner_raw' in data_dictionary['data_items'][key]:
                if data_dictionary['data_items'][key]['runner_raw'] == item_name:
                    to_plot_as_number_of_this_one = ''
                    if 'to_plot_as_number' in data_dictionary['data_items'][key]:
                        to_plot_as_number_of_this_one = data_dictionary['data_items'][key]['to_plot_as_number']

                    # I now have the correct record, now see if I can give back what was asked for out of this record
                    if to_plot_as_number_of_this_one == to_plot_as_number:
                        if key_name in data_dictionary['data_items'][key]:
                            return_value = data_dictionary['data_items'][key][key_name]

    return return_value


# ----------------------------
def extract_lan_summary_data_from_raw_network_data(raw_network_data):
    # ----------------------------
    table_data = {}  # rows, then columns
    help_data = {}

    for d in raw_network_data:
        day_hour = d['day_hour']
        day = day_hour.split('.')[0]
        hour = day_hour.split('.')[1]

        if not day in table_data:
            table_data[day] = {}

        if not 'ValuesList' in table_data[day]:
            table_data[day]['ValuesList'] = {}

        if not hour in table_data[day]:
            table_data[day][hour] = ''

        if not day in help_data:
            help_data[day] = {}

        if not hour in help_data[day]:
            help_data[day][hour] = ''

        raw_values = d['raw']
        for raw_value in raw_values:
            if raw_value:
                if not raw_value in table_data[day]['ValuesList']:
                    table_data[day]['ValuesList'][raw_value] = 0
                table_data[day]['ValuesList'][raw_value] += 1

                if help_data[day][hour]:
                    help_data[day][hour] += ', '
                help_data[day][hour] += raw_value
            try:
                table_data[day][hour] += 1
            except:
                # was empty, now make it a number
                table_data[day][hour] = 1

    for day in table_data:
        table_data[day]['day_summary'] = ', '.join(table_data[day]['ValuesList'])

    return {'table_data': table_data, 'help_data': help_data}


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def get_current_raw_day_hour_TS():
    # ====================================
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    return TS[0:8], TS[8:10], TS


# ====================================
def get_time_array_with_days(ordered_hours):
    # ====================================

    current_time = ordered_hours[-1]

    return_value = []

    for other_time in ordered_hours:
        time_offset = get_time_offset(current_time, other_time)
        if time_offset:
            return_value.append(time_offset + ' (' + other_time + ')')
        else:
            return_value.append('(' + other_time + ')')

    return return_value


# ====================================
def get_time_offset(current_time, other_time):
    # ====================================
    current_day = current_time.split('.')[0]
    current_hour = current_time.split('.')[1]

    other_day = other_time.split('.')[0]
    other_hour = other_time.split('.')[1]

    current_clock = datetime.datetime.strptime(current_day + current_hour, '%Y%m%d%H')
    other_clock = datetime.datetime.strptime(other_day + other_hour, '%Y%m%d%H')

    total_seconds = (other_clock - current_clock).total_seconds()

    days_delta = int(total_seconds / (24 * 3600))
    hour_delta = int((total_seconds - days_delta * 24 * 3600) / 3600)

    return_value = ''
    if days_delta:
        return_value += str(days_delta) + 'd'

    if hour_delta:
        if return_value:
            return_value += ' '
        return_value += str(hour_delta) + 'h'

    return return_value


# ====================================
def gap_raw(x_values, y1_values, y2_values, y3_values, extend_to=''):
    # ====================================
    s_hour_next = {'00': '01',
                   '01': '02',
                   '02': '03',
                   '03': '04',
                   '04': '05',
                   '05': '06',
                   '06': '07',
                   '07': '08',
                   '08': '09',
                   '09': '10',
                   '10': '11',
                   '11': '12',
                   '12': '13',
                   '13': '14',
                   '14': '15',
                   '15': '16',
                   '16': '17',
                   '17': '18',
                   '18': '19',
                   '19': '20',
                   '20': '21',
                   '21': '22',
                   '22': '23',
                   '23': '00'}

    x_gapped = []
    y1_gapped = []
    y2_gapped = []
    y3_gapped = []

    x_values_d = {}
    for item in range(0, len(x_values)):
        try:
            y1 = y1_values[item]
        except:
            y1 = ''
        try:
            y2 = y2_values[item]
        except:
            y2 = ''
        try:
            y3 = y3_values[item]
        except:
            y3 = ''

        x_values_d[x_values[item]] = {'y1': y1, 'y2': y2, 'y3': y3}

    if extend_to:
        if not extend_to == x_values[len(x_values) - 1]:
            x_values_d[extend_to] = {'y1': '', 'y2': '', 'y3': ''}

        trimmed_x_values = {}
        found_keys = x_values_d.keys()
        for key_found in found_keys:
            if key_found > extend_to:
                pass
            else:
                trimmed_x_values[key_found] = x_values_d[key_found]
        x_values_d = trimmed_x_values

    previous = ''
    for key in sorted(x_values_d):
        if previous:
            prev_day = previous.split('.')[0]
            prev_hour = previous.split('.')[1]

            now_day = key.split('.')[0]
            now_hour = key.split('.')[1]

            escape_count = 0
            while (prev_day != now_day) or (prev_hour != now_hour):
                escape_count += 1
                if escape_count > 1000:
                    break
                next_day = prev_day
                next_hour = s_hour_next[prev_hour]
                if next_hour == '00':
                    full_clock = datetime.datetime.strptime(next_day, '%Y%m%d')
                    full_clock += datetime.timedelta(days=1)
                    next_day = full_clock.strftime('%Y%m%d')

                if (next_day != now_day) or (next_hour != now_hour):
                    x_values_d[next_day + '.' + next_hour] = {'y1': '', 'y2': '', 'y3': ''}
                # keep going
                prev_day = next_day
                prev_hour = next_hour

        previous = key

    for key in sorted(x_values_d):
        x_gapped.append(key)
        y1_gapped.append(x_values_d[key]['y1'])
        y2_gapped.append(x_values_d[key]['y2'])
        y3_gapped.append(x_values_d[key]['y3'])

    any_found = False
    for index in range(0, len(y1_gapped)):
        if y1_gapped[index]:
            any_found = True
            break
    if not any_found:
        y1_gapped = []

    any_found = False
    for index in range(0, len(y2_gapped)):
        if y2_gapped[index]:
            any_found = True
            break
    if not any_found:
        y2_gapped = []

    any_found = False
    for index in range(0, len(y3_gapped)):
        if y3_gapped[index]:
            any_found = True
            break
    if not any_found:
        y3_gapped = []

    return x_gapped, y1_gapped, y2_gapped, y3_gapped


# ====================================
def prune_raw(x_values, y_values):
    # ====================================
    x_pruned = []
    y_pruned = []
    last_index_copied = 0

    if x_values:
        x_pruned.append(x_values[0])
        y_pruned.append(y_values[0])
        for index_into in range(1, len(x_values) - 1):
            try:
                if y_values[index_into] != y_values[index_into - 1]:
                    if last_index_copied != index_into - 1:
                        x_pruned.append(x_values[index_into - 1])
                        y_pruned.append(y_values[last_index_copied])

                    x_pruned.append(x_values[index_into])
                    y_pruned.append(y_values[index_into])
                    last_index_copied = index_into
            except:
                pass

        if last_index_copied != len(x_values) - 1:
            x_pruned.append(x_values[-1])
            y_pruned.append(y_values[-1])

    return x_pruned, y_pruned


# ====================================
def build_summary_plot_values(serial_numbers_to_combine, plot_algorithm, to_plot_as_number, add_hours_offset=True,
                              extend_to=''):
    # ====================================

    x_value = 0
    x_values = []
    y1_values = []
    y2_values = []
    y3_values = []

    first_exception = ''

    is_ready_to_ignore_a_zero_count = 0

    count_in_day_hour = {}

    all_raw1_xy = {}
    all_contributors = {}
    all_day_hour = {}

    for serial_part in serial_numbers_to_combine.keys():
        all_raw1_xy[serial_part] = {}

        _ = """
    Maybe take each device within the hour, and create an average number, then totally that up for all devices within the hour?
    It then becomes a utilization number, where 1.0 = always used in the hour to 0.5 is half used in the hour

        """

        for d in serial_numbers_to_combine[serial_part]['raw_data']:
            count_in_day_hour[serial_part] = {}
            day_hour = d['day_hour']

            all_day_hour[day_hour] = True
            if not day_hour in count_in_day_hour[serial_part]:
                count_in_day_hour[serial_part][day_hour] = 0

            if not day_hour in all_raw1_xy[serial_part]:
                all_raw1_xy[serial_part][day_hour] = []
                # all_raw2_xy[day_hour] = []
            if not day_hour in all_contributors:
                all_contributors[day_hour] = {}

            for raw_value in d['raw']:
                reportable = extract_reportable_from_raw(raw_value, to_plot_as_number)

                if reportable != '(none)':
                    if 'value_less_than_' in plot_algorithm:
                        try:
                            the_threshold = float(plot_algorithm.replace('value_less_than_', ''))
                            if reportable < the_threshold:
                                reportable = 1
                                if not serial_part in all_contributors[day_hour]:
                                    all_contributors[day_hour][serial_part] = True
                            else:
                                reportable = 0
                        except:
                            pass

                    elif 'remove_spurious_zero' in plot_algorithm:
                        if reportable == 0:
                            if is_ready_to_ignore_a_zero_count < 2:
                                reportable = '(none)'
                                is_ready_to_ignore_a_zero_count += 1
                        else:
                            is_ready_to_ignore_a_zero_count = 0

                if reportable != '(none)':
                    all_raw1_xy[serial_part][day_hour].append(reportable)
                    count_in_day_hour[serial_part][day_hour] += 1

            if plot_algorithm == 'count_occurences':
                # convert the hour list into a count
                all_raw1_xy[serial_part][day_hour] = [len(all_raw1_xy[serial_part][day_hour])]

            if plot_algorithm == 'count_occurences_non_zero':
                # convert the hour list into a count
                if len(all_raw1_xy[serial_part][day_hour]) != 0:
                    all_raw1_xy[serial_part][day_hour] = [1]
                else:
                    all_raw1_xy[serial_part][day_hour] = [0]

    # combine for all serials
    serial_keys = serial_numbers_to_combine.keys()
    if len(serial_keys) > 1:
        for day_hour in sorted(all_day_hour.keys()):
            average_for_serials = []
            for serial_part in serial_numbers_to_combine.keys():
                raw_values = []
                if day_hour in all_raw1_xy[serial_part]:
                    for value_kept in all_raw1_xy[serial_part][day_hour]:
                        raw_values.append(value_kept)  # * count_in_day_hour[serial_part][day_hour])

                if raw_values:
                    n_array = numpy.array(raw_values)
                    average_for_serials.append(n_array.mean())

            if average_for_serials:
                x_values.append(day_hour)
                n_array = numpy.array(average_for_serials)
                y1_values.append(str(n_array.sum()))
                y2_values.append(str(n_array.sum()))
                y3_values.append(str(n_array.sum()))

    else:
        for serial_part in serial_numbers_to_combine.keys():
            for day_hour in sorted(all_day_hour.keys()):
                raw_values = []
                if day_hour in all_raw1_xy[serial_part]:
                    for value_kept in all_raw1_xy[serial_part][day_hour]:
                        raw_values.append(value_kept)  # * count_in_day_hour[serial_part][day_hour])

                if raw_values:
                    x_values.append(day_hour)
                    n_array = numpy.array(raw_values)
                    if 'plot_only_minimum' in plot_algorithm:
                        #                        y1_values.append(str(n_array.min()))
                        y2_values.append(str(n_array.min()))
                    #                        y3_values.append(str(n_array.min()))
                    elif 'plot_only_mean' in plot_algorithm:
                        #                        y1_values.append(str(n_array.mean()))
                        #                        y2_values.append(str(n_array.mean()))
                        y3_values.append(str(n_array.mean()))
                    else:
                        y1_values.append(str(n_array.max()))
                        y2_values.append(str(n_array.min()))
                        y3_values.append(str(n_array.mean()))

    # look at adding in any missing hours, with null data, to show the gaps
    if x_values:
        x_values, y1_values, y2_values, y3_values = gap_raw(x_values, y1_values, y2_values, y3_values,
                                                            extend_to=extend_to)

        if add_hours_offset:
            x_values = get_time_array_with_days(x_values)

    return x_values, y1_values, y2_values, y3_values


# ====================================
def build_chart(config, x_values, y1_values, y2_values, y3_values):
    # ====================================
    body = ''

    y_axis_title = config['y_label']
    x_axis_title = "-days -hours since data collected, (UTC date.hour of data collection)"
    description_long = config['description_long']

    # versions in a drop down: https://www.chartjs.org/docs/latest/samples/scale-options/titles.html

    # https://www.chartjs.org/
    body += '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>'

    body += '<center>'
    body += '<br>'
    body += '<B>' + config['name_to_show'] + '</B>'
    body += '<br><br>'
    body += '<B>' + config['show_title'] + '</B>'  # ' SUMMARY'
    body += '<br><br>'  # + 'extend_to ' + extend_to
    body += '</center>'

    body += '<center>'
    body += '<table border="1" cellpadding="5">'
    body += '<tr>'

    if True:
        data_string = ''
        data_string += 'var x_values = ["' + '","'.join(x_values) + '"];' + '\n'

        if config['show_minmax'] == '1':
            if y1_values:
                data_string += 'var y1_values = [' + ','.join(y1_values) + '];' + '\n'

            if y2_values:
                data_string += 'var y2_values = [' + ','.join(y2_values) + '];' + '\n'

        if y3_values:
            data_string += 'var y3_values = [' + ','.join(y3_values) + '];' + '\n'

        body += '<td>'
        body += '<canvas id="myChart_' + 'day_hour' + '" width="1200" height="600"></canvas>'
        body += '<script>'
        body += data_string
        body += 'var ctx = document.getElementById("myChart_' + 'day_hour' + '");'

        vertical_axes_items = []

        if config['minimum']:
            vertical_axes_items.append('suggestedMin:' + config['minimum'])

        if config['maximum']:
            vertical_axes_items.append('suggestedMax:' + config['maximum'])

        ticks_adder = ''
        if vertical_axes_items:
            ticks_adder = ', ' + ','.join(vertical_axes_items)

        vertical_adder = ''
        if config['vertical_scale']:
            vertical_adder = ', type:' + config['vertical_scale']

        # test page
        # https://slicer.cardinalhealth.net/datamine?serial=10000000e3669edf,runner_raw=loadavg,view=summary,plot_algorithm=,minimum=,maximum=,scale=,to_plot_as_number=

        # https://stackoverflow.com/questions/27910719/in-chart-js-set-chart-title-name-of-x-axis-and-y-axis

        label_adder = ''
        # https://www.chartjs.org/docs/latest/axes/labelling.html
        # https://www.chartjs.org/docs/latest/samples/scale-options/titles.html
        label_adder = ', title:{display:true,font:{size:20},text:"' + y_axis_title + '"}'

        options_string = ''
        if ticks_adder or vertical_adder or label_adder:
            options_string = ',options:{scales:{x:{' + \
                             'title:{display:true,font:{size:20},text:"' + x_axis_title + '"},' + \
                             'ticks:{maxRotation:90, minRotation:90,autoSkip:true,maxTicksLimit:20}},y:{display:true' + vertical_adder + ticks_adder + label_adder + '}' + '}}'

        body += 'var myChart=new Chart(ctx,{type:"line"' + options_string + ',data:{labels:x_values,datasets:['

        legends_to_show = []
        if config['show_minmax'] == '1':
            if y1_values:
                legends_to_show.append('{data: y1_values,label: "' + 'max' + '",borderColor: "#3e95cd",fill: false}')

            if y2_values:
                legends_to_show.append('{data: y2_values,label: "' + 'min' + '",borderColor: "#8e5ea2",fill: false}')

        if y3_values:
            legends_to_show.append('{data: y3_values,label: "' + 'average' + '",borderColor: "#000000",fill: false}')

        body += ','.join(legends_to_show)

        body += ']}});'
        body += '</script>'
        body += '</td>'

    body += '</td>'
    body += '</tr>'

    body += '</table>'

    body += '<br>' + description_long.replace('. ',
                                              '.<br>')  # make it multi line on sentences, so that it does not run wider than the plot, we hope.

    body += '</center>'

    return body


# ====================================
def build_runner_summary_view(query_items, days_to_show):
    # ====================================
    body = '(nothing built)'

    data_dictionary = build_data_dictionary()

    if days_to_show:
        oldest_time = time.time() - days_to_show * 24 * 60 * 60
    else:
        oldest_time = None

    config = {'show_title': '',
              'name_to_show': '',
              'show_minmax': '1',
              'scale': 'linear',
              'vertical_scale': '',
              'minimum': '',
              'maximum': '',
              'plot_algorithm': '',
              'to_plot_as_number': '',
              'chart_version': '',
              'y_label': '',
              'description_long': '',
              'view_format': 'chart',
              }

    # read from the input query
    for key in config.keys():
        if key in query_items:
            config[key] = query_items[key]

    try:
        x_values = []
        y1_values = []
        y2_values = []
        y3_values = []

        if 'dashboard_raw' in query_items:
            # url = https://slicer.cardinalhealth.net/datamine?dashboard_raw=disk_warning,view=summary
            the_item = query_items['dashboard_raw']

            siteid = ''
            if 'siteid' in query_items:
                siteid = query_items['siteid']
            raw_content = dashboard.get_raw_content(siteid)
            plot_values = dashboard.get_plot_values_from_raw(raw_content, the_item, oldest_time=oldest_time)

            x_values = plot_values['x_values']
            y1_values = plot_values['y1_values']
            y2_values = plot_values['y2_values']
            y3_values = plot_values['y3_values']

            if days_to_show:
                config['show_title'] = "up to " + str(days_to_show) + " day"

        if 'chart_version' in query_items:
            config['chart_version'] = query_items['chart_version']

        if 'runner_raw' in query_items:
            # url = https://slicer.cardinalhealth.net/datamine?serial=1000000060a246e2,runner_raw=s_logging_userinactive,view=summary,plot_algorithm=,minimum=,maximum=,scale=,to_plot_as_number=
            # https://slicer.cardinalhealth.net/datamine?serial=100000000448eb77,runner_raw=loadavg,view=summary,plot_algorithm=,minimum=,maximum=,scale=,to_plot_as_number=

            # calculated defaults
            the_item = query_items['runner_raw']
            config['show_title'] = the_item

            description_short = extract_from_data_dictionary(data_dictionary, the_item, 'description_short',
                                                             to_plot_as_number=config['to_plot_as_number'])
            if description_short:
                config['show_title'] = description_short

            plot_info = extract_from_data_dictionary(data_dictionary, the_item, 'plot_info')
            if plot_info:
                if 'y_label' in plot_info:
                    config['y_label'] = plot_info['y_label']

            description_long = extract_from_data_dictionary(data_dictionary, the_item, 'description_long')
            if description_long:
                config['description_long'] = description_long

            # calculated configs
            if config['scale'] == 'log':
                config[
                    'vertical_scale'] = '"logarithmic"'  # https://www.chartjs.org/docs/latest/samples/scales/log.html

            serial_numbers_to_combine = {}
            if '+' in query_items['serial']:
                for serial_part in query_items['serial'].split('+'):
                    serial_numbers_to_combine[serial_part] = {}
                config['name_to_show'] = 'Collection of ' + str(len(serial_numbers_to_combine)) + ' devices'

            elif 'md5_' in query_items['serial']:
                try:
                    for serial_part in open('/dev/shm/collection_serials/' + query_items['serial'].replace('md5_', ''),
                                            'r').read().split('+'):
                        serial_numbers_to_combine[serial_part] = {}
                except:
                    pass

                config['name_to_show'] = 'Collection of ' + str(len(serial_numbers_to_combine)) + ' devices'
            else:
                the_serial = query_items['serial']
                item = 'device_name_' + the_serial
                the_name = datastore.get_value(item)
                config['name_to_show'] = build_title_from_name_and_serial(the_name, the_serial)
                serial_numbers_to_combine[query_items['serial']] = {}

            for serial_part in serial_numbers_to_combine.keys():
                serial_numbers_to_combine[serial_part]['raw_data'] = datadrop.get_all_raw_data_for_service('runner',
                                                                                                           serial_part,
                                                                                                           the_item)

            day, hour, TS = get_current_raw_day_hour_TS()
            extend_to = day + '.' + hour

            x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine,
                                                                                  config['plot_algorithm'],
                                                                                  config['to_plot_as_number'],
                                                                                  extend_to=extend_to)

        if config['view_format'] == 'chart':
            body = build_chart(config, x_values, y1_values, y2_values, y3_values)

        if 'data_' in config['view_format']:
            body = build_data_report(config, x_values, y1_values, y2_values, y3_values)


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body


# ====================================
def build_data_report(config, x_values, y1_values, y2_values, y3_values):
    # ====================================
    body = '<br><br>'

    body += '<center>'
    body += '<table border="1" cellpadding="5">'

    utc_offset = '-4'

    body += '<tr>'
    body += '<td>'
    body += '<B>'
    body += 'time from now'
    body += '</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>'
    body += 'UTC date.hour'
    body += '</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>'
    body += 'UTC' + utc_offset
    body += '</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>'
    body += 'maximum'
    body += '</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>'
    body += 'minimum'
    body += '</B>'
    body += '</td>'
    body += '<td>'
    body += '<B>'
    body += 'average'
    body += '</B>'
    body += '</td>'
    body += '</tr>'

    for index_to_pull in range(len(x_values) - 1, -1, -1):
        body += '<tr>'
        body += '<td>'
        body += convert_from_now_to_just_hours(x_values[index_to_pull])
        body += '</td>'
        body += '<td>'
        body += convert_from_now_to_just_utc_with_offset(x_values[index_to_pull], 0)
        body += '</td>'
        body += '<td>'
        body += convert_from_now_to_human(
            convert_from_now_to_just_utc_with_offset(x_values[index_to_pull], int(utc_offset)))
        body += '</td>'
        body += '<td>'
        body += str(y1_values[index_to_pull])
        body += '</td>'
        body += '<td>'
        body += str(y2_values[index_to_pull])
        body += '</td>'
        body += '<td>'
        body += str(y3_values[index_to_pull])
        body += '</td>'
        body += '</tr>'

    body += '</table>'
    body += '</center>'

    return body


# ====================================
def build_runner_raw_view(query_items):
    # ====================================
    to_plot_as_number = ''
    if 'to_plot_as_number' in query_items:
        to_plot_as_number = query_items['to_plot_as_number']

    scale = 'linear'
    if 'scale' in query_items:
        scale = query_items['scale']

    vertical_scale = ''
    if scale == 'log':
        vertical_scale = '"logarithmic"'  # https://www.chartjs.org/docs/latest/samples/scales/log.html

    vertical_adder = ''
    if vertical_scale:
        vertical_adder = ', type:' + vertical_scale

    ticks_adder = ''
    options_string = ''
    if vertical_adder:
        # https://stackoverflow.com/questions/22064577/limit-labels-number-on-chart-js-line-chart
        options_string = ',options:{scales:{xAxes:[{ticks:{autoSkip:true,maxTicksLimit:20}}],yAxes:[{display: true' + vertical_adder + ticks_adder + '}]}}'

    if not options_string:
        options_string = ',options:{scales:{xAxes:[{ticks:{autoSkip:true,maxTicksLimit:20}}]}}'

    raw_data = datadrop.get_all_raw_data_for_service('runner', query_items['serial'], query_items['runner_raw'])

    body = ''

    the_serial = query_items['serial']
    the_runner_raw = query_items['runner_raw']

    item = 'device_name_' + the_serial
    the_name = datastore.get_value(item)

    body += '<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>'
    body += '<center>'
    body += '<br>'
    body += '<B>' + build_title_from_name_and_serial(the_name, the_serial) + '</B>'
    body += '<br><br>'
    body += '<B>' + the_runner_raw + '</B>'
    body += '<br><br>'
    body += '</center>'

    body += '<center>'
    body += '<table border="1" cellpadding="5">'

    for d in raw_data:
        day_hour = d['day_hour']

        body += '<tr>'
        body += '<td>'
        body += day_hour
        body += '</td>'

        report_string = ''
        x_value = 0
        x_values = []
        y1_values = []
        y2_values = []
        y3_values = []

        for report_value in d['raw']:
            if report_string:
                report_string += '\n'
            report_string += report_value
            x_value += 1
            x_values.append(str(x_value))
            try:
                # loadavg : report_value = "0.42_0.40_0.43_2/271_22872"
                # networkuse : report_value = "(8284)(2989)(60)"
                if ')(' in report_value:
                    split_values = report_value.replace('(', '').split(')')
                    if len(split_values) > 0:
                        y1_values.append(split_values[0])
                    if len(split_values) > 1:
                        y2_values.append(split_values[1])
                else:
                    if to_plot_as_number == 'bitrate':
                        # like '54%20Mb/s'
                        raw_number = 1
                        try:
                            if report_value:
                                raw_number = float(report_value.strip().replace('%20', ' ').split()[0])
                                if 'Kb/s' in report_value:
                                    raw_number *= 1000
                                if 'Mb/s' in report_value:
                                    raw_number *= 1000000
                                if 'Gb/s' in report_value:
                                    raw_number *= 1000000000
                        except:
                            raw_number = 2
                            pass
                        if raw_number > 2:
                            y1_values.append(str(raw_number / 1000000))
                    else:
                        # default is float of the first value in the string of underscored values
                        if report_value:
                            y1_values.append(str(float(report_value.replace('%20', ' ').replace('_', ' ').split()[
                                                           0])))  # make it work for one minute load value

            except:
                y1_values.append('0')

        body += '<td>'
        body += str(len(report_string.split('\n'))) + ' values' + '<br>'
        body += '<select name="' + 'values' + '" id="' + 'values' + '">'
        for key_name in report_string.split('\n'):
            # body += '<option hidden>' + key_name + '</option>'
            body += '<option>' + key_name.replace('%20', ' ') + '</option>'
        body += '</select>'
        body += '</td>'

        if y1_values:
            x_pruned, y_pruned = prune_raw(x_values, y1_values)

            if len(x_pruned) > 10:
                # just show all the data
                x_pruned, y_pruned = x_values, y1_values

            data_string = ''
            data_string += 'var x_values = ["' + '","'.join(x_pruned) + '"];' + '\n'
            data_string += 'var y1_values = [' + ','.join(y_pruned) + '];' + '\n'
            if y2_values:
                data_string += 'var y2_values = [' + ','.join(y2_values) + '];' + '\n'

            body += '<td>'
            body += '<canvas id="myChart_' + day_hour + '" width="400" height="200"></canvas>'
            body += '<script>'
            body += data_string
            body += 'var ctx = document.getElementById("myChart_' + day_hour + '");'
            body += 'var myChart=new Chart(ctx,{type:"line"' + options_string + ',data:{labels:x_values,datasets:['
            body += '{data: y1_values,label: "' + the_runner_raw + '",borderColor: "#3e95cd",fill: false}'
            if y2_values:
                body += ',{data: y2_values,label: "' + '2' + '",borderColor: "#8e5ea2",fill: false}'
            body += ']}});'
            body += '</script>'
            body += '</td>'
            body += '</tr>'

    body += '</table>'
    body += '</center>'

    return body


# ====================================
def nice_number_sort(input_list):
    # ====================================
    # take ['157','36','56']
    # make ['36','56','157']

    number_list = []
    for item in input_list:
        try:
            number_list.append(int(item))
        except:
            number_list.append('')

    return_list = []
    for num in sorted(number_list):
        return_list.append(str(num))

    return return_list


# ====================================
def make_body_POST(environ):
    # ====================================
    return ''


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    try:
        showed_results = False
        hour_list = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15',
                     '16', '17', '18', '19', '20', '21', '22', '23']

        body = ''

        body += """
    <script>

    function URLjump(jumpLocation) {
        location.href = jumpLocation;
    }

    </script>
        """

        name_to_show = "Home"
        url_to_use = make_home_url_from_environ(environ)
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

        query_items = {}
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]

        if ('serial' in query_items and ('runner_raw' in query_items)) or ('dashboard_raw' in query_items):
            showed_results = True

            the_view = 'raw'
            if 'view' in query_items:
                the_view = query_items['view']

            if the_view == 'raw':
                body += build_runner_raw_view(query_items)
            elif the_view == 'summary':
                body += build_runner_summary_view(query_items, days_to_show=15)
            elif the_view == 'summaryall':
                body += build_runner_summary_view(query_items, days_to_show=None)
            else:
                body += '<br><br><br><center>(view not available: ' + the_view + ')</center>'

        if 'serial' in query_items and 'category' in query_items:
            showed_results = True
            the_serial = query_items['serial']
            the_category = query_items['category']

            item = 'device_name_' + the_serial
            the_name = datastore.get_value(item)

            if the_category == 'wifidaily':
                body += '<center>'
                body += 'ID: ' + query_items['serial']
                body += '</center>'

                body += '<center>'
                body += '<br>'
                body += '<B>' + build_title_from_name_and_serial(the_name, the_serial) + '</B>'
                body += '<br>'
                body += '<B>Day-by-day Wifi Channel Report</B>'
                body += '<br><br>'
                # now_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
                # body += now_time
                body += '</center>'

                table_data = {}  # rows, then columns
                help_data = {}
                serial_number = query_items['serial']
                raw_data = datadrop.get_all_raw_data_for_service('network', serial_number, 'chan')
                #                d = extract_lan_summary_data_from_raw_network_data(raw_network_data)

                for d in raw_data:
                    day_hour = d['day_hour']
                    day = day_hour.split('.')[0]
                    hour = day_hour.split('.')[1]

                    if not day in table_data:
                        table_data[day] = {}

                    if not 'ValuesList' in table_data[day]:
                        table_data[day]['ValuesList'] = {}

                    if not hour in table_data[day]:
                        table_data[day][hour] = ''

                    if not day in help_data:
                        help_data[day] = {}

                    if not hour in help_data[day]:
                        help_data[day][hour] = ''

                    raw_values = d['raw']
                    for raw_value in raw_values:
                        if raw_value:
                            table_data[day]['ValuesList'][raw_value] = 1

                            if help_data[day][hour]:
                                help_data[day][hour] += ', '
                            help_data[day][hour] += raw_value
                        try:
                            table_data[day][hour] += 1
                        except:
                            # was empty, now make it a number
                            table_data[day][hour] = 1

                for day in table_data:
                    table_data[day]['Channels'] = ', '.join(nice_number_sort(table_data[day]['ValuesList']))

                body += '<center>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<td>'
                body += '<center>UTC<br>Date / Hour</center>'
                body += '</td>'

                columns = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14',
                           '15', '16', '17', '18', '19', '20', '21', '22', '23', 'Channels']
                for column in columns:
                    body += '<td>'
                    body += column
                    body += '</td>'
                body += '</tr>'

                for day in sorted(table_data):
                    body += '<tr>'
                    body += '<td>'
                    body += day
                    body += '</td>'

                    for column in columns:
                        help_to_use = ''
                        if day in help_data:
                            if column in help_data[day]:
                                help_to_use = help_data[day][column]

                        body += '<td title="' + help_to_use + '">'
                        if column in table_data[day]:
                            body += str(table_data[day][column])
                        body += '</td>'
                    body += '</tr>'

                body += '</table>'
                body += '</center>'

            elif the_category == 'landaily':
                body += '<center>'
                body += 'ID: ' + query_items['serial']
                body += '</center>'

                body += '<center>'
                body += '<br>'
                body += '<B>' + build_title_from_name_and_serial(the_name, the_serial) + '</B>'
                body += '<br>'
                body += '<B>Day-by-day Lan Connection Report</B>'
                body += '<br><br>'
                # now_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
                # body += now_time
                body += '</center>'

                table_data = {}  # rows, then columns
                help_data = {}
                serial_number = query_items['serial']
                raw_data = datadrop.get_all_raw_data_for_service('network', serial_number, 'lan')

                for d in raw_data:
                    day_hour = d['day_hour']
                    day = day_hour.split('.')[0]
                    hour = day_hour.split('.')[1]

                    if not day in table_data:
                        table_data[day] = {}

                    if not 'ValuesList' in table_data[day]:
                        table_data[day]['ValuesList'] = {}

                    if not hour in table_data[day]:
                        table_data[day][hour] = ''

                    if not day in help_data:
                        help_data[day] = {}

                    if not hour in help_data[day]:
                        help_data[day][hour] = ''

                    raw_values = d['raw']
                    for raw_value in raw_values:
                        if raw_value:
                            table_data[day]['ValuesList'][raw_value] = 1

                            if help_data[day][hour]:
                                help_data[day][hour] += ', '
                            help_data[day][hour] += raw_value
                        try:
                            table_data[day][hour] += 1
                        except:
                            # was empty, now make it a number
                            table_data[day][hour] = 1

                for day in table_data:
                    table_data[day]['Lans'] = ', '.join(table_data[day]['ValuesList'])

                body += '<center>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<td>'
                body += '<center>UTC<br>Date / Hour</center>'
                body += '</td>'

                columns = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14',
                           '15', '16', '17', '18', '19', '20', '21', '22', '23', 'Lans']
                for column in columns:
                    body += '<td>'
                    body += column
                    body += '</td>'
                body += '</tr>'

                for day in sorted(table_data):
                    body += '<tr>'
                    body += '<td>'
                    body += day
                    body += '</td>'

                    for column in columns:
                        help_to_use = ''
                        if day in help_data:
                            if column in help_data[day]:
                                help_to_use = help_data[day][column]

                        body += '<td title="' + help_to_use + '">'
                        if column in table_data[day]:
                            body += str(table_data[day][column])
                        body += '</td>'
                    body += '</tr>'

                body += '</table>'
                body += '</center>'

        if not showed_results:
            body += '<center>'
            body += 'Not enough specification given to perform the datamine request' + '<br><br>'
            body += 'Instead, here is a nice display of the data_items:' + '<br><br>'
            body += '</center>'

            body += dump_html_data_dictionary()


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    page_allowed = False
    if permissions.permission_prefix_allowed(environ, service + '_'):
        page_allowed = True

    if 'dashboard_raw' in query_items:
        page_allowed = True

    if page_allowed:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_no_pruning(self):
        """
        (fill in here)
        """

        x_values = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
        y1_values = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']

        x_pruned_expected = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
        y_pruned_expected = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']

        x_pruned, y_pruned = prune_raw(x_values, y1_values)
        self.assertEqual(x_pruned, x_pruned_expected)
        self.assertEqual(y_pruned, y_pruned_expected)

    def test_middle_pruning(self):
        """
        (fill in here)
        """
        x_values = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
        y1_values = ['1', '1', '1', '1', '1', '1', '7', '8', '9', '10']

        x_pruned_expected = ['1', '6', '7', '8', '9', '10']
        y_pruned_expected = ['1', '1', '7', '8', '9', '10']

        x_pruned, y_pruned = prune_raw(x_values, y1_values)
        self.assertEqual(x_pruned, x_pruned_expected)
        self.assertEqual(y_pruned, y_pruned_expected)

    def test_double_pruning(self):
        """
        (fill in here)
        """
        x_values = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
        y1_values = ['1', '1', '1', '10', '10', '10', '7', '8', '9', '10']

        x_pruned_expected = ['1', '3', '4', '6', '7', '8', '9', '10']
        y_pruned_expected = ['1', '1', '10', '10', '7', '8', '9', '10']

        x_pruned, y_pruned = prune_raw(x_values, y1_values)
        self.assertEqual(x_pruned, x_pruned_expected)
        self.assertEqual(y_pruned, y_pruned_expected)

    def test_filling_gaps_in_x(self):
        x_values = ['20220304.10', '20220304.12']
        y1_values = ['1', '10']
        y2_values = ['1', '10']
        y3_values = ['1', '10']

        x_expected = ['20220304.10', '20220304.11', '20220304.12']
        y1_expected = ['1', '', '10']
        y2_expected = ['1', '', '10']
        y3_expected = ['1', '', '10']

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

        x_values = ['20220305.23', '20220306.01']
        y1_values = ['1', '10']
        y2_values = ['1', '10']
        y3_values = ['1', '10']

        x_expected = ['20220305.23', '20220306.00', '20220306.01']
        y1_expected = ['1', '', '10']
        y2_expected = ['1', '', '10']
        y3_expected = ['1', '', '10']

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

        x_values = ['20220630.23', '20220701.01']
        y1_values = ['1', '10']
        y2_values = ['1', '10']
        y3_values = ['1', '10']

        x_expected = ['20220630.23', '20220701.00', '20220701.01']
        y1_expected = ['1', '', '10']
        y2_expected = ['1', '', '10']
        y3_expected = ['1', '', '10']

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

        x_values = ['20220630.23', '20220701.03']
        y1_values = ['1', '10']
        y2_values = ['2', '20']
        y3_values = ['3', '30']

        x_expected = ['20220630.23', '20220701.00', '20220701.01', '20220701.02', '20220701.03']
        y1_expected = ['1', '', '', '', '10']
        y2_expected = ['2', '', '', '', '20']
        y3_expected = ['3', '', '', '', '30']

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

        x_values = ['20220630.23', '20220701.03']
        y1_values = ['1', '10']
        y2_values = []
        y3_values = []

        x_expected = ['20220630.23', '20220701.00', '20220701.01', '20220701.02', '20220701.03']
        y1_expected = ['1', '', '', '', '10']
        y2_expected = []
        y3_expected = []

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

    def test_filling_gaps_in_x_with_empty_y1_set(self):
        x_values = ['20220630.23', '20220701.03']
        y1_values = []
        y2_values = []
        y3_values = ['1', '10']

        x_expected = ['20220630.23', '20220701.00', '20220701.01', '20220701.02', '20220701.03']
        y1_expected = []
        y2_expected = []
        y3_expected = ['1', '', '', '', '10']

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

    def test_filling_gaps_in_x_with_extension(self):
        x_values = ['20220304.10', '20220304.12']
        y1_values = ['1', '10']
        y2_values = ['1', '10']
        y3_values = ['1', '10']

        x_expected = ['20220304.10', '20220304.11', '20220304.12']
        y1_expected = ['1', '', '10']
        y2_expected = ['1', '', '10']
        y3_expected = ['1', '', '10']

        extend_to = '20220304.12'

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values,
                                                            extend_to=extend_to)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

        x_values = ['20220304.10', '20220304.12']
        y1_values = ['1', '10']
        y2_values = ['2', '20']
        y3_values = ['3', '0.0222']

        x_expected = ['20220304.10', '20220304.11', '20220304.12', '20220304.13', '20220304.14']
        y1_expected = ['1', '', '10', '', '']
        y2_expected = ['2', '', '20', '', '']
        y3_expected = ['3', '', '0.0222', '', '']

        extend_to = '20220304.14'

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values,
                                                            extend_to=extend_to)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

    def test_extend_to_does_not_overwrite_data(self):
        x_values = ['20220304.10', '20220304.11']
        y1_values = ['1', '10']
        y2_values = ['1', '10']
        y3_values = ['1', '10']

        x_expected = ['20220304.10', '20220304.11']
        y1_expected = ['1', '10']
        y2_expected = ['1', '10']
        y3_expected = ['1', '10']

        extend_to = '20220304.11'

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values,
                                                            extend_to=extend_to)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

    def test_extend_to_as_truncation(self):
        x_values = ['20220708.11', '20220708.12', '20220708.13', '20220708.14']
        y1_values = ['1', '10', '', '']
        y2_values = ['2', '20', '', '']
        y3_values = ['3', '0.0222', '', '']

        x_expected = ['20220708.11', '20220708.12', '20220708.13']
        y1_expected = ['1', '10', '']
        y2_expected = ['2', '20', '']
        y3_expected = ['3', '0.0222', '']

        extend_to = '20220708.13'

        x_gapped, y1_gapped, y2_gapped, y3_gapped = gap_raw(x_values, y1_values, y2_values, y3_values,
                                                            extend_to=extend_to)

        self.assertEqual(x_gapped, x_expected)
        self.assertEqual(y1_gapped, y1_expected)
        self.assertEqual(y2_gapped, y2_expected)
        self.assertEqual(y3_gapped, y3_expected)

    def test_time_history(self):
        now_time = '20220524.17'
        test_time = '20220524.17'
        expected = ''
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

        now_time = '20220524.17'
        test_time = '20220524.16'
        expected = '-1h'
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

        now_time = '20220524.17'
        test_time = '20220524.15'
        expected = '-2h'
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

        now_time = '20220524.17'
        test_time = '20220523.15'
        expected = '-1d -2h'
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

        now_time = '20220501.17'
        test_time = '20220430.14'
        expected = '-1d -3h'
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

        now_time = '20220501.13'
        test_time = '20220430.14'
        expected = '-23h'
        actual = get_time_offset(now_time, test_time)
        self.assertEqual(actual, expected)

    def test_time_array_with_days(self):
        x_values = ['20220524.17', '20220524.18', '20220524.19']
        expected = ['-2h (20220524.17)', '-1h (20220524.18)', '(20220524.19)']
        actual = get_time_array_with_days(x_values)
        self.assertEqual(actual, expected)

    def test_data_combine_for_a_single_serial_single_point(self):
        plot_algorithm = ''
        to_plot_as_number = ''
        serial_numbers_to_combine = {}
        serial_numbers_to_combine['test123'] = {}
        serial_numbers_to_combine['test123']['raw_data'] = []
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['1']})

        expected_x_values = ['20220524.18']
        expected_y1_values = ['1.0']
        expected_y2_values = ['1.0']
        expected_y3_values = ['1.0']
        x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine, plot_algorithm,
                                                                              to_plot_as_number, add_hours_offset=False)

        self.assertEqual(expected_x_values, x_values)
        self.assertEqual(expected_y1_values, y1_values)
        self.assertEqual(expected_y2_values, y2_values)
        self.assertEqual(expected_y3_values, y3_values)

    def test_data_combine_for_a_single_serial_two_points(self):
        plot_algorithm = ''
        to_plot_as_number = ''
        serial_numbers_to_combine = {}
        serial_numbers_to_combine['test123'] = {}
        serial_numbers_to_combine['test123']['raw_data'] = []
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['1', '3']})

        expected_x_values = ['20220524.18']
        expected_y1_values = ['3.0']
        expected_y2_values = ['1.0']
        expected_y3_values = ['2.0']
        x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine, plot_algorithm,
                                                                              to_plot_as_number, add_hours_offset=False)

        self.assertEqual(expected_x_values, x_values)
        self.assertEqual(expected_y1_values, y1_values)
        self.assertEqual(expected_y2_values, y2_values)
        self.assertEqual(expected_y3_values, y3_values)

    def test_data_combine_for_a_single_serial_two_points_two_hours(self):
        plot_algorithm = ''
        to_plot_as_number = ''
        serial_numbers_to_combine = {}
        serial_numbers_to_combine['test123'] = {}
        serial_numbers_to_combine['test123']['raw_data'] = []
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['1', '3']})
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.19', 'raw': ['10', '30']})

        expected_x_values = ['20220524.18', '20220524.19']
        expected_y1_values = ['3.0', '30.0']
        expected_y2_values = ['1.0', '10.0']
        expected_y3_values = ['2.0', '20.0']
        x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine, plot_algorithm,
                                                                              to_plot_as_number, add_hours_offset=False)

        self.assertEqual(expected_x_values, x_values)
        self.assertEqual(expected_y1_values, y1_values)
        self.assertEqual(expected_y2_values, y2_values)
        self.assertEqual(expected_y3_values, y3_values)

    def test_data_combine_for_a_single_serial_single_point_two_serials(self):
        plot_algorithm = ''
        to_plot_as_number = ''
        serial_numbers_to_combine = {}
        serial_numbers_to_combine['test123'] = {}
        serial_numbers_to_combine['test123']['raw_data'] = []
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['1']})
        serial_numbers_to_combine['test456'] = {}
        serial_numbers_to_combine['test456']['raw_data'] = []
        serial_numbers_to_combine['test456']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['3']})

        # Want to see each device have its own average in an hour, added to all other serial averages in that same hour
        expected_x_values = ['20220524.18']
        expected_y1_values = ['4.0']
        expected_y2_values = ['4.0']
        expected_y3_values = ['4.0']
        x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine, plot_algorithm,
                                                                              to_plot_as_number, add_hours_offset=False)

        self.assertEqual(expected_x_values, x_values)
        self.assertEqual(expected_y1_values, y1_values)
        self.assertEqual(expected_y2_values, y2_values)
        self.assertEqual(expected_y3_values, y3_values)

    def test_data_combine_for_a_single_serial_multi_point_two_serials(self):
        plot_algorithm = ''
        to_plot_as_number = ''
        serial_numbers_to_combine = {}
        serial_numbers_to_combine['test123'] = {}
        serial_numbers_to_combine['test123']['raw_data'] = []
        serial_numbers_to_combine['test123']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['1', '3']})
        serial_numbers_to_combine['test456'] = {}
        serial_numbers_to_combine['test456']['raw_data'] = []
        serial_numbers_to_combine['test456']['raw_data'].append({'day_hour': '20220524.18', 'raw': ['3', '5']})

        # Want to see each device have its own average in an hour, added to all other serial averages in that same hour
        expected_x_values = ['20220524.18']
        expected_y1_values = ['6.0']
        expected_y2_values = ['6.0']
        expected_y3_values = ['6.0']
        x_values, y1_values, y2_values, y3_values = build_summary_plot_values(serial_numbers_to_combine, plot_algorithm,
                                                                              to_plot_as_number, add_hours_offset=False)

        self.assertEqual(expected_x_values, x_values)
        self.assertEqual(expected_y1_values, y1_values)
        self.assertEqual(expected_y2_values, y2_values)
        self.assertEqual(expected_y3_values, y3_values)

    def test_extract_lan_summary_data_from_raw_network_data(self):
        raw_network_data = []
        expected = {'table_data': {}, 'help_data': {}}
        actual = extract_lan_summary_data_from_raw_network_data(raw_network_data)
        self.assertEqual(expected, actual)

        # serial_number = '10000000a4de5562'
        # raw_network_data = datadrop.get_all_raw_data_for_service('network', serial_number, 'lan')
        #        raw_network_data = [{'day_hour': '20220926.00', 'raw': []}, {'day_hour': '20220926.01', 'raw': []}, {'day_hour': '20220926.02', 'raw': []}, {'day_hour': '20220926.03', 'raw': []}, {'day_hour': '20220926.04', 'raw': []}, {'day_hour': '20220926.05', 'raw': []}, {'day_hour': '20220926.06', 'raw': []}, {'day_hour': '20220926.07', 'raw': []}, {'day_hour': '20220926.08', 'raw': []}, {'day_hour': '20220926.09', 'raw': []}, {'day_hour': '20220926.10', 'raw': []}, {'day_hour': '20220926.11', 'raw': []}, {'day_hour': '20220926.12', 'raw': []}, {'day_hour': '20220926.13', 'raw': []}, {'day_hour': '20220926.14', 'raw': []}, {'day_hour': '20220926.15', 'raw': []}, {'day_hour': '20220926.16', 'raw': []}, {'day_hour': '20220926.17', 'raw': []}, {'day_hour': '20220926.18', 'raw': []}, {'day_hour': '20220926.19', 'raw': []}, {'day_hour': '20220926.20', 'raw': []}, {'day_hour': '20220926.21', 'raw': []}, {'day_hour': '20220926.22', 'raw': []}, {'day_hour': '20220926.23', 'raw': []}, {'day_hour': '20221102.00', 'raw': []}, {'day_hour': '20221102.01', 'raw': []}, {'day_hour': '20221102.02', 'raw': []}, {'day_hour': '20221102.03', 'raw': []}, {'day_hour': '20221102.04', 'raw': []}, {'day_hour': '20221102.05', 'raw': []}, {'day_hour': '20221102.06', 'raw': []}, {'day_hour': '20221102.07', 'raw': []}, {'day_hour': '20221102.08', 'raw': []}, {'day_hour': '20221102.09', 'raw': []}, {'day_hour': '20221102.10', 'raw': []}, {'day_hour': '20221102.11', 'raw': []}, {'day_hour': '20221102.12', 'raw': []}, {'day_hour': '20221102.13', 'raw': []}, {'day_hour': '20221102.14', 'raw': []}, {'day_hour': '20221102.15', 'raw': []}, {'day_hour': '20221102.16', 'raw': []}, {'day_hour': '20221102.17', 'raw': []}, {'day_hour': '20221102.18', 'raw': []}, {'day_hour': '20221102.19', 'raw': []}, {'day_hour': '20221102.20', 'raw': []}, {'day_hour': '20221102.21', 'raw': []}, {'day_hour': '20221102.22', 'raw': []}, {'day_hour': '20221102.23', 'raw': []}, {'day_hour': '20230722.00', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.01', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.02', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.03', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.04', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.05', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.06', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230722.07', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230722.09', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.10', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.11', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.12', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.13', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.14', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.15', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.16', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.17', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.18', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.19', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.20', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.21', 'raw': ['wlan1']}, {'day_hour': '20230722.22', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230722.23', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.00', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.01', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.02', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.03', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.04', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230723.05', 'raw': ['wlan1']}, {'day_hour': '20230723.06', 'raw': []}, {'day_hour': '20230723.07', 'raw': []}, {'day_hour': '20230723.08', 'raw': []}, {'day_hour': '20230723.09', 'raw': []}, {'day_hour': '20230723.10', 'raw': []}, {'day_hour': '20230723.11', 'raw': []}, {'day_hour': '20230723.12', 'raw': []}, {'day_hour': '20230723.13', 'raw': []}, {'day_hour': '20230723.14', 'raw': []}, {'day_hour': '20230723.15', 'raw': []}, {'day_hour': '20230723.16', 'raw': []}, {'day_hour': '20230723.17', 'raw': []}, {'day_hour': '20230723.18', 'raw': []}, {'day_hour': '20230723.19', 'raw': []}, {'day_hour': '20230723.20', 'raw': []}, {'day_hour': '20230723.21', 'raw': []}, {'day_hour': '20230723.22', 'raw': []}, {'day_hour': '20230723.23', 'raw': []}, {'day_hour': '20230724.00', 'raw': []}, {'day_hour': '20230724.01', 'raw': []}, {'day_hour': '20230724.02', 'raw': []}, {'day_hour': '20230724.03', 'raw': []}, {'day_hour': '20230724.04', 'raw': []}, {'day_hour': '20230724.05', 'raw': []}, {'day_hour': '20230724.06', 'raw': []}, {'day_hour': '20230724.07', 'raw': []}, {'day_hour': '20230724.08', 'raw': []}, {'day_hour': '20230724.09', 'raw': []}, {'day_hour': '20230724.10', 'raw': []}, {'day_hour': '20230724.11', 'raw': []}, {'day_hour': '20230724.12', 'raw': []}, {'day_hour': '20230724.13', 'raw': []}, {'day_hour': '20230724.14', 'raw': []}, {'day_hour': '20230724.15', 'raw': []}, {'day_hour': '20230724.16', 'raw': []}, {'day_hour': '20230724.17', 'raw': []}, {'day_hour': '20230724.18', 'raw': []}, {'day_hour': '20230724.19', 'raw': []}, {'day_hour': '20230724.20', 'raw': []}, {'day_hour': '20230724.21', 'raw': []}, {'day_hour': '20230724.22', 'raw': []}, {'day_hour': '20230724.23', 'raw': ['wlan1']}, {'day_hour': '20230725.00', 'raw': []}, {'day_hour': '20230725.01', 'raw': []}, {'day_hour': '20230725.02', 'raw': []}, {'day_hour': '20230725.03', 'raw': []}, {'day_hour': '20230725.04', 'raw': []}, {'day_hour': '20230725.05', 'raw': []}, {'day_hour': '20230725.06', 'raw': []}, {'day_hour': '20230725.07', 'raw': []}, {'day_hour': '20230725.08', 'raw': []}, {'day_hour': '20230725.09', 'raw': []}, {'day_hour': '20230725.10', 'raw': []}, {'day_hour': '20230725.11', 'raw': []}, {'day_hour': '20230725.12', 'raw': []}, {'day_hour': '20230725.13', 'raw': []}, {'day_hour': '20230725.14', 'raw': []}, {'day_hour': '20230725.15', 'raw': []}, {'day_hour': '20230725.16', 'raw': []}, {'day_hour': '20230725.17', 'raw': []}, {'day_hour': '20230725.18', 'raw': []}, {'day_hour': '20230725.19', 'raw': []}, {'day_hour': '20230725.20', 'raw': []}, {'day_hour': '20230725.21', 'raw': []}, {'day_hour': '20230725.22', 'raw': []}, {'day_hour': '20230725.23', 'raw': []}, {'day_hour': '20230726.00', 'raw': []}, {'day_hour': '20230726.01', 'raw': []}, {'day_hour': '20230726.02', 'raw': []}, {'day_hour': '20230726.03', 'raw': []}, {'day_hour': '20230726.04', 'raw': []}, {'day_hour': '20230726.05', 'raw': []}, {'day_hour': '20230726.06', 'raw': []}, {'day_hour': '20230726.07', 'raw': []}, {'day_hour': '20230726.08', 'raw': []}, {'day_hour': '20230726.09', 'raw': []}, {'day_hour': '20230726.10', 'raw': []}, {'day_hour': '20230726.11', 'raw': []}, {'day_hour': '20230726.12', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.13', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.14', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.15', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.16', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.17', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.18', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.19', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.20', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.21', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.22', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230726.23', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.00', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.01', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.02', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.03', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.04', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.05', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230727.06', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.07', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.08', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.09', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.10', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.11', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230727.12', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230727.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.06', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.07', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.10', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.11', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230728.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.06', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.07', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.10', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.11', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230729.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.06', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.07', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.10', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.11', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230730.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.06', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230731.07', 'raw': ['wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.08', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230731.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.10', 'raw': ['wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.11', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230731.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230731.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.06', 'raw': ['wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.07', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.10', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.11', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.14', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.15', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.16', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.17', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.18', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.19', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.20', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.21', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230801.23', 'raw': ['wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230802.00', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230802.01', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230802.02', 'raw': ['wlan1', 'wlan1']}, {'day_hour': '20230802.03', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.04', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.05', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.06', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.07', 'raw': ['wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.08', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.09', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230802.10', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.11', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.12', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.13', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.14', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.15', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.16', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.17', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.18', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.19', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.20', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.21', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.22', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230802.23', 'raw': ['wlan0', 'wlan0']}, {'day_hour': '20230803.00', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.01', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.02', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.03', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.04', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.05', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.06', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.07', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.08', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.09', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.10', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.11', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.12', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.13', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.14', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.15', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.16', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.17', 'raw': ['wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.18', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.19', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.20', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan0']}, {'day_hour': '20230803.21', 'raw': ['wlan0', 'wlan0', 'wlan0', 'wlan0', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230803.22', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230803.23', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.00', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.01', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.02', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.03', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.04', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.05', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.06', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.07', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.08', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.09', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.10', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.11', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.12', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.13', 'raw': ['wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1', 'wlan1']}, {'day_hour': '20230804.14', 'raw': []}, {'day_hour': '20230804.15', 'raw': []}, {'day_hour': '20230804.16', 'raw': []}, {'day_hour': '20230804.17', 'raw': []}, {'day_hour': '20230804.18', 'raw': []}, {'day_hour': '20230804.19', 'raw': []}, {'day_hour': '20230804.20', 'raw': []}, {'day_hour': '20230804.21', 'raw': []}, {'day_hour': '20230804.22', 'raw': []}, {'day_hour': '20230804.23', 'raw': []}]
        raw_network_data = [{'day_hour': '20230722.00', 'raw': ['wlan1', 'wlan1']}]
        expected = {
            'help_data': {'20230722': {'00': 'wlan1, wlan1'}},
            'table_data': {'20230722': {'00': 2,
                                        'day_summary': 'wlan1',
                                        'ValuesList': {'wlan1': 2}}}}
        actual = extract_lan_summary_data_from_raw_network_data(raw_network_data)
        self.assertEqual(expected, actual)

        raw_network_data = [{'day_hour': '20230722.00', 'raw': ['wlan1', 'wlan1', 'wlan0']}]
        expected = {
            'help_data': {'20230722': {'00': 'wlan1, wlan1, wlan0'}},
            'table_data': {'20230722': {'00': 3,
                                        'day_summary': 'wlan1, wlan0',
                                        'ValuesList': {'wlan0': 1, 'wlan1': 2}}}}
        actual = extract_lan_summary_data_from_raw_network_data(raw_network_data)
        self.assertEqual(expected, actual)

    def test_build_title_from_name_and_serial(self):
        name = 'name'
        serial = 'serial'
        expected = 'serial (name)'
        actual = build_title_from_name_and_serial(name, serial)
        self.assertEqual(expected, actual)

        name = ''
        serial = 'serial'
        expected = 'serial'
        actual = build_title_from_name_and_serial(name, serial)
        self.assertEqual(expected, actual)

    def test_build_data_dictionary_completes(self):
        data_dictionary = build_data_dictionary()

    def test_build_data_dictionary_processing(self):
        data_dictionary = {}

        expected = None
        actual = extract_from_data_dictionary(data_dictionary, 's_logging_configchanges', 'plot_info')
        self.assertEqual(expected, actual)

        data_dictionary['data_items'] = {}

        expected = None
        actual = extract_from_data_dictionary(data_dictionary, 's_logging_configchanges', 'plot_info')
        self.assertEqual(expected, actual)

        # build out one item as an example
        data_dictionary['data_items']['configchanges'] = {
            'header': 'config<br>changes',
            'title': 'configchanges',
            'description_short': 'configchanges',
            'runner_raw': 's_logging_configchanges',
            'plot_info': {'y_label': 'config_changes'},
            'filter': ''
        }

        expected = {'y_label': 'config_changes'}
        actual = extract_from_data_dictionary(data_dictionary, 's_logging_configchanges', 'plot_info')
        self.assertEqual(expected, actual)

        data_dictionary['data_items']['loadavg'] = {
            'header': 'loadavg',
            'title': 'loadavg',
            'description_short': 'loadavg',
            'runner_raw': 'loadavg',
            'plot_info': {'y_label': 'loadavg'},
            'filter': ''
        }

        expected = {'y_label': 'config_changes'}
        actual = extract_from_data_dictionary(data_dictionary, 's_logging_configchanges', 'plot_info')
        self.assertEqual(expected, actual)

        expected = {'y_label': 'loadavg'}
        actual = extract_from_data_dictionary(data_dictionary, 'loadavg', 'plot_info')
        self.assertEqual(expected, actual)

        data_dictionary['data_items']['loadavg2'] = {
            'header': 'loadavg',
            'title': 'loadavg',
            'description_short': 'loadavg',
            'runner_raw': 'loadavg',
            'plot_info': {'y_label': 'loadavg2'},
            'to_plot_as_number': 'second_value',
            'filter': ''
        }

        expected = {'y_label': 'loadavg'}
        actual = extract_from_data_dictionary(data_dictionary, 'loadavg', 'plot_info')
        self.assertEqual(expected, actual)

        expected = {'y_label': 'loadavg2'}
        actual = extract_from_data_dictionary(data_dictionary, 'loadavg', 'plot_info', to_plot_as_number='second_value')
        self.assertEqual(expected, actual)

    def test_extract_reportable_from_raw(self):
        # default action
        raw_value = '10'
        to_plot_as_number = ''
        expected = 10
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # loadavg : report_value = "0.42_0.40_0.43_2/271_22872"
        raw_value = '20_30_40'
        to_plot_as_number = ''
        expected = 20
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # networkuse : report_value = "(8284)(2989)(60)"
        raw_value = '(30)(40)(50)'
        to_plot_as_number = ''
        expected = 30
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # partial default, yet pick which one for parens
        raw_value = '(30)(40)(50)'
        to_plot_as_number = 'second_value'
        expected = 40
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # nic_to_number
        raw_value = 'int'
        to_plot_as_number = 'nic_to_number'
        expected = 1
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        raw_value = 'ext'
        to_plot_as_number = 'nic_to_number'
        expected = 2
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        raw_value = 'junk/other'
        to_plot_as_number = 'nic_to_number'
        expected = 0
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # bitrate
        raw_value = '54%20Mb/s'
        to_plot_as_number = 'bitrate'
        expected = 54
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        raw_value = '54%20Kb/s'
        to_plot_as_number = 'bitrate'
        expected = 0.054
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        raw_value = '54%20Gb/s'
        to_plot_as_number = 'bitrate'
        expected = 54000
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

        # divideby_
        raw_value = '1234567890'
        to_plot_as_number = 'divideby_1000000000'
        expected = 1.23456789
        actual = extract_reportable_from_raw(raw_value, to_plot_as_number)
        self.assertEqual(expected, actual)

    def test_convert_from_now_to_just_hours(self):
        time_to_now = '(20230818.17)'
        expected = '0h'
        actual = convert_from_now_to_just_hours(time_to_now)
        self.assertEqual(expected, actual)

        time_to_now = '-1h (20230818.16)'
        expected = '-1h'
        actual = convert_from_now_to_just_hours(time_to_now)
        self.assertEqual(expected, actual)

    def test_convert_from_now_to_just_utc_with_offset(self):
        time_to_now = '(20230818.17)'
        offset = 0
        expected = '20230818.17'
        actual = convert_from_now_to_just_utc_with_offset(time_to_now, offset)
        self.assertEqual(expected, actual)

        time_to_now = '-1h (20230818.16)'
        offset = 0
        expected = '20230818.16'
        actual = convert_from_now_to_just_utc_with_offset(time_to_now, offset)
        self.assertEqual(expected, actual)

        time_to_now = '-1h (20230818.16)'
        offset = -1
        expected = '20230818.15'
        actual = convert_from_now_to_just_utc_with_offset(time_to_now, offset)
        self.assertEqual(expected, actual)

        time_to_now = '-1h (20230818.00)'
        offset = -1
        expected = '20230817.23'
        actual = convert_from_now_to_just_utc_with_offset(time_to_now, offset)
        self.assertEqual(expected, actual)

    def test_convert_from_now_to_human(self):
        time_now = '20230817.23'
        expected = 'Aug 17, 2023 11pm'
        actual = convert_from_now_to_human(time_now)
        self.assertEqual(expected, actual)

        time_now = '20230817.11'
        expected = 'Aug 17, 2023 11am'
        actual = convert_from_now_to_human(time_now)
        self.assertEqual(expected, actual)

        time_now = '20230817.12'
        expected = 'Aug 17, 2023 12pm'
        actual = convert_from_now_to_human(time_now)
        self.assertEqual(expected, actual)

        time_now = '20230817.00'
        expected = 'Aug 17, 2023 12am'
        actual = convert_from_now_to_human(time_now)
        self.assertEqual(expected, actual)

# End of source file
