# Ignore_for_mass_testing

_ = """
On Dave's PC:

cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/
python3 offline_doc.py

Then, in browser:
http://localhost:8001

"""

import os
from wsgiref.simple_server import make_server
from urllib.parse import unquote

# pip install mkdocs
# pip install mkdocs-material
# pip install Pygments

# Define the folder where MkDocs built the static files
DOCS_DIR = os.path.join(os.getcwd(), "site")

def app(environ, start_response):
    path = environ.get("PATH_INFO", "/").lstrip("/")  # Remove leading slash

    # If the request is for the root ("/"), serve "index.html"
    if not path or path.endswith("/"):
        path += "index.html"

    file_path = os.path.join(DOCS_DIR, unquote(path))

    # Ensure the file exists before serving
    if os.path.exists(file_path) and os.path.isfile(file_path):
        with open(file_path, "rb") as f:
            content = f.read()

        # Set content type based on file extension
        content_type = "text/html"
        if file_path.endswith(".css"):
            content_type = "text/css"
        elif file_path.endswith(".js"):
            content_type = "application/javascript"
        elif file_path.endswith(".png"):
            content_type = "image/png"
        elif file_path.endswith(".jpg") or file_path.endswith(".jpeg"):
            content_type = "image/jpeg"

        start_response("200 OK", [("Content-Type", content_type)])
        return [content]

    # Return 404 if the file doesn't exist
    start_response("404 Not Found", [("Content-Type", "text/plain")])
    return [b"404 Not Found"]

if __name__ == "__main__":
    port = 8001  # Change as needed
    print(f"Serving documentation at http://localhost:{port}/")
    make_server("", port, app).serve_forever()
