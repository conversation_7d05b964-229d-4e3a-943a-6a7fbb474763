# A organization setup for slicer services


service = "organization"
version = service + '.0.7'

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

documentation = """
Public Calls:
    get_config(service)
        datastore
            {'location':'x/y/z'}
        login
            {'method':'ldap', 'servers':['xyz.net']}


"""

_ = """
sudo vi /etc/httpd/conf.d/python-organization.conf
----- start copy -----
WSGIScriptAlias /organization /var/www/html/organization.py
----- end copy -----

sudo chown apache:apache /var/www/html/organization.py

sudo systemctl restart httpd

"""
release_notes = """
2022.11.28
0.6
Add a feature for a username and password list, for user_list login.

2022.11.18
0.1



"""

import copy
import json
import os
import shlex
import subprocess
import sys
import time
import traceback
import unittest

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

# can we try looking up what machines we are on and load a named settings file?
all_files_in_sys_path = {}
for folder in sys.path:
    try:
        if folder:
            files_in_folder = os.listdir(folder)
            for file_found in files_in_folder:
                all_files_in_sys_path[file_found] = folder + '/' + file_found
    except:
        pass
        print(traceback.format_exc().replace("\"", "'"))
        print (folder)

# new way, for install from zip file, that will have all the settings files
import platform
host_name = platform.node() # 'lpec5009slicr05'
settings_file = 'settings_' + host_name + '.py' # settings_lpec5009slicr05.py
for item in all_files_in_sys_path.keys():
    if settings_file == item:
        settings = __import__(settings_file.replace('.py',''))
        break
#    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

service_config = {'home_url': ''}
try:
    service_config = get_config(service)
    make_all_dirs(service_config)

except:
    pass

home_url = service_config['home_url']

s_time_to_stay_logged_in = 2 * 60 * 60  # in seconds

_old = """

This file gets loaded to:
/var/www/html/organization.py

using:
sudo vi /var/www/html/organization.py

"""

# ----------------------------
def get_current_linked_folder():
    # ----------------------------
    folder_listing, f = do_one_command('ls -l /var/www')
    current_linked = extract_current_linked_folder(folder_listing)
    return current_linked

# ----------------------------
def extract_current_linked_folder(contents):
    # ----------------------------
    return_value = ''
    for line in contents.split('\n'):
        if 'html -> ' in line:
            return_value = line.split('html -> ')[1].split('/')[-1]
    return return_value

# ====================================
def get_os_from_release(release):
    # ====================================
    return_value = ''

    for item in release.split('\n'):
        splits = item.split('=')
        if len(splits) > 1:
            if splits[0] == 'ID':
                return_value = splits[1].replace('"', '')

    return return_value


# ====================================
def get_session_adders():
    # ====================================
    return_d = {}

    return_d['extra_body'] = '<head><meta http-equiv="refresh" content="' + str(
        s_time_to_stay_logged_in + 60) + '" ></head></body>'

    return_d['extra_script'] = ''

    return return_d


# ====================================
def get_session_adders_next():
    # ====================================
    _ = """
setup JS to get changes to permissions/current user and if changed, then do

location.reload()



    """

    return_d = {}

    return_d['extra_body'] = """
    <text id="timeout-txt" text-anchor="start" x='""" + str(0) + """' y='""" + str(
        0) + """' font-family="Verdana" font-size='""" + str(12) + """' fill="black"></text>
    </body>
    """

    return_d['extra_script'] = """
<script>
var interval = setInterval(function() {
  checkTimeout();
}, 5000);

// ===========================================================================
function checkTimeout()
{
$.ajax({
    url: getAbsolutePath() + "timeremaining?topoff="+topOffTimeout,
    dataType: "json",
    timeout: 1000
  }).done ( function(data) {
      "use strict";

      if ('timeRemaining' in data) {
        seconds_left = data['timeRemaining'];

      } else {
        seconds_left -= 10;
      }
  }).always(function () {  });

    updateClock();

    if (seconds_left <= 0)
    {
        logout(true);
    }
}

// ===========================================================================
function updateClock()
{
    var secondsToShow = (Math.floor(0.5+(seconds_left / 10)))*10

    var minutes = Math.floor(secondsToShow / 60);
    var seconds = secondsToShow - (minutes * 60);
    var clockFormat = minutes.toString() + ":" + zeroFill(seconds,2);

    document.getElementById('timeout-txt').innerHTML = clockFormat;
}

// ===========================================================================
function getAbsolutePath() {
    // Builds result like 'http://127.0.0.1:8218/'

    var loc = window.location;
    var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/') + 1);
    var theResult;

    theResult = loc.href.substring(0, loc.href.length - ((loc.pathname + loc.search + loc.hash).length - pathName.length));
    if (theResult == "file:///temp/20170117/")
        theResult = "http://192.168.1.65:8081/"
    return theResult
}

// ===========================================================================
function logout(forceIt) {
    var doLogOut = false;

    if (typeof forceIt == "undefined") {forceIt = false;}

    if (forceIt) {
        doLogOut = true;
      } else {
        if (confirm("Log out ?")) {
          doLogOut = true;
        }}

    if (doLogOut) {
            var outUrl = getAbsolutePath() + "logout"
            //alert ("ready to logout: " + outUrl);
            var xmlhttp = new XMLHttpRequest();
            xmlhttp.open("GET", outUrl, true);
            xmlhttp.send();

            setTimeout(function () {
            window.location.href = getAbsolutePath() + "login";
        }, 200);
        }
}
</script>
"""

    return return_d


# ====================================
def get_os_name():
    # ====================================
    os_release = open('/etc/os-release', 'r').read()
    os_name = get_os_from_release(os_release)

    return os_name


# ----------------------------
def get_apache_user_name():
    # ----------------------------
    os_name = get_os_name()

    apache_user_name = 'apache-user-missing'

    if os_name == 'ubuntu':
        apache_user_name = 'www-data'

    if os_name == 'rocky':
        apache_user_name = 'apache'

    return apache_user_name


# ----------------------------
def get_apache_config():
    # ----------------------------

    # --- defaults start ---
    apache_files_path = '/var/www/htmlfiles'

    apache_config_content = """<VirtualHost *:80>
	ServerAdmin webmaster@localhost
	DocumentRoot """ + apache_files_path + """
	ErrorLog ${APACHE_LOG_DIR}/error.log
	CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""
    # --- defaults end ---

    all_settings = settings.get()

    if 'apache_files_path' in all_settings:
        apache_files_path = all_settings['apache_files_path']
    if 'apache_config_content' in all_settings:
        apache_config_content = all_settings['apache_config_content']

    apache_config = {}
    apache_config['apache_files_path'] = apache_files_path
    apache_config['apache_config_content'] = apache_config_content

    return apache_config


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def wrap_page_with_session(environ, body):
    # ----------------------------
    session_content_d = get_session_adders()

    # take the body of a built page, and then make it ready for auto logout, and hand it back

    body_to_use = body.replace('</body>', session_content_d['extra_body'])

    if session_content_d['extra_script']:
        body_to_use += session_content_d['extra_script']

    return body_to_use


# ----------------------------
def get_config(service):
    # ----------------------------

    all_settings = settings.get()

    # main difference items
    home_url = all_settings['home_url']
    trust_list = all_settings['trust_list']
    login_authentication = all_settings['login_authentication']
    days_to_keep = all_settings['days_to_keep']
    sites_to_drop = all_settings['sites_to_drop']

    # File locations
    data_drop_base = all_settings['data_drop_base']
    ram_disk_path = all_settings['ram_disk_path']
    datastore_save_path = all_settings['datastore_save_path']

    # calculated file locations
    base_upload_path = data_drop_base + 'upload/files/'
    base_codeupload_path = data_drop_base + 'codeupload/files/'
    thirdparty_upload_path = data_drop_base + 'thirdparty/files/'
    multimedia_upload_path = data_drop_base + 'multimedia/files/'

    # after the base release:
    if 'user_list' in all_settings:
        user_list = all_settings['user_list']
    else:
        user_list = {}

    # do the rest by building on the settings
    checkin_file_root = data_drop_base + 'checkin/json/id/'
    datadrop_save_path = data_drop_base + 'datadrop/json/id/'

    return_value = {}

    # -----------------
    # for all
    # -----------------
    return_value['home_url'] = home_url
    return_value['sites_to_drop'] = sites_to_drop
    return_value['time_of_last_tasks_run_trust_path'] = datastore_save_path + 'tasks_run_trust'
    return_value['tasks_request_path'] = datastore_save_path + 'tasks/'
    #    return_value['permissions_save_path'] = datastore_save_path + 'permissions/'
    return_value['apache_user_name'] = get_apache_user_name()

    if 'site_title' in all_settings:
        return_value['site_title'] = all_settings['site_title']
    else:
        return_value['site_title'] = 'Slicer 2.0'

    try:
        return_value['build'] = get_current_linked_folder()
    except:
        return_value['build'] = ''

    # -----------------
    # for specific
    # -----------------
    all_specifics = []

    specific_service = 'watchdog'
    if service == specific_service:
        return_value['log_root'] = data_drop_base + 'watchdog/'

    specific_service = 'permissions'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['trust_list'] = trust_list
        return_value['base_raw_path'] = data_drop_base + 'permissions/raw/'

    specific_service = 'login'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['login_authentication'] = login_authentication
        return_value['base_log_path'] = '/var/www/slicer/login/'
        return_value['time_to_remain_valid'] = s_time_to_stay_logged_in
        return_value['user_list'] = user_list

    specific_service = 'datadrop'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['datadrop_save_path'] = datadrop_save_path
        return_value['datadrop_raw_root'] = data_drop_base + 'datadrop/raw/'
        return_value['network_utilization_save_path'] = data_drop_base + 'network/id/(id)/'
        return_value['statistics_save_path'] = data_drop_base + 'datadrop/stats/id/'
        return_value['base_upload_path'] = data_drop_base + 'upload/files/'
        return_value['days_to_keep'] = days_to_keep

    specific_service = 'checkin'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['datadrop_save_path'] = datadrop_save_path
        return_value['base_raw_path'] = data_drop_base + 'checkin/raw/'
        return_value['days_to_keep'] = days_to_keep

    specific_service = 'codeupload'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_codeupload_path'] = base_codeupload_path
        return_value['base_upload_path'] = base_upload_path

    specific_service = 'thirdparty'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_upload_path'] = thirdparty_upload_path

    specific_service = 'multimedia'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_upload_path'] = multimedia_upload_path

    specific_service = 'dashboard'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['s_stash_report_file'] = ram_disk_path + 'dashboard_main_report.json'
        return_value['dashboard_raw_root'] = data_drop_base + 'dashboard/raw/'
        return_value['days_to_keep_dashboard_data'] = 60
        if 'days_to_keep_dashboard_data' in all_settings:
            return_value['days_to_keep_dashboard_data'] = all_settings['days_to_keep_dashboard_data']

    specific_service = 'datastore'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_datastore_path'] = datastore_save_path + 'datastore/'
        return_value['base_datastorelog_path'] = datastore_save_path + 'datastorelog/'
        return_value['base_datastorelog_cache_file'] = datastore_save_path + 'datastorelogcache/cache.txt'
        return_value['datastore_first_trust_diff_path'] = datastore_save_path + 'datastore_first_trust_diff'
        return_value['do_trust_path'] = ram_disk_path + 'datastore_trust'
        return_value['datadrop_save_path'] = datadrop_save_path

    specific_service = 'devicecommand'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_devicecommand_path'] = datastore_save_path + 'devicecommand/'

    specific_service = 'deviceupload'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['base_deviceupload_path'] = data_drop_base + 'upload/files/'

    specific_service = 'download'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_upload_path'] = data_drop_base + 'upload/files/'

    specific_service = 'jamf'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['jamf_file_root'] = data_drop_base + 'jamf/'
        return_value['jamf_computer_days_to_keep'] = '90'
        return_value['jamf_patch_information_days_to_keep'] = '365'

    specific_service = 'reports'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['datadrop_save_path'] = datadrop_save_path
        return_value['statistics_save_path'] = data_drop_base + 'datadrop/stats/id/'

    specific_service = 'rings'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['datadrop_save_path'] = datadrop_save_path

    specific_service = 'scan'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_scan_path'] = data_drop_base + 'scan/'

    specific_service = 'tasks'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['checkin_file_root'] = checkin_file_root
        return_value['task_status_file'] = data_drop_base + 'tasks/status_summary'

    specific_service = 'upload'
    all_specifics.append(specific_service)
    if service == specific_service:
        return_value['base_upload_path'] = base_upload_path

    # keep this one last
    if service == 'all_specifics':
        return_value['all_specifics'] = all_specifics

    return return_value


# ----------------------------
def make_all_dirs(return_value):
    # ----------------------------
    for item in return_value:
        content = return_value[item]
        if is_likely_a_path(content):
            if not os.path.exists(os.path.dirname(content)):
                os.makedirs(os.path.dirname(content))
                try:  # if I am root, this will work. If I am apache_user_name, then it will already be the correct permissions
                    apache_user_name = get_apache_user_name()
                    do_one_command('chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + os.path.dirname(content))
                except:
                    pass


# ----------------------------
def is_likely_a_path(content):
    # ----------------------------
    return_value = False

    if not content:
        return_value = False
    else:
        try:
            if not ':/' in content:
                if '/' in content:
                    return_value = True
            else:
                return_value = False
        except:
            return_value = False

    return return_value


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(organization status)'

    status = os.system('systemctl is-active --quiet organization-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content

    # then return what GET would have done
    return make_body_GET(environ), other


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += '<center>'
        body += 'method = GET'
        body += '</center>'

        body += '<center>'

        body += 'base configuration'
        body += '<table border="1" cellpadding="5">'

        base_config = get_config('all_specifics')

        for item in sorted(base_config.keys()):
            body += '<tr>'
            body += '<td>'
            body += str(item)
            body += '</td>'
            body += '<td>'
            body += str(base_config[item])
            body += '</td>'
            body += '</tr>'
        body += '</table>'

        for module_key in sorted(base_config['all_specifics']):
            body += '<br><br>' + module_key + '<br>'
            body += '<table border="1" cellpadding="5">'
            module_config = get_config(module_key)
            for item in sorted(module_config.keys()):
                if not item in base_config:
                    body += '<tr>'
                    body += '<td>'
                    body += str(item)
                    body += '</td>'
                    body += '<td>'
                    body += str(module_config[item])
                    body += '</td>'
                    body += '</tr>'
            body += '</table>'

        body += '<br><br>'

        #        body += str(environ)

        body += '</center>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    #    if permissions.permission_prefix_allowed(environ, 'organization_') or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            try:
                import permissions
                permissions.log_page_allowed(environ, service, other)
            except:
                pass
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n' \
                    '<body>\n'
        html += body

        if other['add_wrapper']:
            html += '</body>\n' \
                    '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1
        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_organization(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_make_home_url_from_environ(self):
        environ = {'REQUEST_SCHEME': 'http', 'HTTP_HOST': 'slicer.cardinalhealth.com'}
        expected = 'http://slicer.cardinalhealth.com'
        actual = make_home_url_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_wrap_page_with_session(self):
        body = '</body>'
        expected = get_session_adders()['extra_body']
        actual = wrap_page_with_session({}, body)
        self.assertEqual(expected, actual)

# End of source file
