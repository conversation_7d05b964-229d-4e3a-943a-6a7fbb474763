Ubuntu22, 2 cores, 4GB ram, 20GB disk, networking bridged
Take most defaults, but be sure to install ssh

#
#sudo su
#apt install -y python3-pip
#exit

#
#pip install pexpect


# set up firewall first
sudo ufw app list
sudo ufw allow OpenSSH
sudo ufw enable
    Command may disrupt existing ssh connections. Proceed with operation (y|n)?
    y
sudo ufw status

The rest is in the top of the slicer_wsgi_loader.py file:

Continue there...


