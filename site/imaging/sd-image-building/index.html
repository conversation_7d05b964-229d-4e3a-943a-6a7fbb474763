
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="../../getting-started/servers/gcp/old-server-docs/">
      
      
        <link rel="next" href="../sd-image-building-qcam/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.9">
    
    
      
        <title>SD Image Building - Slicer Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.4af4bdda.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      
  
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
  
  <style>:root{--md-admonition-icon--note:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M1%207.775V2.75C1%201.784%201.784%201%202.75%201h5.025c.464%200%20.91.184%201.238.513l6.25%206.25a1.75%201.75%200%200%201%200%202.474l-5.026%205.026a1.75%201.75%200%200%201-2.474%200l-6.25-6.25A1.75%201.75%200%200%201%201%207.775m1.5%200c0%20.066.026.13.073.177l6.25%206.25a.25.25%200%200%200%20.354%200l5.025-5.025a.25.25%200%200%200%200-.354l-6.25-6.25a.25.25%200%200%200-.177-.073H2.75a.25.25%200%200%200-.25.25ZM6%205a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E');--md-admonition-icon--abstract:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2.5%201.75v11.5c0%20.138.112.25.25.25h3.17a.75.75%200%200%201%200%201.5H2.75A1.75%201.75%200%200%201%201%2013.25V1.75C1%20.784%201.784%200%202.75%200h8.5C12.216%200%2013%20.784%2013%201.75v7.736a.75.75%200%200%201-1.5%200V1.75a.25.25%200%200%200-.25-.25h-8.5a.25.25%200%200%200-.25.25m13.274%209.537zl-4.557%204.45a.75.75%200%200%201-1.055-.008l-1.943-1.95a.75.75%200%200%201%201.062-1.058l1.419%201.425%204.026-3.932a.75.75%200%201%201%201.048%201.074M4.75%204h4.5a.75.75%200%200%201%200%201.5h-4.5a.75.75%200%200%201%200-1.5M4%207.75A.75.75%200%200%201%204.75%207h2a.75.75%200%200%201%200%201.5h-2A.75.75%200%200%201%204%207.75%22/%3E%3C/svg%3E');--md-admonition-icon--info:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M0%208a8%208%200%201%201%2016%200A8%208%200%200%201%200%208m8-6.5a6.5%206.5%200%201%200%200%2013%206.5%206.5%200%200%200%200-13M6.5%207.75A.75.75%200%200%201%207.25%207h1a.75.75%200%200%201%20.75.75v2.75h.25a.75.75%200%200%201%200%201.5h-2a.75.75%200%200%201%200-1.5h.25v-2h-.25a.75.75%200%200%201-.75-.75M8%206a1%201%200%201%201%200-2%201%201%200%200%201%200%202%22/%3E%3C/svg%3E');--md-admonition-icon--tip:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M3.499.75a.75.75%200%200%201%201.5%200v.996C5.9%202.903%206.793%203.65%207.662%204.376l.24.202c-.036-.694.055-1.422.426-2.163C9.1.873%2010.794-.045%2012.622.26%2014.408.558%2016%201.94%2016%204.25c0%201.278-.954%202.575-2.44%202.734l.146.508.065.22c.203.701.412%201.455.476%202.226.142%201.707-.4%203.03-1.487%203.898C11.714%2014.671%2010.27%2015%208.75%2015h-6a.75.75%200%200%201%200-1.5h1.376a4.5%204.5%200%200%201-.563-1.191%203.84%203.84%200%200%201-.05-2.063%204.65%204.65%200%200%201-2.025-.293.75.75%200%200%201%20.525-1.406c1.357.507%202.376-.006%202.698-.318l.009-.01a.747.747%200%200%201%201.06%200%20.75.75%200%200%201-.012%201.074c-.912.92-.992%201.835-.768%202.586.221.74.745%201.337%201.196%201.621H8.75c1.343%200%202.398-.296%203.074-.836.635-.507%201.036-1.31.928-2.602-.05-.603-.216-1.224-.422-1.93l-.064-.221c-.12-.407-.246-.84-.353-1.29a2.4%202.4%200%200%201-.507-.441%203.1%203.1%200%200%201-.633-1.248.75.75%200%200%201%201.455-.364c.046.185.144.436.31.627.146.168.353.305.712.305.738%200%201.25-.615%201.25-1.25%200-1.47-.95-2.315-2.123-2.51-1.172-.196-2.227.387-2.706%201.345-.46.92-.27%201.774.019%203.062l.042.19.01.05c.348.443.666.949.94%201.553a.75.75%200%201%201-1.365.62c-.553-1.217-1.32-1.94-2.3-2.768L6.7%205.527c-.814-.68-1.75-1.462-2.692-2.619a3.7%203.7%200%200%200-1.023.88c-.406.495-.663%201.036-.722%201.508.116.122.306.21.591.239.388.038.797-.06%201.032-.19a.75.75%200%200%201%20.728%201.31c-.515.287-1.23.439-1.906.373-.682-.067-1.473-.38-1.879-1.193L.75%205.677V5.5c0-.984.48-1.94%201.077-2.664.46-.559%201.05-1.055%201.673-1.353z%22/%3E%3C/svg%3E');--md-admonition-icon--success:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M13.78%204.22a.75.75%200%200%201%200%201.06l-7.25%207.25a.75.75%200%200%201-1.06%200L2.22%209.28a.75.75%200%200%201%20.018-1.042.75.75%200%200%201%201.042-.018L6%2010.94l6.72-6.72a.75.75%200%200%201%201.06%200%22/%3E%3C/svg%3E');--md-admonition-icon--question:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M0%208a8%208%200%201%201%2016%200A8%208%200%200%201%200%208m8-6.5a6.5%206.5%200%201%200%200%2013%206.5%206.5%200%200%200%200-13M6.92%206.085h.001a.749.749%200%201%201-1.342-.67c.169-.339.436-.701.849-.977C6.845%204.16%207.369%204%208%204a2.76%202.76%200%200%201%201.637.525c.503.377.863.965.863%201.725%200%20.448-.115.83-.329%201.15-.205.307-.47.513-.692.662-.109.072-.22.138-.313.195l-.006.004a6%206%200%200%200-.26.16%201%201%200%200%200-.276.245.75.75%200%200%201-1.248-.832c.184-.264.42-.489.692-.661q.154-.1.313-.195l.007-.004c.1-.061.182-.11.258-.161a1%201%200%200%200%20.277-.245C8.96%206.514%209%206.427%209%206.25a.61.61%200%200%200-.262-.525A1.27%201.27%200%200%200%208%205.5c-.369%200-.595.09-.74.187a1%201%200%200%200-.34.398M9%2011a1%201%200%201%201-2%200%201%201%200%200%201%202%200%22/%3E%3C/svg%3E');--md-admonition-icon--warning:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M6.457%201.047c.659-1.234%202.427-1.234%203.086%200l6.082%2011.378A1.75%201.75%200%200%201%2014.082%2015H1.918a1.75%201.75%200%200%201-1.543-2.575Zm1.763.707a.25.25%200%200%200-.44%200L1.698%2013.132a.25.25%200%200%200%20.22.368h12.164a.25.25%200%200%200%20.22-.368Zm.53%203.996v2.5a.75.75%200%200%201-1.5%200v-2.5a.75.75%200%200%201%201.5%200M9%2011a1%201%200%201%201-2%200%201%201%200%200%201%202%200%22/%3E%3C/svg%3E');--md-admonition-icon--failure:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2.344%202.343za8%208%200%200%201%2011.314%2011.314A8.002%208.002%200%200%201%20.234%2010.089a8%208%200%200%201%202.11-7.746m1.06%2010.253a6.5%206.5%200%201%200%209.108-9.275%206.5%206.5%200%200%200-9.108%209.275M6.03%204.97%208%206.94l1.97-1.97a.749.749%200%200%201%201.275.326.75.75%200%200%201-.215.734L9.06%208l1.97%201.97a.749.749%200%200%201-.326%201.275.75.75%200%200%201-.734-.215L8%209.06l-1.97%201.97a.749.749%200%200%201-1.275-.326.75.75%200%200%201%20.215-.734L6.94%208%204.97%206.03a.75.75%200%200%201%20.018-1.042.75.75%200%200%201%201.042-.018%22/%3E%3C/svg%3E');--md-admonition-icon--danger:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M9.504.43a1.516%201.516%200%200%201%202.437%201.713L10.415%205.5h2.123c1.57%200%202.346%201.909%201.22%203.004l-7.34%207.142a1.25%201.25%200%200%201-.871.354h-.302a1.25%201.25%200%200%201-1.157-1.723L5.633%2010.5H3.462c-1.57%200-2.346-1.909-1.22-3.004zm1.047%201.074L3.286%208.571A.25.25%200%200%200%203.462%209H6.75a.75.75%200%200%201%20.694%201.034l-1.713%204.188%206.982-6.793A.25.25%200%200%200%2012.538%207H9.25a.75.75%200%200%201-.683-1.06l2.008-4.418.003-.006-.004-.009-.006-.006-.008-.001q-.005%200-.009.004%22/%3E%3C/svg%3E');--md-admonition-icon--bug:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M4.72.22a.75.75%200%200%201%201.06%200l1%20.999a3.5%203.5%200%200%201%202.441%200l.999-1a.748.748%200%200%201%201.265.332.75.75%200%200%201-.205.729l-.775.776c.616.63.995%201.493.995%202.444v.327q0%20.15-.025.292c.408.14.764.392%201.029.722l1.968-.787a.75.75%200%200%201%20.556%201.392L13%207.258V9h2.25a.75.75%200%200%201%200%201.5H13v.5q-.002.615-.141%201.186l2.17.868a.75.75%200%200%201-.557%201.392l-2.184-.873A5%205%200%200%201%208%2016a5%205%200%200%201-4.288-2.427l-2.183.873a.75.75%200%200%201-.558-1.392l2.17-.868A5%205%200%200%201%203%2011v-.5H.75a.75.75%200%200%201%200-1.5H3V7.258L.971%206.446a.75.75%200%200%201%20.558-1.392l1.967.787c.265-.33.62-.583%201.03-.722a1.7%201.7%200%200%201-.026-.292V4.5c0-.951.38-1.814.995-2.444L4.72%201.28a.75.75%200%200%201%200-1.06m.53%206.28a.75.75%200%200%200-.75.75V11a3.5%203.5%200%201%200%207%200V7.25a.75.75%200%200%200-.75-.75ZM6.173%205h3.654A.17.17%200%200%200%2010%204.827V4.5a2%202%200%201%200-4%200v.327c0%20.096.077.173.173.173%22/%3E%3C/svg%3E');--md-admonition-icon--example:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M5%205.782V2.5h-.25a.75.75%200%200%201%200-1.5h6.5a.75.75%200%200%201%200%201.5H11v3.282l3.666%205.76C15.619%2013.04%2014.543%2015%2012.767%2015H3.233c-1.776%200-2.852-1.96-1.899-3.458Zm-2.4%206.565a.75.75%200%200%200%20.633%201.153h9.534a.75.75%200%200%200%20.633-1.153L12.225%2010.5h-8.45ZM9.5%202.5h-3V6c0%20.143-.04.283-.117.403L4.73%209h6.54L9.617%206.403A.75.75%200%200%201%209.5%206Z%22/%3E%3C/svg%3E');--md-admonition-icon--quote:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M1.75%202.5h10.5a.75.75%200%200%201%200%201.5H1.75a.75.75%200%200%201%200-1.5m4%205h8.5a.75.75%200%200%201%200%201.5h-8.5a.75.75%200%200%201%200-1.5m0%205h8.5a.75.75%200%200%201%200%201.5h-8.5a.75.75%200%200%201%200-1.5M2.5%207.75v6a.75.75%200%200%201-1.5%200v-6a.75.75%200%200%201%201.5%200%22/%3E%3C/svg%3E');}</style>



    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#sd-image-building-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="Slicer Documentation" class="md-header__button md-logo" aria-label="Slicer Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            Slicer Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              SD Image Building
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme)" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m14.3 16-.7-2h-3.2l-.7 2H7.8L11 7h2l3.2 9zM20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zm-9.15 3.96h2.3L12 9z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_2" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to system preference"  type="radio" name="__palette" id="__palette_2">
    
      <label class="md-header__button md-icon" title="Switch to system preference" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256l137.3-137.4c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
        <div class="md-search__suggest" data-md-component="search-suggest"></div>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
    
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m19 2-5 4.5v11l5-4.5zM6.5 5C4.55 5 2.45 5.4 1 6.5v14.66c0 .*********.5.1 0 .15-.07.25-.07 1.35-.65 3.3-1.09 4.75-1.09 1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.31 4.75 *********.***********.25 0 .5-.25.5-.5V6.5c-.6-.45-1.25-.75-2-1V19c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V6.5C10.55 5.4 8.45 5 6.5 5"/></svg>
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../getting-started/knowledge/" class="md-tabs__link">
          
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="./" class="md-tabs__link">
          
  
  Imaging

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../how-tos/how-to-burn/" class="md-tabs__link">
          
  
  How-To

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../pi/applications/" class="md-tabs__link">
          
  
  Raspberry Pi

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../reference/datastore/" class="md-tabs__link">
          
  
  Reference

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../releases/release-notes/" class="md-tabs__link">
          
  
  Release Notes

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="Slicer Documentation" class="md-nav__button md-logo" aria-label="Slicer Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    Slicer Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m19 2-5 4.5v11l5-4.5zM6.5 5C4.55 5 2.45 5.4 1 6.5v14.66c0 .*********.5.1 0 .15-.07.25-.07 1.35-.65 3.3-1.09 4.75-1.09 1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.31 4.75 *********.***********.25 0 .5-.25.5-.5V6.5c-.6-.45-1.25-.75-2-1V19c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V6.5C10.55 5.4 8.45 5 6.5 5"/></svg>
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/knowledge/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Knowledge
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/marketing/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Marketing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/generic-config/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Generic Config
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2_4" >
        
          
          <label class="md-nav__link" for="__nav_2_4" id="__nav_2_4_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    GCP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_4">
            <span class="md-nav__icon md-icon"></span>
            GCP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp-rocky-server/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Rocky Server
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2_4_2" >
        
          
          <label class="md-nav__link" for="__nav_2_4_2" id="__nav_2_4_2_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Servers
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_2_4_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_4_2">
            <span class="md-nav__icon md-icon"></span>
            Servers
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/gcp-servers/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Setup
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/configuration/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/packages/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Install Packages
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/apache/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Apache
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/certificates/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Certificates
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/additional-config/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Additional Config
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/monitoring-setup/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Monitoring Setup
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/bucket-storage/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Bucket Storage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/sudo-access/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Sudo Access
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/python-apache/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Python Apache
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../getting-started/servers/gcp/old-server-docs/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Old Server Documentation
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  <span class="md-ellipsis">
    Imaging
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Imaging
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    SD Image Building
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    SD Image Building
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#to-build-a-new-image" class="md-nav__link">
    <span class="md-ellipsis">
      To Build a New Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="To Build a New Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#20231030-pi5-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2023.10.30 - Pi5 Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#20240312-updated-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2024.03.12 - Updated Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#20240417-latest-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2024.04.17 - Latest Image
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-on-raspberry-pi-at-daves-house" class="md-nav__link">
    <span class="md-ellipsis">
      Testing on Raspberry Pi at Dave's House
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#original-raspberry-pi-os-lite-download" class="md-nav__link">
    <span class="md-ellipsis">
      Original Raspberry Pi OS Lite Download
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Original Raspberry Pi OS Lite Download">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#release-information" class="md-nav__link">
    <span class="md-ellipsis">
      Release Information
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-sha256-checksum-on-mac" class="md-nav__link">
    <span class="md-ellipsis">
      Verify SHA256 Checksum on Mac
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#flashing-the-sd-card" class="md-nav__link">
    <span class="md-ellipsis">
      Flashing the SD Card
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Flashing the SD Card">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#start-ssh" class="md-nav__link">
    <span class="md-ellipsis">
      Start SSH
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enable-ssh" class="md-nav__link">
    <span class="md-ellipsis">
      Enable SSH
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-the-hardware" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Up the Hardware
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#creating-admin-worker-users" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Admin &amp; Worker Users
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Creating Admin &amp; Worker Users">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-admin-user-cah-pi-su" class="md-nav__link">
    <span class="md-ellipsis">
      Create Admin User (cah-pi-su)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-worker-user-worker" class="md-nav__link">
    <span class="md-ellipsis">
      Create Worker User (worker)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#check-the-settings" class="md-nav__link">
    <span class="md-ellipsis">
      Check the settings
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-printer-user" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Up Printer User
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#wlan-pi5-settings" class="md-nav__link">
    <span class="md-ellipsis">
      WLAN Pi5 Settings
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#get-updates-and-required-installs" class="md-nav__link">
    <span class="md-ellipsis">
      Get Updates and Required Installs
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#install-necessary-packages" class="md-nav__link">
    <span class="md-ellipsis">
      Install Necessary Packages
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Install Necessary Packages">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#step-1-update-and-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      Step 1: Update and Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-2-fix-unfinished-package-configurations" class="md-nav__link">
    <span class="md-ellipsis">
      Step 2: Fix Unfinished Package Configurations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-3-install-core-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Step 3: Install Core Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-4-fix-python-package-manager-issues-if-needed" class="md-nav__link">
    <span class="md-ellipsis">
      Step 4: Fix Python Package Manager Issues (If Needed)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-5-install-myqr-for-qr-code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Step 5: Install MyQR for QR Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-6-install-additional-packages" class="md-nav__link">
    <span class="md-ellipsis">
      Step 6: Install Additional Packages
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-7-install-screen-capture-tool" class="md-nav__link">
    <span class="md-ellipsis">
      Step 7: Install Screen Capture Tool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-8-install-required-python-modules-and-utilities" class="md-nav__link">
    <span class="md-ellipsis">
      Step 8: Install Required Python Modules and Utilities
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#edits-to-configure" class="md-nav__link">
    <span class="md-ellipsis">
      Edits to Configure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Edits to Configure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#privoxy-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Privoxy Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cups-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      CUPS Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-audit" class="md-nav__link">
    <span class="md-ellipsis">
      Security Audit
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Audit">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#install-lynis-security-audit-tool" class="md-nav__link">
    <span class="md-ellipsis">
      Install Lynis Security Audit Tool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#initial-test-optional" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Test (Optional)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Initial Test (Optional)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-result" class="md-nav__link">
    <span class="md-ellipsis">
      Initial result
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-updates" class="md-nav__link">
    <span class="md-ellipsis">
      System Updates
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-hardening" class="md-nav__link">
    <span class="md-ellipsis">
      System Hardening
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Hardening">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ssh-configuration-now-done-in-pi_security" class="md-nav__link">
    <span class="md-ellipsis">
      SSH Configuration (Now done in pi_security)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#update-system-message" class="md-nav__link">
    <span class="md-ellipsis">
      Update System Message
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#restrict-access-to-compilers" class="md-nav__link">
    <span class="md-ellipsis">
      Restrict Access to Compilers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#disable-usb-storage" class="md-nav__link">
    <span class="md-ellipsis">
      Disable USB Storage
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#network-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Network Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Network Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#network-manager-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Network Manager Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#set-network-manager-as-primary" class="md-nav__link">
    <span class="md-ellipsis">
      Set Network Manager as Primary
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#disable-wifi-mac-address-randomization" class="md-nav__link">
    <span class="md-ellipsis">
      Disable WiFi MAC Address Randomization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reboot-to-apply-changes" class="md-nav__link">
    <span class="md-ellipsis">
      Reboot to Apply Changes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#config-browser" class="md-nav__link">
    <span class="md-ellipsis">
      Config Browser
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Config Browser">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configure-xinitrc" class="md-nav__link">
    <span class="md-ellipsis">
      Configure .xinitrc
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#set-up-local-html-directory" class="md-nav__link">
    <span class="md-ellipsis">
      Set up local HTML directory
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configure-browser-startup-script" class="md-nav__link">
    <span class="md-ellipsis">
      Configure browser startup script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#startx-issues-on-raspberry-pi-5" class="md-nav__link">
    <span class="md-ellipsis">
      Startx Issues on Raspberry Pi 5
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#runner-fails-2024-03-12" class="md-nav__link">
    <span class="md-ellipsis">
      Runner Fails (2024-03-12)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Runner Fails (2024-03-12)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#check-timezone" class="md-nav__link">
    <span class="md-ellipsis">
      Check Timezone
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#disable-pi5-power-button" class="md-nav__link">
    <span class="md-ellipsis">
      Disable Pi5 Power Button
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-system-snapshot" class="md-nav__link">
    <span class="md-ellipsis">
      Create System Snapshot
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Create System Snapshot">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#make-image-of-the-16gb-card" class="md-nav__link">
    <span class="md-ellipsis">
      Make Image of the 16GB Card
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-browser-process" class="md-nav__link">
    <span class="md-ellipsis">
      Test Browser Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bluetooth-troubleshooting-for-pi45" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth Troubleshooting for Pi4/5
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bluetooth Troubleshooting for Pi4/5">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-approach" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Approach
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Error Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#alternative-solution-1" class="md-nav__link">
    <span class="md-ellipsis">
      Alternative Solution 1
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#alternative-solution-2" class="md-nav__link">
    <span class="md-ellipsis">
      Alternative Solution 2
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-packages-20240417" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Packages (2024.04.17)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-diagnostics" class="md-nav__link">
    <span class="md-ellipsis">
      System Diagnostics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#version-comparisons-and-behaviors" class="md-nav__link">
    <span class="md-ellipsis">
      Version Comparisons and Behaviors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Version Comparisons and Behaviors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#sp40-image-old" class="md-nav__link">
    <span class="md-ellipsis">
      SP.40 Image (Old)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#after-update" class="md-nav__link">
    <span class="md-ellipsis">
      After Update
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#after-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      After Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#kernel-messages-after-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      Kernel Messages After Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sp47-image-middle" class="md-nav__link">
    <span class="md-ellipsis">
      SP.47 Image (Middle)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pi5-image-current" class="md-nav__link">
    <span class="md-ellipsis">
      Pi5 Image (Current)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#set-image-version" class="md-nav__link">
    <span class="md-ellipsis">
      Set Image Version
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#fix-numpy-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Fix NumPy Issues
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#load-pi-runner" class="md-nav__link">
    <span class="md-ellipsis">
      Load Pi Runner
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Load Pi Runner">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#get-raspberry-pi-serial-number" class="md-nav__link">
    <span class="md-ellipsis">
      Get Raspberry Pi Serial Number
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Verify Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#install-components-via-slicer" class="md-nav__link">
    <span class="md-ellipsis">
      Install Components via Slicer
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configure-autologin-for-worker" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Autologin for Worker
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configure Autologin for Worker">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-pi5-and-latest-os-after-20231030" class="md-nav__link">
    <span class="md-ellipsis">
      For Pi5 and Latest OS (After 2023.10.30)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#edit-getty-service" class="md-nav__link">
    <span class="md-ellipsis">
      Edit Getty Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-autologin" class="md-nav__link">
    <span class="md-ellipsis">
      Test Autologin
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configure-automatic-startx-on-login" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Automatic Startx on Login
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configure Automatic Startx on Login">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#add-to-workers-bash-profile" class="md-nav__link">
    <span class="md-ellipsis">
      Add to Worker's Bash Profile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#verify-hmi-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Verify HMI Installation
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-test-image-backup" class="md-nav__link">
    <span class="md-ellipsis">
      Create Test Image Backup
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#test-gui-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Test GUI Performance
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#browser-security-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Browser Security Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Browser Security Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#blocked-shortcuts" class="md-nav__link">
    <span class="md-ellipsis">
      Blocked Shortcuts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#allowed-shortcuts" class="md-nav__link">
    <span class="md-ellipsis">
      Allowed Shortcuts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#test-bookmark-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Test Bookmark Integration
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#multi-device-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Device Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Multi-Device Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chromium-profile-test" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium Profile Test
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bluetooth-scanner-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth Scanner Testing
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#finalize-image-for-distribution" class="md-nav__link">
    <span class="md-ellipsis">
      Finalize Image for Distribution
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Finalize Image for Distribution">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#system-cleanup" class="md-nav__link">
    <span class="md-ellipsis">
      System Cleanup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remove-temporary-files" class="md-nav__link">
    <span class="md-ellipsis">
      Remove Temporary Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#stop-services" class="md-nav__link">
    <span class="md-ellipsis">
      Stop Services
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#clean-log-files" class="md-nav__link">
    <span class="md-ellipsis">
      Clean Log Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reset-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Reset Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-default-startup-page" class="md-nav__link">
    <span class="md-ellipsis">
      Create Default Startup Page
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#final-shutdown" class="md-nav__link">
    <span class="md-ellipsis">
      Final Shutdown
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-distribution-image" class="md-nav__link">
    <span class="md-ellipsis">
      Create Distribution Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Create Distribution Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#using-applepi-baker" class="md-nav__link">
    <span class="md-ellipsis">
      Using ApplePi Baker
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#creating-the-image" class="md-nav__link">
    <span class="md-ellipsis">
      Creating the Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-the-image" class="md-nav__link">
    <span class="md-ellipsis">
      Test the Image
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#slicer-upload-folder" class="md-nav__link">
    <span class="md-ellipsis">
      Slicer Upload Folder
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Slicer Upload Folder">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#upload-to-slicer" class="md-nav__link">
    <span class="md-ellipsis">
      Upload to Slicer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#notify-users" class="md-nav__link">
    <span class="md-ellipsis">
      Notify Users
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-notes" class="md-nav__link">
    <span class="md-ellipsis">
      Development Notes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Notes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gcp-image-upload" class="md-nav__link">
    <span class="md-ellipsis">
      GCP Image Upload
    </span>
  </a>
  
    <nav class="md-nav" aria-label="GCP Image Upload">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-location" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Location
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#datastore-management" class="md-nav__link">
    <span class="md-ellipsis">
      Datastore Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#file-sharing" class="md-nav__link">
    <span class="md-ellipsis">
      File Sharing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#usb-control" class="md-nav__link">
    <span class="md-ellipsis">
      USB Control
    </span>
  </a>
  
    <nav class="md-nav" aria-label="USB Control">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#power-control-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Power Control Installation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#gcp-bucket-upload-process" class="md-nav__link">
    <span class="md-ellipsis">
      GCP Bucket Upload Process
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#generic-lite-image" class="md-nav__link">
    <span class="md-ellipsis">
      Generic Lite Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Generic Lite Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-image-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Image Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Initial Image Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#password-change" class="md-nav__link">
    <span class="md-ellipsis">
      Password Change
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-admin-user" class="md-nav__link">
    <span class="md-ellipsis">
      Create Admin User
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-worker-user" class="md-nav__link">
    <span class="md-ellipsis">
      Create Worker User
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configure-autologin" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Autologin
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chromium-cah-root-certificate" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium CAH Root Certificate
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chromium CAH Root Certificate">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prevent-screen-blanking" class="md-nav__link">
    <span class="md-ellipsis">
      Prevent Screen Blanking
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#planning-and-untested-features" class="md-nav__link">
    <span class="md-ellipsis">
      Planning and Untested Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Planning and Untested Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#step-1-list-completion" class="md-nav__link">
    <span class="md-ellipsis">
      Step 1 List Completion
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#browser-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Browser Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remote-access" class="md-nav__link">
    <span class="md-ellipsis">
      Remote Access
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Remote Access">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#vnc-setup" class="md-nav__link">
    <span class="md-ellipsis">
      VNC Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-enhancements" class="md-nav__link">
    <span class="md-ellipsis">
      Security Enhancements
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Enhancements">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#remove-default-pi-user" class="md-nav__link">
    <span class="md-ellipsis">
      Remove Default Pi User
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Certificate Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rclocal-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      RC.Local Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chromium-certificate-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium Certificate Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing" class="md-nav__link">
    <span class="md-ellipsis">
      Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-updates_1" class="md-nav__link">
    <span class="md-ellipsis">
      System Updates
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Updates">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cve-and-kernel-updates" class="md-nav__link">
    <span class="md-ellipsis">
      CVE and Kernel Updates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#keyboard-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Keyboard Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Keyboard Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#disable-specific-keys" class="md-nav__link">
    <span class="md-ellipsis">
      Disable Specific Keys
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#boot-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Boot Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Boot Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#startup-screen-management" class="md-nav__link">
    <span class="md-ellipsis">
      Startup Screen Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#boot-sequence-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Boot Sequence Customization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Checklist
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Checklist">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#power-button" class="md-nav__link">
    <span class="md-ellipsis">
      Power Button
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#profilessettings-passfail-on-pi4pi5" class="md-nav__link">
    <span class="md-ellipsis">
      Profiles/Settings (Pass/Fail on Pi4/Pi5)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#connectivity" class="md-nav__link">
    <span class="md-ellipsis">
      Connectivity
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bluetooth" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bookworm-specific-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Bookworm-Specific Updates
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bookworm-Specific Updates">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#hdmi-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HDMI Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#audio-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Audio Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pi4-hdmi-audio-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Pi4 HDMI Audio Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#audio-backend-options" class="md-nav__link">
    <span class="md-ellipsis">
      Audio Backend Options
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Steps
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../sd-image-building-qcam/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SD Image Building QCAM (Deprecated)
    
  </span>
  
    
  
  
    <span class="md-status md-status--deprecated"></span>
  

  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How-To
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            How-To
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../how-tos/how-to-burn/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Burn an Image
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../how-tos/how-to-check-md5/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Check MD5
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../how-tos/how-to-show-ppt/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Show PowerPoint
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Raspberry Pi
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Raspberry Pi
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../pi/applications/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Applications
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../pi/run-book/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Run Book
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/datastore/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Datastore
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/data-migration/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Data Migration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/filesystem/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Filesystem
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/python3porting/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Python Porting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/security-compliance-review/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Security Compliance
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/ubuntu22_servers/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Ubuntu22 Servers
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/coding-bible/" class="md-nav__link">
        
  
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 21.5c-1.35-.85-3.8-1.5-5.5-1.5-1.65 0-3.35.3-4.75 1.05-.1.05-.15.05-.25.05-.25 0-.5-.25-.5-.5V6c.6-.45 1.25-.75 2-1 1.11-.35 2.33-.5 3.5-.5 1.95 0 4.05.4 5.5 1.5 1.45-1.1 3.55-1.5 5.5-1.5 1.17 0 2.39.15 ********.25 1.4.55 2 1v14.6c0 .25-.25.5-.5.5-.1 0-.15 0-.25-.05-1.4-.75-3.1-1.05-4.75-1.05-1.7 0-4.15.65-5.5 1.5M12 8v11.5c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5V7c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5m1 3.5c1.11-.68 2.6-1 4.5-1 .91 0 1.76.09 2.5.28V9.23c-.87-.15-1.71-.23-2.5-.23q-2.655 0-4.5.84zm4.5.17c-1.71 0-3.21.26-4.5.79v1.69c1.11-.65 2.6-.99 4.5-.99 1.04 0 1.88.08 2.5.24v-1.5c-.87-.16-1.71-.23-2.5-.23m2.5 2.9c-.87-.16-1.71-.24-2.5-.24-1.83 0-3.33.27-4.5.8v1.69c1.11-.66 2.6-.99 4.5-.99 1.04 0 1.88.08 2.5.24z"/></svg>
  
  <span class="md-ellipsis">
    Coding Bible
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6_8" >
        
          
          <label class="md-nav__link" for="__nav_6_8" id="__nav_6_8_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Branches
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_6_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6_8">
            <span class="md-nav__icon md-icon"></span>
            Branches
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/branches/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6_8_2" >
        
          
          <label class="md-nav__link" for="__nav_6_8_2" id="__nav_6_8_2_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Branch Lists
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_6_8_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6_8_2">
            <span class="md-nav__icon md-icon"></span>
            Branch Lists
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/branches/2024-12-16/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2024-12-16
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../reference/branches/2024-12-11/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2024-12-11
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Release Notes
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Release Notes
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/release-notes/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Notes
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2025-03-12_SP.56/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.56 (2025-03-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2025-01-24_SP.55/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.55 (2025-01-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2025-01-24_SP.54/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.54 (2025-01-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-12-16_SP.53/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.53 (2024-12-16)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-11-12_SP.52/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.52 (2024-11-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-04-18_SP.51/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.51 (2024-04-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-04-01_SP.50/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.50 (2024-04-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-03-12_SP.49/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.49 (2024-03-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-03-11_SP.48/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.48 (2024-03-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2024-02-06_SP.47/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.47 (2024-02-06)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-10-25_SP.46/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.46 (2023-10-25)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-09-18_SP.45/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.45 (2023-09-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-08-21_SP.44/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.44 (2023-08-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-07-03_SP.43/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.43 (2023-07-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-05-31_SP.42/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.42 (2023-05-31)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-05-02_SP.41/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.41 (2023-05-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-04-03_SP.40/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.40 (2023-04-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-02-07_SP.39/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.39 (2023-02-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2023-01-27_SP.38/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.38 (2023-01-27)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-12-07_SP.37/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.37 (2022-12-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-12-05_SP.36/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.36 (2022-12-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-10-11_SP.35/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.35 (2022-10-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-08-24_SP.34/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.34 (2022-08-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-06-28_SP.33/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.33 (2022-06-28)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-06-01_SP.32/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.32 (2022-06-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-04-25_SP.31/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.31 (2022-04-25)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-04-22_SP.30/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.30 (2022-04-22)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-04-20_SP.29/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.29 (2022-04-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-03-11_SP.28/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.28 (2022-03-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-03-07_SP.27/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.27 (2022-03-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-03-02_SP.26/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.26 (2022-03-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-02-21_SP.25/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.25 (2022-02-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2022-01-14_SP.24/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.24 (2022-01-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-12-21_SP.23/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.23 (2021-12-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-11-29_SP.22/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.22 (2021-11-29)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-10-14_SP.21/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.21 (2021-10-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-31_SP.20/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.20 (2021-08-31)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-30_SP.19/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.19 (2021-08-30)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-26_SP.18/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.18 (2021-08-26)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-24_SP.17/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.17 (2021-08-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-20_SP.16/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.16 (2021-08-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-18_SP.15/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.15 (2021-08-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-17_SP.14/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.14 (2021-08-17)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-08-14_SP.13/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.13 (2021-08-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-22_SP.12/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.12 (2021-07-22)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-20_SP.11/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.11 (2021-07-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-12_SP.10/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.10 (2021-07-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-03_SP.9/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.9 (2021-07-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-02_SP.8/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.8 (2021-07-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-02_SP.7/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.7 (2021-07-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-06-24_SP.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.6 (2021-06-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-06-23_SP.5/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.5 (2021-06-23)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-06-21_SP.4/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.4 (2021-06-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-28_SP.3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.3 (2021-05-28)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-19_SP.2/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.2 (2021-05-19)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-17_SP.1/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.1 (2021-05-17)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-07-13_2.1.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.1.6 (2021-07-13)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-18_2.0.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.6 (2021-05-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-06_2.0.4/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.4 (2021-05-06)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-05_2.0.3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.3 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-05_2.0.2/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.2 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-05_2.0.1/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.1 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/2021-05-01_2.0.0/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.0 (2021-05-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../releases/versioning/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Versioning
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#to-build-a-new-image" class="md-nav__link">
    <span class="md-ellipsis">
      To Build a New Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="To Build a New Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#20231030-pi5-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2023.10.30 - Pi5 Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#20240312-updated-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2024.03.12 - Updated Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#20240417-latest-image" class="md-nav__link">
    <span class="md-ellipsis">
      📅 2024.04.17 - Latest Image
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-on-raspberry-pi-at-daves-house" class="md-nav__link">
    <span class="md-ellipsis">
      Testing on Raspberry Pi at Dave's House
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#original-raspberry-pi-os-lite-download" class="md-nav__link">
    <span class="md-ellipsis">
      Original Raspberry Pi OS Lite Download
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Original Raspberry Pi OS Lite Download">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#release-information" class="md-nav__link">
    <span class="md-ellipsis">
      Release Information
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-sha256-checksum-on-mac" class="md-nav__link">
    <span class="md-ellipsis">
      Verify SHA256 Checksum on Mac
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#flashing-the-sd-card" class="md-nav__link">
    <span class="md-ellipsis">
      Flashing the SD Card
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Flashing the SD Card">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#start-ssh" class="md-nav__link">
    <span class="md-ellipsis">
      Start SSH
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enable-ssh" class="md-nav__link">
    <span class="md-ellipsis">
      Enable SSH
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-the-hardware" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Up the Hardware
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#creating-admin-worker-users" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Admin &amp; Worker Users
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Creating Admin &amp; Worker Users">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-admin-user-cah-pi-su" class="md-nav__link">
    <span class="md-ellipsis">
      Create Admin User (cah-pi-su)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-worker-user-worker" class="md-nav__link">
    <span class="md-ellipsis">
      Create Worker User (worker)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#check-the-settings" class="md-nav__link">
    <span class="md-ellipsis">
      Check the settings
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#setting-up-printer-user" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Up Printer User
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#wlan-pi5-settings" class="md-nav__link">
    <span class="md-ellipsis">
      WLAN Pi5 Settings
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#get-updates-and-required-installs" class="md-nav__link">
    <span class="md-ellipsis">
      Get Updates and Required Installs
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#install-necessary-packages" class="md-nav__link">
    <span class="md-ellipsis">
      Install Necessary Packages
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Install Necessary Packages">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#step-1-update-and-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      Step 1: Update and Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-2-fix-unfinished-package-configurations" class="md-nav__link">
    <span class="md-ellipsis">
      Step 2: Fix Unfinished Package Configurations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-3-install-core-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Step 3: Install Core Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-4-fix-python-package-manager-issues-if-needed" class="md-nav__link">
    <span class="md-ellipsis">
      Step 4: Fix Python Package Manager Issues (If Needed)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-5-install-myqr-for-qr-code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Step 5: Install MyQR for QR Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-6-install-additional-packages" class="md-nav__link">
    <span class="md-ellipsis">
      Step 6: Install Additional Packages
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-7-install-screen-capture-tool" class="md-nav__link">
    <span class="md-ellipsis">
      Step 7: Install Screen Capture Tool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-8-install-required-python-modules-and-utilities" class="md-nav__link">
    <span class="md-ellipsis">
      Step 8: Install Required Python Modules and Utilities
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#edits-to-configure" class="md-nav__link">
    <span class="md-ellipsis">
      Edits to Configure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Edits to Configure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#privoxy-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Privoxy Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cups-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      CUPS Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-audit" class="md-nav__link">
    <span class="md-ellipsis">
      Security Audit
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Audit">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#install-lynis-security-audit-tool" class="md-nav__link">
    <span class="md-ellipsis">
      Install Lynis Security Audit Tool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#initial-test-optional" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Test (Optional)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Initial Test (Optional)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-result" class="md-nav__link">
    <span class="md-ellipsis">
      Initial result
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-updates" class="md-nav__link">
    <span class="md-ellipsis">
      System Updates
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-hardening" class="md-nav__link">
    <span class="md-ellipsis">
      System Hardening
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Hardening">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ssh-configuration-now-done-in-pi_security" class="md-nav__link">
    <span class="md-ellipsis">
      SSH Configuration (Now done in pi_security)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#update-system-message" class="md-nav__link">
    <span class="md-ellipsis">
      Update System Message
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#restrict-access-to-compilers" class="md-nav__link">
    <span class="md-ellipsis">
      Restrict Access to Compilers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#disable-usb-storage" class="md-nav__link">
    <span class="md-ellipsis">
      Disable USB Storage
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#network-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Network Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Network Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#network-manager-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Network Manager Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#set-network-manager-as-primary" class="md-nav__link">
    <span class="md-ellipsis">
      Set Network Manager as Primary
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#disable-wifi-mac-address-randomization" class="md-nav__link">
    <span class="md-ellipsis">
      Disable WiFi MAC Address Randomization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reboot-to-apply-changes" class="md-nav__link">
    <span class="md-ellipsis">
      Reboot to Apply Changes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#config-browser" class="md-nav__link">
    <span class="md-ellipsis">
      Config Browser
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Config Browser">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configure-xinitrc" class="md-nav__link">
    <span class="md-ellipsis">
      Configure .xinitrc
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#set-up-local-html-directory" class="md-nav__link">
    <span class="md-ellipsis">
      Set up local HTML directory
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configure-browser-startup-script" class="md-nav__link">
    <span class="md-ellipsis">
      Configure browser startup script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#startx-issues-on-raspberry-pi-5" class="md-nav__link">
    <span class="md-ellipsis">
      Startx Issues on Raspberry Pi 5
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#runner-fails-2024-03-12" class="md-nav__link">
    <span class="md-ellipsis">
      Runner Fails (2024-03-12)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Runner Fails (2024-03-12)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#check-timezone" class="md-nav__link">
    <span class="md-ellipsis">
      Check Timezone
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#disable-pi5-power-button" class="md-nav__link">
    <span class="md-ellipsis">
      Disable Pi5 Power Button
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-system-snapshot" class="md-nav__link">
    <span class="md-ellipsis">
      Create System Snapshot
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Create System Snapshot">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#make-image-of-the-16gb-card" class="md-nav__link">
    <span class="md-ellipsis">
      Make Image of the 16GB Card
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-browser-process" class="md-nav__link">
    <span class="md-ellipsis">
      Test Browser Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bluetooth-troubleshooting-for-pi45" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth Troubleshooting for Pi4/5
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bluetooth Troubleshooting for Pi4/5">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-approach" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Approach
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Error Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#alternative-solution-1" class="md-nav__link">
    <span class="md-ellipsis">
      Alternative Solution 1
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#alternative-solution-2" class="md-nav__link">
    <span class="md-ellipsis">
      Alternative Solution 2
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-packages-20240417" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Packages (2024.04.17)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-diagnostics" class="md-nav__link">
    <span class="md-ellipsis">
      System Diagnostics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#version-comparisons-and-behaviors" class="md-nav__link">
    <span class="md-ellipsis">
      Version Comparisons and Behaviors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Version Comparisons and Behaviors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#sp40-image-old" class="md-nav__link">
    <span class="md-ellipsis">
      SP.40 Image (Old)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#after-update" class="md-nav__link">
    <span class="md-ellipsis">
      After Update
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#after-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      After Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#kernel-messages-after-upgrade" class="md-nav__link">
    <span class="md-ellipsis">
      Kernel Messages After Upgrade
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sp47-image-middle" class="md-nav__link">
    <span class="md-ellipsis">
      SP.47 Image (Middle)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pi5-image-current" class="md-nav__link">
    <span class="md-ellipsis">
      Pi5 Image (Current)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#set-image-version" class="md-nav__link">
    <span class="md-ellipsis">
      Set Image Version
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#fix-numpy-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Fix NumPy Issues
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#load-pi-runner" class="md-nav__link">
    <span class="md-ellipsis">
      Load Pi Runner
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Load Pi Runner">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#get-raspberry-pi-serial-number" class="md-nav__link">
    <span class="md-ellipsis">
      Get Raspberry Pi Serial Number
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Verify Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#install-components-via-slicer" class="md-nav__link">
    <span class="md-ellipsis">
      Install Components via Slicer
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configure-autologin-for-worker" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Autologin for Worker
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configure Autologin for Worker">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-pi5-and-latest-os-after-20231030" class="md-nav__link">
    <span class="md-ellipsis">
      For Pi5 and Latest OS (After 2023.10.30)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#edit-getty-service" class="md-nav__link">
    <span class="md-ellipsis">
      Edit Getty Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-autologin" class="md-nav__link">
    <span class="md-ellipsis">
      Test Autologin
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configure-automatic-startx-on-login" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Automatic Startx on Login
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configure Automatic Startx on Login">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#add-to-workers-bash-profile" class="md-nav__link">
    <span class="md-ellipsis">
      Add to Worker's Bash Profile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#verify-hmi-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Verify HMI Installation
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-test-image-backup" class="md-nav__link">
    <span class="md-ellipsis">
      Create Test Image Backup
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#test-gui-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Test GUI Performance
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#browser-security-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Browser Security Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Browser Security Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#blocked-shortcuts" class="md-nav__link">
    <span class="md-ellipsis">
      Blocked Shortcuts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#allowed-shortcuts" class="md-nav__link">
    <span class="md-ellipsis">
      Allowed Shortcuts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#test-bookmark-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Test Bookmark Integration
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#multi-device-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Device Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Multi-Device Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chromium-profile-test" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium Profile Test
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bluetooth-scanner-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth Scanner Testing
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#finalize-image-for-distribution" class="md-nav__link">
    <span class="md-ellipsis">
      Finalize Image for Distribution
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Finalize Image for Distribution">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#system-cleanup" class="md-nav__link">
    <span class="md-ellipsis">
      System Cleanup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remove-temporary-files" class="md-nav__link">
    <span class="md-ellipsis">
      Remove Temporary Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#stop-services" class="md-nav__link">
    <span class="md-ellipsis">
      Stop Services
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#clean-log-files" class="md-nav__link">
    <span class="md-ellipsis">
      Clean Log Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reset-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Reset Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-default-startup-page" class="md-nav__link">
    <span class="md-ellipsis">
      Create Default Startup Page
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#final-shutdown" class="md-nav__link">
    <span class="md-ellipsis">
      Final Shutdown
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#create-distribution-image" class="md-nav__link">
    <span class="md-ellipsis">
      Create Distribution Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Create Distribution Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#using-applepi-baker" class="md-nav__link">
    <span class="md-ellipsis">
      Using ApplePi Baker
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#creating-the-image" class="md-nav__link">
    <span class="md-ellipsis">
      Creating the Image
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-the-image" class="md-nav__link">
    <span class="md-ellipsis">
      Test the Image
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#slicer-upload-folder" class="md-nav__link">
    <span class="md-ellipsis">
      Slicer Upload Folder
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Slicer Upload Folder">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#upload-to-slicer" class="md-nav__link">
    <span class="md-ellipsis">
      Upload to Slicer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#notify-users" class="md-nav__link">
    <span class="md-ellipsis">
      Notify Users
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-notes" class="md-nav__link">
    <span class="md-ellipsis">
      Development Notes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Notes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gcp-image-upload" class="md-nav__link">
    <span class="md-ellipsis">
      GCP Image Upload
    </span>
  </a>
  
    <nav class="md-nav" aria-label="GCP Image Upload">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-location" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Location
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#datastore-management" class="md-nav__link">
    <span class="md-ellipsis">
      Datastore Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#file-sharing" class="md-nav__link">
    <span class="md-ellipsis">
      File Sharing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#usb-control" class="md-nav__link">
    <span class="md-ellipsis">
      USB Control
    </span>
  </a>
  
    <nav class="md-nav" aria-label="USB Control">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#power-control-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Power Control Installation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#gcp-bucket-upload-process" class="md-nav__link">
    <span class="md-ellipsis">
      GCP Bucket Upload Process
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#generic-lite-image" class="md-nav__link">
    <span class="md-ellipsis">
      Generic Lite Image
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Generic Lite Image">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-image-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Image Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Initial Image Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#password-change" class="md-nav__link">
    <span class="md-ellipsis">
      Password Change
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-admin-user" class="md-nav__link">
    <span class="md-ellipsis">
      Create Admin User
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#create-worker-user" class="md-nav__link">
    <span class="md-ellipsis">
      Create Worker User
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configure-autologin" class="md-nav__link">
    <span class="md-ellipsis">
      Configure Autologin
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chromium-cah-root-certificate" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium CAH Root Certificate
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chromium CAH Root Certificate">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prevent-screen-blanking" class="md-nav__link">
    <span class="md-ellipsis">
      Prevent Screen Blanking
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#planning-and-untested-features" class="md-nav__link">
    <span class="md-ellipsis">
      Planning and Untested Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Planning and Untested Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#step-1-list-completion" class="md-nav__link">
    <span class="md-ellipsis">
      Step 1 List Completion
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#browser-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Browser Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remote-access" class="md-nav__link">
    <span class="md-ellipsis">
      Remote Access
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Remote Access">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#vnc-setup" class="md-nav__link">
    <span class="md-ellipsis">
      VNC Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-enhancements" class="md-nav__link">
    <span class="md-ellipsis">
      Security Enhancements
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Enhancements">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#remove-default-pi-user" class="md-nav__link">
    <span class="md-ellipsis">
      Remove Default Pi User
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Certificate Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rclocal-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      RC.Local Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Installation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chromium-certificate-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Chromium Certificate Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing" class="md-nav__link">
    <span class="md-ellipsis">
      Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-updates_1" class="md-nav__link">
    <span class="md-ellipsis">
      System Updates
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Updates">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cve-and-kernel-updates" class="md-nav__link">
    <span class="md-ellipsis">
      CVE and Kernel Updates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#keyboard-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Keyboard Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Keyboard Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#disable-specific-keys" class="md-nav__link">
    <span class="md-ellipsis">
      Disable Specific Keys
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#boot-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Boot Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Boot Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#startup-screen-management" class="md-nav__link">
    <span class="md-ellipsis">
      Startup Screen Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#boot-sequence-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Boot Sequence Customization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Checklist
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Checklist">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#power-button" class="md-nav__link">
    <span class="md-ellipsis">
      Power Button
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#profilessettings-passfail-on-pi4pi5" class="md-nav__link">
    <span class="md-ellipsis">
      Profiles/Settings (Pass/Fail on Pi4/Pi5)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#connectivity" class="md-nav__link">
    <span class="md-ellipsis">
      Connectivity
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bluetooth" class="md-nav__link">
    <span class="md-ellipsis">
      Bluetooth
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bookworm-specific-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Bookworm-Specific Updates
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bookworm-Specific Updates">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#hdmi-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HDMI Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#audio-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Audio Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pi4-hdmi-audio-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Pi4 HDMI Audio Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#audio-backend-options" class="md-nav__link">
    <span class="md-ellipsis">
      Audio Backend Options
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Steps
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="sd-image-building-guide">SD Image Building Guide<a class="headerlink" href="#sd-image-building-guide" title="Permalink">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permalink">&para;</a></h2>
<p>This guide provides step-by-step instructions on how to build and configure a new Raspberry Pi OS image.</p>
<hr />
<h2 id="to-build-a-new-image">To Build a New Image<a class="headerlink" href="#to-build-a-new-image" title="Permalink">&para;</a></h2>
<h3 id="20231030-pi5-image">📅 2023.10.30 - Pi5 Image<a class="headerlink" href="#20231030-pi5-image" title="Permalink">&para;</a></h3>
<p><a href="https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-10-10/2023-10-10-raspios-bookworm-armhf-lite.img.xz">Download Raspberry Pi OS Lite (2023-10-10)</a></p>
<p>📌 <strong>Location:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/Starting_image/2023-10-10-raspios-bookworm-armhf-lite.img.xz
</span></code></pre></div>
<h3 id="20240312-updated-image">📅 2024.03.12 - Updated Image<a class="headerlink" href="#20240312-updated-image" title="Permalink">&para;</a></h3>
<p><a href="https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-12-11/2023-12-11-raspios-bookworm-armhf-lite.img.xz">Download Raspberry Pi OS Lite (2023-12-11)</a></p>
<h3 id="20240417-latest-image">📅 2024.04.17 - Latest Image<a class="headerlink" href="#20240417-latest-image" title="Permalink">&para;</a></h3>
<p><a href="https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2024-03-15/2024-03-15-raspios-bookworm-armhf-lite.img.xz">Download Raspberry Pi OS Lite (2024-03-15)</a></p>
<div class="admonition info">
<p class="admonition-title">For official Raspberry Pi OS releases, visit:</p>
<p><a href="https://www.raspberrypi.com/software/operating-systems/">Raspberry Pi OS</a></p>
</div>
<hr />
<h2 id="testing-on-raspberry-pi-at-daves-house">Testing on Raspberry Pi at Dave's House<a class="headerlink" href="#testing-on-raspberry-pi-at-daves-house" title="Permalink">&para;</a></h2>
<p><strong>Test Device IDs:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-1-1"><a id="__codelineno-1-1" name="__codelineno-1-1" href="#__codelineno-1-1"></a><span class="o">(</span><span class="nv">ID</span><span class="o">=</span>10000000e3669edf<span class="o">)</span>
</span><span id="__span-1-2"><a id="__codelineno-1-2" name="__codelineno-1-2" href="#__codelineno-1-2"></a><span class="o">(</span><span class="nv">IDr</span><span class="o">=</span><span class="m">1530</span>-0022-4805-1292-511<span class="o">)</span>
</span></code></pre></div>
<p><strong>SSH into the Pi:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-2-1"><a id="__codelineno-2-1" name="__codelineno-2-1" href="#__codelineno-2-1"></a>ssh<span class="w"> </span>pi@10.226.171.139
</span></code></pre></div>
<hr />
<h2 id="original-raspberry-pi-os-lite-download">Original Raspberry Pi OS Lite Download<a class="headerlink" href="#original-raspberry-pi-os-lite-download" title="Permalink">&para;</a></h2>
<p>📌 <strong><a href="https://www.raspberrypi.org/software/operating-systems/#raspberry-pi-os-32-bit">Official Download Page</a></strong></p>
<p>📌 <strong>GCP Bucket Location:</strong>
<a href="https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/raw_image_starts?project=mac-mgmt-pr-cah&amp;pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&amp;prefix=&amp;forceOnObjectsSortingFiltering=false">View in Google Cloud Console</a></p>
<p>📌 <strong>Alternative Download from GCP Storage:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-3-1"><a id="__codelineno-3-1" name="__codelineno-3-1" href="#__codelineno-3-1"></a>gs://pi-mgmt-pr-cah-distribution/raw_image_starts/2021-03-04-raspios-buster-armhf-lite.zip
</span></code></pre></div>
<h3 id="release-information">Release Information<a class="headerlink" href="#release-information" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-4-1"><a id="__codelineno-4-1" name="__codelineno-4-1" href="#__codelineno-4-1"></a>Release<span class="w"> </span>date:<span class="w"> </span>March<span class="w"> </span>4th<span class="w"> </span><span class="m">2021</span>
</span><span id="__span-4-2"><a id="__codelineno-4-2" name="__codelineno-4-2" href="#__codelineno-4-2"></a>Kernel<span class="w"> </span>version:<span class="w"> </span><span class="m">5</span>.10
</span><span id="__span-4-3"><a id="__codelineno-4-3" name="__codelineno-4-3" href="#__codelineno-4-3"></a>Size:<span class="w"> </span>442MB
</span><span id="__span-4-4"><a id="__codelineno-4-4" name="__codelineno-4-4" href="#__codelineno-4-4"></a>SHA256:<span class="w"> </span>ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39
</span></code></pre></div>
<details class="abstract" open="open">
<summary>Most recent release note:</summary>
<p><strong>2021-03-04:</strong>
    <div class="language-text highlight"><pre><span></span><code><span id="__span-5-1"><a id="__codelineno-5-1" name="__codelineno-5-1" href="#__codelineno-5-1"></a>* Thonny upgraded to version 3.3.52.0
</span><span id="__span-5-2"><a id="__codelineno-5-2" name="__codelineno-5-2" href="#__codelineno-5-2"></a>* SD Card Copier made compatible with NVMe devices; now built against GTK+3 toolkit
</span><span id="__span-5-3"><a id="__codelineno-5-3" name="__codelineno-5-3" href="#__codelineno-5-3"></a>* Composite video options removed from Raspberry Pi 4 in Raspberry Pi Configuration
</span><span id="__span-5-4"><a id="__codelineno-5-4" name="__codelineno-5-4" href="#__codelineno-5-4"></a>* Boot order options in raspi-config adjusted for more flexibility
</span><span id="__span-5-5"><a id="__codelineno-5-5" name="__codelineno-5-5" href="#__codelineno-5-5"></a>* Recommended Software now built against GTK+3 toolkit
</span><span id="__span-5-6"><a id="__codelineno-5-6" name="__codelineno-5-6" href="#__codelineno-5-6"></a>* Fix for crash in volume plugin when using keyboard could push value out of range
</span><span id="__span-5-7"><a id="__codelineno-5-7" name="__codelineno-5-7" href="#__codelineno-5-7"></a>* Fix for focus changing between windows in file manager when using keyboard to navigate directory view
</span><span id="__span-5-8"><a id="__codelineno-5-8" name="__codelineno-5-8" href="#__codelineno-5-8"></a>* Fix for Raspberry Pi 400 keyboard country not being read correctly in startup wizard
</span><span id="__span-5-9"><a id="__codelineno-5-9" name="__codelineno-5-9" href="#__codelineno-5-9"></a>* Armenian and Japanese translations added to several packages
</span><span id="__span-5-10"><a id="__codelineno-5-10" name="__codelineno-5-10" href="#__codelineno-5-10"></a>* Automatically load aes-neon-bs on ARM64 to speed up OpenSSL
</span><span id="__span-5-11"><a id="__codelineno-5-11" name="__codelineno-5-11" href="#__codelineno-5-11"></a>* Raspberry Pi firmware fcf8d2f7639ad8d0330db9c8db9b71bd33eaaa28
</span><span id="__span-5-12"><a id="__codelineno-5-12" name="__codelineno-5-12" href="#__codelineno-5-12"></a>* Linux kernel 5.10.17
</span></code></pre></div></p>
</details>
<h3 id="verify-sha256-checksum-on-mac">Verify SHA256 Checksum on Mac<a class="headerlink" href="#verify-sha256-checksum-on-mac" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-6-1"><a id="__codelineno-6-1" name="__codelineno-6-1" href="#__codelineno-6-1"></a>shasum<span class="w"> </span>-a<span class="w"> </span><span class="m">256</span><span class="w"> </span>~/Downloads/2021-03-04-raspios-buster-armhf-lite.zip
</span><span id="__span-6-2"><a id="__codelineno-6-2" name="__codelineno-6-2" href="#__codelineno-6-2"></a><span class="c1"># or</span>
</span><span id="__span-6-3"><a id="__codelineno-6-3" name="__codelineno-6-3" href="#__codelineno-6-3"></a>shasum<span class="w"> </span>-a<span class="w"> </span><span class="m">256</span><span class="w"> </span>~/Downloads/raw_image_starts-2021-03-04-raspios-buster-armhf-lite.zip
</span></code></pre></div>
<details open="open">
<summary>Expected SHA256</summary>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-7-1"><a id="__codelineno-7-1" name="__codelineno-7-1" href="#__codelineno-7-1"></a>ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39
</span></code></pre></div>
</details>
<p><strong>Leave the file zipped</strong> - Balena Etcher can flash directly from the zip file.</p>
<div class="admonition danger">
<p class="admonition-title"><strong>Mac Users:</strong></p>
<p>If Mac has been forced to not allow USB memory, go to Self Service find the <strong>Remove DLP-15.0</strong>, and run the remove.
(This is a not-normal remove, and must be made visible by using Jamf tools to show it)</p>
</div>
<hr />
<h2 id="flashing-the-sd-card">Flashing the SD Card<a class="headerlink" href="#flashing-the-sd-card" title="Permalink">&para;</a></h2>
<p>This section has been moved to a dedicated guide.
For step-by-step instructions on burning an image, visit:</p>
<p>📌 <strong><a href="../../how-tos/how-to-burn/">How to Burn an Image</a></strong></p>
<hr />
<h3 id="initial-setup">Initial Setup<a class="headerlink" href="#initial-setup" title="Permalink">&para;</a></h3>
<p>Added 2024.03.12</p>
<ul>
<li><strong>Keyboard Layout:</strong> English (US)</li>
</ul>
<div class="tabbed-set tabbed-alternate" data-tabs="1:2"><input checked="checked" id="__tabbed_1_1" name="__tabbed_1" type="radio" /><input id="__tabbed_1_2" name="__tabbed_1" type="radio" /><div class="tabbed-labels"><label for="__tabbed_1_1">New</label><label for="__tabbed_1_2">Old</label></div>
<div class="tabbed-content">
<div class="tabbed-block">
<div class="admonition info">
<p class="admonition-title"><strong>Login Credentials:</strong></p>
<ul>
<li>Set a new user as <code>pi</code></li>
<li>Use the cah pi default password, that is not <code>raspberry</code></li>
<li>Login as the <code>pi user</code></li>
</ul>
</div>
</div>
<div class="tabbed-block">
<div class="admonition info">
<p class="admonition-title"><strong>Login Credentials:</strong></p>
<ul>
<li><strong>Username:</strong> <code>pi</code></li>
<li><strong>Password:</strong> <code>raspberry</code> (Change immediately!)
<div class="language-sh highlight"><span class="filename">Change Password To:</span><pre><span></span><code><span id="__span-8-1"><a id="__codelineno-8-1" name="__codelineno-8-1" href="#__codelineno-8-1"></a>passwd
</span></code></pre></div></li>
</ul>
</div>
</div>
</div>
</div>
<h3 id="start-ssh">Start SSH<a class="headerlink" href="#start-ssh" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-9-1"><a id="__codelineno-9-1" name="__codelineno-9-1" href="#__codelineno-9-1"></a>sudo<span class="w"> </span>systemctl<span class="w"> </span>start<span class="w"> </span>ssh
</span></code></pre></div>
<div class="admonition info">
<p><strong>Now you can connect the Pi to a wired network.</strong></p>
</div>
<p><strong>Get the IP Address:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-10-1"><a id="__codelineno-10-1" name="__codelineno-10-1" href="#__codelineno-10-1"></a>ip<span class="w"> </span>addr
</span></code></pre></div>
<p><strong>SSH into the Pi:</strong></p>
<div class="admonition info">
<p class="admonition-title">Info</p>
<p>---- At this point, you no longer need the keyboard, and can do the rest from another station
<div class="language-sh highlight"><span class="filename">Connect from another computer/station:</span><pre><span></span><code><span id="__span-11-1"><a id="__codelineno-11-1" name="__codelineno-11-1" href="#__codelineno-11-1"></a>ssh<span class="w"> </span>pi@&lt;ip-address&gt;
</span></code></pre></div></p>
</div>
<h3 id="enable-ssh">Enable SSH<a class="headerlink" href="#enable-ssh" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-12-1"><a id="__codelineno-12-1" name="__codelineno-12-1" href="#__codelineno-12-1"></a>sudo<span class="w"> </span>systemctl<span class="w"> </span><span class="nb">enable</span><span class="w"> </span>ssh
</span></code></pre></div>
<hr />
<h2 id="setting-up-the-hardware">Setting Up the Hardware<a class="headerlink" href="#setting-up-the-hardware" title="Permalink">&para;</a></h2>
<p><del>Change Keyboard Layout (Old):</del></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-13-1"><a id="__codelineno-13-1" name="__codelineno-13-1" href="#__codelineno-13-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/default/keyboard
</span><span id="__span-13-2"><a id="__codelineno-13-2" name="__codelineno-13-2" href="#__codelineno-13-2"></a><span class="c1"># Change &quot;gb&quot; to &quot;us&quot;</span>
</span></code></pre></div>
<hr />
<h2 id="creating-admin-worker-users">Creating Admin &amp; Worker Users<a class="headerlink" href="#creating-admin-worker-users" title="Permalink">&para;</a></h2>
<div class="admonition info">
<p class="admonition-title">Started 2021.07.23 with Image version 2.2.0</p>
</div>
<h3 id="create-admin-user-cah-pi-su"><strong>Create Admin User (<code>cah-pi-su</code>)</strong><a class="headerlink" href="#create-admin-user-cah-pi-su" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-14-1"><a id="__codelineno-14-1" name="__codelineno-14-1" href="#__codelineno-14-1"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>cah-pi-su
</span></code></pre></div>
<blockquote>
<p>(vault lookup?)</p>
<p>(leave all entries blank, and keep hitting enter until it saves)</p>
</blockquote>
<div class="language-sh highlight"><span class="filename">ADM group gets us the journalctl log prints for all</span><pre><span></span><code><span id="__span-15-1"><a id="__codelineno-15-1" name="__codelineno-15-1" href="#__codelineno-15-1"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>video,adm<span class="w"> </span>cah-pi-su
</span><span id="__span-15-2"><a id="__codelineno-15-2" name="__codelineno-15-2" href="#__codelineno-15-2"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>cah-pi-su<span class="w"> </span>sudo
</span></code></pre></div>
<p><strong>Grant Passwordless Sudo Access:</strong>
To avoid typing password at each restart of sudo permission window</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-16-1"><a id="__codelineno-16-1" name="__codelineno-16-1" href="#__codelineno-16-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/sudoers.d/010_cah-pi-su
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Add the following line:</span><pre><span></span><code><span id="__span-17-1"><a id="__codelineno-17-1" name="__codelineno-17-1" href="#__codelineno-17-1"></a>cah-pi-su<span class="w"> </span><span class="nv">ALL</span><span class="o">=(</span>ALL<span class="o">)</span><span class="w"> </span>NOPASSWD:<span class="w"> </span>ALL
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Log out of pi user</span><pre><span></span><code><span id="__span-18-1"><a id="__codelineno-18-1" name="__codelineno-18-1" href="#__codelineno-18-1"></a><span class="nb">exit</span>
</span></code></pre></div>
<p><strong>Disable Default <code>pi</code> User:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-19-1"><a id="__codelineno-19-1" name="__codelineno-19-1" href="#__codelineno-19-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/shadow
</span></code></pre></div>
<div class="admonition info">
<p class="admonition-title">Info</p>
<div class="language-sh highlight"><span class="filename">Find</span><pre><span></span><code><span id="__span-20-1"><a id="__codelineno-20-1" name="__codelineno-20-1" href="#__codelineno-20-1"></a><span class="s2">&quot;pi:</span><span class="nv">$6$rseVWwvM7AfG7bKY$TV6WN558gwmBs</span><span class="s2">.idUmnkzO9PlOgYJtenJM5oBJeDO4FcGAo.qEWdkHAw4CBDmYR.q3HZxRkO8fVPEa69t2cGo1:18690:0:99999:7:::&quot;</span>
</span><span id="__span-20-2"><a id="__codelineno-20-2" name="__codelineno-20-2" href="#__codelineno-20-2"></a><span class="c1"># And remove the entire line</span>
</span></code></pre></div>
</div>
<h3 id="create-worker-user-worker"><strong>Create Worker User (<code>worker</code>)</strong><a class="headerlink" href="#create-worker-user-worker" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-21-1"><a id="__codelineno-21-1" name="__codelineno-21-1" href="#__codelineno-21-1"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>worker
</span></code></pre></div>
<p>Use a <code>CamelCase</code> password.</p>
<div class="admonition info">
<p class="admonition-title">Printing (long install) (Must do it now, to set permissions just once, which include <code>lpadmin</code>)</p>
</div>
<blockquote>
<p>Added 2024.03.12</p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-22-1"><a id="__codelineno-22-1" name="__codelineno-22-1" href="#__codelineno-22-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>update
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">This is the long time to install one</span><pre><span></span><code><span id="__span-23-1"><a id="__codelineno-23-1" name="__codelineno-23-1" href="#__codelineno-23-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>cups
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Assign the necessary groups</span><pre><span></span><code><span id="__span-24-1"><a id="__codelineno-24-1" name="__codelineno-24-1" href="#__codelineno-24-1"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>video,input,lpadmin<span class="w"> </span>worker
</span></code></pre></div>
<h3 id="check-the-settings"><strong>Check the settings</strong><a class="headerlink" href="#check-the-settings" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-25-1"><a id="__codelineno-25-1" name="__codelineno-25-1" href="#__codelineno-25-1"></a>ls<span class="w"> </span>-l<span class="w"> </span>/dev/input/
</span><span id="__span-25-2"><a id="__codelineno-25-2" name="__codelineno-25-2" href="#__codelineno-25-2"></a>groups<span class="w"> </span>worker
</span></code></pre></div>
<hr />
<h2 id="setting-up-printer-user">Setting Up Printer User<a class="headerlink" href="#setting-up-printer-user" title="Permalink">&para;</a></h2>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-26-1"><a id="__codelineno-26-1" name="__codelineno-26-1" href="#__codelineno-26-1"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>printer
</span><span id="__span-26-2"><a id="__codelineno-26-2" name="__codelineno-26-2" href="#__codelineno-26-2"></a><span class="c1"># Password: printer</span>
</span><span id="__span-26-3"><a id="__codelineno-26-3" name="__codelineno-26-3" href="#__codelineno-26-3"></a>
</span><span id="__span-26-4"><a id="__codelineno-26-4" name="__codelineno-26-4" href="#__codelineno-26-4"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>lpadmin<span class="w"> </span>printer
</span></code></pre></div>
<hr />
<h2 id="wlan-pi5-settings">WLAN Pi5 Settings<a class="headerlink" href="#wlan-pi5-settings" title="Permalink">&para;</a></h2>
<p>In <code>sudo raspi-config</code>, under <strong>Localization Options</strong>, there is a choice for WLAN legal settings:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-27-1"><a id="__codelineno-27-1" name="__codelineno-27-1" href="#__codelineno-27-1"></a>Localization...<span class="w"> </span>&gt;<span class="w"> </span>WLAN<span class="w"> </span>Country...<span class="w"> </span>&gt;<span class="w"> </span>US
</span></code></pre></div>
<blockquote>
<p><strong>FixMe:</strong> Figure out what we need to set for WLAN legal.</p>
<p><strong>FixMe:</strong> Does this also impact Bluetooth?</p>
</blockquote>
<hr />
<h2 id="get-updates-and-required-installs">Get Updates and Required Installs<a class="headerlink" href="#get-updates-and-required-installs" title="Permalink">&para;</a></h2>
<blockquote>
<p><strong>Reference:</strong> <a href="https://blog.r0b.io/post/minimal-rpi-kiosk/">Minimal RPi Kiosk Setup</a></p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-28-1"><a id="__codelineno-28-1" name="__codelineno-28-1" href="#__codelineno-28-1"></a><span class="c1"># sudo apt-get update # now included in pi_hmi</span>
</span><span id="__span-28-2"><a id="__codelineno-28-2" name="__codelineno-28-2" href="#__codelineno-28-2"></a>
</span><span id="__span-28-3"><a id="__codelineno-28-3" name="__codelineno-28-3" href="#__codelineno-28-3"></a><span class="c1"># Install browser and X environment</span>
</span><span id="__span-28-4"><a id="__codelineno-28-4" name="__codelineno-28-4" href="#__codelineno-28-4"></a><span class="c1"># sudo apt-get install -y --no-install-recommends xserver-xorg-video-all \</span>
</span><span id="__span-28-5"><a id="__codelineno-28-5" name="__codelineno-28-5" href="#__codelineno-28-5"></a><span class="c1">#   xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils \</span>
</span><span id="__span-28-6"><a id="__codelineno-28-6" name="__codelineno-28-6" href="#__codelineno-28-6"></a><span class="c1">#   chromium-browser unclutter # now in pi_hmi</span>
</span><span id="__span-28-7"><a id="__codelineno-28-7" name="__codelineno-28-7" href="#__codelineno-28-7"></a>
</span><span id="__span-28-8"><a id="__codelineno-28-8" name="__codelineno-28-8" href="#__codelineno-28-8"></a><span class="c1"># Install Chromium trust store manager</span>
</span><span id="__span-28-9"><a id="__codelineno-28-9" name="__codelineno-28-9" href="#__codelineno-28-9"></a><span class="c1"># sudo apt-get install -y libnss3-tools # now in pi_hmi</span>
</span><span id="__span-28-10"><a id="__codelineno-28-10" name="__codelineno-28-10" href="#__codelineno-28-10"></a>
</span><span id="__span-28-11"><a id="__codelineno-28-11" name="__codelineno-28-11" href="#__codelineno-28-11"></a><span class="c1"># Install MyQR for QR code generation</span>
</span><span id="__span-28-12"><a id="__codelineno-28-12" name="__codelineno-28-12" href="#__codelineno-28-12"></a><span class="c1"># sudo apt-get install -y python3-pip</span>
</span><span id="__span-28-13"><a id="__codelineno-28-13" name="__codelineno-28-13" href="#__codelineno-28-13"></a><span class="c1"># sudo pip3 install MyQR</span>
</span><span id="__span-28-14"><a id="__codelineno-28-14" name="__codelineno-28-14" href="#__codelineno-28-14"></a><span class="c1"># sudo apt-get install libopenjp2-7 # now in pi_hmi</span>
</span><span id="__span-28-15"><a id="__codelineno-28-15" name="__codelineno-28-15" href="#__codelineno-28-15"></a>
</span><span id="__span-28-16"><a id="__codelineno-28-16" name="__codelineno-28-16" href="#__codelineno-28-16"></a><span class="c1"># Install Privoxy for whitelisting</span>
</span><span id="__span-28-17"><a id="__codelineno-28-17" name="__codelineno-28-17" href="#__codelineno-28-17"></a><span class="c1"># sudo apt-get install -y privoxy # now in pi_hmi</span>
</span></code></pre></div>
<blockquote>
<p><strong>Note:</strong> The above packages are now included in <code>pi_hmi</code>. Continue installing manually until <code>hmi</code> can be debugged.</p>
</blockquote>
<p>Log in as <strong>cah-pi-su</strong> user before proceeding.</p>
<hr />
<h2 id="install-necessary-packages">Install Necessary Packages<a class="headerlink" href="#install-necessary-packages" title="Permalink">&para;</a></h2>
<h3 id="step-1-update-and-upgrade"><strong>Step 1: Update and Upgrade</strong><a class="headerlink" href="#step-1-update-and-upgrade" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-29-1"><a id="__codelineno-29-1" name="__codelineno-29-1" href="#__codelineno-29-1"></a>sudo<span class="w"> </span>apt<span class="w"> </span>-y<span class="w"> </span>update
</span><span id="__span-29-2"><a id="__codelineno-29-2" name="__codelineno-29-2" href="#__codelineno-29-2"></a>
</span><span id="__span-29-3"><a id="__codelineno-29-3" name="__codelineno-29-3" href="#__codelineno-29-3"></a><span class="c1"># This next one takes a while</span>
</span><span id="__span-29-4"><a id="__codelineno-29-4" name="__codelineno-29-4" href="#__codelineno-29-4"></a>sudo<span class="w"> </span>apt<span class="w"> </span>-y<span class="w"> </span>upgrade
</span></code></pre></div>
<h3 id="step-2-fix-unfinished-package-configurations"><strong>Step 2: Fix Unfinished Package Configurations</strong><a class="headerlink" href="#step-2-fix-unfinished-package-configurations" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-30-1"><a id="__codelineno-30-1" name="__codelineno-30-1" href="#__codelineno-30-1"></a>sudo<span class="w"> </span>dpkg<span class="w"> </span>--configure<span class="w"> </span>-a
</span></code></pre></div>
<h3 id="step-3-install-core-dependencies"><strong>Step 3: Install Core Dependencies</strong><a class="headerlink" href="#step-3-install-core-dependencies" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-31-1"><a id="__codelineno-31-1" name="__codelineno-31-1" href="#__codelineno-31-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>privoxy
</span><span id="__span-31-2"><a id="__codelineno-31-2" name="__codelineno-31-2" href="#__codelineno-31-2"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>python3-pip
</span></code></pre></div>
<h3 id="step-4-fix-python-package-manager-issues-if-needed"><strong>Step 4: Fix Python Package Manager Issues (If Needed)</strong><a class="headerlink" href="#step-4-fix-python-package-manager-issues-if-needed" title="Permalink">&para;</a></h3>
<blockquote>
<p>Added 2024.03.12</p>
<p><strong>Reference:</strong> <a href="https://www.makeuseof.com/fix-pip-error-externally-managed-environment-linux/">Fixing Pip Error in Linux</a></p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-32-1"><a id="__codelineno-32-1" name="__codelineno-32-1" href="#__codelineno-32-1"></a>ls<span class="w"> </span>-l<span class="w"> </span>/usr/lib/<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>python3
</span><span id="__span-32-2"><a id="__codelineno-32-2" name="__codelineno-32-2" href="#__codelineno-32-2"></a><span class="c1"># Modify the following line based on the Python version found above</span>
</span><span id="__span-32-3"><a id="__codelineno-32-3" name="__codelineno-32-3" href="#__codelineno-32-3"></a>sudo<span class="w"> </span>rm<span class="w"> </span>/usr/lib/python3.11/EXTERNALLY-MANAGED
</span></code></pre></div>
<h3 id="step-5-install-myqr-for-qr-code-generation"><strong>Step 5: Install MyQR for QR Code Generation</strong><a class="headerlink" href="#step-5-install-myqr-for-qr-code-generation" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-33-1"><a id="__codelineno-33-1" name="__codelineno-33-1" href="#__codelineno-33-1"></a>sudo<span class="w"> </span>pip3<span class="w"> </span>install<span class="w"> </span>MyQR
</span></code></pre></div>
<p><strong>If the above fails, try:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-34-1"><a id="__codelineno-34-1" name="__codelineno-34-1" href="#__codelineno-34-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>cmake
</span><span id="__span-34-2"><a id="__codelineno-34-2" name="__codelineno-34-2" href="#__codelineno-34-2"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libblas-dev
</span><span id="__span-34-3"><a id="__codelineno-34-3" name="__codelineno-34-3" href="#__codelineno-34-3"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libblas3<span class="w"> </span>liblapack3<span class="w"> </span>liblapack-dev<span class="w"> </span>libblas-dev
</span><span id="__span-34-4"><a id="__codelineno-34-4" name="__codelineno-34-4" href="#__codelineno-34-4"></a>
</span><span id="__span-34-5"><a id="__codelineno-34-5" name="__codelineno-34-5" href="#__codelineno-34-5"></a><span class="c1"># Fix dependencies for MyQR</span>
</span><span id="__span-34-6"><a id="__codelineno-34-6" name="__codelineno-34-6" href="#__codelineno-34-6"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libjpeg-dev<span class="w"> </span>zlib1g-dev
</span><span id="__span-34-7"><a id="__codelineno-34-7" name="__codelineno-34-7" href="#__codelineno-34-7"></a>sudo<span class="w"> </span>pip3<span class="w"> </span>install<span class="w"> </span>MyQR<span class="w"> </span>--break-system-packages
</span></code></pre></div>
<blockquote>
<p>Reference: <a href="https://pillow.readthedocs.io/en/latest/installation.html">Pillow Installation</a></p>
</blockquote>
<p><strong>Test the installation:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-35-1"><a id="__codelineno-35-1" name="__codelineno-35-1" href="#__codelineno-35-1"></a>sudo<span class="w"> </span>python3<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;from MyQR import myqr as mq&quot;</span>
</span><span id="__span-35-2"><a id="__codelineno-35-2" name="__codelineno-35-2" href="#__codelineno-35-2"></a><span class="c1"># Should return no errors</span>
</span></code></pre></div>
<h3 id="step-6-install-additional-packages"><strong>Step 6: Install Additional Packages</strong><a class="headerlink" href="#step-6-install-additional-packages" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-36-1"><a id="__codelineno-36-1" name="__codelineno-36-1" href="#__codelineno-36-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>libopenjp2-7
</span><span id="__span-36-2"><a id="__codelineno-36-2" name="__codelineno-36-2" href="#__codelineno-36-2"></a>
</span><span id="__span-36-3"><a id="__codelineno-36-3" name="__codelineno-36-3" href="#__codelineno-36-3"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>--no-install-recommends<span class="w"> </span><span class="se">\</span>
</span><span id="__span-36-4"><a id="__codelineno-36-4" name="__codelineno-36-4" href="#__codelineno-36-4"></a><span class="w">    </span>xserver-xorg-video-all<span class="w"> </span>xserver-xorg-input-all<span class="w"> </span><span class="se">\</span>
</span><span id="__span-36-5"><a id="__codelineno-36-5" name="__codelineno-36-5" href="#__codelineno-36-5"></a><span class="w">    </span>xserver-xorg-core<span class="w"> </span>xinit<span class="w"> </span>x11-xserver-utils<span class="w"> </span><span class="se">\</span>
</span><span id="__span-36-6"><a id="__codelineno-36-6" name="__codelineno-36-6" href="#__codelineno-36-6"></a><span class="w">    </span>chromium-browser<span class="w"> </span>unclutter
</span><span id="__span-36-7"><a id="__codelineno-36-7" name="__codelineno-36-7" href="#__codelineno-36-7"></a>
</span><span id="__span-36-8"><a id="__codelineno-36-8" name="__codelineno-36-8" href="#__codelineno-36-8"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libnss3-tools
</span></code></pre></div>
<h3 id="step-7-install-screen-capture-tool"><strong>Step 7: Install Screen Capture Tool</strong><a class="headerlink" href="#step-7-install-screen-capture-tool" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-37-1"><a id="__codelineno-37-1" name="__codelineno-37-1" href="#__codelineno-37-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>scrot
</span></code></pre></div>
<h3 id="step-8-install-required-python-modules-and-utilities"><strong>Step 8: Install Required Python Modules and Utilities</strong><a class="headerlink" href="#step-8-install-required-python-modules-and-utilities" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-38-1"><a id="__codelineno-38-1" name="__codelineno-38-1" href="#__codelineno-38-1"></a><span class="c1"># Required for pi_bluetooth and pi_runner</span>
</span><span id="__span-38-2"><a id="__codelineno-38-2" name="__codelineno-38-2" href="#__codelineno-38-2"></a>sudo<span class="w"> </span>pip3<span class="w"> </span>install<span class="w"> </span>apscheduler<span class="w"> </span>--break-system-packages
</span><span id="__span-38-3"><a id="__codelineno-38-3" name="__codelineno-38-3" href="#__codelineno-38-3"></a>
</span><span id="__span-38-4"><a id="__codelineno-38-4" name="__codelineno-38-4" href="#__codelineno-38-4"></a><span class="c1"># Install system monitoring tools</span>
</span><span id="__span-38-5"><a id="__codelineno-38-5" name="__codelineno-38-5" href="#__codelineno-38-5"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>tmux
</span><span id="__span-38-6"><a id="__codelineno-38-6" name="__codelineno-38-6" href="#__codelineno-38-6"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>nmon
</span></code></pre></div>
<p><strong>To use nmon:</strong></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-39-1"><a id="__codelineno-39-1" name="__codelineno-39-1" href="#__codelineno-39-1"></a>nmon
</span></code></pre></div>
<hr />
<h2 id="edits-to-configure">Edits to Configure<a class="headerlink" href="#edits-to-configure" title="Permalink">&para;</a></h2>
<h3 id="privoxy-configuration">Privoxy Configuration<a class="headerlink" href="#privoxy-configuration" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-40-1"><a id="__codelineno-40-1" name="__codelineno-40-1" href="#__codelineno-40-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/privoxy/config
</span></code></pre></div>
<ul>
<li>Find the line that starts with <code>#debug 1024</code> and <strong>uncomment</strong> it.</li>
<li>This enables logging to <code>/var/log/privoxy/logfile</code>, which starts automatically based on the config change.</li>
<li>Uncommenting this line allows us to collect details about what is blocked.</li>
</ul>
<h3 id="cups-configuration">CUPS Configuration<a class="headerlink" href="#cups-configuration" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-41-1"><a id="__codelineno-41-1" name="__codelineno-41-1" href="#__codelineno-41-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/cups/cupsd.conf
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Change the line:</span><pre><span></span><code><span id="__span-42-1"><a id="__codelineno-42-1" name="__codelineno-42-1" href="#__codelineno-42-1"></a>DefaultAuthType<span class="w"> </span>Basic
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">To:</span><pre><span></span><code><span id="__span-43-1"><a id="__codelineno-43-1" name="__codelineno-43-1" href="#__codelineno-43-1"></a>DefaultAuthType<span class="w"> </span>None
</span></code></pre></div>
<ul>
<li>Restart CUPS:</li>
</ul>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-44-1"><a id="__codelineno-44-1" name="__codelineno-44-1" href="#__codelineno-44-1"></a>sudo<span class="w"> </span>systemctl<span class="w"> </span>restart<span class="w"> </span>cups
</span></code></pre></div>
<hr />
<h2 id="security-audit">Security Audit<a class="headerlink" href="#security-audit" title="Permalink">&para;</a></h2>
<h3 id="install-lynis-security-audit-tool">Install Lynis Security Audit Tool<a class="headerlink" href="#install-lynis-security-audit-tool" title="Permalink">&para;</a></h3>
<blockquote>
<p><a href="https://cisofy.com/lynis/">Lynis Documentation</a></p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-45-1"><a id="__codelineno-45-1" name="__codelineno-45-1" href="#__codelineno-45-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>lynis
</span></code></pre></div>
<h3 id="initial-test-optional">Initial Test (Optional)<a class="headerlink" href="#initial-test-optional" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-46-1"><a id="__codelineno-46-1" name="__codelineno-46-1" href="#__codelineno-46-1"></a>sudo<span class="w"> </span>lynis<span class="w"> </span>audit<span class="w"> </span>system<span class="w"> </span>-Q
</span></code></pre></div>
<h4 id="initial-result">Initial result<a class="headerlink" href="#initial-result" title="Permalink">&para;</a></h4>
<blockquote>
<p>As of 2021-07-24</p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-47-1"><a id="__codelineno-47-1" name="__codelineno-47-1" href="#__codelineno-47-1"></a>Hardening<span class="w"> </span>index<span class="w"> </span>:<span class="w"> </span><span class="m">55</span><span class="w"> </span><span class="o">[</span><span class="c1">###########         ]</span>
</span><span id="__span-47-2"><a id="__codelineno-47-2" name="__codelineno-47-2" href="#__codelineno-47-2"></a>Tests<span class="w"> </span>performed<span class="w"> </span>:<span class="w"> </span><span class="m">213</span>
</span><span id="__span-47-3"><a id="__codelineno-47-3" name="__codelineno-47-3" href="#__codelineno-47-3"></a>Plugins<span class="w"> </span>enabled<span class="w"> </span>:<span class="w"> </span><span class="m">1</span>
</span></code></pre></div>
<h2 id="system-updates">System Updates<a class="headerlink" href="#system-updates" title="Permalink">&para;</a></h2>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-48-1"><a id="__codelineno-48-1" name="__codelineno-48-1" href="#__codelineno-48-1"></a>sudo<span class="w"> </span>apt<span class="w"> </span>update
</span><span id="__span-48-2"><a id="__codelineno-48-2" name="__codelineno-48-2" href="#__codelineno-48-2"></a>sudo<span class="w"> </span>apt<span class="w"> </span>upgrade<span class="w"> </span>-y
</span></code></pre></div>
<hr />
<h2 id="system-hardening">System Hardening<a class="headerlink" href="#system-hardening" title="Permalink">&para;</a></h2>
<h3 id="ssh-configuration-now-done-in-pi_security">SSH Configuration (Now done in <code>pi_security</code>)<a class="headerlink" href="#ssh-configuration-now-done-in-pi_security" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-49-1"><a id="__codelineno-49-1" name="__codelineno-49-1" href="#__codelineno-49-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/ssh/sshd_config
</span></code></pre></div>
<h3 id="update-system-message">Update System Message<a class="headerlink" href="#update-system-message" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-50-1"><a id="__codelineno-50-1" name="__codelineno-50-1" href="#__codelineno-50-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/init.net
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Expected Output</span><pre><span></span><code><span id="__span-51-1"><a id="__codelineno-51-1" name="__codelineno-51-1" href="#__codelineno-51-1"></a>********************************************************************************
</span><span id="__span-51-2"><a id="__codelineno-51-2" name="__codelineno-51-2" href="#__codelineno-51-2"></a>This<span class="w"> </span>computer<span class="w"> </span>system<span class="w"> </span>and<span class="w"> </span>associated<span class="w"> </span>networks<span class="w"> </span>are<span class="w"> </span>the<span class="w"> </span>property<span class="w"> </span>of<span class="w"> </span>and<span class="w"> </span><span class="k">for</span><span class="w"> </span>the
</span><span id="__span-51-3"><a id="__codelineno-51-3" name="__codelineno-51-3" href="#__codelineno-51-3"></a>sole<span class="w"> </span>business<span class="w"> </span>use<span class="w"> </span>of<span class="w"> </span>Cardinal<span class="w"> </span>Health,<span class="w"> </span>Inc.<span class="w"> </span>authorized<span class="w"> </span>users.<span class="w"> </span>Cardinal<span class="w"> </span>Health
</span><span id="__span-51-4"><a id="__codelineno-51-4" name="__codelineno-51-4" href="#__codelineno-51-4"></a>reserves<span class="w"> </span>the<span class="w"> </span>right<span class="w"> </span>to<span class="w"> </span>monitor<span class="w"> </span>computer<span class="w"> </span>and<span class="w"> </span>network<span class="w"> </span>usage,<span class="w"> </span>and<span class="w"> </span>your<span class="w"> </span>use<span class="w"> </span>of
</span><span id="__span-51-5"><a id="__codelineno-51-5" name="__codelineno-51-5" href="#__codelineno-51-5"></a>Cardinal<span class="w"> </span>systems<span class="w"> </span>constitutes<span class="w"> </span>consent<span class="w"> </span>to<span class="w"> </span>such<span class="w"> </span>monitoring.<span class="w"> </span>The<span class="w"> </span>company<span class="err">&#39;</span>s
</span><span id="__span-51-6"><a id="__codelineno-51-6" name="__codelineno-51-6" href="#__codelineno-51-6"></a>computers<span class="w"> </span>and<span class="w"> </span>the<span class="w"> </span>proprietary<span class="w"> </span>data<span class="w"> </span>and<span class="w"> </span>information<span class="w"> </span>stored<span class="w"> </span>on<span class="w"> </span>them<span class="w"> </span>remain<span class="w"> </span>at<span class="w"> </span>all
</span><span id="__span-51-7"><a id="__codelineno-51-7" name="__codelineno-51-7" href="#__codelineno-51-7"></a><span class="nb">times</span><span class="w"> </span>the<span class="w"> </span>property<span class="w"> </span>of<span class="w"> </span>Cardinal<span class="w"> </span>Health,<span class="w"> </span>Inc.<span class="w"> </span>Unauthorized<span class="w"> </span>access<span class="w"> </span>to<span class="w"> </span>this<span class="w"> </span>system
</span><span id="__span-51-8"><a id="__codelineno-51-8" name="__codelineno-51-8" href="#__codelineno-51-8"></a>is<span class="w"> </span>strictly<span class="w"> </span>forbidden.<span class="w"> </span>This<span class="w"> </span>server<span class="w"> </span>and<span class="w"> </span>your<span class="w"> </span>actions<span class="w"> </span>after<span class="w"> </span>login<span class="w"> </span>are<span class="w"> </span>monitored.
</span><span id="__span-51-9"><a id="__codelineno-51-9" name="__codelineno-51-9" href="#__codelineno-51-9"></a>********************************************************************************
</span></code></pre></div>
<h3 id="restrict-access-to-compilers">Restrict Access to Compilers<a class="headerlink" href="#restrict-access-to-compilers" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-52-1"><a id="__codelineno-52-1" name="__codelineno-52-1" href="#__codelineno-52-1"></a>sudo<span class="w"> </span>chmod<span class="w"> </span>o-rx<span class="w"> </span>/usr/bin/gcc
</span><span id="__span-52-2"><a id="__codelineno-52-2" name="__codelineno-52-2" href="#__codelineno-52-2"></a>sudo<span class="w"> </span>chmod<span class="w"> </span>o-rx<span class="w"> </span>/usr/bin/g++
</span></code></pre></div>
<h3 id="disable-usb-storage">Disable USB Storage<a class="headerlink" href="#disable-usb-storage" title="Permalink">&para;</a></h3>
<p><a href="https://www.cyberciti.biz/faq/linux-disable-modprobe-loading-of-usb-storage-driver/">Reference</a></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-53-1"><a id="__codelineno-53-1" name="__codelineno-53-1" href="#__codelineno-53-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/modprobe.d/blacklist.conf
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-54-1"><a id="__codelineno-54-1" name="__codelineno-54-1" href="#__codelineno-54-1"></a>blacklist<span class="w"> </span>usb-storage
</span></code></pre></div>
<details class="info" open="open">
<summary>Skip doing this test, unless you want to know the numbers now</summary>
<p><div class="language-sh highlight"><pre><span></span><code><span id="__span-55-1"><a id="__codelineno-55-1" name="__codelineno-55-1" href="#__codelineno-55-1"></a>sudo<span class="w"> </span>lynis<span class="w"> </span>audit<span class="w"> </span>system<span class="w"> </span>-Q
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-56-1"><a id="__codelineno-56-1" name="__codelineno-56-1" href="#__codelineno-56-1"></a>Hardening<span class="w"> </span>index<span class="w"> </span>:<span class="w"> </span><span class="m">55</span><span class="w"> </span><span class="o">[</span><span class="c1">###########         ]</span>
</span><span id="__span-56-2"><a id="__codelineno-56-2" name="__codelineno-56-2" href="#__codelineno-56-2"></a>Tests<span class="w"> </span>performed<span class="w"> </span>:<span class="w"> </span><span class="m">213</span>
</span><span id="__span-56-3"><a id="__codelineno-56-3" name="__codelineno-56-3" href="#__codelineno-56-3"></a>Plugins<span class="w"> </span>enabled<span class="w"> </span>:<span class="w"> </span><span class="m">1</span>
</span></code></pre></div></p>
</details>
<hr />
<h2 id="network-configuration">Network Configuration<a class="headerlink" href="#network-configuration" title="Permalink">&para;</a></h2>
<h3 id="network-manager-installation">Network Manager Installation<a class="headerlink" href="#network-manager-installation" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-57-1"><a id="__codelineno-57-1" name="__codelineno-57-1" href="#__codelineno-57-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>update
</span><span id="__span-57-2"><a id="__codelineno-57-2" name="__codelineno-57-2" href="#__codelineno-57-2"></a>sudo<span class="w"> </span>apt<span class="w"> </span>-y<span class="w"> </span>--fix-broken<span class="w"> </span>install
</span><span id="__span-57-3"><a id="__codelineno-57-3" name="__codelineno-57-3" href="#__codelineno-57-3"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>network-manager
</span></code></pre></div>
<h3 id="set-network-manager-as-primary">Set Network Manager as Primary<a class="headerlink" href="#set-network-manager-as-primary" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-58-1"><a id="__codelineno-58-1" name="__codelineno-58-1" href="#__codelineno-58-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/dhcpcd.conf
</span></code></pre></div>
<div class="language-bash highlight"><span class="filename">Add the following lines at the end:</span><pre><span></span><code><span id="__span-59-1"><a id="__codelineno-59-1" name="__codelineno-59-1" href="#__codelineno-59-1"></a>denyinterfaces<span class="w"> </span>wlan0
</span><span id="__span-59-2"><a id="__codelineno-59-2" name="__codelineno-59-2" href="#__codelineno-59-2"></a>denyinterfaces<span class="w"> </span>wlan1
</span></code></pre></div>
<h3 id="disable-wifi-mac-address-randomization">Disable WiFi MAC Address Randomization<a class="headerlink" href="#disable-wifi-mac-address-randomization" title="Permalink">&para;</a></h3>
<p><a href="https://www.raspberrypi.org/forums/viewtopic.php?t=237623">Reference</a></p>
<p>Configure Network Manager to not randomize the WiFi Mac Address</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-60-1"><a id="__codelineno-60-1" name="__codelineno-60-1" href="#__codelineno-60-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/NetworkManager/conf.d/100-disable-wifi-mac-randomization.conf
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-61-1"><a id="__codelineno-61-1" name="__codelineno-61-1" href="#__codelineno-61-1"></a><span class="o">[</span>connection<span class="o">]</span>
</span><span id="__span-61-2"><a id="__codelineno-61-2" name="__codelineno-61-2" href="#__codelineno-61-2"></a>wifi.mac-address-randomization<span class="o">=</span><span class="m">1</span>
</span><span id="__span-61-3"><a id="__codelineno-61-3" name="__codelineno-61-3" href="#__codelineno-61-3"></a>
</span><span id="__span-61-4"><a id="__codelineno-61-4" name="__codelineno-61-4" href="#__codelineno-61-4"></a><span class="o">[</span>device<span class="o">]</span>
</span><span id="__span-61-5"><a id="__codelineno-61-5" name="__codelineno-61-5" href="#__codelineno-61-5"></a>wifi.scan-rand-mac-address<span class="o">=</span>no
</span></code></pre></div>
<h3 id="reboot-to-apply-changes">Reboot to Apply Changes<a class="headerlink" href="#reboot-to-apply-changes" title="Permalink">&para;</a></h3>
<p>To fix the randomized <code>wlan0</code> mac address</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-62-1"><a id="__codelineno-62-1" name="__codelineno-62-1" href="#__codelineno-62-1"></a>sudo<span class="w"> </span>reboot
</span></code></pre></div>
<hr />
<h2 id="config-browser">Config Browser<a class="headerlink" href="#config-browser" title="Permalink">&para;</a></h2>
<p><a href="https://pimylifeup.com/raspberry-pi-kiosk/">Source: Raspberry Pi Kiosk Setup</a></p>
<h3 id="configure-xinitrc">Configure <code>.xinitrc</code><a class="headerlink" href="#configure-xinitrc" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-63-1"><a id="__codelineno-63-1" name="__codelineno-63-1" href="#__codelineno-63-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/home/<USER>/.xinitrc
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Contents</span><pre><span></span><code><span id="__span-64-1"><a id="__codelineno-64-1" name="__codelineno-64-1" href="#__codelineno-64-1"></a><span class="ch">#!/usr/bin/env sh</span>
</span><span id="__span-64-2"><a id="__codelineno-64-2" name="__codelineno-64-2" href="#__codelineno-64-2"></a>xset<span class="w"> </span>-dpms
</span><span id="__span-64-3"><a id="__codelineno-64-3" name="__codelineno-64-3" href="#__codelineno-64-3"></a>xset<span class="w"> </span>s<span class="w"> </span>off
</span><span id="__span-64-4"><a id="__codelineno-64-4" name="__codelineno-64-4" href="#__codelineno-64-4"></a>xset<span class="w"> </span>s<span class="w"> </span>noblank
</span><span id="__span-64-5"><a id="__codelineno-64-5" name="__codelineno-64-5" href="#__codelineno-64-5"></a>
</span><span id="__span-64-6"><a id="__codelineno-64-6" name="__codelineno-64-6" href="#__codelineno-64-6"></a><span class="nv">screen_width</span><span class="o">=</span><span class="s2">&quot;</span><span class="k">$(</span>fbset<span class="w"> </span>-s<span class="w"> </span><span class="p">|</span><span class="w"> </span>awk<span class="w"> </span><span class="s1">&#39;$1 == &quot;geometry&quot; {print $2}&#39;</span><span class="k">)</span><span class="s2">&quot;</span>
</span><span id="__span-64-7"><a id="__codelineno-64-7" name="__codelineno-64-7" href="#__codelineno-64-7"></a><span class="nv">screen_height</span><span class="o">=</span><span class="s2">&quot;</span><span class="k">$(</span>fbset<span class="w"> </span>-s<span class="w"> </span><span class="p">|</span><span class="w"> </span>awk<span class="w"> </span><span class="s1">&#39;$1 == &quot;geometry&quot; {print $3}&#39;</span><span class="k">)</span><span class="s2">&quot;</span>
</span><span id="__span-64-8"><a id="__codelineno-64-8" name="__codelineno-64-8" href="#__codelineno-64-8"></a>
</span><span id="__span-64-9"><a id="__codelineno-64-9" name="__codelineno-64-9" href="#__codelineno-64-9"></a><span class="c1"># Disable keyboard shortcuts</span>
</span><span id="__span-64-10"><a id="__codelineno-64-10" name="__codelineno-64-10" href="#__codelineno-64-10"></a>unclutter<span class="w"> </span>-idle<span class="w"> </span><span class="m">2</span><span class="w"> </span><span class="p">&amp;</span>
</span><span id="__span-64-11"><a id="__codelineno-64-11" name="__codelineno-64-11" href="#__codelineno-64-11"></a>xmodmap<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;keycode 37=&quot;</span>
</span><span id="__span-64-12"><a id="__codelineno-64-12" name="__codelineno-64-12" href="#__codelineno-64-12"></a>xmodmap<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;keycode 67=&quot;</span>
</span><span id="__span-64-13"><a id="__codelineno-64-13" name="__codelineno-64-13" href="#__codelineno-64-13"></a>xmodmap<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;keycode 105=&quot;</span>
</span><span id="__span-64-14"><a id="__codelineno-64-14" name="__codelineno-64-14" href="#__codelineno-64-14"></a>xmodmap<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;keycode 133=&quot;</span>
</span><span id="__span-64-15"><a id="__codelineno-64-15" name="__codelineno-64-15" href="#__codelineno-64-15"></a>xmodmap<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;keycode 134=&quot;</span>
</span><span id="__span-64-16"><a id="__codelineno-64-16" name="__codelineno-64-16" href="#__codelineno-64-16"></a>
</span><span id="__span-64-17"><a id="__codelineno-64-17" name="__codelineno-64-17" href="#__codelineno-64-17"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/home/<USER>/.config/chromium/Singleton*
</span><span id="__span-64-18"><a id="__codelineno-64-18" name="__codelineno-64-18" href="#__codelineno-64-18"></a>
</span><span id="__span-64-19"><a id="__codelineno-64-19" name="__codelineno-64-19" href="#__codelineno-64-19"></a><span class="k">while</span><span class="w"> </span>true<span class="p">;</span><span class="w"> </span><span class="k">do</span>
</span><span id="__span-64-20"><a id="__codelineno-64-20" name="__codelineno-64-20" href="#__codelineno-64-20"></a><span class="w">    </span>/cardinal/browserstart
</span><span id="__span-64-21"><a id="__codelineno-64-21" name="__codelineno-64-21" href="#__codelineno-64-21"></a><span class="k">done</span><span class="p">;</span>
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-65-1"><a id="__codelineno-65-1" name="__codelineno-65-1" href="#__codelineno-65-1"></a>sudo<span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>worker:worker<span class="w"> </span>/home/<USER>/.xinitrc
</span></code></pre></div>
<blockquote>
<p><code># https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login</code></p>
</blockquote>
<h3 id="set-up-local-html-directory">Set up local HTML directory<a class="headerlink" href="#set-up-local-html-directory" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-66-1"><a id="__codelineno-66-1" name="__codelineno-66-1" href="#__codelineno-66-1"></a>sudo<span class="w"> </span>mkdir<span class="w"> </span>/cardinal
</span><span id="__span-66-2"><a id="__codelineno-66-2" name="__codelineno-66-2" href="#__codelineno-66-2"></a>sudo<span class="w"> </span>mkdir<span class="w"> </span>/cardinal/localhtml
</span><span id="__span-66-3"><a id="__codelineno-66-3" name="__codelineno-66-3" href="#__codelineno-66-3"></a>sudo<span class="w"> </span>su
</span><span id="__span-66-4"><a id="__codelineno-66-4" name="__codelineno-66-4" href="#__codelineno-66-4"></a>
</span><span id="__span-66-5"><a id="__codelineno-66-5" name="__codelineno-66-5" href="#__codelineno-66-5"></a><span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;blank page&quot;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/localhtml/index.html
</span><span id="__span-66-6"><a id="__codelineno-66-6" name="__codelineno-66-6" href="#__codelineno-66-6"></a><span class="nb">exit</span>
</span></code></pre></div>
<h3 id="configure-browser-startup-script">Configure browser startup script<a class="headerlink" href="#configure-browser-startup-script" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-67-1"><a id="__codelineno-67-1" name="__codelineno-67-1" href="#__codelineno-67-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/cardinal/browserstart_default
</span></code></pre></div>
<p>The following file is also maintained by pi_runner, so make matching changes there.</p>
<div class="language-sh highlight"><span class="filename">Contents</span><pre><span></span><code><span id="__span-68-1"><a id="__codelineno-68-1" name="__codelineno-68-1" href="#__codelineno-68-1"></a>cp<span class="w"> </span>/cardinal/localhtml/chromium_Default_Preferences<span class="w"> </span>/home/<USER>/.config/chromium/Default/Preferences
</span><span id="__span-68-2"><a id="__codelineno-68-2" name="__codelineno-68-2" href="#__codelineno-68-2"></a>
</span><span id="__span-68-3"><a id="__codelineno-68-3" name="__codelineno-68-3" href="#__codelineno-68-3"></a>chromium-browser<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-4"><a id="__codelineno-68-4" name="__codelineno-68-4" href="#__codelineno-68-4"></a><span class="w">  </span>--proxy-server<span class="o">=</span><span class="s2">&quot;https=127.0.0.1:8118;http=127.0.01:8118&quot;</span><span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-5"><a id="__codelineno-68-5" name="__codelineno-68-5" href="#__codelineno-68-5"></a><span class="w">  </span>--window-size<span class="o">=</span><span class="nv">$screen_width</span>,<span class="nv">$screen_height</span><span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-6"><a id="__codelineno-68-6" name="__codelineno-68-6" href="#__codelineno-68-6"></a><span class="w">  </span>--window-position<span class="o">=</span><span class="m">0</span>,0<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-7"><a id="__codelineno-68-7" name="__codelineno-68-7" href="#__codelineno-68-7"></a><span class="w">  </span>--start-fullscreen<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-8"><a id="__codelineno-68-8" name="__codelineno-68-8" href="#__codelineno-68-8"></a><span class="w">  </span>--incognito<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-9"><a id="__codelineno-68-9" name="__codelineno-68-9" href="#__codelineno-68-9"></a><span class="w">  </span>--noerrdialogs<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-10"><a id="__codelineno-68-10" name="__codelineno-68-10" href="#__codelineno-68-10"></a><span class="w">  </span>--disable-translate<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-11"><a id="__codelineno-68-11" name="__codelineno-68-11" href="#__codelineno-68-11"></a><span class="w">  </span>--no-first-run<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-12"><a id="__codelineno-68-12" name="__codelineno-68-12" href="#__codelineno-68-12"></a><span class="w">  </span>--fast<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-13"><a id="__codelineno-68-13" name="__codelineno-68-13" href="#__codelineno-68-13"></a><span class="w">  </span>--fast-start<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-14"><a id="__codelineno-68-14" name="__codelineno-68-14" href="#__codelineno-68-14"></a><span class="w">  </span>--disable-infobars<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-15"><a id="__codelineno-68-15" name="__codelineno-68-15" href="#__codelineno-68-15"></a><span class="w">  </span>--disable-features<span class="o">=</span>TranslateUI<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-16"><a id="__codelineno-68-16" name="__codelineno-68-16" href="#__codelineno-68-16"></a><span class="w">  </span>--disable-features<span class="o">=</span>Translate<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-17"><a id="__codelineno-68-17" name="__codelineno-68-17" href="#__codelineno-68-17"></a><span class="w">  </span>--disk-cache-dir<span class="o">=</span>/dev/null<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-18"><a id="__codelineno-68-18" name="__codelineno-68-18" href="#__codelineno-68-18"></a><span class="w">  </span>--overscroll-history-navigation<span class="o">=</span><span class="m">0</span><span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-19"><a id="__codelineno-68-19" name="__codelineno-68-19" href="#__codelineno-68-19"></a><span class="w">  </span>--disable-pinch<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-20"><a id="__codelineno-68-20" name="__codelineno-68-20" href="#__codelineno-68-20"></a><span class="w">  </span>--kiosk<span class="w"> </span><span class="se">\</span>
</span><span id="__span-68-21"><a id="__codelineno-68-21" name="__codelineno-68-21" href="#__codelineno-68-21"></a><span class="w">  </span>file:///cardinal/localhtml/index.html
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-69-1"><a id="__codelineno-69-1" name="__codelineno-69-1" href="#__codelineno-69-1"></a>sudo<span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>worker:worker<span class="w"> </span>/cardinal/browserstart_default
</span><span id="__span-69-2"><a id="__codelineno-69-2" name="__codelineno-69-2" href="#__codelineno-69-2"></a>sudo<span class="w"> </span>chmod<span class="w"> </span>+x<span class="w"> </span>/cardinal/browserstart_default
</span><span id="__span-69-3"><a id="__codelineno-69-3" name="__codelineno-69-3" href="#__codelineno-69-3"></a>sudo<span class="w"> </span>cp<span class="w"> </span>/cardinal/browserstart_default<span class="w"> </span>/cardinal/browserstart
</span></code></pre></div>
<hr />
<h2 id="startx-issues-on-raspberry-pi-5">Startx Issues on Raspberry Pi 5<a class="headerlink" href="#startx-issues-on-raspberry-pi-5" title="Permalink">&para;</a></h2>
<blockquote>
<p>Added 2024-03-12</p>
</blockquote>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-70-1"><a id="__codelineno-70-1" name="__codelineno-70-1" href="#__codelineno-70-1"></a>bash:<span class="w"> </span>startx:<span class="w"> </span><span class="nb">command</span><span class="w"> </span>not<span class="w"> </span>found
</span></code></pre></div>
<p><a href="https://forums.raspberrypi.com/viewtopic.php?t=361722">Reference</a></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-71-1"><a id="__codelineno-71-1" name="__codelineno-71-1" href="#__codelineno-71-1"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>gldriver-test
</span></code></pre></div>
<p><a href="https://iiab.me/kiwix/raspberrypi.stackexchange.com_en_all_2022-11/questions/84804/how-to-start-gui-with-startx-command-not-found">Reference</a></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-72-1"><a id="__codelineno-72-1" name="__codelineno-72-1" href="#__codelineno-72-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>xinit<span class="w"> </span>-y
</span></code></pre></div>
<hr />
<h2 id="runner-fails-2024-03-12">Runner Fails (2024-03-12)<a class="headerlink" href="#runner-fails-2024-03-12" title="Permalink">&para;</a></h2>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-73-1"><a id="__codelineno-73-1" name="__codelineno-73-1" href="#__codelineno-73-1"></a>Mar<span class="w"> </span><span class="m">13</span><span class="w"> </span><span class="m">07</span>:57:07<span class="w"> </span>raspberrypi<span class="w"> </span>pi-runner<span class="o">[</span><span class="m">945</span><span class="o">]</span>:
</span><span id="__span-73-2"><a id="__codelineno-73-2" name="__codelineno-73-2" href="#__codelineno-73-2"></a>zoneinfo._common.ZoneInfoNotFoundError:<span class="w"> </span><span class="s1">&#39;Multiple conflicting time zone configurations found:\n/etc/timezone:</span>
</span><span id="__span-73-3"><a id="__codelineno-73-3" name="__codelineno-73-3" href="#__codelineno-73-3"></a><span class="s1">Europe/London\n/etc/localtime is a symlink to: America/New_York\nFix the configuration, or set the time zone in a TZ environment variable.\n&#39;</span>
</span></code></pre></div>
<h3 id="check-timezone">Check Timezone<a class="headerlink" href="#check-timezone" title="Permalink">&para;</a></h3>
<div class="admonition info">
<p class="admonition-title">Check Current Timezone Configuration</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-74-1"><a id="__codelineno-74-1" name="__codelineno-74-1" href="#__codelineno-74-1"></a>cat<span class="w"> </span>/etc/timezone
</span><span id="__span-74-2"><a id="__codelineno-74-2" name="__codelineno-74-2" href="#__codelineno-74-2"></a>Europe/London
</span><span id="__span-74-3"><a id="__codelineno-74-3" name="__codelineno-74-3" href="#__codelineno-74-3"></a>ls<span class="w"> </span>-l<span class="w"> </span>/etc/localtime
</span><span id="__span-74-4"><a id="__codelineno-74-4" name="__codelineno-74-4" href="#__codelineno-74-4"></a>lrwxrwxrwx<span class="w"> </span><span class="m">1</span><span class="w"> </span>root<span class="w"> </span>root<span class="w"> </span><span class="m">38</span><span class="w"> </span>Mar<span class="w"> </span><span class="m">12</span><span class="w"> </span><span class="m">15</span>:12<span class="w"> </span>/etc/localtime<span class="w"> </span>-&gt;<span class="w"> </span>../usr/share/zoneinfo/America/New_York
</span></code></pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Fix Timezone Issue</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-75-1"><a id="__codelineno-75-1" name="__codelineno-75-1" href="#__codelineno-75-1"></a>sudo<span class="w"> </span>rm<span class="w"> </span>/etc/localtime
</span></code></pre></div>
</div>
<div class="admonition tip">
<p class="admonition-title">Note</p>
<p>After removing the conflicting timezone link, the system will use the timezone specified in <code>/etc/timezone</code></p>
</div>
<hr />
<h2 id="disable-pi5-power-button">Disable Pi5 Power Button<a class="headerlink" href="#disable-pi5-power-button" title="Permalink">&para;</a></h2>
<blockquote>
<p>Added 2024.03.12</p>
<p><a href="https://forums.raspberrypi.com/viewtopic.php?p=2188834&amp;hilit=power+button+disable#p2188834">Reference</a></p>
</blockquote>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-76-1"><a id="__codelineno-76-1" name="__codelineno-76-1" href="#__codelineno-76-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/systemd/logind.conf
</span></code></pre></div>
<p>Add the following configuration:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-77-1"><a id="__codelineno-77-1" name="__codelineno-77-1" href="#__codelineno-77-1"></a><span class="o">[</span>Login<span class="o">]</span>
</span><span id="__span-77-2"><a id="__codelineno-77-2" name="__codelineno-77-2" href="#__codelineno-77-2"></a><span class="nv">HandlePowerKey</span><span class="o">=</span>ignore
</span><span id="__span-77-3"><a id="__codelineno-77-3" name="__codelineno-77-3" href="#__codelineno-77-3"></a><span class="nv">HandlePowerKeyLongPress</span><span class="o">=</span>ignore
</span></code></pre></div>
<div class="admonition warning">
<p class="admonition-title">FixMe: Update/Upgrade Considerations</p>
<p>Does this setting get modified when we do update/upgrade activity?
If so, then do it at the end, or better yet, auto fix it if anyone changes it.</p>
</div>
<hr />
<h2 id="create-system-snapshot">Create System Snapshot<a class="headerlink" href="#create-system-snapshot" title="Permalink">&para;</a></h2>
<p>Shut down the system to create a backup image:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-78-1"><a id="__codelineno-78-1" name="__codelineno-78-1" href="#__codelineno-78-1"></a>sudo<span class="w"> </span>shutdown<span class="w"> </span>-h<span class="w"> </span>now
</span></code></pre></div>
<h3 id="make-image-of-the-16gb-card">Make Image of the 16GB Card<a class="headerlink" href="#make-image-of-the-16gb-card" title="Permalink">&para;</a></h3>
<p>Use ApplePi Baker with shrink turned off:</p>
<ul>
<li>2024.03.28 using Pi OS 2023-12-11 → (<code>downloads/20240328/backup3</code>)</li>
<li>2024.04.17 using Pi OS 2024-03-15 → (<code>downloads/20240417/backup1</code>)</li>
<li>2024.04.23 using Pi OS 2024-03-15 → (<code>downloads/20240417/backup1a</code>)</li>
<li>2024.11.25 → <code>Downloads/pi_image_2024.11.25.zip</code></li>
</ul>
<div class="admonition info">
<p class="admonition-title">Testing</p>
<p>Feel free to stop here, reboot, manually log in as worker, then manually run <code>startx</code></p>
</div>
<h3 id="test-browser-process">Test Browser Process<a class="headerlink" href="#test-browser-process" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-79-1"><a id="__codelineno-79-1" name="__codelineno-79-1" href="#__codelineno-79-1"></a><span class="c1"># On the device:</span>
</span><span id="__span-79-2"><a id="__codelineno-79-2" name="__codelineno-79-2" href="#__codelineno-79-2"></a>startx
</span><span id="__span-79-3"><a id="__codelineno-79-3" name="__codelineno-79-3" href="#__codelineno-79-3"></a>
</span><span id="__span-79-4"><a id="__codelineno-79-4" name="__codelineno-79-4" href="#__codelineno-79-4"></a><span class="c1"># On a remote SSH terminal</span>
</span><span id="__span-79-5"><a id="__codelineno-79-5" name="__codelineno-79-5" href="#__codelineno-79-5"></a>ps<span class="w"> </span>ax<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>chromium
</span></code></pre></div>
<hr />
<h2 id="bluetooth-troubleshooting-for-pi45">Bluetooth Troubleshooting for Pi4/5<a class="headerlink" href="#bluetooth-troubleshooting-for-pi45" title="Permalink">&para;</a></h2>
<div class="admonition warning">
<p class="admonition-title">Development Test - Not for Production</p>
<p>This section contains experimental configurations for addressing Bluetooth issues on Pi4 and Pi5 devices.</p>
</div>
<h3 id="initial-approach">Initial Approach<a class="headerlink" href="#initial-approach" title="Permalink">&para;</a></h3>
<p><a href="https://forums.debian.net/viewtopic.php?t=151557">Reference: Debian Forum</a> (Near the bottom of the post)</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-80-1"><a id="__codelineno-80-1" name="__codelineno-80-1" href="#__codelineno-80-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>pulseaudio-module-bluetooth<span class="w"> </span>-y
</span><span id="__span-80-2"><a id="__codelineno-80-2" name="__codelineno-80-2" href="#__codelineno-80-2"></a>pulseaudio<span class="w"> </span>--kill
</span><span id="__span-80-3"><a id="__codelineno-80-3" name="__codelineno-80-3" href="#__codelineno-80-3"></a>pulseaudio<span class="w"> </span>--start
</span><span id="__span-80-4"><a id="__codelineno-80-4" name="__codelineno-80-4" href="#__codelineno-80-4"></a>
</span><span id="__span-80-5"><a id="__codelineno-80-5" name="__codelineno-80-5" href="#__codelineno-80-5"></a>sudo<span class="w"> </span>journalctl<span class="w"> </span>--vacuum-time<span class="o">=</span>1m
</span><span id="__span-80-6"><a id="__codelineno-80-6" name="__codelineno-80-6" href="#__codelineno-80-6"></a>journalctl<span class="w"> </span>-u<span class="w"> </span>pi-bluetooth.service<span class="w"> </span>--no-pager
</span></code></pre></div>
<h3 id="error-analysis">Error Analysis<a class="headerlink" href="#error-analysis" title="Permalink">&para;</a></h3>
<p>After attempting the above solution, the following errors were observed:</p>
<div class="language-text highlight"><pre><span></span><code><span id="__span-81-1"><a id="__codelineno-81-1" name="__codelineno-81-1" href="#__codelineno-81-1"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/vcp.c:vcp_init() D-Bus experimental not enabled
</span><span id="__span-81-2"><a id="__codelineno-81-2" name="__codelineno-81-2" href="#__codelineno-81-2"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init vcp plugin
</span><span id="__span-81-3"><a id="__codelineno-81-3" name="__codelineno-81-3" href="#__codelineno-81-3"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/mcp.c:mcp_init() D-Bus experimental not enabled
</span><span id="__span-81-4"><a id="__codelineno-81-4" name="__codelineno-81-4" href="#__codelineno-81-4"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init mcp plugin
</span><span id="__span-81-5"><a id="__codelineno-81-5" name="__codelineno-81-5" href="#__codelineno-81-5"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/bap.c:bap_init() D-Bus experimental not enabled
</span><span id="__span-81-6"><a id="__codelineno-81-6" name="__codelineno-81-6" href="#__codelineno-81-6"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init bap plugin
</span><span id="__span-81-7"><a id="__codelineno-81-7" name="__codelineno-81-7" href="#__codelineno-81-7"></a>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: Bluetooth management interface 1.22 initialized
</span><span id="__span-81-8"><a id="__codelineno-81-8" name="__codelineno-81-8" href="#__codelineno-81-8"></a>Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/sap/server.c:sap_server_register() Sap driver initialization failed.
</span><span id="__span-81-9"><a id="__codelineno-81-9" name="__codelineno-81-9" href="#__codelineno-81-9"></a>Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: sap-server: Operation not permitted (1)
</span><span id="__span-81-10"><a id="__codelineno-81-10" name="__codelineno-81-10" href="#__codelineno-81-10"></a>Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: Failed to set privacy: Rejected (0x0b)
</span></code></pre></div>
<h3 id="alternative-solution-1">Alternative Solution 1<a class="headerlink" href="#alternative-solution-1" title="Permalink">&para;</a></h3>
<p><a href="https://forums.debian.net/viewtopic.php?t=154544">Reference: Debian Forum</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-82-1"><a id="__codelineno-82-1" name="__codelineno-82-1" href="#__codelineno-82-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>libspa-0.2-bluetooth<span class="w"> </span>-y
</span><span id="__span-82-2"><a id="__codelineno-82-2" name="__codelineno-82-2" href="#__codelineno-82-2"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>remove<span class="w"> </span>pulseaudio-module-bluetooth<span class="w"> </span>-y
</span><span id="__span-82-3"><a id="__codelineno-82-3" name="__codelineno-82-3" href="#__codelineno-82-3"></a>systemctl<span class="w"> </span>--user<span class="w"> </span>start<span class="w"> </span>pulseaudio.socket
</span></code></pre></div>
<h3 id="alternative-solution-2">Alternative Solution 2<a class="headerlink" href="#alternative-solution-2" title="Permalink">&para;</a></h3>
<p><a href="https://www.reddit.com/r/archlinux/comments/yu9az9/bluetooth_errors_since_2_days_ago/">Reference: Reddit</a></p>
<p>A user reported fixing similar issues with the following commands:</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-83-1"><a id="__codelineno-83-1" name="__codelineno-83-1" href="#__codelineno-83-1"></a>sudo<span class="w"> </span>rmmod<span class="w"> </span>btusb
</span><span id="__span-83-2"><a id="__codelineno-83-2" name="__codelineno-83-2" href="#__codelineno-83-2"></a>sudo<span class="w"> </span>rmmod<span class="w"> </span>btintel
</span><span id="__span-83-3"><a id="__codelineno-83-3" name="__codelineno-83-3" href="#__codelineno-83-3"></a>sudo<span class="w"> </span>modprobe<span class="w"> </span>btintel
</span><span id="__span-83-4"><a id="__codelineno-83-4" name="__codelineno-83-4" href="#__codelineno-83-4"></a>sudo<span class="w"> </span>modprobe<span class="w"> </span>btusb
</span></code></pre></div>
<h3 id="additional-packages-20240417">Additional Packages (2024.04.17)<a class="headerlink" href="#additional-packages-20240417" title="Permalink">&para;</a></h3>
<p><a href="https://pimylifeup.com/raspberry-pi-bluetooth/">Reference: Pi My Life Up</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-84-1"><a id="__codelineno-84-1" name="__codelineno-84-1" href="#__codelineno-84-1"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>bluetooth<span class="w"> </span>pi-bluetooth<span class="w"> </span>bluez<span class="w"> </span>blueman
</span></code></pre></div>
<div class="admonition note">
<p>Result: No improvement observed</p>
</div>
<h3 id="system-diagnostics">System Diagnostics<a class="headerlink" href="#system-diagnostics" title="Permalink">&para;</a></h3>
<p>Useful commands to check Bluetooth version information:</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-85-1"><a id="__codelineno-85-1" name="__codelineno-85-1" href="#__codelineno-85-1"></a>bluetoothctl<span class="w"> </span>--version
</span><span id="__span-85-2"><a id="__codelineno-85-2" name="__codelineno-85-2" href="#__codelineno-85-2"></a>bluetoothd<span class="w"> </span>--version
</span></code></pre></div>
<p>Also check <code>hciuart</code> status if needed.</p>
<h3 id="version-comparisons-and-behaviors">Version Comparisons and Behaviors<a class="headerlink" href="#version-comparisons-and-behaviors" title="Permalink">&para;</a></h3>
<h4 id="sp40-image-old">SP.40 Image (Old)<a class="headerlink" href="#sp40-image-old" title="Permalink">&para;</a></h4>
<p><div class="language-bash highlight"><pre><span></span><code><span id="__span-86-1"><a id="__codelineno-86-1" name="__codelineno-86-1" href="#__codelineno-86-1"></a>bluetoothctl<span class="w"> </span>--version
</span><span id="__span-86-2"><a id="__codelineno-86-2" name="__codelineno-86-2" href="#__codelineno-86-2"></a>bluetoothd<span class="w"> </span>--version
</span></code></pre></div>
Output:
<div class="language-text highlight"><pre><span></span><code><span id="__span-87-1"><a id="__codelineno-87-1" name="__codelineno-87-1" href="#__codelineno-87-1"></a>bluetoothctl: 5.50
</span><span id="__span-87-2"><a id="__codelineno-87-2" name="__codelineno-87-2" href="#__codelineno-87-2"></a>5.50
</span></code></pre></div></p>
<p><div class="language-bash highlight"><pre><span></span><code><span id="__span-88-1"><a id="__codelineno-88-1" name="__codelineno-88-1" href="#__codelineno-88-1"></a>dmesg<span class="w"> </span><span class="p">|</span><span class="w"> </span>tee<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>Bluetooth
</span></code></pre></div>
Output:
<div class="language-text highlight"><pre><span></span><code><span id="__span-89-1"><a id="__codelineno-89-1" name="__codelineno-89-1" href="#__codelineno-89-1"></a>(empty) # But maybe only empty of bluetooth, because it was pushed out the beginning of the log...
</span></code></pre></div></p>
<p><div class="language-bash highlight"><pre><span></span><code><span id="__span-90-1"><a id="__codelineno-90-1" name="__codelineno-90-1" href="#__codelineno-90-1"></a>uname<span class="w"> </span>-a
</span></code></pre></div>
Output:
<div class="language-text highlight"><pre><span></span><code><span id="__span-91-1"><a id="__codelineno-91-1" name="__codelineno-91-1" href="#__codelineno-91-1"></a>Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux
</span></code></pre></div></p>
<p><div class="language-bash highlight"><pre><span></span><code><span id="__span-92-1"><a id="__codelineno-92-1" name="__codelineno-92-1" href="#__codelineno-92-1"></a>dpkg<span class="w"> </span>--status<span class="w"> </span>bluez<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>Version
</span></code></pre></div>
Output:
<div class="language-text highlight"><pre><span></span><code><span id="__span-93-1"><a id="__codelineno-93-1" name="__codelineno-93-1" href="#__codelineno-93-1"></a>Version: 5.50-1.2~deb10u3+rpt1
</span></code></pre></div></p>
<p>Result: RS6000 worked as expected</p>
<h4 id="after-update">After Update<a class="headerlink" href="#after-update" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-94-1"><a id="__codelineno-94-1" name="__codelineno-94-1" href="#__codelineno-94-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>update
</span><span id="__span-94-2"><a id="__codelineno-94-2" name="__codelineno-94-2" href="#__codelineno-94-2"></a>reboot
</span></code></pre></div>
<p>Version check after update:
<div class="language-text highlight"><pre><span></span><code><span id="__span-95-1"><a id="__codelineno-95-1" name="__codelineno-95-1" href="#__codelineno-95-1"></a>bluetoothctl: 5.50
</span><span id="__span-95-2"><a id="__codelineno-95-2" name="__codelineno-95-2" href="#__codelineno-95-2"></a>5.50
</span></code></pre></div></p>
<p>Result: RS6000 worked as expected</p>
<h4 id="after-upgrade">After Upgrade<a class="headerlink" href="#after-upgrade" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-96-1"><a id="__codelineno-96-1" name="__codelineno-96-1" href="#__codelineno-96-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>upgrade
</span><span id="__span-96-2"><a id="__codelineno-96-2" name="__codelineno-96-2" href="#__codelineno-96-2"></a>reboot
</span></code></pre></div>
<p>Version check after upgrade:
<div class="language-text highlight"><pre><span></span><code><span id="__span-97-1"><a id="__codelineno-97-1" name="__codelineno-97-1" href="#__codelineno-97-1"></a>bluetoothctl: 5.50
</span><span id="__span-97-2"><a id="__codelineno-97-2" name="__codelineno-97-2" href="#__codelineno-97-2"></a>5.50
</span></code></pre></div></p>
<p>System information:
<div class="language-text highlight"><pre><span></span><code><span id="__span-98-1"><a id="__codelineno-98-1" name="__codelineno-98-1" href="#__codelineno-98-1"></a>Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux
</span></code></pre></div></p>
<p>Bluetooth package version:
<div class="language-text highlight"><pre><span></span><code><span id="__span-99-1"><a id="__codelineno-99-1" name="__codelineno-99-1" href="#__codelineno-99-1"></a>Version: 5.50-1.2~deb10u4
</span></code></pre></div></p>
<p>Result: RS6000 stopped working</p>
<p>Update to latest pi-bluetooth to handle tmux changes: Still failed.</p>
<h4 id="kernel-messages-after-upgrade">Kernel Messages After Upgrade<a class="headerlink" href="#kernel-messages-after-upgrade" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-100-1"><a id="__codelineno-100-1" name="__codelineno-100-1" href="#__codelineno-100-1"></a>dmesg<span class="w"> </span><span class="p">|</span><span class="w"> </span>tee<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>Bluetooth
</span></code></pre></div>
<div class="language-text highlight"><pre><span></span><code><span id="__span-101-1"><a id="__codelineno-101-1" name="__codelineno-101-1" href="#__codelineno-101-1"></a>[    8.455095] Bluetooth: Core ver 2.22
</span><span id="__span-101-2"><a id="__codelineno-101-2" name="__codelineno-101-2" href="#__codelineno-101-2"></a>[    8.455217] Bluetooth: HCI device and connection manager initialized
</span><span id="__span-101-3"><a id="__codelineno-101-3" name="__codelineno-101-3" href="#__codelineno-101-3"></a>[    8.455342] Bluetooth: HCI socket layer initialized
</span><span id="__span-101-4"><a id="__codelineno-101-4" name="__codelineno-101-4" href="#__codelineno-101-4"></a>[    8.455403] Bluetooth: L2CAP socket layer initialized
</span><span id="__span-101-5"><a id="__codelineno-101-5" name="__codelineno-101-5" href="#__codelineno-101-5"></a>[    8.455454] Bluetooth: SCO socket layer initialized
</span><span id="__span-101-6"><a id="__codelineno-101-6" name="__codelineno-101-6" href="#__codelineno-101-6"></a>[    8.491685] Bluetooth: HCI UART driver ver 2.3
</span><span id="__span-101-7"><a id="__codelineno-101-7" name="__codelineno-101-7" href="#__codelineno-101-7"></a>[    8.491710] Bluetooth: HCI UART protocol H4 registered
</span><span id="__span-101-8"><a id="__codelineno-101-8" name="__codelineno-101-8" href="#__codelineno-101-8"></a>[    8.491849] Bluetooth: HCI UART protocol Three-wire (H5) registered
</span><span id="__span-101-9"><a id="__codelineno-101-9" name="__codelineno-101-9" href="#__codelineno-101-9"></a>[    8.506279] Bluetooth: HCI UART protocol Broadcom registered
</span><span id="__span-101-10"><a id="__codelineno-101-10" name="__codelineno-101-10" href="#__codelineno-101-10"></a>[    9.130041] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
</span><span id="__span-101-11"><a id="__codelineno-101-11" name="__codelineno-101-11" href="#__codelineno-101-11"></a>[    9.130061] Bluetooth: BNEP filters: protocol multicast
</span><span id="__span-101-12"><a id="__codelineno-101-12" name="__codelineno-101-12" href="#__codelineno-101-12"></a>[    9.130106] Bluetooth: BNEP socket layer initialized
</span><span id="__span-101-13"><a id="__codelineno-101-13" name="__codelineno-101-13" href="#__codelineno-101-13"></a>[ 1385.452174] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
</span><span id="__span-101-14"><a id="__codelineno-101-14" name="__codelineno-101-14" href="#__codelineno-101-14"></a>[ 1385.452213] Bluetooth: HIDP socket layer initialized
</span></code></pre></div>
<h4 id="sp47-image-middle">SP.47 Image (Middle)<a class="headerlink" href="#sp47-image-middle" title="Permalink">&para;</a></h4>
<p>Version information:
<div class="language-text highlight"><pre><span></span><code><span id="__span-102-1"><a id="__codelineno-102-1" name="__codelineno-102-1" href="#__codelineno-102-1"></a>bluetoothctl: 5.50
</span><span id="__span-102-2"><a id="__codelineno-102-2" name="__codelineno-102-2" href="#__codelineno-102-2"></a>5.50
</span></code></pre></div></p>
<p>Kernel messages:
<div class="language-text highlight"><pre><span></span><code><span id="__span-103-1"><a id="__codelineno-103-1" name="__codelineno-103-1" href="#__codelineno-103-1"></a>[    7.884677] Bluetooth: Core ver 2.22
</span><span id="__span-103-2"><a id="__codelineno-103-2" name="__codelineno-103-2" href="#__codelineno-103-2"></a>[    7.884794] Bluetooth: HCI device and connection manager initialized
</span><span id="__span-103-3"><a id="__codelineno-103-3" name="__codelineno-103-3" href="#__codelineno-103-3"></a>[    7.884821] Bluetooth: HCI socket layer initialized
</span><span id="__span-103-4"><a id="__codelineno-103-4" name="__codelineno-103-4" href="#__codelineno-103-4"></a>[    7.884840] Bluetooth: L2CAP socket layer initialized
</span><span id="__span-103-5"><a id="__codelineno-103-5" name="__codelineno-103-5" href="#__codelineno-103-5"></a>[    7.884874] Bluetooth: SCO socket layer initialized
</span><span id="__span-103-6"><a id="__codelineno-103-6" name="__codelineno-103-6" href="#__codelineno-103-6"></a>[    7.934892] Bluetooth: HCI UART driver ver 2.3
</span><span id="__span-103-7"><a id="__codelineno-103-7" name="__codelineno-103-7" href="#__codelineno-103-7"></a>[    7.934918] Bluetooth: HCI UART protocol H4 registered
</span><span id="__span-103-8"><a id="__codelineno-103-8" name="__codelineno-103-8" href="#__codelineno-103-8"></a>[    7.935024] Bluetooth: HCI UART protocol Three-wire (H5) registered
</span><span id="__span-103-9"><a id="__codelineno-103-9" name="__codelineno-103-9" href="#__codelineno-103-9"></a>[    7.935448] Bluetooth: HCI UART protocol Broadcom registered
</span><span id="__span-103-10"><a id="__codelineno-103-10" name="__codelineno-103-10" href="#__codelineno-103-10"></a>[    8.494406] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
</span><span id="__span-103-11"><a id="__codelineno-103-11" name="__codelineno-103-11" href="#__codelineno-103-11"></a>[    8.494432] Bluetooth: BNEP filters: protocol multicast
</span><span id="__span-103-12"><a id="__codelineno-103-12" name="__codelineno-103-12" href="#__codelineno-103-12"></a>[    8.494466] Bluetooth: BNEP socket layer initialized
</span></code></pre></div></p>
<h4 id="pi5-image-current">Pi5 Image (Current)<a class="headerlink" href="#pi5-image-current" title="Permalink">&para;</a></h4>
<p>Version information:
<div class="language-text highlight"><pre><span></span><code><span id="__span-104-1"><a id="__codelineno-104-1" name="__codelineno-104-1" href="#__codelineno-104-1"></a>bluetoothctl: 5.66
</span><span id="__span-104-2"><a id="__codelineno-104-2" name="__codelineno-104-2" href="#__codelineno-104-2"></a>bluetoothd: 5.66
</span></code></pre></div></p>
<p>System information:
<div class="language-text highlight"><pre><span></span><code><span id="__span-105-1"><a id="__codelineno-105-1" name="__codelineno-105-1" href="#__codelineno-105-1"></a>Linux cah-rp-ec885c8aa5f46f0d 6.6.20+rpt-rpi-v8 #1 SMP PREEMPT Debian 1:6.6.20-1+rpt1 (2024-03-07) aarch64 GNU/Linux
</span></code></pre></div></p>
<p>Bluetooth package version:
<div class="language-text highlight"><pre><span></span><code><span id="__span-106-1"><a id="__codelineno-106-1" name="__codelineno-106-1" href="#__codelineno-106-1"></a>Version: 5.66-1+rpt1+deb12u1
</span></code></pre></div></p>
<p>Kernel messages:
<div class="language-text highlight"><pre><span></span><code><span id="__span-107-1"><a id="__codelineno-107-1" name="__codelineno-107-1" href="#__codelineno-107-1"></a>[    5.664370] Bluetooth: Core ver 2.22
</span><span id="__span-107-2"><a id="__codelineno-107-2" name="__codelineno-107-2" href="#__codelineno-107-2"></a>[    5.664402] NET: Registered PF_BLUETOOTH protocol family
</span><span id="__span-107-3"><a id="__codelineno-107-3" name="__codelineno-107-3" href="#__codelineno-107-3"></a>[    5.664404] Bluetooth: HCI device and connection manager initialized
</span><span id="__span-107-4"><a id="__codelineno-107-4" name="__codelineno-107-4" href="#__codelineno-107-4"></a>[    5.664411] Bluetooth: HCI socket layer initialized
</span><span id="__span-107-5"><a id="__codelineno-107-5" name="__codelineno-107-5" href="#__codelineno-107-5"></a>[    5.664415] Bluetooth: L2CAP socket layer initialized
</span><span id="__span-107-6"><a id="__codelineno-107-6" name="__codelineno-107-6" href="#__codelineno-107-6"></a>[    5.664420] Bluetooth: SCO socket layer initialized
</span><span id="__span-107-7"><a id="__codelineno-107-7" name="__codelineno-107-7" href="#__codelineno-107-7"></a>[    5.674567] Bluetooth: HCI UART driver ver 2.3
</span><span id="__span-107-8"><a id="__codelineno-107-8" name="__codelineno-107-8" href="#__codelineno-107-8"></a>[    5.674575] Bluetooth: HCI UART protocol H4 registered
</span><span id="__span-107-9"><a id="__codelineno-107-9" name="__codelineno-107-9" href="#__codelineno-107-9"></a>[    5.674609] Bluetooth: HCI UART protocol Three-wire (H5) registered
</span><span id="__span-107-10"><a id="__codelineno-107-10" name="__codelineno-107-10" href="#__codelineno-107-10"></a>[    5.674840] Bluetooth: HCI UART protocol Broadcom registered
</span><span id="__span-107-11"><a id="__codelineno-107-11" name="__codelineno-107-11" href="#__codelineno-107-11"></a>[    6.042366] Bluetooth: hci0: BCM: chip id 107
</span><span id="__span-107-12"><a id="__codelineno-107-12" name="__codelineno-107-12" href="#__codelineno-107-12"></a>[    6.043130] Bluetooth: hci0: BCM: features 0x2f
</span><span id="__span-107-13"><a id="__codelineno-107-13" name="__codelineno-107-13" href="#__codelineno-107-13"></a>[    6.044254] Bluetooth: hci0: BCM4345C0
</span><span id="__span-107-14"><a id="__codelineno-107-14" name="__codelineno-107-14" href="#__codelineno-107-14"></a>[    6.044261] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0000
</span><span id="__span-107-15"><a id="__codelineno-107-15" name="__codelineno-107-15" href="#__codelineno-107-15"></a>[    6.047494] Bluetooth: hci0: BCM4345C0 &#39;brcm/BCM4345C0.raspberrypi,5-model-b.hcd&#39; Patch
</span><span id="__span-107-16"><a id="__codelineno-107-16" name="__codelineno-107-16" href="#__codelineno-107-16"></a>[    6.759113] Bluetooth: hci0: BCM: features 0x2f
</span><span id="__span-107-17"><a id="__codelineno-107-17" name="__codelineno-107-17" href="#__codelineno-107-17"></a>[    6.760571] Bluetooth: hci0: BCM43455 37.4MHz Raspberry Pi 3+-0190
</span><span id="__span-107-18"><a id="__codelineno-107-18" name="__codelineno-107-18" href="#__codelineno-107-18"></a>[    6.760583] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0382
</span><span id="__span-107-19"><a id="__codelineno-107-19" name="__codelineno-107-19" href="#__codelineno-107-19"></a>[    6.761807] Bluetooth: hci0: BCM: Using default device address (43:45:c0:00:1f:ac)
</span><span id="__span-107-20"><a id="__codelineno-107-20" name="__codelineno-107-20" href="#__codelineno-107-20"></a>[   13.840787] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
</span><span id="__span-107-21"><a id="__codelineno-107-21" name="__codelineno-107-21" href="#__codelineno-107-21"></a>[   13.840794] Bluetooth: BNEP filters: protocol multicast
</span><span id="__span-107-22"><a id="__codelineno-107-22" name="__codelineno-107-22" href="#__codelineno-107-22"></a>[   13.840800] Bluetooth: BNEP socket layer initialized
</span><span id="__span-107-23"><a id="__codelineno-107-23" name="__codelineno-107-23" href="#__codelineno-107-23"></a>[   13.842897] Bluetooth: MGMT ver 1.22
</span></code></pre></div></p>
<hr />
<h2 id="set-image-version">Set Image Version<a class="headerlink" href="#set-image-version" title="Permalink">&para;</a></h2>
<p>Must be ready before runner install</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-108-1"><a id="__codelineno-108-1" name="__codelineno-108-1" href="#__codelineno-108-1"></a>sudo<span class="w"> </span>su
</span><span id="__span-108-2"><a id="__codelineno-108-2" name="__codelineno-108-2" href="#__codelineno-108-2"></a>
</span><span id="__span-108-3"><a id="__codelineno-108-3" name="__codelineno-108-3" href="#__codelineno-108-3"></a><span class="c1"># This sets the base image value that gets reported</span>
</span><span id="__span-108-4"><a id="__codelineno-108-4" name="__codelineno-108-4" href="#__codelineno-108-4"></a><span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Image version: 2.4.1&quot;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/image_ver.txt
</span><span id="__span-108-5"><a id="__codelineno-108-5" name="__codelineno-108-5" href="#__codelineno-108-5"></a><span class="c1"># 2024.03.13 Bookworm Build for Pi5</span>
</span><span id="__span-108-6"><a id="__codelineno-108-6" name="__codelineno-108-6" href="#__codelineno-108-6"></a><span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Image version: 2.4.2&quot;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/image_ver.txt
</span><span id="__span-108-7"><a id="__codelineno-108-7" name="__codelineno-108-7" href="#__codelineno-108-7"></a>
</span><span id="__span-108-8"><a id="__codelineno-108-8" name="__codelineno-108-8" href="#__codelineno-108-8"></a><span class="nb">exit</span>
</span></code></pre></div>
<hr />
<h2 id="fix-numpy-issues">Fix NumPy Issues<a class="headerlink" href="#fix-numpy-issues" title="Permalink">&para;</a></h2>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-109-1"><a id="__codelineno-109-1" name="__codelineno-109-1" href="#__codelineno-109-1"></a>sudo<span class="w"> </span>pip3<span class="w"> </span>uninstall<span class="w"> </span>numpy<span class="w"> </span>-y
</span><span id="__span-109-2"><a id="__codelineno-109-2" name="__codelineno-109-2" href="#__codelineno-109-2"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>python3-numpy
</span></code></pre></div>
<hr />
<h2 id="load-pi-runner">Load Pi Runner<a class="headerlink" href="#load-pi-runner" title="Permalink">&para;</a></h2>
<h3 id="get-raspberry-pi-serial-number">Get Raspberry Pi Serial Number<a class="headerlink" href="#get-raspberry-pi-serial-number" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-110-1"><a id="__codelineno-110-1" name="__codelineno-110-1" href="#__codelineno-110-1"></a><span class="c1"># To collect serial number like: 10000000e3669edf</span>
</span><span id="__span-110-2"><a id="__codelineno-110-2" name="__codelineno-110-2" href="#__codelineno-110-2"></a>cat<span class="w"> </span>/proc/cpuinfo<span class="w"> </span><span class="p">|</span><span class="w"> </span>fgrep<span class="w"> </span>Serial
</span></code></pre></div>
<h3 id="manual-installation">Manual Installation<a class="headerlink" href="#manual-installation" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><span class="filename">pi_runner</span><pre><span></span><code><span id="__span-111-1"><a id="__codelineno-111-1" name="__codelineno-111-1" href="#__codelineno-111-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/cardinal/pi_runner.py
</span><span id="__span-111-2"><a id="__codelineno-111-2" name="__codelineno-111-2" href="#__codelineno-111-2"></a><span class="c1"># Copy and paste the content of the source file</span>
</span><span id="__span-111-3"><a id="__codelineno-111-3" name="__codelineno-111-3" href="#__codelineno-111-3"></a>
</span><span id="__span-111-4"><a id="__codelineno-111-4" name="__codelineno-111-4" href="#__codelineno-111-4"></a><span class="nb">cd</span><span class="w"> </span>/cardinal
</span><span id="__span-111-5"><a id="__codelineno-111-5" name="__codelineno-111-5" href="#__codelineno-111-5"></a>sudo<span class="w"> </span>python3<span class="w"> </span>pi_runner.py<span class="w"> </span>install
</span></code></pre></div>
<div class="admonition warning">
<p class="admonition-title">Important Note</p>
<p>Once running and checking into Slicer, if there is an update to settings ready for it in Slicer, then a reboot may occur automatically. This is normal behavior.</p>
</div>
<h3 id="verify-registration">Verify Registration<a class="headerlink" href="#verify-registration" title="Permalink">&para;</a></h3>
<p>Check that the device appears in Slicer:
<div class="language-text highlight"><pre><span></span><code><span id="__span-112-1"><a id="__codelineno-112-1" name="__codelineno-112-1" href="#__codelineno-112-1"></a>https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf
</span></code></pre></div></p>
<hr />
<h2 id="install-components-via-slicer">Install Components via Slicer<a class="headerlink" href="#install-components-via-slicer" title="Permalink">&para;</a></h2>
<p>The following components should be loaded through the Slicer interface (Runner must be loaded and running first):</p>
<ul>
<li>pi_bluetooth</li>
<li>pi_config</li>
<li>pi_hmi</li>
<li>pi_logging</li>
<li>pi_monitor</li>
<li>pi_network</li>
<li>pi_organization</li>
<li>pi_security</li>
<li>pi_settings</li>
</ul>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-113-1"><a id="__codelineno-113-1" name="__codelineno-113-1" href="#__codelineno-113-1"></a>sudo<span class="w"> </span>reboot
</span></code></pre></div>
<div class="admonition danger">
<p class="admonition-title">Testing Required</p>
<p><strong>Stop here</strong> and manually test by logging in as worker, then running <code>startx</code></p>
</div>
<hr />
<h2 id="configure-autologin-for-worker">Configure Autologin for Worker<a class="headerlink" href="#configure-autologin-for-worker" title="Permalink">&para;</a></h2>
<div class="admonition note">
<p class="admonition-title">Command Line Only Login</p>
<p>This configuration gets to the command line prompt only. Do not expect auto startx or anything further.</p>
</div>
<h3 id="for-pi5-and-latest-os-after-20231030">For Pi5 and Latest OS (After 2023.10.30)<a class="headerlink" href="#for-pi5-and-latest-os-after-20231030" title="Permalink">&para;</a></h3>
<p><a href="https://www.nixcraft.com/t/how-to-configure-autologin-on-the-raspbian-buster-console/3922/2">Reference</a></p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-114-1"><a id="__codelineno-114-1" name="__codelineno-114-1" href="#__codelineno-114-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/systemd/logind.conf
</span><span id="__span-114-2"><a id="__codelineno-114-2" name="__codelineno-114-2" href="#__codelineno-114-2"></a><span class="c1"># Replace &quot;#NAutoVTs=6&quot; with &quot;NAutoVTs=1&quot;</span>
</span></code></pre></div>
<h3 id="edit-getty-service">Edit Getty Service<a class="headerlink" href="#edit-getty-service" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-115-1"><a id="__codelineno-115-1" name="__codelineno-115-1" href="#__codelineno-115-1"></a>sudo<span class="w"> </span>systemctl<span class="w"> </span>edit<span class="w"> </span><EMAIL>
</span><span id="__span-115-2"><a id="__codelineno-115-2" name="__codelineno-115-2" href="#__codelineno-115-2"></a>
</span><span id="__span-115-3"><a id="__codelineno-115-3" name="__codelineno-115-3" href="#__codelineno-115-3"></a><span class="c1"># sudo vi /etc/systemd/system/<EMAIL>.d</span>
</span></code></pre></div>
<p>Add the following above "### Lines below this comment will be discarded":</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-116-1"><a id="__codelineno-116-1" name="__codelineno-116-1" href="#__codelineno-116-1"></a><span class="o">[</span>Service<span class="o">]</span>
</span><span id="__span-116-2"><a id="__codelineno-116-2" name="__codelineno-116-2" href="#__codelineno-116-2"></a><span class="nv">ExecStart</span><span class="o">=</span>
</span><span id="__span-116-3"><a id="__codelineno-116-3" name="__codelineno-116-3" href="#__codelineno-116-3"></a><span class="nv">ExecStart</span><span class="o">=</span>-/usr/sbin/agetty<span class="w"> </span>--autologin<span class="w"> </span>worker<span class="w"> </span>--noclear<span class="w"> </span>%I<span class="w"> </span><span class="nv">$TERM</span>
</span></code></pre></div>
<p>Save with <code>Ctrl+X</code>, <code>Y</code>, <code>Enter</code></p>
<details class="info" open="open">
<summary>Old Method (For Reference)</summary>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-117-1"><a id="__codelineno-117-1" name="__codelineno-117-1" href="#__codelineno-117-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/systemd/system/<EMAIL>.d/autologin.conf
</span><span id="__span-117-2"><a id="__codelineno-117-2" name="__codelineno-117-2" href="#__codelineno-117-2"></a><span class="c1"># Add:</span>
</span><span id="__span-117-3"><a id="__codelineno-117-3" name="__codelineno-117-3" href="#__codelineno-117-3"></a><span class="o">[</span>Service<span class="o">]</span>
</span><span id="__span-117-4"><a id="__codelineno-117-4" name="__codelineno-117-4" href="#__codelineno-117-4"></a><span class="nv">ExecStart</span><span class="o">=</span>
</span><span id="__span-117-5"><a id="__codelineno-117-5" name="__codelineno-117-5" href="#__codelineno-117-5"></a><span class="nv">ExecStart</span><span class="o">=</span>-/sbin/agetty<span class="w"> </span>--autologin<span class="w"> </span>worker<span class="w"> </span>--noclear<span class="w"> </span>%I<span class="w"> </span><span class="se">\$</span>TERM
</span><span id="__span-117-6"><a id="__codelineno-117-6" name="__codelineno-117-6" href="#__codelineno-117-6"></a>
</span><span id="__span-117-7"><a id="__codelineno-117-7" name="__codelineno-117-7" href="#__codelineno-117-7"></a>sudo<span class="w"> </span>ln<span class="w"> </span>-fs<span class="w"> </span>/lib/systemd/system/getty@.service<span class="w"> </span>/etc/systemd/system/getty.target.wants/<EMAIL>
</span></code></pre></div>
</details>
<h3 id="test-autologin">Test Autologin<a class="headerlink" href="#test-autologin" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-118-1"><a id="__codelineno-118-1" name="__codelineno-118-1" href="#__codelineno-118-1"></a>sudo<span class="w"> </span>reboot
</span></code></pre></div>
<hr />
<h2 id="configure-automatic-startx-on-login">Configure Automatic Startx on Login<a class="headerlink" href="#configure-automatic-startx-on-login" title="Permalink">&para;</a></h2>
<h3 id="add-to-workers-bash-profile">Add to Worker's Bash Profile<a class="headerlink" href="#add-to-workers-bash-profile" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-119-1"><a id="__codelineno-119-1" name="__codelineno-119-1" href="#__codelineno-119-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/home/<USER>/.bash_profile
</span></code></pre></div>
<p>Add:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-120-1"><a id="__codelineno-120-1" name="__codelineno-120-1" href="#__codelineno-120-1"></a><span class="k">if</span><span class="w"> </span><span class="o">[</span><span class="w"> </span>-z<span class="w"> </span><span class="nv">$DISPLAY</span><span class="w"> </span><span class="o">]</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="o">[</span><span class="w"> </span><span class="k">$(</span>tty<span class="k">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span>/dev/tty1<span class="w"> </span><span class="o">]</span>
</span><span id="__span-120-2"><a id="__codelineno-120-2" name="__codelineno-120-2" href="#__codelineno-120-2"></a><span class="k">then</span>
</span><span id="__span-120-3"><a id="__codelineno-120-3" name="__codelineno-120-3" href="#__codelineno-120-3"></a><span class="w">  </span>startx
</span><span id="__span-120-4"><a id="__codelineno-120-4" name="__codelineno-120-4" href="#__codelineno-120-4"></a><span class="k">fi</span>
</span></code></pre></div>
<p>Set ownership:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-121-1"><a id="__codelineno-121-1" name="__codelineno-121-1" href="#__codelineno-121-1"></a>sudo<span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>worker:worker<span class="w"> </span>/home/<USER>/.bash_profile
</span></code></pre></div>
<hr />
<h2 id="verify-hmi-installation">Verify HMI Installation<a class="headerlink" href="#verify-hmi-installation" title="Permalink">&para;</a></h2>
<p>The HMI install script should no longer be present when installation is complete:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-122-1"><a id="__codelineno-122-1" name="__codelineno-122-1" href="#__codelineno-122-1"></a><span class="c1"># Check that tempscript is gone (installation may take up to 10 minutes)</span>
</span><span id="__span-122-2"><a id="__codelineno-122-2" name="__codelineno-122-2" href="#__codelineno-122-2"></a>ls<span class="w"> </span>-l<span class="w"> </span>/cardinal/pi_hmi_tempscript
</span></code></pre></div>
<hr />
<h2 id="create-test-image-backup">Create Test Image Backup<a class="headerlink" href="#create-test-image-backup" title="Permalink">&para;</a></h2>
<p>Make a backup at this stage:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-123-1"><a id="__codelineno-123-1" name="__codelineno-123-1" href="#__codelineno-123-1"></a><span class="c1"># Recommended backup naming conventions:</span>
</span><span id="__span-123-2"><a id="__codelineno-123-2" name="__codelineno-123-2" href="#__codelineno-123-2"></a><span class="c1"># 2024.04.17 -&gt; backup2</span>
</span><span id="__span-123-3"><a id="__codelineno-123-3" name="__codelineno-123-3" href="#__codelineno-123-3"></a><span class="c1"># 2024.04.23 -&gt; starting from backup1b, worked to here, then save as -&gt; 20240417/backup2b</span>
</span></code></pre></div>
<hr />
<h2 id="test-gui-performance">Test GUI Performance<a class="headerlink" href="#test-gui-performance" title="Permalink">&para;</a></h2>
<p>Reboot and test the GUI:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-124-1"><a id="__codelineno-124-1" name="__codelineno-124-1" href="#__codelineno-124-1"></a>sudo<span class="w"> </span>reboot
</span></code></pre></div>
<hr />
<h2 id="browser-security-testing">Browser Security Testing<a class="headerlink" href="#browser-security-testing" title="Permalink">&para;</a></h2>
<p><a href="https://www.howtogeek.com/408289/what-your-function-keys-do-in-google-chrome/">Reference: Function Keys in Google Chrome</a></p>
<h3 id="blocked-shortcuts">Blocked Shortcuts<a class="headerlink" href="#blocked-shortcuts" title="Permalink">&para;</a></h3>
<p>The following keyboard combinations should be blocked:</p>
<ul>
<li><code>Ctrl+O</code> (open file)</li>
<li><code>Ctrl+Alt+F2</code> (opens terminal)</li>
<li><code>Ctrl+W</code> (close window)</li>
<li><code>Alt+Esc</code> (incognito)</li>
</ul>
<p>Dont want to allow F1, but cannot block F1</p>
<ul>
<li><code>F1</code> (opens help, should show a fail page due to Privoxy blacklist)</li>
</ul>
<h3 id="allowed-shortcuts">Allowed Shortcuts<a class="headerlink" href="#allowed-shortcuts" title="Permalink">&para;</a></h3>
<p>Should be covered by do while loop:</p>
<ul>
<li><code>Alt+F4</code> (exit) - Use this to escape from F1 result screens</li>
</ul>
<hr />
<h2 id="test-bookmark-integration">Test Bookmark Integration<a class="headerlink" href="#test-bookmark-integration" title="Permalink">&para;</a></h2>
<p>Load bookmarks via the Slicer interface and test all links on the Pi.</p>
<hr />
<h2 id="multi-device-testing">Multi-Device Testing<a class="headerlink" href="#multi-device-testing" title="Permalink">&para;</a></h2>
<h3 id="chromium-profile-test">Chromium Profile Test<a class="headerlink" href="#chromium-profile-test" title="Permalink">&para;</a></h3>
<p>Load the card into a second pi, and see that there is not a warning about a duplicate chromium profile</p>
<p>Test that the image works correctly across multiple devices:</p>
<ol>
<li>
<p>Shut down the current Pi:
   <div class="language-sh highlight"><pre><span></span><code><span id="__span-125-1"><a id="__codelineno-125-1" name="__codelineno-125-1" href="#__codelineno-125-1"></a>sudo<span class="w"> </span>shutdown<span class="w"> </span>-h<span class="w"> </span>now
</span></code></pre></div></p>
</li>
<li>
<p>Move the SD card to a second Raspberry Pi</p>
</li>
<li>
<p>Verify it boots without warnings about duplicate Chromium profiles</p>
</li>
</ol>
<hr />
<h2 id="bluetooth-scanner-testing">Bluetooth Scanner Testing<a class="headerlink" href="#bluetooth-scanner-testing" title="Permalink">&para;</a></h2>
<ol>
<li>Configure two devices with Bluetooth enabled via the Slicer interface</li>
<li>Start with all devices powered off, then power on the Raspberry Pi devices first</li>
<li>Verify devices report "(no devices seen)"</li>
<li>
<p>Cold boot the scanner:</p>
<ul>
<li>Press the side button while inserting the battery</li>
<li>Hold until it beeps</li>
<li>Scan the one-time configuration barcode</li>
<li>Confirm both devices show a pairing barcode after 15-30 seconds</li>
</ul>
</li>
<li>
<p>Pairing Tests:</p>
<ul>
<li>Pair to device 1 (may require two scans over 30-40 seconds)</li>
<li>Pair to device 2 (should unpair from device 1 after 30-60 seconds)</li>
<li>Remove scanner battery for at least 10 minutes</li>
<li>Verify both devices revert to "(searching..)"</li>
<li>Power up scanner (should not auto-reconnect)</li>
<li>Manually pair to device 1, then to device 2</li>
<li>With scanner powered, reboot devices and verify no automatic pairing occurs within 10 minutes</li>
</ul>
</li>
</ol>
<hr />
<h2 id="finalize-image-for-distribution">Finalize Image for Distribution<a class="headerlink" href="#finalize-image-for-distribution" title="Permalink">&para;</a></h2>
<h3 id="system-cleanup">System Cleanup<a class="headerlink" href="#system-cleanup" title="Permalink">&para;</a></h3>
<p>Log in as root:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-126-1"><a id="__codelineno-126-1" name="__codelineno-126-1" href="#__codelineno-126-1"></a>sudo<span class="w"> </span>su
</span></code></pre></div>
<p>Clean packages:</p>
<p>All of these as a single copy and paste into the terminal:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-127-1"><a id="__codelineno-127-1" name="__codelineno-127-1" href="#__codelineno-127-1"></a>apt-get<span class="w"> </span>clean
</span><span id="__span-127-2"><a id="__codelineno-127-2" name="__codelineno-127-2" href="#__codelineno-127-2"></a>apt-get<span class="w"> </span>-y<span class="w"> </span>autoremove<span class="w"> </span>--purge
</span></code></pre></div>
<h3 id="remove-temporary-files">Remove Temporary Files<a class="headerlink" href="#remove-temporary-files" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-128-1"><a id="__codelineno-128-1" name="__codelineno-128-1" href="#__codelineno-128-1"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/Downloads
</span><span id="__span-128-2"><a id="__codelineno-128-2" name="__codelineno-128-2" href="#__codelineno-128-2"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/home/<USER>/*
</span><span id="__span-128-3"><a id="__codelineno-128-3" name="__codelineno-128-3" href="#__codelineno-128-3"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/cardinal/save_values
</span></code></pre></div>
<h3 id="stop-services">Stop Services<a class="headerlink" href="#stop-services" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-129-1"><a id="__codelineno-129-1" name="__codelineno-129-1" href="#__codelineno-129-1"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-bluetooth.service
</span><span id="__span-129-2"><a id="__codelineno-129-2" name="__codelineno-129-2" href="#__codelineno-129-2"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-config.service
</span><span id="__span-129-3"><a id="__codelineno-129-3" name="__codelineno-129-3" href="#__codelineno-129-3"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-hmi.service
</span><span id="__span-129-4"><a id="__codelineno-129-4" name="__codelineno-129-4" href="#__codelineno-129-4"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-logging.service
</span><span id="__span-129-5"><a id="__codelineno-129-5" name="__codelineno-129-5" href="#__codelineno-129-5"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-monitor.service
</span><span id="__span-129-6"><a id="__codelineno-129-6" name="__codelineno-129-6" href="#__codelineno-129-6"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-network.service
</span><span id="__span-129-7"><a id="__codelineno-129-7" name="__codelineno-129-7" href="#__codelineno-129-7"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-organization.service
</span><span id="__span-129-8"><a id="__codelineno-129-8" name="__codelineno-129-8" href="#__codelineno-129-8"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-runner.service
</span><span id="__span-129-9"><a id="__codelineno-129-9" name="__codelineno-129-9" href="#__codelineno-129-9"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-security.service
</span><span id="__span-129-10"><a id="__codelineno-129-10" name="__codelineno-129-10" href="#__codelineno-129-10"></a>systemctl<span class="w"> </span>stop<span class="w"> </span>pi-settings.service
</span></code></pre></div>
<h3 id="clean-log-files">Clean Log Files<a class="headerlink" href="#clean-log-files" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-130-1"><a id="__codelineno-130-1" name="__codelineno-130-1" href="#__codelineno-130-1"></a>find<span class="w"> </span>/var/log<span class="w"> </span>-type<span class="w"> </span>f<span class="w"> </span>-regex<span class="w"> </span><span class="s2">&quot;.*\.gz</span>$<span class="s2">&quot;</span><span class="w"> </span>-delete
</span><span id="__span-130-2"><a id="__codelineno-130-2" name="__codelineno-130-2" href="#__codelineno-130-2"></a>find<span class="w"> </span>/var/log<span class="w"> </span>-type<span class="w"> </span>f<span class="w"> </span>-regex<span class="w"> </span><span class="s2">&quot;.*\.[0-9]</span>$<span class="s2">&quot;</span><span class="w"> </span>-delete
</span><span id="__span-130-3"><a id="__codelineno-130-3" name="__codelineno-130-3" href="#__codelineno-130-3"></a>rm<span class="w"> </span>/var/log/*
</span></code></pre></div>
<h3 id="reset-configuration">Reset Configuration<a class="headerlink" href="#reset-configuration" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-131-1"><a id="__codelineno-131-1" name="__codelineno-131-1" href="#__codelineno-131-1"></a><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;yes&#39;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/needsexpand
</span><span id="__span-131-2"><a id="__codelineno-131-2" name="__codelineno-131-2" href="#__codelineno-131-2"></a><span class="nb">echo</span><span class="w"> </span>-n<span class="w"> </span><span class="s2">&quot;0&quot;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/boot_count.txt
</span><span id="__span-131-3"><a id="__codelineno-131-3" name="__codelineno-131-3" href="#__codelineno-131-3"></a><span class="nb">echo</span><span class="w"> </span>-n<span class="w"> </span><span class="s2">&quot;0&quot;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/grab_count.txt
</span><span id="__span-131-4"><a id="__codelineno-131-4" name="__codelineno-131-4" href="#__codelineno-131-4"></a>cp<span class="w"> </span>/cardinal/browserstart_default<span class="w"> </span>/cardinal/browserstart
</span><span id="__span-131-5"><a id="__codelineno-131-5" name="__codelineno-131-5" href="#__codelineno-131-5"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/cardinal/localhtml/*
</span><span id="__span-131-6"><a id="__codelineno-131-6" name="__codelineno-131-6" href="#__codelineno-131-6"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/cardinal/log/*
</span><span id="__span-131-7"><a id="__codelineno-131-7" name="__codelineno-131-7" href="#__codelineno-131-7"></a>rm<span class="w"> </span>-rf<span class="w"> </span>/cardinal/config_*
</span><span id="__span-131-8"><a id="__codelineno-131-8" name="__codelineno-131-8" href="#__codelineno-131-8"></a>rm<span class="w"> </span>/cardinal/call_home_locations.txt
</span><span id="__span-131-9"><a id="__codelineno-131-9" name="__codelineno-131-9" href="#__codelineno-131-9"></a>rm<span class="w"> </span>/cardinal/wifi_ssid_psk.txt
</span><span id="__span-131-10"><a id="__codelineno-131-10" name="__codelineno-131-10" href="#__codelineno-131-10"></a>rm<span class="w"> </span>/cardinal/screen_resolution.txt
</span><span id="__span-131-11"><a id="__codelineno-131-11" name="__codelineno-131-11" href="#__codelineno-131-11"></a>rm<span class="w"> </span>/cardinal/screen_zoom.txt
</span><span id="__span-131-12"><a id="__codelineno-131-12" name="__codelineno-131-12" href="#__codelineno-131-12"></a>rm<span class="w"> </span>/cardinal/wifi_config.txt
</span><span id="__span-131-13"><a id="__codelineno-131-13" name="__codelineno-131-13" href="#__codelineno-131-13"></a>rm<span class="w"> </span>/cardinal/call_home_locations.txt
</span></code></pre></div>
<h3 id="create-default-startup-page">Create Default Startup Page<a class="headerlink" href="#create-default-startup-page" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-132-1"><a id="__codelineno-132-1" name="__codelineno-132-1" href="#__codelineno-132-1"></a>mkdir<span class="w"> </span>/cardinal/localhtml/
</span><span id="__span-132-2"><a id="__codelineno-132-2" name="__codelineno-132-2" href="#__codelineno-132-2"></a><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;&lt;head&gt;&lt;meta http-equiv=&quot;refresh&quot; content=&quot;5&quot; &gt;&lt;/head&gt;&lt;body&gt;&lt;center&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;table border=&quot;1&quot; cellpadding=&quot;10&quot;&gt;&lt;tr&gt;&lt;td style=&quot;font-size:30px&quot;&gt;&lt;center&gt;Starting up...&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;font-size:30px&quot;&gt;&lt;center&gt;3 boot ups is the normal sequence for a new image.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;font-size:30px&quot;&gt;&lt;center&gt;Screen may not fill to edges until all boots complete.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;font-size:30px&quot;&gt;&lt;center&gt;If this screen does not disappear after 10 seconds,&lt;br&gt;then press Alt F4 to reset the screen.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/center&gt;&lt;/body&gt;&#39;</span><span class="w"> </span>&gt;<span class="w"> </span>/cardinal/localhtml/index.html
</span></code></pre></div>
<h3 id="final-shutdown">Final Shutdown<a class="headerlink" href="#final-shutdown" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-133-1"><a id="__codelineno-133-1" name="__codelineno-133-1" href="#__codelineno-133-1"></a>sudo<span class="w"> </span>shutdown<span class="w"> </span>-h<span class="w"> </span>now
</span></code></pre></div>
<hr />
<h2 id="create-distribution-image">Create Distribution Image<a class="headerlink" href="#create-distribution-image" title="Permalink">&para;</a></h2>
<h3 id="using-applepi-baker">Using ApplePi Baker<a class="headerlink" href="#using-applepi-baker" title="Permalink">&para;</a></h3>
<ol>
<li>
<p>Install ApplePi Baker from <a href="https://www.tweaking4all.com/software/macosx-software/applepi-baker-v2/#DownloadApplePiBaker">Tweaking4All</a>
   <div class="language-text highlight"><pre><span></span><code><span id="__span-134-1"><a id="__codelineno-134-1" name="__codelineno-134-1" href="#__codelineno-134-1"></a>https://www.tweaking4all.com/downloads/ApplePi-Baker-v2.dmg
</span></code></pre></div></p>
</li>
<li>
<p>Run ApplePi-Baker from the Applications folder</p>
</li>
<li>Select the external disk to get the permissions popup</li>
<li>Follow prompts to allow full disk access</li>
<li>Reboot Mac to apply changes correctly</li>
</ol>
<div class="admonition danger">
<p class="admonition-title">MacOS USB Access</p>
<p>On a Mac, JAMF settings may block USB memory card access. If you don't see the SD card reader, contact the Mac admin to enable the Self Service item 'Remove DLP-15.0', then run that program.</p>
</div>
<h3 id="creating-the-image">Creating the Image<a class="headerlink" href="#creating-the-image" title="Permalink">&para;</a></h3>
<p>Each Time:</p>
<ol>
<li>Run ApplePi Baker from Applications</li>
<li>Select the disk</li>
<li>In the lower right corner, enable the shrink option (2nd from left)</li>
<li>Click "Backup" and name it (e.g., "cah_pi_raw_image_2.4.2.SP.XX")<ul>
<li>For a 64GB card, this takes 11-12 minutes</li>
<li>The ZIP should be under 2.5GB; if larger, you missed the shrink setting</li>
</ul>
</li>
</ol>
<div class="admonition info">
<p class="admonition-title">Future:</p>
<p><a href="http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php">Resizing SD Images</a></p>
</div>
<h3 id="test-the-image">Test the Image<a class="headerlink" href="#test-the-image" title="Permalink">&para;</a></h3>
<ol>
<li>Use Balena Etcher to flash a test card from the newly created ZIP file<ul>
<li>If CrowdStrike blocks this, email <EMAIL> for an exception</li>
</ul>
</li>
<li>Insert the card in a Raspberry Pi and verify it boots correctly</li>
</ol>
<hr />
<h2 id="slicer-upload-folder">Slicer Upload Folder<a class="headerlink" href="#slicer-upload-folder" title="Permalink">&para;</a></h2>
<h3 id="upload-to-slicer">Upload to Slicer<a class="headerlink" href="#upload-to-slicer" title="Permalink">&para;</a></h3>
<ol>
<li>Upload the image to Slicer</li>
<li>Mark it as "available for download"</li>
</ol>
<h3 id="notify-users">Notify Users<a class="headerlink" href="#notify-users" title="Permalink">&para;</a></h3>
<p>Inform the "LATAM Raspberry Pi" group that the new version is available:</p>
<div class="language-text highlight"><pre><span></span><code><span id="__span-135-1"><a id="__codelineno-135-1" name="__codelineno-135-1" href="#__codelineno-135-1"></a>Pi image version 2.Y.Z is available.
</span><span id="__span-135-2"><a id="__codelineno-135-2" name="__codelineno-135-2" href="#__codelineno-135-2"></a>
</span><span id="__span-135-3"><a id="__codelineno-135-3" name="__codelineno-135-3" href="#__codelineno-135-3"></a>https://slicer.cardinalhealth.net
</span><span id="__span-135-4"><a id="__codelineno-135-4" name="__codelineno-135-4" href="#__codelineno-135-4"></a>Log in
</span><span id="__span-135-5"><a id="__codelineno-135-5" name="__codelineno-135-5" href="#__codelineno-135-5"></a>Go to the &quot;download&quot; page to view version text file, and to download images.
</span></code></pre></div>
<hr />
<h2 id="development-notes">Development Notes<a class="headerlink" href="#development-notes" title="Permalink">&para;</a></h2>
<div class="admonition warning">
<p class="admonition-title">Development Status</p>
<p>The following notes contain configurations that have been tested but are not yet integrated into the standard build process.</p>
</div>
<h3 id="gcp-image-upload">GCP Image Upload<a class="headerlink" href="#gcp-image-upload" title="Permalink">&para;</a></h3>
<h4 id="storage-location">Storage Location<a class="headerlink" href="#storage-location" title="Permalink">&para;</a></h4>
<p><a href="https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&amp;pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&amp;prefix=&amp;forceOnObjectsSortingFiltering=false">View in Google Cloud Console</a></p>
<h4 id="datastore-management">Datastore Management<a class="headerlink" href="#datastore-management" title="Permalink">&para;</a></h4>
<div class="admonition warning">
<p class="admonition-title">Important</p>
<p>Revert any old "roll forward" "device_service_" entries in the datastore to prevent accidental version rollbacks.</p>
</div>
<h4 id="file-sharing">File Sharing<a class="headerlink" href="#file-sharing" title="Permalink">&para;</a></h4>
<ol>
<li>
<p><strong>Generate Download Link</strong>
   <div class="language-text highlight"><pre><span></span><code><span id="__span-136-1"><a id="__codelineno-136-1" name="__codelineno-136-1" href="#__codelineno-136-1"></a>https://storage.cloud.google.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.2.zip
</span></code></pre></div></p>
</li>
<li>
<p><strong>Set Permissions</strong></p>
<ul>
<li>Add users at file level (e.g., <EMAIL>) - (Did not work)</li>
<li>Add users at bucket level for <code>pi-mgmt-pr-cah-distribution</code></li>
</ul>
</li>
</ol>
<hr />
<h2 id="usb-control">USB Control<a class="headerlink" href="#usb-control" title="Permalink">&para;</a></h2>
<h4 id="power-control-installation">Power Control Installation<a class="headerlink" href="#power-control-installation" title="Permalink">&para;</a></h4>
<p>For power control of USB in the <code>pi_hmi</code> service:</p>
<p><a href="https://snapcraft.io/install/hub-ctrl/raspbian">HUB CTRL: Raspbian</a></p>
<ol>
<li>
<p><strong>Using hub-ctrl</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-137-1"><a id="__codelineno-137-1" name="__codelineno-137-1" href="#__codelineno-137-1"></a><span class="c1"># Install snapd</span>
</span><span id="__span-137-2"><a id="__codelineno-137-2" name="__codelineno-137-2" href="#__codelineno-137-2"></a>sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>snapd
</span><span id="__span-137-3"><a id="__codelineno-137-3" name="__codelineno-137-3" href="#__codelineno-137-3"></a>
</span><span id="__span-137-4"><a id="__codelineno-137-4" name="__codelineno-137-4" href="#__codelineno-137-4"></a><span class="c1"># Install hub-ctrl</span>
</span><span id="__span-137-5"><a id="__codelineno-137-5" name="__codelineno-137-5" href="#__codelineno-137-5"></a>sudo<span class="w"> </span>snap<span class="w"> </span>install<span class="w"> </span>hub-ctrl
</span></code></pre></div></p>
</li>
<li>
<p><strong>Using uhubctl</strong></p>
<p><a href="https://github.com/mvp/uhubctl">Github: UHUBCTL</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-138-1"><a id="__codelineno-138-1" name="__codelineno-138-1" href="#__codelineno-138-1"></a><span class="c1"># Install dependencies</span>
</span><span id="__span-138-2"><a id="__codelineno-138-2" name="__codelineno-138-2" href="#__codelineno-138-2"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libusb-1.0-0-dev
</span><span id="__span-138-3"><a id="__codelineno-138-3" name="__codelineno-138-3" href="#__codelineno-138-3"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>git
</span><span id="__span-138-4"><a id="__codelineno-138-4" name="__codelineno-138-4" href="#__codelineno-138-4"></a>
</span><span id="__span-138-5"><a id="__codelineno-138-5" name="__codelineno-138-5" href="#__codelineno-138-5"></a><span class="c1"># Clone and build</span>
</span><span id="__span-138-6"><a id="__codelineno-138-6" name="__codelineno-138-6" href="#__codelineno-138-6"></a>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/mvp/uhubctl
</span><span id="__span-138-7"><a id="__codelineno-138-7" name="__codelineno-138-7" href="#__codelineno-138-7"></a><span class="nb">cd</span><span class="w"> </span>uhubctl
</span><span id="__span-138-8"><a id="__codelineno-138-8" name="__codelineno-138-8" href="#__codelineno-138-8"></a>make
</span><span id="__span-138-9"><a id="__codelineno-138-9" name="__codelineno-138-9" href="#__codelineno-138-9"></a>sudo<span class="w"> </span>make<span class="w"> </span>install
</span></code></pre></div>
</li>
</ol>
<hr />
<h2 id="gcp-bucket-upload-process">GCP Bucket Upload Process<a class="headerlink" href="#gcp-bucket-upload-process" title="Permalink">&para;</a></h2>
<ol>
<li>
<p><strong>Access Storage Console</strong></p>
<ul>
<li>
<p><a href="https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&amp;pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&amp;prefix=&amp;forceOnObjectsSortingFiltering=false">View in Google Cloud Console</a></p>
</li>
<li>
<p><a href="https://wiki.cardinalhealth.net/GCP_Cloud_Storage/PublicBuckets#Requesting_an_Exception()">Wiki: GCP Cloud Storage Guide</a></p>
</li>
</ul>
</li>
<li>
<p><strong>Upload Steps</strong></p>
<ul>
<li>Upload the zip file</li>
<li>Click into the file</li>
<li>Edit Permissions</li>
<li>Set Entity=Public, Name=allUsers, Access=Reader</li>
<li>Save permissions</li>
</ul>
</li>
<li>
<p><strong>Generate Public URL</strong></p>
<div class="language-sh highlight"><span class="filename">Example format:</span><pre><span></span><code><span id="__span-139-1"><a id="__codelineno-139-1" name="__codelineno-139-1" href="#__codelineno-139-1"></a>https://storage.googleapis.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.0.zip
</span></code></pre></div>
</li>
</ol>
<hr />
<h2 id="generic-lite-image">Generic Lite Image<a class="headerlink" href="#generic-lite-image" title="Permalink">&para;</a></h2>
<h3 id="initial-image-setup">Initial Image Setup<a class="headerlink" href="#initial-image-setup" title="Permalink">&para;</a></h3>
<ol>
<li>
<p><strong>Base Image Preparation</strong></p>
<ul>
<li>Set up a generic new Lite image (not NOOBS)</li>
<li>Expand to only 1 GB (consider doing in VM)</li>
<li>Connect Pi to network</li>
</ul>
</li>
<li>
<p><strong>Initial Configuration</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-140-1"><a id="__codelineno-140-1" name="__codelineno-140-1" href="#__codelineno-140-1"></a>curl<span class="w"> </span>-k<span class="w"> </span>slicer.cardinalhealth.net/build
</span></code></pre></div>
    This initiates an SSH connection to:</p>
<ul>
<li>Install initial monitor</li>
<li>Allow full compliance push</li>
</ul>
</li>
<li>
<p><strong>File System Management</strong></p>
<ul>
<li>Shrink the file system</li>
<li>Re-expand on next boot</li>
</ul>
<p><em>Reference Links:</em></p>
<ul>
<li><a href="https://github.com/qrti/shrink">Shrink Tool</a></li>
<li><a href="https://blog.febo.com/?p=283">Shrinking Guide</a></li>
<li><a href="https://raspberrypi.stackexchange.com/questions/29947/reverse-the-expand-root-fs">Reverse Root FS Expansion</a></li>
</ul>
</li>
<li>
<p><strong>Image Creation Process</strong></p>
<ul>
<li>Perform clean shutdown</li>
<li>Create ISO/equivalent of card</li>
<li>Burn to new card</li>
<li>Allow startup process to expand card to full size</li>
</ul>
<p><em>Reference:</em> <a href="http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php">SD Image Resizing Guide</a></p>
</li>
<li>
<p><strong>Expansion Commands</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-141-1"><a id="__codelineno-141-1" name="__codelineno-141-1" href="#__codelineno-141-1"></a>sudo<span class="w"> </span>raspi-config<span class="w"> </span>--expand-rootfs
</span><span id="__span-141-2"><a id="__codelineno-141-2" name="__codelineno-141-2" href="#__codelineno-141-2"></a>sudo<span class="w"> </span>reboot
</span><span id="__span-141-3"><a id="__codelineno-141-3" name="__codelineno-141-3" href="#__codelineno-141-3"></a>
</span><span id="__span-141-4"><a id="__codelineno-141-4" name="__codelineno-141-4" href="#__codelineno-141-4"></a>raspi-config<span class="w"> </span>--expand-rootfs
</span></code></pre></div></p>
</li>
</ol>
<h4 id="password-change">Password Change<a class="headerlink" href="#password-change" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-142-1"><a id="__codelineno-142-1" name="__codelineno-142-1" href="#__codelineno-142-1"></a>passwd
</span></code></pre></div>
<h4 id="create-admin-user">Create Admin User<a class="headerlink" href="#create-admin-user" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-143-1"><a id="__codelineno-143-1" name="__codelineno-143-1" href="#__codelineno-143-1"></a><span class="c1"># Create new admin user</span>
</span><span id="__span-143-2"><a id="__codelineno-143-2" name="__codelineno-143-2" href="#__codelineno-143-2"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>pisuperuser
</span><span id="__span-143-3"><a id="__codelineno-143-3" name="__codelineno-143-3" href="#__codelineno-143-3"></a>
</span><span id="__span-143-4"><a id="__codelineno-143-4" name="__codelineno-143-4" href="#__codelineno-143-4"></a><span class="c1"># Add to necessary groups</span>
</span><span id="__span-143-5"><a id="__codelineno-143-5" name="__codelineno-143-5" href="#__codelineno-143-5"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>video<span class="w"> </span>pisuperuser
</span><span id="__span-143-6"><a id="__codelineno-143-6" name="__codelineno-143-6" href="#__codelineno-143-6"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>pisuperuser<span class="w"> </span>sudo
</span></code></pre></div>
<h4 id="create-worker-user">Create Worker User<a class="headerlink" href="#create-worker-user" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-144-1"><a id="__codelineno-144-1" name="__codelineno-144-1" href="#__codelineno-144-1"></a><span class="c1"># Create worker user</span>
</span><span id="__span-144-2"><a id="__codelineno-144-2" name="__codelineno-144-2" href="#__codelineno-144-2"></a>sudo<span class="w"> </span>adduser<span class="w"> </span>worker
</span><span id="__span-144-3"><a id="__codelineno-144-3" name="__codelineno-144-3" href="#__codelineno-144-3"></a>
</span><span id="__span-144-4"><a id="__codelineno-144-4" name="__codelineno-144-4" href="#__codelineno-144-4"></a><span class="c1"># Add to necessary groups</span>
</span><span id="__span-144-5"><a id="__codelineno-144-5" name="__codelineno-144-5" href="#__codelineno-144-5"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>video<span class="w"> </span>worker
</span><span id="__span-144-6"><a id="__codelineno-144-6" name="__codelineno-144-6" href="#__codelineno-144-6"></a>sudo<span class="w"> </span>usermod<span class="w"> </span>-a<span class="w"> </span>-G<span class="w"> </span>audio<span class="w"> </span>worker<span class="w">  </span><span class="c1"># Added 2024.05.09</span>
</span></code></pre></div>
<h4 id="configure-autologin">Configure Autologin<a class="headerlink" href="#configure-autologin" title="Permalink">&para;</a></h4>
<p>Change the autologin user to the worker</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-145-1"><a id="__codelineno-145-1" name="__codelineno-145-1" href="#__codelineno-145-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/lightdm/lightdm.conf
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Replace:</span><pre><span></span><code><span id="__span-146-1"><a id="__codelineno-146-1" name="__codelineno-146-1" href="#__codelineno-146-1"></a>autologin-user<span class="o">=</span>pi
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">With:</span><pre><span></span><code><span id="__span-147-1"><a id="__codelineno-147-1" name="__codelineno-147-1" href="#__codelineno-147-1"></a>autologin-user<span class="o">=</span>worker
</span></code></pre></div>
<h2 id="chromium-cah-root-certificate">Chromium CAH Root Certificate<a class="headerlink" href="#chromium-cah-root-certificate" title="Permalink">&para;</a></h2>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-148-1"><a id="__codelineno-148-1" name="__codelineno-148-1" href="#__codelineno-148-1"></a><span class="c1"># Navigate to certificates directory</span>
</span><span id="__span-148-2"><a id="__codelineno-148-2" name="__codelineno-148-2" href="#__codelineno-148-2"></a><span class="nb">cd</span><span class="w"> </span>/usr/local/share/ca-certificates
</span><span id="__span-148-3"><a id="__codelineno-148-3" name="__codelineno-148-3" href="#__codelineno-148-3"></a>
</span><span id="__span-148-4"><a id="__codelineno-148-4" name="__codelineno-148-4" href="#__codelineno-148-4"></a><span class="c1"># Convert and install certificate</span>
</span><span id="__span-148-5"><a id="__codelineno-148-5" name="__codelineno-148-5" href="#__codelineno-148-5"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Root-CA-PR1.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Root-CA-PR1.crt
</span><span id="__span-148-6"><a id="__codelineno-148-6" name="__codelineno-148-6" href="#__codelineno-148-6"></a>sudo<span class="w"> </span>update-ca-certificates
</span><span id="__span-148-7"><a id="__codelineno-148-7" name="__codelineno-148-7" href="#__codelineno-148-7"></a>
</span><span id="__span-148-8"><a id="__codelineno-148-8" name="__codelineno-148-8" href="#__codelineno-148-8"></a><span class="c1"># Update system packages</span>
</span><span id="__span-148-9"><a id="__codelineno-148-9" name="__codelineno-148-9" href="#__codelineno-148-9"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>update
</span><span id="__span-148-10"><a id="__codelineno-148-10" name="__codelineno-148-10" href="#__codelineno-148-10"></a>sudo<span class="w"> </span>apt<span class="w"> </span>-y<span class="w"> </span>--fix-broken<span class="w"> </span>install
</span><span id="__span-148-11"><a id="__codelineno-148-11" name="__codelineno-148-11" href="#__codelineno-148-11"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>libnss3-tools
</span><span id="__span-148-12"><a id="__codelineno-148-12" name="__codelineno-148-12" href="#__codelineno-148-12"></a>
</span><span id="__span-148-13"><a id="__codelineno-148-13" name="__codelineno-148-13" href="#__codelineno-148-13"></a><span class="c1"># Add certificate to NSS database</span>
</span><span id="__span-148-14"><a id="__codelineno-148-14" name="__codelineno-148-14" href="#__codelineno-148-14"></a>certutil<span class="w"> </span>-d<span class="w"> </span>sql:<span class="nv">$HOME</span>/.pki/nssdb<span class="w"> </span>-L
</span><span id="__span-148-15"><a id="__codelineno-148-15" name="__codelineno-148-15" href="#__codelineno-148-15"></a>certutil<span class="w"> </span>-d<span class="w"> </span>sql:<span class="nv">$HOME</span>/.pki/nssdb<span class="w"> </span>-A<span class="w"> </span>-t<span class="w"> </span>C<span class="w"> </span>-n<span class="w"> </span>CAH-Root-CA-PR1<span class="w"> </span>-i<span class="w"> </span>CAH-Root-CA-PR1.crt
</span><span id="__span-148-16"><a id="__codelineno-148-16" name="__codelineno-148-16" href="#__codelineno-148-16"></a>
</span><span id="__span-148-17"><a id="__codelineno-148-17" name="__codelineno-148-17" href="#__codelineno-148-17"></a><span class="c1"># Restart Browser</span>
</span><span id="__span-148-18"><a id="__codelineno-148-18" name="__codelineno-148-18" href="#__codelineno-148-18"></a>
</span><span id="__span-148-19"><a id="__codelineno-148-19" name="__codelineno-148-19" href="#__codelineno-148-19"></a><span class="c1"># Set permissions for worker user</span>
</span><span id="__span-148-20"><a id="__codelineno-148-20" name="__codelineno-148-20" href="#__codelineno-148-20"></a>sudo<span class="w"> </span>chmod<span class="w"> </span><span class="m">644</span><span class="w"> </span>/usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
</span><span id="__span-148-21"><a id="__codelineno-148-21" name="__codelineno-148-21" href="#__codelineno-148-21"></a>
</span><span id="__span-148-22"><a id="__codelineno-148-22" name="__codelineno-148-22" href="#__codelineno-148-22"></a><span class="c1"># sudo mkdir -p /home/<USER>/.pki/nssdb</span>
</span><span id="__span-148-23"><a id="__codelineno-148-23" name="__codelineno-148-23" href="#__codelineno-148-23"></a><span class="c1"># sudo certutil -d /home/<USER>/.pki/nssdb -N --empty-password</span>
</span><span id="__span-148-24"><a id="__codelineno-148-24" name="__codelineno-148-24" href="#__codelineno-148-24"></a>
</span><span id="__span-148-25"><a id="__codelineno-148-25" name="__codelineno-148-25" href="#__codelineno-148-25"></a><span class="c1"># Add certificate for worker user</span>
</span><span id="__span-148-26"><a id="__codelineno-148-26" name="__codelineno-148-26" href="#__codelineno-148-26"></a>sudo<span class="w"> </span>su<span class="w"> </span>worker
</span><span id="__span-148-27"><a id="__codelineno-148-27" name="__codelineno-148-27" href="#__codelineno-148-27"></a>certutil<span class="w"> </span>-d<span class="w"> </span>sql:<span class="nv">$HOME</span>/.pki/nssdb<span class="w"> </span>-L
</span><span id="__span-148-28"><a id="__codelineno-148-28" name="__codelineno-148-28" href="#__codelineno-148-28"></a>certutil<span class="w"> </span>-d<span class="w"> </span>sql:<span class="nv">$HOME</span>/.pki/nssdb<span class="w"> </span>-A<span class="w"> </span>-t<span class="w"> </span>C<span class="w"> </span>-n<span class="w"> </span>CAH-Root-CA-PR1<span class="w"> </span>-i<span class="w"> </span>/usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
</span><span id="__span-148-29"><a id="__codelineno-148-29" name="__codelineno-148-29" href="#__codelineno-148-29"></a><span class="nb">exit</span>
</span></code></pre></div>
<h4 id="prevent-screen-blanking">Prevent Screen Blanking<a class="headerlink" href="#prevent-screen-blanking" title="Permalink">&para;</a></h4>
<p><a href="https://www.raspberrypi.org/documentation/configuration/">Raspberry Pi: Configuration</a>
<a href="https://www.raspberrypi.org/documentation/configuration/screensaver.md">Raspberry Pi: Screensaver</a></p>
<ol>
<li>
<p><strong>Edit Boot Configuration</strong>
    <div class="language-bash highlight"><pre><span></span><code><span id="__span-149-1"><a id="__codelineno-149-1" name="__codelineno-149-1" href="#__codelineno-149-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/boot/cmdline.txt
</span></code></pre></div></p>
<div class="language-sh highlight"><span class="filename">Add to end of line:</span><pre><span></span><code><span id="__span-150-1"><a id="__codelineno-150-1" name="__codelineno-150-1" href="#__codelineno-150-1"></a><span class="nv">consoleblank</span><span class="o">=</span><span class="m">0</span>
</span></code></pre></div>
</li>
<li>
<p><strong>Configure LightDM</strong></p>
<p><a href="https://raspberrypi.stackexchange.com/questions/752/how-do-i-prevent-the-screen-from-going-blank">How to Prevent Screen going Blank</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-151-1"><a id="__codelineno-151-1" name="__codelineno-151-1" href="#__codelineno-151-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/lightdm/lightdm.conf
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">Change:</span><pre><span></span><code><span id="__span-152-1"><a id="__codelineno-152-1" name="__codelineno-152-1" href="#__codelineno-152-1"></a><span class="c1">#xserver-command=X</span>
</span></code></pre></div>
<div class="language-sh highlight"><span class="filename">To:</span><pre><span></span><code><span id="__span-153-1"><a id="__codelineno-153-1" name="__codelineno-153-1" href="#__codelineno-153-1"></a>xserver-command<span class="o">=</span>X<span class="w"> </span>-s<span class="w"> </span><span class="m">0</span><span class="w"> </span>dpms
</span></code></pre></div>
</li>
</ol>
<h2 id="planning-and-untested-features">Planning and Untested Features<a class="headerlink" href="#planning-and-untested-features" title="Permalink">&para;</a></h2>
<div class="admonition warning">
<p class="admonition-title">Development Status</p>
<p>The following features are planned but have not yet been tested or integrated into the standard build process.</p>
</div>
<h3 id="step-1-list-completion">Step 1 List Completion<a class="headerlink" href="#step-1-list-completion" title="Permalink">&para;</a></h3>
<ul>
<li>Complete remaining items from the "step1" list</li>
</ul>
<h3 id="browser-configuration">Browser Configuration<a class="headerlink" href="#browser-configuration" title="Permalink">&para;</a></h3>
<ol>
<li><strong>Auto-start Browser</strong></li>
<li>
<p>Implement automatic browser startup on system boot</p>
</li>
<li>
<p><strong>Disable Update Warnings</strong></p>
</li>
<li>Configure browser to suppress update notification popups</li>
</ol>
<h3 id="remote-access">Remote Access<a class="headerlink" href="#remote-access" title="Permalink">&para;</a></h3>
<h4 id="vnc-setup">VNC Setup<a class="headerlink" href="#vnc-setup" title="Permalink">&para;</a></h4>
<p><a href="https://mike632t.wordpress.com/2013/04/18/remote-desktop-vnc/">Reference: Remote Desktop VNC Guide</a></p>
<ul>
<li><a href="https://www.realvnc.com/en/connect/download/viewer/">VNC Viewer Download</a></li>
</ul>
<h3 id="security-enhancements">Security Enhancements<a class="headerlink" href="#security-enhancements" title="Permalink">&para;</a></h3>
<h4 id="remove-default-pi-user">Remove Default Pi User<a class="headerlink" href="#remove-default-pi-user" title="Permalink">&para;</a></h4>
<p><a href="https://www.raspberrypi.org/documentation/configuration/security.md">Reference: Security Documentation</a></p>
<h3 id="certificate-management">Certificate Management<a class="headerlink" href="#certificate-management" title="Permalink">&para;</a></h3>
<h4 id="rclocal-configuration">RC.Local Configuration<a class="headerlink" href="#rclocal-configuration" title="Permalink">&para;</a></h4>
<p>Add to <code>/etc/rc.local</code>:
<div class="language-bash highlight"><pre><span></span><code><span id="__span-154-1"><a id="__codelineno-154-1" name="__codelineno-154-1" href="#__codelineno-154-1"></a>update-ca-certificates
</span></code></pre></div></p>
<p><a href="http://manpages.ubuntu.com/manpages/xenial/man8/update-ca-certificates.8.html">Reference: update-ca-certificates Manual</a></p>
<h4 id="certificate-installation">Certificate Installation<a class="headerlink" href="#certificate-installation" title="Permalink">&para;</a></h4>
<p>All <code>*.crt</code> files in <code>/usr/local/share/ca-certificates</code> are trusted.</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-155-1"><a id="__codelineno-155-1" name="__codelineno-155-1" href="#__codelineno-155-1"></a><span class="nb">cd</span><span class="w"> </span>/usr/local/share/ca-certificates
</span><span id="__span-155-2"><a id="__codelineno-155-2" name="__codelineno-155-2" href="#__codelineno-155-2"></a>
</span><span id="__span-155-3"><a id="__codelineno-155-3" name="__codelineno-155-3" href="#__codelineno-155-3"></a><span class="c1"># Convert and install certificates</span>
</span><span id="__span-155-4"><a id="__codelineno-155-4" name="__codelineno-155-4" href="#__codelineno-155-4"></a>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>PEM<span class="w"> </span>-in<span class="w"> </span>entrust_l1k.cer<span class="w"> </span>-outform<span class="w"> </span>PEM<span class="w"> </span>-out<span class="w"> </span>entrust_l1k.crt
</span><span id="__span-155-5"><a id="__codelineno-155-5" name="__codelineno-155-5" href="#__codelineno-155-5"></a>
</span><span id="__span-155-6"><a id="__codelineno-155-6" name="__codelineno-155-6" href="#__codelineno-155-6"></a><span class="c1"># Install CAH certificates</span>
</span><span id="__span-155-7"><a id="__codelineno-155-7" name="__codelineno-155-7" href="#__codelineno-155-7"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Dev-Root01.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Dev-Root01.crt
</span><span id="__span-155-8"><a id="__codelineno-155-8" name="__codelineno-155-8" href="#__codelineno-155-8"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-DMZ-Root01.cer<span class="w"> </span>-out<span class="w"> </span>CAH-DMZ-Root01.crt
</span><span id="__span-155-9"><a id="__codelineno-155-9" name="__codelineno-155-9" href="#__codelineno-155-9"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Issuing01.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Issuing01.crt
</span><span id="__span-155-10"><a id="__codelineno-155-10" name="__codelineno-155-10" href="#__codelineno-155-10"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Issuing02.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Issuing02.crt
</span><span id="__span-155-11"><a id="__codelineno-155-11" name="__codelineno-155-11" href="#__codelineno-155-11"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Issuing03.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Issuing03.crt
</span><span id="__span-155-12"><a id="__codelineno-155-12" name="__codelineno-155-12" href="#__codelineno-155-12"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Issuing-CA-PR1.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Issuing-CA-PR1.crt
</span><span id="__span-155-13"><a id="__codelineno-155-13" name="__codelineno-155-13" href="#__codelineno-155-13"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Issuing-CA-PR2.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Issuing-CA-PR2.crt
</span><span id="__span-155-14"><a id="__codelineno-155-14" name="__codelineno-155-14" href="#__codelineno-155-14"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Root01.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Root01.crt
</span><span id="__span-155-15"><a id="__codelineno-155-15" name="__codelineno-155-15" href="#__codelineno-155-15"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Root-CA-PR1.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Root-CA-PR1.crt
</span><span id="__span-155-16"><a id="__codelineno-155-16" name="__codelineno-155-16" href="#__codelineno-155-16"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAIssuing3.cer<span class="w"> </span>-out<span class="w"> </span>CAIssuing3.crt
</span><span id="__span-155-17"><a id="__codelineno-155-17" name="__codelineno-155-17" href="#__codelineno-155-17"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAIssuing4.cer<span class="w"> </span>-out<span class="w"> </span>CAIssuing4.crt
</span><span id="__span-155-18"><a id="__codelineno-155-18" name="__codelineno-155-18" href="#__codelineno-155-18"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span><span class="s1">&#39;Cardinal Health JSS Built-in Certificate Authority.cer&#39;</span><span class="w"> </span>-out<span class="w"> </span><span class="s1">&#39;Cardinal Health JSS Built-in Certificate Authority.crt&#39;</span>
</span><span id="__span-155-19"><a id="__codelineno-155-19" name="__codelineno-155-19" href="#__codelineno-155-19"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span><span class="s1">&#39;CAROOT 1.cer&#39;</span><span class="w"> </span>-out<span class="w"> </span><span class="s1">&#39;CAROOT 1.crt&#39;</span>
</span><span id="__span-155-20"><a id="__codelineno-155-20" name="__codelineno-155-20" href="#__codelineno-155-20"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CARoot.cer<span class="w"> </span>-out<span class="w"> </span>CARoot.crt
</span><span id="__span-155-21"><a id="__codelineno-155-21" name="__codelineno-155-21" href="#__codelineno-155-21"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span><span class="s1">&#39;COMODO ECC Certification Authority.cer&#39;</span><span class="w"> </span>-out<span class="w"> </span><span class="s1">&#39;COMODO ECC Certification Authority.crt&#39;</span>
</span><span id="__span-155-22"><a id="__codelineno-155-22" name="__codelineno-155-22" href="#__codelineno-155-22"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span><span class="s1">&#39;COMODO RSA Certification Authority.cer&#39;</span><span class="w"> </span>-out<span class="w"> </span><span class="s1">&#39;COMODO RSA Certification Authority.crt&#39;</span>
</span><span id="__span-155-23"><a id="__codelineno-155-23" name="__codelineno-155-23" href="#__codelineno-155-23"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span><span class="s1">&#39;COMODO RSA Organization Validation Secure Server CA.cer&#39;</span><span class="w"> </span>-out<span class="w"> </span><span class="s1">&#39;COMODO RSA Organization Validation Secure Server CA.crt&#39;</span>
</span></code></pre></div>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-156-1"><a id="__codelineno-156-1" name="__codelineno-156-1" href="#__codelineno-156-1"></a><span class="c1"># Did not help</span>
</span><span id="__span-156-2"><a id="__codelineno-156-2" name="__codelineno-156-2" href="#__codelineno-156-2"></a>sudo<span class="w"> </span>update-ca-certificates
</span><span id="__span-156-3"><a id="__codelineno-156-3" name="__codelineno-156-3" href="#__codelineno-156-3"></a><span class="c1"># or just the one</span>
</span><span id="__span-156-4"><a id="__codelineno-156-4" name="__codelineno-156-4" href="#__codelineno-156-4"></a>sudo<span class="w"> </span>openssl<span class="w"> </span>x509<span class="w"> </span>-inform<span class="w"> </span>DER<span class="w"> </span>-in<span class="w"> </span>CAH-Root-CA-PR1.cer<span class="w"> </span>-out<span class="w"> </span>CAH-Root-CA-PR1.crt
</span></code></pre></div>
<h4 id="chromium-certificate-configuration">Chromium Certificate Configuration<a class="headerlink" href="#chromium-certificate-configuration" title="Permalink">&para;</a></h4>
<ol>
<li>
<p><strong>In Chromium</strong></p>
<ul>
<li>Settings → Advanced → Privacy and security → Manage certificates → Authorities</li>
<li>Import → Other Locations → Computer → <code>/usr/local/share/ca-certificates</code></li>
<li>Select <code>CAH-Root-CA-PR1.crt</code> → Open</li>
<li>Refresh browser page (with the url that had the issue)</li>
</ul>
</li>
<li>
<p><strong>Alternative Methods</strong></p>
<ul>
<li><a href="https://chromium.googlesource.com/chromium/src/+/master/docs/linux/cert_management.md">Chromium Certificate Management</a></li>
<li><a href="https://wiki.archlinux.org/index.php/Chromium">Arch Linux Chromium Guide</a></li>
</ul>
<div class="tabbed-set tabbed-alternate" data-tabs="2:2"><input checked="checked" id="__tabbed_2_1" name="__tabbed_2" type="radio" /><input id="__tabbed_2_2" name="__tabbed_2" type="radio" /><div class="tabbed-labels"><label for="__tabbed_2_1"><strong>Configuration Files</strong></label><label for="__tabbed_2_2"><strong>Command Line Options</strong></label></div>
<div class="tabbed-content">
<div class="tabbed-block">
<div class="language-bash highlight"><pre><span></span><code><span id="__span-157-1"><a id="__codelineno-157-1" name="__codelineno-157-1" href="#__codelineno-157-1"></a>~/.pki/nssdb
</span><span id="__span-157-2"><a id="__codelineno-157-2" name="__codelineno-157-2" href="#__codelineno-157-2"></a>/home/<USER>/.config/chromium/Default/Preferences
</span><span id="__span-157-3"><a id="__codelineno-157-3" name="__codelineno-157-3" href="#__codelineno-157-3"></a><span class="s1">&#39;/home/<USER>/.config/chromium/Default/Current Session&#39;</span>
</span></code></pre></div>
</div>
<div class="tabbed-block">
<ul>
<li><a href="https://peter.sh/experiments/chromium-command-line-switches/">Chromium Command Line Switches</a></li>
<li><a href="https://www.chromium.org/developers/how-tos/run-chromium-with-flags">Running Chromium with Flags</a></li>
<li><a href="https://desertbot.io/blog/raspberry-pi-touchscreen-kiosk-setup">Raspberry Pi Touchscreen Kiosk Setup</a></li>
</ul>
</div>
</div>
</div>
<h4 id="testing">Testing<a class="headerlink" href="#testing" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-158-1"><a id="__codelineno-158-1" name="__codelineno-158-1" href="#__codelineno-158-1"></a><span class="c1"># Verify connectivity</span>
</span><span id="__span-158-2"><a id="__codelineno-158-2" name="__codelineno-158-2" href="#__codelineno-158-2"></a>curl<span class="w"> </span>https://slicer.cardinalhealth.net
</span><span id="__span-158-3"><a id="__codelineno-158-3" name="__codelineno-158-3" href="#__codelineno-158-3"></a>
</span><span id="__span-158-4"><a id="__codelineno-158-4" name="__codelineno-158-4" href="#__codelineno-158-4"></a><span class="c1"># Install and test with Lynx</span>
</span><span id="__span-158-5"><a id="__codelineno-158-5" name="__codelineno-158-5" href="#__codelineno-158-5"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>lynx
</span><span id="__span-158-6"><a id="__codelineno-158-6" name="__codelineno-158-6" href="#__codelineno-158-6"></a>lynx<span class="w"> </span>https://slicer.cardinalhealth.net
</span><span id="__span-158-7"><a id="__codelineno-158-7" name="__codelineno-158-7" href="#__codelineno-158-7"></a>lynx<span class="w"> </span>https://slicer.cardinalhealth.net/reports
</span><span id="__span-158-8"><a id="__codelineno-158-8" name="__codelineno-158-8" href="#__codelineno-158-8"></a>
</span><span id="__span-158-9"><a id="__codelineno-158-9" name="__codelineno-158-9" href="#__codelineno-158-9"></a><span class="c1"># openssl x509 -in /etc/ssl/certs/ca-certificates.crt -text -noout</span>
</span><span id="__span-158-10"><a id="__codelineno-158-10" name="__codelineno-158-10" href="#__codelineno-158-10"></a><span class="c1"># restart:</span>
</span><span id="__span-158-11"><a id="__codelineno-158-11" name="__codelineno-158-11" href="#__codelineno-158-11"></a><span class="c1"># sudo rm /etc/ssl/certs/ca-certificates.crt</span>
</span></code></pre></div>
</li>
</ol>
<hr />
<h2 id="system-updates_1">System Updates<a class="headerlink" href="#system-updates_1" title="Permalink">&para;</a></h2>
<h3 id="cve-and-kernel-updates">CVE and Kernel Updates<a class="headerlink" href="#cve-and-kernel-updates" title="Permalink">&para;</a></h3>
<p><a href="https://www.raspberrypi.org/documentation/raspbian/updating.md">Reference: Updating Documentation</a></p>
<h3 id="keyboard-configuration">Keyboard Configuration<a class="headerlink" href="#keyboard-configuration" title="Permalink">&para;</a></h3>
<h4 id="disable-specific-keys">Disable Specific Keys<a class="headerlink" href="#disable-specific-keys" title="Permalink">&para;</a></h4>
<p><a href="https://stackoverflow.com/questions/50646587/making-xmodmap-changes-permanent-on-raspberry-pi">Reference: Making xmodmap Changes Permanent</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-159-1"><a id="__codelineno-159-1" name="__codelineno-159-1" href="#__codelineno-159-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/usr/share/X11/xkb/keycodes/evdev
</span></code></pre></div>
<p>Add <code>//</code> to front of specified lines:</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-160-1"><a id="__codelineno-160-1" name="__codelineno-160-1" href="#__codelineno-160-1"></a>***<span class="w"> </span>start<span class="w"> </span>***
</span><span id="__span-160-2"><a id="__codelineno-160-2" name="__codelineno-160-2" href="#__codelineno-160-2"></a>//<span class="w">      </span>&lt;LALT&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">64</span><span class="p">;</span>
</span><span id="__span-160-3"><a id="__codelineno-160-3" name="__codelineno-160-3" href="#__codelineno-160-3"></a>//<span class="w">      </span>&lt;LCTL&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">37</span><span class="p">;</span>
</span><span id="__span-160-4"><a id="__codelineno-160-4" name="__codelineno-160-4" href="#__codelineno-160-4"></a><span class="w">        </span>&lt;SPCE&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">65</span><span class="p">;</span>
</span><span id="__span-160-5"><a id="__codelineno-160-5" name="__codelineno-160-5" href="#__codelineno-160-5"></a>//<span class="w">      </span>&lt;RCTL&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">105</span><span class="p">;</span>
</span><span id="__span-160-6"><a id="__codelineno-160-6" name="__codelineno-160-6" href="#__codelineno-160-6"></a>//<span class="w">      </span>&lt;RALT&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">108</span><span class="p">;</span>
</span><span id="__span-160-7"><a id="__codelineno-160-7" name="__codelineno-160-7" href="#__codelineno-160-7"></a><span class="w">        </span>//<span class="w"> </span>Microsoft<span class="w"> </span>keyboard<span class="w"> </span>extra<span class="w"> </span>keys
</span><span id="__span-160-8"><a id="__codelineno-160-8" name="__codelineno-160-8" href="#__codelineno-160-8"></a>//<span class="w">      </span>&lt;LWIN&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">133</span><span class="p">;</span>
</span><span id="__span-160-9"><a id="__codelineno-160-9" name="__codelineno-160-9" href="#__codelineno-160-9"></a>//<span class="w">      </span>&lt;RWIN&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">134</span><span class="p">;</span>
</span><span id="__span-160-10"><a id="__codelineno-160-10" name="__codelineno-160-10" href="#__codelineno-160-10"></a>
</span><span id="__span-160-11"><a id="__codelineno-160-11" name="__codelineno-160-11" href="#__codelineno-160-11"></a><span class="w">        </span>&lt;ESC&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">9</span><span class="p">;</span>
</span><span id="__span-160-12"><a id="__codelineno-160-12" name="__codelineno-160-12" href="#__codelineno-160-12"></a>//<span class="w">      </span>&lt;FK01&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">67</span><span class="p">;</span>
</span><span id="__span-160-13"><a id="__codelineno-160-13" name="__codelineno-160-13" href="#__codelineno-160-13"></a>//<span class="w">      </span>&lt;FK02&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">68</span><span class="p">;</span>
</span><span id="__span-160-14"><a id="__codelineno-160-14" name="__codelineno-160-14" href="#__codelineno-160-14"></a>//<span class="w">      </span>&lt;FK03&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">69</span><span class="p">;</span>
</span><span id="__span-160-15"><a id="__codelineno-160-15" name="__codelineno-160-15" href="#__codelineno-160-15"></a>//<span class="w">      </span>&lt;FK04&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">70</span><span class="p">;</span>
</span><span id="__span-160-16"><a id="__codelineno-160-16" name="__codelineno-160-16" href="#__codelineno-160-16"></a>//<span class="w">      </span>&lt;FK05&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">71</span><span class="p">;</span>
</span><span id="__span-160-17"><a id="__codelineno-160-17" name="__codelineno-160-17" href="#__codelineno-160-17"></a>//<span class="w">      </span>&lt;FK06&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">72</span><span class="p">;</span>
</span><span id="__span-160-18"><a id="__codelineno-160-18" name="__codelineno-160-18" href="#__codelineno-160-18"></a>//<span class="w">      </span>&lt;FK07&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">73</span><span class="p">;</span>
</span><span id="__span-160-19"><a id="__codelineno-160-19" name="__codelineno-160-19" href="#__codelineno-160-19"></a>//<span class="w">      </span>&lt;FK08&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">74</span><span class="p">;</span>
</span><span id="__span-160-20"><a id="__codelineno-160-20" name="__codelineno-160-20" href="#__codelineno-160-20"></a>//<span class="w">      </span>&lt;FK09&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">75</span><span class="p">;</span>
</span><span id="__span-160-21"><a id="__codelineno-160-21" name="__codelineno-160-21" href="#__codelineno-160-21"></a>//<span class="w">      </span>&lt;FK10&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">76</span><span class="p">;</span>
</span><span id="__span-160-22"><a id="__codelineno-160-22" name="__codelineno-160-22" href="#__codelineno-160-22"></a>//<span class="w">      </span>&lt;FK11&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">95</span><span class="p">;</span>
</span><span id="__span-160-23"><a id="__codelineno-160-23" name="__codelineno-160-23" href="#__codelineno-160-23"></a>//<span class="w">      </span>&lt;FK12&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">96</span><span class="p">;</span>
</span><span id="__span-160-24"><a id="__codelineno-160-24" name="__codelineno-160-24" href="#__codelineno-160-24"></a>
</span><span id="__span-160-25"><a id="__codelineno-160-25" name="__codelineno-160-25" href="#__codelineno-160-25"></a>//<span class="w">      </span>&lt;VOL-&gt;<span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="m">122</span><span class="p">;</span>
</span><span id="__span-160-26"><a id="__codelineno-160-26" name="__codelineno-160-26" href="#__codelineno-160-26"></a>***<span class="w"> </span>end<span class="w"> </span>***
</span></code></pre></div>
<h3 id="boot-configuration">Boot Configuration<a class="headerlink" href="#boot-configuration" title="Permalink">&para;</a></h3>
<h4 id="startup-screen-management">Startup Screen Management<a class="headerlink" href="#startup-screen-management" title="Permalink">&para;</a></h4>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-161-1"><a id="__codelineno-161-1" name="__codelineno-161-1" href="#__codelineno-161-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/home/<USER>/.xinitrc
</span></code></pre></div>
<p>Add tvservice:</p>
<p>We could do this inside of /cardinal/browserstart, which is managed in pi_runner</p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-162-1"><a id="__codelineno-162-1" name="__codelineno-162-1" href="#__codelineno-162-1"></a><span class="k">while</span><span class="w"> </span>true<span class="p">;</span><span class="w"> </span><span class="k">do</span>
</span><span id="__span-162-2"><a id="__codelineno-162-2" name="__codelineno-162-2" href="#__codelineno-162-2"></a><span class="w">    </span>tvservice<span class="w"> </span>-p
</span><span id="__span-162-3"><a id="__codelineno-162-3" name="__codelineno-162-3" href="#__codelineno-162-3"></a><span class="w">    </span>/cardinal/browserstart
</span><span id="__span-162-4"><a id="__codelineno-162-4" name="__codelineno-162-4" href="#__codelineno-162-4"></a><span class="k">done</span><span class="p">;</span>
</span></code></pre></div>
<h4 id="boot-sequence-customization">Boot Sequence Customization<a class="headerlink" href="#boot-sequence-customization" title="Permalink">&para;</a></h4>
<p><a href="https://scribles.net/customizing-boot-up-screen-on-raspberry-pi/">Reference: Customizing Boot Screen</a></p>
<ol>
<li><strong>Configure Console</strong></li>
</ol>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-163-1"><a id="__codelineno-163-1" name="__codelineno-163-1" href="#__codelineno-163-1"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/boot/cmdline.txt
</span></code></pre></div>
<p>Set to <code>console=tty3</code></p>
<ol>
<li><strong>Add Boot Parameters</strong></li>
</ol>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-164-1"><a id="__codelineno-164-1" name="__codelineno-164-1" href="#__codelineno-164-1"></a>splash<span class="w"> </span>quiet<span class="w"> </span>logo.nologo<span class="w"> </span>vt.global_cursor_default<span class="o">=</span><span class="m">0</span>
</span></code></pre></div>
<ol>
<li><strong>Custom Boot Logo</strong></li>
</ol>
<p><a href="https://raspberry-projects.com/pi/pi-operating-systems/raspbian/custom-boot-up-screen">Reference: Custom Boot Screen Guide</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-165-1"><a id="__codelineno-165-1" name="__codelineno-165-1" href="#__codelineno-165-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>update
</span><span id="__span-165-2"><a id="__codelineno-165-2" name="__codelineno-165-2" href="#__codelineno-165-2"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>fbi
</span><span id="__span-165-3"><a id="__codelineno-165-3" name="__codelineno-165-3" href="#__codelineno-165-3"></a>
</span><span id="__span-165-4"><a id="__codelineno-165-4" name="__codelineno-165-4" href="#__codelineno-165-4"></a>sudo<span class="w"> </span>vi<span class="w"> </span>/etc/systemd/system/splashscreen.service
</span></code></pre></div>
<div class="language-ini highlight"><span class="filename">Add</span><pre><span></span><code><span id="__span-166-1"><a id="__codelineno-166-1" name="__codelineno-166-1" href="#__codelineno-166-1"></a><span class="k">[Unit]</span>
</span><span id="__span-166-2"><a id="__codelineno-166-2" name="__codelineno-166-2" href="#__codelineno-166-2"></a><span class="na">Description</span><span class="o">=</span><span class="s">Splash screen</span>
</span><span id="__span-166-3"><a id="__codelineno-166-3" name="__codelineno-166-3" href="#__codelineno-166-3"></a><span class="na">DefaultDependencies</span><span class="o">=</span><span class="s">no</span>
</span><span id="__span-166-4"><a id="__codelineno-166-4" name="__codelineno-166-4" href="#__codelineno-166-4"></a><span class="na">After</span><span class="o">=</span><span class="s">local-fs.target</span>
</span><span id="__span-166-5"><a id="__codelineno-166-5" name="__codelineno-166-5" href="#__codelineno-166-5"></a>
</span><span id="__span-166-6"><a id="__codelineno-166-6" name="__codelineno-166-6" href="#__codelineno-166-6"></a><span class="k">[Service]</span>
</span><span id="__span-166-7"><a id="__codelineno-166-7" name="__codelineno-166-7" href="#__codelineno-166-7"></a><span class="na">ExecStart</span><span class="o">=</span><span class="s">/usr/bin/fbi -d /dev/fb0 --noverbose -a /opt/splash.png</span>
</span><span id="__span-166-8"><a id="__codelineno-166-8" name="__codelineno-166-8" href="#__codelineno-166-8"></a><span class="na">StandardInput</span><span class="o">=</span><span class="s">tty</span>
</span><span id="__span-166-9"><a id="__codelineno-166-9" name="__codelineno-166-9" href="#__codelineno-166-9"></a><span class="na">StandardOutput</span><span class="o">=</span><span class="s">tty</span>
</span><span id="__span-166-10"><a id="__codelineno-166-10" name="__codelineno-166-10" href="#__codelineno-166-10"></a>
</span><span id="__span-166-11"><a id="__codelineno-166-11" name="__codelineno-166-11" href="#__codelineno-166-11"></a><span class="k">[Install]</span>
</span><span id="__span-166-12"><a id="__codelineno-166-12" name="__codelineno-166-12" href="#__codelineno-166-12"></a><span class="na">WantedBy</span><span class="o">=</span><span class="s">sysinit.target</span>
</span></code></pre></div>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-167-1"><a id="__codelineno-167-1" name="__codelineno-167-1" href="#__codelineno-167-1"></a>sudo<span class="w"> </span>systemctl<span class="w"> </span><span class="nb">enable</span><span class="w"> </span>splashscreen
</span></code></pre></div>
<h3 id="testing-checklist">Testing Checklist<a class="headerlink" href="#testing-checklist" title="Permalink">&para;</a></h3>
<h4 id="power-button">Power Button<a class="headerlink" href="#power-button" title="Permalink">&para;</a></h4>
<div class="language-text highlight"><pre><span></span><code><span id="__span-168-1"><a id="__codelineno-168-1" name="__codelineno-168-1" href="#__codelineno-168-1"></a>PASS    - Press power button, verify no shutdown
</span><span id="__span-168-2"><a id="__codelineno-168-2" name="__codelineno-168-2" href="#__codelineno-168-2"></a>PASS    - Long press power button, verify no shutdown/reboot
</span></code></pre></div>
<h4 id="profilessettings-passfail-on-pi4pi5">Profiles/Settings (Pass/Fail on Pi4/Pi5)<a class="headerlink" href="#profilessettings-passfail-on-pi4pi5" title="Permalink">&para;</a></h4>
<div class="language-text highlight"><pre><span></span><code><span id="__span-169-1"><a id="__codelineno-169-1" name="__codelineno-169-1" href="#__codelineno-169-1"></a>F4P5    - Set to MP4 profile, verify video plays with sound
</span><span id="__span-169-2"><a id="__codelineno-169-2" name="__codelineno-169-2" href="#__codelineno-169-2"></a>         - Sound issues on Pi4: [Reference](https://www.omglinux.com/raspberry-pi-os-bookworm/)
</span><span id="__span-169-3"><a id="__codelineno-169-3" name="__codelineno-169-3" href="#__codelineno-169-3"></a>PASS    - Set to GIF profile, verify functionality
</span><span id="__span-169-4"><a id="__codelineno-169-4" name="__codelineno-169-4" href="#__codelineno-169-4"></a>PASS    - Set bluetooth enabled, connect scanner, verify operation
</span><span id="__span-169-5"><a id="__codelineno-169-5" name="__codelineno-169-5" href="#__codelineno-169-5"></a>F4P5    - Autodetect 720p screen (1360x768 on test device)
</span><span id="__span-169-6"><a id="__codelineno-169-6" name="__codelineno-169-6" href="#__codelineno-169-6"></a>FAIL    - Set to 720p resolution
</span></code></pre></div>
<h4 id="connectivity">Connectivity<a class="headerlink" href="#connectivity" title="Permalink">&para;</a></h4>
<div class="language-text highlight"><pre><span></span><code><span id="__span-170-1"><a id="__codelineno-170-1" name="__codelineno-170-1" href="#__codelineno-170-1"></a>PASS    - Connect to non-NAC ethernet, verify check-in
</span><span id="__span-170-2"><a id="__codelineno-170-2" name="__codelineno-170-2" href="#__codelineno-170-2"></a>P4?5    - Connect to IoT WiFi, verify check-in
</span><span id="__span-170-3"><a id="__codelineno-170-3" name="__codelineno-170-3" href="#__codelineno-170-3"></a>?4P5    - SSH from laptop to Pi (expect failure)
</span><span id="__span-170-4"><a id="__codelineno-170-4" name="__codelineno-170-4" href="#__codelineno-170-4"></a>?4P5    - SSH from slicer to Pi (expect success)
</span><span id="__span-170-5"><a id="__codelineno-170-5" name="__codelineno-170-5" href="#__codelineno-170-5"></a>?4F5    - WiFi reporting from scan data shows correct SSID
</span></code></pre></div>
<h4 id="bluetooth">Bluetooth<a class="headerlink" href="#bluetooth" title="Permalink">&para;</a></h4>
<div class="language-text highlight"><pre><span></span><code><span id="__span-171-1"><a id="__codelineno-171-1" name="__codelineno-171-1" href="#__codelineno-171-1"></a>FAIL    - Connect scanner and scan test barcode
</span></code></pre></div>
<h3 id="bookworm-specific-updates">Bookworm-Specific Updates<a class="headerlink" href="#bookworm-specific-updates" title="Permalink">&para;</a></h3>
<h4 id="hdmi-configuration">HDMI Configuration<a class="headerlink" href="#hdmi-configuration" title="Permalink">&para;</a></h4>
<p>(2024.04.22)</p>
<p><a href="https://raspberrypi.stackexchange.com/questions/144876/forcing-hdmi-output-on-bookworm">Reference: HDMI Output Configuration</a></p>
<p><a href="https://www.raspberrypi.com/documentation/computers/config_txt.html#hdmi-mode">Reference: HDMI Mode Documentation</a></p>
<div class="admonition info">
<p class="admonition-title">Pi5 Hardware Decoding</p>
<p>The Raspberry Pi 5 includes H.265 (HEVC) hardware decoding enabled by default. A hardware codec licence key is not needed.</p>
</div>
<h4 id="audio-configuration">Audio Configuration<a class="headerlink" href="#audio-configuration" title="Permalink">&para;</a></h4>
<p>(2024.05.09)</p>
<h4 id="pi4-hdmi-audio-issues">Pi4 HDMI Audio Issues<a class="headerlink" href="#pi4-hdmi-audio-issues" title="Permalink">&para;</a></h4>
<p><a href="https://github.com/raspberrypi/bookworm-feedback/issues/233">Reference: Bookworm Feedback</a></p>
<p><a href="https://dietpi.com/forum/t/raspberry-pi-5-bookworm-aplay-l-no-soundcards-found/19760">Reference: DietPi Forum</a></p>
<div class="language-text highlight"><pre><span></span><code><span id="__span-172-1"><a id="__codelineno-172-1" name="__codelineno-172-1" href="#__codelineno-172-1"></a>cat /proc/asound/cards
</span></code></pre></div>
<h4 id="audio-backend-options">Audio Backend Options<a class="headerlink" href="#audio-backend-options" title="Permalink">&para;</a></h4>
<p><a href="https://www.raspberrypi.com/documentation/computers/configuration.html">Reference: Audio Configuration</a></p>
<div class="admonition info">
<p class="admonition-title">Audio Backend</p>
<p>Use this option to switch between PulseAudio and PipeWire backends. PipeWire was introduced in Bookworm.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Not yet answered</p>
<p><a href="https://forums.raspberrypi.com/viewtopic.php?t=365650">Raspberry Pi: Forum Topic</a></p>
</div>
<h4 id="troubleshooting-steps">Troubleshooting Steps<a class="headerlink" href="#troubleshooting-steps" title="Permalink">&para;</a></h4>
<ol>
<li>
<p><strong>Check Audio Devices</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-173-1"><a id="__codelineno-173-1" name="__codelineno-173-1" href="#__codelineno-173-1"></a>cat<span class="w"> </span>/proc/asound/cards
</span></code></pre></div></p>
</li>
<li>
<p><strong>System Information</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-174-1"><a id="__codelineno-174-1" name="__codelineno-174-1" href="#__codelineno-174-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>inxi<span class="w"> </span>-y
</span><span id="__span-174-2"><a id="__codelineno-174-2" name="__codelineno-174-2" href="#__codelineno-174-2"></a>inxi<span class="w"> </span>-ACSxxz
</span></code></pre></div></p>
</li>
<li>
<p><strong>Config.txt Settings</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-175-1"><a id="__codelineno-175-1" name="__codelineno-175-1" href="#__codelineno-175-1"></a><span class="c1"># Required settings</span>
</span><span id="__span-175-2"><a id="__codelineno-175-2" name="__codelineno-175-2" href="#__codelineno-175-2"></a><span class="nv">dtparam</span><span class="o">=</span><span class="nv">audio</span><span class="o">=</span>on
</span><span id="__span-175-3"><a id="__codelineno-175-3" name="__codelineno-175-3" href="#__codelineno-175-3"></a><span class="nv">disable_overscan</span><span class="o">=</span><span class="m">1</span>
</span><span id="__span-175-4"><a id="__codelineno-175-4" name="__codelineno-175-4" href="#__codelineno-175-4"></a><span class="nv">hdmi_force_hotplug</span><span class="o">=</span><span class="m">1</span>
</span><span id="__span-175-5"><a id="__codelineno-175-5" name="__codelineno-175-5" href="#__codelineno-175-5"></a><span class="nv">hdmi_drive</span><span class="o">=</span><span class="m">2</span>
</span></code></pre></div></p>
</li>
<li>
<p><strong>Pi4 Specific Settings</strong>
   <div class="language-bash highlight"><pre><span></span><code><span id="__span-176-1"><a id="__codelineno-176-1" name="__codelineno-176-1" href="#__codelineno-176-1"></a><span class="nv">dtoverlay</span><span class="o">=</span>vc4-fkms-v3d
</span><span id="__span-176-2"><a id="__codelineno-176-2" name="__codelineno-176-2" href="#__codelineno-176-2"></a><span class="nv">max_framebuffers</span><span class="o">=</span><span class="m">2</span>
</span></code></pre></div></p>
</li>
<li>
<p><strong>Audio Control</strong></p>
<p><a href="https://askubuntu.com/questions/1129013/speakers-not-working-sunrise-point-lp-hd-audio">Reference: Speaker Troubleshooting</a></p>
<div class="language-bash highlight"><pre><span></span><code><span id="__span-177-1"><a id="__codelineno-177-1" name="__codelineno-177-1" href="#__codelineno-177-1"></a>sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>pavucontrol
</span><span id="__span-177-2"><a id="__codelineno-177-2" name="__codelineno-177-2" href="#__codelineno-177-2"></a>pavucontrol
</span></code></pre></div>
<p>Ensure auto-mute is disabled.</p>
</li>
</ol>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
    
      
      <nav class="md-footer__inner md-grid" aria-label="Footer" >
        
          
          <a href="../../getting-started/servers/gcp/old-server-docs/" class="md-footer__link md-footer__link--prev" aria-label="Previous: Old Server Documentation">
            <div class="md-footer__button md-icon">
              
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256l137.3-137.4c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>
            </div>
            <div class="md-footer__title">
              <span class="md-footer__direction">
                Previous
              </span>
              <div class="md-ellipsis">
                Old Server Documentation
              </div>
            </div>
          </a>
        
        
          
          <a href="../sd-image-building-qcam/" class="md-footer__link md-footer__link--next" aria-label="Next: SD Image Building QCAM (Deprecated)">
            <div class="md-footer__title">
              <span class="md-footer__direction">
                Next
              </span>
              <div class="md-ellipsis">
                SD Image Building QCAM (Deprecated)
              </div>
            </div>
            <div class="md-footer__button md-icon">
              
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M278.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-160 160c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L210.7 256 73.4 118.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l160 160z"/></svg>
            </div>
          </a>
        
      </nav>
    
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
      <div class="md-progress" data-md-component="progress" role="progressbar"></div>
    
    
    <script id="__config" type="application/json">{"base": "../..", "features": ["content.code.copy", "content.code.select", "content.code.annotate", "navigation.footer", "search.suggest", "search.highlight", "navigation.breadcrumbs", "navigation.instant", "navigation.instant.prefetch", "navigation.instant.progress", "navigation.tracking", "navigation.tabs", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top"], "search": "../../assets/javascripts/workers/search.f8cc74c7.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.c8b220af.min.js"></script>
      
    
  </body>
</html>