{"config": {"lang": ["en"], "separator": "[\\s\\-]+", "pipeline": ["stop<PERSON>ordFilter"]}, "docs": [{"location": "", "title": "Slicer Documentation", "text": "<p>Welcome to the Slicer Documentation! This guide serves as a central knowledge base for developers and users working on the Slicer project. Here, you'll find detailed information about system configurations, usage instructions, and development processes.</p>"}, {"location": "#quick-links", "title": "Quick Links", "text": "DocumentationTechnicalProject Info <ul> <li>System Overview</li> <li>Architecture</li> <li>Disaster Recovery Plan</li> <li>Standard Operating Procedures</li> <li>Backup and Recovery</li> </ul> <ul> <li>Communication from the Pi</li> <li>Runtime Layout</li> <li>Pi Services</li> <li>GCP Services</li> <li>Pi Services Versioning</li> </ul> <ul> <li>Authors</li> <li>Acknowledgments</li> </ul>"}, {"location": "#system-overview", "title": "System Overview", "text": "<p>Project Overview</p> <p>The Slicer Project is designed to run on Raspberry Pi, supporting automated processing and data management functionalities. This documentation consolidates all relevant project details, making it easier for developers and maintainers to navigate.</p>"}, {"location": "#key-features", "title": "Key Features", "text": "<ul> <li>[x] Fully documented setup and configuration</li> <li>[x] Branch and version tracking</li> <li>[x] Guidelines for deployment and maintenance</li> <li>[x] Raspberry Pi compatibility</li> <li>[x] User-friendly interface for seamless interaction</li> <li>[x] Robust API for integration with other services</li> </ul>"}, {"location": "#built-with", "title": "Built With", "text": ""}, {"location": "#architecture", "title": "Architecture", "text": "<p>System Architecture</p> Item Application Name Slicer Application Status Standard Criticality[^A1] Operational RTO[^A2] Tier 4 (120+ Hours) RPO[^A3] Data Recovery Not Required Landscape[^A4] GCP Primary[^A5] us-central1-c Alternate[^A6] (none yet) Project <code>mac-mgmt-pr</code> Primary Server <code>lpec5009slicr01</code> Boot disk 31 GB, standard lpec5009slicr01-boot Data Disk 60 GB, SSD lpec5009slicr01-disk-lpec5009slicr01-d DNS <code>slicer.cardinalhealth.net</code> IP address <code>***********</code> Certificate ???"}, {"location": "#disaster-recovery-plan", "title": "Disaster Recovery Plan", "text": "<p>Recovery Procedures</p> <p>In the event of GCP disk loss, follow these procedures to ensure data integrity and system restoration:</p> <ol> <li>Use the latest one-hour disk snapshot to set up a new instance</li> <li>Verify system integrity and ensure all necessary services are running</li> <li>Restore any additional configurations and data from backups if required</li> <li>Conduct a system test to confirm full functionality</li> </ol>"}, {"location": "#sop", "title": "SOP", "text": "<p>Standard Operating Procedures</p> <p>There is no particular maintenance action(s) required, in order to provide day-to-day operation.</p>"}, {"location": "#backup-and-recovery", "title": "Backup and Recovery", "text": ""}, {"location": "#backup-strategy", "title": "Backup Strategy", "text": "<p>Backup Methods</p> <ul> <li>Automated Hourly Snapshots: Backups are set up in GCP to create system snapshots every hour</li> <li>Daily Database Backups: Critical databases are backed up daily and stored securely</li> <li>Weekly Full System Snapshots: A complete system backup is taken weekly to ensure redundancy</li> <li>Offsite Backup Storage: Backups are securely stored in a separate GCP region for disaster recovery</li> <li>Incremental Backups: Only changed data is backed up to optimize storage and speed</li> </ul>"}, {"location": "#recovery-procedure", "title": "Recovery Procedure", "text": "<p>Recovery Steps</p> <ol> <li>Identify the Latest Snapshot – Determine the most recent viable backup</li> <li>Rebuild from Snapshot – Use the latest one-hour snapshot to set up a new instance</li> <li>Verify System Integrity – Ensure all necessary services are running properly</li> <li>Restore Additional Configurations – Apply any additional configuration or database backups if required</li> <li>Perform System Testing – Conduct a full system test to confirm functionality and data consistency</li> <li>Deploy Fixes if Necessary – Address any detected issues before bringing the system live</li> </ol> <p>Regular recovery drills are conducted to ensure a smooth and reliable restoration process.</p>"}, {"location": "#communication-from-the-pi", "title": "Communication from the Pi", "text": "<p>Communication Methods</p> Interface User pi_runner Method Chromium Browser requests library Options Bookmark List N/A Restriction Whitelist Proxy N/A Corp Network N/A N/A Destination Any whitelisted URL slicer.cardinalhealth.net"}, {"location": "#runtime-layout", "title": "Runtime Layout", "text": "<p>System Components</p>"}, {"location": "#deployment-overview", "title": "Deployment Overview", "text": "Category Server (Slicer) Client Data Location <code>/mnt/disks/SSD/var/log/slicer</code><code>/var/log/slicer</code> <code>/cardinal/</code> Code Location <code>/var/www/html/</code> <code>/cardinal/</code> Deployed Files <code>*.py</code> <code>pi_*.py</code> Repository Files <code>slicer_wsgi_*.py</code> <code>pi_*.py</code> Programming Language <code>Python</code> <code>Python 3</code> Runtime <code>WSGI</code> <code>(Custom Wrapper)</code> Static Files <code>/var/www/htmlfiles/</code> <code>N/A</code> Application <code>Apache</code> <code>Shell</code> Startup Method <code>SystemD</code> <code>SystemD</code> Operating System <code>CentOS</code> <code>Raspbian</code> Location <code>GCP</code> <code>Raspberry Pi</code>"}, {"location": "#pi-services", "title": "Pi Services", "text": "<p>Image Building</p> <p>The information needed to build a new full image is available in the Image Building Documentation.</p> <p>Files named as <code>pi_*.py</code> fully describe a service to be run on the remote Pi device.</p>"}, {"location": "#gcp-services", "title": "GCP Services", "text": "<p>Server Information</p> <p>This is the code that runs on a central server, managing remote devices. It is set up on a Production GCP server.</p> <p>Refer to the GCP Servers Documentation file for further details.</p>"}, {"location": "#key-functionalities", "title": "Key Functionalities", "text": "<ol> <li>Remote Data Collection – Gathers current IP addresses from connected devices</li> <li>Device Status Reporting – Displays real-time device status in a user-friendly table</li> <li>Configuration Management – Provides bookmarks and whitelist configurations to devices</li> <li>Pi Service Updates – Manages and distributes updates for Pi services</li> </ol>"}, {"location": "#deployed-files", "title": "Deployed Files", "text": "<p>Files beginning with <code>slicer_wsgi_*.py</code> are deployed to the server at <code>/var/www/html/*.py</code>. Additionally, a corresponding Apache configuration file is created at <code>/etc/httpd/conf.d/python-*.conf</code>.</p> <p>Example deployed files:</p> <ul> <li><code>slicer_wsgi_checkin.py</code> – Handles check-ins from field devices</li> <li><code>slicer_wsgi_reports.py</code> – Generates reports based on check-in data</li> </ul> <p>Access Point</p> <p>Access the GCP-hosted Slicer services at: https://slicer.cardinalhealth.net</p>"}, {"location": "#pi-services-versioning-on-slicer", "title": "Pi Services Versioning on Slicer", "text": "<p>Version Structure</p> <p>The <code>pi_(service)</code> files are loaded onto the Slicer server to be served out to the Raspberry Pi devices.</p>"}, {"location": "#storage-structure", "title": "Storage Structure", "text": "<pre><code>/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py\n/var/www/html/pi_services/pi_runner/R.1.1/pi_runner.py\n</code></pre> <p>Each version (<code>R.x.x</code>) corresponds to a different release of the service, ensuring that multiple versions can be maintained and deployed as needed.</p>"}, {"location": "#authors", "title": "Authors", "text": "<p>Contributors</p> <ul> <li><PERSON> – Lead Developer</li> <li><PERSON> – Developer</li> </ul>"}, {"location": "#acknowledgments", "title": "Acknowledgments", "text": ""}, {"location": "#markdown-resources", "title": "Markdown Resources", "text": "<ul> <li>Markdown Guide</li> <li>Basic Writing and Formatting Syntax</li> <li>Mastering Markdown</li> </ul>"}, {"location": "#live-edit-of-markdown-content", "title": "Live Edit of Markdown Content", "text": "<p>Edit Markdown content live using: - StackEdit</p> <p>[^A1]: What is the criticality of this business process, application, or infrastructure component?     Essential: Business has defined these processes, systems, and data as crucial for operating the business. Cardinal Health will likely cease to exist or experience major operational impacts if these first-tier processes, systems, and data are unavailable or compromised. These systems must be recovered within the first 24 hours of a disaster, cyber event, or outage.     Mission: Business has defined these processes, systems, or data as mission-critical to support the business. There will be significant, but not existential harm to employee productivity, Cardinal Health reputation, revenue, and customer impact if these second-tier systems are unavailable or compromised. These processes, systems, and data must be recovered within the first 24 - 48 hours of a disaster or outage.     Business: Business has defined these processes, systems, or data as critical to support the business. These systems are critical to day-to-day operations but will not cause existential harm to Cardinal Health, patients, customers, or employees if they are unavailable for 48 hours. These systems do need to be recovered within 48 – 120 hours following a disaster.     Operational: Processes, systems, or data defined as required for day-to-day operations of the business but not critical in the event of a disaster or other major system outage/event. There will be a reduction in organizational efficiency but otherwise limited impact on Cardinal Health if these fourth-tier processes, systems, or data are unavailable or compromised.</p> <p>[^A2]: What is the Recovery Time Objective (RTO) for this application/data/process?     This is the timeframe in which the business expects this application/data/process to be recovered following a disaster (this is the timeframe before a financial, operational, and/or legal impact is expected).     Tier 1 (0 - 24 Hours)     Tier 2 (24 - 48 Hours)     Tier 3 (48 - 120 Hours)     Tier 4 (120+ Hours)     DR Not Required Needs Assessed Incubate Status</p> <p>[^A3]: What is the maximum timeframe amount of data that can be lost due to an unplanned solution outage?     The Recovery Point Objective (RPO) is the maximum allowable amount of data loss the business can tolerate following an outage, business interruption, or disaster. (RPO) describes a point in time to which data must be restored in order to be acceptable to the owner(s) of the processes supported by that data. This is often thought of as the time between the last available backup and the time a disruption could potentially occur. The RPO is established based on tolerance for loss of data or re-entering of data.</p> <p>[^A4]: Where is your application located?     Examples: GCP, AWS, Dublin, Colo, SaaS, Azure, Cardinal Site (Manufacturing/Distribution), Other.</p> <p>[^A5]: What is your Primary Location? (GCP Zone)</p> <p>[^A6]: What is your Alternate Location? (GCP Zone)</p>"}, {"location": "getting-started/generic-config/", "title": "Generic Configuration Guide", "text": "<p>This document outlines the steps for configuring and resetting a system to its default state.</p>"}, {"location": "getting-started/generic-config/#system-cleanup-commands", "title": "System Cleanup Commands", "text": "<pre><code>sudo su\n\n# Remove unnecessary files and stop services\nrm -rf /Downloads\nrm -rf /home/<USER>/*\nrm -rf /cardinal/save_values\n\nsystemctl stop pi-hmi.service\nsystemctl stop pi-runner.service\nsystemctl stop pi-config.service\n\n# Reset organization logo\nrm /cardinal/organization_logo.gif\necho 47494638376101000100910000000000ffffff00000000000021f90409000002002c00000000010001000002024c01003b &gt;/cardinal/organization_logo.xxd\ncat /cardinal/organization_logo.xxd | xxd -p -r &gt;/cardinal/organization_logo.gif\nrm /cardinal/organization_logo.xxd\n\n# Clean up logs\nfind /var/log -type f -regex \".*\\.gz$\" -delete\nfind /var/log -type f -regex \".*\\.[0-9]$\" -delete\nrm /var/log/*\necho 'yes' &gt; /cardinal/needsexpand\necho -n \"0\" &gt; /cardinal/boot_count.txt\necho -n \"0\" &gt; /cardinal/grab_count.txt\ncp /cardinal/browserstart_default /cardinal/browserstart\nrm -rf /cardinal/localhtml/*\nrm -rf /cardinal/log/*\n\n# Remove configuration files\nrm -rf /cardinal/config_*\nrm /cardinal/call_home_locations.txt\nrm /cardinal/wifi_ssid_psk.txt\nrm /cardinal/screen_resolution.txt\nrm /cardinal/screen_zoom.txt\nrm /cardinal/wifi_config.txt\nrm /cardinal/TempCert.p12\nrm /cardinal/wifi_settings.txt\nrm -rf /cardinal/corp_certs\n\n# Reset call home locations\necho \"['']\" &gt;/cardinal/call_home_locations.txt\n</code></pre>"}, {"location": "getting-started/generic-config/#creating-startup-page", "title": "Creating Startup Page", "text": "<pre><code>mkdir /cardinal/localhtml/\necho '&lt;head&gt;&lt;meta http-equiv=\"refresh\" content=\"5\" &gt;&lt;/head&gt;&lt;body&gt;&lt;center&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;table border=\"1\" cellpadding=\"10\"&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Starting up...&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;3 boot ups is the normal sequence for a new image.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Screen may not fill to edges until all boots complete.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;If this screen does not disappear after 10 seconds,&lt;br&gt;then press Alt F4 to reset the screen.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/center&gt;&lt;/body&gt;' &gt; /cardinal/localhtml/index.html\n</code></pre>"}, {"location": "getting-started/generic-config/#system-shutdown", "title": "System Shutdown", "text": "<pre><code>sudo shutdown -h now\n</code></pre>"}, {"location": "getting-started/generic-config/#creating-an-image", "title": "Creating an Image", "text": "<pre><code>Downloads/20221102/slicer_pi_image_2.4.1.SP.36_generic.zip\n</code></pre>"}, {"location": "getting-started/generic-config/#configuring-for-cah", "title": "Configuring for CAH", "text": "<ul> <li>Set the call home location:</li> </ul> <pre><code>config: set the call home to ['https://slicer.cardinalhealth.net']\n</code></pre> <ul> <li>Load <code>pi_organization</code> and start it (Slicer pull).</li> <li>Remove the WiFi config, so that it can use old style Corp and IoT:</li> </ul> <pre><code>rm /cardinal/wifi_config.txt\n</code></pre>"}, {"location": "getting-started/generic-config/#testing-updates", "title": "Testing Updates", "text": "<ul> <li>Load an old image, then pull <code>pi_organization</code> and verify functionality.</li> <li>Pull the latest networking updates and ensure everything is functioning as expected.</li> </ul> <p>End of document.</p>"}, {"location": "getting-started/knowledge/", "title": "Knowledge", "text": "<p>A place to hold the key pieces of information about the project.</p>"}, {"location": "getting-started/knowledge/#repository", "title": "Repository", "text": "<p>Note</p> <p>The <code>README.md</code> file is meant to be viewed on GitHub (.md is Markdown formatting). You can access the repository here: GitHub Repository</p> <p>Login with the CAH GitHub-specific username, such as:</p> <pre><code>cah-david-ferguson\n</code></pre>"}, {"location": "getting-started/knowledge/#google-cloud-commands", "title": "Google Cloud Commands", "text": "<p>To modify metadata for the instance <code>lpec5009slicr04</code> in <code>us-central1-a</code>:</p> Disable OS Login<pre><code>gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=FALSE\n</code></pre> Enable OS Login<pre><code>gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=TRUE\n</code></pre>"}, {"location": "getting-started/knowledge/#apms", "title": "APMs", "text": "Raspberry Pi<pre><code>APM0027587\n</code></pre> Slicer<pre><code>APM0028269\n</code></pre>"}, {"location": "getting-started/knowledge/#filename-prefixes", "title": "Filename Prefixes", "text": "<p>The project follows a structured naming convention for files:</p>"}, {"location": "getting-started/knowledge/#aa-", "title": "<code>AA-*</code>", "text": "<ul> <li>Documentation for developers.</li> <li>Named <code>AA</code> so they appear at the top of the file list.</li> </ul>"}, {"location": "getting-started/knowledge/#config_", "title": "<code>config_*</code>", "text": "<ul> <li>Files needed to configure the project.</li> </ul>"}, {"location": "getting-started/knowledge/#datastore_", "title": "<code>datastore_*</code>", "text": "<ul> <li>Captures of the Slicer datastore content.</li> </ul>"}, {"location": "getting-started/knowledge/#offline_", "title": "<code>offline_*</code>", "text": "<ul> <li>Tools used outside of Pi or Slicer (meant for developer use).</li> </ul>"}, {"location": "getting-started/knowledge/#pi_", "title": "<code>pi_*</code>", "text": "<ul> <li>Source code files loaded onto the Raspberry Pi.</li> <li>See <code>AA-pi-applications.md</code> for details.</li> </ul>"}, {"location": "getting-started/knowledge/#read_", "title": "<code>read_*</code>", "text": "<ul> <li>Text files meant for users to read.</li> </ul>"}, {"location": "getting-started/knowledge/#how_to_", "title": "<code>how_to_*</code>", "text": "<ul> <li>Instructional documents.</li> <li>Uploaded to Slicer for user downloads.</li> </ul>"}, {"location": "getting-started/knowledge/#release_notes_", "title": "<code>release_notes_*</code>", "text": "<ul> <li>Explains changes in new releases.</li> </ul> <p>Example:</p> <pre><code>read_release_notes_slicer_pi.md\n</code></pre> <ul> <li>Contains release notes and definitions of service packs (SP).</li> </ul>"}, {"location": "getting-started/knowledge/#slicer_wsgi_", "title": "<code>slicer_wsgi_*</code>", "text": "<ul> <li>Source code files loaded onto the main Slicer server.</li> </ul> <p>End of document.</p>"}, {"location": "getting-started/marketing/", "title": "Marketing Overview", "text": ""}, {"location": "getting-started/marketing/#what-is-it", "title": "What is it?", "text": "<ul> <li> <p>General Purpose Computer (Can easily run any web page)</p> </li> <li> <p>Small (The size of a credit card and only one inch thick)</p> </li> <li> <p>Low Cost ($100 all in—just add a monitor, keyboard, and mouse)</p> </li> <li> <p>Low Power (Consumes only 5 Watts—about the same as a night light; can run a full shift on a 60 WHr battery)</p> </li> <li> <p>Low Maintenance (No fans, no spinning disks)</p> </li> </ul>"}, {"location": "getting-started/marketing/#what-can-it-do", "title": "What Can it Do?", "text": "<ul> <li> <p>Run any website (Ideal for kiosks, display boards, metrics boards, ERP, VOE)</p> </li> <li> <p>Replace a Desktop</p> </li> <li>Replace a Laptop</li> <li>Replace a Thin Client</li> <li>Interface to sensors and provide central visibility</li> </ul>"}, {"location": "getting-started/marketing/#what-have-we-done", "title": "What Have We Done?", "text": ""}, {"location": "getting-started/marketing/#cardinal-pi-os", "title": "Cardinal PI OS", "text": "<ul> <li>Secure private web browsing as the only function</li> <li>Allow list enforced (Work sites only)</li> <li>Option to auto-launch a site (Display boards)</li> <li>Option to auto-logout (VOE and ERP systems)</li> </ul>"}, {"location": "getting-started/marketing/#slicer", "title": "<PERSON>licer", "text": "<ul> <li>Central Configuration and Management Portal</li> </ul>"}, {"location": "getting-started/marketing/#small-deployment", "title": "Small Deployment", "text": "<ul> <li>Have many of them ready, standing by, for VOE uses.</li> </ul>"}, {"location": "getting-started/marketing/#where-can-it-be", "title": "Where Can it Be?", "text": "<ul> <li>Anywhere on the CAH wired network</li> <li>Where available, on the IoT WiFi network</li> </ul>"}, {"location": "getting-started/marketing/#why-the-pi", "title": "Why the Pi?", "text": "<ul> <li>Lowest cost tech refresh available</li> <li>Regional RCS configurable</li> </ul>"}, {"location": "getting-started/marketing/#where-to-buy-pi", "title": "Where to Buy Pi?", "text": "<p>Now In Stock</p> <p>End of document.</p>"}, {"location": "getting-started/servers/gcp-rocky-server/", "title": "GCP Rocky Server Setup &amp; Configuration", "text": ""}, {"location": "getting-started/servers/gcp-rocky-server/#ad-groups", "title": "AD Groups", "text": ""}, {"location": "getting-started/servers/gcp-rocky-server/#to-be-able-to-create-the-slicer-project-in-gcp", "title": "To be able to create the slicer project in GCP:", "text": "<p><code>A-APM0028269-Slicer-admins</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#to-be-able-to-operate-the-slicer-servers-in-gcp", "title": "To be able to operate the slicer server(s) in GCP:", "text": "<p><code>a-cloud-slicer-pr-editor</code> <code>a-cloud-slicer-pr-owner</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#to-gain-access-to-the-reports-from-bigpandadynatrace", "title": "To gain access to the reports from BigPanda/Dynatrace:", "text": "<p><code>A-OktaSSO-Dynatrace-Users</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#snow-request-to-create-a-new-server", "title": "SNOW Request to Create a New Server", "text": "<p>02 → 2023.01.05 07:55:01 <code>REQ3264966</code></p> <p>03 → 2023.02.10 <code>REQ3310940</code></p> <p><code>https://cardinal.service-now.com/gith/?id=sc_cat_item&amp;sys_id=67bcbe291b450150ca2ba645624bcb62</code></p> <ul> <li>Type: Create VM</li> <li>( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)</li> <li>Image: <code>cah-rocky-8</code></li> <li>GCP Project: <code>mac-mgmt-pr-cah</code></li> <li>Environment: Production</li> <li>Internet facing: No</li> <li>Instance name: <code>slicr03</code></li> <li>(builds instance name of lpec5009slicr03)</li> <li>Zone: <code>us-central1-a</code></li> <li>Service account: <code>pi-mgmt-pr-slicer-main</code></li> <li>Network tags: <code>int-webserver</code></li> <li>AD Groups: <code>A-MacSysAdmins</code> (Must add this before selecting cah-rocky-8)</li> </ul> <p>Machine Family: GENERAL-PURPOSE Series: N1 Machine Type: <code>n1-standard-4</code> Boot Disk Type: <code>pd-standard</code> Boot Disk Size: <code>30</code> Default Disk Type: <code>pd-ssd</code> Name: <code>slicer03-ssd</code> Size: <code>60</code> Additional: No APM ID: <code>APM0022695</code> (not changeable) (this is JamfPro APM, Slicer is APM0028269, Raspberry Pi is APM0027587, Jamf Cloud is 28731) Funded: No Submit</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#servicenow-requests-for-additional-servers", "title": "ServiceNow Requests for Additional Servers", "text": "<p>04 → 2024.01.08 GCP Project Request: <code>https://cardinal.service-now.com/now/nav/ui/classic/params/target/com.glideapp.servicecatalog_cat_item_view.do%3Fv%3D1%26sysparm_id%3Df432c90edb7b9300be676165ca9619dd</code></p> <ul> <li>Looking to do: Create New</li> <li>Is this an EDDIE, ...: No</li> <li>Project Name: slicer</li> <li>Environment: Prod</li> <li>Project ID: <code>slicer-pr-cah</code> (auto populate from choices)</li> <li>Primary Application: Slicer (<code>APM0028269</code>)</li> <li>Contact Manager: <PERSON> (auto populated from application)</li> <li>Cost Center: <code>**********</code> (auto populated from application)</li> <li>Click 'Add to <PERSON><PERSON>' → 'Submit Items'</li> </ul> <p><code>REQ3708042</code></p> <p>After creation, go to the GCP page: <code>https://login.cardinalhealth.net/</code></p> <p>Search and open \"slicer-pr\". If you see an error: You need additional access to the project: slicer-pr To request access, contact your project administrator and provide them a copy of the following information: - Missing permissions:   - <code>compute.instances.list</code> - Troubleshooting URL: <code>console.cloud.google.com/iam-admin/troubleshooter;permissions=compute.instances.list;principal=<EMAIL>;resources=%2F%2Fcloudresourcemanager.googleapis.com%2Fprojects%2Fslicer-pr-cah/result</code></p> <p>To request access: <code>https://cardinal.service-now.com/gith?sys_id=6e98df6a4763715073d32a24836d43e2&amp;view=sp&amp;id=ticket_request&amp;table=sc_req_item</code></p> <p>Post a question: \"How do we resolve this?\"</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#granting-access-to-ad-groups", "title": "Granting Access to AD Groups", "text": "<p>Go to \"Get IT Help\": <code>https://cardinal.service-now.com/gith?id=sc_home</code></p> <p>Click \"Access Request for Active Directory Groups\"</p> <ul> <li>Type of Request: Grant User Access</li> <li>Account Type: Normal</li> <li>Domain: <code>CardinalHealth.net</code></li> <li>Select the Group: <code>a-cloud-slicer-pr-owner</code></li> <li>Click \"Add to Cart\" → Checkout → Submit</li> </ul> <p><code>REQ3712842</code> (Created 2024.01.11)</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#vm-creation-setup", "title": "VM Creation &amp; Setup", "text": "<p>Check the wiki for instructions:</p> <ul> <li>(old) <code>https://wiki.cardinalhealth.net/GCP_Create_VM_Instance</code></li> <li>(current) <code>https://wiki.cardinalhealth.net/GCP_VM_Instance_Requests</code></li> </ul> <p>If prompted to log in, follow: <code>https://cardinal.service-now.com/gith?id=sc_cat_item&amp;table=sc_cat_item&amp;sys_id=5e0450f71be93550473c36ef034bcbec</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#troubleshooting-ssh-access-issues", "title": "🔍 Troubleshooting SSH Access Issues", "text": "<p>If Remote into it via SSH fails, toggle the <code>oslogin</code> metadata: gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=TRUE gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=FALSE</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#mounting-persistent-disk-ssd", "title": "Mounting Persistent Disk (SSD)", "text": "<p>lsblk sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb sudo mkdir -p /mnt/disks/SSD sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD sudo chmod a+w /mnt/disks/SSD</p> <p>Make mount persistent: sudo cp /etc/fstab /etc/fstab.backup sudo blkid /dev/sdb</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#installing-apache-configuring-tls-13", "title": "Installing Apache &amp; Configuring TLS 1.3", "text": "<p>sudo dnf update -y sudo dnf install -y mod_ssl httpd</p> <p>Edit <code>/etc/httpd/conf.d/ssl.conf</code> and add: SSLProtocol +TLSv1.3</p> <p>Restart Apache: sudo systemctl restart httpd</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#apache-tuning-avoid-scoreboard-full-issue", "title": "Apache Tuning (Avoid Scoreboard Full Issue)", "text": "<p>Modify <code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code>:</p> <pre><code>&lt;IfModule mpm_event_module&gt;\n    StartServers             3\n    MinSpareThreads          75\n    MaxSpareThreads          250\n    ThreadLimit              64\n    ThreadsPerChild          25\n    MaxRequestWorkers        400\n    MaxConnectionsPerChild   0\n&lt;/IfModule&gt;\n</code></pre> <p>Restart Apache: sudo apachectl configtest sudo systemctl restart httpd</p>"}, {"location": "getting-started/servers/gcp-rocky-server/#additional-steps", "title": "Additional Steps", "text": ""}, {"location": "getting-started/servers/gcp-rocky-server/#vm-creation-for-slicr04", "title": "VM Creation for slicr04", "text": "<ul> <li>Type: Create VM</li> <li>Image: <code>cah-rocky-9</code></li> <li>GCP Project: <code>slicer-pr-cah</code></li> <li>Environment: Production</li> <li>Internet facing: No</li> <li>Instance name: <code>slicr04</code></li> <li>Zone: <code>us-central1-a</code></li> <li>Service account: <code>slicer-pr-def</code></li> <li>Network tags: <code>int-webserver</code></li> <li>AD Groups: <code>A-APM0028269-Slicer-admins</code> (Must add this before selecting cah-rocky-9)</li> </ul> <p>Machine Family: GENERAL-PURPOSE Series: N1 Machine Type: <code>n1-standard-4</code> Boot Disk Type: <code>pd-standard</code> Boot Disk Size: <code>30</code> Default Disk Size: <code>200</code> Default Disk Name: <code>slicer04-ssd</code> Additional: No APM ID: <code>APM0028269</code> (not changeable) Funded: No Submit Now</p> <p>2024.01.10 <code>REQ3711830</code> <code>RITM5739116</code> (Since there was not an owner at the time of the request, this request was cancelled. Make a new one)</p> <p>2024.01.16 <code>REQ3718032</code> <code>RITM5747141</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#vm-creation-for-slicr05", "title": "VM Creation for slicr05", "text": "<ul> <li>Type: Create VM</li> <li>Image: <code>cah-rocky-9</code></li> <li>GCP Project: <code>slicer-pr-cah</code></li> <li>Environment: Production</li> <li>Internet facing: No</li> <li>Instance name: <code>slicr05</code></li> <li>Zone: <code>us-central1-a</code></li> <li>Service account: <code>slicer-pr-def</code></li> <li>Network tags: <code>int-webserver</code></li> <li>AD Groups: <code>A-APM0028269-Slicer-admins</code> (Must add this before selecting cah-rocky-9)</li> </ul> <p>Machine Family: GENERAL-PURPOSE Series: N1 Machine Type: <code>n1-standard-4</code> Boot Disk Type: <code>pd-standard</code> Boot Disk Size: <code>30</code> Additional: 1 additional Type: <code>pd-ssd</code> Default Disk Size: <code>200</code> Default Disk Name: <code>slicer05-ssd</code> APM ID: <code>APM0028269</code> (not changeable) Funded: No Submit Now</p> <p>2024.03.19 <code>REQ3799382</code> <code>RITM5856652</code></p>"}, {"location": "getting-started/servers/gcp-rocky-server/#setting-root-password-to-never-expire", "title": "Setting Root Password to Never Expire", "text": "<p>Check if the root account is set to expire, and if so, set it to not expire:</p> <pre><code>sudo chage -l root\nsudo chage -l david.ferguson\n\nsudo chage -M -1 root\nsudo chage -M -1 david.ferguson\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#additional-configuration-for-apache", "title": "Additional Configuration for Apache", "text": "<pre><code>sudo setfacl -m \"u:apache:rwx\" \"/usr/local/lib/python3.6/site-packages\"\nsudo chmod 755 /usr/local/lib/python3.6/site-packages -R\nsudo restorecon -R /usr/local/lib/python3.6/site-packages/\nsudo setsebool -P httpd_tmp_exec on\nsudo chmod o+rx /usr/local/lib/python3.6/site-packages -R\nsudo setsebool -P httpd_read_user_content 1\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#installing-htop-on-rocky-linux", "title": "Installing htop on Rocky Linux", "text": "<pre><code>sudo dnf upgrade --refresh\nsudo dnf config-manager --set-enabled crb\nsudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm\nsudo dnf install htop -y\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#dns-configuration", "title": "DNS Configuration", "text": "<p>2023.01.25</p> <ul> <li>What: Add</li> <li>Type: A Record</li> <li>ASAP: Yes</li> <li>Host: slicer2</li> <li>Domain: cardinalhealth.net</li> <li>FQDN: slicer2.cardinalhealth.net</li> <li>IP: ************</li> <li>Internal</li> </ul> <p>Submit: <code>REQ3288460</code></p> <p>2023.01.25</p> <ul> <li>Modify</li> <li>A Record</li> <li>ASAP: Yes</li> <li>Host: slicer2</li> <li>Domain: cardinalhealth.net</li> <li>FQDN: slicer2.cardinalhealth.net</li> <li>OLD: ************</li> <li>NEW: ***********</li> <li>Internal</li> </ul>"}, {"location": "getting-started/servers/gcp-rocky-server/#troubleshooting-apache-lockup", "title": "Troubleshooting Apache Lockup", "text": "<p>2024.04.07</p> <p>Midnight Saturday to Sunday rollover lockup of apache:</p> <pre><code>sudo apachectl configtest\nsystemctl restart httpd\n</code></pre> <p>Modify <code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code>:</p> <pre><code>&lt;IfModule mpm_event_module&gt;\n    StartServers             3\n    MinSpareThreads          75\n    MaxSpareThreads          250\n    ThreadLimit              64\n    ThreadsPerChild          25\n    MaxRequestWorkers        400\n    MaxConnectionsPerChild   0\n&lt;/IfModule&gt;\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#additional-steps-for-content-loading", "title": "Additional Steps for Content Loading", "text": "<ul> <li>Browse to the IP address of the server.</li> <li>Enable Trust by clicking on the \"Not Trusted\" when the index (home) page first loads.</li> <li>Log in as a user that has the admin privilege by being a member of \"A-APM0028269-Slicer-admins\".</li> <li>Click into 'users' page, and give yourself 'loader create', 'dataport_create' permissions.</li> <li>Go back home, see the dataport link, follow it, and then choose the saved datastore_snapshot.txt file.</li> <li>Go back home, see all permissions restored, and shows all allowed modules.</li> <li>Go to 'upload', and upload the file 'read_release_notes_slicer_pi.txt'.</li> <li>Pull down htmlfiles content, and upload to the new server.</li> <li>Pull down the multimedia content for all, and load to new server.</li> <li>Codeupload all the pi content.</li> <li>Download all download content from old server, then \"upload\" to new.</li> </ul>"}, {"location": "getting-started/servers/gcp-rocky-server/#manual-fixes-after-first-loader-install-attempt", "title": "Manual Fixes After First Loader Install Attempt", "text": "<pre><code>sudo setfacl -m \"u:apache:rwx\" \"/usr/local/lib/python3.6/site-packages\"\nsudo chmod 755 /usr/local/lib/python3.6/site-packages -R\nsudo restorecon -R /usr/local/lib/python3.6/site-packages/\nsudo setsebool -P httpd_tmp_exec on\nsudo chmod o+rx /usr/local/lib/python3.6/site-packages -R\nsudo setsebool -P httpd_read_user_content 1\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#dev-setup-for-rocky-linux", "title": "Dev Setup for Rocky Linux", "text": "<p>2022.12.20</p> <ul> <li>Download Rocky Linux 8.7, x86, minimal from <code>https://rockylinux.org/download/</code>.</li> <li>Set up VirtualBox with the following settings:</li> <li>Name: Rocky8</li> <li>Image: Rocky 8 minimal</li> <li>Skip unattended: checked</li> <li>Base memory: 8000 MB</li> <li>Processors: 4</li> <li>Create: 20 GB</li> <li>Network: Bridged Adapter (for CAH remote, must be wired to USB ethernet adapter, to Meraki, and select that for the bridge)</li> <li>Start the VM and follow the installation prompts.</li> <li>Set root password and create a user account.</li> <li>Configure network and host settings.</li> <li>Begin installation and reboot system after completion.</li> <li>Take a snapshot of the VM.</li> </ul>"}, {"location": "getting-started/servers/gcp-rocky-server/#additional-configuration-for-dns", "title": "Additional Configuration for DNS", "text": "<p>2023.01.25</p> <ul> <li>What: Add</li> <li>Type: A Record</li> <li>ASAP: Yes</li> <li>Host: slicer2</li> <li>Domain: cardinalhealth.net</li> <li>FQDN: slicer2.cardinalhealth.net</li> <li>IP: ************</li> <li>Internal</li> </ul> <p>Submit: <code>REQ3288460</code></p> <p>2023.01.25</p> <ul> <li>Modify</li> <li>A Record</li> <li>ASAP: Yes</li> <li>Host: slicer2</li> <li>Domain: cardinalhealth.net</li> <li>FQDN: slicer2.cardinalhealth.net</li> <li>OLD: ************</li> <li>NEW: ***********</li> <li>Internal</li> </ul>"}, {"location": "getting-started/servers/gcp-rocky-server/#installing-htop-on-rocky-linux_1", "title": "Installing htop on Rocky Linux", "text": "<pre><code>sudo dnf upgrade --refresh\nsudo dnf config-manager --set-enabled crb\nsudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm\nsudo dnf install htop -y\n</code></pre>"}, {"location": "getting-started/servers/gcp-rocky-server/#troubleshooting-apache-lockup_1", "title": "Troubleshooting Apache Lockup", "text": "<p>2024.04.07</p> <p>Midnight Saturday to Sunday rollover lockup of apache:</p> <pre><code>sudo apachectl configtest\nsystemctl restart httpd\n</code></pre> <p>Modify <code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code>:</p> <pre><code>&lt;IfModule mpm_event_module&gt;\n    StartServers             3\n    MinSpareThreads          75\n    MaxSpareThreads          250\n    ThreadLimit              64\n    ThreadsPerChild          25\n    MaxRequestWorkers        400\n    MaxConnectionsPerChild   0\n&lt;/IfModule&gt;\n</code></pre> <p>End of document.</p>"}, {"location": "getting-started/servers/gcp/additional-config/", "title": "Additional Config", "text": ""}, {"location": "getting-started/servers/gcp/additional-config/#additional-configurations", "title": "Additional Configurations", "text": "<pre><code>cat /etc/httpd/conf/httpd.conf\ncp\n</code></pre>"}, {"location": "getting-started/servers/gcp/additional-config/#install-python3-and-required-libraries", "title": "Install Python3 and Required Libraries:", "text": "<pre><code>sudo yum update -y\nsudo yum install -y python3\npython3 --version\nsudo yum install traceroute\nsudo yum install sshpass\n</code></pre> <p>Can not see it with the internal address (from any place other than the server itself)</p> <pre><code>ping lpec5009slicr01.c.mac-mgmt-pr-cah.internal\n</code></pre> <p>Firewall Rules for GCP Instances</p> <pre><code>gcloud config set project mac-mgmt-pr-cah\ngcloud deployment-manager deployments list\ngcloud deployment-manager deployments describe lpec5009slicr01\ngcloud compute firewall-rules list --project mac-mgmt-pr-cah --format=\"table(selfLink)\"\n(empty)\n</code></pre> Network tags that provide rules: <p>GCP Create VM Instance</p> <p>Remove the rule called <code>edge</code> also <code>glb</code> also <code>internal-smb</code>, save and reboot (updated the yaml, to be clean for these tags)</p>"}, {"location": "getting-started/servers/gcp/additional-config/#python-on-apache-mod_wsgi", "title": "Python on Apache (mod_wsgi)", "text": "<p>HowToForge Guide</p> <pre><code>sudo yum install -y apache2-utils ssl-cert libapache2-mod-wsgi-py3\n</code></pre> <pre><code>sudo vi /var/www/html/wsgy.py\n</code></pre> Paste the following:<pre><code>import json\n\ndef application(environ, start_response):\n    status = '200 OK'\n    html = '''\n    &lt;html&gt;\n    &lt;body&gt;\n    &lt;div style=\"width: 100%; font-size: 40px; font-weight: bold; text-align: center;\"&gt;\n    Welcome to mod_wsgi Test Page\n    &lt;/div&gt;\n    &lt;/body&gt;\n    &lt;/html&gt;\n    '''\n    response_header = [('Content-type','text/html')]\n    start_response(status, response_header)\n    return str(environ['QUERY_STRING'])\n</code></pre> <pre><code>sudo chown apache:apache /var/www/html/wsgy.py\n</code></pre> <pre><code>vi /etc/apache2/conf-available/wsgi.conf\nsudo vi /etc/httpd/conf.d/python-wsgi.conf\n</code></pre> Paste:<pre><code>WSGIScriptAlias /wsgi /var/www/html/wsgy.py\n</code></pre> <pre><code>sudo systemctl restart httpd\n</code></pre>"}, {"location": "getting-started/servers/gcp/additional-config/#test-wsgi", "title": "Test WSGI", "text": "<ul> <li>Open https://slicer.cardinalhealth.net/wsgi</li> <li>Test with parameters: https://slicer.cardinalhealth.net/wsgi?parametersGoHere</li> </ul>"}, {"location": "getting-started/servers/gcp/additional-config/#monitor-check-in-receiver", "title": "Monitor Check-in Receiver", "text": "<p>Refer to <code>slicer_wsgi_checkin.py</code> for details.</p>"}, {"location": "getting-started/servers/gcp/additional-config/#connection-to-persistent-disk-in-gcp-vm", "title": "Connection to persistent disk in GCP VM", "text": "<p>GCP VM Console</p> Disc created in Cloud Console<pre><code>[david.ferguson2@lpec5009slicr01 html]$ lsblk\nNAME   MAJ:MIN RM  SIZE RO TYPE MOUNTPOINT\nsda      8:0    0   30G  0 disk\n├─sda1   8:1    0  200M  0 part /boot/efi\n└─sda2   8:2    0 29.8G  0 part /\nsdb      8:16   0   60G  0 disk\n</code></pre> <pre><code>sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb\n[y/N]: y\n\nsudo mkdir -p /mnt/disks/SSD\nsudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD\nsudo chmod a+w /mnt/disks/SSD\n\nsudo cp /etc/fstab /etc/fstab.backup\nsudo blkid /dev/sdb\n</code></pre> <pre><code>/dev/sdb: UUID=\"736ba877-71b1-43c5-93e3-e7676892e65b\" TYPE=\"ext4\"\n</code></pre> <pre><code>sudo vi /etc/fstab\nUUID=736ba877-71b1-43c5-93e3-e7676892e65b /mnt/disks/SSD ext4 discard,defaults,nofail 0 2\n\nsudo chmod 755 /mnt\nsudo chmod 755 /mnt/disks\n\nsudo chown -R apache:apache /mnt/disks/SSD\nsudo chmod 777 /mnt/disks/SSD\n\nsudo mkdir /mnt/disks/SSD/var\nsudo chown -R apache:apache /mnt/disks/SSD/var\n</code></pre>"}, {"location": "getting-started/servers/gcp/additional-config/#set-root-password-to-never-expire", "title": "Set root password to never expire", "text": "<pre><code>sudo chage -M -1 root\nsudo chage -M -1 david.ferguson\n</code></pre>"}, {"location": "getting-started/servers/gcp/additional-config/#data-view-and-delete-ncdu", "title": "Data view and delete NCDU", "text": "<p>Install NCDU on Red Hat</p> On Mac:On Slicer Server: <p>NCDU</p> <p>Download the <code>gz</code></p> <p><pre><code>xxd ~/Downloads/ncdu-1.16.tar.gz &gt; ncdu-1.16.tar.gz.xxd\ncat ncdu-1.16.tar.gz.xxd\n</code></pre> (Copy from terminal, paste into text editor, trim off extra lines)</p> <p><pre><code>mkdir /home/<USER>/install_ncdu\ncd /home/<USER>/install_ncdu\nvi ncdu-1.16.tar.gz.xxd\n</code></pre> (Paste contents of the ncdu-1.16.tar.gz.xxd)</p> <pre><code>cat ncdu-1.16.tar.gz.xxd | xxd -r &gt;ncdu-1.16.tar.gz\ntar -xzvf ncdu-1.16.tar.gz\ncd ncdu-1.16\nsudo yum install -y gcc\n./configure --prefix=/usr\nmake\nsudo make install\n</code></pre> <p>Usage:</p> <pre><code>sudo ncdu /\nsudo ncdu /var/log/slicer\n</code></pre> <pre><code>sudo yum clean all\nsudo rm -rf /var/cache/yum\n</code></pre> <p>Log Cleanup (Log is 14.4 GB on 2021.09.27):</p> <pre><code>sudo rm /var/log/httpd/access_log\n</code></pre> Rotate Logs (Rotation was not working since June 2021) <p>Changing Apache Log Rotation Behaviour</p> <pre><code>sudo vi /etc/logrotate.conf\n</code></pre> <p>(Change to only 7 days of logs)</p> NewOld <p>Rotate log files daily <pre><code>daily\n</code></pre></p> <p>Keep 7 days worth of backlogs <pre><code>rotate 7\n</code></pre></p> <p>Rotate log files weekly <pre><code>weekly\n</code></pre></p> <p>Keep 4 weeks worth of backlogs <pre><code>rotate 4\n</code></pre></p> Run Rotation<pre><code>sudo logrotate --force /etc/logrotate.conf\n</code></pre> <pre><code>sudo cat /var/log/cron\n</code></pre>"}, {"location": "getting-started/servers/gcp/additional-config/#user-access-and-password-expiry-check", "title": "User Access and Password Expiry Check", "text": "<p>Shows that June 6 was the last clean run, which coincides with login failures and the need to create a new persona to log into gcloud for <PERSON><PERSON><PERSON>.</p> <pre><code>sudo chage -l root\nsudo passwd root\n</code></pre> Root Password Expiry Details <pre><code>[david.ferguson2@lpec5009slicr01 ~]$ sudo chage -l root\nLast password change                : Mar 11, 2021\nPassword expires                     : Jun 09, 2021\nPassword inactive                     : never\nAccount expires                       : never\nMinimum number of days between password change  : 0\nMaximum number of days between password change  : 90\nNumber of days of warning before password expires   : 7\n</code></pre> <p>Jamf Password Expiry Details</p> <pre><code>[david.ferguson2@lpec5008jamfa01 ~]$ sudo chage -l root\n</code></pre> Jamf Password Expiry Details <pre><code>Last password change                : Sep 20, 2021\nPassword expires                     : never\nPassword inactive                     : never\nAccount expires                       : never\nMinimum number of days between password change  : 0\nMaximum number of days between password change  : -1\nNumber of days of warning before password expires   : 7\n</code></pre>"}, {"location": "getting-started/servers/gcp/apache/", "title": "Apache", "text": ""}, {"location": "getting-started/servers/gcp/apache/#install-apache", "title": "Install Apache", "text": "<p>DigitalOcean Guide: How to Install Apache on CentOS 7</p> Update and Install Apache<pre><code>sudo yum update httpd\nsudo yum install httpd\n</code></pre> Enable and Start Apache<pre><code>sudo systemctl enable httpd\nsudo systemctl start httpd\nsudo systemctl status httpd\n</code></pre> Check Default Configuration<pre><code>cat /etc/httpd/conf.d/welcome.conf\n</code></pre> Set Up a Test Page<pre><code>sudo su\necho \"Slicer Server\" &gt; /var/www/html/index.html\nchmod 644 /var/www/html/index.html\nls -l /var/www/html/index.html\nexit\n</code></pre> <p>Access the server:</p> <ul> <li><code>http://***********</code></li> <li><code>https://***********</code></li> </ul>"}, {"location": "getting-started/servers/gcp/apache/#configure-wsgi-modules", "title": "Configure WSGI Modules", "text": "<p>Load up the <code>slicer_wsgi_*</code> modules.</p> <p>To prioritize the index wsgi page, update Apache's configuration:</p> Edit Apache Configuration<pre><code>sudo vi /etc/httpd/conf/httpd.conf\n</code></pre> <p>Modify the DirectoryIndex section to look like this:</p> <pre><code>#\n# DirectoryIndex: sets the file that Apache will serve if a directory\n# is requested.\n#\n&lt;IfModule dir_module&gt;\n    DirectoryIndex index index.html\n&lt;/IfModule&gt;\n</code></pre> Restart Apache<pre><code>sudo systemctl restart httpd\n</code></pre>"}, {"location": "getting-started/servers/gcp/bucket-storage/", "title": "Bucket Storage", "text": ""}, {"location": "getting-started/servers/gcp/bucket-storage/#connection-to-bucket-storage-in-gcp", "title": "Connection to Bucket Storage in GCP", "text": "<p>Google Cloud Storage Guide</p> <p>Step 1: Initialize GCloud on Slicer Server</p> <pre><code>gcloud init\n</code></pre> <p>Step 2: Select Service Account</p> <pre><code># Choose the correct service account for bucket access\n<EMAIL>\n</code></pre> <p>Step 3: Verify Connection</p> <pre><code>gcloud compute instances list\ngcloud auth list\ngcloud storage buckets list\n</code></pre> <p>This ensures secure storage access for Slicer in Google Cloud Platform.</p> <p>GCS Buckets</p>"}, {"location": "getting-started/servers/gcp/bucket-storage/#firestore-setup", "title": "Firestore Setup", "text": "<p>You must have these permissions in GCP to see the interface in GCP:</p> <ul> <li><code>appengine.applications.get</code></li> <li><code>datastore.entities.get</code></li> <li><code>datastore.entities.list</code></li> </ul>"}, {"location": "getting-started/servers/gcp/bucket-storage/#things-that-do-not-work-yet", "title": "Things that do not work yet", "text": "<p>One-liner create fails, but provides useful information As of 2021.03.26, this gets us a small CentOS7 server in np (non-production):</p>"}, {"location": "getting-started/servers/gcp/bucket-storage/#point-to-the-correct-project", "title": "Point to the correct project", "text": "<pre><code>gcloud config set project mac-mgmt-np-cah\n</code></pre>"}, {"location": "getting-started/servers/gcp/bucket-storage/#create-the-vm-with-command-line-no-yaml", "title": "Create the VM with command line (no yaml)", "text": "<pre><code>gcloud deployment-manager deployments create ldec5009slicr01 --composite-type management-cah/composite:vm --properties name:ldec5009slicr01,region:'us-central1' --project mac-mgmt-np-cah\n</code></pre> <ul> <li>This creates the instance, but it cannot be accessed via SSH.</li> <li>However, it does show the service account in the GUI:</li> </ul> <pre><code><EMAIL>\n</code></pre>"}, {"location": "getting-started/servers/gcp/bucket-storage/#check-vm-details", "title": "Check VM details", "text": "<pre><code>gcloud deployment-manager deployments describe ldec5009slicr01\n</code></pre>"}, {"location": "getting-started/servers/gcp/bucket-storage/#to-remove-the-instance", "title": "To remove the instance", "text": "<p>Use caution before deleting deployments</p> <p>The following command is commented out to prevent accidental deletion. Uncomment and execute if removal is necessary.</p> <pre><code># gcloud deployment-manager deployments delete ldec5009slicr01\n# gcloud deployment-manager deployments delete lpec5009slicr01\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/", "title": "Certificates", "text": ""}, {"location": "getting-started/servers/gcp/certificates/#self-signed-tls-certificate-for-initial-configuration", "title": "Self-Signed TLS Certificate for Initial Configuration", "text": "<p>Linode Guide: Create a Self-Signed TLS Certificate</p> <p>Unix SE: Trust Self-Signed Certificates in cURL</p> Generate Self-Signed Certificate<pre><code>sudo su\ncd ~\nmkdir /root/certs &amp;&amp; cd /root/certs\nopenssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out slicer.crt -keyout slicer.key -addext \"basicConstraints=critical,CA:TRUE,pathlen:1\"\n</code></pre> <p>Use the following answers for the certificate prompt:</p> <pre><code>Country Name (2 letter code) [US]: US\nState or Province Name (full name) [Ohio]: Ohio\nLocality Name (eg, city) [Dublin]: Dublin\nOrganization Name (eg, company) [CardinalHealth]: CardinalHealth\nOrganizational Unit Name (eg, section) [ClientEngineering]: ClientEngineering\nCommon Name (e.g. server FQDN or YOUR name) [slicer.cardinalhealth.net]: slicer.cardinalhealth.net\nEmail Address [<EMAIL>]: <EMAIL>\n</code></pre> Install Additional Certificate Tools<pre><code>sudo yum install gnutls-utils\n</code></pre> Verify Certificate<pre><code>certtool -i &lt; slicer.crt\n</code></pre> Generate Additional Certificates<pre><code>certtool -p --outfile localhost.key\ncerttool -s --load-privkey localhost.key --outfile localhost.crt\n</code></pre> <p>Use the same certificate details as before.</p> Move Certificates to Secure Location<pre><code>cp localhost.crt /etc/pki/tls/private/slicer.cert\ncp localhost.key /etc/pki/tls/private/slicer.key\n</code></pre> Exit Root User<pre><code>exit\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#certificate", "title": "Certificate", "text": "<p>ServiceNow Cert Request</p> Property Details Project Standard Name https://slicer.cardinalhealth.net Server Type Unix Hosting Server GCP <code>lpec5009slicr01</code> in project <code>mac-mgmt-pr-cah</code> IP <code>***********</code> Audience Internal Environment Production Application Slicer Owner Russell Lobuzzetta Group Mailbox GMB-EIT-RaspberryPi Port 443"}, {"location": "getting-started/servers/gcp/certificates/#when-cert-is-issued", "title": "When Cert is Issued:", "text": "<p>SSL Certificate Installation Guide</p> <p>Cert arrived as a compressed file \"slicercardinalhealthnet.7z\", with a password sent in a separate email.</p> <p>To extract on Mac:</p> <ul> <li>Install \"The Unarchiver\" or \"Extractor - Unarchive Files\" from the App Store.   <code>(one of them worked, I forget which; once I got a clean file and password)</code></li> </ul> <p>To prepare the Cert on Mac</p> <p>In terminal on your Mac, go to where the pfx file is unzipped: <pre><code>cd /Users/<USER>/Documents/slicerCert\nxxd slicercardinalhealthnet.pfx\n</code></pre> Copy all text output, paste into a text document, and clean up the start and end to remove prompt lines.</p>"}, {"location": "getting-started/servers/gcp/certificates/#on-slicer-server", "title": "On Slicer Server:", "text": "<pre><code>sudo su\nmkdir /etc/pki/tls/private/20210404\ncd /etc/pki/tls/private/20210404\nvi slicercardinalhealthnet.txt\n</code></pre> Insert the cleaned-up content<pre><code>cat slicercardinalhealthnet.txt | xxd -r &gt;slicercardinalhealthnet.pfx\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#convert-pfx-to-cert-key-files", "title": "Convert PFX to Cert &amp; Key Files:", "text": "<p>Apache SSL Setup Converting PFX Certificate with Apache SSL</p> <p><pre><code>openssl pkcs12 -info -in slicercardinalhealthnet.pfx\n</code></pre> <pre><code>openssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert\n</code></pre> Use the password that was supplied in the email for this cert</p> <p><pre><code>openssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes -out slicer.key\n</code></pre> Use the password that was supplied in the email for this cert</p> <pre><code>openssl rsa -in slicer.key -outform PEM -out slicer_pem.key\n</code></pre> <p><pre><code>openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem\n</code></pre> Use the password that was supplied in the email for this cert</p> <p><pre><code>openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -nodes -out domain-ca.crt\n</code></pre> Use the password that was supplied in the email for this cert</p>"}, {"location": "getting-started/servers/gcp/certificates/#move-certificates-to-secure-location", "title": "Move Certificates to Secure Location:", "text": "<pre><code>cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt\ncp slicer.cert /etc/pki/tls/private/slicer.cert\ncp slicer.key /etc/pki/tls/private/slicer.key\ncp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem\n\nchmod 600 /etc/pki/tls/private/slicer.cert\nchmod 600 /etc/pki/tls/private/slicer.key\nchmod 600 /etc/pki/tls/private/domain-cabundle.pem\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#verify-certificate-installation", "title": "Verify Certificate Installation:", "text": "<pre><code>openssl verify /etc/pki/tls/private/slicer.cert\n</code></pre> <p>Expected Output</p> <pre><code>/etc/pki/tls/private/slicer.cert: C = US, ST = Ohio, L = Dublin, O = Cardinal Health, OU = EIT, CN = slicer.cardinalhealth.net\nerror 20 at 0 depth lookup:unable to get local issuer certificate\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#restart-apache", "title": "Restart Apache:", "text": "<pre><code>service httpd restart\nexit\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#check-certificate-validity", "title": "Check Certificate Validity:", "text": "<pre><code>openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2&gt;/dev/null | openssl x509 -noout -dates\n</code></pre> <p>Expected Output</p> <pre><code>notBefore=Apr  5 15:37:06 2021 GMT\nnotAfter=Apr  5 15:37:06 2022 GMT\n</code></pre>"}, {"location": "getting-started/servers/gcp/certificates/#validate-certificate-in-browser-chrome-edge", "title": "Validate Certificate in Browser (Chrome, Edge):", "text": "<ul> <li>Open https://slicer.cardinalhealth.net and check for security warnings.</li> </ul>"}, {"location": "getting-started/servers/gcp/certificates/#test-from-a-raspberry-pi", "title": "Test from a Raspberry Pi:", "text": "<pre><code>curl https://slicer.cardinalhealth.net\n</code></pre> <p>If it fails:</p> <p><pre><code>curl: (60) SSL certificate problem: unable to get local issuer certificate\n</code></pre> More details here: https://curl.haxx.se/docs/sslcerts.html <pre><code>curl failed to verify the legitimacy of the server and therefore could not\nestablish a secure connection to it. To learn more about this situation and\nhow to fix it, please visit the web page mentioned above.\n</code></pre></p> <p>Try:</p> <pre><code>openssl s_client -connect slicer.cardinalhealth.net:443\n</code></pre> Info <p>Need to add root trust cert for <PERSON>? Pi Trusted Certificate Setup</p> <p>How to install certificates for command line? Ask Ubuntu - How to Install Certificates</p> <p>On Pi, to add Trusted Cert put the Cert in <code>/usr/local/share/ca-certificates/</code></p> <pre><code>sudo cp domain-ca.crt /usr/local/share/ca-certificates/\n</code></pre> <pre><code>sudo rm /etc/ssl/certs/ca-certificates.crt\nsudo apt-get update &amp;&amp; sudo apt-get install -y --reinstall ca-certificates\n# sudo dpkg-reconfigure ca-certificates\n</code></pre> Re-run the update command<pre><code>sudo update-ca-certificates\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/", "title": "Configuration", "text": ""}, {"location": "getting-started/servers/gcp/configuration/#production-configuration", "title": "Production Configuration", "text": "<pre><code>gcloud config set project mac-mgmt-pr-cah\ngcloud deployment-manager deployments list\n</code></pre> Example Output <pre><code>NAME            LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS\njamf-pro-prod   update               DONE                 manifest-1584487098797  []\ntech-help-prod  insert               DONE                 manifest-1578490211746  []\n</code></pre> To SSH into the server:<pre><code>gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip\n</code></pre> When that log in eventually does not work, due to an expiration, then use a new name, like:<pre><code>gcloud compute ssh david.ferguson2@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#non-production-configuration", "title": "Non-Production Configuration", "text": "<pre><code>gcloud config set project mac-mgmt-np-cah\ngcloud deployment-manager deployments list\n</code></pre> Example Output <pre><code>NAME               LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS\ngraylog-dev        update               DONE                 manifest-1551531702436  []\njamf-pro-dev       insert               DONE                 manifest-1552401100556  []\njamf-pro-frontend  insert               DONE                 manifest-1551992656221  []\nkolide-fleet-dev   insert               DONE                 manifest-1550851334781  []\nldec5009slicr01    insert               DONE                                         [MANIFEST_EXPANSION_USER_ERROR]\npuppet-master-dev  insert               DONE                 manifest-1571256331515  []\nresillio-dev       insert               DONE                 manifest-1556562646161  []\n</code></pre> To describe a specific deployment:<pre><code>gcloud deployment-manager deployments describe my-deployment\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#web-view-of-deployments", "title": "Web View of Deployments", "text": "<p>Google Cloud Deployment Manager Quickstart</p> <p>Click the link that says \"Go to Deployment Manager\": Google Cloud Deployment Manager Console</p>"}, {"location": "getting-started/servers/gcp/configuration/#new-production-vm-setup", "title": "New Production VM Setup", "text": "<p>To create a new instance, execute the following steps in a terminal window on your PC:</p> <p>Google Cloud Deployment Manager Quickstart</p>"}, {"location": "getting-started/servers/gcp/configuration/#step-1-set-the-correct-project", "title": "Step 1: Set the Correct Project", "text": "<pre><code>gcloud config set project mac-mgmt-pr-cah\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#step-2-name-your-instance", "title": "Step 2: Name Your Instance", "text": "<p>Refer to the GCP Naming Standards.</p> <p>Example:</p> <pre><code>lpec5009slicr01\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#step-3-manage-service-accounts", "title": "Step 3: Manage Service Accounts", "text": "<p>Service accounts define how VMs obtain permissions.</p> <p>Google Cloud IAM Service Accounts</p> <pre><code>gcloud iam service-accounts list\n</code></pre> <pre><code>gcloud iam service-accounts create pi-mgmt-pr-slicer-main \\ --description=\"pi-mgmt-pr-slicer-main\" \\ --display-name=\"pi-mgmt-pr-slicer-main\"\n</code></pre> <p>Generated Service Account</p> <pre><code><EMAIL>\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#step-4-create-with-yaml", "title": "Step 4: Create with YAML", "text": "<pre><code>cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer\ngcloud deployment-manager deployments create lpec5009slicr01 --config config_lpec5009slicr01.yaml --project mac-mgmt-pr-cah\n</code></pre> <p>After deployment, update the boot disk in the GCP console</p> <p>Add the tag <code>\"snapshots\"</code> with a value of <code>\"h0_d10-2\"</code> to enable hourly snapshots of trhe disk.</p>"}, {"location": "getting-started/servers/gcp/configuration/#step-5-access-maybe-needed", "title": "Step 5: Access (maybe needed)", "text": "<p>Note</p> <p>You may need to be added to the <code>\"A-MacSysAdmins\"</code> group. (<PERSON> owns it, <PERSON> asked for it for <PERSON>).</p>"}, {"location": "getting-started/servers/gcp/configuration/#step-6-fix-ssh-access-issues-if-fails", "title": "Step 6: Fix SSH Access Issues (if fails)", "text": "<p>If the \"Remote into it via SSH\" in below here fails, then toggle the OS Login on then off, like:</p> Turn ON<pre><code>gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=TRUE\n</code></pre> Turn OFF<pre><code>gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=FALSE\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#step-7-remote-into-the-instance-via-ssh", "title": "Step 7: Remote into the Instance via SSH", "text": "<pre><code>gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip\n</code></pre>"}, {"location": "getting-started/servers/gcp/configuration/#step-8-retrieve-instance-ip-address", "title": "Step 8: Retrieve Instance IP Address", "text": "<pre><code>ip addr | fgrep inet\n</code></pre> <p>Example Output</p> <pre><code>***********\n</code></pre>"}, {"location": "getting-started/servers/gcp/gcp-servers/", "title": "Servers", "text": ""}, {"location": "getting-started/servers/gcp/gcp-servers/#list-of-servers", "title": "List of Servers", "text": "Server IP Address slicer01 <code>***********</code> slicer03 <code>************</code> slicer04 <code>***********</code> <p>Note</p> <p>If the server moves again, then the firewall will need to be updated for the ??? project (Qcam was going to be a dependency but is not now).</p>"}, {"location": "getting-started/servers/gcp/gcp-servers/#notes", "title": "Notes", "text": "<p>This was the original instance. As of 2022.10.14, refer to GCP Rocky Server.</p> slicer01 <p>📅 Date: 2024.01.08</p> <pre><code>hostnamectl\n</code></pre> Property Value Static Hostname <code>lpec5009slicr01</code> Icon Name <code>computer-vm</code> Chassi<PERSON> <code>vm</code> Machine ID <code>012a787168254cbcaa5f13dde54611bc</code> Boot ID <code>6b72c8126ef94b4bbfb2d86e0de5c4a0</code> Virtualization <code>kvm</code> Operating System <code>CentOS Linux 7 (Core)</code> CPE OS Name <code>cpe:/o:centos:centos:7</code> Kernel <code>Linux 3.10.0-1160.102.1.el7.x86_64</code> Architecture <code>x86-64</code>"}, {"location": "getting-started/servers/gcp/gcp-servers/#old-setup-creating-the-slicer-server-instance-on-gcp", "title": "Old Setup: Creating the Slicer Server Instance on GCP", "text": ""}, {"location": "getting-started/servers/gcp/gcp-servers/#starting-point", "title": "Starting Point", "text": "<p>Refer to: GCP Create VM Instance.</p>"}, {"location": "getting-started/servers/gcp/gcp-servers/#install-google-cloud-sdk-on-your-pc", "title": "Install Google Cloud SDK on Your PC", "text": "<p>On your PC (tested on MAC), install the GCloud SDK, which includes the gcloud command line utility:</p> <p>Download and Install: Google Cloud SDK Installation Guide</p>"}, {"location": "getting-started/servers/gcp/gcp-servers/#authenticate-with-gcp", "title": "Authenticate with GCP", "text": "<pre><code>gcloud auth login\n</code></pre> <p>Get someone to add you to the required permissions group to access: <code>(<PERSON> set this up for <PERSON>)</code></p> <pre><code>mac-mgmt-pr\nmac-mgmt-np\n</code></pre> <p>That is:     <pre><code>a-cloud-mac-mgmt-np-editor\na-cloud-mac-mgmt-np-owner\na-cloud-mac-mgmt-pr-editor\na-cloud-mac-mgmt-pr-owner\n</code></pre></p>"}, {"location": "getting-started/servers/gcp/gcp-servers/#projects-based-on-given-permissions", "title": "Projects Based on Given Permissions", "text": "<pre><code>gcloud components update\ngcloud init\ngcloud auth login\ngcloud auth list\ngcloud <NAME_EMAIL>\ngcloud auth login\n</code></pre> <pre><code>gcloud config set project mac-mgmt-np-cah\ngcloud projects list\n</code></pre> Expected Output <pre><code>cah-host-nonprod  cah-host-nonprod  ************\ncah-host-prod     cah-host-prod     527853414833\ncims-np-cah       cims-np           776122696896\nmac-mgmt-np-cah   mac-mgmt-np       979866627918\nmac-mgmt-pr-cah   mac-mgmt-pr       92183180931\nmanagement-cah    management        372449746971\nslicer-pr-cah     slicer-pr         361361986442\n</code></pre>"}, {"location": "getting-started/servers/gcp/monitoring-setup/", "title": "Monitoring Setup", "text": ""}, {"location": "getting-started/servers/gcp/monitoring-setup/#newrelic", "title": "NewRelic", "text": "<p>📅 Date: 2022.02.08</p> <p>Hello Project Owners,</p> <p>Attached is a list of production cloud instances which are not currently being monitored by New Relic. Cardinal standard is that all production instances be monitored by New Relic. To bring these instances into compliance we are submitting a change control to push the New Relic client to these instances on 2/19. This should be transparent to the instance and does not require a reboot unless the instance is running 32-bit windows OS in which case the instance will be rebooted.</p> <p>If the timing of this activity is in conflict with other activities planned for the instance you may use the self installation tools located here https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts. If there are any issues with the installation please submit a request to EITSS-MONITORING.</p> <p>If there is a technical reason where New Relic cannot be installed on this instance please reach out to the Infrastructure Security and Governance team to document this issue in as an Archer item. Any other questions can be sent directly to me.</p> <p>Thank you for your compliance.</p> <p>-<PERSON></p> <p>Sr. Engineer, Infrastructure Security and Governance</p> <p>For self-installation tools, refer to: 🔗 New Relic Installation Scripts</p> <p>For issues with installation, submit a request to EITSS-MONITORING. If New Relic cannot be installed for technical reasons, document the issue as an Archer item.</p> NewRelic License Key <pre><code>mac-mgmt-pr-cah\n8919c2c426b3b41c45e5bb2f0b429d8bb4beNRAL\n</code></pre>"}, {"location": "getting-started/servers/gcp/monitoring-setup/#installation-steps", "title": "Installation Steps", "text": "<pre><code># Clone the NewRelic installation repository\ngit clone https://github.com/CardinalHealth/esm_self_service.git\n</code></pre>"}, {"location": "getting-started/servers/gcp/monitoring-setup/#verify-newrelic-monitoring", "title": "Verify NewRelic Monitoring", "text": "<p>View instance status at: 🔗 NewRelic One Dashboard</p> NewRelic User Access <pre><code>david.ferguson\nNrbud+1!\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/", "title": "Old Development Server Notes", "text": ""}, {"location": "getting-started/servers/gcp/old-server-docs/#new-development-vm", "title": "New Development VM", "text": "<p>In a terminal window on the PC (only do this to create a new instance):</p> <p>Google Cloud Deployment Manager Quickstart</p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-1-set-the-correct-project", "title": "Step 1: Set the Correct Project", "text": "<pre><code>gcloud config set project mac-mgmt-np-cah\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-2-name-your-instance", "title": "Step 2: Name Your Instance", "text": "<p>Refer to the GCP Naming Standards.</p> <p>Example: <pre><code>ldec5009slicr01\n</code></pre></p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-3-manage-service-accounts", "title": "Step 3: Manage Service Accounts", "text": "<p>Service accounts define how VMs obtain permissions.</p> <p>Google Cloud IAM Service Accounts</p> <pre><code>gcloud iam service-accounts list\n</code></pre> <pre><code>gcloud iam service-accounts create pi-mgmt-np-slicer-master \\\n    --description=\"pi-mgmt-np-slicer-master\" \\\n    --display-name=\"pi-mgmt-np-slicer-master\"\n</code></pre> <p>Generated Service Account:</p> <pre><code><EMAIL>\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-4-create-with-yaml", "title": "Step 4: Create with YAML", "text": "<pre><code>cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer\ngcloud deployment-manager deployments create ldec5009slicr01 --config ldec5009slicr01.yaml --project mac-mgmt-np-cah\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-5-access-maybe-needed", "title": "Step 5: Access (maybe needed)", "text": "<p>You may need to be added to the <code>\"A-MacSysAdmins\"</code> group. (Russ <PERSON> owns it, <PERSON> asked for it for <PERSON>).</p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-6-fix-ssh-access-issues-if-fails", "title": "Step 6: Fix SSH Access Issues (if fails)", "text": "<p>If the \"Remote into it via SSH\" in below here fails, then toggle the OS Login on then off, like:</p> <pre><code>gcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=TRUE\ngcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=FALSE\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-7-remote-into-the-instance-via-ssh", "title": "Step 7: Remote into the Instance via SSH", "text": "<pre><code>gcloud compute ssh david.ferguson@ldec5009slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#install-apache", "title": "Install Apache", "text": "<p>DigitalOcean Guide: How to Install Apache on CentOS 7</p> Update and Install Apache<pre><code>sudo yum update httpd\nsudo yum install httpd\n</code></pre> Configure Firewall for Apache<pre><code>sudo firewall-cmd --permanent --add-service=https\nsudo firewall-cmd --reload\n</code></pre> Start and Verify Apache<pre><code>sudo systemctl start httpd\nsudo systemctl status httpd\n</code></pre> Check Default Configuration<pre><code>cat /etc/httpd/conf.d/welcome.conf\n</code></pre> Set Up a Test Page<pre><code>sudo su\necho \"empty\" &gt; /var/www/html/index.html\nexit\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#ssl-certificate-setup", "title": "SSL Certificate Setup", "text": ""}, {"location": "getting-started/servers/gcp/old-server-docs/#certificate-generation", "title": "Certificate Generation", "text": "<p>References</p> <ul> <li>DigitalOcean: Create SSL Certificate on Apache for CentOS 7</li> <li>The SSL Store: Apache OpenSSL Installation</li> <li>The SSL Store: SSL Certificate Generation</li> <li>GetPageSpeed: SSL Directory Setup</li> </ul>"}, {"location": "getting-started/servers/gcp/old-server-docs/#generate-ssl-certificate-signing-request-csr", "title": "Generate SSL Certificate Signing Request (CSR)", "text": "<pre><code>openssl req -new -newkey rsa:2048 -nodes -keyout server.key -out server.csr\n</code></pre> <p>Provide the following answers when prompted:</p> <pre><code>Country Name (2 letter code) [US]: US\nState or Province Name (full name) [Ohio]: Ohio\nLocality Name (eg, city) [Dublin]: Dublin\nOrganization Name (eg, company) [CardinalHealth]: CardinalHealth\nOrganizational Unit Name (eg, section) [ClientEngineering]: ClientEngineering\nCommon Name (e.g. server FQDN or YOUR name) [dslicer.cardinalhealth.net]: dslicer.cardinalhealth.net\nEmail Address [<EMAIL>]: <EMAIL>\nChallenge Password: thisisslicer\nOptional Company Name (Leave blank): (empty)\n</code></pre> View the generated CSR<pre><code>cat server.csr\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#send-csr-to-ad-team-not-completed", "title": "Send CSR to AD Team (not completed)", "text": "<p>Submit Request in ServiceNow</p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#ssl-certificate-installation", "title": "SSL Certificate Installation", "text": ""}, {"location": "getting-started/servers/gcp/old-server-docs/#move-and-secure-the-certificate", "title": "Move and Secure the Certificate", "text": "<pre><code>sudo cp server.key /etc/pki/tls/private/dslicer.cardinalhealth.net\nsudo chown root:root /etc/pki/tls/private/dslicer.cardinalhealth.net\nsudo chmod 0600 /etc/pki/tls/private/dslicer.cardinalhealth.net\n</code></pre> Verify Apache Configuration<pre><code>cat /etc/httpd/conf/httpd.conf\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#internal-dns-configuration", "title": "Internal DNS Configuration", "text": "<p>Verify Internal DNS Resolution</p> <pre><code>ping ldec5009slicr01.c.mac-mgmt-np-cah.internal\n</code></pre> <p>Note</p> <p>This may not work out of the box.</p> <p>Check Current GCP IP Address</p> <pre><code>nslookup ************\n</code></pre> <p>Current address in GCP: <code>************</code></p> <p>Request DNS Entry Addition</p> <p>Request DNS Entry in ServiceNow</p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#install-python3-and-required-libraries", "title": "Install Python3 and Required Libraries", "text": "<pre><code>sudo yum update -y\nsudo yum install -y python3\npython3 --version\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#flask-setup-for-running-python", "title": "Flask Setup for Running Python", "text": "<p>References</p> <ul> <li>Build a Simple Python REST API with Apache2, Gunicorn, and Flask</li> <li>Flask Deployment with Apache on CentOS</li> <li>Flask Deployment using mod_wsgi</li> <li>Flask with uWSGI on CentOS</li> <li>Setting Up a Python Virtual Environment on CentOS</li> </ul>"}, {"location": "getting-started/servers/gcp/old-server-docs/#install-required-packages", "title": "Install Required Packages", "text": "<pre><code>sudo pip3 install --upgrade pip\nsudo pip3 install virtualenv flask\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#setting-up-a-flask-application", "title": "Setting Up a Flask Application", "text": ""}, {"location": "getting-started/servers/gcp/old-server-docs/#step-1-create-project-directory", "title": "Step 1: Create Project Directory", "text": "<pre><code>cd /var/www\nsudo mkdir hitme\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-2-create-the-main-application-file", "title": "Step 2: Create the Main Application File", "text": "<pre><code>sudo vi /var/www/hitme/run.py\n</code></pre> <p>Paste the following code:</p> <pre><code>import os\nfrom app import app\n\nif __name__ == \"__main__\":\n    port = int(os.environ.get(\"PORT\", 5000))\n    app.run(host='0.0.0.0', port=port, debug=True)\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-3-create-the-flask-application-directory", "title": "Step 3: Create the Flask Application Directory", "text": "<pre><code>sudo mkdir /var/www/hitme/app\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-4-create-the-flask-app-initialization-file", "title": "Step 4: Create the Flask App Initialization File", "text": "<p>Edit <code>__init__.py</code>:</p> <pre><code>sudo vi /var/www/hitme/app/__init__.py\n</code></pre> Paste the following code:<pre><code>from flask import Flask\napp = Flask(__name__)\n\***********(\"/\")\ndef hello():\n    return \"Hello world!\"\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-5-set-up-a-virtual-environment", "title": "Step 5: Set Up a Virtual Environment", "text": "<pre><code>cd /var/www\nsudo python3 -m venv hitme\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-6-activate-the-virtual-environment-and-install-flask", "title": "Step 6: Activate the Virtual Environment and Install Flask", "text": "<pre><code>cd /var/www/hitme\nsource bin/activate\npip install flask\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#step-7-run-the-flask-application", "title": "Step 7: Run the Flask Application", "text": "<pre><code>export FLASK_APP=run.py\nflask run\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#additional-setup", "title": "Additional Setup", "text": "<pre><code>sudo virtualenv --python=python3 hitme # the venv is created inside the app folder\nsudo cd /var/www/hitme\nsource bin/activate\npip install --upgrade pip\npip freeze\npip install -U pip requests\npip freeze\ndeactivate\n</code></pre> Configure WSGI<pre><code>sudo vi /var/www/hitme/wsgi.py\n</code></pre> Paste the following code:<pre><code>#!/usr/bin/env python\nimport sys\nimport site\n\nsite.addsitedir('/var/www/hitme/lib/python3.6/site-packages')\n\nsys.path.insert(0, '/var/www/hitme')\n\nfrom app import app as application\n</code></pre> Configure Apache for Flask<pre><code>sudo vi /etc/httpd/conf.d/flask-hitme.conf\n</code></pre> Paste the following:<pre><code>&lt;VirtualHost _default_:443&gt;\n     WSGIDaemonProcess hitme user=apache group=apache threads=2\n     WSGIScriptAlias /hitme /var/www/hitme/wsgi.py\n     &lt;Directory /var/www/hitme&gt;\n         Require all granted\n     &lt;/Directory&gt;\nSSLEngine on\nSSLCertificateFile \"/etc/pki/tls/private/slicer.cert\"\nSSLCertificateKeyFile \"/etc/pki/tls/private/slicer.key\"\nSSLCACertificateFile  \"/etc/pki/tls/private/domain-cabundle.pem\"\n&lt;/VirtualHost&gt;\n</code></pre> Restart Apache<pre><code>sudo systemctl restart httpd\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#extra-dev-server-with-5008", "title": "Extra Dev Server with 5008", "text": "Create with YAML<pre><code>cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer\ngcloud deployment-manager deployments create ldec5008slicr01 --config ldec5008slicr01.yaml --project mac-mgmt-np-cah\n</code></pre> <p>May need to be added to <code>A-MacSysAdmins</code> as a member <code>(<PERSON> owns it, <PERSON> asked for it for <PERSON>)</code></p> <p>If the remote in below here fails, then toggle the oslogin on then off, like:</p> <pre><code>gcloud config set project mac-mgmt-np-cah\ngcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=TRUE\ngcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=FALSE\n</code></pre> Remote Access via SSH<pre><code>gcloud compute ssh david.ferguson@ldec5008slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip\n</code></pre> Check Known Hosts:<pre><code>/root/.ssh/known_hosts\n</code></pre> User Login Attempt<pre><code>cp /var/log/slicer/login/throw/david.ferguson /var/log/slicer/login/keep/david.ferguson\nsudo chown apache:apache /var/log/slicer/login/keep/david.ferguson\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#tornado-server-side", "title": "Tornado (Server-side)", "text": "<p>References</p> <ul> <li>Tornado Setup Guide</li> <li>Tornado WSGI</li> <li>Tornado Best Web Framework</li> <li>Tornado Web</li> <li>Python Tornado Guide</li> <li><PERSON><PERSON><PERSON> vs Tornado</li> </ul> <p>This file gets loaded to:</p> <p><code>/var/www/html/tornado_server.py</code> Using<pre><code>sudo vi /var/www/html/tornado_server.py\n</code></pre> Run with:<pre><code>sudo python /var/www/html/tornado_server.py\n</code></pre></p> Add the following:<pre><code>import tornado.ioloop\nimport tornado.web\n\nclass MainHandler(tornado.web.RequestHandler):\n   def get(self):\n      self.write(\"Hello, world\")\n\napplication = tornado.web.Application([\n   (r\"/\", MainHandler),\n])\n\nif __name__ == \"__main__\":\n   application.listen(8888)\n   tornado.ioloop.IOLoop.instance().start()\n</code></pre> <p>2023.01.25: After updating to Mac OS Ventura 13.2, ssh breaks.</p> <p>SSH Issue After Updating MacOS Ventura <pre><code>~/.ssh/config\n</code></pre> <pre><code>Host *\nPubkeyAcceptedKeyTypes=+ssh-rsa\n</code></pre></p> Run Tornado:<pre><code>sudo python /var/www/html/tornado_server.py\n</code></pre>"}, {"location": "getting-started/servers/gcp/old-server-docs/#certificate-renewal", "title": "Certificate Renewal", "text": "<p>Reference Ticket</p> <ul> <li>CHG0321708</li> </ul> <p>Cert arrived as a compressed file \"slicercardinalhealthnet.7z\", with a password sent in a separate email.</p> <p>Install on Mac, the app \"Extractor - Unarchive Files\" from the app store (free)</p> <p>In terminal on mac, go to where the pfx file is unzipped:</p> <pre><code>cd /Users/<USER>/Downloads/20240112\nxxd slicercardinalhealthnet.pfx\n</code></pre> <p>Prints text. Copy all in terminal, paste into a text document, clean up the start and end to remove prompt lines</p>"}, {"location": "getting-started/servers/gcp/old-server-docs/#on-slicer-server", "title": "On Slicer Server", "text": "Create certificate directory:<pre><code>sudo su\nmkdir /etc/pki/tls/private/20240112\ncd /etc/pki/tls/private/20240112\nvi slicercardinalhealthnet.txt\n</code></pre> Insert cleaned certificate content, then convert PFX:<pre><code>cat slicercardinalhealthnet.txt | xxd -r &gt;slicercardinalhealthnet.pfx\n</code></pre> <p>Make Cert and Key files from PFX</p> <ul> <li>Convert a PFX Certficate file on Linux Server</li> <li>Converting PFX Certficate Bag to Apache SSL</li> </ul> Convert PFX<pre><code>openssl pkcs12 -info -in slicercardinalhealthnet.pfx\nopenssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert\nopenssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes -out slicer.key\nopenssl rsa -in slicer.key -outform PEM -out slicer_pem.key\nopenssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem\nopenssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -nodes -out domain-ca.crt\n</code></pre> Move certificates to the correct location:<pre><code>cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt\ncp slicer.cert /etc/pki/tls/private/slicer.cert\ncp slicer.key /etc/pki/tls/private/slicer.key\ncp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem\n</code></pre> Set permissions:<pre><code>chmod 600 /etc/pki/tls/private/slicer.cert\nchmod 600 /etc/pki/tls/private/slicer.key\nchmod 600 /etc/pki/tls/private/domain-cabundle.pem\n</code></pre> Verify and restart Apache:<pre><code>openssl verify /etc/pki/tls/private/slicer.cert\nservice httpd restart\n</code></pre> Check certificate validity:<pre><code>openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2&gt;/dev/null | openssl x509 -noout -dates\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/", "title": "Install Packages", "text": ""}, {"location": "getting-started/servers/gcp/packages/#installing-required-packages", "title": "Installing Required Packages", "text": ""}, {"location": "getting-started/servers/gcp/packages/#ldap-login-support", "title": "LDAP Login Support", "text": "<pre><code>sudo yum install -y python-ldap\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#network-scanner", "title": "Network Scanner", "text": "<pre><code>sudo yum install -y nmap\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#timezone-library", "title": "Timezone Library", "text": "<pre><code>sudo yum install -y pytz\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#scientific-computing-libraries", "title": "Scientific Computing Libraries", "text": "<pre><code>sudo yum install -y numpy scipy\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#tornado-web-server", "title": "Tornado Web Server", "text": "<pre><code>sudo yum install -y python-tornado\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#requests-library", "title": "Requests Library", "text": "<pre><code>sudo yum install -y python-requests\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#new-ldap-method-20230905", "title": "New LDAP Method (2023.09.05)", "text": "<pre><code>sudo yum install openldap-clients\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#set-proper-file-ownership", "title": "Set Proper File Ownership", "text": "<pre><code>sudo chown -R apache:apache /var/www/slicer\nsudo chown -R apache:apache /var/www/slicer/login\n</code></pre>"}, {"location": "getting-started/servers/gcp/packages/#security-issue-policykit-polkit-vulnerability-pwnkit-cve-2021-4034", "title": "Security Issue: PolicyKit, Polkit Vulnerability (PwnKit) (CVE-2021-4034)", "text": "<p>2022.01.16 - Security Fix</p> <p>PolicyKit (Polkit) vulnerability, also known as PwnKit, needs to be mitigated by disabling privilege escalation.</p> Check current permissions<pre><code>ls -l /usr/bin/pkexec\n</code></pre> Expected Output <pre><code>-rwsr-xr-x. 1 root root 23576 Apr  1  2020 /usr/bin/pkexec\n</code></pre> Fix: Disable Privilege Escalation<pre><code>sudo chmod 0755 /usr/bin/pkexec\n</code></pre> Verify Changes<pre><code>ls -l /usr/bin/pkexec\n</code></pre> Expected Output After Fix <pre><code>-rwxr-xr-x. 1 <USER> <GROUP> 23576 Apr  1  2020 /usr/bin/pkexec\n</code></pre>"}, {"location": "getting-started/servers/gcp/python-apache/", "title": "Python Apache", "text": ""}, {"location": "getting-started/servers/gcp/python-apache/#python-on-apache", "title": "Python on Apache", "text": "<ul> <li>UnixMen: Set up Python scripting for Apache</li> <li>Stack Overflow: Running Python script from Apache</li> </ul>"}, {"location": "getting-started/servers/gcp/python-apache/#websockets-in-javascript", "title": "WebSockets in JavaScript", "text": "<ul> <li>MDN: Writing WebSocket Client Applications</li> </ul>"}, {"location": "getting-started/servers/gcp/python-apache/#firewall-and-ip-tables-notes-not-used", "title": "Firewall and IP tables notes (not used)", "text": ""}, {"location": "getting-started/servers/gcp/python-apache/#list-open-ports", "title": "List open ports", "text": "<pre><code>sudo firewall-cmd --list-ports\n</code></pre>"}, {"location": "getting-started/servers/gcp/python-apache/#enable-https-and-disable-http", "title": "Enable HTTPS and disable HTTP", "text": "<pre><code>sudo firewall-cmd --permanent --add-service=https\nsudo firewall-cmd --permanent --remove-service=http\nsudo firewall-cmd --permanent --remove-port=80/tcp\nsudo firewall-cmd --reload\n</code></pre>"}, {"location": "getting-started/servers/gcp/python-apache/#check-and-modify-iptables-rules", "title": "Check and modify iptables rules", "text": "<pre><code>sudo iptables -L\nsudo iptables -A INPUT -p tcp --destination-port 80 -j DROP\nsudo firewall-cmd --reload\n</code></pre>"}, {"location": "getting-started/servers/gcp/python-apache/#apache", "title": "Apache", "text": "<p>Apache Redirect to HTTPS</p> <p> Not Used:</p> <pre><code>vi /etc/httpd/conf/httpd.conf\n</code></pre> <p>Add to end</p> <pre><code>ServerSignature Off\nServerTokens Prod\n</code></pre> <p>Install mod_wsgi</p> <pre><code>sudo yum install mod_wsgi\nsudo systemctl restart httpd\nsudo httpd -M | grep wsgi\n</code></pre>"}, {"location": "getting-started/servers/gcp/sudo-access/", "title": "Sudo Access", "text": "<p>Request permission via ServiceNow.</p> <p>Service account on the Chef production server (where <PERSON> has sudo):</p> <pre><code><EMAIL>\n</code></pre> <p>Has SSH key added in GCP GUI panel. Su<PERSON> works freely.</p>"}, {"location": "getting-started/servers/gcp/sudo-access/#troubleshooting-os-login-issues", "title": "Troubleshooting OS Login Issues", "text": "<pre><code>\"Using OS Login user [david_ferguson_cardinalhealth_co] instead of requested user [david.ferguson]\"\n</code></pre>"}, {"location": "getting-started/servers/gcp/sudo-access/#manually-add-ssh-key-to-vm", "title": "Manually add SSH key to VM", "text": "<p>Edit the VM and enter the SSH key from prod:</p> <pre><code>ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDVkewljS/EuebHOO7fTjX3TSsdjYPqxBnEhzjXxDiNw3PNXRNnaSc8h4KcdUR+QUNjlVhSHbOmJzMkFPjSy1XOADi9ax+DsG64NlKcTOvS7NeNFBw1f3UqV58lQTH7xQhL7TcGhJUhopCRt/O54GfUD4qiku8Be9D+XluyJsMBNaTJX2dmnseSp+8GKq2JUQn/jBv13Of43lyUpDTxmzKq4yyOANwKyWDcwoQ3zu3f57qkTij5tTQm9OCSq+oBXYC4iMnWMwtnltY2mhZda0BTGu/8wmf+TRDQAr6F9Env7HPzhRow6inU6qUoEch/orIOyvITFv0PA5C2TWEjG7+cyPPpilVrJ67+kDx7bEu2/LrEhlUH6myj58K4Jg7yCFR7lzBe/C9E0OqinLoeqfHowBDvImAtDx3QXwVBorMPe0PaKuwAVuOY8aXmxUh+S9cSbkIPOxVCraCyjmIyIhAdkkrPbmMn6/ewmHM4xJzoCr2YctXj5mgISRkMEbSc+l8= <EMAIL>\n</code></pre> <p>For reference: Google Cloud: Managing Instance Access</p>"}, {"location": "how-tos/how-to-burn/", "title": "How to Burn Image", "text": ""}, {"location": "how-tos/how-to-burn/#flashing-the-sd-card", "title": "Flashing the SD Card", "text": "<p>USB Write Restrictions &amp; Admin Privileges</p> <p>Cardinal Health Computer Use Policies may restrict your ability to write to external media. If your system is affected, follow the steps below to gain the necessary permissions.</p>"}, {"location": "how-tos/how-to-burn/#enabling-usb-write-access-mac", "title": "Enabling USB Write Access (Mac)", "text": "<ul> <li>On Mac, this restriction is controlled via DLP-15.0.</li> <li> <p>To remove it, use the Self-Service program:   <code>Remove DLP-15.0</code> (This option is not visible by default and must be enabled in JAMF.)</p> </li> <li> <p>To be added to the USB Exception Group, send an email to:   <pre><code><EMAIL>\n</code></pre></p> </li> <li> <p>If a ServiceNow request is required, submit a Device Exception Request here:   🔗 Device Exception Request</p> </li> </ul>"}, {"location": "how-tos/how-to-burn/#gaining-admin-privileges-for-balena-etcher", "title": "Gaining <PERSON><PERSON> Privileges for <PERSON><PERSON>", "text": "<ul> <li>Mac Admin Access is needed to install and run <PERSON><PERSON>.</li> <li>Contact Client Engineering (<PERSON>) to have the \"Privileges\" app installed.</li> <li>Open the Privileges app → Request Install Permission.</li> <li>Quickly open <PERSON><PERSON> and start the image writing process (permissions last only 5 minutes).</li> </ul>"}, {"location": "how-tos/how-to-burn/#using-balena-etcher", "title": "Using <PERSON><PERSON>", "text": "<p>Follow these steps to correctly write the Raspberry Pi OS image to an SD card using <PERSON><PERSON>:</p> <ol> <li>Download &amp; Install Balena Etcher.</li> <li>Launch Balena Etcher and select:<ul> <li>\"Flash from file\" → Choose the zip file (<PERSON><PERSON> E<PERSON>er will extract it automatically).</li> <li>\"Select target\" → Choose the 16GB SD card.</li> <li>\"Flash!\" → Start the process. (Enter your Mac password if prompted.)</li> </ul> </li> <li>Wait for the flashing process to complete. (This may take a few minutes.)</li> <li>Safely eject the SD card and insert it into the Raspberry Pi.</li> <li> <p>Prepare for First Boot:</p> <ul> <li>Connect an HDMI monitor to the HDMI0 port (closest to the power connector).</li> <li>Attach a keyboard for setup.</li> </ul> 🚨 Important: Disconnect Pi from Network Before Booting <ul> <li>Before booting the Raspberry Pi:<ol> <li>Ensure it is not connected to any wired network.</li> </ol> </li> </ul> </li> <li> <p>Power on the Raspberry Pi by plugging in the power adapter.</p> </li> </ol> <p>Device Registration Process</p> <ul> <li>The Pi will reboot once as part of the process and will then land on a home page   displaying the Device ID and other important details.</li> <li>Now you can connect to the wired network and proceed with the device registration process.</li> <li>After one minute, the device will register with Slicer and appear in the list at:   👉 Slicer Reports</li> <li>Use the Find feature in your browser to search by ID or IDr, then click on the   ID to view the device's details.</li> <li>If you are logged into Slicer and have the necessary permissions, you will be able to   configure the device directly from this page.</li> </ul> <p>End of document.</p>"}, {"location": "how-tos/how-to-check-md5/", "title": "How to Check an MD5 Hash on a Downloaded File", "text": "<p>To verify the integrity of a downloaded file, you can check its MD5 checksum against the original value provided by the developer or the download page. Follow the instructions for your operating system below.</p> WindowsmacOSLinux <ol> <li>Download the latest version of WinMD5Free.</li> <li>Extract the downloaded zip file and launch <code>WinMD5.exe</code>.</li> <li>Click Browse, navigate to the file you want to check, and select it.</li> <li>The tool will automatically display the file’s MD5 checksum.</li> <li>Copy and paste the original MD5 value provided by the developer or download page.</li> <li>Click Verify to compare the values.</li> </ol> <ol> <li>Download the file you want to check and open the Downloads folder in Finder.</li> <li>Open Terminal from Applications &gt; Utilities.</li> <li>Type the following command without pressing Enter:    <pre><code>md5\n</code></pre></li> <li>Drag and drop the downloaded file from Finder into the Terminal window.</li> <li>Press Enter, and wait for the MD5 hash to be displayed.</li> <li>Open the checksum file provided on the download page (usually a <code>.cksum</code> file).</li> <li>Compare the MD5 hash in the checksum file with the one displayed in Terminal.</li> <li>If they match, the file was downloaded successfully. Otherwise, re-download the file.</li> </ol> <ol> <li>Open a terminal window.</li> <li>Type the following command:    <pre><code>md5sum [file_name]\n</code></pre> Alternatively, you can drag the file into the terminal to insert its path automatically.</li> <li>Press Enter.</li> <li>The MD5 hash of the file will be displayed.</li> <li>Compare it with the original MD5 checksum.</li> <li>If the values match, the file is valid; otherwise, re-download the file.</li> </ol> Pro Tip:  <p>Always verify the checksum of files downloaded from the internet, especially for software, to prevent corruption or tampering!</p> <p>End of document.</p>"}, {"location": "how-tos/how-to-show-ppt/", "title": "How to Show PowerPoint", "text": ""}, {"location": "how-tos/how-to-show-ppt/#export-powerpoint-as-animated-gif", "title": "Export PowerPoint as Animated GIF", "text": "<ol> <li>Open your PowerPoint presentation.</li> <li>Navigate to:<ul> <li>File → Export...</li> <li>File Format = Animated GIF</li> <li>Export As: (Choose a location and file name)</li> <li>Quality = Large</li> <li>Seconds spent on each slide = (Set as needed)</li> <li>Click Export</li> </ul> </li> </ol>"}, {"location": "how-tos/how-to-show-ppt/#upload-to-slicer", "title": "Upload to Slicer", "text": "<ol> <li>Log in to Slicer.</li> <li>Go to Reports.</li> <li>Find the device you want.</li> <li>Navigate to:<ul> <li>Profile → profile_Show_Slides → Submit</li> </ul> </li> </ol>"}, {"location": "how-tos/how-to-show-ppt/#upload-content-to-device", "title": "Upload Content to Device", "text": "<ol> <li>Locate the section titled \"Click to review Device file content\".</li> <li>Click on the Serial Number.<ul> <li>(Requires permission: \"deviceupload create\")</li> </ul> </li> <li>Use the new page to upload the content.</li> <li>The device will detect the profile change, download the content, and automatically launch it.</li> </ol>"}, {"location": "imaging/sd-image-building-qcam/", "title": "SD Image Building Guide QCAM", "text": "<p>Deprecated</p> <p>This document is no longer being used and is considered deprecated. For the latest information on building an image, please refer to the SD Image Building guide.</p>"}, {"location": "imaging/sd-image-building-qcam/#overview", "title": "Overview", "text": "<p>To build a new QCAM image, follow these steps.</p>"}, {"location": "imaging/sd-image-building-qcam/#download-raspberry-pi-os", "title": "Download Raspberry Pi OS", "text": "<ul> <li>Visit: Raspberry Pi OS</li> <li>Select: Raspberry Pi OS with Desktop</li> <li>Release Date: May 3rd, 2023</li> <li>System: 32-bit</li> <li>Kernel Version: 6.1</li> <li>Debian Version: 11 (Bullseye)</li> <li>Size: 872MB</li> <li>Show SHA256 file integrity hash:</li> <li>Release Notes</li> </ul> Save To:Copy To: <p><code>/downloads/2023-05-03-raspios-bullseye-armhf.img.xz</code></p> <p><code>/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/QCAM/</code></p>"}, {"location": "imaging/sd-image-building-qcam/#flash-the-sd-card", "title": "Flash the SD Card", "text": "<p>Use <PERSON><PERSON> to burn the SD card with the downloaded image.</p>"}, {"location": "imaging/sd-image-building-qcam/#initial-setup-without-network-connection", "title": "Initial Setup (Without Network Connection)", "text": "<ol> <li>Insert the SD card into the Raspberry Pi and power it on.</li> <li>Follow the setup wizard:</li> <li>Country: United States</li> <li>Language: American English</li> <li>Timezone: New York</li> <li>Click Next.</li> </ol>"}, {"location": "imaging/sd-image-building-qcam/#create-a-user-account", "title": "Create a User Account", "text": "<ul> <li>Username: <code>qcam</code></li> <li>Alternate Username: <code>cah-pi-su</code></li> <li>Enter and confirm password.</li> <li>Click Next.</li> </ul> <p>Note</p> <p>Do not connect to WiFI, keep hitting 'Next', until you get 'Restart', and take it.</p>"}, {"location": "imaging/sd-image-building-qcam/#configure-system-preferences", "title": "Configure System Preferences", "text": "<ol> <li>Enable SSH (On boot):<ul> <li>Go to Preferences &gt; Raspberry Pi Configuration &gt; Interfaces</li> <li>Enable SSH</li> </ul> </li> <li>Modify UI Settings (On boot):<ul> <li>Go to Preferences &gt; Appearance Settings</li> <li>Uncheck all options; set layout to No Image</li> </ul> </li> <li>Restrict Menu Access (On boot):<ul> <li>Go to Preferences &gt; Main Menu Editor</li> <li>Uncheck all except System Tools</li> </ul> </li> <li>Remove Terminal Shortcut:<ul> <li>Right-click the Terminal shortcut and remove it.</li> </ul> </li> </ol> <p>FixMe</p> <p>Need to disable screen blanking (keep on always)</p>"}, {"location": "imaging/sd-image-building-qcam/#find-device-ip-address", "title": "Find Device IP Address", "text": "<ul> <li>Hover over the network icon in the top-right corner.</li> <li>SSH into the device:</li> </ul> <pre><code>ssh cah-pi-su@&lt;device-ip&gt;\n</code></pre>"}, {"location": "imaging/sd-image-building-qcam/#configure-wi-fi", "title": "Configure Wi-Fi", "text": "<p>Edit the Wi-Fi config file:</p> <pre><code>sudo vi /etc/wpa_supplicant/wpa_supplicant.conf\n</code></pre> <p>Add the following:</p> <pre><code>network={\n    ssid=\"cah-iot\"\n    psk=\"BVIm5bvQ65\"\n    key_mgmt=WPA-PSK\n}\n</code></pre> <p>Set Up <code>qcam</code> User</p> <pre><code>sudo adduser qcam\n</code></pre> <ul> <li>Use standard Camel Case password.</li> <li>Assign necessary groups:</li> </ul> <pre><code>sudo usermod -a -G video,input,lpadmin qcam\n</code></pre> <ul> <li>Verify settings:</li> </ul> <pre><code>ls -l /dev/input/\ngroups qcam\n</code></pre>"}, {"location": "imaging/sd-image-building-qcam/#enable-auto-login-for-qcam", "title": "Enable Auto-login for <code>qcam</code>", "text": "<p>Edit the autologin config:</p> <pre><code>sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf\n</code></pre> <p>Add the following:</p> <pre><code>[Service]\nExecStart=\nExecStart=-/sbin/agetty --autologin qcam --noclear %I \\$TERM\n</code></pre> Run Command<pre><code>sudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>\n</code></pre> <p>Enable GUI auto-login:</p> <pre><code>sudo vi /etc/lightdm/lightdm.conf\n</code></pre> <p>Find and set (Search for <code>autologin-user</code> by typing <code>/autologin-user</code>):</p> Set To:<pre><code>autologin-user=qcam\n</code></pre> <p>Reboot to test auto-login:</p> <pre><code>sudo reboot\n</code></pre>"}, {"location": "imaging/sd-image-building-qcam/#install-updates-and-required-packages", "title": "Install Updates and Required Packages", "text": "<p>Log in as <code>cah-pi-su</code> user</p> Screen grabs<pre><code>sudo apt-get install -y scrot\n</code></pre> <p>6.1 already has this installed</p>"}, {"location": "imaging/sd-image-building-qcam/#install-slicer-services", "title": "Install Slicer Services", "text": "<pre><code>sudo mkdir /cardinal\n</code></pre> <p><code>pi_runner</code></p> <ul> <li>Manually set up this one, according to the instructions inside it, or push from Slicer</li> </ul> <p><code>pi_monitor</code></p> <ul> <li>Load manually or through Slicer interface to the device (Runner must have been loaded first)</li> </ul>"}, {"location": "imaging/sd-image-building-qcam/#clean-up-and-finalize-image", "title": "Clean Up and Finalize Image", "text": "<pre><code>sudo su\n</code></pre> All of these as a single copy and paste into the terminal:<pre><code>apt-get clean\napt-get -y autoremove --purge\n</code></pre> All of these as a single copy and paste into the terminal:<pre><code>rm -rf /Downloads\nrm -rf /home/<USER>/*\nrm -rf /cardinal/save_values\n</code></pre> All of these as a single copy and paste into the terminal:<pre><code>systemctl stop pi-hmi.service\nsystemctl stop pi-runner.service\nsystemctl stop pi-config.service\nfind /var/log -type f -regex \".*\\.gz$\" -delete\nfind /var/log -type f -regex \".*\\.[0-9]$\" -delete\nrm /var/log/*\necho 'yes' &gt; /cardinal/needsexpand\necho -n \"0\" &gt; /cardinal/boot_count.txt\necho -n \"0\" &gt; /cardinal/grab_count.txt\ncp /cardinal/browserstart_default /cardinal/browserstart\nrm -rf /cardinal/localhtml/*\nrm -rf /cardinal/log/*\nrm -rf /cardinal/config_*\nmkdir /cardinal/localhtml/\n</code></pre> <p>Create startup page:</p> <pre><code>echo '&lt;head&gt;&lt;meta http-equiv=\"refresh\" content=\"5\" &gt;&lt;/head&gt;&lt;body&gt;&lt;center&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;table border=\"1\" cellpadding=\"10\"&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Starting up...&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;3 boot ups is the normal sequence for a new image.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Screen may not fill to edges until all boots complete.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;If this screen does not disappear after 10 seconds,&lt;br&gt;then press Alt F4 to reset the screen.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/center&gt;&lt;/body&gt;' &gt; /cardinal/localhtml/index.html\n</code></pre> <p>Shutdown:</p> <pre><code>sudo shutdown -h now\n</code></pre>"}, {"location": "imaging/sd-image-building-qcam/#load-qcam-software", "title": "Load QCAM Software", "text": "<pre><code>sudo raspi-config --expand-rootfs\nsudo reboot\n</code></pre> <pre><code>sudo mkdir /cardinal/thirdparty\ncd /cardinal/thirdparty\nsudo curl -k --output qcam_20230623_120500.zip https://************/thirdparty?filetodownload=qcam_20230623_120500\n</code></pre> <pre><code>sudo unzip qcam_20230623_120500.zip\ncd /cardinal/thirdparty/QCAM_Release_2023-06-23\nsudo chmod +x ./installer.sh\nsudo ./installer.sh\n</code></pre> <p>Output</p> <pre><code>--------------------\nTesting Python packages...\ncut: requirements.txt: No such file or directory\nTesting camera...\nListing all available cameras:\nNo cameras available!\nERROR: arducam_64mp [9248x6944] camera is not available.\nChecking if all cameras are working:\npython: can't open file '/cardinal/thirdparty/QCAM_Release_2023-06-22/./test/test_camera_working.py': [Errno 2] No such file or directory\nTesting file and folder setup...\nTesting Python script installation...\nAll tests completed.\n--------------------\n</code></pre>"}, {"location": "imaging/sd-image-building-qcam/#verify-installation", "title": "Verify Installation", "text": "<pre><code>qcamcli --version\nsystemctl status qcam_ui-runner.service\nsystemctl status qcam_monitor_weight.service\n</code></pre> <p>End of document.</p>"}, {"location": "imaging/sd-image-building/", "title": "SD Image Building Guide", "text": ""}, {"location": "imaging/sd-image-building/#overview", "title": "Overview", "text": "<p>This guide provides step-by-step instructions on how to build and configure a new Raspberry Pi OS image.</p>"}, {"location": "imaging/sd-image-building/#to-build-a-new-image", "title": "To Build a New Image", "text": ""}, {"location": "imaging/sd-image-building/#20231030-pi5-image", "title": "📅 2023.10.30 - Pi5 Image", "text": "<p>Download Raspberry Pi OS Lite (2023-10-10)</p> <p>📌 Location:</p> <pre><code>/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/Starting_image/2023-10-10-raspios-bookworm-armhf-lite.img.xz\n</code></pre>"}, {"location": "imaging/sd-image-building/#20240312-updated-image", "title": "📅 2024.03.12 - Updated Image", "text": "<p>Download Raspberry Pi OS Lite (2023-12-11)</p>"}, {"location": "imaging/sd-image-building/#20240417-latest-image", "title": "📅 2024.04.17 - Latest Image", "text": "<p>Download Raspberry Pi OS Lite (2024-03-15)</p> <p>For official Raspberry Pi OS releases, visit:</p> <p>Raspberry Pi OS</p>"}, {"location": "imaging/sd-image-building/#testing-on-raspberry-pi-at-daves-house", "title": "Testing on Raspberry Pi at Dave's House", "text": "<p>Test Device IDs:</p> <pre><code>(ID=10000000e3669edf)\n(IDr=1530-0022-4805-1292-511)\n</code></pre> <p>SSH into the Pi:</p> <pre><code>ssh pi@**************\n</code></pre>"}, {"location": "imaging/sd-image-building/#original-raspberry-pi-os-lite-download", "title": "Original Raspberry Pi OS Lite Download", "text": "<p>📌 Official Download Page</p> <p>📌 GCP Bucket Location: View in Google Cloud Console</p> <p>📌 Alternative Download from GCP Storage:</p> <pre><code>gs://pi-mgmt-pr-cah-distribution/raw_image_starts/2021-03-04-raspios-buster-armhf-lite.zip\n</code></pre>"}, {"location": "imaging/sd-image-building/#release-information", "title": "Release Information", "text": "<pre><code>Release date: March 4th 2021\nKernel version: 5.10\nSize: 442MB\nSHA256: ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39\n</code></pre> Most recent release note: <p>2021-03-04: <pre><code>* <PERSON><PERSON><PERSON> upgraded to version ********\n* SD Card Copier made compatible with NVMe devices; now built against GTK+3 toolkit\n* Composite video options removed from Raspberry Pi 4 in Raspberry Pi Configuration\n* Boot order options in raspi-config adjusted for more flexibility\n* Recommended Software now built against GTK+3 toolkit\n* Fix for crash in volume plugin when using keyboard could push value out of range\n* Fix for focus changing between windows in file manager when using keyboard to navigate directory view\n* Fix for Raspberry Pi 400 keyboard country not being read correctly in startup wizard\n* Armenian and Japanese translations added to several packages\n* Automatically load aes-neon-bs on ARM64 to speed up OpenSSL\n* Raspberry Pi firmware fcf8d2f7639ad8d0330db9c8db9b71bd33eaaa28\n* Linux kernel 5.10.17\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#verify-sha256-checksum-on-mac", "title": "Verify SHA256 Checksum on Mac", "text": "<pre><code>shasum -a 256 ~/Downloads/2021-03-04-raspios-buster-armhf-lite.zip\n# or\nshasum -a 256 ~/Downloads/raw_image_starts-2021-03-04-raspios-buster-armhf-lite.zip\n</code></pre> Expected SHA256 <pre><code>ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39\n</code></pre> <p>Leave the file zipped - <PERSON><PERSON> can flash directly from the zip file.</p> <p>Mac Users:</p> <p>If <PERSON> has been forced to not allow USB memory, go to Self Service find the Remove DLP-15.0, and run the remove. (This is a not-normal remove, and must be made visible by using Jamf tools to show it)</p>"}, {"location": "imaging/sd-image-building/#flashing-the-sd-card", "title": "Flashing the SD Card", "text": "<p>This section has been moved to a dedicated guide. For step-by-step instructions on burning an image, visit:</p> <p>📌 How to Burn an Image</p>"}, {"location": "imaging/sd-image-building/#initial-setup", "title": "Initial Setup", "text": "<p>Added 2024.03.12</p> <ul> <li>Keyboard Layout: English (US)</li> </ul> NewOld <p>Login Credentials:</p> <ul> <li>Set a new user as <code>pi</code></li> <li>Use the cah pi default password, that is not <code>raspberry</code></li> <li><PERSON>gin as the <code>pi user</code></li> </ul> <p>Login Credentials:</p> <ul> <li>Username: <code>pi</code></li> <li>Password: <code>raspberry</code> (Change immediately!) Change Password To:<pre><code>passwd\n</code></pre></li> </ul>"}, {"location": "imaging/sd-image-building/#start-ssh", "title": "Start SSH", "text": "<pre><code>sudo systemctl start ssh\n</code></pre> <p>Now you can connect the Pi to a wired network.</p> <p>Get the IP Address:</p> <pre><code>ip addr\n</code></pre> <p>SSH into the Pi:</p> <p>Info</p> <p>---- At this point, you no longer need the keyboard, and can do the rest from another station Connect from another computer/station:<pre><code>ssh pi@&lt;ip-address&gt;\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#enable-ssh", "title": "Enable SSH", "text": "<pre><code>sudo systemctl enable ssh\n</code></pre>"}, {"location": "imaging/sd-image-building/#setting-up-the-hardware", "title": "Setting Up the Hardware", "text": "<p>Change Keyboard Layout (Old):</p> <pre><code>sudo vi /etc/default/keyboard\n# Change \"gb\" to \"us\"\n</code></pre>"}, {"location": "imaging/sd-image-building/#creating-admin-worker-users", "title": "Creating Admin &amp; Worker Users", "text": "<p>Started 2021.07.23 with Image version 2.2.0</p>"}, {"location": "imaging/sd-image-building/#create-admin-user-cah-pi-su", "title": "Create Admin User (<code>cah-pi-su</code>)", "text": "<pre><code>sudo adduser cah-pi-su\n</code></pre> <p>(vault lookup?)</p> <p>(leave all entries blank, and keep hitting enter until it saves)</p> ADM group gets us the journalctl log prints for all<pre><code>sudo usermod -a -G video,adm cah-pi-su\nsudo adduser cah-pi-su sudo\n</code></pre> <p>Grant Passwordless Sudo Access: To avoid typing password at each restart of sudo permission window</p> <pre><code>sudo vi /etc/sudoers.d/010_cah-pi-su\n</code></pre> Add the following line:<pre><code>cah-pi-su ALL=(ALL) NOPASSWD: ALL\n</code></pre> Log out of pi user<pre><code>exit\n</code></pre> <p>Disable Default <code>pi</code> User:</p> <pre><code>sudo vi /etc/shadow\n</code></pre> <p>Info</p> Find<pre><code>\"pi:$6$rseVWwvM7AfG7bKY$TV6WN558gwmBs.idUmnkzO9PlOgYJtenJM5oBJeDO4FcGAo.qEWdkHAw4CBDmYR.q3HZxRkO8fVPEa69t2cGo1:18690:0:99999:7:::\"\n# And remove the entire line\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-worker-user-worker", "title": "Create Worker User (<code>worker</code>)", "text": "<pre><code>sudo adduser worker\n</code></pre> <p>Use a <code>CamelCase</code> password.</p> <p>Printing (long install) (Must do it now, to set permissions just once, which include <code>lpadmin</code>)</p> <p>Added 2024.03.12</p> <pre><code>sudo apt-get update\n</code></pre> This is the long time to install one<pre><code>sudo apt-get install -y cups\n</code></pre> Assign the necessary groups<pre><code>sudo usermod -a -G video,input,lpadmin worker\n</code></pre>"}, {"location": "imaging/sd-image-building/#check-the-settings", "title": "Check the settings", "text": "<pre><code>ls -l /dev/input/\ngroups worker\n</code></pre>"}, {"location": "imaging/sd-image-building/#setting-up-printer-user", "title": "Setting Up Printer User", "text": "<pre><code>sudo adduser printer\n# Password: printer\n\nsudo usermod -a -G lpadmin printer\n</code></pre>"}, {"location": "imaging/sd-image-building/#wlan-pi5-settings", "title": "WLAN Pi5 Settings", "text": "<p>In <code>sudo raspi-config</code>, under Localization Options, there is a choice for WLAN legal settings:</p> <pre><code>Localization... &gt; WLAN Country... &gt; US\n</code></pre> <p>FixMe: Figure out what we need to set for WLAN legal.</p> <p>FixMe: Does this also impact Bluetooth?</p>"}, {"location": "imaging/sd-image-building/#get-updates-and-required-installs", "title": "Get Updates and Required Installs", "text": "<p>Reference: Minimal RPi Kiosk Setup</p> <pre><code># sudo apt-get update # now included in pi_hmi\n\n# Install browser and X environment\n# sudo apt-get install -y --no-install-recommends xserver-xorg-video-all \\\n#   xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils \\\n#   chromium-browser unclutter # now in pi_hmi\n\n# Install Chromium trust store manager\n# sudo apt-get install -y libnss3-tools # now in pi_hmi\n\n# Install MyQR for QR code generation\n# sudo apt-get install -y python3-pip\n# sudo pip3 install MyQR\n# sudo apt-get install libopenjp2-7 # now in pi_hmi\n\n# Install Privoxy for whitelisting\n# sudo apt-get install -y privoxy # now in pi_hmi\n</code></pre> <p>Note: The above packages are now included in <code>pi_hmi</code>. Continue installing manually until <code>hmi</code> can be debugged.</p> <p>Log in as cah-pi-su user before proceeding.</p>"}, {"location": "imaging/sd-image-building/#install-necessary-packages", "title": "Install Necessary Packages", "text": ""}, {"location": "imaging/sd-image-building/#step-1-update-and-upgrade", "title": "Step 1: Update and Upgrade", "text": "<pre><code>sudo apt -y update\n\n# This next one takes a while\nsudo apt -y upgrade\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-2-fix-unfinished-package-configurations", "title": "Step 2: Fix Unfinished Package Configurations", "text": "<pre><code>sudo dpkg --configure -a\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-3-install-core-dependencies", "title": "Step 3: Install Core Dependencies", "text": "<pre><code>sudo apt-get install -y privoxy\nsudo apt-get install -y python3-pip\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-4-fix-python-package-manager-issues-if-needed", "title": "Step 4: Fix Python Package Manager Issues (If Needed)", "text": "<p>Added 2024.03.12</p> <p>Reference: Fixing Pip Error in Linux</p> <pre><code>ls -l /usr/lib/ | fgrep python3\n# Modify the following line based on the Python version found above\nsudo rm /usr/lib/python3.11/EXTERNALLY-MANAGED\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-5-install-myqr-for-qr-code-generation", "title": "Step 5: Install MyQR for QR Code Generation", "text": "<pre><code>sudo pip3 install MyQR\n</code></pre> <p>If the above fails, try:</p> <pre><code>sudo apt-get install -y cmake\nsudo apt-get install -y libblas-dev\nsudo apt install -y libblas3 liblapack3 liblapack-dev libblas-dev\n\n# Fix dependencies for MyQR\nsudo apt install -y libjpeg-dev zlib1g-dev\nsudo pip3 install MyQR --break-system-packages\n</code></pre> <p>Reference: Pillow Installation</p> <p>Test the installation:</p> <pre><code>sudo python3 -c \"from MyQR import myqr as mq\"\n# Should return no errors\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-6-install-additional-packages", "title": "Step 6: Install Additional Packages", "text": "<pre><code>sudo apt-get install libopenjp2-7\n\nsudo apt-get install -y --no-install-recommends \\\n    xserver-xorg-video-all xserver-xorg-input-all \\\n    xserver-xorg-core xinit x11-xserver-utils \\\n    chromium-browser unclutter\n\nsudo apt-get install -y libnss3-tools\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-7-install-screen-capture-tool", "title": "Step 7: Install Screen Capture Tool", "text": "<pre><code>sudo apt-get install -y scrot\n</code></pre>"}, {"location": "imaging/sd-image-building/#step-8-install-required-python-modules-and-utilities", "title": "Step 8: Install Required Python Modules and Utilities", "text": "<pre><code># Required for pi_bluetooth and pi_runner\nsudo pip3 install apscheduler --break-system-packages\n\n# Install system monitoring tools\nsudo apt-get install -y tmux\nsudo apt-get install -y nmon\n</code></pre> <p>To use nmon:</p> <pre><code>nmon\n</code></pre>"}, {"location": "imaging/sd-image-building/#edits-to-configure", "title": "Edits to Configure", "text": ""}, {"location": "imaging/sd-image-building/#privoxy-configuration", "title": "Privoxy Configuration", "text": "<pre><code>sudo vi /etc/privoxy/config\n</code></pre> <ul> <li>Find the line that starts with <code>#debug 1024</code> and uncomment it.</li> <li>This enables logging to <code>/var/log/privoxy/logfile</code>, which starts automatically based on the config change.</li> <li>Uncommenting this line allows us to collect details about what is blocked.</li> </ul>"}, {"location": "imaging/sd-image-building/#cups-configuration", "title": "CUPS Configuration", "text": "<pre><code>sudo vi /etc/cups/cupsd.conf\n</code></pre> Change the line:<pre><code>DefaultAuthType Basic\n</code></pre> To:<pre><code>DefaultAuthType None\n</code></pre> <ul> <li>Restart CUPS:</li> </ul> <pre><code>sudo systemctl restart cups\n</code></pre>"}, {"location": "imaging/sd-image-building/#security-audit", "title": "Security Audit", "text": ""}, {"location": "imaging/sd-image-building/#install-lynis-security-audit-tool", "title": "Install Lynis Security Audit Tool", "text": "<p>Lynis Documentation</p> <pre><code>sudo apt-get install -y lynis\n</code></pre>"}, {"location": "imaging/sd-image-building/#initial-test-optional", "title": "Initial Test (Optional)", "text": "<pre><code>sudo lynis audit system -Q\n</code></pre>"}, {"location": "imaging/sd-image-building/#initial-result", "title": "Initial result", "text": "<p>As of 2021-07-24</p> <pre><code>Hardening index : 55 [###########         ]\nTests performed : 213\nPlugins enabled : 1\n</code></pre>"}, {"location": "imaging/sd-image-building/#system-updates", "title": "System Updates", "text": "<pre><code>sudo apt update\nsudo apt upgrade -y\n</code></pre>"}, {"location": "imaging/sd-image-building/#system-hardening", "title": "System Hardening", "text": ""}, {"location": "imaging/sd-image-building/#ssh-configuration-now-done-in-pi_security", "title": "SSH Configuration (Now done in <code>pi_security</code>)", "text": "<pre><code>sudo vi /etc/ssh/sshd_config\n</code></pre>"}, {"location": "imaging/sd-image-building/#update-system-message", "title": "Update System Message", "text": "<pre><code>sudo vi /etc/init.net\n</code></pre> Expected Output<pre><code>********************************************************************************\nThis computer system and associated networks are the property of and for the\nsole business use of Cardinal Health, Inc. authorized users. Cardinal Health\nreserves the right to monitor computer and network usage, and your use of\nCardinal systems constitutes consent to such monitoring. The company's\ncomputers and the proprietary data and information stored on them remain at all\ntimes the property of Cardinal Health, Inc. Unauthorized access to this system\nis strictly forbidden. This server and your actions after login are monitored.\n********************************************************************************\n</code></pre>"}, {"location": "imaging/sd-image-building/#restrict-access-to-compilers", "title": "Restrict Access to Compilers", "text": "<pre><code>sudo chmod o-rx /usr/bin/gcc\nsudo chmod o-rx /usr/bin/g++\n</code></pre>"}, {"location": "imaging/sd-image-building/#disable-usb-storage", "title": "Disable USB Storage", "text": "<p>Reference</p> <pre><code>sudo vi /etc/modprobe.d/blacklist.conf\n</code></pre> <pre><code>blacklist usb-storage\n</code></pre> <PERSON><PERSON> doing this test, unless you want to know the numbers now <p><pre><code>sudo lynis audit system -Q\n</code></pre> <pre><code>Hardening index : 55 [###########         ]\nTests performed : 213\nPlugins enabled : 1\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#network-configuration", "title": "Network Configuration", "text": ""}, {"location": "imaging/sd-image-building/#network-manager-installation", "title": "Network Manager Installation", "text": "<pre><code>sudo apt-get update\nsudo apt -y --fix-broken install\nsudo apt-get install -y network-manager\n</code></pre>"}, {"location": "imaging/sd-image-building/#set-network-manager-as-primary", "title": "Set Network Manager as Primary", "text": "<pre><code>sudo vi /etc/dhcpcd.conf\n</code></pre> Add the following lines at the end:<pre><code>denyinterfaces wlan0\ndenyinterfaces wlan1\n</code></pre>"}, {"location": "imaging/sd-image-building/#disable-wifi-mac-address-randomization", "title": "Disable WiFi MAC Address Randomization", "text": "<p>Reference</p> <p>Configure Network Manager to not randomize the WiFi Mac Address</p> <pre><code>sudo vi /etc/NetworkManager/conf.d/100-disable-wifi-mac-randomization.conf\n</code></pre> <pre><code>[connection]\nwifi.mac-address-randomization=1\n\n[device]\nwifi.scan-rand-mac-address=no\n</code></pre>"}, {"location": "imaging/sd-image-building/#reboot-to-apply-changes", "title": "Reboot to Apply Changes", "text": "<p>To fix the randomized <code>wlan0</code> mac address</p> <pre><code>sudo reboot\n</code></pre>"}, {"location": "imaging/sd-image-building/#config-browser", "title": "Config <PERSON>", "text": "<p>Source: Raspberry Pi Kiosk Setup</p>"}, {"location": "imaging/sd-image-building/#configure-xinitrc", "title": "Configure <code>.xinitrc</code>", "text": "<pre><code>sudo vi /home/<USER>/.xinitrc\n</code></pre> Contents<pre><code>#!/usr/bin/env sh\nxset -dpms\nxset s off\nxset s noblank\n\nscreen_width=\"$(fbset -s | awk '$1 == \"geometry\" {print $2}')\"\nscreen_height=\"$(fbset -s | awk '$1 == \"geometry\" {print $3}')\"\n\n# Disable keyboard shortcuts\nunclutter -idle 2 &amp;\nxmodmap -e \"keycode 37=\"\nxmodmap -e \"keycode 67=\"\nxmodmap -e \"keycode 105=\"\nxmodmap -e \"keycode 133=\"\nxmodmap -e \"keycode 134=\"\n\nrm -rf /home/<USER>/.config/chromium/Singleton*\n\nwhile true; do\n    /cardinal/browserstart\ndone;\n</code></pre> <pre><code>sudo chown -R worker:worker /home/<USER>/.xinitrc\n</code></pre> <p><code># https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login</code></p>"}, {"location": "imaging/sd-image-building/#set-up-local-html-directory", "title": "Set up local HTML directory", "text": "<pre><code>sudo mkdir /cardinal\nsudo mkdir /cardinal/localhtml\nsudo su\n\necho \"blank page\" &gt; /cardinal/localhtml/index.html\nexit\n</code></pre>"}, {"location": "imaging/sd-image-building/#configure-browser-startup-script", "title": "Configure browser startup script", "text": "<pre><code>sudo vi /cardinal/browserstart_default\n</code></pre> <p>The following file is also maintained by pi_runner, so make matching changes there.</p> Contents<pre><code>cp /cardinal/localhtml/chromium_Default_Preferences /home/<USER>/.config/chromium/Default/Preferences\n\nchromium-browser \\\n  --proxy-server=\"https=127.0.0.1:8118;http=127.0.01:8118\" \\\n  --window-size=$screen_width,$screen_height \\\n  --window-position=0,0 \\\n  --start-fullscreen \\\n  --incognito \\\n  --noerrdialogs \\\n  --disable-translate \\\n  --no-first-run \\\n  --fast \\\n  --fast-start \\\n  --disable-infobars \\\n  --disable-features=TranslateUI \\\n  --disable-features=Translate \\\n  --disk-cache-dir=/dev/null \\\n  --overscroll-history-navigation=0 \\\n  --disable-pinch \\\n  --kiosk \\\n  file:///cardinal/localhtml/index.html\n</code></pre> <pre><code>sudo chown -R worker:worker /cardinal/browserstart_default\nsudo chmod +x /cardinal/browserstart_default\nsudo cp /cardinal/browserstart_default /cardinal/browserstart\n</code></pre>"}, {"location": "imaging/sd-image-building/#startx-issues-on-raspberry-pi-5", "title": "Startx Issues on Raspberry Pi 5", "text": "<p>Added 2024-03-12</p> <pre><code>bash: startx: command not found\n</code></pre> <p>Reference</p> <pre><code>sudo apt install gldriver-test\n</code></pre> <p>Reference</p> <pre><code>sudo apt-get install xinit -y\n</code></pre>"}, {"location": "imaging/sd-image-building/#runner-fails-2024-03-12", "title": "Runner Fails (2024-03-12)", "text": "<pre><code>Mar 13 07:57:07 raspberry<PERSON> pi-runner[945]:\nzoneinfo._common.ZoneInfoNotFoundError: 'Multiple conflicting time zone configurations found:\\n/etc/timezone:\nEurope/London\\n/etc/localtime is a symlink to: America/New_York\\nFix the configuration, or set the time zone in a TZ environment variable.\\n'\n</code></pre>"}, {"location": "imaging/sd-image-building/#check-timezone", "title": "Check Timezone", "text": "<p>Check Current Timezone Configuration</p> <pre><code>cat /etc/timezone\nEurope/London\nls -l /etc/localtime\nlrwxrwxrwx 1 root root 38 Mar 12 15:12 /etc/localtime -&gt; ../usr/share/zoneinfo/America/New_York\n</code></pre> <p>Fix Timezone Issue</p> <pre><code>sudo rm /etc/localtime\n</code></pre> <p>Note</p> <p>After removing the conflicting timezone link, the system will use the timezone specified in <code>/etc/timezone</code></p>"}, {"location": "imaging/sd-image-building/#disable-pi5-power-button", "title": "Disable Pi5 Power Button", "text": "<p>Added 2024.03.12</p> <p>Reference</p> <pre><code>sudo vi /etc/systemd/logind.conf\n</code></pre> <p>Add the following configuration:</p> <pre><code>[Login]\nHandlePowerKey=ignore\nHandlePowerKeyLongPress=ignore\n</code></pre> <p>FixMe: Update/Upgrade Considerations</p> <p>Does this setting get modified when we do update/upgrade activity? If so, then do it at the end, or better yet, auto fix it if anyone changes it.</p>"}, {"location": "imaging/sd-image-building/#create-system-snapshot", "title": "Create System Snapshot", "text": "<p>Shut down the system to create a backup image:</p> <pre><code>sudo shutdown -h now\n</code></pre>"}, {"location": "imaging/sd-image-building/#make-image-of-the-16gb-card", "title": "Make Image of the 16GB Card", "text": "<p>Use Apple<PERSON><PERSON> Baker with shrink turned off:</p> <ul> <li>2024.03.28 using Pi OS 2023-12-11 → (<code>downloads/20240328/backup3</code>)</li> <li>2024.04.17 using Pi OS 2024-03-15 → (<code>downloads/20240417/backup1</code>)</li> <li>2024.04.23 using Pi OS 2024-03-15 → (<code>downloads/20240417/backup1a</code>)</li> <li>2024.11.25 → <code>Downloads/pi_image_2024.11.25.zip</code></li> </ul> <p>Testing</p> <p>Feel free to stop here, reboot, manually log in as worker, then manually run <code>startx</code></p>"}, {"location": "imaging/sd-image-building/#test-browser-process", "title": "Test Browser Process", "text": "<pre><code># On the device:\nstartx\n\n# On a remote SSH terminal\nps ax | fgrep chromium\n</code></pre>"}, {"location": "imaging/sd-image-building/#bluetooth-troubleshooting-for-pi45", "title": "Bluetooth Troubleshooting for Pi4/5", "text": "<p>Development Test - Not for Production</p> <p>This section contains experimental configurations for addressing Bluetooth issues on Pi4 and Pi5 devices.</p>"}, {"location": "imaging/sd-image-building/#initial-approach", "title": "Initial Approach", "text": "<p>Reference: Debian Forum (Near the bottom of the post)</p> <pre><code>sudo apt-get install pulseaudio-module-bluetooth -y\npulseaudio --kill\npulseaudio --start\n\nsudo journalctl --vacuum-time=1m\njournalctl -u pi-bluetooth.service --no-pager\n</code></pre>"}, {"location": "imaging/sd-image-building/#error-analysis", "title": "Error Analysis", "text": "<p>After attempting the above solution, the following errors were observed:</p> <pre><code>Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/vcp.c:vcp_init() D-Bus experimental not enabled\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init vcp plugin\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/mcp.c:mcp_init() D-Bus experimental not enabled\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init mcp plugin\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/bap.c:bap_init() D-Bus experimental not enabled\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init bap plugin\nApr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: Bluetooth management interface 1.22 initialized\nApr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/sap/server.c:sap_server_register() Sap driver initialization failed.\nApr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: sap-server: Operation not permitted (1)\nApr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: Failed to set privacy: Rejected (0x0b)\n</code></pre>"}, {"location": "imaging/sd-image-building/#alternative-solution-1", "title": "Alternative Solution 1", "text": "<p>Reference: Debian Forum</p> <pre><code>sudo apt-get install libspa-0.2-bluetooth -y\nsudo apt-get remove pulseaudio-module-bluetooth -y\nsystemctl --user start pulseaudio.socket\n</code></pre>"}, {"location": "imaging/sd-image-building/#alternative-solution-2", "title": "Alternative Solution 2", "text": "<p>Reference: Reddit</p> <p>A user reported fixing similar issues with the following commands:</p> <pre><code>sudo rmmod btusb\nsudo rmmod btintel\nsudo modprobe btintel\nsudo modprobe btusb\n</code></pre>"}, {"location": "imaging/sd-image-building/#additional-packages-20240417", "title": "Additional Packages (2024.04.17)", "text": "<p>Reference: Pi My Life Up</p> <pre><code>sudo apt install -y bluetooth pi-bluetooth bluez blueman\n</code></pre> <p>Result: No improvement observed</p>"}, {"location": "imaging/sd-image-building/#system-diagnostics", "title": "System Diagnostics", "text": "<p>Useful commands to check Bluetooth version information:</p> <pre><code>bluetoothctl --version\nbluetoothd --version\n</code></pre> <p>Also check <code>hciuart</code> status if needed.</p>"}, {"location": "imaging/sd-image-building/#version-comparisons-and-behaviors", "title": "Version Comparisons and Behaviors", "text": ""}, {"location": "imaging/sd-image-building/#sp40-image-old", "title": "SP.40 Image (Old)", "text": "<p><pre><code>bluetoothctl --version\nbluetoothd --version\n</code></pre> Output: <pre><code>bluetoothctl: 5.50\n5.50\n</code></pre></p> <p><pre><code>dmesg | tee | fgrep Bluetooth\n</code></pre> Output: <pre><code>(empty) # But maybe only empty of bluetooth, because it was pushed out the beginning of the log...\n</code></pre></p> <p><pre><code>uname -a\n</code></pre> Output: <pre><code>Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux\n</code></pre></p> <p><pre><code>dpkg --status bluez | fgrep Version\n</code></pre> Output: <pre><code>Version: 5.50-1.2~deb10u3+rpt1\n</code></pre></p> <p>Result: RS6000 worked as expected</p>"}, {"location": "imaging/sd-image-building/#after-update", "title": "After Update", "text": "<pre><code>sudo apt-get update\nreboot\n</code></pre> <p>Version check after update: <pre><code>bluetoothctl: 5.50\n5.50\n</code></pre></p> <p>Result: RS6000 worked as expected</p>"}, {"location": "imaging/sd-image-building/#after-upgrade", "title": "After Upgrade", "text": "<pre><code>sudo apt-get upgrade\nreboot\n</code></pre> <p>Version check after upgrade: <pre><code>bluetoothctl: 5.50\n5.50\n</code></pre></p> <p>System information: <pre><code>Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux\n</code></pre></p> <p>Bluetooth package version: <pre><code>Version: 5.50-1.2~deb10u4\n</code></pre></p> <p>Result: RS6000 stopped working</p> <p>Update to latest pi-bluetooth to handle tmux changes: Still failed.</p>"}, {"location": "imaging/sd-image-building/#kernel-messages-after-upgrade", "title": "Kernel Messages After Upgrade", "text": "<pre><code>dmesg | tee | fgrep Bluetooth\n</code></pre> <pre><code>[    8.455095] Bluetooth: Core ver 2.22\n[    8.455217] Bluetooth: HCI device and connection manager initialized\n[    8.455342] Bluetooth: HCI socket layer initialized\n[    8.455403] Bluetooth: L2CAP socket layer initialized\n[    8.455454] Bluetooth: SCO socket layer initialized\n[    8.491685] Bluetooth: HCI UART driver ver 2.3\n[    8.491710] Bluetooth: HCI UART protocol H4 registered\n[    8.491849] Bluetooth: HCI UART protocol Three-wire (H5) registered\n[    8.506279] Bluetooth: HCI UART protocol Broadcom registered\n[    9.130041] Bluetooth: BNEP (Ethernet Emulation) ver 1.3\n[    9.130061] Bluetooth: BNEP filters: protocol multicast\n[    9.130106] Bluetooth: BNEP socket layer initialized\n[ 1385.452174] Bluetooth: HIDP (Human Interface Emulation) ver 1.2\n[ 1385.452213] Bluetooth: HIDP socket layer initialized\n</code></pre>"}, {"location": "imaging/sd-image-building/#sp47-image-middle", "title": "SP.47 Image (Middle)", "text": "<p>Version information: <pre><code>bluetoothctl: 5.50\n5.50\n</code></pre></p> <p>Kernel messages: <pre><code>[    7.884677] Bluetooth: Core ver 2.22\n[    7.884794] Bluetooth: HCI device and connection manager initialized\n[    7.884821] Bluetooth: HCI socket layer initialized\n[    7.884840] Bluetooth: L2CAP socket layer initialized\n[    7.884874] Bluetooth: SCO socket layer initialized\n[    7.934892] Bluetooth: HCI UART driver ver 2.3\n[    7.934918] Bluetooth: HCI UART protocol H4 registered\n[    7.935024] Bluetooth: HCI UART protocol Three-wire (H5) registered\n[    7.935448] Bluetooth: HCI UART protocol Broadcom registered\n[    8.494406] Bluetooth: BNEP (Ethernet Emulation) ver 1.3\n[    8.494432] Bluetooth: BNEP filters: protocol multicast\n[    8.494466] Bluetooth: BNEP socket layer initialized\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#pi5-image-current", "title": "Pi5 Image (Current)", "text": "<p>Version information: <pre><code>bluetoothctl: 5.66\nbluetoothd: 5.66\n</code></pre></p> <p>System information: <pre><code>Linux cah-rp-ec885c8aa5f46f0d 6.6.20+rpt-rpi-v8 #1 SMP PREEMPT Debian 1:6.6.20-1+rpt1 (2024-03-07) aarch64 GNU/Linux\n</code></pre></p> <p>Bluetooth package version: <pre><code>Version: 5.66-1+rpt1+deb12u1\n</code></pre></p> <p>Kernel messages: <pre><code>[    5.664370] Bluetooth: Core ver 2.22\n[    5.664402] NET: Registered PF_BLUETOOTH protocol family\n[    5.664404] Bluetooth: HCI device and connection manager initialized\n[    5.664411] Bluetooth: HCI socket layer initialized\n[    5.664415] Bluetooth: L2CAP socket layer initialized\n[    5.664420] Bluetooth: SCO socket layer initialized\n[    5.674567] Bluetooth: HCI UART driver ver 2.3\n[    5.674575] Bluetooth: HCI UART protocol H4 registered\n[    5.674609] Bluetooth: HCI UART protocol Three-wire (H5) registered\n[    5.674840] Bluetooth: HCI UART protocol Broadcom registered\n[    6.042366] Bluetooth: hci0: BCM: chip id 107\n[    6.043130] Bluetooth: hci0: BCM: features 0x2f\n[    6.044254] Bluetooth: hci0: BCM4345C0\n[    6.044261] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0000\n[    6.047494] Bluetooth: hci0: BCM4345C0 'brcm/BCM4345C0.raspberrypi,5-model-b.hcd' Patch\n[    6.759113] Bluetooth: hci0: BCM: features 0x2f\n[    6.760571] Bluetooth: hci0: BCM43455 37.4MHz Raspberry Pi 3+-0190\n[    6.760583] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0382\n[    6.761807] Bluetooth: hci0: BCM: Using default device address (43:45:c0:00:1f:ac)\n[   13.840787] Bluetooth: BNEP (Ethernet Emulation) ver 1.3\n[   13.840794] Bluetooth: BNEP filters: protocol multicast\n[   13.840800] Bluetooth: BNEP socket layer initialized\n[   13.842897] Bluetooth: MGMT ver 1.22\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#set-image-version", "title": "Set Image Version", "text": "<p>Must be ready before runner install</p> <pre><code>sudo su\n\n# This sets the base image value that gets reported\necho \"Image version: 2.4.1\" &gt; /cardinal/image_ver.txt\n# 2024.03.13 Bookworm Build for Pi5\necho \"Image version: 2.4.2\" &gt; /cardinal/image_ver.txt\n\nexit\n</code></pre>"}, {"location": "imaging/sd-image-building/#fix-numpy-issues", "title": "Fix NumPy Issues", "text": "<pre><code>sudo pip3 uninstall numpy -y\nsudo apt install python3-numpy\n</code></pre>"}, {"location": "imaging/sd-image-building/#load-pi-runner", "title": "Load <PERSON>", "text": ""}, {"location": "imaging/sd-image-building/#get-raspberry-pi-serial-number", "title": "Get Raspberry Pi Serial Number", "text": "<pre><code># To collect serial number like: 10000000e3669edf\ncat /proc/cpuinfo | fgrep Serial\n</code></pre>"}, {"location": "imaging/sd-image-building/#manual-installation", "title": "Manual Installation", "text": "pi_runner<pre><code>sudo vi /cardinal/pi_runner.py\n# Copy and paste the content of the source file\n\ncd /cardinal\nsudo python3 pi_runner.py install\n</code></pre> <p>Important Note</p> <p>Once running and checking into Slicer, if there is an update to settings ready for it in Slicer, then a reboot may occur automatically. This is normal behavior.</p>"}, {"location": "imaging/sd-image-building/#verify-registration", "title": "Verify Registration", "text": "<p>Check that the device appears in Slicer: <pre><code>https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf\n</code></pre></p>"}, {"location": "imaging/sd-image-building/#install-components-via-slicer", "title": "Install Components via Slicer", "text": "<p>The following components should be loaded through the Slicer interface (Runner must be loaded and running first):</p> <ul> <li>pi_bluetooth</li> <li>pi_config</li> <li>pi_hmi</li> <li>pi_logging</li> <li>pi_monitor</li> <li>pi_network</li> <li>pi_organization</li> <li>pi_security</li> <li>pi_settings</li> </ul> <pre><code>sudo reboot\n</code></pre> <p>Testing Required</p> <p>Stop here and manually test by logging in as worker, then running <code>startx</code></p>"}, {"location": "imaging/sd-image-building/#configure-autologin-for-worker", "title": "Configure Autologin for Worker", "text": "<p>Command Line Only Login</p> <p>This configuration gets to the command line prompt only. Do not expect auto startx or anything further.</p>"}, {"location": "imaging/sd-image-building/#for-pi5-and-latest-os-after-20231030", "title": "For Pi5 and Latest OS (After 2023.10.30)", "text": "<p>Reference</p> <pre><code>sudo vi /etc/systemd/logind.conf\n# Replace \"#NAutoVTs=6\" with \"NAutoVTs=1\"\n</code></pre>"}, {"location": "imaging/sd-image-building/#edit-getty-service", "title": "Edit Getty Service", "text": "<pre><code>sudo <NAME_EMAIL>\n\n# sudo vi /etc/systemd/system/<EMAIL>.d\n</code></pre> <p>Add the following above \"### Lines below this comment will be discarded\":</p> <pre><code>[Service]\nExecStart=\nExecStart=-/usr/sbin/agetty --autologin worker --noclear %I $TERM\n</code></pre> <p>Save with <code>Ctrl+X</code>, <code>Y</code>, <code>Enter</code></p> Old Method (For Reference) <pre><code>sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf\n# Add:\n[Service]\nExecStart=\nExecStart=-/sbin/agetty --autologin worker --noclear %I \\$TERM\n\nsudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>\n</code></pre>"}, {"location": "imaging/sd-image-building/#test-autologin", "title": "Test Autologin", "text": "<pre><code>sudo reboot\n</code></pre>"}, {"location": "imaging/sd-image-building/#configure-automatic-startx-on-login", "title": "Configure Automatic Startx on Login", "text": ""}, {"location": "imaging/sd-image-building/#add-to-workers-bash-profile", "title": "Add to Worker's Bash Profile", "text": "<pre><code>sudo vi /home/<USER>/.bash_profile\n</code></pre> <p>Add:</p> <pre><code>if [ -z $DISPLAY ] &amp;&amp; [ $(tty) = /dev/tty1 ]\nthen\n  startx\nfi\n</code></pre> <p>Set ownership:</p> <pre><code>sudo chown -R worker:worker /home/<USER>/.bash_profile\n</code></pre>"}, {"location": "imaging/sd-image-building/#verify-hmi-installation", "title": "Verify HMI Installation", "text": "<p>The HMI install script should no longer be present when installation is complete:</p> <pre><code># Check that tempscript is gone (installation may take up to 10 minutes)\nls -l /cardinal/pi_hmi_tempscript\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-test-image-backup", "title": "Create Test Image Backup", "text": "<p>Make a backup at this stage:</p> <pre><code># Recommended backup naming conventions:\n# 2024.04.17 -&gt; backup2\n# 2024.04.23 -&gt; starting from backup1b, worked to here, then save as -&gt; 20240417/backup2b\n</code></pre>"}, {"location": "imaging/sd-image-building/#test-gui-performance", "title": "Test GUI Performance", "text": "<p>Reboot and test the GUI:</p> <pre><code>sudo reboot\n</code></pre>"}, {"location": "imaging/sd-image-building/#browser-security-testing", "title": "Browser Security Testing", "text": "<p>Reference: Function Keys in Google Chrome</p>"}, {"location": "imaging/sd-image-building/#blocked-shortcuts", "title": "Blocked Shortcuts", "text": "<p>The following keyboard combinations should be blocked:</p> <ul> <li><code>Ctrl+O</code> (open file)</li> <li><code>Ctrl+Alt+F2</code> (opens terminal)</li> <li><code>Ctrl+W</code> (close window)</li> <li><code>Alt+Esc</code> (incognito)</li> </ul> <p>Dont want to allow F1, but cannot block F1</p> <ul> <li><code>F1</code> (opens help, should show a fail page due to Privoxy blacklist)</li> </ul>"}, {"location": "imaging/sd-image-building/#allowed-shortcuts", "title": "Allowed Shortcuts", "text": "<p>Should be covered by do while loop:</p> <ul> <li><code>Alt+F4</code> (exit) - Use this to escape from F1 result screens</li> </ul>"}, {"location": "imaging/sd-image-building/#test-bookmark-integration", "title": "Test Bookmark Integration", "text": "<p>Load bookmarks via the Slicer interface and test all links on the Pi.</p>"}, {"location": "imaging/sd-image-building/#multi-device-testing", "title": "Multi-Device Testing", "text": ""}, {"location": "imaging/sd-image-building/#chromium-profile-test", "title": "Chromium Profile Test", "text": "<p>Load the card into a second pi, and see that there is not a warning about a duplicate chromium profile</p> <p>Test that the image works correctly across multiple devices:</p> <ol> <li> <p>Shut down the current Pi:    <pre><code>sudo shutdown -h now\n</code></pre></p> </li> <li> <p>Move the SD card to a second Raspberry Pi</p> </li> <li> <p>Verify it boots without warnings about duplicate Chromium profiles</p> </li> </ol>"}, {"location": "imaging/sd-image-building/#bluetooth-scanner-testing", "title": "Bluetooth Scanner Testing", "text": "<ol> <li>Configure two devices with Bluetooth enabled via the Slicer interface</li> <li>Start with all devices powered off, then power on the Raspberry Pi devices first</li> <li>Verify devices report \"(no devices seen)\"</li> <li> <p>Cold boot the scanner:</p> <ul> <li>Press the side button while inserting the battery</li> <li>Hold until it beeps</li> <li>Scan the one-time configuration barcode</li> <li>Confirm both devices show a pairing barcode after 15-30 seconds</li> </ul> </li> <li> <p>Pairing Tests:</p> <ul> <li>Pair to device 1 (may require two scans over 30-40 seconds)</li> <li>Pair to device 2 (should unpair from device 1 after 30-60 seconds)</li> <li>Remove scanner battery for at least 10 minutes</li> <li>Verify both devices revert to \"(searching..)\"</li> <li>Power up scanner (should not auto-reconnect)</li> <li>Manually pair to device 1, then to device 2</li> <li>With scanner powered, reboot devices and verify no automatic pairing occurs within 10 minutes</li> </ul> </li> </ol>"}, {"location": "imaging/sd-image-building/#finalize-image-for-distribution", "title": "Finalize Image for Distribution", "text": ""}, {"location": "imaging/sd-image-building/#system-cleanup", "title": "System Cleanup", "text": "<p>Log in as root:</p> <pre><code>sudo su\n</code></pre> <p>Clean packages:</p> <p>All of these as a single copy and paste into the terminal:</p> <pre><code>apt-get clean\napt-get -y autoremove --purge\n</code></pre>"}, {"location": "imaging/sd-image-building/#remove-temporary-files", "title": "Remove Temporary Files", "text": "<pre><code>rm -rf /Downloads\nrm -rf /home/<USER>/*\nrm -rf /cardinal/save_values\n</code></pre>"}, {"location": "imaging/sd-image-building/#stop-services", "title": "Stop Services", "text": "<pre><code>systemctl stop pi-bluetooth.service\nsystemctl stop pi-config.service\nsystemctl stop pi-hmi.service\nsystemctl stop pi-logging.service\nsystemctl stop pi-monitor.service\nsystemctl stop pi-network.service\nsystemctl stop pi-organization.service\nsystemctl stop pi-runner.service\nsystemctl stop pi-security.service\nsystemctl stop pi-settings.service\n</code></pre>"}, {"location": "imaging/sd-image-building/#clean-log-files", "title": "Clean Log Files", "text": "<pre><code>find /var/log -type f -regex \".*\\.gz$\" -delete\nfind /var/log -type f -regex \".*\\.[0-9]$\" -delete\nrm /var/log/*\n</code></pre>"}, {"location": "imaging/sd-image-building/#reset-configuration", "title": "Reset Configuration", "text": "<pre><code>echo 'yes' &gt; /cardinal/needsexpand\necho -n \"0\" &gt; /cardinal/boot_count.txt\necho -n \"0\" &gt; /cardinal/grab_count.txt\ncp /cardinal/browserstart_default /cardinal/browserstart\nrm -rf /cardinal/localhtml/*\nrm -rf /cardinal/log/*\nrm -rf /cardinal/config_*\nrm /cardinal/call_home_locations.txt\nrm /cardinal/wifi_ssid_psk.txt\nrm /cardinal/screen_resolution.txt\nrm /cardinal/screen_zoom.txt\nrm /cardinal/wifi_config.txt\nrm /cardinal/call_home_locations.txt\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-default-startup-page", "title": "Create De<PERSON>ult Startup Page", "text": "<pre><code>mkdir /cardinal/localhtml/\necho '&lt;head&gt;&lt;meta http-equiv=\"refresh\" content=\"5\" &gt;&lt;/head&gt;&lt;body&gt;&lt;center&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;table border=\"1\" cellpadding=\"10\"&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Starting up...&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;3 boot ups is the normal sequence for a new image.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;Screen may not fill to edges until all boots complete.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=\"font-size:30px\"&gt;&lt;center&gt;If this screen does not disappear after 10 seconds,&lt;br&gt;then press Alt F4 to reset the screen.&lt;/center&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/center&gt;&lt;/body&gt;' &gt; /cardinal/localhtml/index.html\n</code></pre>"}, {"location": "imaging/sd-image-building/#final-shutdown", "title": "Final Shutdown", "text": "<pre><code>sudo shutdown -h now\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-distribution-image", "title": "Create Distribution Image", "text": ""}, {"location": "imaging/sd-image-building/#using-applepi-baker", "title": "Using ApplePi Baker", "text": "<ol> <li> <p>Install <PERSON><PERSON><PERSON> Baker from Tweaking4All <pre><code>https://www.tweaking4all.com/downloads/ApplePi-Baker-v2.dmg\n</code></pre></p> </li> <li> <p>Run <PERSON><PERSON>i-Baker from the Applications folder</p> </li> <li>Select the external disk to get the permissions popup</li> <li>Follow prompts to allow full disk access</li> <li>Reboot Mac to apply changes correctly</li> </ol> <p>MacOS USB Access</p> <p>On a Mac, JAMF settings may block USB memory card access. If you don't see the SD card reader, contact the Mac admin to enable the Self Service item 'Remove DLP-15.0', then run that program.</p>"}, {"location": "imaging/sd-image-building/#creating-the-image", "title": "Creating the Image", "text": "<p>Each Time:</p> <ol> <li><PERSON> <PERSON><PERSON><PERSON> Baker from Applications</li> <li>Select the disk</li> <li>In the lower right corner, enable the shrink option (2nd from left)</li> <li>Click \"Backup\" and name it (e.g., \"cah_pi_raw_image_2.4.2.SP.XX\")<ul> <li>For a 64GB card, this takes 11-12 minutes</li> <li>The ZIP should be under 2.5GB; if larger, you missed the shrink setting</li> </ul> </li> </ol> <p>Future:</p> <p>Resizing SD Images</p>"}, {"location": "imaging/sd-image-building/#test-the-image", "title": "Test the Image", "text": "<ol> <li>Use <PERSON><PERSON> to flash a test card from the newly created ZIP file<ul> <li>If CrowdStrike blocks this, email <EMAIL> for an exception</li> </ul> </li> <li>Insert the card in a Raspberry Pi and verify it boots correctly</li> </ol>"}, {"location": "imaging/sd-image-building/#slicer-upload-folder", "title": "Slicer Upload Folder", "text": ""}, {"location": "imaging/sd-image-building/#upload-to-slicer", "title": "Upload to Slicer", "text": "<ol> <li>Upload the image to Slicer</li> <li>Mark it as \"available for download\"</li> </ol>"}, {"location": "imaging/sd-image-building/#notify-users", "title": "Notify Users", "text": "<p>Inform the \"LATAM Raspberry Pi\" group that the new version is available:</p> <pre><code>Pi image version 2.Y.Z is available.\n\nhttps://slicer.cardinalhealth.net\nLog in\nGo to the \"download\" page to view version text file, and to download images.\n</code></pre>"}, {"location": "imaging/sd-image-building/#development-notes", "title": "Development Notes", "text": "<p>Development Status</p> <p>The following notes contain configurations that have been tested but are not yet integrated into the standard build process.</p>"}, {"location": "imaging/sd-image-building/#gcp-image-upload", "title": "GCP Image Upload", "text": ""}, {"location": "imaging/sd-image-building/#storage-location", "title": "Storage Location", "text": "<p>View in Google Cloud Console</p>"}, {"location": "imaging/sd-image-building/#datastore-management", "title": "Datastore Management", "text": "<p>Important</p> <p>Revert any old \"roll forward\" \"device_service_\" entries in the datastore to prevent accidental version rollbacks.</p>"}, {"location": "imaging/sd-image-building/#file-sharing", "title": "File Sharing", "text": "<ol> <li> <p>Generate Download Link <pre><code>https://storage.cloud.google.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.2.zip\n</code></pre></p> </li> <li> <p>Set Permissions</p> <ul> <li>Add users at file level (e.g., <EMAIL>) - (Did not work)</li> <li>Add users at bucket level for <code>pi-mgmt-pr-cah-distribution</code></li> </ul> </li> </ol>"}, {"location": "imaging/sd-image-building/#usb-control", "title": "USB Control", "text": ""}, {"location": "imaging/sd-image-building/#power-control-installation", "title": "Power Control Installation", "text": "<p>For power control of USB in the <code>pi_hmi</code> service:</p> <p>HUB CTRL: Raspbian</p> <ol> <li> <p>Using hub-ctrl <pre><code># Install snapd\nsudo apt install -y snapd\n\n# Install hub-ctrl\nsudo snap install hub-ctrl\n</code></pre></p> </li> <li> <p>Using uhubctl</p> <p>Github: UHUBCTL</p> <pre><code># Install dependencies\nsudo apt-get install -y libusb-1.0-0-dev\nsudo apt-get install -y git\n\n# Clone and build\ngit clone https://github.com/mvp/uhubctl\ncd uhubctl\nmake\nsudo make install\n</code></pre> </li> </ol>"}, {"location": "imaging/sd-image-building/#gcp-bucket-upload-process", "title": "GCP Bucket Upload Process", "text": "<ol> <li> <p>Access Storage Console</p> <ul> <li> <p>View in Google Cloud Console</p> </li> <li> <p>Wiki: GCP Cloud Storage Guide</p> </li> </ul> </li> <li> <p>Upload Steps</p> <ul> <li>Upload the zip file</li> <li>Click into the file</li> <li>Edit Permissions</li> <li>Set Entity=Public, Name=allUsers, Access=Reader</li> <li>Save permissions</li> </ul> </li> <li> <p>Generate Public URL</p> Example format:<pre><code>https://storage.googleapis.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.0.zip\n</code></pre> </li> </ol>"}, {"location": "imaging/sd-image-building/#generic-lite-image", "title": "Generic Lite Image", "text": ""}, {"location": "imaging/sd-image-building/#initial-image-setup", "title": "Initial Image Setup", "text": "<ol> <li> <p>Base Image Preparation</p> <ul> <li>Set up a generic new Lite image (not NOOBS)</li> <li>Expand to only 1 GB (consider doing in VM)</li> <li>Connect Pi to network</li> </ul> </li> <li> <p>Initial Configuration <pre><code>curl -k slicer.cardinalhealth.net/build\n</code></pre>     This initiates an SSH connection to:</p> <ul> <li>Install initial monitor</li> <li>Allow full compliance push</li> </ul> </li> <li> <p>File System Management</p> <ul> <li>Shrink the file system</li> <li>Re-expand on next boot</li> </ul> <p>Reference Links:</p> <ul> <li>Shrink Tool</li> <li>Shrinking Guide</li> <li>Reverse Root FS Expansion</li> </ul> </li> <li> <p>Image Creation Process</p> <ul> <li>Perform clean shutdown</li> <li>Create ISO/equivalent of card</li> <li>Burn to new card</li> <li>Allow startup process to expand card to full size</li> </ul> <p>Reference: SD Image Resizing Guide</p> </li> <li> <p>Expansion Commands <pre><code>sudo raspi-config --expand-rootfs\nsudo reboot\n\nraspi-config --expand-rootfs\n</code></pre></p> </li> </ol>"}, {"location": "imaging/sd-image-building/#password-change", "title": "Password Change", "text": "<pre><code>passwd\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-admin-user", "title": "Create Admin User", "text": "<pre><code># Create new admin user\nsudo adduser pisuperuser\n\n# Add to necessary groups\nsudo usermod -a -G video pisuperuser\nsudo adduser pisuperuser sudo\n</code></pre>"}, {"location": "imaging/sd-image-building/#create-worker-user", "title": "Create Worker User", "text": "<pre><code># Create worker user\nsudo adduser worker\n\n# Add to necessary groups\nsudo usermod -a -G video worker\nsudo usermod -a -G audio worker  # Added 2024.05.09\n</code></pre>"}, {"location": "imaging/sd-image-building/#configure-autologin", "title": "Configure Autologin", "text": "<p>Change the autologin user to the worker</p> <pre><code>sudo vi /etc/lightdm/lightdm.conf\n</code></pre> Replace:<pre><code>autologin-user=pi\n</code></pre> With:<pre><code>autologin-user=worker\n</code></pre>"}, {"location": "imaging/sd-image-building/#chromium-cah-root-certificate", "title": "Chromium CAH Root Certificate", "text": "<pre><code># Navigate to certificates directory\ncd /usr/local/share/ca-certificates\n\n# Convert and install certificate\nsudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt\nsudo update-ca-certificates\n\n# Update system packages\nsudo apt-get update\nsudo apt -y --fix-broken install\nsudo apt-get install -y libnss3-tools\n\n# Add certificate to NSS database\ncertutil -d sql:$HOME/.pki/nssdb -L\ncertutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i CAH-Root-CA-PR1.crt\n\n# Restart Browser\n\n# Set permissions for worker user\nsudo chmod 644 /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt\n\n# sudo mkdir -p /home/<USER>/.pki/nssdb\n# sudo certutil -d /home/<USER>/.pki/nssdb -N --empty-password\n\n# Add certificate for worker user\nsudo su worker\ncertutil -d sql:$HOME/.pki/nssdb -L\ncertutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt\nexit\n</code></pre>"}, {"location": "imaging/sd-image-building/#prevent-screen-blanking", "title": "Prevent Screen Blanking", "text": "<p>Raspberry Pi: Configuration Raspberry Pi: Screensaver</p> <ol> <li> <p>Edit Boot Configuration <pre><code>sudo vi /boot/cmdline.txt\n</code></pre></p> Add to end of line:<pre><code>consoleblank=0\n</code></pre> </li> <li> <p>Configure LightDM</p> <p>How to Prevent Screen going Blank</p> <pre><code>sudo vi /etc/lightdm/lightdm.conf\n</code></pre> Change:<pre><code>#xserver-command=X\n</code></pre> To:<pre><code>xserver-command=X -s 0 dpms\n</code></pre> </li> </ol>"}, {"location": "imaging/sd-image-building/#planning-and-untested-features", "title": "Planning and Untested Features", "text": "<p>Development Status</p> <p>The following features are planned but have not yet been tested or integrated into the standard build process.</p>"}, {"location": "imaging/sd-image-building/#step-1-list-completion", "title": "Step 1 List Completion", "text": "<ul> <li>Complete remaining items from the \"step1\" list</li> </ul>"}, {"location": "imaging/sd-image-building/#browser-configuration", "title": "Browser Configuration", "text": "<ol> <li>Auto-start Browser</li> <li> <p>Implement automatic browser startup on system boot</p> </li> <li> <p>Disable Update Warnings</p> </li> <li>Configure browser to suppress update notification popups</li> </ol>"}, {"location": "imaging/sd-image-building/#remote-access", "title": "Remote Access", "text": ""}, {"location": "imaging/sd-image-building/#vnc-setup", "title": "VNC Setup", "text": "<p>Reference: Remote Desktop VNC Guide</p> <ul> <li>VNC Viewer Download</li> </ul>"}, {"location": "imaging/sd-image-building/#security-enhancements", "title": "Security Enhancements", "text": ""}, {"location": "imaging/sd-image-building/#remove-default-pi-user", "title": "Remove Default Pi User", "text": "<p>Reference: Security Documentation</p>"}, {"location": "imaging/sd-image-building/#certificate-management", "title": "Certificate Management", "text": ""}, {"location": "imaging/sd-image-building/#rclocal-configuration", "title": "RC.Local Configuration", "text": "<p>Add to <code>/etc/rc.local</code>: <pre><code>update-ca-certificates\n</code></pre></p> <p>Reference: update-ca-certificates Manual</p>"}, {"location": "imaging/sd-image-building/#certificate-installation", "title": "Certificate Installation", "text": "<p>All <code>*.crt</code> files in <code>/usr/local/share/ca-certificates</code> are trusted.</p> <pre><code>cd /usr/local/share/ca-certificates\n\n# Convert and install certificates\nopenssl x509 -inform PEM -in entrust_l1k.cer -outform PEM -out entrust_l1k.crt\n\n# Install CAH certificates\nsudo openssl x509 -inform DER -in CAH-Dev-Root01.cer -out CAH-Dev-Root01.crt\nsudo openssl x509 -inform DER -in CAH-DMZ-Root01.cer -out CAH-DMZ-Root01.crt\nsudo openssl x509 -inform DER -in CAH-Issuing01.cer -out CAH-Issuing01.crt\nsudo openssl x509 -inform DER -in CAH-Issuing02.cer -out CAH-Issuing02.crt\nsudo openssl x509 -inform DER -in CAH-Issuing03.cer -out CAH-Issuing03.crt\nsudo openssl x509 -inform DER -in CAH-Issuing-CA-PR1.cer -out CAH-Issuing-CA-PR1.crt\nsudo openssl x509 -inform DER -in CAH-Issuing-CA-PR2.cer -out CAH-Issuing-CA-PR2.crt\nsudo openssl x509 -inform DER -in CAH-Root01.cer -out CAH-Root01.crt\nsudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt\nsudo openssl x509 -inform DER -in CAIssuing3.cer -out CAIssuing3.crt\nsudo openssl x509 -inform DER -in CAIssuing4.cer -out CAIssuing4.crt\nsudo openssl x509 -inform DER -in 'Cardinal Health JSS Built-in Certificate Authority.cer' -out 'Cardinal Health JSS Built-in Certificate Authority.crt'\nsudo openssl x509 -inform DER -in 'CAROOT 1.cer' -out 'CAROOT 1.crt'\nsudo openssl x509 -inform DER -in CARoot.cer -out CARoot.crt\nsudo openssl x509 -inform DER -in 'COMODO ECC Certification Authority.cer' -out 'COMODO ECC Certification Authority.crt'\nsudo openssl x509 -inform DER -in 'COMODO RSA Certification Authority.cer' -out 'COMODO RSA Certification Authority.crt'\nsudo openssl x509 -inform DER -in 'COMODO RSA Organization Validation Secure Server CA.cer' -out 'COMODO RSA Organization Validation Secure Server CA.crt'\n</code></pre> <pre><code># Did not help\nsudo update-ca-certificates\n# or just the one\nsudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt\n</code></pre>"}, {"location": "imaging/sd-image-building/#chromium-certificate-configuration", "title": "Chromium Certificate Configuration", "text": "<ol> <li> <p>In Chromium</p> <ul> <li>Settings → Advanced → Privacy and security → Manage certificates → Authorities</li> <li>Import → Other Locations → Computer → <code>/usr/local/share/ca-certificates</code></li> <li>Select <code>CAH-Root-CA-PR1.crt</code> → Open</li> <li>Refresh browser page (with the url that had the issue)</li> </ul> </li> <li> <p>Alternative Methods</p> <ul> <li>Chromium Certificate Management</li> <li>Arch Linux Chromium Guide</li> </ul> Configuration FilesCommand Line Options <pre><code>~/.pki/nssdb\n/home/<USER>/.config/chromium/Default/Preferences\n'/home/<USER>/.config/chromium/Default/Current Session'\n</code></pre> <ul> <li>Chromium Command Line Switches</li> <li>Running Chromium with Flags</li> <li>Raspberry Pi Touchscreen Kiosk Setup</li> </ul> </li> </ol>"}, {"location": "imaging/sd-image-building/#testing", "title": "Testing", "text": "<pre><code># Verify connectivity\ncurl https://slicer.cardinalhealth.net\n\n# Install and test with Lynx\nsudo apt-get install -y lynx\nlynx https://slicer.cardinalhealth.net\nlynx https://slicer.cardinalhealth.net/reports\n\n# openssl x509 -in /etc/ssl/certs/ca-certificates.crt -text -noout\n# restart:\n# sudo rm /etc/ssl/certs/ca-certificates.crt\n</code></pre>"}, {"location": "imaging/sd-image-building/#system-updates_1", "title": "System Updates", "text": ""}, {"location": "imaging/sd-image-building/#cve-and-kernel-updates", "title": "CVE and Kernel Updates", "text": "<p>Reference: Updating Documentation</p>"}, {"location": "imaging/sd-image-building/#keyboard-configuration", "title": "Keyboard Configuration", "text": ""}, {"location": "imaging/sd-image-building/#disable-specific-keys", "title": "Disable Specific Keys", "text": "<p>Reference: Making xmodmap Changes Permanent</p> <pre><code>sudo vi /usr/share/X11/xkb/keycodes/evdev\n</code></pre> <p>Add <code>//</code> to front of specified lines:</p> <pre><code>*** start ***\n//      &lt;LALT&gt; = 64;\n//      &lt;LCTL&gt; = 37;\n        &lt;SPCE&gt; = 65;\n//      &lt;RCTL&gt; = 105;\n//      &lt;RALT&gt; = 108;\n        // Microsoft keyboard extra keys\n//      &lt;LWIN&gt; = 133;\n//      &lt;RWIN&gt; = 134;\n\n        &lt;ESC&gt; = 9;\n//      &lt;FK01&gt; = 67;\n//      &lt;FK02&gt; = 68;\n//      &lt;FK03&gt; = 69;\n//      &lt;FK04&gt; = 70;\n//      &lt;FK05&gt; = 71;\n//      &lt;FK06&gt; = 72;\n//      &lt;FK07&gt; = 73;\n//      &lt;FK08&gt; = 74;\n//      &lt;FK09&gt; = 75;\n//      &lt;FK10&gt; = 76;\n//      &lt;FK11&gt; = 95;\n//      &lt;FK12&gt; = 96;\n\n//      &lt;VOL-&gt; = 122;\n*** end ***\n</code></pre>"}, {"location": "imaging/sd-image-building/#boot-configuration", "title": "Boot Configuration", "text": ""}, {"location": "imaging/sd-image-building/#startup-screen-management", "title": "Startup Screen Management", "text": "<pre><code>sudo vi /home/<USER>/.xinitrc\n</code></pre> <p>Add tvservice:</p> <p>We could do this inside of /cardinal/browserstart, which is managed in pi_runner</p> <pre><code>while true; do\n    tvservice -p\n    /cardinal/browserstart\ndone;\n</code></pre>"}, {"location": "imaging/sd-image-building/#boot-sequence-customization", "title": "Boot Sequence Customization", "text": "<p>Reference: Customizing Boot Screen</p> <ol> <li>Configure Console</li> </ol> <pre><code>sudo vi /boot/cmdline.txt\n</code></pre> <p>Set to <code>console=tty3</code></p> <ol> <li>Add Boot Parameters</li> </ol> <pre><code>splash quiet logo.nologo vt.global_cursor_default=0\n</code></pre> <ol> <li>Custom Boot Logo</li> </ol> <p>Reference: Custom Boot Screen Guide</p> <pre><code>sudo apt-get update\nsudo apt-get install -y fbi\n\nsudo vi /etc/systemd/system/splashscreen.service\n</code></pre> Add<pre><code>[Unit]\nDescription=Splash screen\nDefaultDependencies=no\nAfter=local-fs.target\n\n[Service]\nExecStart=/usr/bin/fbi -d /dev/fb0 --noverbose -a /opt/splash.png\nStandardInput=tty\nStandardOutput=tty\n\n[Install]\nWantedBy=sysinit.target\n</code></pre> <pre><code>sudo systemctl enable splashscreen\n</code></pre>"}, {"location": "imaging/sd-image-building/#testing-checklist", "title": "Testing Checklist", "text": ""}, {"location": "imaging/sd-image-building/#power-button", "title": "Power Button", "text": "<pre><code>PASS    - Press power button, verify no shutdown\nPASS    - Long press power button, verify no shutdown/reboot\n</code></pre>"}, {"location": "imaging/sd-image-building/#profilessettings-passfail-on-pi4pi5", "title": "Profiles/Settings (Pass/Fail on Pi4/Pi5)", "text": "<pre><code>F4P5    - Set to MP4 profile, verify video plays with sound\n         - Sound issues on Pi4: [Reference](https://www.omglinux.com/raspberry-pi-os-bookworm/)\nPASS    - Set to GIF profile, verify functionality\nPASS    - Set bluetooth enabled, connect scanner, verify operation\nF4P5    - Autodetect 720p screen (1360x768 on test device)\nFAIL    - Set to 720p resolution\n</code></pre>"}, {"location": "imaging/sd-image-building/#connectivity", "title": "Connectivity", "text": "<pre><code>PASS    - Connect to non-NAC ethernet, verify check-in\nP4?5    - Connect to IoT WiFi, verify check-in\n?4P5    - SSH from laptop to Pi (expect failure)\n?4P5    - SSH from slicer to Pi (expect success)\n?4F5    - WiFi reporting from scan data shows correct SSID\n</code></pre>"}, {"location": "imaging/sd-image-building/#bluetooth", "title": "Bluetooth", "text": "<pre><code>FAIL    - Connect scanner and scan test barcode\n</code></pre>"}, {"location": "imaging/sd-image-building/#bookworm-specific-updates", "title": "Bookworm-Specific Updates", "text": ""}, {"location": "imaging/sd-image-building/#hdmi-configuration", "title": "HDMI Configuration", "text": "<p>(2024.04.22)</p> <p>Reference: HDMI Output Configuration</p> <p>Reference: HDMI Mode Documentation</p> <p>Pi5 Hardware Decoding</p> <p>The Raspberry Pi 5 includes H.265 (HEVC) hardware decoding enabled by default. A hardware codec licence key is not needed.</p>"}, {"location": "imaging/sd-image-building/#audio-configuration", "title": "Audio Configuration", "text": "<p>(2024.05.09)</p>"}, {"location": "imaging/sd-image-building/#pi4-hdmi-audio-issues", "title": "Pi4 HDMI Audio Issues", "text": "<p>Reference: Bookworm Feedback</p> <p>Reference: DietPi Forum</p> <pre><code>cat /proc/asound/cards\n</code></pre>"}, {"location": "imaging/sd-image-building/#audio-backend-options", "title": "Audio Backend Options", "text": "<p>Reference: Audio Configuration</p> <p>Audio Backend</p> <p>Use this option to switch between PulseAudio and PipeWire backends. PipeWire was introduced in Bookworm.</p> <p>Not yet answered</p> <p>Raspberry Pi: Forum Topic</p>"}, {"location": "imaging/sd-image-building/#troubleshooting-steps", "title": "Troubleshooting Steps", "text": "<ol> <li> <p>Check Audio Devices <pre><code>cat /proc/asound/cards\n</code></pre></p> </li> <li> <p>System Information <pre><code>sudo apt-get install inxi -y\ninxi -ACSxxz\n</code></pre></p> </li> <li> <p>Config.txt Settings <pre><code># Required settings\ndtparam=audio=on\ndisable_overscan=1\nhdmi_force_hotplug=1\nhdmi_drive=2\n</code></pre></p> </li> <li> <p>Pi4 Specific Settings <pre><code>dtoverlay=vc4-fkms-v3d\nmax_framebuffers=2\n</code></pre></p> </li> <li> <p>Audio Control</p> <p>Reference: Speaker Troubleshooting</p> <pre><code>sudo apt-get install -y pavucontrol\npavucontrol\n</code></pre> <p>Ensure auto-mute is disabled.</p> </li> </ol>"}, {"location": "pi/applications/", "title": "Raspberry Pi Applications", "text": "<p>There are many \"applications\" that we run on the Raspberry Pi:</p>"}, {"location": "pi/applications/#monitor", "title": "Monitor", "text": "<p>This is the simplest application that should change the least. Its purpose is to call home to the server every 30 minutes and report the IP address of the device so that we can remote back into it using SSH.</p>"}, {"location": "pi/applications/#runner", "title": "Runner", "text": "<p>This application also checks into the server once a minute to check for new content, including profiles, application updates, and settings.</p>"}, {"location": "pi/applications/#localhost-web-servers", "title": "Localhost Web Servers", "text": "<p>(Search for <code>class MyServer</code>)</p> Port Application 6999 template 7000 config 7010 hmi H.5.1 (builds profile-specific dynamic pages to be shown locally) 7020 logging 7040 network_* 7080 bluetooth"}, {"location": "pi/applications/#file-access", "title": "File Access", "text": "<ul> <li><code>file:///cardinal/localhtml/index.html</code> → hmi</li> </ul> Info <p>File interactions were originally used because they were simple to implement. However, as more features were needed (such as POST of data or dynamic content), file access became cumbersome. To address this, <code>from http.server import</code> HTTPServer was introduced as a lightweight solution, which is only accessed on the device itself.</p> <p>End of document.</p>"}, {"location": "pi/run-book/", "title": "Raspberry Pi Run Book", "text": ""}, {"location": "pi/run-book/#introduction", "title": "Introduction", "text": "<p>The Raspberry Pi project aims to provide clients with a low-cost computer that enables web-based access to designated applications.</p> <p>Key Features:</p> <ul> <li>Automatic startup into a full-screen web browser</li> <li>Limited user interaction (bookmarks only, no OS access)</li> <li>Optional auto-click bookmarks (for sign boards, delay of 25s)</li> </ul>"}, {"location": "pi/run-book/#slicer-overview", "title": "Slicer Overview", "text": "<p>Slicer is a web application for managing Raspberry Pi devices.</p> Key Functions of <PERSON>licer <ul> <li>Device Visibility: Tracks all registered devices.</li> <li>Usage Profiles: Manages settings like time zones and bookmarks.</li> <li>Version Management: Allows upgrades/rollbacks for device services.</li> <li>Access Control: Restricts features based on user permissions.</li> <li>Security Compliance: Runs HTTPS only (port 443), hosted on GCP (mac-mgmt-pr-cah).</li> </ul> <p>Configure a Pi using Slicer: ▶️ Watch Configuration Guide</p>"}, {"location": "pi/run-book/#github-repository", "title": "GitHub Repository", "text": "<p>All source code and project notes are stored in a private GitHub repository:</p> <p>GitHub Repository (Restricted Access)</p> <p>Admin Access: <PERSON> &amp; <PERSON></p>"}, {"location": "pi/run-book/#security-compliance-review", "title": "Security Compliance Review", "text": "<p>Review Date: 2021.08.26 This review aligns with security recommendations from <PERSON> (Email: Aug 12, 2021, Jira: EUDE-773)</p> Security Controls &amp; Responses <p>1️⃣ Device Theft Prevention 🔹 Pending - Expected resolution via an orderable 'kit'.</p> <p>2️⃣ Centralized Management Tool ✅ Slicer serves as the central tool for device configuration and updates.</p> <p>3️⃣ Inventory Tracking ❌ Out of scope for software; requires separate inventory management.</p> <p>4️⃣ Network Monitoring ✅ Devices check in every minute for monitoring (EUDE-1329).</p> <p>5️⃣ No Data Storage on Device ✅ Uses Chromium Incognito Mode (no downloads or history saved).</p> <p>6️⃣ OS Security &amp; Hardening ❌ Pending - Requires further policy implementation.</p> <p>7️⃣ Device Authentication via Certificates ✅ No longer required (EUDE-1331).</p> <p>8️⃣ Restrict Device Communication ✅ Enforced via IOT network controls.</p> <p>9️⃣ Least Privilege Access ✅ Enforced through whitelisted access only.</p> <p>🔟 No Password-Based Local Accounts ❌ Pending (EUDE-1333)</p> <p>🔢 CIS OS Baseline Compliance ✅ Uses Lynis Security Audits (EUDE-1350).</p>"}, {"location": "pi/run-book/#hardware-setup", "title": "Hardware Setup", "text": "Important: <p>On Raspberry Pi 4, when using only one monitor, plug it into HDMI0 (closest to power connection).</p> <p>End of document.</p>"}, {"location": "reference/branches/", "title": "Branches", "text": "<p>This section contains documentation for all branches in the project, organized by date.</p>"}, {"location": "reference/branches/#branch-documentation", "title": "Branch Documentation", "text": "<ul> <li>December 16, 2024 - <code>dwf_pi_temporary_file_management</code></li> <li>December 11, 2024 - <code>dwf_custom_display_board</code></li> </ul>"}, {"location": "reference/branches/#overview", "title": "Overview", "text": "<p>Each branch documentation includes:</p> <ul> <li>Branch name</li> <li>Date</li> <li>List of modified files</li> <li>Any additional relevant information</li> </ul> <p>For detailed information about specific branches, please refer to the individual branch documentation pages.</p>"}, {"location": "reference/coding-bible/", "title": "📖 Coding Bible", "text": "<p>Our Philosophy</p> <p>Code is read much more often than it is written. Prioritize clarity and maintainability over cleverness.</p>"}, {"location": "reference/coding-bible/#code-commandments", "title": "✨ Code Commandments", "text": "<p>Be mindful of your fellow developers and make your code READABLE. Writing clever code hurts the productivity of your team. Don't be that person.</p> <ul> <li>✅ If you don't know the answer, ask.</li> <li>✅ Before asking a question, do your research.</li> <li>✅ Always write tests – they have saved millions of lives already.</li> </ul>"}, {"location": "reference/coding-bible/#code-organization", "title": "🧩 Code Organization", "text": "<ul> <li>✅ Follow the Single Responsibility Principle - Functions and classes should do one thing only</li> <li>✅ Keep functions small and focused - Ideally under 30 lines</li> <li>✅ Use descriptive variable and function names - <code>calculate_total_price()</code> instead of <code>calc()</code></li> <li>✅ Comment on \"why\", not \"what\" - Code shows what, comments explain why</li> </ul>"}, {"location": "reference/coding-bible/#code-cleanliness", "title": "🧹 Code Cleanliness", "text": "<ul> <li>✅ Remove dead/commented-out code - We have version control for a reason</li> <li>✅ Avoid nested conditionals - Extract to helper functions when nesting gets deep</li> <li>✅ Use consistent formatting - Follow the team's style guide or use autoformatters</li> <li>✅ Handle errors gracefully - Never silently swallow exceptions</li> </ul> <p>Code Review Tip</p> <p>Review your own code first. If you can't explain a part clearly, refactor it.</p>"}, {"location": "reference/coding-bible/#version-control-commandments", "title": "🔄 Version Control Commandments", "text": "<p>💡 Are you making awesome changes to the repository? 👉 Branch out!</p> <p>💾 Always commit your changes to GitHub.</p> <p>🚀 Ready to ship? Create a Pull Request and follow the message format.</p> <p>🗑️ Clean up stale branches – just like spoiled food in a fridge.</p>"}, {"location": "reference/coding-bible/#branching-strategy", "title": "🌿 Branching Strategy", "text": "<ul> <li>✅ Branch per feature/bug fix - Never commit directly to main/master</li> <li>✅ Keep branches short-lived - Merge frequently to avoid drift</li> <li>✅ Use descriptive branch names - Format: <code>type/description</code> (e.g., <code>feat/user-authentication</code>)</li> <li>✅ Rebase before merging - Keep history clean and linear when possible</li> </ul>"}, {"location": "reference/coding-bible/#code-review-best-practices", "title": "🔍 Code Review Best Practices", "text": "<ul> <li>✅ Review changes in small batches - Aim for PRs under 400 lines</li> <li>✅ Be kind and constructive - Focus on the code, not the coder</li> <li>✅ Respond to reviews promptly - Don't let PRs languish</li> <li>✅ Use automated checks - Let CI/CD catch formatting and basic issues</li> </ul>"}, {"location": "reference/coding-bible/#pull-request-message-format", "title": "✅ Pull Request Message Format", "text": "<p>Use the format: <code>&lt;type&gt;(&lt;optional scope&gt;): subject</code> for all pull requests.</p> <p>Example:</p> <ul> <li>feat: A new feature</li> <li>chore(deps): update packages</li> </ul>"}, {"location": "reference/coding-bible/#commit-message-types", "title": "🏗️ Commit Message Types", "text": "Type Description <code>build</code> Build-related changes (e.g., npm updates, adding dependencies). <code>chore</code> Internal code changes (e.g., updating <code>.gitignore</code>, <code>.prettierrc</code>). <code>feat</code> A new feature implementation. <code>fix</code> A bug fix. <code>docs</code> Documentation updates or improvements. <code>refactor</code> Code restructuring without fixing bugs or adding features (eg: You can use this when there have semantic changes like renaming a variable/ function name). <code>perf</code> Performance improvements. <code>style</code> Styling updates (e.g., formatting, spacing, missing semicolons). <code>test</code> Adding new test or making changes to existing test."}, {"location": "reference/coding-bible/#development-best-practices", "title": "🛠️ Development Best Practices", "text": ""}, {"location": "reference/coding-bible/#testing-guidelines", "title": "📊 Testing Guidelines", "text": "<ul> <li>✅ Write tests first (TDD) - Understand requirements before coding</li> <li>✅ Test edge cases - Empty inputs, large data, invalid formats</li> <li>✅ Keep tests independent - No dependencies between test cases</li> <li>✅ Use descriptive test names - <code>test_user_cannot_access_admin_area()</code></li> </ul>"}, {"location": "reference/coding-bible/#security-practices", "title": "🔒 Security Practices", "text": "<ul> <li>✅ Never commit credentials - Use environment variables</li> <li>✅ Validate all inputs - Especially user-provided data</li> <li>✅ Use prepared statements - Prevent SQL injection</li> <li>✅ Keep dependencies updated - Regular security audits</li> </ul>"}, {"location": "reference/coding-bible/#performance-considerations", "title": "🚀 Performance Considerations", "text": "<ul> <li>✅ Profile before optimizing - Identify actual bottlenecks</li> <li>✅ Optimize database queries - Use indexes and minimize N+1 problems</li> <li>✅ Consider caching - For expensive or repetitive operations</li> <li>✅ Lazy load resources - Only load what you need, when you need it</li> </ul> <p>Premature Optimization</p> <p>\"Premature optimization is the root of all evil\" - <PERSON>. Focus on clean, working code first. Optimize only where necessary.</p> <p>🚀 Follow these guidelines to keep the codebase clean and maintainable!</p>"}, {"location": "reference/data-migration/", "title": "Data Migration Guide", "text": ""}, {"location": "reference/data-migration/#migrating-run-data-to-a-new-server", "title": "Migrating Run Data to a New Server", "text": "<p>Follow these steps to migrate data from an old server to a new one:</p>"}, {"location": "reference/data-migration/#tasks-that-can-be-done-anytime", "title": "Tasks That Can Be Done Anytime", "text": "<ol> <li>Snapshot the datastore on the old server</li> <li>Check the snapshot into GIT.</li> <li>Load the snapshot onto the new server to populate all profiles.</li> <li> <p>This ensures that multimedia loads can happen properly.</p> </li> <li> <p>Migrate Device Media Content</p> </li> <li>Retrieve media content from the multimedia page.</li> <li>Download the content to a temporary location (e.g., OneDrive?).</li> <li>Upload the content to the new server.</li> </ol>"}, {"location": "reference/data-migration/#final-step", "title": "Final Step", "text": "<ul> <li>Take another datastore snapshot on the old server.</li> <li>Check it into GIT and load it onto the new server.</li> </ul> <p>This ensures all the latest data is transferred successfully.</p> <p>End of document.</p>"}, {"location": "reference/datastore/", "title": "Slicer Datastore Usage Map", "text": "<p>The following table provides a mapping of data usage within the Slicer datastore, including data creators, consumers, and destinations.</p>"}, {"location": "reference/datastore/#data-mapping", "title": "Data Mapping", "text": "Prefix Creator Consumer Destination <code>device_profile_(id)</code> reports datadrop Used to pull single profile <code>device_service_(id)</code> reports datadrop On Raspberry Pi: <code>pi_runner</code> uses this list, compares it to its running service versions, and pulls any that do not match. The services are then loaded and started/restarted. <code>device_name_(id)</code> reports reports, datadrop Reports: Displays in device list as an additional attribute.  Datadrop: Goes out with profile information or service versions, tied to <code>(id)</code>, like bookmarks. <code>profile_(name)</code> (manually) datadrop On Raspberry Pi: <code>pi_runner</code> saves this information locally in the <code>/cardinal</code> directory. The <code>pi_hmi</code> service picks it up from that directory to build the landing page, which reloads every 30 seconds. Datastore Overview <ul> <li>Reports: Generates data to track device attributes and configurations.</li> <li>Datadrop: Provides a way for Raspberry Pi services to fetch required configurations and updates.</li> <li>pi_runner: A key service on Raspberry Pi that ensures services are up to date.</li> <li>pi_hmi: Handles the user interface and profile-based landing pages.</li> </ul> <p>End of document.</p>"}, {"location": "reference/filesystem/", "title": "Slicer Filesystem Usage &amp; Layout", "text": ""}, {"location": "reference/filesystem/#main-directories", "title": "Main Directories", "text": ""}, {"location": "reference/filesystem/#1-wsgi-applications", "title": "1. WSGI Applications", "text": "<pre><code>/var/www/html\n</code></pre> <p>This is the location of all WSGI apps running on the server.</p>"}, {"location": "reference/filesystem/#2-versioned-service-files", "title": "2. Versioned Service Files", "text": "<pre><code>/var/www/html/pi_services/(app)/(version)/(app).py\n</code></pre> <ul> <li>This is where versioned files are stored.</li> <li><code>pi_runner</code> pulls files from here when needed.</li> <li>Files are initially placed manually.</li> <li>Pis retrieve them via API calls to the Slicer app (<code>download</code>).</li> </ul>"}, {"location": "reference/filesystem/#3-static-content", "title": "3. Static Content", "text": "<pre><code>/var/www/htmlfiles\n</code></pre> <ul> <li>Intended for statically served content (none as of <code>2021.05.21</code>).</li> <li>Created so that Apache serves files away from <code>/var/www/html</code>.</li> </ul>"}, {"location": "reference/filesystem/#log-files-data-storage", "title": "Log Files &amp; Data Storage", "text": ""}, {"location": "reference/filesystem/#4-log-directory-for-slicer-apps", "title": "4. Log Directory for Slicer Apps", "text": "<pre><code>/var/log/slicer/(app)\n</code></pre> <p>Each Slicer app (WSGI-based Python module) has its own log directory.</p> Logging Structure <ul> <li>checkin:   <pre><code>/var/log/slicer/checkin/json\n/var/log/slicer/checkin/raw\n</code></pre>   Logs device check-ins and updates.</li> <li>datadrop:   <pre><code>/var/log/slicer/datadrop/json/id/(id)/(service)\n</code></pre>   Stores the most recently received JSON data from a device.   Example: <pre><code>sudo cat /var/log/slicer/datadrop/json/id/10000000bd6f7a19/runner\n</code></pre>   Returns:   <pre><code>{\"service:pi_monitor\": \"M.2.2\", \"temperature\": \"61.3\", \"Memory:MemFree\": \"2920873984\"}\n</code></pre></li> <li>Raw Data Logs:   <pre><code>/var/log/slicer/datadrop/raw/(YYYYMMDD)/(HH)/(YYYYMMDDHHMMSSmmmuuu)_(id).txt\n</code></pre> Example: <pre><code>/var/log/slicer/datadrop/raw/20210526/08/20210526085941378807_100000004e61fc41.txt\n</code></pre></li> <li>Rollups: <pre><code>/mnt/disks/SSD/var/log/slicer/datadrop/raw/20220728/00/rollup_20000000cf60766f\n</code></pre></li> <li>Statistical Reports: <pre><code>/mnt/disks/SSD/var/log/slicer/datadrop/stats/id/10000000fc5317fa\n</code></pre></li> </ul>"}, {"location": "reference/filesystem/#5-key-value-data-storage", "title": "5. Key-Value Data Storage", "text": "<pre><code>/var/log/slicer/datastore\n</code></pre> <ul> <li>Stores key-value pairs.</li> <li>Each file represents a 'key', and its content is a JSON-dumped 'value'.</li> </ul>"}, {"location": "reference/filesystem/#6-login-logs", "title": "6. <PERSON><PERSON>", "text": "<pre><code>/var/log/slicer/login\n</code></pre> Login Structure <ul> <li>Persistent Logs (<code>keep</code>):   <pre><code>/var/log/slicer/login/keep\n</code></pre>   Stores authenticated user sessions.</li> <li>Temporary Logs (<code>throw</code>):   <pre><code>/var/log/slicer/login/throw\n</code></pre>   Stores temporary login attempts.</li> <li>Login Activity Tracking (<code>touch</code>):   <pre><code>/var/log/slicer/login/touch\n</code></pre>   Logs login touchpoints.</li> <li>User-Specific Data (<code>user</code>):   <pre><code>/var/log/slicer/login/user\n</code></pre>   Tracks per-user authentication data.</li> </ul>"}, {"location": "reference/filesystem/#7-task-management-logs", "title": "7. Task Management Logs", "text": "<pre><code>/var/log/slicer/tasks\n</code></pre> Task Processing Logs <ul> <li>Task Status Summary:   <pre><code>/var/log/slicer/tasks/status_summary\n</code></pre></li> <li>Task Request Logs:   <pre><code>/var/log/slicer/tasks/(datetime_stamp)/request\n</code></pre><ul> <li>Stores JSON-dumped task requests.</li> <li>Created by the reports module, dropping the request here, to kick off the process</li> </ul> </li> <li>Task Processing Logs:   <pre><code>/var/log/slicer/tasks/(datetime_stamp)/status\n</code></pre>   Text file with the JSON-dumped of the tasks runner work content</li> <li>Task Completion Logs:   <pre><code>/var/log/slicer/tasks/(datetime_stamp)/complete\n</code></pre>   The existence of this file indicates task completion.</li> </ul>"}, {"location": "reference/filesystem/#8-file-uploads", "title": "8. File Uploads", "text": "<pre><code>/var/log/slicer/uploads\n</code></pre> <ul> <li>(Currently unused)</li> <li>Intended for future file upload functionality.</li> </ul>"}, {"location": "reference/filesystem/#summary-table", "title": "Summary Table", "text": "Directory Purpose <code>/var/www/html</code> Hosts WSGI applications <code>/var/www/html/pi_services</code> Stores versioned files for Pis <code>/var/www/htmlfiles</code> Static content serving location <code>/var/log/slicer</code> Central logging directory for Slicer apps <code>/var/log/slicer/datadrop</code> Stores incoming data from Pis <code>/var/log/slicer/login</code> Logs authentication attempts <code>/var/log/slicer/tasks</code> Logs task requests and processing statuses <code>/var/log/slicer/uploads</code> (Future use) File uploads <p>End of document.</p>"}, {"location": "reference/python3porting/", "title": "Python 3 Porting Guide", "text": "<p>About this guide</p> <p>This document highlights common issues encountered when porting Python 2 code to Python 3, with practical solutions for each problem.</p>"}, {"location": "reference/python3porting/#string-handling-issues", "title": "String Handling Issues", "text": ""}, {"location": "reference/python3porting/#explicit-encoding-with-encode", "title": "Explicit Encoding with <code>.encode()</code>", "text": "<p>Type Mismatch</p> <p>Certain operations require explicitly encoding strings to avoid type mismatches in Python 3.</p> <pre><code># Python 2\nmy_string = \"hello\"\nsome_function(my_string)  # Worked fine\n\n# Python 3\nmy_string = \"hello\"\nsome_function(my_string.encode('utf-8'))  # Explicit encoding needed\n</code></pre>"}, {"location": "reference/python3porting/#byte-string-operations-with-split", "title": "Byte String Operations with <code>split()</code>", "text": "<p>When working with byte strings, methods like <code>split()</code> must be used with byte-like objects:</p> <pre><code># This won't work in Python 3\n# byte_string.split('\\n')  # TypeError: a bytes-like object is required\n\n# Correct approach\nbyte_string = b\"line1\\nline2\\nline3\"\nsplit_lines = byte_string.split(b'\\n')  # Returns a list of byte strings\n</code></pre>"}, {"location": "reference/python3porting/#module-import-changes", "title": "Module Import Changes", "text": ""}, {"location": "reference/python3porting/#urllib-restructuring", "title": "<code>urllib</code> Restructuring", "text": "<p>Module reorganization</p> <p>Many modules were reorganized in Python 3, with <code>urllib</code> being significantly restructured.</p> Python 3 Python 2  <pre><code>from urllib.parse import parse_qs\n</code></pre> <pre><code>from cgi import parse_qs\n</code></pre> <p>JSON Serialization Issue</p> <p>Using <code>parse_qs</code> in Python 3 results in a dictionary where all strings are bytes, which <code>json.dumps()</code> does not handle well.</p> <p>Solution: Decode the request body before passing it to <code>parse_qs</code>:</p> <pre><code># Fix JSON serialization issues\nd = parse_qs(request_body.decode(\"utf-8\"))\n</code></pre>"}, {"location": "reference/python3porting/#additional-resources", "title": "Additional Resources", "text": "<ul> <li>Python 3 Porting Guide (External)</li> <li>Official 2to3 Documentation</li> <li>Six - Python 2 and 3 Compatibility Library</li> </ul> <p>*[JSON]: JavaScript Object Notation</p>"}, {"location": "reference/security-compliance-review/", "title": "Security Compliance Review", "text": "<p>As of 2021.08.26, this is a review of the current state of the recommended security notes.</p> <p>The input to this review comes from <PERSON>, in an email on August 12, 2021, and is attached to Jira card EUDE-773. The goal of this section is to list out each requirement and provide a place for response/evidence that the requirement is either currently met or is being worked on.</p>"}, {"location": "reference/security-compliance-review/#security-controls-requirements", "title": "Security Controls / Requirements", "text": ""}, {"location": "reference/security-compliance-review/#1-device-theft-prevention", "title": "1. <PERSON>ce Theft Prevention", "text": "<p>\"We must have controls in place that ensure the device itself is not stolen, see Global Security for physical security controls.\"</p> <p>Response: This is an open item and is anticipated to be addressed by providing a complete 'kit' as an orderable item.</p>"}, {"location": "reference/security-compliance-review/#2-centralized-management-tool", "title": "2. Centralized Management Tool", "text": "<p>\"We must use a centralized management tool for device governance including, but not limited to: OS Management, Device Enrollment, Patching, Updates, Enforce Encryption, Inventory.\"</p> <p>Response: The site Slicer provides the central communication and control point for the configuration and update of Raspberry Pis. Currently, device enrollment is fully automatic. Encryption is enforced through HTTPS as the sole communication path. Patching and updates are an active backlog item (EUDE-1328).</p>"}, {"location": "reference/security-compliance-review/#3-device-inventory", "title": "3. <PERSON><PERSON>", "text": "<p>\"We must maintain an inventory of these devices.\"</p> <p>Response: ??? This is outside the scope of software.</p>"}, {"location": "reference/security-compliance-review/#4-network-monitoring", "title": "4. Network Monitoring", "text": "<p>\"We must be able to monitor these devices and their behavior on the network.\"</p> <p>Response: Devices check in to the Slicer central site every minute. Status is available there. EUDE-1329 will add monitoring of network behavior.</p>"}, {"location": "reference/security-compliance-review/#5-data-security", "title": "5. Data Security", "text": "<p>\"No Cardinal Health information is stored on the device in any way.\"</p> <p>Response: The Raspberry Pi operates using the Chromium browser in incognito mode, ensuring that when the browser is closed/restarted, no information is saved. There is no capability for downloading, storing, or saving Cardinal Health information.</p>"}, {"location": "reference/security-compliance-review/#6-os-hardening-media-theft-prevention", "title": "6. OS Hardening &amp; Media Theft Prevention", "text": "<p>\"We need to ensure that the OS cannot be compromised. Theft prevention of any and all media that is installed on the device, i.e. SD card, etc. Prevent unauthorized OS modification.\"</p> <p>Response: ??? This is outside the scope of software.</p>"}, {"location": "reference/security-compliance-review/#7-machine-level-certificates", "title": "7. Machine-Level Certificates", "text": "<p>\"Machine-level certificates must be leveraged in order to identify the device.\"</p> <p>Response: EUDE-1331 confirms this requirement is outdated per email from <PERSON>.</p>"}, {"location": "reference/security-compliance-review/#8-restricted-device-communication", "title": "8. Restricted Device Communication", "text": "<p>\"Must ensure that controls are in place to ensure that devices cannot communicate with each other directly.\"</p> <p>Response: Discussions with <PERSON> indicate that IOT network-level controls will fulfill this requirement.</p>"}, {"location": "reference/security-compliance-review/#9-least-privilege-access", "title": "9. Least Privilege Access", "text": "<p>\"Least Privilege access must be followed. The Pi can access only what is needed and nothing else.\"</p> <p>Response: The Chromium browser is in incognito mode, meaning no history is saved. Privoxy proxy filtering enforces a strict allowlist of sites tailored to the specific use case.</p>"}, {"location": "reference/security-compliance-review/#10-no-local-password-authentication", "title": "10. No Local Password Authentication", "text": "<p>\"No password local accounts that can be used to authenticate to the device.\"</p> <p>Response: Addressed in EUDE-1333.</p>"}, {"location": "reference/security-compliance-review/#11-os-baselines-workstation-hardening", "title": "11. OS Baselines &amp; Workstation Hardening", "text": "<p>\"Leverage CIS OS Baselines / Workstation Hardening.\"</p> <p>Response: The current build includes a pi_security service, which uses lynis to report system hardening status. EUDE-1350 will further improve the hardening score.</p> <p>End of document.</p>"}, {"location": "reference/ubuntu22_servers/", "title": "Ubuntu 22 Server Setup", "text": ""}, {"location": "reference/ubuntu22_servers/#system-requirements", "title": "System Requirements", "text": "<ul> <li>Ubuntu 22</li> <li>2 cores</li> <li>4GB RAM</li> <li>20GB disk</li> <li>Networking: bridged</li> </ul>"}, {"location": "reference/ubuntu22_servers/#initial-setup", "title": "Initial Setup", "text": "<p>When installing Ubuntu 22, take the default options but ensure SSH is installed.</p>"}, {"location": "reference/ubuntu22_servers/#system-configuration", "title": "System Configuration", "text": ""}, {"location": "reference/ubuntu22_servers/#python-setup", "title": "Python Setup", "text": "<pre><code>sudo su\napt install -y python3-pip\nexit\n</code></pre>"}, {"location": "reference/ubuntu22_servers/#required-python-packages", "title": "Required Python Packages", "text": "<pre><code>pip install pexpect\n</code></pre>"}, {"location": "reference/ubuntu22_servers/#firewall-configuration", "title": "Firewall Configuration", "text": "<ol> <li> <p>List available firewall applications:    <pre><code>sudo ufw app list\n</code></pre></p> </li> <li> <p>Allow SSH connections:    <pre><code>sudo ufw allow OpenSSH\n</code></pre></p> </li> <li> <p>Enable the firewall:    <pre><code>sudo ufw enable\n</code></pre></p> <p>Note: This command may disrupt existing SSH connections. You will be prompted to proceed.</p> </li> <li> <p>Verify firewall status:    <pre><code>sudo ufw status\n</code></pre></p> </li> </ol>"}, {"location": "reference/ubuntu22_servers/#additional-configuration", "title": "Additional Configuration", "text": "<p>For additional configuration details, refer to the <code>slicer_wsgi_loader.py</code> file.</p>"}, {"location": "reference/branches/2024-12-11/", "title": "Branch: December 11, 2024", "text": "<p>Branch: <code>dwf_custom_display_board</code></p> Files <ul> <li><code>A-Knowledge.txt</code></li> <li><code>AA-branches.txt</code></li> <li><code>AA-pi-applications.txt</code></li> <li><code>datastore_snapshot.txt</code></li> <li><code>offline_test.py</code></li> <li><code>pi_hmi.py</code></li> <li><code>pi_template.py</code></li> <li><code>read_release_notes_slicer_pi.txt</code></li> <li><code>slicer_wsgi_datadrop.py</code></li> <li><code>slicer_wsgi_dataport.py</code></li> <li><code>slicer_wsgi_multimedia.py</code></li> </ul>"}, {"location": "reference/branches/2024-12-16/", "title": "Branch: December 16, 2024", "text": "<p>Branch: <code>dwf_pi_temporary_file_management</code></p> Files: <ul> <li><code>pi_hmi.py</code></li> <li><code>pi_network_cah.py</code></li> <li><code>pi_network_generic.py</code></li> <li><code>pi_runner.py</code></li> <li><code>read_release_notes_slicer_pi.txt</code></li> </ul>"}, {"location": "releases/2021-05-01_2.0.0/", "title": "2.0.0 (2021-05-01)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.0</li> <li>Release Date: May 1, 2021</li> <li>Type: Major Release</li> </ul>"}, {"location": "releases/2021-05-01_2.0.0/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-01_2.0.0/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-01_2.0.0/#component-updates", "title": "Component Updates", "text": "Component Version Changes Initial test build - Initial release of all components"}, {"location": "releases/2021-05-01_2.0.0/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Initial base image setup</li> <li>Basic system configuration and dependencies</li> </ul>"}, {"location": "releases/2021-05-01_2.0.0/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>Initial release - no upgrade procedures required.</p>"}, {"location": "releases/2021-05-01_2.0.0/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-05_2.0.1/", "title": "2.0.1 (2021-05-05)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.1</li> <li>Release Date: May 5, 2021</li> <li>Type: Patch Release</li> </ul>"}, {"location": "releases/2021-05-05_2.0.1/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-05_2.0.1/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-05_2.0.1/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Now with allowlist enforced, and must be included in the bookmarks definition.</li> </ul>"}, {"location": "releases/2021-05-05_2.0.1/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No changes to base image</li> </ul>"}, {"location": "releases/2021-05-05_2.0.1/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-05_2.0.1/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-05_2.0.2/", "title": "2.0.2 (2021-05-05)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.2</li> <li>Release Date: May 5, 2021</li> <li>Type: Patch Release</li> </ul>"}, {"location": "releases/2021-05-05_2.0.2/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-05_2.0.2/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-05_2.0.2/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Put the expansion of the filesystem back into pi_hmi</li> </ul>"}, {"location": "releases/2021-05-05_2.0.2/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No changes to base image</li> </ul>"}, {"location": "releases/2021-05-05_2.0.2/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-05_2.0.2/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-05_2.0.3/", "title": "2.0.3 (2021-05-05)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.3</li> <li>Release Date: May 5, 2021</li> <li>Type: Patch Release</li> </ul>"}, {"location": "releases/2021-05-05_2.0.3/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-05_2.0.3/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-05_2.0.3/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Clean up the hmi make of the allowlist for privoxy</li> </ul>"}, {"location": "releases/2021-05-05_2.0.3/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No changes to base image</li> </ul>"}, {"location": "releases/2021-05-05_2.0.3/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-05_2.0.3/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-06_2.0.4/", "title": "2.0.4 (2021-05-06)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.4</li> <li>Release Date: May 6, 2021</li> <li>Type: Patch Release</li> </ul>"}, {"location": "releases/2021-05-06_2.0.4/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-06_2.0.4/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-06_2.0.4/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Implemented remote bookmark system integration</li> </ul>"}, {"location": "releases/2021-05-06_2.0.4/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_network - Added remote bookmark pull functionality <p>Additional Notes</p> <p>Still require to manually put those bookmarks to the server-side, per device.</p>"}, {"location": "releases/2021-05-06_2.0.4/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No changes to base image</li> </ul>"}, {"location": "releases/2021-05-06_2.0.4/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-06_2.0.4/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-17_SP.1/", "title": "SP.1 (2021-05-17)", "text": "<p>Release Information</p> <ul> <li>Version: SP.1</li> <li>Release Date: May 17, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-05-17_SP.1/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-17_SP.1/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-17_SP.1/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Now with pi_runner installed, doing the bookmark pulls, instead of pi_network</li> <li>pi_runner also doing service pulls/installs, as given in the datadrop response from Slicer</li> <li>pi_network now actively managing configuring corp wifi, if not plugged to wired; if wired, remove corp</li> <li>Actively remove any non-corp wifi, all the time</li> </ul>"}, {"location": "releases/2021-05-17_SP.1/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_monitor M.2.2 - pi_runner R.1.1 - pi_hmi H.1.0 - pi_network N.1.2 - <p>Image Changes</p> <ul> <li>Image fixed to have the worker user have permissions on the input devices, so that the keyboard and mouse work</li> </ul>"}, {"location": "releases/2021-05-17_SP.1/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-17_SP.1/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-18_2.0.6/", "title": "2.0.6 (2021-05-18)", "text": "<p>Release Information</p> <ul> <li>Version: 2.0.6</li> <li>Release Date: May 18, 2021</li> <li>Type: Patch Release</li> </ul>"}, {"location": "releases/2021-05-18_2.0.6/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-18_2.0.6/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-18_2.0.6/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Clear Chromium profile before imaging to prevent device conflicts</li> </ul>"}, {"location": "releases/2021-05-18_2.0.6/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No changes to base image</li> </ul>"}, {"location": "releases/2021-05-18_2.0.6/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-18_2.0.6/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-19_SP.2/", "title": "SP.2 (2021-05-19)", "text": "<p>Release Information</p> <ul> <li>Version: SP.2</li> <li>Release Date: May 19, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-05-19_SP.2/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-19_SP.2/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-19_SP.2/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_monitor M.2.2 - pi_runner R.1.3 - pi_hmi H.1.1 - pi_network N.1.2 -"}, {"location": "releases/2021-05-19_SP.2/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul> <p>Image Changes</p> <ul> <li>Enforce the new Google \"disable translate\" pop-up setting. This requires re-flashing the SD card.</li> </ul>"}, {"location": "releases/2021-05-19_SP.2/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-19_SP.2/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-05-28_SP.3/", "title": "SP.3 (2021-05-28)", "text": "<p>Release Information</p> <ul> <li>Version: SP.3</li> <li>Release Date: May 28, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-05-28_SP.3/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-05-28_SP.3/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-05-28_SP.3/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_monitor M.2.2 - pi_runner R.1.5 - pi_hmi H.1.2 - pi_network N.1.2 - pi_logging L.1.0 -"}, {"location": "releases/2021-05-28_SP.3/#image-changes", "title": "Image Changes", "text": "<p>Image Changes</p> <ul> <li>Added the logging service to the image</li> <li>Set the browser to not ask to translate the page</li> </ul>"}, {"location": "releases/2021-05-28_SP.3/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-05-28_SP.3/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-06-21_SP.4/", "title": "SP.4 (2021-06-21)", "text": "<p>Release Information</p> <ul> <li>Version: SP.4</li> <li>Release Date: June 21, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-06-21_SP.4/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-06-21_SP.4/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-06-21_SP.4/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Adds support for the pairing of Zebra RS6000 bluetooth connected scanner</li> <li>This feature is hidden by default. Slicer must be set to enable Bluetooth on each device</li> </ul>"}, {"location": "releases/2021-06-21_SP.4/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.0 Support interfacing to Zebra RS6000 scanner pi_monitor M.2.2 Same as before pi_runner R.1.6 Screen Height and Width sent to Slicer, Bluetooth Enable picked up from Slicer is saved locally pi_hmi H.1.3 Add support for bluetooth pairing visual interface pi_network N.1.2 Same as before"}, {"location": "releases/2021-06-21_SP.4/#image-changes", "title": "Image Changes", "text": "<p>Image Changes</p> <ul> <li>Adds the background scheduler required by the pi_bluetooth service</li> <li>Drive HDMI output always (Force hot-plug)</li> <li>Adds the screen capture driver scrot</li> </ul>"}, {"location": "releases/2021-06-21_SP.4/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-06-21_SP.4/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-06-23_SP.5/", "title": "SP.5 (2021-06-23)", "text": "<p>Release Information</p> <ul> <li>Version: SP.5</li> <li>Release Date: June 23, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-06-23_SP.5/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-06-23_SP.5/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-06-23_SP.5/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Added support for clock view on the landing page</li> </ul>"}, {"location": "releases/2021-06-23_SP.5/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.0 Same as before pi_monitor M.2.3 - pi_runner R.1.2 - pi_hmi H.1.1 - pi_network N.1.3 - pi_logging L.1.0 - pi_config C.1.0 - pi_security S.1.0 -"}, {"location": "releases/2021-06-23_SP.5/#image-changes", "title": "Image Changes", "text": "<p>Image Changes</p> <ul> <li>Set the keyboard to US, instead of the default GB</li> </ul>"}, {"location": "releases/2021-06-23_SP.5/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-06-23_SP.5/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-06-24_SP.6/", "title": "SP.6 (2021-06-24)", "text": "<p>Release Information</p> <ul> <li>Version: SP.6</li> <li>Release Date: June 24, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-06-24_SP.6/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-06-24_SP.6/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-06-24_SP.6/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Fix the clock reporting of am/pm time for the 12 hour rollover</li> </ul>"}, {"location": "releases/2021-06-24_SP.6/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.0 Same as before pi_monitor M.2.2 Same as before pi_runner R.1.8 Same as before pi_hmi H.1.5 Fix the AM/PM reporting in the 12 O'clock hours pi_network N.1.2 Same as before"}, {"location": "releases/2021-06-24_SP.6/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-06-24_SP.6/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-06-24_SP.6/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-02_SP.7/", "title": "SP.7 (2021-07-02)", "text": "<p>Release Information</p> <ul> <li>Version: SP.7</li> <li>Release Date: July 2, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-02_SP.7/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-02_SP.7/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-02_SP.7/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>More robust Bluetooth pairing and connection controls</li> <li>Bluetooth pairing is now initiated by the user picking from a list</li> </ul>"}, {"location": "releases/2021-07-02_SP.7/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.1 Connection method is now from a list pi_monitor M.2.2 Same as before pi_runner R.1.8 Same as before pi_hmi H.1.5 Same as before pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-02_SP.7/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-07-02_SP.7/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-02_SP.7/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-02_SP.8/", "title": "SP.8 (2021-07-02)", "text": "<p>Release Information</p> <ul> <li>Version: SP.8</li> <li>Release Date: July 2, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-02_SP.8/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-02_SP.8/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-02_SP.8/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>More robust Bluetooth pairing and connection controls</li> <li>Bluetooth pairing is now initiated by the user picking address from a list</li> <li>Show name (serial number) in the pairing list</li> </ul>"}, {"location": "releases/2021-07-02_SP.8/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.2 Connection method is now from a list pi_monitor M.2.2 Same as before pi_runner R.1.8 Same as before pi_hmi H.1.5 Same as before pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-02_SP.8/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-07-02_SP.8/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-02_SP.8/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-03_SP.9/", "title": "SP.9 (2021-07-03)", "text": "<p>Release Information</p> <ul> <li>Version: SP.9</li> <li>Release Date: July 3, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-03_SP.9/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-03_SP.9/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-03_SP.9/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>More robust Bluetooth pairing and connection controls</li> <li>Bluetooth pairing is now initiated by the user picking address from a list</li> <li>Show name (serial number) in the pairing list</li> <li>Screen formatting changes, to work with many bookmarks, on a small screen</li> </ul>"}, {"location": "releases/2021-07-03_SP.9/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.3 Connection method is now from a list, and status display is more compact pi_monitor M.2.2 Same as before pi_runner R.1.8 Same as before pi_hmi H.1.6 More compact for long bookmark lists pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-03_SP.9/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-07-03_SP.9/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-03_SP.9/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-12_SP.10/", "title": "SP.10 (2021-07-12)", "text": "<p>Release Information</p> <ul> <li>Version: SP.10</li> <li>Release Date: July 12, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-12_SP.10/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-12_SP.10/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-12_SP.10/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Make a tab mode for the bookmarks and browser; go between using Alt 1, Alt 2, etc</li> <li>Allow Slicer to enable showing a Menu to show shutdown and reboot on the pi</li> <li>Screen resolution set by Slicer configuration</li> </ul>"}, {"location": "releases/2021-07-12_SP.10/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.4 Once paired, turn off scan, to allow for fast data transfers pi_monitor M.2.2 Same as before pi_runner R.2.0 Pulling out screen resolution setting, and show menu option pi_hmi H.1.7 Tabs mode for browser and Menu for shutdown/reboot pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-12_SP.10/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>B<PERSON>er start pulled out of init, to allow for it to be configured for tabs by pi_runner</li> </ul>"}, {"location": "releases/2021-07-12_SP.10/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-12_SP.10/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-13_2.1.6/", "title": "2.1.6 (2021-07-13)", "text": "<p>Release Information</p> <ul> <li>Version: 2.1.6</li> <li>Release Date: July 13, 2021</li> <li>Type: Minor Release</li> </ul>"}, {"location": "releases/2021-07-13_2.1.6/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-13_2.1.6/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-13_2.1.6/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_hmi H.1.7 Fixes service startup issues"}, {"location": "releases/2021-07-13_2.1.6/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Fixes the issue where the HMI service was not correctly starting.</li> </ul>"}, {"location": "releases/2021-07-13_2.1.6/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-13_2.1.6/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-20_SP.11/", "title": "SP.11 (2021-07-20)", "text": "<p>Release Information</p> <ul> <li>Version: SP.11</li> <li>Release Date: July 20, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-20_SP.11/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-20_SP.11/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-20_SP.11/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Remote Pi reboot from Slicer configuration page</li> <li>Allow full keyboard access, dependent on profile setting</li> <li>Build index page in a way that should always be able to load, to address the issue where it shows as missing sometimes</li> </ul>"}, {"location": "releases/2021-07-20_SP.11/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.5 Add note to top of selection list, that only a single click is required pi_monitor M.2.2 Same as before pi_runner R.2.1 Send more in checkin: uptime, boot_count, React to reboot request, React to setting of all keyboard access pi_hmi H.1.8 If all keyboard access is allowed, show ctrl key hints in the tabbed homepage view pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-20_SP.11/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-07-20_SP.11/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-20_SP.11/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-07-22_SP.12/", "title": "SP.12 (2021-07-22)", "text": "<p>Release Information</p> <ul> <li>Version: SP.12</li> <li>Release Date: July 22, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-07-22_SP.12/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-07-22_SP.12/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-07-22_SP.12/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support logo options</li> </ul>"}, {"location": "releases/2021-07-22_SP.12/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.5 Same as before pi_monitor M.2.2 Same as before pi_runner R.2.1 Same as before pi_hmi H.1.9 Support logo options pi_network N.1.2 Same as before"}, {"location": "releases/2021-07-22_SP.12/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-07-22_SP.12/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-07-22_SP.12/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-14_SP.13/", "title": "SP.13 (2021-08-14)", "text": "<p>Release Information</p> <ul> <li>Version: SP.13</li> <li>Release Date: August 14, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-14_SP.13/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-14_SP.13/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-14_SP.13/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Build second page with device info, by clicking on bolded ID title field</li> <li>Add first round of security audit scanning</li> <li>Wait for screen resolution to be set before making screen settings on boot</li> <li>Allow support for disabling kiosk mode on the browser on the pi</li> <li>This allows F11 to be used to switch back and forth to Full Screen mode</li> <li>This allows the open keys to be used for 'ctrl tab' to move between tabs, even for the ERP key grabbed page at PR005, when exited from full screen mode</li> </ul>"}, {"location": "releases/2021-08-14_SP.13/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.6 Extend the polling time, to be better where there are lots of devices pi_monitor M.2.2 Same as before pi_runner R.2.2 Allow support for disabling kiosk mode, Add startup delay to let HMI settings settle pi_hmi H.2.0 Show ID screen with MAC address, and other items, If not in kiosk mode, show prompt for F11 to exit full screen mode pi_network N.2.0 Add support to show cah-iot ssid status on the ID screen, Not ready to connect to it yet, just supporting development pi_security S.1.0 New service, to report on security issues"}, {"location": "releases/2021-08-14_SP.13/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Install lynis security audit tool</li> <li>Lock down the default account</li> </ul>"}, {"location": "releases/2021-08-14_SP.13/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-14_SP.13/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-17_SP.14/", "title": "SP.14 (2021-08-17)", "text": "<p>Release Information</p> <ul> <li>Version: SP.14</li> <li>Release Date: August 17, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-17_SP.14/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-17_SP.14/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-17_SP.14/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Adjust the screen size for the browser based on current 'fbset -s' size, instead of chromium screen size</li> <li>Addresses the issue with Dell P2219H display</li> </ul>"}, {"location": "releases/2021-08-17_SP.14/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.6 Same as before pi_monitor M.2.2 Same as before pi_runner R.2.3 Use fbset to adjust the browser size to fill the screen pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-17_SP.14/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-17_SP.14/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-17_SP.14/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-18_SP.15/", "title": "SP.15 (2021-08-18)", "text": "<p>Release Information</p> <ul> <li>Version: SP.15</li> <li>Release Date: August 18, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-18_SP.15/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-18_SP.15/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-18_SP.15/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Backoff the timing for polling bluetooth, based on how many devices are found; 60 seconds max</li> </ul>"}, {"location": "releases/2021-08-18_SP.15/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.7 Poll at 5 seconds until one device, then add 5 seconds for each device, max 60 pi_monitor M.2.2 Same as before pi_runner R.2.3 Same as before pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-18_SP.15/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-18_SP.15/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-18_SP.15/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-20_SP.16/", "title": "SP.16 (2021-08-20)", "text": "<p>Release Information</p> <ul> <li>Version: SP.16</li> <li>Release Date: August 20, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-20_SP.16/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-20_SP.16/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-20_SP.16/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Bluetooth change to limit number of device transactions; in test of PR005 scanners</li> </ul>"}, {"location": "releases/2021-08-20_SP.16/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.8 Only enact trust on the one device we are try to pair to pi_monitor M.2.2 Same as before pi_runner R.2.3 Same as before pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-20_SP.16/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-20_SP.16/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-20_SP.16/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-24_SP.17/", "title": "SP.17 (2021-08-24)", "text": "<p>Release Information</p> <ul> <li>Version: SP.17</li> <li>Release Date: August 24, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-24_SP.17/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-24_SP.17/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-24_SP.17/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Bluetooth change to properly recognize the user click for the pairing request</li> </ul>"}, {"location": "releases/2021-08-24_SP.17/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.1.9 Correctly look at all devices in the selection list, to see which one is picked pi_monitor M.2.2 Same as before pi_runner R.2.3 Same as before pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-24_SP.17/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-24_SP.17/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-24_SP.17/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-26_SP.18/", "title": "SP.18 (2021-08-26)", "text": "<p>Release Information</p> <ul> <li>Version: SP.18</li> <li>Release Date: August 26, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-26_SP.18/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-26_SP.18/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-26_SP.18/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Bluetooth change to be more flexible when making a new connection</li> </ul>"}, {"location": "releases/2021-08-26_SP.18/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.0 In area with lots of bluetooth devices, allow more flexibility on timing when making a new connection pi_monitor M.2.2 Same as before pi_runner R.2.3 Same as before pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-26_SP.18/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-26_SP.18/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-26_SP.18/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-30_SP.19/", "title": "SP.19 (2021-08-30)", "text": "<p>Release Information</p> <ul> <li>Version: SP.19</li> <li>Release Date: August 30, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-30_SP.19/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-30_SP.19/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-30_SP.19/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Screen grab of pi display, with delivery back to Slicer</li> </ul>"}, {"location": "releases/2021-08-30_SP.19/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.0 Same as before pi_monitor M.2.2 Same as before pi_runner R.2.4 Implement screen grab, and reporting back to Slicer pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-30_SP.19/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-30_SP.19/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-30_SP.19/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-08-31_SP.20/", "title": "SP.20 (2021-08-31)", "text": "<p>Release Information</p> <ul> <li>Version: SP.20</li> <li>Release Date: August 31, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-08-31_SP.20/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-08-31_SP.20/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-08-31_SP.20/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Make changes for bluetooth in crowded scanner environment, and many pis looking for connection</li> </ul>"}, {"location": "releases/2021-08-31_SP.20/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.1 Eliminate info request during discovery, to not over communicate with devices pi_monitor M.2.2 Same as before pi_runner R.2.4 Same as before pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.0 Same as before"}, {"location": "releases/2021-08-31_SP.20/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-08-31_SP.20/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-08-31_SP.20/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-10-14_SP.21/", "title": "SP.21 (2021-10-14)", "text": "<p>Release Information</p> <ul> <li>Version: SP.21</li> <li>Release Date: October 14, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-10-14_SP.21/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-10-14_SP.21/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-10-14_SP.21/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Allow Pi OS updates, on request in Slicer</li> <li>Collect network usage data</li> <li>Do not let USB devices sleep</li> <li>Support browser restart on inactivity</li> <li>OS update support</li> <li>Limit inbound to Slicer only</li> <li>System logging service active</li> <li>Bluetooth change to not show RSSI column, because we no longer collect that data</li> </ul>"}, {"location": "releases/2021-10-14_SP.21/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.2 Do not show RSSI column, because we no longer collect that data pi_monitor M.2.2 Same as before pi_runner R.3.8 Be able to pull service updates from Slicer, Do not let USB devices sleep, Support browser inactivity restart, Network monitoring pi_hmi H.2.0 Same as before pi_network N.2.0 Same as before pi_security S.1.5 Scan for updates, Allow Slicer to have us take updates, Limit inbound to Slicer only pi_logging L.1.4 System activity logging"}, {"location": "releases/2021-10-14_SP.21/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2021-10-14_SP.21/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-10-14_SP.21/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-11-29_SP.22/", "title": "SP.22 (2021-11-29)", "text": "<p>Release Information</p> <ul> <li>Version: SP.22</li> <li>Release Date: November 29, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-11-29_SP.22/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-11-29_SP.22/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-11-29_SP.22/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Network: If the identified lan changes (wlan0, wlan1, eth0), then send new status</li> <li>Network: Fix the reporting, to correctly show wlanX, when connected</li> <li>Network: Add support to allow wlan1 (external wifi adapter) to be configured</li> <li>Network: Add to the network report: the adapter providing the access (wlan0, wlan1, eth0, ...)</li> </ul>"}, {"location": "releases/2021-11-29_SP.22/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.2 - pi_hmi H.3.1 - pi_logging L.1.8 - pi_monitor M.2.2 - pi_network N.3.6 - pi_runner R.4.1 - pi_security S.1.6 -"}, {"location": "releases/2021-11-29_SP.22/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>CUPS installed with User printer:printer</li> </ul>"}, {"location": "releases/2021-11-29_SP.22/#additional-information", "title": "Additional Information", "text": "<p>Network Adapter Information</p> <ul> <li>eth0: Wired network ethernet port on the pi</li> <li>wlan0: Internal WiFi radio</li> <li>wlan1: External WiFi radio</li> </ul> <p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-11-29_SP.22/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2021-12-21_SP.23/", "title": "SP.23 (2021-12-21)", "text": "<p>Release Information</p> <ul> <li>Version: SP.23</li> <li>Release Date: December 21, 2021</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2021-12-21_SP.23/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2021-12-21_SP.23/#changes", "title": "Changes", "text": ""}, {"location": "releases/2021-12-21_SP.23/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>External WiFi supported</li> <li>IOT temporarily disabled</li> <li>Printing support</li> </ul>"}, {"location": "releases/2021-12-21_SP.23/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.2 - pi_hmi H.3.1 - pi_logging L.1.8 - pi_monitor M.2.2 - pi_network N.3.1 - pi_runner R.5.0 - pi_security S.1.6 -"}, {"location": "releases/2021-12-21_SP.23/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Started with 2.3.1 image, and bring all services forward, except network, leaving it at N.3.1</li> <li>Re-includes CUPS (printing), which was stepped over previously</li> </ul>"}, {"location": "releases/2021-12-21_SP.23/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2021-12-21_SP.23/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-01-14_SP.24/", "title": "SP.24 (2022-01-14)", "text": "<p>Release Information</p> <ul> <li>Version: SP.24</li> <li>Release Date: January 14, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-01-14_SP.24/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-01-14_SP.24/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-01-14_SP.24/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Wireless network connection to IOT supported as a manual configuration choice (internal and external radio)</li> <li>New HMI options: Slideshow mode, Auto page refresh mode</li> </ul>"}, {"location": "releases/2022-01-14_SP.24/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.3 - pi_hmi H.3.6 - pi_logging L.2.0 - pi_monitor M.2.2 - pi_network N.4.0 - pi_runner R.5.7 - pi_security S.1.6 -"}, {"location": "releases/2022-01-14_SP.24/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Re-includes CUPS (printing), which was stepped over previously</li> </ul>"}, {"location": "releases/2022-01-14_SP.24/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-01-14_SP.24/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-02-21_SP.25/", "title": "SP.25 (2022-02-21)", "text": "<p>Release Information</p> <ul> <li>Version: SP.25</li> <li>Release Date: February 21, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-02-21_SP.25/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-02-21_SP.25/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-02-21_SP.25/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Wireless network connection to IOT supported as a manual configuration choice (internal and external radio)</li> <li>New HMI options: Slideshow mode, Auto page refresh mode, Interact with local pi_config page</li> <li>Start with 2.3.4, and update with all current services</li> </ul>"}, {"location": "releases/2022-02-21_SP.25/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.4 - pi_config C.1.0 - pi_hmi H.3.8 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.4.0 - pi_runner R.5.9 - pi_security S.1.7 -"}, {"location": "releases/2022-02-21_SP.25/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>Includes CUPS (printing), which was stepped over in 2.3.3</li> </ul>"}, {"location": "releases/2022-02-21_SP.25/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-02-21_SP.25/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-03-02_SP.26/", "title": "SP.26 (2022-03-02)", "text": "<p>Release Information</p> <ul> <li>Version: SP.26</li> <li>Release Date: March 2, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-03-02_SP.26/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-03-02_SP.26/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-03-02_SP.26/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Networking service now has a new certificate to connect to corp WiFi, that expires March 2, 2023</li> </ul>"}, {"location": "releases/2022-03-02_SP.26/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.4 - pi_config C.1.0 - pi_hmi H.3.9 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.0 - pi_runner R.5.9 - pi_security S.1.7 -"}, {"location": "releases/2022-03-02_SP.26/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2022-03-02_SP.26/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-03-02_SP.26/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-03-07_SP.27/", "title": "SP.27 (2022-03-07)", "text": "<p>Release Information</p> <ul> <li>Version: SP.27</li> <li>Release Date: March 7, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-03-07_SP.27/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-03-07_SP.27/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-03-07_SP.27/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Networking service now starts from zero connections each startup, to get clean onto corp</li> </ul>"}, {"location": "releases/2022-03-07_SP.27/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.4 - pi_config C.1.0 - pi_hmi H.3.9 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.2 - pi_runner R.5.9 - pi_security S.1.7 -"}, {"location": "releases/2022-03-07_SP.27/#image-changes", "title": "Image Changes", "text": "<p>Image Updates</p> <ul> <li>No Changes</li> </ul>"}, {"location": "releases/2022-03-07_SP.27/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-03-07_SP.27/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-03-11_SP.28/", "title": "SP.28 (2022-03-11)", "text": "<p>Release Information</p> <ul> <li>Version: SP.28</li> <li>Release Date: March 11, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-03-11_SP.28/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-03-11_SP.28/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-03-11_SP.28/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Updates on HMI to show Service Pack as SP, instead of showing image version</li> </ul>"}, {"location": "releases/2022-03-11_SP.28/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.4 - pi_config C.1.0 - pi_hmi H.4.0 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.2 - pi_runner R.6.1 - pi_security S.1.7 -"}, {"location": "releases/2022-03-11_SP.28/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-03-11_SP.28/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-04-20_SP.29/", "title": "SP.29 (2022-04-20)", "text": "<p>Release Information</p> <ul> <li>Version: SP.29</li> <li>Release Date: April 20, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-04-20_SP.29/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-04-20_SP.29/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-04-20_SP.29/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Network WiFi Hopper option to jump to closest access point</li> </ul>"}, {"location": "releases/2022-04-20_SP.29/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.4 - pi_config C.1.0 - pi_hmi H.4.1 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.3 - pi_runner R.6.1 - pi_security S.1.7 -"}, {"location": "releases/2022-04-20_SP.29/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-04-20_SP.29/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-04-22_SP.30/", "title": "SP.30 (2022-04-22)", "text": "<p>Release Information</p> <ul> <li>Version: SP.30</li> <li>Release Date: April 22, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-04-22_SP.30/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-04-22_SP.30/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-04-22_SP.30/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Bluetooth 4 digit list, and bluetooth rescan link</li> </ul>"}, {"location": "releases/2022-04-22_SP.30/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.0 - pi_hmi H.4.1 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.3 - pi_runner R.6.1 - pi_security S.1.7 -"}, {"location": "releases/2022-04-22_SP.30/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-04-22_SP.30/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-04-25_SP.31/", "title": "SP.31 (2022-04-25)", "text": "<p>Release Information</p> <ul> <li>Version: SP.31</li> <li>Release Date: April 25, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-04-25_SP.31/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-04-25_SP.31/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-04-25_SP.31/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>WiFi coaster on access point change (hold network settings for a time period)</li> </ul>"}, {"location": "releases/2022-04-25_SP.31/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.0 - pi_hmi H.4.1 - pi_logging L.2.4 - pi_monitor M.2.3 - pi_network N.5.4 - pi_runner R.6.4 - pi_security S.1.7 -"}, {"location": "releases/2022-04-25_SP.31/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-04-25_SP.31/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-06-01_SP.32/", "title": "SP.32 (2022-06-01)", "text": "<p>Release Information</p> <ul> <li>Version: SP.32</li> <li>Release Date: June 1, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-06-01_SP.32/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-06-01_SP.32/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-06-01_SP.32/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Home page shows if a browser reset is wanted</li> <li>Logging cleans up old logs</li> </ul>"}, {"location": "releases/2022-06-01_SP.32/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.0 - pi_hmi H.4.2 Zero reset when updated pi_logging L.2.7 - pi_monitor M.2.4 - pi_network N.5.4 - pi_runner R.6.8 Zero reset when updated pi_security S.1.7 -"}, {"location": "releases/2022-06-01_SP.32/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-06-01_SP.32/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-06-28_SP.33/", "title": "SP.33 (2022-06-28)", "text": "<p>Release Information</p> <ul> <li>Version: SP.33</li> <li>Release Date: June 28, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-06-28_SP.33/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-06-28_SP.33/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-06-28_SP.33/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Runner tracks user activity more accurately (drops removed devices)</li> </ul>"}, {"location": "releases/2022-06-28_SP.33/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.0 - pi_hmi H.4.2 Zero reset when updated pi_logging L.2.7 - pi_monitor M.2.4 - pi_network N.5.4 - pi_runner R.6.9 Zero reset when updated pi_security S.1.7 -"}, {"location": "releases/2022-06-28_SP.33/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-06-28_SP.33/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-08-24_SP.34/", "title": "SP.34 (2022-08-24)", "text": "<p>Release Information</p> <ul> <li>Version: SP.34</li> <li>Release Date: August 24, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-08-24_SP.34/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-08-24_SP.34/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-08-24_SP.34/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Runner reports Chromium version</li> </ul>"}, {"location": "releases/2022-08-24_SP.34/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.0 - pi_hmi H.4.2 Zero reset when updated pi_logging L.2.7 - pi_monitor M.2.4 - pi_network N.5.4 - pi_runner R.7.0 Report Chromium Browser version pi_security S.1.7 -"}, {"location": "releases/2022-08-24_SP.34/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-08-24_SP.34/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-10-11_SP.35/", "title": "SP.35 (2022-10-11)", "text": "<p>Release Information</p> <ul> <li>Version: SP.35</li> <li>Release Date: October 11, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-10-11_SP.35/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-10-11_SP.35/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-10-11_SP.35/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>External radio improved support</li> </ul>"}, {"location": "releases/2022-10-11_SP.35/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.1 Handle multiple call home entries pi_hmi H.4.4 Show more network details on page 2, and int/ext on page1 pi_logging L.2.7 - pi_monitor M.2.4 - pi_network N.6.7 Better external radio support pi_runner R.7.4 Report which radio is being used pi_security S.1.8 Allow ssh from any configure call home entry"}, {"location": "releases/2022-10-11_SP.35/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-10-11_SP.35/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-12-05_SP.36/", "title": "SP.36 (2022-12-05)", "text": "<p>Release Information</p> <ul> <li>Version: SP.36</li> <li>Release Date: December 5, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-12-05_SP.36/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-12-05_SP.36/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-12-05_SP.36/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support manual image configuration at the Raspberry Pi</li> </ul>"}, {"location": "releases/2022-12-05_SP.36/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.2 Support manual configuration of a generic image pi_hmi H.4.6 If manual settings are present, then use them, otherwise default to original pi_logging L.2.9 Make a privoxy log by day, that is viewable on the pi pi_monitor M.2.4 - pi_network N.6.8 If manual network settings are found, then use those pi_organization O.0.3 Set the company logo pi_runner R.7.6 Handle manual settings, handle multiple call_homes cleanly, add timeout on get_cpu_temperature pi_security S.1.9 Handle multiple call_homes cleanly"}, {"location": "releases/2022-12-05_SP.36/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-12-05_SP.36/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2022-12-07_SP.37/", "title": "SP.37 (2022-12-07)", "text": "<p>Release Information</p> <ul> <li>Version: SP.37</li> <li>Release Date: December 7, 2022</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2022-12-07_SP.37/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2022-12-07_SP.37/#changes", "title": "Changes", "text": ""}, {"location": "releases/2022-12-07_SP.37/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Better SSH rules definition for the case of multiple address configuration</li> </ul>"}, {"location": "releases/2022-12-07_SP.37/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.2 Support manual configuration of a generic image pi_hmi H.4.6 If manual settings are present, then use them, otherwise default to original pi_logging L.2.9 Make a privoxy log by day, that is viewable on the pi pi_monitor M.2.4 - pi_network N.6.8 If manual network settings are found, then use those pi_organization O.1.0 Set the company specifics from settings pi_runner R.7.7 Handle manual settings, handle multiple call_homes cleanly, add timeout on get_cpu_temperature pi_security S.2.0 Pull from settings, to add sshd target(s) and allowed user(s) pi_settings s.0.5 The one source of configuration settings"}, {"location": "releases/2022-12-07_SP.37/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2022-12-07_SP.37/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-01-27_SP.38/", "title": "SP.38 (2023-01-27)", "text": "<p>Release Information</p> <ul> <li>Version: SP.38</li> <li>Release Date: January 27, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-01-27_SP.38/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2023-01-27_SP.38/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-01-27_SP.38/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support Remote Commands</li> </ul>"}, {"location": "releases/2023-01-27_SP.38/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.2 - pi_hmi H.4.6 - pi_logging L.2.9 - pi_monitor M.2.4 - pi_network N.6.9 - pi_organization O.1.1 - pi_runner R.8.0 Remote commands from Slicer pi_security S.2.0 - pi_settings s.0.5 -"}, {"location": "releases/2023-01-27_SP.38/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-01-27_SP.38/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-02-07_SP.39/", "title": "SP.39 (2023-02-07)", "text": "<p>Release Information</p> <ul> <li>Version: SP.39</li> <li>Release Date: February 7, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-02-07_SP.39/#known-issues", "title": "Known Issues", "text": "<p>Current Status</p> <ul> <li>None reported</li> </ul>"}, {"location": "releases/2023-02-07_SP.39/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-02-07_SP.39/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support remote commands (2023.02.15 bumped R.8.1 to R.8.2 to handle upgrade from really early images)</li> </ul>"}, {"location": "releases/2023-02-07_SP.39/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.2 - pi_hmi H.4.6 - pi_logging L.2.9 - pi_monitor M.2.4 - pi_network N.7.1 Corp wifi cert to 2024.02.06 pi_organization O.1.1 - pi_runner R.8.2 Remote commands from <PERSON>lice<PERSON>, reset corp wifi on forced browser reset, install apscheduler if it is missing pi_security S.2.0 - pi_settings s.0.6 Corp wifi cert to 2024.02.06"}, {"location": "releases/2023-02-07_SP.39/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-02-07_SP.39/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-04-03_SP.40/", "title": "SP.40 (2023-04-03)", "text": "<p>Release Information</p> <ul> <li>Version: SP.40</li> <li>Release Date: April 3, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-04-03_SP.40/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-04-03_SP.40/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Remove corp WiFi</li> </ul>"}, {"location": "releases/2023-04-03_SP.40/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.5 - pi_config C.1.2 - pi_hmi H.4.7 Remove option to switch to corp pi_logging L.3.1 Periodically clear chromium history to save drive space pi_monitor M.2.4 - pi_network N.7.2 Remove corp wifi as a connection option pi_organization O.1.1 - pi_runner R.8.3 Log and report configuration changes pi_security S.2.0 - pi_settings s.0.7 Remove corp wifi cert"}, {"location": "releases/2023-04-03_SP.40/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-04-03_SP.40/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-05-02_SP.41/", "title": "SP.41 (2023-05-02)", "text": "<p>Release Information</p> <ul> <li>Version: SP.41</li> <li>Release Date: May 2, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-05-02_SP.41/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-05-02_SP.41/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Update Bluetooth configuration 2D Barcode, to ignore capslock</li> </ul>"}, {"location": "releases/2023-05-02_SP.41/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 Updated configuration 2D barcode pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.1 - pi_monitor M.2.4 - pi_network N.7.2 - pi_organization O.1.1 - pi_runner R.8.3 - pi_security S.2.0 - pi_settings s.0.7 -"}, {"location": "releases/2023-05-02_SP.41/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-05-02_SP.41/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-05-31_SP.42/", "title": "SP.42 (2023-05-31)", "text": "<p>Release Information</p> <ul> <li>Version: SP.42</li> <li>Release Date: May 31, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-05-31_SP.42/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-05-31_SP.42/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Update Bluetooth configuration 2D Barcode, to ignore capslock</li> </ul>"}, {"location": "releases/2023-05-31_SP.42/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.2 Clean up /var/log/wtmp also pi_monitor M.2.4 - pi_network N.7.2 - pi_organization O.1.1 - pi_runner R.8.4 Add logging of the last active user device pi_security S.2.1 Support older base images pi_settings s.0.7 -"}, {"location": "releases/2023-05-31_SP.42/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-05-31_SP.42/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-07-03_SP.43/", "title": "SP.43 (2023-07-03)", "text": "<p>Release Information</p> <ul> <li>Version: SP.43</li> <li>Release Date: July 3, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-07-03_SP.43/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-07-03_SP.43/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>On network config page, show connection rate with the channel number</li> </ul>"}, {"location": "releases/2023-07-03_SP.43/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.2 - pi_monitor M.2.4 - pi_network N.7.3 Show connection speeds pi_organization O.1.1 - pi_runner R.8.4 - pi_security S.2.1 - pi_settings s.0.7 -"}, {"location": "releases/2023-07-03_SP.43/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-07-03_SP.43/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-08-21_SP.44/", "title": "SP.44 (2023-08-21)", "text": "<p>Release Information</p> <ul> <li>Version: SP.44</li> <li>Release Date: August 21, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-08-21_SP.44/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-08-21_SP.44/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Activity tracking now correctly ignores input devices that are in a non-connected state</li> <li>Logging will now report the device names for all found devices</li> <li>On network config page, show connection rate with the channel number</li> </ul>"}, {"location": "releases/2023-08-21_SP.44/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.3 Report device names pi_monitor M.2.4 - pi_network N.7.3 - pi_organization O.1.1 - pi_runner R.8.7 Activity monitoring ignore disconnected inputs, and start with age of one day pi_security S.2.1 - pi_settings s.0.7 -"}, {"location": "releases/2023-08-21_SP.44/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-08-21_SP.44/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-09-18_SP.45/", "title": "SP.45 (2023-09-18)", "text": "<p>Release Information</p> <ul> <li>Version: SP.45</li> <li>Release Date: September 18, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-09-18_SP.45/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-09-18_SP.45/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Network service now reporting the count of NIC (Network Interface Connection) changes between internal and external</li> </ul>"}, {"location": "releases/2023-09-18_SP.45/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.3 - pi_monitor M.2.4 - pi_network N.7.4 Report nic change count (since imaged) pi_organization O.1.1 - pi_runner R.8.7 - pi_security S.2.1 - pi_settings s.0.7 -"}, {"location": "releases/2023-09-18_SP.45/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-09-18_SP.45/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2023-10-25_SP.46/", "title": "SP.46 (2023-10-25)", "text": "<p>Release Information</p> <ul> <li>Version: SP.46</li> <li>Release Date: October 25, 2023</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2023-10-25_SP.46/#changes", "title": "Changes", "text": ""}, {"location": "releases/2023-10-25_SP.46/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Network connection monitoring, and elimination of Corp WiFi from the codebase</li> </ul>"}, {"location": "releases/2023-10-25_SP.46/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.7 - pi_logging L.3.3 - pi_monitor M.2.4 - pi_network N.7.6 Manage network connection state, and eliminate corp WiFi from codebase pi_organization O.1.1 - pi_runner R.8.8 Support extended display setting values pi_security S.2.1 - pi_settings s.0.7 -"}, {"location": "releases/2023-10-25_SP.46/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2023-10-25_SP.46/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-02-06_SP.47/", "title": "SP.47 (2024-02-06)", "text": "<p>Release Information</p> <ul> <li>Version: SP.47</li> <li>Release Date: February 6, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-02-06_SP.47/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-02-06_SP.47/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Runner update to report IP address</li> </ul>"}, {"location": "releases/2024-02-06_SP.47/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.9 Show management block ID; IP address source change pi_logging L.3.3 - pi_monitor M.2.7 Handle python 3.x; IP address source change pi_network N.7.6 - pi_organization O.1.1 - pi_runner R.9.0 Handle python 3.x; report IP address on periodic update pi_security S.2.1 - pi_settings s.0.7 -"}, {"location": "releases/2024-02-06_SP.47/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-02-06_SP.47/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-03-11_SP.48/", "title": "SP.48 (2024-03-11)", "text": "<p>Release Information</p> <ul> <li>Version: SP.48</li> <li>Release Date: March 11, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-03-11_SP.48/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-03-11_SP.48/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support MP4 for multimedia shows</li> </ul>"}, {"location": "releases/2024-03-11_SP.48/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.6 - pi_config C.1.2 - pi_hmi H.4.9 - pi_logging L.3.3 - pi_monitor M.2.7 - pi_network N.7.6 - pi_organization O.1.1 - pi_runner R.9.1 Support mp4 for shows content pi_security S.2.1 - pi_settings s.1.0 Clean out old settings"}, {"location": "releases/2024-03-11_SP.48/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-03-11_SP.48/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-03-12_SP.49/", "title": "SP.49 (2024-03-12)", "text": "<p>Release Information</p> <ul> <li>Version: SP.49</li> <li>Release Date: March 12, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-03-12_SP.49/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-03-12_SP.49/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Services support for Raspberry Pi 5 (python3.x)</li> </ul>"}, {"location": "releases/2024-03-12_SP.49/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 Support new/latest pi OS (Backport from later release) pi_config C.1.3 Support Raspberry Pi5 pi_hmi H.5.0 Support Raspberry Pi5 pi_logging L.3.4 Support Raspberry Pi5 pi_monitor M.2.8 Support Raspberry Pi5 pi_network N.7.7 Support Raspberry Pi5 pi_organization O.1.2 Support Raspberry Pi5 pi_runner R.9.1 - pi_security S.2.2 Support Raspberry Pi5 pi_settings s.1.1 Support Raspberry Pi5"}, {"location": "releases/2024-03-12_SP.49/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-03-12_SP.49/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-04-01_SP.50/", "title": "SP.50 (2024-04-01)", "text": "<p>Release Information</p> <ul> <li>Version: SP.50</li> <li>Release Date: April 1, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-04-01_SP.50/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-04-01_SP.50/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support screen reporting, looking for resolution issues</li> </ul>"}, {"location": "releases/2024-04-01_SP.50/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 Support new/latest pi OS (Backport from later release) pi_config C.1.3 - pi_hmi H.5.0 - pi_logging L.3.4 - pi_monitor M.2.8 - pi_network N.7.8 Support new NetworkManger handling of wired connection pi_organization O.1.2 - pi_runner R.9.3 Screen reporting, clear timezone, installs for Pi5 pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2024-04-01_SP.50/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-04-01_SP.50/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-04-18_SP.51/", "title": "SP.51 (2024-04-18)", "text": "<p>Release Information</p> <ul> <li>Version: SP.51</li> <li>Release Date: April 18, 2024</li> <li>Type: Service Pack</li> <li>Base Image: 2.4.2</li> </ul>"}, {"location": "releases/2024-04-18_SP.51/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul>"}, {"location": "releases/2024-04-18_SP.51/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-04-18_SP.51/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Support screen reporting, looking for resolution issues</li> </ul>"}, {"location": "releases/2024-04-18_SP.51/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 Support new/latest pi OS pi_config C.1.3 - pi_hmi H.5.0 - pi_logging L.3.4 - pi_monitor M.2.8 - pi_network N.7.8 - pi_organization O.1.2 - pi_runner R.9.4 Send back OS version, and bluez version to Slicer pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2024-04-18_SP.51/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-04-18_SP.51/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-11-12_SP.52/", "title": "SP.52 (2024-11-12)", "text": "<p>Release Information</p> <ul> <li>Version: SP.52</li> <li>Release Date: November 12, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-11-12_SP.52/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul>"}, {"location": "releases/2024-11-12_SP.52/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-11-12_SP.52/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Change the time setting to set localtime instead of timezone</li> </ul>"}, {"location": "releases/2024-11-12_SP.52/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 - pi_config C.1.3 - pi_hmi H.5.2 - pi_logging L.3.5 Vacuum the journal down to 5M, periodically pi_monitor M.2.8 - pi_network N.7.9 - pi_organization O.1.2 - pi_runner R.9.6 - pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2024-11-12_SP.52/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-11-12_SP.52/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2024-12-16_SP.53/", "title": "SP.53 (2024-12-16)", "text": "<p>Release Information</p> <ul> <li>Version: SP.53</li> <li>Release Date: December 16, 2024</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2024-12-16_SP.53/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul> <p>Functionality Issues</p> <ul> <li>Remove temporary files before writing new ones</li> </ul>"}, {"location": "releases/2024-12-16_SP.53/#changes", "title": "Changes", "text": ""}, {"location": "releases/2024-12-16_SP.53/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Allow for text based dynamic content page to be built (Display Board)</li> </ul>"}, {"location": "releases/2024-12-16_SP.53/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 Support new/latest pi OS pi_config C.1.3 - pi_hmi H.5.1 Allow for text based dynamic content page to be built; remove temporary files before writing new ones pi_logging L.3.4 - pi_monitor M.2.8 - pi_network N.7.9 Remove temporary files before writing new ones pi_organization O.1.2 - pi_runner R.9.6 Remove temporary files before writing new ones pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2024-12-16_SP.53/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2024-12-16_SP.53/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2025-01-24_SP.54/", "title": "SP.54 (2025-01-24)", "text": "<p>Release Information</p> <ul> <li>Version: SP.54</li> <li>Release Date: January 24, 2025</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2025-01-24_SP.54/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul> <p>Functionality Issues</p> <ul> <li>Remove temporary files before writing new ones</li> </ul>"}, {"location": "releases/2025-01-24_SP.54/#changes", "title": "Changes", "text": ""}, {"location": "releases/2025-01-24_SP.54/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Allow for text based dynamic content page to be built (Display Board)</li> </ul>"}, {"location": "releases/2025-01-24_SP.54/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 - pi_config C.1.3 - pi_hmi H.5.2 Allow dynamic page content to have slash (/) and question mark (?) pi_logging L.3.4 - pi_monitor M.2.8 - pi_network N.7.9 - pi_organization O.1.2 - pi_runner R.9.6 - pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2025-01-24_SP.54/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2025-01-24_SP.54/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2025-01-24_SP.55/", "title": "SP.55 (2025-01-24)", "text": "<p>Release Information</p> <ul> <li>Version: SP.55</li> <li>Release Date: January 24, 2025</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2025-01-24_SP.55/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul> <p>Functionality Issues</p> <ul> <li>Remove temporary files before writing new ones</li> </ul>"}, {"location": "releases/2025-01-24_SP.55/#changes", "title": "Changes", "text": ""}, {"location": "releases/2025-01-24_SP.55/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Vacuum the journal down to 5M, periodically</li> </ul>"}, {"location": "releases/2025-01-24_SP.55/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 - pi_config C.1.3 - pi_hmi H.5.2 - pi_logging L.3.5 Vacuum the journal down to 5M, periodically pi_monitor M.2.8 - pi_network N.7.9 - pi_organization O.1.2 - pi_runner R.9.6 - pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2025-01-24_SP.55/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2025-01-24_SP.55/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/2025-03-12_SP.56/", "title": "SP.56 (2025-03-12)", "text": "<p>Release Information</p> <ul> <li>Version: SP.56</li> <li>Release Date: March 12, 2025</li> <li>Type: Service Pack</li> </ul>"}, {"location": "releases/2025-03-12_SP.56/#known-issues", "title": "Known Issues", "text": "<p>Hardware Limitations</p> <ul> <li>MP4 playback audio does not work to HDMI when using a Raspberry Pi 4, but does work with a Raspberry Pi 5</li> <li>Screen resolution is not yet settable from a Slicer configuration</li> </ul> <p>Functionality Issues</p> <ul> <li>Increment screen grab, even if there are errors on sending the image</li> </ul>"}, {"location": "releases/2025-03-12_SP.56/#changes", "title": "Changes", "text": ""}, {"location": "releases/2025-03-12_SP.56/#system-maintenance", "title": "System Maintenance", "text": "<ul> <li>Vacuum the journal down to 5M, periodically</li> </ul>"}, {"location": "releases/2025-03-12_SP.56/#component-updates", "title": "Component Updates", "text": "Component Version Changes pi_bluetooth B.2.8 - pi_config C.1.3 - pi_hmi H.5.2 - pi_logging L.3.5 - pi_monitor M.2.8 - pi_network N.7.9 - pi_organization O.1.2 - pi_runner R.9.7 Increment screen grab, even if there are errors on sending the image pi_security S.2.2 - pi_settings s.1.1 -"}, {"location": "releases/2025-03-12_SP.56/#additional-information", "title": "Additional Information", "text": "<p>Upgrade Notes</p> <p>No special upgrade procedures are required for this release.</p>"}, {"location": "releases/2025-03-12_SP.56/#support", "title": "Support", "text": "<p>For any issues or questions regarding this release, please contact the support team.</p>"}, {"location": "releases/release-notes/", "title": "Release Notes", "text": ""}, {"location": "releases/release-notes/#1-known-issues", "title": "1. Known Issues", "text": ""}, {"location": "releases/release-notes/#20231101", "title": "2023.11.01", "text": "<p>Screen resolution not correctly automatically detecting the screen attached:</p> <ul> <li>Sometimes the default \"automatically detect\" setting results in the screen being oversized, undersized, or generally incorrect.</li> <li>This can happen when unplugging the HDMI cable from the Pi, powering off, then back on, and after a minute, plugging the HDMI cable back in.</li> <li>Fix: Reboot the device twice. This can be accomplished remotely via the Slicer interface:</li> <li>Browse to the device configuration page:     Slicer Interface</li> <li>Find the \"Request Reboot\" line, select the reboot marker value from the dropdown, then click \"Submit\".</li> <li>Wait a few minutes, refresh the Slicer page to get a new reboot marker number, then request another reboot.</li> <li>Wait a few minutes and check that the device displays correctly (filled to edges, not overfilled).</li> </ul>"}, {"location": "releases/release-notes/#2-configuration-notes", "title": "2. Configuration Notes", "text": ""}, {"location": "releases/release-notes/#to-configure-printing-on-the-pi-base-image-235-and-later", "title": "To configure printing on the Pi (Base Image 2.3.5 and later):", "text": "<ul> <li>On the main page, click the bold \"ID\" in the upper-left corner.</li> <li>On the line with \"Printer\", click the bold \"Configure\".</li> <li>Navigate to: Administration -&gt; Add Printer</li> <li>Username: printer</li> <li>Password: printer</li> <li>When done, press Alt + F4 to return.</li> <li>Click the bold \"ID\" to return to the main page.</li> </ul>"}, {"location": "releases/release-notes/#3-target-service-pack", "title": "3. Target Service Pack", "text": ""}, {"location": "releases/release-notes/#20240217", "title": "2024.02.17", "text": "<p>Bring all up to this level.</p> <ul> <li>Target SP: <code>SP.47</code></li> <li>Optional Service: <code>pi_thirdparty, P.0.0 to P.9.9</code></li> </ul>"}, {"location": "releases/release-notes/#4-rollback-service-pack", "title": "4. Rollback Service Pack", "text": "<ul> <li>Rollback SP: <code>SP.47</code></li> <li>The rollback service pack is the lowest version allowed for rollback, both for the collection and individual services.</li> </ul>"}, {"location": "releases/versioning/", "title": "Versioning Guide", "text": ""}, {"location": "releases/versioning/#overview", "title": "Overview", "text": "<p>Semantic Versioning (SemVer) is a versioning scheme that aims to convey meaning about the underlying changes in a release through the version number itself. It provides a consistent way to communicate when backwards-incompatible changes, new features, or bug fixes are introduced.</p>"}, {"location": "releases/versioning/#basic-format", "title": "Basic Format", "text": "<p>A semantic version number takes the form of <code>X.Y.Z</code> where:</p> <ul> <li>X is the major version</li> <li>Y is the minor version</li> <li>Z is the patch version</li> </ul> <p>Additional labels for pre-release or build metadata can be appended as extensions to this format.</p>"}, {"location": "releases/versioning/#version-increments", "title": "Version Increments", "text": ""}, {"location": "releases/versioning/#major-version-x", "title": "Major Version (X)", "text": "<p>Increment when making incompatible API changes.</p> <ul> <li>Changes that break backward compatibility</li> <li>Removing deprecated features</li> <li>Changing the expected behavior of existing functionality</li> </ul> <p>Example: <code>1.9.0</code> → <code>2.0.0</code></p>"}, {"location": "releases/versioning/#minor-version-y", "title": "Minor Version (Y)", "text": "<p>Increment when adding functionality in a backward compatible manner.</p> <ul> <li>New features that don't break existing functionality</li> <li>Deprecating existing functionality (but not removing it)</li> <li>Large internal refactors that don't change the public API</li> </ul> <p>Example: <code>1.8.3</code> → <code>1.9.0</code></p>"}, {"location": "releases/versioning/#patch-version-z", "title": "Patch Version (Z)", "text": "<p>Increment when making backward compatible bug fixes.</p> <ul> <li>Bug fixes and patches that don't change the API</li> <li>Performance improvements</li> <li>Small internal changes</li> </ul> <p>Example: <code>1.8.2</code> → <code>1.8.3</code></p>"}, {"location": "releases/versioning/#pre-release-versions", "title": "Pre-release Versions", "text": "<p>Pre-release versions can be denoted by appending a hyphen and a series of dot-separated identifiers after the patch version:</p> <p>1.0.0-alpha 1.0.0-alpha.1 1.0.0-beta 1.0.0-beta.2 1.0.0-rc.1</p>"}, {"location": "releases/versioning/#build-metadata", "title": "Build Metadata", "text": "<p>Build metadata can be denoted by appending a plus sign and a series of dot-separated identifiers after the patch or pre-release version:</p> <p>1.0.0+build.1 1.0.0-alpha+build.5</p>"}, {"location": "releases/versioning/#version-precedence", "title": "Version Precedence", "text": "<p>Precedence refers to how versions are compared to each other when ordered.</p> <ol> <li>Major, minor, and patch versions are always compared numerically.</li> <li> <p>Example: <code>1.0.0</code> &lt; <code>2.0.0</code> &lt; <code>2.1.0</code> &lt; <code>2.1.1</code></p> </li> <li> <p>When major, minor, and patch are equal, a pre-release version has lower precedence than a normal version.</p> </li> <li> <p>Example: <code>1.0.0-alpha</code> &lt; <code>1.0.0</code></p> </li> <li> <p>Precedence for pre-release versions is determined by comparing each dot-separated identifier numerically if they are numbers, and lexically if they are letters or hyphens.</p> </li> <li>Example: <code>1.0.0-alpha</code> &lt; <code>1.0.0-alpha.1</code> &lt; <code>1.0.0-beta</code> &lt; <code>1.0.0-beta.2</code> &lt; <code>1.0.0-rc.1</code> &lt; <code>1.0.0</code></li> </ol>"}, {"location": "releases/versioning/#adoption-in-slicer-project", "title": "Adoption in Slicer Project", "text": ""}, {"location": "releases/versioning/#current-implementation", "title": "Current Implementation", "text": "<p>In the Slicer project, we follow Semantic Versioning with the SP.XX format representing our releases, where SP stands for \"Service Pack\" and XX is an incremental number.</p> <p>Each SP release corresponds to a specific semantic version and may contain:</p> <ul> <li>Bug fixes (patch)</li> <li>New features (minor)</li> <li>Breaking changes (major)</li> </ul>"}, {"location": "releases/versioning/#sp-to-semver-mapping", "title": "SP to SemVer Mapping", "text": "SP Release Semantic Version Release Date Type of Changes SP.56 3.2.0 2025-03-12 Feature Release SP.55 3.1.1 2025-01-24 Bug Fix Release SP.54 3.1.0 2025-01-24 Feature Release SP.53 3.0.1 2024-12-16 Bug Fix Release"}, {"location": "releases/versioning/#version-notes", "title": "Version Notes", "text": "<ul> <li>Major version changes (X) typically occur with significant infrastructure changes or when breaking compatibility with older clients.</li> <li>Minor version changes (Y) occur with new feature additions that maintain backwards compatibility.</li> <li>Patch version changes (Z) occur with bug fixes and minor improvements.</li> </ul>"}, {"location": "releases/versioning/#best-practices", "title": "Best Practices", "text": "<ol> <li>Document All Changes: Maintain detailed release notes for each version.</li> <li>Version Control Tags: Tag each release in your version control system.</li> <li>Communicate Changes: Clearly communicate when a new version introduces breaking changes.</li> <li>Deprecation Notices: Use deprecation notices in minor releases before removing functionality in a major release.</li> <li>Testing: Ensure comprehensive testing for all versioned releases.</li> </ol>"}, {"location": "releases/versioning/#references", "title": "References", "text": "<ul> <li>Official Semantic Versioning Specification</li> <li>NPM's Semantic Versioning Documentation</li> <li>GitHub's Semantic Versioning Guide</li> </ul>"}]}