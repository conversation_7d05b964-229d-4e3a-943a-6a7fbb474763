
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
      
        <link rel="prev" href="../../generic-config/">
      
      
        <link rel="next" href="../gcp/gcp-servers/">
      
      
      <link rel="icon" href="../../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.9">
    
    
      
        <title>Rocky Server - Slicer Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../../assets/stylesheets/main.4af4bdda.min.css">
      
        
        <link rel="stylesheet" href="../../../assets/stylesheets/palette.06af60db.min.css">
      
      
  
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
    
    
  
  
  <style>:root{--md-admonition-icon--note:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M1%207.775V2.75C1%201.784%201.784%201%202.75%201h5.025c.464%200%20.91.184%201.238.513l6.25%206.25a1.75%201.75%200%200%201%200%202.474l-5.026%205.026a1.75%201.75%200%200%201-2.474%200l-6.25-6.25A1.75%201.75%200%200%201%201%207.775m1.5%200c0%20.066.026.13.073.177l6.25%206.25a.25.25%200%200%200%20.354%200l5.025-5.025a.25.25%200%200%200%200-.354l-6.25-6.25a.25.25%200%200%200-.177-.073H2.75a.25.25%200%200%200-.25.25ZM6%205a1%201%200%201%201%200%202%201%201%200%200%201%200-2%22/%3E%3C/svg%3E');--md-admonition-icon--abstract:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2.5%201.75v11.5c0%20.138.112.25.25.25h3.17a.75.75%200%200%201%200%201.5H2.75A1.75%201.75%200%200%201%201%2013.25V1.75C1%20.784%201.784%200%202.75%200h8.5C12.216%200%2013%20.784%2013%201.75v7.736a.75.75%200%200%201-1.5%200V1.75a.25.25%200%200%200-.25-.25h-8.5a.25.25%200%200%200-.25.25m13.274%209.537zl-4.557%204.45a.75.75%200%200%201-1.055-.008l-1.943-1.95a.75.75%200%200%201%201.062-1.058l1.419%201.425%204.026-3.932a.75.75%200%201%201%201.048%201.074M4.75%204h4.5a.75.75%200%200%201%200%201.5h-4.5a.75.75%200%200%201%200-1.5M4%207.75A.75.75%200%200%201%204.75%207h2a.75.75%200%200%201%200%201.5h-2A.75.75%200%200%201%204%207.75%22/%3E%3C/svg%3E');--md-admonition-icon--info:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M0%208a8%208%200%201%201%2016%200A8%208%200%200%201%200%208m8-6.5a6.5%206.5%200%201%200%200%2013%206.5%206.5%200%200%200%200-13M6.5%207.75A.75.75%200%200%201%207.25%207h1a.75.75%200%200%201%20.75.75v2.75h.25a.75.75%200%200%201%200%201.5h-2a.75.75%200%200%201%200-1.5h.25v-2h-.25a.75.75%200%200%201-.75-.75M8%206a1%201%200%201%201%200-2%201%201%200%200%201%200%202%22/%3E%3C/svg%3E');--md-admonition-icon--tip:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M3.499.75a.75.75%200%200%201%201.5%200v.996C5.9%202.903%206.793%203.65%207.662%204.376l.24.202c-.036-.694.055-1.422.426-2.163C9.1.873%2010.794-.045%2012.622.26%2014.408.558%2016%201.94%2016%204.25c0%201.278-.954%202.575-2.44%202.734l.146.508.065.22c.203.701.412%201.455.476%202.226.142%201.707-.4%203.03-1.487%203.898C11.714%2014.671%2010.27%2015%208.75%2015h-6a.75.75%200%200%201%200-1.5h1.376a4.5%204.5%200%200%201-.563-1.191%203.84%203.84%200%200%201-.05-2.063%204.65%204.65%200%200%201-2.025-.293.75.75%200%200%201%20.525-1.406c1.357.507%202.376-.006%202.698-.318l.009-.01a.747.747%200%200%201%201.06%200%20.75.75%200%200%201-.012%201.074c-.912.92-.992%201.835-.768%202.586.221.74.745%201.337%201.196%201.621H8.75c1.343%200%202.398-.296%203.074-.836.635-.507%201.036-1.31.928-2.602-.05-.603-.216-1.224-.422-1.93l-.064-.221c-.12-.407-.246-.84-.353-1.29a2.4%202.4%200%200%201-.507-.441%203.1%203.1%200%200%201-.633-1.248.75.75%200%200%201%201.455-.364c.046.185.144.436.31.627.146.168.353.305.712.305.738%200%201.25-.615%201.25-1.25%200-1.47-.95-2.315-2.123-2.51-1.172-.196-2.227.387-2.706%201.345-.46.92-.27%201.774.019%203.062l.042.19.01.05c.348.443.666.949.94%201.553a.75.75%200%201%201-1.365.62c-.553-1.217-1.32-1.94-2.3-2.768L6.7%205.527c-.814-.68-1.75-1.462-2.692-2.619a3.7%203.7%200%200%200-1.023.88c-.406.495-.663%201.036-.722%201.508.116.122.306.21.591.239.388.038.797-.06%201.032-.19a.75.75%200%200%201%20.728%201.31c-.515.287-1.23.439-1.906.373-.682-.067-1.473-.38-1.879-1.193L.75%205.677V5.5c0-.984.48-1.94%201.077-2.664.46-.559%201.05-1.055%201.673-1.353z%22/%3E%3C/svg%3E');--md-admonition-icon--success:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M13.78%204.22a.75.75%200%200%201%200%201.06l-7.25%207.25a.75.75%200%200%201-1.06%200L2.22%209.28a.75.75%200%200%201%20.018-1.042.75.75%200%200%201%201.042-.018L6%2010.94l6.72-6.72a.75.75%200%200%201%201.06%200%22/%3E%3C/svg%3E');--md-admonition-icon--question:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M0%208a8%208%200%201%201%2016%200A8%208%200%200%201%200%208m8-6.5a6.5%206.5%200%201%200%200%2013%206.5%206.5%200%200%200%200-13M6.92%206.085h.001a.749.749%200%201%201-1.342-.67c.169-.339.436-.701.849-.977C6.845%204.16%207.369%204%208%204a2.76%202.76%200%200%201%201.637.525c.503.377.863.965.863%201.725%200%20.448-.115.83-.329%201.15-.205.307-.47.513-.692.662-.109.072-.22.138-.313.195l-.006.004a6%206%200%200%200-.26.16%201%201%200%200%200-.276.245.75.75%200%200%201-1.248-.832c.184-.264.42-.489.692-.661q.154-.1.313-.195l.007-.004c.1-.061.182-.11.258-.161a1%201%200%200%200%20.277-.245C8.96%206.514%209%206.427%209%206.25a.61.61%200%200%200-.262-.525A1.27%201.27%200%200%200%208%205.5c-.369%200-.595.09-.74.187a1%201%200%200%200-.34.398M9%2011a1%201%200%201%201-2%200%201%201%200%200%201%202%200%22/%3E%3C/svg%3E');--md-admonition-icon--warning:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M6.457%201.047c.659-1.234%202.427-1.234%203.086%200l6.082%2011.378A1.75%201.75%200%200%201%2014.082%2015H1.918a1.75%201.75%200%200%201-1.543-2.575Zm1.763.707a.25.25%200%200%200-.44%200L1.698%2013.132a.25.25%200%200%200%20.22.368h12.164a.25.25%200%200%200%20.22-.368Zm.53%203.996v2.5a.75.75%200%200%201-1.5%200v-2.5a.75.75%200%200%201%201.5%200M9%2011a1%201%200%201%201-2%200%201%201%200%200%201%202%200%22/%3E%3C/svg%3E');--md-admonition-icon--failure:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M2.344%202.343za8%208%200%200%201%2011.314%2011.314A8.002%208.002%200%200%201%20.234%2010.089a8%208%200%200%201%202.11-7.746m1.06%2010.253a6.5%206.5%200%201%200%209.108-9.275%206.5%206.5%200%200%200-9.108%209.275M6.03%204.97%208%206.94l1.97-1.97a.749.749%200%200%201%201.275.326.75.75%200%200%201-.215.734L9.06%208l1.97%201.97a.749.749%200%200%201-.326%201.275.75.75%200%200%201-.734-.215L8%209.06l-1.97%201.97a.749.749%200%200%201-1.275-.326.75.75%200%200%201%20.215-.734L6.94%208%204.97%206.03a.75.75%200%200%201%20.018-1.042.75.75%200%200%201%201.042-.018%22/%3E%3C/svg%3E');--md-admonition-icon--danger:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M9.504.43a1.516%201.516%200%200%201%202.437%201.713L10.415%205.5h2.123c1.57%200%202.346%201.909%201.22%203.004l-7.34%207.142a1.25%201.25%200%200%201-.871.354h-.302a1.25%201.25%200%200%201-1.157-1.723L5.633%2010.5H3.462c-1.57%200-2.346-1.909-1.22-3.004zm1.047%201.074L3.286%208.571A.25.25%200%200%200%203.462%209H6.75a.75.75%200%200%201%20.694%201.034l-1.713%204.188%206.982-6.793A.25.25%200%200%200%2012.538%207H9.25a.75.75%200%200%201-.683-1.06l2.008-4.418.003-.006-.004-.009-.006-.006-.008-.001q-.005%200-.009.004%22/%3E%3C/svg%3E');--md-admonition-icon--bug:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M4.72.22a.75.75%200%200%201%201.06%200l1%20.999a3.5%203.5%200%200%201%202.441%200l.999-1a.748.748%200%200%201%201.265.332.75.75%200%200%201-.205.729l-.775.776c.616.63.995%201.493.995%202.444v.327q0%20.15-.025.292c.408.14.764.392%201.029.722l1.968-.787a.75.75%200%200%201%20.556%201.392L13%207.258V9h2.25a.75.75%200%200%201%200%201.5H13v.5q-.002.615-.141%201.186l2.17.868a.75.75%200%200%201-.557%201.392l-2.184-.873A5%205%200%200%201%208%2016a5%205%200%200%201-4.288-2.427l-2.183.873a.75.75%200%200%201-.558-1.392l2.17-.868A5%205%200%200%201%203%2011v-.5H.75a.75.75%200%200%201%200-1.5H3V7.258L.971%206.446a.75.75%200%200%201%20.558-1.392l1.967.787c.265-.33.62-.583%201.03-.722a1.7%201.7%200%200%201-.026-.292V4.5c0-.951.38-1.814.995-2.444L4.72%201.28a.75.75%200%200%201%200-1.06m.53%206.28a.75.75%200%200%200-.75.75V11a3.5%203.5%200%201%200%207%200V7.25a.75.75%200%200%200-.75-.75ZM6.173%205h3.654A.17.17%200%200%200%2010%204.827V4.5a2%202%200%201%200-4%200v.327c0%20.096.077.173.173.173%22/%3E%3C/svg%3E');--md-admonition-icon--example:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M5%205.782V2.5h-.25a.75.75%200%200%201%200-1.5h6.5a.75.75%200%200%201%200%201.5H11v3.282l3.666%205.76C15.619%2013.04%2014.543%2015%2012.767%2015H3.233c-1.776%200-2.852-1.96-1.899-3.458Zm-2.4%206.565a.75.75%200%200%200%20.633%201.153h9.534a.75.75%200%200%200%20.633-1.153L12.225%2010.5h-8.45ZM9.5%202.5h-3V6c0%20.143-.04.283-.117.403L4.73%209h6.54L9.617%206.403A.75.75%200%200%201%209.5%206Z%22/%3E%3C/svg%3E');--md-admonition-icon--quote:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20d%3D%22M1.75%202.5h10.5a.75.75%200%200%201%200%201.5H1.75a.75.75%200%200%201%200-1.5m4%205h8.5a.75.75%200%200%201%200%201.5h-8.5a.75.75%200%200%201%200-1.5m0%205h8.5a.75.75%200%200%201%200%201.5h-8.5a.75.75%200%200%201%200-1.5M2.5%207.75v6a.75.75%200%200%201-1.5%200v-6a.75.75%200%200%201%201.5%200%22/%3E%3C/svg%3E');}</style>



    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#gcp-rocky-server-setup-configuration" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../../.." title="Slicer Documentation" class="md-header__button md-logo" aria-label="Slicer Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            Slicer Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Rocky Server
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme)" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m14.3 16-.7-2h-3.2l-.7 2H7.8L11 7h2l3.2 9zM20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zm-9.15 3.96h2.3L12 9z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_2" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="Switch to system preference"  type="radio" name="__palette" id="__palette_2">
    
      <label class="md-header__button md-icon" title="Switch to system preference" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256l137.3-137.4c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
        <div class="md-search__suggest" data-md-component="search-suggest"></div>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../.." class="md-tabs__link">
        
  
    
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m19 2-5 4.5v11l5-4.5zM6.5 5C4.55 5 2.45 5.4 1 6.5v14.66c0 .*********.5.1 0 .15-.07.25-.07 1.35-.65 3.3-1.09 4.75-1.09 1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.31 4.75 *********.***********.25 0 .5-.25.5-.5V6.5c-.6-.45-1.25-.75-2-1V19c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V6.5C10.55 5.4 8.45 5 6.5 5"/></svg>
    
  
  Home

      </a>
    </li>
  

      
        
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../../knowledge/" class="md-tabs__link">
          
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../imaging/sd-image-building/" class="md-tabs__link">
          
  
  Imaging

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../how-tos/how-to-burn/" class="md-tabs__link">
          
  
  How-To

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../pi/applications/" class="md-tabs__link">
          
  
  Raspberry Pi

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../reference/datastore/" class="md-tabs__link">
          
  
  Reference

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../../releases/release-notes/" class="md-tabs__link">
          
  
  Release Notes

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../../.." title="Slicer Documentation" class="md-nav__button md-logo" aria-label="Slicer Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    Slicer Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../.." class="md-nav__link">
        
  
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m19 2-5 4.5v11l5-4.5zM6.5 5C4.55 5 2.45 5.4 1 6.5v14.66c0 .*********.5.1 0 .15-.07.25-.07 1.35-.65 3.3-1.09 4.75-1.09 1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.31 4.75 *********.***********.25 0 .5-.25.5-.5V6.5c-.6-.45-1.25-.75-2-1V19c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V6.5C10.55 5.4 8.45 5 6.5 5"/></svg>
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="">
            
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../knowledge/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Knowledge
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../marketing/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Marketing
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../generic-config/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Generic Config
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_4" checked>
        
          
          <label class="md-nav__link" for="__nav_2_4" id="__nav_2_4_label" tabindex="">
            
  
  <span class="md-ellipsis">
    GCP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2_4">
            <span class="md-nav__icon md-icon"></span>
            GCP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    Rocky Server
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    Rocky Server
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#ad-groups" class="md-nav__link">
    <span class="md-ellipsis">
      AD Groups
    </span>
  </a>
  
    <nav class="md-nav" aria-label="AD Groups">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#to-be-able-to-create-the-slicer-project-in-gcp" class="md-nav__link">
    <span class="md-ellipsis">
      To be able to create the slicer project in GCP:
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#to-be-able-to-operate-the-slicer-servers-in-gcp" class="md-nav__link">
    <span class="md-ellipsis">
      To be able to operate the slicer server(s) in GCP:
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#to-gain-access-to-the-reports-from-bigpandadynatrace" class="md-nav__link">
    <span class="md-ellipsis">
      To gain access to the reports from BigPanda/Dynatrace:
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snow-request-to-create-a-new-server" class="md-nav__link">
    <span class="md-ellipsis">
      SNOW Request to Create a New Server
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#servicenow-requests-for-additional-servers" class="md-nav__link">
    <span class="md-ellipsis">
      ServiceNow Requests for Additional Servers
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#granting-access-to-ad-groups" class="md-nav__link">
    <span class="md-ellipsis">
      Granting Access to AD Groups
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#vm-creation-setup" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation &amp; Setup
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting-ssh-access-issues" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Troubleshooting SSH Access Issues
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#mounting-persistent-disk-ssd" class="md-nav__link">
    <span class="md-ellipsis">
      Mounting Persistent Disk (SSD)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#installing-apache-configuring-tls-13" class="md-nav__link">
    <span class="md-ellipsis">
      Installing Apache &amp; Configuring TLS 1.3
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#apache-tuning-avoid-scoreboard-full-issue" class="md-nav__link">
    <span class="md-ellipsis">
      Apache Tuning (Avoid Scoreboard Full Issue)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#additional-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Steps
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Additional Steps">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#vm-creation-for-slicr04" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation for slicr04
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vm-creation-for-slicr05" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation for slicr05
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#setting-root-password-to-never-expire" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Root Password to Never Expire
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-configuration-for-apache" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Configuration for Apache
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#installing-htop-on-rocky-linux" class="md-nav__link">
    <span class="md-ellipsis">
      Installing htop on Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dns-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      DNS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-apache-lockup" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Apache Lockup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-steps-for-content-loading" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Steps for Content Loading
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-fixes-after-first-loader-install-attempt" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Fixes After First Loader Install Attempt
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dev-setup-for-rocky-linux" class="md-nav__link">
    <span class="md-ellipsis">
      Dev Setup for Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-configuration-for-dns" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Configuration for DNS
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#installing-htop-on-rocky-linux_1" class="md-nav__link">
    <span class="md-ellipsis">
      Installing htop on Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-apache-lockup_1" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Apache Lockup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2_4_2" >
        
          
          <label class="md-nav__link" for="__nav_2_4_2" id="__nav_2_4_2_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Servers
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_2_4_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_4_2">
            <span class="md-nav__icon md-icon"></span>
            Servers
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/gcp-servers/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Setup
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/configuration/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/packages/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Install Packages
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/apache/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Apache
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/certificates/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Certificates
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/additional-config/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Additional Config
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/monitoring-setup/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Monitoring Setup
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/bucket-storage/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Bucket Storage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/sudo-access/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Sudo Access
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/python-apache/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Python Apache
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../gcp/old-server-docs/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Old Server Documentation
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Imaging
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Imaging
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../imaging/sd-image-building/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SD Image Building
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../imaging/sd-image-building-qcam/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SD Image Building QCAM (Deprecated)
    
  </span>
  
    
  
  
    <span class="md-status md-status--deprecated"></span>
  

  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How-To
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            How-To
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../how-tos/how-to-burn/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Burn an Image
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../how-tos/how-to-check-md5/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Check MD5
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../how-tos/how-to-show-ppt/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Show PowerPoint
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Raspberry Pi
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Raspberry Pi
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../pi/applications/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Applications
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../pi/run-book/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Run Book
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Reference
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/datastore/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Datastore
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/data-migration/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Data Migration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/filesystem/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Filesystem
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/python3porting/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Python Porting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/security-compliance-review/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Security Compliance
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/ubuntu22_servers/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Ubuntu22 Servers
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/coding-bible/" class="md-nav__link">
        
  
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 21.5c-1.35-.85-3.8-1.5-5.5-1.5-1.65 0-3.35.3-4.75 1.05-.1.05-.15.05-.25.05-.25 0-.5-.25-.5-.5V6c.6-.45 1.25-.75 2-1 1.11-.35 2.33-.5 3.5-.5 1.95 0 4.05.4 5.5 1.5 1.45-1.1 3.55-1.5 5.5-1.5 1.17 0 2.39.15 ********.25 1.4.55 2 1v14.6c0 .25-.25.5-.5.5-.1 0-.15 0-.25-.05-1.4-.75-3.1-1.05-4.75-1.05-1.7 0-4.15.65-5.5 1.5M12 8v11.5c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5V7c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5m1 3.5c1.11-.68 2.6-1 4.5-1 .91 0 1.76.09 2.5.28V9.23c-.87-.15-1.71-.23-2.5-.23q-2.655 0-4.5.84zm4.5.17c-1.71 0-3.21.26-4.5.79v1.69c1.11-.65 2.6-.99 4.5-.99 1.04 0 1.88.08 2.5.24v-1.5c-.87-.16-1.71-.23-2.5-.23m2.5 2.9c-.87-.16-1.71-.24-2.5-.24-1.83 0-3.33.27-4.5.8v1.69c1.11-.66 2.6-.99 4.5-.99 1.04 0 1.88.08 2.5.24z"/></svg>
  
  <span class="md-ellipsis">
    Coding Bible
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6_8" >
        
          
          <label class="md-nav__link" for="__nav_6_8" id="__nav_6_8_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Branches
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_6_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6_8">
            <span class="md-nav__icon md-icon"></span>
            Branches
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/branches/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_6_8_2" >
        
          
          <label class="md-nav__link" for="__nav_6_8_2" id="__nav_6_8_2_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Branch Lists
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_6_8_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6_8_2">
            <span class="md-nav__icon md-icon"></span>
            Branch Lists
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/branches/2024-12-16/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2024-12-16
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../reference/branches/2024-12-11/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2024-12-11
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Release Notes
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Release Notes
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/release-notes/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Notes
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2025-03-12_SP.56/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.56 (2025-03-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2025-01-24_SP.55/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.55 (2025-01-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2025-01-24_SP.54/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.54 (2025-01-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-12-16_SP.53/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.53 (2024-12-16)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-11-12_SP.52/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.52 (2024-11-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-04-18_SP.51/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.51 (2024-04-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-04-01_SP.50/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.50 (2024-04-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-03-12_SP.49/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.49 (2024-03-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-03-11_SP.48/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.48 (2024-03-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2024-02-06_SP.47/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.47 (2024-02-06)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-10-25_SP.46/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.46 (2023-10-25)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-09-18_SP.45/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.45 (2023-09-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-08-21_SP.44/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.44 (2023-08-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-07-03_SP.43/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.43 (2023-07-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-05-31_SP.42/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.42 (2023-05-31)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-05-02_SP.41/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.41 (2023-05-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-04-03_SP.40/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.40 (2023-04-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-02-07_SP.39/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.39 (2023-02-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2023-01-27_SP.38/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.38 (2023-01-27)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-12-07_SP.37/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.37 (2022-12-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-12-05_SP.36/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.36 (2022-12-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-10-11_SP.35/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.35 (2022-10-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-08-24_SP.34/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.34 (2022-08-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-06-28_SP.33/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.33 (2022-06-28)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-06-01_SP.32/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.32 (2022-06-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-04-25_SP.31/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.31 (2022-04-25)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-04-22_SP.30/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.30 (2022-04-22)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-04-20_SP.29/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.29 (2022-04-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-03-11_SP.28/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.28 (2022-03-11)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-03-07_SP.27/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.27 (2022-03-07)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-03-02_SP.26/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.26 (2022-03-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-02-21_SP.25/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.25 (2022-02-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2022-01-14_SP.24/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.24 (2022-01-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-12-21_SP.23/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.23 (2021-12-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-11-29_SP.22/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.22 (2021-11-29)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-10-14_SP.21/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.21 (2021-10-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-31_SP.20/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.20 (2021-08-31)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-30_SP.19/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.19 (2021-08-30)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-26_SP.18/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.18 (2021-08-26)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-24_SP.17/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.17 (2021-08-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-20_SP.16/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.16 (2021-08-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-18_SP.15/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.15 (2021-08-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-17_SP.14/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.14 (2021-08-17)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-08-14_SP.13/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.13 (2021-08-14)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-22_SP.12/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.12 (2021-07-22)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-20_SP.11/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.11 (2021-07-20)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-12_SP.10/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.10 (2021-07-12)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-03_SP.9/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.9 (2021-07-03)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-02_SP.8/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.8 (2021-07-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-02_SP.7/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.7 (2021-07-02)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-06-24_SP.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.6 (2021-06-24)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-06-23_SP.5/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.5 (2021-06-23)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-06-21_SP.4/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.4 (2021-06-21)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-28_SP.3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.3 (2021-05-28)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-19_SP.2/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.2 (2021-05-19)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-17_SP.1/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SP.1 (2021-05-17)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-07-13_2.1.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.1.6 (2021-07-13)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-18_2.0.6/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.6 (2021-05-18)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-06_2.0.4/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.4 (2021-05-06)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-05_2.0.3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.3 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-05_2.0.2/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.2 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-05_2.0.1/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.1 (2021-05-05)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/2021-05-01_2.0.0/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2.0.0 (2021-05-01)
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../releases/versioning/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Versioning
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#ad-groups" class="md-nav__link">
    <span class="md-ellipsis">
      AD Groups
    </span>
  </a>
  
    <nav class="md-nav" aria-label="AD Groups">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#to-be-able-to-create-the-slicer-project-in-gcp" class="md-nav__link">
    <span class="md-ellipsis">
      To be able to create the slicer project in GCP:
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#to-be-able-to-operate-the-slicer-servers-in-gcp" class="md-nav__link">
    <span class="md-ellipsis">
      To be able to operate the slicer server(s) in GCP:
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#to-gain-access-to-the-reports-from-bigpandadynatrace" class="md-nav__link">
    <span class="md-ellipsis">
      To gain access to the reports from BigPanda/Dynatrace:
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snow-request-to-create-a-new-server" class="md-nav__link">
    <span class="md-ellipsis">
      SNOW Request to Create a New Server
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#servicenow-requests-for-additional-servers" class="md-nav__link">
    <span class="md-ellipsis">
      ServiceNow Requests for Additional Servers
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#granting-access-to-ad-groups" class="md-nav__link">
    <span class="md-ellipsis">
      Granting Access to AD Groups
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#vm-creation-setup" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation &amp; Setup
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting-ssh-access-issues" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Troubleshooting SSH Access Issues
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#mounting-persistent-disk-ssd" class="md-nav__link">
    <span class="md-ellipsis">
      Mounting Persistent Disk (SSD)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#installing-apache-configuring-tls-13" class="md-nav__link">
    <span class="md-ellipsis">
      Installing Apache &amp; Configuring TLS 1.3
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#apache-tuning-avoid-scoreboard-full-issue" class="md-nav__link">
    <span class="md-ellipsis">
      Apache Tuning (Avoid Scoreboard Full Issue)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#additional-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Steps
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Additional Steps">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#vm-creation-for-slicr04" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation for slicr04
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vm-creation-for-slicr05" class="md-nav__link">
    <span class="md-ellipsis">
      VM Creation for slicr05
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#setting-root-password-to-never-expire" class="md-nav__link">
    <span class="md-ellipsis">
      Setting Root Password to Never Expire
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-configuration-for-apache" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Configuration for Apache
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#installing-htop-on-rocky-linux" class="md-nav__link">
    <span class="md-ellipsis">
      Installing htop on Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dns-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      DNS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-apache-lockup" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Apache Lockup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-steps-for-content-loading" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Steps for Content Loading
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-fixes-after-first-loader-install-attempt" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Fixes After First Loader Install Attempt
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dev-setup-for-rocky-linux" class="md-nav__link">
    <span class="md-ellipsis">
      Dev Setup for Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-configuration-for-dns" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Configuration for DNS
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#installing-htop-on-rocky-linux_1" class="md-nav__link">
    <span class="md-ellipsis">
      Installing htop on Rocky Linux
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-apache-lockup_1" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Apache Lockup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="gcp-rocky-server-setup-configuration">GCP Rocky Server Setup &amp; Configuration<a class="headerlink" href="#gcp-rocky-server-setup-configuration" title="Permalink">&para;</a></h1>
<h2 id="ad-groups">AD Groups<a class="headerlink" href="#ad-groups" title="Permalink">&para;</a></h2>
<h3 id="to-be-able-to-create-the-slicer-project-in-gcp">To be able to create the slicer project in GCP:<a class="headerlink" href="#to-be-able-to-create-the-slicer-project-in-gcp" title="Permalink">&para;</a></h3>
<p><code>A-APM0028269-Slicer-admins</code></p>
<h3 id="to-be-able-to-operate-the-slicer-servers-in-gcp">To be able to operate the slicer server(s) in GCP:<a class="headerlink" href="#to-be-able-to-operate-the-slicer-servers-in-gcp" title="Permalink">&para;</a></h3>
<p><code>a-cloud-slicer-pr-editor</code>
<code>a-cloud-slicer-pr-owner</code></p>
<h3 id="to-gain-access-to-the-reports-from-bigpandadynatrace">To gain access to the reports from BigPanda/Dynatrace:<a class="headerlink" href="#to-gain-access-to-the-reports-from-bigpandadynatrace" title="Permalink">&para;</a></h3>
<p><code>A-OktaSSO-Dynatrace-Users</code></p>
<hr />
<h2 id="snow-request-to-create-a-new-server">SNOW Request to Create a New Server<a class="headerlink" href="#snow-request-to-create-a-new-server" title="Permalink">&para;</a></h2>
<p><strong>02 → 2023.01.05 07:55:01</strong>
<code>REQ3264966</code></p>
<p><strong>03 → 2023.02.10</strong>
<code>REQ3310940</code></p>
<p><code>https://cardinal.service-now.com/gith/?id=sc_cat_item&amp;sys_id=67bcbe291b450150ca2ba645624bcb62</code></p>
<ul>
<li><strong>Type:</strong> Create VM</li>
<li><strong>( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)</strong></li>
<li><strong>Image:</strong> <code>cah-rocky-8</code></li>
<li><strong>GCP Project:</strong> <code>mac-mgmt-pr-cah</code></li>
<li><strong>Environment:</strong> Production</li>
<li><strong>Internet facing:</strong> No</li>
<li><strong>Instance name:</strong> <code>slicr03</code></li>
<li><strong>(builds instance name of lpec5009slicr03)</strong></li>
<li><strong>Zone:</strong> <code>us-central1-a</code></li>
<li><strong>Service account:</strong> <code>pi-mgmt-pr-slicer-main</code></li>
<li><strong>Network tags:</strong> <code>int-webserver</code></li>
<li><strong>AD Groups:</strong> <code>A-MacSysAdmins</code> <strong>(Must add this before selecting cah-rocky-8)</strong></li>
</ul>
<p><strong>Machine Family:</strong> GENERAL-PURPOSE
<strong>Series:</strong> N1
<strong>Machine Type:</strong> <code>n1-standard-4</code>
<strong>Boot Disk Type:</strong> <code>pd-standard</code>
<strong>Boot Disk Size:</strong> <code>30</code>
<strong>Default Disk Type:</strong> <code>pd-ssd</code>
<strong>Name:</strong> <code>slicer03-ssd</code>
<strong>Size:</strong> <code>60</code>
<strong>Additional:</strong> No
<strong>APM ID:</strong> <code>APM0022695</code> (not changeable)
<em>(this is JamfPro APM, Slicer is APM0028269, Raspberry Pi is APM0027587, Jamf Cloud is 28731)</em>
<strong>Funded:</strong> No
<strong>Submit</strong></p>
<hr />
<h2 id="servicenow-requests-for-additional-servers">ServiceNow Requests for Additional Servers<a class="headerlink" href="#servicenow-requests-for-additional-servers" title="Permalink">&para;</a></h2>
<p><strong>04 → 2024.01.08</strong>
<strong>GCP Project Request:</strong>
<code>https://cardinal.service-now.com/now/nav/ui/classic/params/target/com.glideapp.servicecatalog_cat_item_view.do%3Fv%3D1%26sysparm_id%3Df432c90edb7b9300be676165ca9619dd</code></p>
<ul>
<li><strong>Looking to do:</strong> Create New</li>
<li><strong>Is this an EDDIE, ...:</strong> No</li>
<li><strong>Project Name:</strong> slicer</li>
<li><strong>Environment:</strong> Prod</li>
<li><strong>Project ID:</strong> <code>slicer-pr-cah</code> <em>(auto populate from choices)</em></li>
<li><strong>Primary Application:</strong> Slicer (<code>APM0028269</code>)</li>
<li><strong>Contact Manager:</strong> Aaron Perkins <em>(auto populated from application)</em></li>
<li><strong>Cost Center:</strong> <code>**********</code> <em>(auto populated from application)</em></li>
<li><strong>Click 'Add to Cart' → 'Submit Items'</strong></li>
</ul>
<p><code>REQ3708042</code></p>
<p>After creation, go to the GCP page:
<code>https://login.cardinalhealth.net/</code></p>
<p>Search and open <strong>"slicer-pr"</strong>. If you see an error:
You need additional access to the project: slicer-pr
To request access, contact your project administrator and provide them a copy of the following information:
- <strong>Missing permissions:</strong>
  - <code>compute.instances.list</code>
- <strong>Troubleshooting URL:</strong>
  <code>console.cloud.google.com/iam-admin/troubleshooter;permissions=compute.instances.list;principal=<EMAIL>;resources=%2F%2Fcloudresourcemanager.googleapis.com%2Fprojects%2Fslicer-pr-cah/result</code></p>
<p>To request access:
<code>https://cardinal.service-now.com/gith?sys_id=6e98df6a4763715073d32a24836d43e2&amp;view=sp&amp;id=ticket_request&amp;table=sc_req_item</code></p>
<p>Post a question: <strong>"How do we resolve this?"</strong></p>
<hr />
<h2 id="granting-access-to-ad-groups">Granting Access to AD Groups<a class="headerlink" href="#granting-access-to-ad-groups" title="Permalink">&para;</a></h2>
<p>Go to <strong>"Get IT Help"</strong>:
<code>https://cardinal.service-now.com/gith?id=sc_home</code></p>
<p>Click <strong>"Access Request for Active Directory Groups"</strong></p>
<ul>
<li><strong>Type of Request:</strong> Grant User Access</li>
<li><strong>Account Type:</strong> Normal</li>
<li><strong>Domain:</strong> <code>CardinalHealth.net</code></li>
<li><strong>Select the Group:</strong> <code>a-cloud-slicer-pr-owner</code></li>
<li><strong>Click "Add to Cart" → Checkout → Submit</strong></li>
</ul>
<p><code>REQ3712842</code> (Created 2024.01.11)</p>
<hr />
<h2 id="vm-creation-setup">VM Creation &amp; Setup<a class="headerlink" href="#vm-creation-setup" title="Permalink">&para;</a></h2>
<p>Check the wiki for instructions:</p>
<ul>
<li><strong>(old)</strong> <code>https://wiki.cardinalhealth.net/GCP_Create_VM_Instance</code></li>
<li><strong>(current)</strong> <code>https://wiki.cardinalhealth.net/GCP_VM_Instance_Requests</code></li>
</ul>
<p>If prompted to log in, follow:
<code>https://cardinal.service-now.com/gith?id=sc_cat_item&amp;table=sc_cat_item&amp;sys_id=5e0450f71be93550473c36ef034bcbec</code></p>
<hr />
<h2 id="troubleshooting-ssh-access-issues">🔍 Troubleshooting SSH Access Issues<a class="headerlink" href="#troubleshooting-ssh-access-issues" title="Permalink">&para;</a></h2>
<p>If <strong>Remote into it via SSH</strong> fails, toggle the <code>oslogin</code> metadata:
gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=TRUE
gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=FALSE</p>
<hr />
<h2 id="mounting-persistent-disk-ssd">Mounting Persistent Disk (SSD)<a class="headerlink" href="#mounting-persistent-disk-ssd" title="Permalink">&para;</a></h2>
<p>lsblk
sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb
sudo mkdir -p /mnt/disks/SSD
sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD
sudo chmod a+w /mnt/disks/SSD</p>
<p>Make mount persistent:
sudo cp /etc/fstab /etc/fstab.backup
sudo blkid /dev/sdb</p>
<hr />
<h2 id="installing-apache-configuring-tls-13">Installing Apache &amp; Configuring TLS 1.3<a class="headerlink" href="#installing-apache-configuring-tls-13" title="Permalink">&para;</a></h2>
<p>sudo dnf update -y
sudo dnf install -y mod_ssl httpd</p>
<p>Edit <strong><code>/etc/httpd/conf.d/ssl.conf</code></strong> and add:
SSLProtocol +TLSv1.3</p>
<p>Restart Apache:
sudo systemctl restart httpd</p>
<hr />
<h2 id="apache-tuning-avoid-scoreboard-full-issue">Apache Tuning (Avoid Scoreboard Full Issue)<a class="headerlink" href="#apache-tuning-avoid-scoreboard-full-issue" title="Permalink">&para;</a></h2>
<p>Modify <strong><code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code></strong>:</p>
<div class="language-text highlight"><pre><span></span><code><span id="__span-0-1"><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>&lt;IfModule mpm_event_module&gt;
</span><span id="__span-0-2"><a id="__codelineno-0-2" name="__codelineno-0-2" href="#__codelineno-0-2"></a>    StartServers             3
</span><span id="__span-0-3"><a id="__codelineno-0-3" name="__codelineno-0-3" href="#__codelineno-0-3"></a>    MinSpareThreads          75
</span><span id="__span-0-4"><a id="__codelineno-0-4" name="__codelineno-0-4" href="#__codelineno-0-4"></a>    MaxSpareThreads          250
</span><span id="__span-0-5"><a id="__codelineno-0-5" name="__codelineno-0-5" href="#__codelineno-0-5"></a>    ThreadLimit              64
</span><span id="__span-0-6"><a id="__codelineno-0-6" name="__codelineno-0-6" href="#__codelineno-0-6"></a>    ThreadsPerChild          25
</span><span id="__span-0-7"><a id="__codelineno-0-7" name="__codelineno-0-7" href="#__codelineno-0-7"></a>    MaxRequestWorkers        400
</span><span id="__span-0-8"><a id="__codelineno-0-8" name="__codelineno-0-8" href="#__codelineno-0-8"></a>    MaxConnectionsPerChild   0
</span><span id="__span-0-9"><a id="__codelineno-0-9" name="__codelineno-0-9" href="#__codelineno-0-9"></a>&lt;/IfModule&gt;
</span></code></pre></div>
<p>Restart Apache:
sudo apachectl configtest
sudo systemctl restart httpd</p>
<hr />
<h2 id="additional-steps">Additional Steps<a class="headerlink" href="#additional-steps" title="Permalink">&para;</a></h2>
<h3 id="vm-creation-for-slicr04">VM Creation for slicr04<a class="headerlink" href="#vm-creation-for-slicr04" title="Permalink">&para;</a></h3>
<ul>
<li><strong>Type:</strong> Create VM</li>
<li><strong>Image:</strong> <code>cah-rocky-9</code></li>
<li><strong>GCP Project:</strong> <code>slicer-pr-cah</code></li>
<li><strong>Environment:</strong> Production</li>
<li><strong>Internet facing:</strong> No</li>
<li><strong>Instance name:</strong> <code>slicr04</code></li>
<li><strong>Zone:</strong> <code>us-central1-a</code></li>
<li><strong>Service account:</strong> <code>slicer-pr-def</code></li>
<li><strong>Network tags:</strong> <code>int-webserver</code></li>
<li><strong>AD Groups:</strong> <code>A-APM0028269-Slicer-admins</code> <strong>(Must add this before selecting cah-rocky-9)</strong></li>
</ul>
<p><strong>Machine Family:</strong> GENERAL-PURPOSE
<strong>Series:</strong> N1
<strong>Machine Type:</strong> <code>n1-standard-4</code>
<strong>Boot Disk Type:</strong> <code>pd-standard</code>
<strong>Boot Disk Size:</strong> <code>30</code>
<strong>Default Disk Size:</strong> <code>200</code>
<strong>Default Disk Name:</strong> <code>slicer04-ssd</code>
<strong>Additional:</strong> No
<strong>APM ID:</strong> <code>APM0028269</code> (not changeable)
<strong>Funded:</strong> No
<strong>Submit Now</strong></p>
<p><strong>2024.01.10</strong>
<code>REQ3711830</code>
<code>RITM5739116</code> (Since there was not an owner at the time of the request, this request was cancelled. Make a new one)</p>
<p><strong>2024.01.16</strong>
<code>REQ3718032</code>
<code>RITM5747141</code></p>
<hr />
<h3 id="vm-creation-for-slicr05">VM Creation for slicr05<a class="headerlink" href="#vm-creation-for-slicr05" title="Permalink">&para;</a></h3>
<ul>
<li><strong>Type:</strong> Create VM</li>
<li><strong>Image:</strong> <code>cah-rocky-9</code></li>
<li><strong>GCP Project:</strong> <code>slicer-pr-cah</code></li>
<li><strong>Environment:</strong> Production</li>
<li><strong>Internet facing:</strong> No</li>
<li><strong>Instance name:</strong> <code>slicr05</code></li>
<li><strong>Zone:</strong> <code>us-central1-a</code></li>
<li><strong>Service account:</strong> <code>slicer-pr-def</code></li>
<li><strong>Network tags:</strong> <code>int-webserver</code></li>
<li><strong>AD Groups:</strong> <code>A-APM0028269-Slicer-admins</code> <strong>(Must add this before selecting cah-rocky-9)</strong></li>
</ul>
<p><strong>Machine Family:</strong> GENERAL-PURPOSE
<strong>Series:</strong> N1
<strong>Machine Type:</strong> <code>n1-standard-4</code>
<strong>Boot Disk Type:</strong> <code>pd-standard</code>
<strong>Boot Disk Size:</strong> <code>30</code>
<strong>Additional:</strong> 1 additional
<strong>Type:</strong> <code>pd-ssd</code>
<strong>Default Disk Size:</strong> <code>200</code>
<strong>Default Disk Name:</strong> <code>slicer05-ssd</code>
<strong>APM ID:</strong> <code>APM0028269</code> (not changeable)
<strong>Funded:</strong> No
<strong>Submit Now</strong></p>
<p><strong>2024.03.19</strong>
<code>REQ3799382</code>
<code>RITM5856652</code></p>
<hr />
<h3 id="setting-root-password-to-never-expire">Setting Root Password to Never Expire<a class="headerlink" href="#setting-root-password-to-never-expire" title="Permalink">&para;</a></h3>
<p>Check if the root account is set to expire, and if so, set it to not expire:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-1-1"><a id="__codelineno-1-1" name="__codelineno-1-1" href="#__codelineno-1-1"></a>sudo<span class="w"> </span>chage<span class="w"> </span>-l<span class="w"> </span>root
</span><span id="__span-1-2"><a id="__codelineno-1-2" name="__codelineno-1-2" href="#__codelineno-1-2"></a>sudo<span class="w"> </span>chage<span class="w"> </span>-l<span class="w"> </span>david.ferguson
</span><span id="__span-1-3"><a id="__codelineno-1-3" name="__codelineno-1-3" href="#__codelineno-1-3"></a>
</span><span id="__span-1-4"><a id="__codelineno-1-4" name="__codelineno-1-4" href="#__codelineno-1-4"></a>sudo<span class="w"> </span>chage<span class="w"> </span>-M<span class="w"> </span>-1<span class="w"> </span>root
</span><span id="__span-1-5"><a id="__codelineno-1-5" name="__codelineno-1-5" href="#__codelineno-1-5"></a>sudo<span class="w"> </span>chage<span class="w"> </span>-M<span class="w"> </span>-1<span class="w"> </span>david.ferguson
</span></code></pre></div>
<hr />
<h3 id="additional-configuration-for-apache">Additional Configuration for Apache<a class="headerlink" href="#additional-configuration-for-apache" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-2-1"><a id="__codelineno-2-1" name="__codelineno-2-1" href="#__codelineno-2-1"></a>sudo<span class="w"> </span>setfacl<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;u:apache:rwx&quot;</span><span class="w"> </span><span class="s2">&quot;/usr/local/lib/python3.6/site-packages&quot;</span>
</span><span id="__span-2-2"><a id="__codelineno-2-2" name="__codelineno-2-2" href="#__codelineno-2-2"></a>sudo<span class="w"> </span>chmod<span class="w"> </span><span class="m">755</span><span class="w"> </span>/usr/local/lib/python3.6/site-packages<span class="w"> </span>-R
</span><span id="__span-2-3"><a id="__codelineno-2-3" name="__codelineno-2-3" href="#__codelineno-2-3"></a>sudo<span class="w"> </span>restorecon<span class="w"> </span>-R<span class="w"> </span>/usr/local/lib/python3.6/site-packages/
</span><span id="__span-2-4"><a id="__codelineno-2-4" name="__codelineno-2-4" href="#__codelineno-2-4"></a>sudo<span class="w"> </span>setsebool<span class="w"> </span>-P<span class="w"> </span>httpd_tmp_exec<span class="w"> </span>on
</span><span id="__span-2-5"><a id="__codelineno-2-5" name="__codelineno-2-5" href="#__codelineno-2-5"></a>sudo<span class="w"> </span>chmod<span class="w"> </span>o+rx<span class="w"> </span>/usr/local/lib/python3.6/site-packages<span class="w"> </span>-R
</span><span id="__span-2-6"><a id="__codelineno-2-6" name="__codelineno-2-6" href="#__codelineno-2-6"></a>sudo<span class="w"> </span>setsebool<span class="w"> </span>-P<span class="w"> </span>httpd_read_user_content<span class="w"> </span><span class="m">1</span>
</span></code></pre></div>
<hr />
<h3 id="installing-htop-on-rocky-linux">Installing htop on Rocky Linux<a class="headerlink" href="#installing-htop-on-rocky-linux" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-3-1"><a id="__codelineno-3-1" name="__codelineno-3-1" href="#__codelineno-3-1"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>upgrade<span class="w"> </span>--refresh
</span><span id="__span-3-2"><a id="__codelineno-3-2" name="__codelineno-3-2" href="#__codelineno-3-2"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>config-manager<span class="w"> </span>--set-enabled<span class="w"> </span>crb
</span><span id="__span-3-3"><a id="__codelineno-3-3" name="__codelineno-3-3" href="#__codelineno-3-3"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>install<span class="w"> </span>https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm<span class="w"> </span>https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
</span><span id="__span-3-4"><a id="__codelineno-3-4" name="__codelineno-3-4" href="#__codelineno-3-4"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>install<span class="w"> </span>htop<span class="w"> </span>-y
</span></code></pre></div>
<hr />
<h3 id="dns-configuration">DNS Configuration<a class="headerlink" href="#dns-configuration" title="Permalink">&para;</a></h3>
<p><strong>2023.01.25</strong></p>
<ul>
<li><strong>What:</strong> Add</li>
<li><strong>Type:</strong> A Record</li>
<li><strong>ASAP:</strong> Yes</li>
<li><strong>Host:</strong> slicer2</li>
<li><strong>Domain:</strong> cardinalhealth.net</li>
<li><strong>FQDN:</strong> slicer2.cardinalhealth.net</li>
<li><strong>IP:</strong> ************</li>
<li><strong>Internal</strong></li>
</ul>
<p>Submit:
<code>REQ3288460</code></p>
<p><strong>2023.01.25</strong></p>
<ul>
<li><strong>Modify</strong></li>
<li><strong>A Record</strong></li>
<li><strong>ASAP:</strong> Yes</li>
<li><strong>Host:</strong> slicer2</li>
<li><strong>Domain:</strong> cardinalhealth.net</li>
<li><strong>FQDN:</strong> slicer2.cardinalhealth.net</li>
<li><strong>OLD:</strong> ************</li>
<li><strong>NEW:</strong> ***********</li>
<li><strong>Internal</strong></li>
</ul>
<hr />
<h3 id="troubleshooting-apache-lockup">Troubleshooting Apache Lockup<a class="headerlink" href="#troubleshooting-apache-lockup" title="Permalink">&para;</a></h3>
<p><strong>2024.04.07</strong></p>
<p>Midnight Saturday to Sunday rollover lockup of apache:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-4-1"><a id="__codelineno-4-1" name="__codelineno-4-1" href="#__codelineno-4-1"></a>sudo<span class="w"> </span>apachectl<span class="w"> </span>configtest
</span><span id="__span-4-2"><a id="__codelineno-4-2" name="__codelineno-4-2" href="#__codelineno-4-2"></a>systemctl<span class="w"> </span>restart<span class="w"> </span>httpd
</span></code></pre></div>
<p>Modify <strong><code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code></strong>:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-5-1"><a id="__codelineno-5-1" name="__codelineno-5-1" href="#__codelineno-5-1"></a>&lt;IfModule<span class="w"> </span>mpm_event_module&gt;
</span><span id="__span-5-2"><a id="__codelineno-5-2" name="__codelineno-5-2" href="#__codelineno-5-2"></a><span class="w">    </span>StartServers<span class="w">             </span><span class="m">3</span>
</span><span id="__span-5-3"><a id="__codelineno-5-3" name="__codelineno-5-3" href="#__codelineno-5-3"></a><span class="w">    </span>MinSpareThreads<span class="w">          </span><span class="m">75</span>
</span><span id="__span-5-4"><a id="__codelineno-5-4" name="__codelineno-5-4" href="#__codelineno-5-4"></a><span class="w">    </span>MaxSpareThreads<span class="w">          </span><span class="m">250</span>
</span><span id="__span-5-5"><a id="__codelineno-5-5" name="__codelineno-5-5" href="#__codelineno-5-5"></a><span class="w">    </span>ThreadLimit<span class="w">              </span><span class="m">64</span>
</span><span id="__span-5-6"><a id="__codelineno-5-6" name="__codelineno-5-6" href="#__codelineno-5-6"></a><span class="w">    </span>ThreadsPerChild<span class="w">          </span><span class="m">25</span>
</span><span id="__span-5-7"><a id="__codelineno-5-7" name="__codelineno-5-7" href="#__codelineno-5-7"></a><span class="w">    </span>MaxRequestWorkers<span class="w">        </span><span class="m">400</span>
</span><span id="__span-5-8"><a id="__codelineno-5-8" name="__codelineno-5-8" href="#__codelineno-5-8"></a><span class="w">    </span>MaxConnectionsPerChild<span class="w">   </span><span class="m">0</span>
</span><span id="__span-5-9"><a id="__codelineno-5-9" name="__codelineno-5-9" href="#__codelineno-5-9"></a>&lt;/IfModule&gt;
</span></code></pre></div>
<hr />
<h3 id="additional-steps-for-content-loading">Additional Steps for Content Loading<a class="headerlink" href="#additional-steps-for-content-loading" title="Permalink">&para;</a></h3>
<ul>
<li>Browse to the IP address of the server.</li>
<li>Enable Trust by clicking on the "Not Trusted" when the index (home) page first loads.</li>
<li>Log in as a user that has the admin privilege by being a member of "A-APM0028269-Slicer-admins".</li>
<li>Click into 'users' page, and give yourself 'loader create', 'dataport_create' permissions.</li>
<li>Go back home, see the dataport link, follow it, and then choose the saved datastore_snapshot.txt file.</li>
<li>Go back home, see all permissions restored, and shows all allowed modules.</li>
<li>Go to 'upload', and upload the file 'read_release_notes_slicer_pi.txt'.</li>
<li>Pull down htmlfiles content, and upload to the new server.</li>
<li>Pull down the multimedia content for all, and load to new server.</li>
<li>Codeupload all the pi content.</li>
<li>Download all download content from old server, then "upload" to new.</li>
</ul>
<hr />
<h3 id="manual-fixes-after-first-loader-install-attempt">Manual Fixes After First Loader Install Attempt<a class="headerlink" href="#manual-fixes-after-first-loader-install-attempt" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-6-1"><a id="__codelineno-6-1" name="__codelineno-6-1" href="#__codelineno-6-1"></a>sudo<span class="w"> </span>setfacl<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;u:apache:rwx&quot;</span><span class="w"> </span><span class="s2">&quot;/usr/local/lib/python3.6/site-packages&quot;</span>
</span><span id="__span-6-2"><a id="__codelineno-6-2" name="__codelineno-6-2" href="#__codelineno-6-2"></a>sudo<span class="w"> </span>chmod<span class="w"> </span><span class="m">755</span><span class="w"> </span>/usr/local/lib/python3.6/site-packages<span class="w"> </span>-R
</span><span id="__span-6-3"><a id="__codelineno-6-3" name="__codelineno-6-3" href="#__codelineno-6-3"></a>sudo<span class="w"> </span>restorecon<span class="w"> </span>-R<span class="w"> </span>/usr/local/lib/python3.6/site-packages/
</span><span id="__span-6-4"><a id="__codelineno-6-4" name="__codelineno-6-4" href="#__codelineno-6-4"></a>sudo<span class="w"> </span>setsebool<span class="w"> </span>-P<span class="w"> </span>httpd_tmp_exec<span class="w"> </span>on
</span><span id="__span-6-5"><a id="__codelineno-6-5" name="__codelineno-6-5" href="#__codelineno-6-5"></a>sudo<span class="w"> </span>chmod<span class="w"> </span>o+rx<span class="w"> </span>/usr/local/lib/python3.6/site-packages<span class="w"> </span>-R
</span><span id="__span-6-6"><a id="__codelineno-6-6" name="__codelineno-6-6" href="#__codelineno-6-6"></a>sudo<span class="w"> </span>setsebool<span class="w"> </span>-P<span class="w"> </span>httpd_read_user_content<span class="w"> </span><span class="m">1</span>
</span></code></pre></div>
<hr />
<h3 id="dev-setup-for-rocky-linux">Dev Setup for Rocky Linux<a class="headerlink" href="#dev-setup-for-rocky-linux" title="Permalink">&para;</a></h3>
<p><strong>2022.12.20</strong></p>
<ul>
<li>Download Rocky Linux 8.7, x86, minimal from <code>https://rockylinux.org/download/</code>.</li>
<li>Set up VirtualBox with the following settings:</li>
<li>Name: Rocky8</li>
<li>Image: Rocky 8 minimal</li>
<li>Skip unattended: checked</li>
<li>Base memory: 8000 MB</li>
<li>Processors: 4</li>
<li>Create: 20 GB</li>
<li>Network: Bridged Adapter (for CAH remote, must be wired to USB ethernet adapter, to Meraki, and select that for the bridge)</li>
<li>Start the VM and follow the installation prompts.</li>
<li>Set root password and create a user account.</li>
<li>Configure network and host settings.</li>
<li>Begin installation and reboot system after completion.</li>
<li>Take a snapshot of the VM.</li>
</ul>
<hr />
<h3 id="additional-configuration-for-dns">Additional Configuration for DNS<a class="headerlink" href="#additional-configuration-for-dns" title="Permalink">&para;</a></h3>
<p><strong>2023.01.25</strong></p>
<ul>
<li><strong>What:</strong> Add</li>
<li><strong>Type:</strong> A Record</li>
<li><strong>ASAP:</strong> Yes</li>
<li><strong>Host:</strong> slicer2</li>
<li><strong>Domain:</strong> cardinalhealth.net</li>
<li><strong>FQDN:</strong> slicer2.cardinalhealth.net</li>
<li><strong>IP:</strong> ************</li>
<li><strong>Internal</strong></li>
</ul>
<p>Submit:
<code>REQ3288460</code></p>
<p><strong>2023.01.25</strong></p>
<ul>
<li><strong>Modify</strong></li>
<li><strong>A Record</strong></li>
<li><strong>ASAP:</strong> Yes</li>
<li><strong>Host:</strong> slicer2</li>
<li><strong>Domain:</strong> cardinalhealth.net</li>
<li><strong>FQDN:</strong> slicer2.cardinalhealth.net</li>
<li><strong>OLD:</strong> ************</li>
<li><strong>NEW:</strong> ***********</li>
<li><strong>Internal</strong></li>
</ul>
<hr />
<h3 id="installing-htop-on-rocky-linux_1">Installing htop on Rocky Linux<a class="headerlink" href="#installing-htop-on-rocky-linux_1" title="Permalink">&para;</a></h3>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-7-1"><a id="__codelineno-7-1" name="__codelineno-7-1" href="#__codelineno-7-1"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>upgrade<span class="w"> </span>--refresh
</span><span id="__span-7-2"><a id="__codelineno-7-2" name="__codelineno-7-2" href="#__codelineno-7-2"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>config-manager<span class="w"> </span>--set-enabled<span class="w"> </span>crb
</span><span id="__span-7-3"><a id="__codelineno-7-3" name="__codelineno-7-3" href="#__codelineno-7-3"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>install<span class="w"> </span>https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm<span class="w"> </span>https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
</span><span id="__span-7-4"><a id="__codelineno-7-4" name="__codelineno-7-4" href="#__codelineno-7-4"></a>sudo<span class="w"> </span>dnf<span class="w"> </span>install<span class="w"> </span>htop<span class="w"> </span>-y
</span></code></pre></div>
<hr />
<h3 id="troubleshooting-apache-lockup_1">Troubleshooting Apache Lockup<a class="headerlink" href="#troubleshooting-apache-lockup_1" title="Permalink">&para;</a></h3>
<p><strong>2024.04.07</strong></p>
<p>Midnight Saturday to Sunday rollover lockup of apache:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-8-1"><a id="__codelineno-8-1" name="__codelineno-8-1" href="#__codelineno-8-1"></a>sudo<span class="w"> </span>apachectl<span class="w"> </span>configtest
</span><span id="__span-8-2"><a id="__codelineno-8-2" name="__codelineno-8-2" href="#__codelineno-8-2"></a>systemctl<span class="w"> </span>restart<span class="w"> </span>httpd
</span></code></pre></div>
<p>Modify <strong><code>/etc/httpd/conf.modules.d/10-mpm-event.conf</code></strong>:</p>
<div class="language-sh highlight"><pre><span></span><code><span id="__span-9-1"><a id="__codelineno-9-1" name="__codelineno-9-1" href="#__codelineno-9-1"></a>&lt;IfModule<span class="w"> </span>mpm_event_module&gt;
</span><span id="__span-9-2"><a id="__codelineno-9-2" name="__codelineno-9-2" href="#__codelineno-9-2"></a><span class="w">    </span>StartServers<span class="w">             </span><span class="m">3</span>
</span><span id="__span-9-3"><a id="__codelineno-9-3" name="__codelineno-9-3" href="#__codelineno-9-3"></a><span class="w">    </span>MinSpareThreads<span class="w">          </span><span class="m">75</span>
</span><span id="__span-9-4"><a id="__codelineno-9-4" name="__codelineno-9-4" href="#__codelineno-9-4"></a><span class="w">    </span>MaxSpareThreads<span class="w">          </span><span class="m">250</span>
</span><span id="__span-9-5"><a id="__codelineno-9-5" name="__codelineno-9-5" href="#__codelineno-9-5"></a><span class="w">    </span>ThreadLimit<span class="w">              </span><span class="m">64</span>
</span><span id="__span-9-6"><a id="__codelineno-9-6" name="__codelineno-9-6" href="#__codelineno-9-6"></a><span class="w">    </span>ThreadsPerChild<span class="w">          </span><span class="m">25</span>
</span><span id="__span-9-7"><a id="__codelineno-9-7" name="__codelineno-9-7" href="#__codelineno-9-7"></a><span class="w">    </span>MaxRequestWorkers<span class="w">        </span><span class="m">400</span>
</span><span id="__span-9-8"><a id="__codelineno-9-8" name="__codelineno-9-8" href="#__codelineno-9-8"></a><span class="w">    </span>MaxConnectionsPerChild<span class="w">   </span><span class="m">0</span>
</span><span id="__span-9-9"><a id="__codelineno-9-9" name="__codelineno-9-9" href="#__codelineno-9-9"></a>&lt;/IfModule&gt;
</span></code></pre></div>
<hr />
<p><strong>End of document.</strong></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
    
      
      <nav class="md-footer__inner md-grid" aria-label="Footer" >
        
          
          <a href="../../generic-config/" class="md-footer__link md-footer__link--prev" aria-label="Previous: Generic Config">
            <div class="md-footer__button md-icon">
              
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256l137.3-137.4c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>
            </div>
            <div class="md-footer__title">
              <span class="md-footer__direction">
                Previous
              </span>
              <div class="md-ellipsis">
                Generic Config
              </div>
            </div>
          </a>
        
        
          
          <a href="../gcp/gcp-servers/" class="md-footer__link md-footer__link--next" aria-label="Next: Setup">
            <div class="md-footer__title">
              <span class="md-footer__direction">
                Next
              </span>
              <div class="md-ellipsis">
                Setup
              </div>
            </div>
            <div class="md-footer__button md-icon">
              
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M278.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-160 160c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L210.7 256 73.4 118.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l160 160z"/></svg>
            </div>
          </a>
        
      </nav>
    
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
      <div class="md-progress" data-md-component="progress" role="progressbar"></div>
    
    
    <script id="__config" type="application/json">{"base": "../../..", "features": ["content.code.copy", "content.code.select", "content.code.annotate", "navigation.footer", "search.suggest", "search.highlight", "navigation.breadcrumbs", "navigation.instant", "navigation.instant.prefetch", "navigation.instant.progress", "navigation.tracking", "navigation.tabs", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top"], "search": "../../../assets/javascripts/workers/search.f8cc74c7.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../../../assets/javascripts/bundle.c8b220af.min.js"></script>
      
    
  </body>
</html>