# A download for slicer page services

service = 'download'
version = service + '.0.2'

_ = """
This file gets loaded to:
/var/www/html/download.py

using:
sudo vi /var/www/html/download.py

It also requires:

sudo vi /etc/httpd/conf.d/python-download.conf
----- start copy -----
WSGIScriptAlias /download /var/www/html/download.py
----- end copy -----

sudo chown apache:apache /var/www/html/download.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import download; print(download.make_body({'QUERY_STRING':'','REQUEST_METHOD':'GET'}))"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/download

https://slicer.cardinalhealth.net/download?service=pi_hmi,version=H.1.1
https://slicer.cardinalhealth.net/download?service=pi_logging,version=L.1.9



"""

import copy
import traceback
import json
import os
import sys
import time
import unittest

import shlex
import subprocess

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import login
    import codeupload
    import datastore
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_upload_path = service_config['base_upload_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# md5:
# https://knowledge.autodesk.com/search-result/caas/sfdcarticles/sfdcarticles/Checking-the-MD5-checksum-of-a-Downloaded-File.html

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def calculate_rot13(s):
    # ====================================
    chars = "abcdefghijklmnopqrstuvwxyz"
    trans = chars[13:] + chars[:13]
    rot_char = lambda c: trans[chars.find(c)] if chars.find(c) > -1 else c
    return ''.join(rot_char(c) for c in s)


# ====================================
def application(environ, start_response):
    # ====================================
    # only implementing 'GET'
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    try:

        query_items = {}
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]

        # if file download request:
        # https://stackoverflow.com/questions/3622675/returning-a-file-to-a-wsgi-get-request/3622786
        # query_items = {'filetodownload': 'testfile1.txt'}

        if ('service' in query_items) and ('version' in query_items):
            file_content = codeupload.get_pi_service_version(query_items['service'], query_items['version'])
            body = calculate_rot13(file_content)

            status = '200 OK'
            response_header = [('Content-type', 'text/html')]

            # allow non wrapped response
            start_response(status, response_header)

            # do not leg when devices are pulling service content
            # permissions.log_page_allowed(environ, service, other)

            return [body.encode()]

        if 'filetoview' in query_items:
            response_header = [('Content-type', 'text/html')]
            status = '200 OK'
            do_encode = True
            try:
                file_to_get = query_items['filetoview']
                zOutFilename = base_upload_path + file_to_get
                body = '(empty)'

                if file_to_get[-4:] == '.txt':
                    body = open(zOutFilename, 'rb').read().decode().replace('\n', '<br>')
                if file_to_get[-4:] == '.png':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/png')]
                    do_encode = False
                if file_to_get[-4:] == '.jpg':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/jpg')]
                    do_encode = False
                if file_to_get[-4:] == '.gif':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'image/gif')]
                    do_encode = False
                if file_to_get[-4:] == '.pdf':
                    body = open(zOutFilename, 'rb').read()
                    response_header = [('Content-type', 'application/pdf')]
                    do_encode = False
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            # allow non wrapped response
            permissions.log_page_allowed(environ, service, other)
            start_response(status, response_header)
            if do_encode:
                return [body.encode()]
            else:
                return [body]

        elif 'filetodownload' in query_items:
            try:
                file_to_get = query_items['filetodownload']
                zOutFilename = base_upload_path + file_to_get
                block_size = 1024
                size = os.path.getsize(zOutFilename)

                if ".zip" in zOutFilename:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'application/zip'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'rb')
                elif ".mov" in zOutFilename:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'video/quicktime'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'rb')
                else:
                    # allow non wrapped response
                    start_response("200 OK", [('Content-Type', 'text/html'), ('Content-length', str(size)),
                                              ('Content-Disposition', 'attachment; filename=' + file_to_get)])
                    filelike = open(zOutFilename, 'r')

                permissions.log_page_allowed(environ, service, other)
                if 'wsgi.file_wrapper' in environ:
                    return environ['wsgi.file_wrapper'](filelike, block_size)
                else:
                    return iter(lambda: filelike.read(block_size), '')
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            status = '200 OK'
            response_header = [('Content-type', 'text/html')]
            # allow non wrapped response
            permissions.log_page_allowed(environ, service, other)
            start_response(status, response_header)
            return [body.encode()]

        else:

            try:
                body = ''

                body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
                """

                name_to_show = "Home"
                url_to_use = make_home_url_from_environ(environ)
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

                if permissions.permission_allowed(environ, 'download_view'):
                    try:
                        body += "<br><br>"

                        body += '<center>'
                        body += '<table border="1" cellpadding="5">'
                        body += '<tr>'
                        body += '<td>'
                        body += '<B>Notes</B>'
                        body += '</td>'
                        body += '<td>'
                        body += '</td>'
                        body += '</tr>'

                        body += '<tr>'
                        body += '<td>'
                        body += "To prevent auto unzip in Safari"
                        body += '</td>'
                        body += '<td>'
                        body += "Safari -> Preferences... -> General -> uncheck 'Open safe files after downloading'"
                        body += '</td>'
                        body += '</tr>'

                        body += '<tr>'
                        body += '<td>'
                        body += "Raspberry pi images"
                        body += '</td>'
                        body += '<td>'
                        body += "These are the 'cah_pi_raw_image_X.Y.Z.zip' files"
                        body += '</td>'
                        body += '</tr>'

                        body += '<tr>'
                        body += '<td>'
                        body += "Help documents"
                        body += '</td>'
                        body += '<td>'
                        body += "These are the 'how_to_' files"
                        body += '</td>'
                        body += '</tr>'

                        body += '</table>'

                        body += '</center>'
                        body += "<br><br>"

                        body += '<center>'
                        body += '<table border="1" cellpadding="5">'
                        body += '<tr>'
                        body += '<td>'
                        body += 'file'
                        body += '</td>'
                        body += '<td>'
                        body += 'size'
                        body += '</td>'
                        body += '<td>'
                        body += 'View in new tab'
                        body += '</td>'
                        body += '<td>'
                        body += 'Available for download'
                        body += '</td>'
                        body += '<td>'
                        body += 'md5'
                        body += '</td>'
                        body += '</tr>'

                        files = sorted(os.listdir(base_upload_path))
                        for file_name in files:
                            is_allowed = False
                            result = datastore.get_value('download_permission_' + file_name)
                            if result == 'Yes':
                                is_allowed = True

                            if is_allowed:
                                full_file_path = base_upload_path + file_name
                                md5_file = full_file_path + '.md5'
                                if not os.path.isfile(md5_file):
                                    command = 'md5sum ' + full_file_path
                                    mem_string, fails = do_one_command(command)
                                    with open(md5_file, 'w') as f:
                                        f.write(mem_string.split()[0])

                                md5_value = ''
                                if os.path.isfile(md5_file):
                                    with open(md5_file, 'r') as f:
                                        md5_value = f.read()

                                body += '<tr>'
                                body += '<td>'
                                body += file_name
                                body += '</td>'

                                body += '<td>'
                                body += make_file_size_human_readable(full_file_path)
                                body += '</td>'

                                body += '<td>'
                                if (full_file_path[-4:] == '.txt') or (full_file_path[-4:] == '.png') or (full_file_path[-4:] == '.gif') or (full_file_path[-4:] == '.pdf'):
                                    # the target="_blank" makes the click open a new tab for the result
                                    body += '<a href="' + url_to_use + '/download' + '?filetoview=' + file_name + '" target="_blank"> ' + 'view' + '</a>'
                                body += '</td>'

                                body += '<td>'
                                body += '<a href="' + url_to_use + '/download' + '?filetodownload=' + file_name + '"> ' + 'click here to download' + '</a>'
                                body += '</td>'

                                body += '<td>'
                                body += md5_value
                                body += '</td>'

                                body += '</tr>'

                        body += '</table>'
                        body += '</center>'

                        # body += '<br><br><br>'
                        # body += str(query_items)

                    except Exception as e:
                        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

                else:
                    body = ""
                    body += "<br><br><br><br><br>"
                    body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
            except:
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            status = '200 OK'
            response_header = [('Content-type', 'text/html')]

            html = body

            permissions.log_page_allowed(environ, service, other)
            try:
                html = organization.wrap_page_with_session(environ, html)
                start_response(status, response_header)
            except:
                # still on slicer01
                # allow non wrapped response
                start_response(status, response_header)

            return [html.encode()]
    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    permissions.log_page_allowed(environ, service, other)

    return [body.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

# End of file
