Offline tests to be performed manually:

1) Test that the device remote commands functions as expected

- in reports, filter down to a single device
    https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf,siteid=VPN1
- open the collection (of 1 IDs)
- be sure the test user has permission "device_command"
- note the "most recent command number",
    and then after submit, notice the actual assigned command number,
    like "100000000e3c804f,command_entered: ls -l -> 24 timeout: 15"
    has "-> 24" as the actually generated command number.
- enter  command of "ls -l /cardinal" and submit
- after a minute, check in the upload page, to see that there is a command
    response from that device 'zzz_done_command_(id)_(cmd_num)', with a command number after the number
    noted earlier, and then view that response, to see the results of the ls
    command are what the expected contents of that directory are
        https://slicer.cardinalhealth.net/upload

- if the test fails, then go to the file slicer_wsgi_devicecommand.py, and
    follow the trouble shooting steps outlined in the string "_troubleshooting"


2) Check Apache error logs for any activity since the last manual test cycle:
    - error_log
        sudo su
        cat /var/log/httpd/error_log*

        (collect all content, then remove all with "sudo rm /var/log/httpd/error_log*")

    - messages
        sudo su
        cat /var/log/messages
            Look for what's in there, and see if action needs to be taken.

3) added 2023.06.08
    From the summary at the top of the reports page, click into a single site dashboard.
    like:
        https://slicer.cardinalhealth.net/dashboard?siteid=PR005

    - confirm that the page loads (had an issue with error "TypeError: headers is not iterable")
        - This was fixed by having each site cache (stash_file_for_site) created in main
    - confirm that the data shown for 'Count Current', 'min', and 'max' match what the
        summary in reports shows.(PR005 about 108 min, and 114 max)

4) added 2023.06.22
    test the render of csv is non-empty, when sorted by iot (was broken until now)

    https://slicer.cardinalhealth.net/reports?csv_view=yes,device_view=(MAC2),siteid=MEX03,sort=(iot),sortd=rev,status=current

5) added 2023.07.06
    test that an IP address can be used as the ID, to initiate push of software services
    (User must be logged in, and have permissions to "(all)" sites setting)
    (check that the page loads without errors, and that the section "Device Push List: **************" shows up)

    https://slicer.cardinalhealth.net/reports?serial=**************

6) added 2023.07.11
    Take a snapshot of the datastore content, paste into the text file 'datastore_snapshot.txt',
        and check it into GitHub
    (must be logged in, and have permissions,
        then load the following page, select all, and copy, paste result overtop of current
        content in datastore_snapshot.txt)

    https://slicer.cardinalhealth.net/datastore

    Go into github, review diffs, and commit the change "manual test snapshot", and push to origin

7) added 2023.07.19
    Do certificate reviews as this "test", to be aware of upcoming expirations,

    https://slicer.cardinalhealth.net/certificates

8) Added 2023.07.31
    When the test cases run, the count of methods with non-conforming names needs to go down.
        120 on 2023.07.31
        106 on 2024.12.02

9) Added 2023.08.08
    Test for dashboard files being written, or dash board up to todays date, or both.
        (They only get written if the new data is different, so only a few may be today's date and time)

            gcloud install: https://cloud.google.com/sdk/docs/install
            cd ~/Downloads/google-cloud-sdk
            ./install.sh
            gcloud auth login
            ./slicer04
                gcloud compute ssh david.ferguson@lpec5009slicr04 --verbosity=debug --zone us-central1-a --project slicer-pr-cah --internal-ip

        On slicer server:
            ls -l  /dev/shm/dashboard_main_report.json*
        if not updating, look to log files with:
            cat /dev/shm/startup_exceptions_dashboard
            cat /dev/shm/dashboard_main.txt

10) Added 2023.08.18
    Open the reports page, and click through each tab of items to show, and
        see that the page builds (had errors on one tab, so use this to
        catch any issues, before users do)

    https://slicer.cardinalhealth.net/reports

11) Added 2023.08.28
    Check for any devices that need to be added to clear pass.
    open:
        https://slicer.cardinalhealth.net/reports?device_view=(MAC1),sort=(iot0),sortd=fwd
    and see if the ones at the top, that are marked "In ClearPass" as "no", need to be added.

12) Added 2023.09.11
    go to loader page, and check for any errors in the "exception" or "run status" column.
        http://slicer.cardinalhealth.net/loader

13) Added 2024.05.13
    Have all members go log into the Cardinal portal for github, so that their 90 day
        expiration does not trigger a removal of github app access.
            open DSO ticket for GitHub access at "https://jira.cardinalhealth.com/projects/DSO" for getting access back
                Type: Access request, DevOps Tool: GitHub
                Dave: 2024.05.13: DSO-10583

    https://github.com/CardinalHealth/cs_sp_pi_slicer
        2024.05.21 cah-david-ferguson logged in with Ghbud+1!, added "cah-jackvincent-balcita" as admin

    after May 2025:

    https://github.com/cardinal-health/
        Log out any previous user profile, then:
        david-ferguson_cardinal
        then click "Sign in with your Identity Provider"

    reminder: Go to https://github.com/CardinalHealth/cs_sp_pi_slicer and log in, to keep your account active



14) Added 2024.05.23
    Check for any new Pi OS release, and if there is a new one, then go get it,
        and re-test for known current issues (like no audio on mp4 playback on Pi4.
    https://downloads.raspberrypi.com/raspios_lite_armhf/release_notes.txt

    2024-07-04 is the current latest, and still has the issue.
    2024-10-22 new release:
        change that will require us to make a change to our code:
            chromium-browser renamed to chromium
                The hmi module will need to try to detect which is present, to know which to launch.

    If there is a new release, then make a Jira card, to get the new release, build a new
    image for the pi, and test out if mp4 playback has sound.

15) Added 2024.06.06
    Check Service Now for any Big Panda tickets that might be there.
        https://cardinal.service-now.com/now/nav/ui/classic/params/target/task_list.do%3Fsysparm_fixed_query%3D%26sysparm_query%3Dassignment_group%253D9034e51edb247b841a603ede7c961998%255Eu_statusNOT%2BINResolved%252CClosed%252CClosed%2BIncomplete%252CClosed%2B-%2BComplete%252CClosed%2B-%2BCancelled%252CClosed%2B-%2BDuplicate%252CSuccessful%252CCancelled%252CPartially%2BImplemented%252CRolled%2BBack%252CImplemented%2Bwith%2BIssues%255EstateNOT%2BIN3%252C7%252C4%252C6%252C5%255Eactive%253Dtrue%26sysparm_view%3Dsdlc

16) Added 2024.06.11
    Change the timezone for a test site, like VPN1, and see that the local time is
        updated to the new timezone, by looking at the clock report on the main page.
        (Give it a few minutes, to get the setting, and then for the screen update to happen)
        (clock view enable must be set for that device).
        https://slicer.cardinalhealth.net/sites

        on a pi device:
            date
            cat /cardinal/localhtml/timezone








