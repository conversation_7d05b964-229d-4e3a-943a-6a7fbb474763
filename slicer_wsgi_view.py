# A view for slicer page services

service = "view"
version = service + '.0.1'

release_notes = """
2023.04.11
view.0.1

Start as a place to make html views of data passed in.

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/view.py

using:
sudo vi /var/www/html/view.py

It also requires:

sudo vi /etc/httpd/conf.d/python-view.conf
----- start copy -----
WSGIScriptAlias /view /var/www/html/view.py
----- end copy -----

sudo chown apache:apache /var/www/html/view.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/view-runner
sudo chmod +x /var/www/html/view-runner

# ===== begin: start file
#!/usr/bin/env python
import view
view.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/view-runner.service
sudo systemctl daemon-reload
sudo systemctl stop view-runner.service
sudo systemctl start view-runner.service
sudo systemctl enable view-runner.service

systemctl status view-runner.service

sudo systemctl restart view-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep view-runner

OR

tail -f /var/log/syslog | fgrep view-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/view-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import view; print(view.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/view

https://slicer.cardinalhealth.net/view?siteid=PR005

https://slicer.cardinalhealth.net/view?serial=100000002a5da842

https://slicer.cardinalhealth.net/view?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_view


"""

import copy
import traceback
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ----------------------------
def simple(the_data, the_items=None, the_config={}, first_column='', sort_column=''):
    # ----------------------------
    the_format = ''
    highlight_sorted = False

    if 'the_format' in the_config:
        the_format = the_config['the_format']
    if 'highlight_sorted' in the_config:
        highlight_sorted = the_config['highlight_sorted']

    return_value = ''
    return_value += '<center>'
    if the_format:
        return_value += '<table ' + the_format + '>'
    else:
        return_value += '<table>'

    if not the_items:
        # auto generate items from the data
        # default to sorted order
        item_names = {}
        for data in the_data:
            for key in data.keys():
                item_names[key] = True

        the_items = []
        if first_column:
            the_items.append(first_column)

        for key in sorted(item_names.keys()):
            use_it = True
            if first_column:
                if first_column == key:
                    use_it = False
            if use_it:
                the_items.append(key)

    # default is to sort by the first column
    sort_by = the_items[0]
    if sort_column:
        sort_by = sort_column

    sort_items = {}
    sequence_number = 0
    for entry in the_data:
        sequence_number += 1  # prevent duplicates in the sorted value from stomping on each other
        if sort_by in entry:
            the_sort_key = str(entry[sort_by])
        else:
            the_sort_key = ''
        sort_items[the_sort_key + '_' + str(sequence_number)] = entry

    # ===================
    # header row
    # ===================
    return_value += '<tr>'
    for the_item in the_items:
        if highlight_sorted and (the_item == sort_by):
            return_value += '<td style="background-color:rgba(0,255,0,0.3)">'
        else:
            return_value += '<td>'
        return_value += the_item
        return_value += '</td>'
    return_value += '</tr>'

    # ===================
    # data row(s)
    # ===================
    for the_key in sorted(sort_items.keys()):
        entry = sort_items[the_key]

        return_value += '<tr>'
        for the_item in the_items:
            return_value += '<td>'
            if the_item in entry:
                return_value += str(entry[the_item])
            return_value += '</td>'
        return_value += '</tr>'

    return_value += '</table></center>'

    return return_value.replace('\n', '')


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except ValueError:
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    get_content = ''
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':
            the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
            the_type = ''
            if 'type' in query_items:
                the_type = query_items['type']

            if the_type == 'issues':
                live_data = get_live_data()

                the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

                the_data['headers'] = live_data['headers']

                for item in live_data['data']:
                    row_content = []
                    row_links = []
                    row_colors = []
                    for header_name in the_data['headers']:
                        try:
                            item_content = item[header_name]
                        except:
                            item_content = ''

                        try:
                            item_link = item[header_name + '_link']
                        except:
                            item_link = ''

                        try:
                            item_color = item[header_name + '_color']
                        except:
                            item_color = ''

                        row_content.append(item_content)
                        row_links.append(item_link)
                        row_colors.append(item_color)

                    the_data['rows'].append(row_content)
                    the_data['links'].append(row_links)
                    the_data['color'].append(row_colors)

            else:
                # echo it back out, so that we can see it
                for key in query_items.keys():
                    the_data['headers'].append(key)
                    the_data['rows'].append([query_items[key]])

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

            load_url = url_to_use + '/' + service + '?get=data,type=issues'

            live_table_content_d = make_live_table_content(load_url)
            other['head'] = live_table_content_d['head']

            body = ''

            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            #    name_to_show = "Home"
            #    url_to_use = "https://slicer.cardinalhealth.net"
            #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '<center>'
            body += version
            body += '</center>'

            # ------------------------------------
            # Live dashboard view
            # ------------------------------------
            dashboard = ''
            dashboard += live_table_content_d['body']
            dashboard += live_table_content_d['javascript']

            body += dashboard

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    if service == 'temp' + 'late':  # Split the string, so that global replace does not find it when saving this as a new service
        body += '<br><br>'
        body += '<center>'
        body += 'environ'
        body += '<table border="1" cellpadding="5">'
        for item in sorted(environ.keys()):
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            body += '<td>'
            try:
                body += str(environ[item])
            except:
                body += '(na)'
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += 'logged in user'
        body += '<br><br>'
        body += str(login.get_current_user(environ))
        body += '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'test1'
        body += '</td>'
        body += '<td>'
        body += 'test2'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ,
                                             service + '_'):  # or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)

        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_one(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 1}]
        the_items = ['id', 'data_item_1']
        expected = """
<center>
<table>
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn001</td><td>1</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, the_items)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_two(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn002', 'data_item_1': 3}, {'id': 'sn001', 'data_item_1': 1}]
        the_items = ['id', 'data_item_1']
        expected = """
<center>
<table>
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn001</td><td>1</td></tr>
<tr><td>sn002</td><td>3</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, the_items)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_three_no_stomp(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn002', 'data_item_1': 3}, {'id': 'sn002', 'data_item_1': 4},
                    {'id': 'sn001', 'data_item_1': 1}]
        the_items = ['id', 'data_item_1']
        expected = """
<center>
<table>
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn001</td><td>1</td></tr>
<tr><td>sn002</td><td>3</td></tr>
<tr><td>sn002</td><td>4</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, the_items)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_one_auto_items(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 1}]
        expected = """
<center>
<table>
<tr><td>data_item_1</td><td>id</td></tr>
<tr><td>1</td><td>sn001</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_one_auto_items_missing(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001'}, {'id': 'sn002', 'data_item_1': 2}]
        expected = """
<center>
<table>
<tr><td>data_item_1</td><td>id</td></tr>
<tr><td>2</td><td>sn002</td></tr>
<tr><td></td><td>sn001</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_one_with_format(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 1}]
        the_items = ['id', 'data_item_1']
        the_config = {}
        the_config['the_format'] = 'border="1" cellpadding="5"'
        expected = """
<center>
<table border="1" cellpadding="5">
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn001</td><td>1</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, the_items, the_config)
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_one_first_column(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 1}]
        expected = """
<center>
<table>
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn001</td><td>1</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, first_column='id')
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_two_first_column_sort_other(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 2}, {'id': 'sn002', 'data_item_1': 1}]
        expected = """
<center>
<table>
<tr><td>id</td><td>data_item_1</td></tr>
<tr><td>sn002</td><td>1</td></tr>
<tr><td>sn001</td><td>2</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, first_column='id', sort_column='data_item_1')
        self.assertEqual(expected, actual)

    # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    def test_view_simple_two_flag_sort_header(self):
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        """
        (fill in here)
        """
        the_data = [{'id': 'sn001', 'data_item_1': 2}, {'id': 'sn002', 'data_item_1': 1}]
        the_config = {}
        the_config['highlight_sorted'] = True
        expected = """
<center>
<table>
<tr><td>id</td><td style="background-color:rgba(0,255,0,0.3)">data_item_1</td></tr>
<tr><td>sn002</td><td>1</td></tr>
<tr><td>sn001</td><td>2</td></tr>
</table>
</center>
""".replace('\n', '')
        actual = simple(the_data, first_column='id', sort_column='data_item_1', the_config=the_config)
        self.assertEqual(expected, actual)

# End of source file
