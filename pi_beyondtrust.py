_ = """
sudo vi /cardinal/pi_beyondtrust.py

sudo systemctl restart pi-beyondtrust.service

watch -n 1 sudo cat /dev/shm/pi_beyondtrust_datadrop.txt

cat /cardinal/log/pi_beyondtrust_lastupdate.txt

"""

service = 'beyondtrust'
version = 'T.1.0'
description = """
This is a pi service beyondtrust.

"""

release_notes = """
2022.09.02
T.1.0

Service to install and configure the BeyondTrust tool.

"""

_notes = """
installed_location/settings.ini
This file contains version information

"""

_manual_setup_test = """
bomgar-scc-w0eec30fhzzzhi8z6fyjjfdfe5xg67wdy1xg5fjc40hc90.bin


On Pi: 10000000aaef9969


cd /cardinal
sudo python3
import pi_runner
#bomgar-scc-w0eec30e8x71ihxdyfzidg6jfy67fi51yyyed6xc40hc90.bin
pi_runner.get_server_file_to_local('bomgar-scc-w0eec30e8x71ihxdyfzidg6jfy67fi51yyyed6xc40hc90.bin', '/cardinal/bomgar-scc-w0eec30e8x71ihxdyfzidg6jfy67fi51yyyed6xc40hc90.bin')
exit()


1)
cd /cardinal
sudo sh ./bomgar-scc-w0eec30e8x71ihxdyfzidg6jfy67fi51yyyed6xc40hc90.bin --install-dir /cardinal/bomgar --user root

sudo /cardinal/bomgar/init-script start
sudo /cardinal/bomgar/init-script status
sudo /cardinal/bomgar/init-script stop


later???
sudo /cardinal/bomgar/uninstall

or 2)
sudo su
cd /cardinal
sh ./bomgar-scc-w0eec30e8x71ihxdyfzidg6jfy67fi51yyyed6xc40hc90.bin --install-dir /home/<USER>/bomgar --user root

/home/<USER>/bomgar/init-script start
/home/<USER>/bomgar/init-script status
/home/<USER>/bomgar/init-script stop

/home/<USER>/bomgar/uninstall

end)




The client is now installed but is not started.  Please arrange for the
following init script to be run at boot or at the time of your choosing:
	/cardinal/bomgar/init-script

This init script accepts the conventional 'start', 'stop', 'restart' and
'status' arguments.

Note, this deployment is still subject to the configured mass deployment
expiry until it is started and successfully connects for the first time.

The following command may be run to uninstall the client.
sudo /cardinal/bomgar/uninstall
!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!
(requires typing y to continue... not cool for script doing the work)
!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!

==============================
==============================
Cert: (New try)
==============================
on mac, ~/downloads/20220901/cert20220901-092618.cer
rename to bomgar-cert2.crt

on pi:

cd /cardinal
sudo python3
import pi_runner
pi_runner.get_server_file_to_local('bomgar-cert2.crt', '/usr/local/share/ca-certificates/bomgar-cert2.crt')
exit()

cd /usr/local/share/ca-certificates
sudo update-ca-certificates

sudo /cardinal/bomgar/init-script stop
sudo /cardinal/bomgar/init-script status
sudo /cardinal/bomgar/init-script start
sudo /cardinal/bomgar/init-script status


==============================
on mac, ~/downloads/cert20220831-082308.cer
rename to bomgar-cert.crt


upload to Slicer

on pi:

cd /cardinal
sudo python3
import pi_runner
pi_runner.get_server_file_to_local('bomgar-cert.crt', '/usr/local/share/ca-certificates/bomgar-cert.crt')
exit()

cd /usr/local/share/ca-certificates
sudo update-ca-certificates

sudo /cardinal/bomgar/init-script stop
sudo /cardinal/bomgar/init-script status
sudo /cardinal/bomgar/init-script start
sudo /cardinal/bomgar/init-script status


==============================
cert did not work (original try):
==============================
on mac:
cd ~/Downloads
openssl pkcs7 -print_certs -in certs20220831-075733.p7b -out bomgar-cert.cer


on pi:

cd /cardinal
sudo python3
import pi_runner
pi_runner.get_server_file_to_local('bomgar-cert.p7b', '/usr/local/share/ca-certificates/bomgar-cert.cer')
exit()

cd /usr/local/share/ca-certificates
sudo openssl x509 -inform DER -in bomgar-cert.p7b -out bomgar-cert.crt
sudo update-ca-certificates

sudo vi /etc/blog.ini (before install)
------------------ start cut below here
[blog/debug]
output = /tmp/$COMPANY-$APP_NAME.log
category.All=All
flush_interval=0
file_size_limit=75M
file_size_limit_retain=5F
enabled=1
------------------ end cut above here

after uninstall, install, start
cat /tmp/cardinalhealthsr-bomgar-scc.log

rm /tmp/cardinalhealthsr-bomgar-scc.log

"""

other_content = """
sudo vi /cardinal/pi-beyondtrust
sudo chmod +x /cardinal/pi-beyondtrust

# ===== begin: start file
#!/usr/bin/env python3
import pi_beyondtrust
pi_beyondtrust.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-beyondtrust.service
sudo systemctl daemon-reload
sudo systemctl stop pi-beyondtrust.service
sudo systemctl start pi-beyondtrust.service
sudo systemctl enable pi-beyondtrust.service

systemctl status pi-beyondtrust.service

sudo systemctl restart pi-beyondtrust.service

# beyondtrust of std out
cat /var/log/syslog | fgrep pi-beyondtrust

OR

tail -f /var/log/syslog | fgrep pi-beyondtrust

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-beyondtrust
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_beyondtrust

Admin:
https://eval-cf891d09.beyondtrustcloud.com/login/login

Jumper:
https://eval-cf891d09.beyondtrustcloud.com/console

email (df@cardinal)
password (9)


+ On Meraki



"""

import copy
import json
import math
import os

try:
    import requests
except:
    pass  # for unittest
import shutil
import socket
import subprocess
import sys
import time
import traceback
import unittest


# ----------------------------
def do_one_command(command):
    # ----------------------------
    import shlex
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def do_one_time():
    # ----------------------------
    list_of_cmds = ['sudo /cardinal/bomgar/init-script start']
    #    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass


# ----------------------------
def get_serial():
    # ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial


# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
    # ----------------------------
    the_file = '/dev/shm/pi_beyondtrust_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')


# ----------------------------
def call_home_locations():
    # ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'", '"'))
    except:
        pass

    return response


# ----------------------------
def do_datadrop():
    # ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug('beyondtrust data drop: Start', True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in with slicer
            try:
                do_datadrop_debug('Start beyondtrust...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug('beyondtrust result: ' + url_result)

                try:
                    result_json = json.loads(
                        url_result)  # This will throw exception if the previous block passed 'exception'

                    if 'action_request' in result_json:
                        pass
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())

    do_datadrop_debug('beyondtrust data drop: End')


# ----------------------------
def do_maintenance():
    # ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()


# ----------------------------
def main():
    # ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string for " + service + ": " + version)

    do_one_time()

    # For two different working examples of having many worker threads:
    #    look to pi_bluetooth, in this same spot (apscheduler)
    #    look to pi_network, in this same spot (built in threading)

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * 30):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_beyondtrust_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print("!!! failed to write wake_count")


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_beyondtrust(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)
