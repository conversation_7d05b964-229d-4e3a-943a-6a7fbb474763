AA-gcp_rocky_server.txt

#######################
AD Groups:
#######################

# To be able to create the slicer project in GCP
A-APM0028269-Slicer-admins

# To be able to operate the slicer server(s) in GCP
a-cloud-slicer-pr-editor
a-cloud-slicer-pr-owner

# To gain access to the reports from BigPanda/Dynatrace
A-OktaSSO-Dynatrace-Users


SNOW request to create new server:

02 -> 2023.01.05 07:55:01
REQ3264966

03 -> 2023.02.10
REQ3310940

    https://cardinal.service-now.com/gith/?id=sc_cat_item&sys_id=67bcbe291b450150ca2ba645624bcb62

    Type: Create VM
    ( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)
    image: cah-rocky-8
    GCP Project: mac-mgmt-pr-cah
    Environment: Production
    Internet facing: No
    Instance name: slicr03
    (builds instance name of lpec5009slicr03)
    Zone: us-central1-a
    service account: pi-mgmt-pr-slicer-main
    network tags: int-webserver
    AD Groups: A-MacSysAdmins (Must add this before selecting cah-rocky-8)

    Machine Family: GENERAL-PURPOSE
    Series: N1
    Machine Type: n1-standard-4
    Boot Disk Type: pd-standard
    Boot Disk Size: 30
    Default Disk Type: pd-ssd
    Name: slicer03-ssd
    Size: 60
    Additional: No
    APM ID: APM0022695 (not changeable) (this is JamfPro APM, Slicer is APM0028269, Raspberry Pi is APM0027587, Jamf Cloud is 28731)
    Funded: No
    Submit

04 -> 2024.01.08
    GCP project request:
        Service now, search for "GCP cloud project request", and follow the link:
        https://cardinal.service-now.com/now/nav/ui/classic/params/target/com.glideapp.servicecatalog_cat_item_view.do%3Fv%3D1%26sysparm_id%3Df432c90edb7b9300be676165ca9619dd
        naming: https://wiki.cardinalhealth.net/GCP_Naming_Standards/Projects

        Looking to do: Create New
        Is this an EDDIE, ...: No
        project name: slicer
        environment: Prod
        project ID: slicer-pr-cah [auto populate from choices]
        primary application: Slicer (APM0028269)
        contact manager: Aaron Perkins [auto populate from application]
        cost center: ********** [auto populate from application]
        click 'add to cart' (go to check out)
        click 'Submit Items'
        REQ3708042

        After creation, went to the GCP page (https://login.cardinalhealth.net/),
            and tried to search and open "slicer-pr", and it reported:
"
You need additional access to the project:  slicer-pr
To request access, contact your project administrator and provide them a copy of the following information:

Troubleshooting info:
  Principal: <EMAIL>
  Resource: slicer-pr-cah
  Troubleshooting URL: console.cloud.google.com/iam-admin/troubleshooter;permissions=compute.instances.list;principal=<EMAIL>;resources=%2F%2Fcloudresourcemanager.googleapis.com%2Fprojects%2Fslicer-pr-cah/result

Missing permissions:
  compute.instances.list
"
        https://cardinal.service-now.com/gith?sys_id=6e98df6a4763715073d32a24836d43e2&view=sp&id=ticket_request&table=sc_req_item
            Posted a question of "how do we resolve this" on

        Go to "Get IT help": https://cardinal.service-now.com/gith?id=sc_home
        click on "Access Request for Active Directory Groups"
            type of request: Grant user access
            account type: normal
            domain: CardinalHealth.net
            select the group: a-cloud-slicer-pr-owner
            click "add to cart"
            click on the cart icon,. that now has a "1" on it: Checkout, then Checkout again
            REQ3712842 Created 2024.01.11

        # owner was enough to get VM created, but not enough to get ssh access to it, so go for editor also

        Go to "Get IT help": https://cardinal.service-now.com/gith?id=sc_home
        click on "Access Request for Active Directory Groups"
            type of request: Grant user access
            account type: normal
            domain: CardinalHealth.net
            select the group: a-cloud-slicer-pr-editor
            click "add to cart"
            click on the cart icon,. that now has a "1" on it: Checkout, then Checkout again
            REQ3730580 Created 2024.01.25
                RITM5763634



    # VM Creation
    (old) https://wiki.cardinalhealth.net/GCP_Create_VM_Instance
    (current) https://wiki.cardinalhealth.net/GCP_VM_Instance_Requests
        - it complains about needing to be logged in, so click the log in link, and try again
        - find the link in the section "GCP VM Instance Request Steps - ServiceNow"
        - that looks like " ServiceNow > Service Catalog > Cloud Management > VM Instance Creation",
            and click on the last part of the link. (and log in again, if prompted)
            https://cardinal.service-now.com/gith?id=sc_cat_item&table=sc_cat_item&sys_id=5e0450f71be93550473c36ef034bcbec
        - then, copy that link, and open Chrome, and paste it in the address bar.

    Type: Create VM
    ( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)
    image: cah-rocky-9
    GCP Project: slicer-pr-cah
    Environment: Production
    Internet facing: No
    Instance name: slicr04
    (builds instance name of lpec5009slicr04)
    Zone: us-central1-a
    service account: slicer-pr-def
    network tags: int-webserver
    AD Groups: A-APM0028269-Slicer-admins (Must add this before selecting cah-rocky-9)

    Machine Family: GENERAL-PURPOSE
    Series: N1
    Machine Type: n1-standard-4
    Boot Disk Type: pd-standard
    Boot Disk Size: 30
    Default Disk Size: 200
    Default Disk Name: slicer04-ssd
    Additional: No
    APM ID: APM0028269 (not changeable) (this is Slicer APM APM0028269, Raspberry Pi is APM0027587)
    Autostart:
    Autostop:
    Funded: No
    Submit Now
    2024.01.10
        REQ3711830
            RITM5739116 (Since there was not an owner at the time of the request, this request was cancelled. Make a new one)
    2024.01.16
        REQ3718032
            RITM5747141


    Wait for it to get approved and created
    (search req number in ServiceNow to see status)

    Go to GCP console to see that it exists: (May take a while to get created?)
        https://console.cloud.google.com/welcome?project=slicer-pr-cah
            - Click into "Compute Engine"



    Make a start file, 'slicer04', with the line 'gcloud compute ssh david.ferguson@lpec5009slicr04 --zone us-central1-a --project slicer-pr-cah --internal-ip'
    Then 'chmod +x slicer04'
    Then, 'gcloud config set project slicer-pr-cah'
    Then, 'gcloud auth login' (Follow prompts to get authenticated)
    Then, launch with ./slicer04

# If the "Remote into it via SSH" in below here fails, then toggle the oslogin on then off, like:
(First one will fail the first time, so just use the second line to get the value set)
gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=FALSE

(slicr04 = ***********)


05 -> 2024.03.19
    (current) https://wiki.cardinalhealth.net/GCP_VM_Instance_Requests
        - it complains about needing to be logged in, so click the log in link, and try again
        - find the link in the section "GCP VM Instance Request Steps - ServiceNow"
        - that looks like " ServiceNow > Service Catalog > Cloud Management > VM Instance Creation",
            and click on the last part of the link. (and log in again, if prompted)
            https://cardinal.service-now.com/gith?id=sc_cat_item&table=sc_cat_item&sys_id=5e0450f71be93550473c36ef034bcbec
        - then, copy that link, and open Chrome, and paste it in the address bar.

    Type: Create VM
    ( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)
    image: cah-rocky-9
    GCP Project: slicer-pr-cah
    Environment: Production
    Internet facing: No
    Instance name: slicr05
    (builds instance name of lpec5009slicr05)
    Zone: us-central1-a
    service account: slicer-pr-def
    network tags: int-webserver
    AD Groups: A-APM0028269-Slicer-admins (Must add this before selecting cah-rocky-9)

    Machine Family: GENERAL-PURPOSE
    Series: N1
    Machine Type: n1-standard-4
    Boot Disk Type: pd-standard
    Boot Disk Size: 30
    Additional: 1 additional
    type: pd-ssd
    Default Disk Size: 200
    Default Disk Name: slicer05-ssd
    APM ID: APM0028269 (not changeable) (this is Slicer APM APM0028269, Raspberry Pi is APM0027587)
    Autostart:
    Autostop:
    Funded: No
    Submit Now
    2024.03.19
        REQ3799382
            RITM5856652


    Wait for it to get approved and created
    (search req number in ServiceNow to see status)

    Go to GCP console to see that it exists: (May take a while to get created?)
        https://console.cloud.google.com/welcome?project=slicer-pr-cah
            - Click into "Compute Engine"

    Make a start file, 'slicer05', with the line 'gcloud compute ssh david.ferguson@lpec5009slicr05 --zone us-central1-a --project slicer-pr-cah --internal-ip'
    Then 'chmod +x slicer05'
    Then, 'gcloud config set project slicer-pr-cah'
    Then, 'gcloud auth login' (Follow prompts to get authenticated)
    Then, launch with ./slicer05

# If the "Remote into it via SSH" in below here fails (or asks for a password), then toggle the oslogin on then off, like:
(First one will fail the first time, so just use the second line to get the value set)
gcloud compute instances add-metadata lpec5009slicr05 --zone us-central1-a --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata lpec5009slicr05 --zone us-central1-a --metadata enable-oslogin=FALSE

(slicr05 = ***********)
lpec5009slicr05.cardinalhealth.net

on first login, check if the root account is set to expire, and is so, set it to not expire:
(same for user account)

# ================
# Set root password to never expire
# ================
test:
sudo chage -l root
sudo chage -l david.ferguson

set:
sudo chage -M -1 root
sudo chage -M -1 david.ferguson


# ================
# Connection to persistent disk (SSD) in GCP VM
# ================
https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

(disc created in ServiceNow request)

[david.ferguson@lpec5009slicr02 ~]$
lsblk

NAME   MAJ:MIN RM  SIZE RO TYPE MOUNTPOINT
sda      8:0    0   30G  0 disk
├─sda1   8:1    0  200M  0 part /boot/efi
└─sda2   8:2    0 29.8G  0 part /
sdb      8:16   0   60G  0 disk


sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb

sudo mkdir -p /mnt/disks/SSD
sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD
sudo chmod a+w /mnt/disks/SSD

sudo cp /etc/fstab /etc/fstab.backup
sudo blkid /dev/sdb

(slicr02)
/dev/sdb: UUID="14f5e8b5-48c8-4001-b4fd-a30a91756baf" BLOCK_SIZE="4096" TYPE="ext4"

(slicr03)
/dev/sdb: UUID="02a464b5-ac91-4789-b056-28aa528f31e4" BLOCK_SIZE="4096" TYPE="ext4"

(slicer04)
/dev/sdb: UUID="3869d303-aa4e-4c82-bc32-097359cb279b" TYPE="ext4"

(slicer05)
/dev/sdb: UUID="ec7bd656-5ff6-4ed8-ac25-7344a7ce011c" TYPE="ext4"

sudo vi /etc/fstab

(slicr02)
UUID=14f5e8b5-48c8-4001-b4fd-a30a91756baf /mnt/disks/SSD ext4 discard,defaults,nofail 0 2

(slicr03)
UUID=02a464b5-ac91-4789-b056-28aa528f31e4 /mnt/disks/SSD ext4 discard,defaults,nofail 0 2

(slicr03)
UUID=3869d303-aa4e-4c82-bc32-097359cb279b /mnt/disks/SSD ext4 discard,defaults,nofail 0 2

(slicr04)
UUID=3869d303-aa4e-4c82-bc32-097359cb279b /mnt/disks/SSD ext4 discard,defaults,nofail 0 2

(slicr05)
UUID=ec7bd656-5ff6-4ed8-ac25-7344a7ce011c /mnt/disks/SSD ext4 discard,defaults,nofail 0 2


sudo chmod 755 /mnt
sudo chmod 755 /mnt/disks

# Do the install to get apache user created
sudo dnf update -y
sudo dnf install -y mod_ssl
sudo dnf install -y httpd

sudo chown -R apache:apache /mnt/disks/SSD
sudo chmod 777 /mnt/disks/SSD

sudo mkdir /mnt/disks/SSD/var
sudo chown -R apache:apache /mnt/disks/SSD/var

sudo dnf upgrade --refresh
sudo dnf config-manager --set-enabled crb
sudo dnf install     https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm     https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
sudo dnf install htop -y

--------------------------
None of this block worked to get the apache user to be able to make xlsx using pandas:
Save it, just for future attempts.

sudo -H -u apache pip install pandas
sudo -u apache pip install openpyxl
OR
sudo su
cd ~
umask 022
pip install pandas
pip install openpyxl
exit

sudo dnf install mlocate -y
sudo updatedb
locate pandas

sudo pip install --upgrade --target /var/www/html pandas
sudo pip install --upgrade --target /var/www/html openpyxl

--------------------------

slicer05 - take a snapshot at this point (do a "sudo shutdown -h now", then go to GCP, and do the snapshot)
    image-20240320101400 (and any snapshot shortly there after would work also)


From here, go to the top of the slicer_wsgi_loader.py file, and follow the setup-by-step, then come back here.

---------------------------------------------------
Added 2024.09.25

Restrict to TLS1.3 only:

https://www.leaderssl.com/news/471-how-to-disable-outdated-versions-of-ssl-tls-in-apache

sudo vi /etc/httpd/conf.d/ssl.conf

(just after the #SSLProtocol line)

SSLProtocol +TLSv1.3

# end of edits

There was another suggested solution, which was:
SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1 -TLSv1.2

2024.10.02 5:58am.
<EMAIL>
Confirmed that slicr05 is TLS1.2 and TLS1.3, which is what is desired.

Change CHG0355824 is approved to make the update to production on 2024.10.08

2024.10.08
Making the change in production (slicer04)


---------------------------------------------------
Load up content:

Browse to the IP address of the server.

Enabled Trust by clicking on the "Not Trusted" when the index (home) page first loads.

Log in as a user that has the admin privilege by being a member of "A-APM0028269-Slicer-admins"

Click into 'users' page, and give yourself 'loader create', 'dataport_create' permissions.

Go back home, see the dataport link, follow it, and then choose the saved datastore_snapshot.txt file.

Go back home, see all permissions restored, and shows all allowed modules.

Go to 'upload', and upload the file 'read_release_notes_slicer_pi.txt'

Pull down htmlfiles content, and upload to the new server.

FixMe: ????? Done to here

Pull down the multimedia content for all, and load to new server.

codeupload all the pi content.

download all download content from old server, then "upload" to new

DNS:
Requested Service Not Found
EITSS-Directory Services Engineering


++++++++
Maybe, allow on slicer instance to pull videos (htmlfiles),
multimedia (including old ones), logs... basically all the sections of the
'organization' directory reporting... maybe do it there, so that we can see
the content on the source side, and then on the pull side, click to start pulling,
and then compare the results, to see that the content matches. Maybe do rsynch to keep
it current, and the data continues to pour in... that would be good for setting up a
warm spare too.
Might want to throttle this sync, so that we do not swamp the network connection, yet
still complete in a decent time... in any case, show the GB/MB remaining to sync.

Can I back propagate, so that any device checking in to the 04 server, can get its data on the 01 server?
I would need to know which server the 'source' is for that device (currently/last data), in order to know that
we are getting the traffic changed over, and to know when it is ok to close down the old server.

Would need to have a text (html) response to status questions, and then
a 'file download' equivalent interface to get the content from one server to the other
(like how pi devices download content from the server, to be used locally.)

How to "skip" reporting on temporary files? (So that we do not even attempt the copy.


++++++++


Click into 'htmlfiles', upload the video files we need (where to find them?)

Click into 'multimedia', upload the saved content from the most recent multimedia pages on the old server.



Upload pi code, readme files, and test device updates are working.

Upload pi image
    fails, look to:
    https://stackoverflow.com/questions/8896644/request-entity-too-large

---------------------------------------------------

SHOULD BE FULLY WORKING BY HERE

---------------------------------------------------
Manual fixes after first loader install attempt:
---------------------------------------------------
http://************/loader
http://************


# Not sure if any of these helped, but they were attempted
sudo setfacl -m "u:apache:rwx" "/usr/local/lib/python3.6/site-packages"


sudo chmod 755 /usr/local/lib/python3.6/site-packages -R

sudo restorecon -R /usr/local/lib/python3.6/site-packages/

sudo setsebool -P httpd_tmp_exec on

sudo chmod o+rx /usr/local/lib/python3.6/site-packages -R

sudo setsebool -P httpd_read_user_content 1


tried but fails:
chgrp -R www-data /usr/local/lib/python3.6/site-packages


#############################################
2022.12.20
Dev setup

https://rockylinux.org/download/
pick Rocky8.7, x86, minimal

Virtual Box
New
Name: Rocky8
image: Rocky 8 minimal
skip unattended: checked
next

base memory: 8000 MB
processors: 4
next

create: 20 GB
next

finish

settings
network
(For CAH remote, must be wired to USB ethernet adapter, to Meraki, and select that for the bridge)
attached to: Bridged Adapter
ok

start

prompt in vm: arrow up to "Install Rocky Linux 8"
enter

english
continue

root password (set it)

scroll to see user creation
Ubuntu
ubuntu
make administrator: checked
done

installation destination
done

network & host
toggle slide switch: ON
done

begin installation

(left command key to escape mouse capture)

(once install complete)
Reboot System

shutdown -h now

(take a snapshot here)

2023.01.25
https://collab.cardinalhealth.net/sites/ADForms/Shared%20Documents/DNS_Guide-ServiceNow.pdf

https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=9e9f167cdbf0e344be676165ca961963

2023.01.25
What: Add
Type: A Record
ASAP: Yes
Host: slicer2
Domain: cardinalhealth.net
FQDN: slicer2.cardinalhealth.net
IP: ************
: Internal

submit:
REQ3288460

Took about 30 minutes, got an automated email reporting that there was already a
    DNS entry pointing to that IP, and ping started to work.

# Now change it
Modify
A Record
ASAP: Yes
slicer2
Host: slicer2
Domain: cardinalhealth.net
FQDN: slicer2.cardinalhealth.net
OLD: ************
NEW: ***********
: Internal




#############################################
2024.02.14
https://www.linuxcapable.com/install-htop-on-rocky-linux/

sudo dnf upgrade --refresh
sudo dnf config-manager --set-enabled crb
sudo dnf install     https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm     https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
sudo dnf install htop -y

#############################################
2024.04.07
Midnight Saturday to Sunday rollover lockup of apache:

https://www.reddit.com/r/debian/comments/15stmn7/apache_error_scoreboard_is_full_not_at/

https://forums.rockylinux.org/t/apache-mpm-event-module/9274
cat /etc/httpd/conf.modules.d/00-mpm.conf


Put into:
/etc/httpd/conf.modules.d/10-mpm-event.conf

sudo apachectl configtest

systemctl restart httpd




Are these the defaults???:
<IfModule mpm_event_module>
    StartServers             3
    MinSpareThreads          75
    MaxSpareThreads          250
    ThreadLimit              64
    ThreadsPerChild          25
    MaxRequestWorkers        400
    MaxConnectionsPerChild   0
</IfModule>









