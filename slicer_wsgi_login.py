# A login handler

service = 'login'
version = service + '.0.8.a'

release_notes = """
2022.11.28
0.6
Add option for user_list login.
Clear the error that was preventing ldap from working.

2022.11.23
0.5

Add login exception reporting.

"""

_ = """
This file gets loaded to:
/var/www/html/login.py

using:
sudo mkdir /var/log/slicer
sudo chown -R apache:apache /var/log/slicer

sudo vi /var/www/html/login.py

It also requires:

sudo vi /etc/httpd/conf.d/python-login.conf
----- start copy -----
WSGIScriptAlias /login /var/www/html/login.py
----- end copy -----

sudo chown apache:apache /var/www/html/login.py

sudo systemctl restart httpd

sudo systemctl restart login-runner.service

##############
# notes
##############

The url to hit it is https://slicer.cardinalhealth.net/login

It looks to for content:
/var/log/slicer/login/


LDAP:
https://blog.thomastoye.be/python-ldap-authentication-with-microsoft-active-directory-46661bebc483
sudo yum install -y python-ldap


https://wiki.cardinalhealth.net/IAM_Corp_LDAP#Corporate_LDAP

https://stackoverflow.com/questions/7716562/pythonldapssl
https://www.python-ldap.org/en/python-ldap-3.3.0/reference/ldap.html


"""

_ = """

---- websockets ----
Look into websockets as a way to communicate permission status to a live web page

https://web.dev/websockets-basics/


https://en.wikipedia.org/wiki/HTTP/2
As of Apache 2.4.17 all patches are included in the main Apache source tree, although the module itself was renamed mod_http2

https://www.cvedetails.com/version-list/45/66/1/Apache-Http-Server.html?order=0

Rocky:
httpd -v
Server version: Apache/2.4.37 (rocky)

Ubuntu:
apache2 -v
Server version: Apache/2.4.52 (Ubuntu)


https://httpd.apache.org/docs/2.4/mod/mod_proxy_wstunnel.html
https://orangeable.com/server/secure-websocket
https://orangeable.com/javascript/chat

---- web socket ----
https://gist.github.com/mortenege/91ec6fe02dca6f736303a00f8cea2731

example with VNC also
https://techglimpse.com/configure-apache-for-websockets-using-reverse-proxy/

---- web socket using Pulsar ----
https://pulsar.apache.org/docs/2.11.x/client-libraries-websocket/

https://pulsar.apache.org/docs/2.11.x/client-libraries-websocket/#client-examples

---- web socket other ----

https://stackoverflow.com/questions/17334319/setting-up-a-websocket-on-apache

https://deephaven.io/blog/2022/08/01/coinbase-websockets/


















---- h2 ----
Python:
https://python-hyper.org/projects/hyper-h2/en/stable/wsgi-example.html

Rocky:
sudo python3 -m pip install h2


https://stackoverflow.com/questions/52273174/how-to-implement-http-2-stream-connection-in-browser




"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

import datetime
from hashlib import sha256
import json
import os
import shlex
import subprocess
import sys
import time
import traceback
import unittest

try:
    import ldap
except:
    pass  # unittest

try:
    # python 2 and some early python 3
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

user_domain = '(none)'
user_domain_list = []
service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
startup_exceptions = ''
login_authentication = {'authentication_type': ''}
try:
    import organization

    service_config = organization.get_config(service)

    login_authentication = service_config['login_authentication']

    if 'user_domain' in login_authentication:
        user_domain = login_authentication['user_domain']
    else:
        user_domain = '(missing)'

    if 'user_domain_list' in login_authentication:
        user_domain_list = login_authentication['user_domain_list']
    else:
        user_domain_list = ['(domain missing)']

    if 'user_list' in service_config:
        user_list = service_config['user_list']
    else:
        user_list = {}

    base_log_path = service_config['base_log_path']
    time_to_remain_valid = service_config['time_to_remain_valid']

    organization.make_all_dirs(service_config)  # FixMe: throwing problem on slicer01, so move to end

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import ldapsupport
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


#    ldap_servers = ['ldap://WPIL0219ADIDC' + '02' + '.cardinalhealth.net',
#                    'ldap://WPIL0219ADIDC' + '25' + '.cardinalhealth.net',
#                    'ldap://WPIL0219ADIDC' + '25' + '.cardinalhealth.net']
#    user_domain = '@cardinalhealth.com'
#    base_log_path = '/var/log/slicer/login/'
#    time_to_remain_valid = 2 * 60 * 60 # in seconds

# ====================================
def get_current_raw_day_hour_TS():
    # ====================================
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704

    return TS[0:8], TS[8:10], TS


# ====================================
def set_user_details_keys_string(user, user_details_keys_string):
    # ====================================
    the_who = '(login)'
    datastore_name_key = 'user_login_' + user + '_details'

    datastore.set_value(datastore_name_key, user_details_keys_string, who=the_who)


# ====================================
def set_user_is_admin(user, has_the_admin_priveledge=True):
    # ====================================
    the_who = '(login)'
    datastore_name_key = 'user_login_' + user + '_admin'

    if has_the_admin_priveledge:
        datastore.set_value(datastore_name_key, 'Yes', who=the_who)
    else:
        datastore.set_value(datastore_name_key, 'No', who=the_who)
    pass


# ====================================
def get_user_is_admin(user):
    # ====================================
    datastore_name_key = 'user_login_' + user + '_admin'

    if datastore.get_value(datastore_name_key) == 'Yes':
        return True
    else:
        return False


# ====================================
def get_all_login_valid_users():
    # ====================================
    result = []

    try:
        list_names = os.listdir(base_log_path + '/attempts/valid/')
        for list_name in list_names:
            if not list_name in result:
                result.append(list_name)
    except:
        pass

    return result


# ====================================
def get_all_login_attempt_users():
    # ====================================
    result = []

    try:
        list_names = os.listdir(base_log_path + '/attempts/valid/')
        for list_name in list_names:
            if not list_name in result:
                result.append(list_name)
    except:
        pass

    try:
        list_names = os.listdir(base_log_path + '/attempts/notvalid/')
        for list_name in list_names:
            if not list_name in result:
                result.append(list_name)
    except:
        pass

    return result


# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def _get_touch_and_user_filenames(addr, TS):
    # ====================================
    last_touch_file = base_log_path + 'touch/' + addr + '.' + TS
    last_user_file = base_log_path + 'user/' + addr + '.' + TS

    if not os.path.exists(os.path.dirname(last_touch_file)):
        os.makedirs(os.path.dirname(last_touch_file))

        if not os.path.exists(os.path.dirname(last_user_file)):
            os.makedirs(os.path.dirname(last_user_file))

    return last_touch_file, last_user_file


# ====================================
def __current_login_active(environ, refresh_timeout):
    # ====================================
    is_current = False

    addr = environ['REMOTE_ADDR']
    cookie_content = get_cookie_contents_from_environ(environ)
    TS = ''
    if 'login_time' in cookie_content:
        TS = cookie_content['login_time']

    if TS:
        try:
            last_touch_file, last_user_file = _get_touch_and_user_filenames(addr, TS)

            last_time = 0
            with open(last_touch_file, 'r') as f:
                last_time = f.read()

            time_of_last_touch = time.time()
            if time_of_last_touch - float(last_time) < time_to_remain_valid:
                is_current = True
                if refresh_timeout:
                    with open(last_touch_file, 'w') as f:
                        f.write(str(time_of_last_touch))

            if not is_current:
                with open(last_user_file, 'w') as f:
                    f.write('')
        except:
            pass

    return is_current


# ====================================
def get_current_user(environ, refresh_timeout=False):
    # ====================================
    last_user = ''

    addr = environ['REMOTE_ADDR']
    cookie_content = get_cookie_contents_from_environ(environ)
    TS = ''
    if 'login_time' in cookie_content:
        TS = cookie_content['login_time']

    if TS:
        try:
            last_touch_file, last_user_file = _get_touch_and_user_filenames(addr, TS)

            with open(last_user_file, 'r') as f:
                last_user = f.read()
        except:
            pass

    if not __current_login_active(environ, refresh_timeout):
        last_user = ''

    return last_user


# ====================================
def save_user_login(time_of_last_touch, addr, user='', TS=''):
    # ====================================
    last_touch_file, last_user_file = _get_touch_and_user_filenames(addr, TS)

    # this is the look up of the user for this session
    with open(last_touch_file, 'w') as f:
        f.write(str(time_of_last_touch))

    with open(last_user_file, 'w') as f:
        f.write(str(user))


# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def check_ldap_command_line_response_good(response):
    # ====================================
    if 'Result: Success (0)' in response:
        return True
    else:
        return False


# ====================================
def check_if_valid_user(user, password):
    # ====================================
    valid = False
    return_status = ''
    return_log = ''
    ok_to_write_valid_status_file = False
    if password == 'this is to fake the valid login and get the user into the system':
        ok_to_write_valid_status_file = True

    if not ok_to_write_valid_status_file:
        authentication_type = login_authentication['authentication_type']

        if authentication_type == 'ldap-cmdln':
            domain_to_check = user_domain

            ldap_servers = login_authentication['ldap_servers']
            for ldap_server in ldap_servers:
                for domain_to_check in user_domain_list:
                    command = "ldapwhoami -h " + ldap_server + " -D <test_user> -x -w <test_password> -vvv"
                    command = command.replace('<test_user>', user + domain_to_check)
                    command = command.replace('<test_password>', password)
                    p, f = do_one_command(command)
                    return_log += '---------------------------------' + '\n'
                    return_log += 'ldap_server:' + ldap_server + '\n'
                    return_log += 'domain_to_check:' + domain_to_check + '\n'
                    return_log += 'user:' + user + '\n'
                    return_log += 'pass:' + p + '\n'
                    return_log += 'fail:' + f + '\n'
                    return_log += '---------------------------------' + '\n'
                    response = p + f
                    valid_in_this_domain = check_ldap_command_line_response_good(response)

                    if valid_in_this_domain:
                        # take this opportunity to check any user associations in ldap that we want to check
                        # reach out to ldapsupport module to do the work
                        is_user_admin = False

                        #                    if False:
                        if 'basedn' in login_authentication:
                            basedn = login_authentication['basedn']
                            results = ldapsupport.get_user_group_member_results(user, user_domain, password,
                                                                                'ldap://' + ldap_server, basedn)
                            user_details = ldapsupport.get_user_details_from_search_results(results)
                            keys_found = ','.join(sorted(user_details.keys()))
                            set_user_details_keys_string(user,
                                                         keys_found)  # Go look in datastore, for a substring of "user_login_(user)_details"
                            if 'admin_AD_group' in login_authentication:
                                if login_authentication['admin_AD_group'] in user_details:
                                    if user_details[login_authentication['admin_AD_group']]:
                                        is_user_admin = True

                        if is_user_admin:
                            set_user_is_admin(user)
                        else:
                            set_user_is_admin(user, False)
                        valid = True
                        break
                else:
                    continue
                break  # once we find it is valid, and hit the break, then skip checking the other servers

        elif authentication_type == 'ldap':
            try:
                ldap_servers = login_authentication['ldap_servers']
                for ldap_server in ldap_servers:
                    conn = ldap.initialize(ldap_server)
                    try:
                        response = conn.simple_bind_s(user + user_domain, password)
                        valid = True
                        break
                    except:
                        time.sleep(2)  # be nice to the servers, and not pound through the list
                        return_status = 'after time delay => ' + str(
                            traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
            except:
                return_status = 'failed to get list of ldap servers => ' + str(
                    traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

        elif authentication_type == 'blind_trust':
            valid = True

        elif authentication_type == 'user_list':
            valid = False
            if user in user_list:
                if 'password' in user_list[user]:
                    if password == user_list[user]['password']:
                        valid = True

        # other
        # https://httpd.apache.org/docs/2.4/mod/mod_session.html
        # https://history.phas.ubc.ca/System/UserAuthentication

        if valid:
            day, hour, TS = get_current_raw_day_hour_TS()
            datastore_name_key = 'user_login_' + user + '_lastvalidlogin'
            the_who = '(login)'
            datastore.set_value(datastore_name_key, day + '_' + hour, who=the_who)

    return valid, return_status, ok_to_write_valid_status_file, return_log


# ====================================
def check_if_valid(user, pass_hash):
    # ====================================
    notice = '1'
    user_to_use = user.lower()

    data_path = base_log_path + 'keep/' + user_to_use
    data_log = base_log_path + 'throw/' + user_to_use

    if not os.path.exists(os.path.dirname(data_path)):
        os.makedirs(os.path.dirname(data_path))

    valid = False

    try:
        notice = '2'
        with open(data_path, 'r') as f:
            notice = '3'
            if f.read() == pass_hash:
                notice = '4'
                valid = True
    except:
        pass

    if not valid:
        if not os.path.exists(os.path.dirname(data_log)):
            os.makedirs(os.path.dirname(data_log))
        with open(data_log, 'w') as f:
            f.write(pass_hash)

        # slow the response, to limit bad attempts
        time.sleep(3)

    return valid


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def make_body(environ):
    # ====================================
    status = '200 OK'
    redirect = '/'
    body = ''
    cookie_content_to_return = None

    query_items = {}
    query_string = ''
    if 'QUERY_STRING' in environ:
        query_string = environ['QUERY_STRING']
    for item in query_string.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        if environ['REQUEST_METHOD'] == 'POST':
            # https://wsgi.tutorial.codepoint.net/parsing-the-request-post

            try:
                request_body_size = int(environ.get('CONTENT_LENGTH', 0))
            except (ValueError):
                request_body_size = 0
            request_body = environ['wsgi.input'].read(request_body_size)
            d = parse_qs(request_body.decode('utf-8'))

            valid = False
            user = ''
            password = ''
            try:
                user = d['user'][0]
                password = d['pass'][0]
            except:
                pass

            if user and password:
                valid, login_status, ok_to_write_valid_status_file, return_log = check_if_valid_user(user, password)

                if login_status:
                    try:
                        open('/dev/shm/startup_exceptions_' + service, 'w').write(login_status)
                    except:
                        pass

            if valid:
                time_of_last_touch = time.time()
                status = '303 See Other'
                redirect = '/'
                log_report = 'valid'
            else:
                time_of_last_touch = 0
                status = '303 See Other'
                redirect = '/login'
                log_report = 'notvalid'

            TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
            # 20210406201829131704
            log_to_write = log_report
            if ok_to_write_valid_status_file:
                log_to_write = 'valid'
            output_file = base_log_path + '/attempts/' + log_to_write + '/' + user + '/' + TS
            # output_file = "/var/log/slicer/login/attempts/valid/david.ferguson/20210707193135658776"
            try:
                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))
                with open(output_file, 'w') as f:
                    f.write(log_report + '\n' + return_log)
            except:
                pass
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")) + '<br>' * 5 + output_file
                return status, body, []

            try:
                # Remember this address and TS with this user
                save_user_login(time_of_last_touch, environ['REMOTE_ADDR'], user, TS)

                # build what the browser needs to remember this user
                cookie_content_to_return = set_cookie_header(name='login_time', value=TS, days=1)
            except:
                pass
                body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
                return status, body, []


        elif environ['REQUEST_METHOD'] == 'GET':
            the_action = 'login'
            if 'action' in query_items:
                if query_items['action'] == 'logout':
                    the_action = 'logout'

            if the_action == 'logout':
                cookie_content = get_cookie_contents_from_environ(environ)
                if 'login_time' in cookie_content:
                    TS = cookie_content['login_time']
                    save_user_login(0, environ['REMOTE_ADDR'], TS=TS)  # This does the 'logout' action
                status = '303 See Other'

            if the_action == 'login':
                body += '<form method="post" action="">'
                body += '<center>'
                if 'site_title' in service_config:
                    body += '<B>' + service_config['site_title'] + '</B>'
                    body += '<br>'
                body += '<br>'
                body += version
                body += '<br>'
                body += '<br>'

                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += 'User'
                body += '</td>'
                body += '<td>'
                body += """<input type="text" size="""
                body += str(20)
                body += " name=\""""
                body += 'user'
                body += """\" value=\""""
                body += ""
                body += """\">"""
                body += '</td>'
                body += '<td>'
                if user_domain_list:
                    body += '<br> OR <br>'.join(user_domain_list)
                else:
                    body += user_domain
                body += '</td>'
                body += '</tr>'

                body += '<tr>'
                body += '<td>'
                body += 'Password'
                body += '</td>'
                body += '<td>'
                body += """<input type="password" size="""
                body += str(20)
                body += " name=\""""
                body += 'pass'
                body += """\" value=\""""
                body += ""
                body += """\">"""
                body += '</td>'
                body += '<td>'
                body += '</td>'
                body += '</tr>'

                body += '</table>'
                body += '</center>'

                body += """<center>"""
                body += """<br/><br/><input type="submit" value='login'> """
                body += """</center>"""

                body += '</form>'

                if 'admin_AD_group' in login_authentication:
                    body += '<br>' * 3
                    body += '<center>'
                    body += '-> admin user permission is automatically assigned to users in the ' + \
                            login_authentication['admin_AD_group'] + ' group'
                    body += '</center>'

                if False:
                    body += '<br>' * 100
                    body += str(service_config)

                    body += '<br>' * 2
                    body += str(login_authentication)

                    body += '<br>' * 2
                    body += str(user_domain_list)

        else:
            body += str(environ)

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    response_header = []
    if status == '200 OK':
        response_header = [('Content-type', 'text/html')]
        html = '<html>\n' \
               '<body>\n'
        html += body
        html += '</body>\n' \
                '</html>\n'
    if status == '303 See Other':
        response_header = [('Location', redirect)]
        html = ''

    if cookie_content_to_return:
        response_header.append(cookie_content_to_return)

    return status, html, response_header


# ====================================
def application(environ, start_response):
    # ====================================
    try:
        status, html, response_header = make_body(environ)

    except Exception as e:
        status = '200 OK'
        html = '<html>\n' \
               '<body>\n'
        html = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'
        response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def get_active_users():
    # ====================================
    return_value = {}

    last_touch_path = base_log_path + 'touch/'
    last_user_path = base_log_path + 'user/'

    time_now = time.time()
    try:
        touches = os.listdir(last_touch_path)
        for touch in touches:
            touch_file = last_touch_path + touch
            user_file = last_user_path + touch
            try:
                timestamp = float(open(touch_file, 'r').read())
                if time_now - timestamp < time_to_remain_valid:
                    try:
                        if os.path.isfile(user_file):
                            user = open(user_file, 'r').read()
                            if not user in return_value:
                                return_value[user] = []
                            # seconds remaining in the session (might be logged in on more than one machine/browser)
                            return_value[user].append(int(time_to_remain_valid - (time_now - timestamp)))
                    except:
                        pass
            except:
                pass
    except:
        print(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))

    return return_value


# ====================================
def clean_expired_sessions():
    # ====================================
    last_touch_path = base_log_path + 'touch/'
    last_user_path = base_log_path + 'user/'

    time_now = time.time()
    try:
        touches = os.listdir(last_touch_path)
        for touch in touches:
            touch_file = last_touch_path + touch
            user_file = last_user_path + touch
            timestamp = float(open(touch_file, 'r').read())
            if time_now - timestamp > time_to_remain_valid:
                # clean this one
                if os.path.isfile(touch_file):
                    os.remove(touch_file)
                if os.path.isfile(user_file):
                    os.remove(user_file)
    except:
        pass


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        if pass_count % 10 == 0:
            # do some work to clean out expired sessions
            clean_expired_sessions()
        time.sleep(2)
        pass_count += 1


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

    def test_check_ldap_command_line_response_good(self):
        response = """ldap_initialize( ldap://WPIL0219ADIDC02.cardinalhealth.net )
u:CARDINALHEALTH\david.ferguson
Result: Success (0)
"""
        expected = True
        actual = check_ldap_command_line_response_good(response)
        self.assertEqual(expected, actual)

        response = """ldap_initialize( ldap://WPIL0219ADIDC02.cardinalhealth.net )
ldap_bind: Invalid credentials (49)
	additional info: 80090308: LdapErr: DSID-0C09044E, comment: AcceptSecurityContext error, data 52e, v2580
"""
        expected = False
        actual = check_ldap_command_line_response_good(response)
        self.assertEqual(expected, actual)

# end of file
