# A charting interface

service = "chart"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/chart.py

using:
sudo mkdir /var/log/slicer
sudo chown -R apache:apache /var/log/slicer

sudo vi /var/www/html/chart.py

It also requires:

sudo vi /etc/httpd/conf.d/python-chart.conf
----- start copy -----
WSGIScriptAlias /chart /var/www/html/chart.py
----- end copy -----

sudo chown apache:apache /var/www/html/chart.py

sudo systemctl restart httpd


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/chart?chart=network,serial=10000000e3669edf

Reference:
https://tobiasahlin.com/blog/introduction-to-chartjs/

"""

import datetime
import json
import os
import shutil
import sys
import time
import unittest

network_utilization_save_path = '/var/log/slicer/network/id/(id)/'

startup_exceptions = ''

try:
    pass
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ====================================
def get_hourly_network_data(id):
    # ====================================

    hours = {}
    data_dir_network = network_utilization_save_path.replace('(id)', id)
    try:
        for file_found in os.listdir(data_dir_network):
            with open(data_dir_network + file_found, 'r') as f:
                for line_read in f.readlines():
                    try:
                        d = json.loads(line_read)
                        hour = datetime.datetime.fromtimestamp(float(d['time'])).strftime('%Y.%m.%d.%H')
                        if not hour in hours:
                            hours[hour] = {'rx': 0, 'tx': 0}
                        splits_rxtx = d['usage'].replace('(', '').split(')')
                        hours[hour]['rx'] += int(splits_rxtx[0])
                        hours[hour]['tx'] += int(splits_rxtx[1])
                    except:
                        pass
    except:
        pass

    return hours


# ====================================
def build_network_chart(environ):
    # ====================================

    # query = 'serial=10000000e3669edf'
    id = ''
    try:
        query = environ['QUERY_STRING']
        for item in query.split(','):
            if 'serial=' in item:
                id = item.split('=')[1]
    except:
        pass

    hours_data = get_hourly_network_data(id)

    if hours_data:
        hours = []
        rx = []
        tx = []
        for hour in sorted(hours_data):
            hours.append(hour)
            rx.append(str(float(hours_data[hour]['rx']) / 1024.0 / 1024.0))
            tx.append(str(float(hours_data[hour]['tx']) / 1024.0 / 1024.0))

        data_string = ''
        data_string += 'var x_values = ["' + '","'.join(hours) + '"];' + '\n'
        data_string += 'var y1_values = [' + ','.join(rx) + '];' + '\n'
        data_string += 'var y2_values = [' + ','.join(tx) + '];' + '\n'

        html = """
    <!DOCTYPE html>
    <html>

    <head>
      <title>Chart</title>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
    <style>
    body {font-family: Helvetica Neue, Arial, sans-serif; text-align: center;}
    .wrapper { max-width: 800px; margin: 50px auto;}
    h1 { font-weight: 200; font-size: 3em; margin: 0 0 0.1em 0;}
    h2 { font-weight: 200; font-size: 0.9em; margin: 0 0 50px; color: #999;}
    a { margin-top: 50px; display: block; color: #3e95cd;}
    </style>
    </head>

    <body>

    <div class="wrapper">
      <h1>Network utilization """ + id + """</h1>
      <h2>Megabytes per hour</h2>

    <canvas id="myChart" width="1600" height="900"></canvas>

    </div>


    <script>

    """

        html += data_string

        html += """
    var ctx = document.getElementById("myChart");
    var myChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: x_values,
        datasets: [
          {
            data: y1_values,
            label: "RX",
            borderColor: "#3e95cd",
            fill: false
          },
          {
            data: y2_values,
            label: "TX",
            borderColor: "#8e5ea2",
            fill: false
          }
        ]
      }
    });


    </script>



    </body>
    </html>

        """
    else:
        html = """
    <!DOCTYPE html>
    <html>

    <head>
      <title>Chart</title>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
    <style>
    body {font-family: Helvetica Neue, Arial, sans-serif; text-align: center;}
    .wrapper { max-width: 800px; margin: 50px auto;}
    h1 { font-weight: 200; font-size: 3em; margin: 0 0 0.1em 0;}
    h2 { font-weight: 200; font-size: 0.9em; margin: 0 0 50px; color: #999;}
    a { margin-top: 50px; display: block; color: #3e95cd;}
    </style>
    </head>

    <body>
    No data found
    </body>
    </html>
    """
    return html


# ====================================
def application(environ, start_response):
    # ====================================
    notes = ''

    try:
        # query = 'pi-m-10000000a499ee0d-**************-1908-M.0.9-missing-stale'
        query = environ['QUERY_STRING']
        chart = ''
        for item in query.split(','):
            if 'chart=' in item:
                chart = item.split('=')[1]

        html = "chart not found: " + chart
        if chart == 'network':
            html = build_network_chart(environ)

    except:
        html = "error"

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
