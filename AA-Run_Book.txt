Raspberry Pi

Introduction
The Raspberry Pi project main goal is to provide clients with a low cost computer, to provide a web based access to certain "applications".

This is accomplished by automatically starting up on a full screen web browser, and nothing else accessible to the user.

A simple list of bookmarks can be configured, that will allow the user to click into one of many pre-set links. There is also the option to have a bookmark automatically "click through" after a 25 second delay, to support the use case of sign boards, where no keyboard or mouse will be available.


Slicer
Slicer is a web application, to provide management of the Raspberry Pis.

The first purpose of Slicer is to provide visibility of all the devices that are registered. As of image 2.x, all of those devices check in directly. Some of the 1.4.8 image devices have been enhanced to check in also.

The second purpose of Slicer is to allow for setting of the usage Profile. This includes settings like Time zone and also a bookmark list (including allow-list).

Another purpose is to be able to view the versions of various services running on the end device, and to set upgrades or rollbacks to be performed on the device.

Some of Slicer's features are only available to logged in users, with specific permissions set; these permissions are stored in a 'datastore' that is trusted by <PERSON>lice<PERSON>. If the server that runs Slicer is powered off for longer than a given time, it initially starts as not trusted; this is to allow for a restore from an old backup, and restore of the datastore as a two step process. If a reboot happens, and there is no trust, then there is hard-coded permission for certain users to begin the trust (<PERSON> and Russ Lobuzzetta).

The Slicer application runs https only (port 443 only), and runs on a GCP virtual machine, under the project "mac-mgmt-pr-cah".

Using Slicer to configure a Pi:
https://slicer.cardinalhealth.net/download?filetodownload=SlicerConfigurationOfPi_720p.mov


Github Repository
All source code, and notes about how to setup all aspects of the Slicer and Raspberry pi project are in one Github repository.

Link to the Github project: https://github.com/CardinalHealth/cs_sp_pi_slicer
The project requires permissions to even see the repository. So far David Ferguson and Jeff Smith have admin access.

Security

Compliance Review

(working in Github to start the rest of this content, so that it can be version controlled. I will copy and paste it here when ready to publish. Source is /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/security_compliance_review.txt)

As of 2021.08.26, this is a review of the current state of the recommended security notes.

The input to this review comes from Andrew Beardsley, in an email on August 12, 2021, and is attached to Jira card EUDE-773. To goal of this section is to list out each requirement, and to provide a place for response/evidence that the requirement is either currently met, or is being worked on.

Security Controls / Requirements

1) "We must have controls in place that ensure the device itself is not stolen, see Global Security for physical security controls."

Response: This is an open item, and is anticipated to be addressed by providing a complete 'kit' as an order-able item.

2) "We must use a centralized management tool for device governance including, but not limited to: OS Management, Device Enrollment, Patching, Updates, Enforce Encryption, Inventory."

Response: The site https://slicer.cardinalhealth.net provides the central communication and control point for the configuration and update of the raspberry pis. Currently, the device enrollment is fully automatic. Encryption is enforced through the use of https traffic as the sole communications path. The patching and updates is the topic of an active backlog item EUDE-1328.

3) "We must maintain an inventory of these devices."

Response: ??? This is outside the scope of software

4) "We must be able to monitor these devices and their behavior on the network."

Response: The devices check in to the slicer central site every minute. Status is available there. A card EUDE-1329 will add monitoring of network behaviour.

5) "No Cardinal Health information is stored on the device in anyway."

Response: The operation of the raspberry pi uses the chromium browser, in incognito mode, which means that when the browser is closed/restarted the active information is not saved. There is no other source of viewing new material, no saving, no downloading, or any other means for Cardinal Health information to be added to the device.

6) "We need to ensure that the OS cannot be compromised. Theft prevention of any and all media that is installed on the device, i.e. SD card, etc. Prevent unauthorized OS modification."

Response: ??? This is outside the scope of software

7) "Machine level certificates must be leveraged in order to identify the device."

Response: Card EUDE-1331 shows email from Andrew Beardsley confirming that this requirement is out of date.

8) "Must ensure that controls are in place to ensure that devices cannot communicate with each other directly."

Response: In discussions with Jeff Paugh, he was under the understanding that devices on the IOT network would have network level controls to that would fulfill this requirement.

9) "Least Privilege access must be followed the Pi can access only what is needed to and nothing else (sic)"

Response: The chromium browser is in incognito mode, so there will be no history saved.
The traffic to and from the browser is run through a proxy (privoxy) that enforces an allowlist only set of sites.
The white list is managed on a use case basis, so each use case only has access to what that use case requires, and no more.

10) "No password local accounts that can be used to authenticate to the device."

Response: Card in the backlog EUDE:1333

11) "Leverage CIS OS Baselines / Workstation Hardening"

Response: The current build includes a pi_security service, which used "lynis" as a report of "hardening".
Card EUDE-1350 will work on further improving the hardening score.


Hardware Setup
There are two HDMI ports om the Model 4 Pi. If only one monitor is used, it must be plugged into the port closest to the power connection. This is the HDMI0 port.












