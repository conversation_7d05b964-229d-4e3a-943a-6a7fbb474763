Servers:
slicer01 = ***********
slicer03 = ************
slicer04 = ***********

If the server moves again, then the firewall will need to be updated for the ??? project (Qcam was going to be a dependency, but is not now)

Notes:

This was the original instance.
As of 2022.10.14, look to AA-gcp_rocky_server.txt

slicer01:
    2024.01.08
        hostnamectl
           Static hostname: lpec5009slicr01
                 Icon name: computer-vm
                   Chassis: vm
                Machine ID: 012a787168254cbcaa5f13dde54611bc
                   Boot ID: 6b72c8126ef94b4bbfb2d86e0de5c4a0
            Virtualization: kvm
          Operating System: CentOS Linux 7 (Core)
               CPE OS Name: cpe:/o:centos:centos:7
                    Kernel: Linux 3.10.0-1160.102.1.el7.x86_64
              Architecture: x86-64

old:
The setup step-by-step used to make the Slicer server instance on google compute

Starting point:

https://wiki.cardinalhealth.net/GCP_Create_VM_Instance

On your PC (tested on MAC), install the gcloud SDK, which includes the gcloud command line utility:

https://cloud.google.com/sdk/docs/install

Get authenticated into gcloud.

Get someone to add you to the required permissions group to get access to:
(Russ L. set this up for Dave F.)
mac-mgmt-pr
mac-mgmt-np
    That is:
        a-cloud-mac-mgmt-np-editor,
        a-cloud-mac-mgmt-np-owner,
        a-cloud-mac-mgmt-pr-editor,
        a-cloud-mac-mgmt-pr-owner,

# ================
Projects (Based on given permissions):
# ================
gcloud components update
gcloud init
gcloud auth login
gcloud auth list
gcloud <NAME_EMAIL>
gcloud auth login


gcloud config set project mac-mgmt-np-cah
gcloud projects list

cah-host-nonprod  cah-host-nonprod  987823003607
cah-host-prod     cah-host-prod     527853414833
cims-np-cah       cims-np           776122696896
mac-mgmt-np-cah   mac-mgmt-np       979866627918
mac-mgmt-pr-cah   mac-mgmt-pr       92183180931
management-cah    management        372449746971

slicer-pr-cah                   slicer-pr                       361361986442

# ================
# Production
# ================
gcloud config set project mac-mgmt-pr-cah
gcloud deployment-manager deployments list

NAME            LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS
jamf-pro-prod   update               DONE                 manifest-1584487098797  []
tech-help-prod  insert               DONE                 manifest-1578490211746  []

gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip

When that log in eventually does not work, due to an expiration, then use a new name, like:

gcloud compute ssh david.ferguson2@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip


# ================
# Not-Production
# ================
gcloud config set project mac-mgmt-np-cah
gcloud deployment-manager deployments list

NAME               LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS
graylog-dev        update               DONE                 manifest-1551531702436  []
jamf-pro-dev       insert               DONE                 manifest-1552401100556  []
jamf-pro-frontend  insert               DONE                 manifest-1551992656221  []
kolide-fleet-dev   insert               DONE                 manifest-1550851334781  []
ldec5009slicr01    insert               DONE                                         [MANIFEST_EXPANSION_USER_ERROR]
puppet-master-dev  insert               DONE                 manifest-1571256331515  []
resillio-dev       insert               DONE                 manifest-1556562646161  []


gcloud deployment-manager deployments describe my-deployment

# ================
# Web view of deployments
# ================
https://cloud.google.com/deployment-manager/docs/quickstart

clicked link that says "Go to Deployment Manager":
https://console.cloud.google.com/dm/deployments?project=_&_ga=2.********.*********.**********-*********.**********

# ================
# New production VM
# ================
In a terminal window on the PC (only do this to create a new instance):

https://cloud.google.com/deployment-manager/docs/quickstart

# Point to the correct project
gcloud config set project mac-mgmt-pr-cah

Make a name for the instance:
https://wiki.cardinalhealth.net/GCP_Naming_Standards/Hostnames

lpec5009slicr01

# Manage Service Accounts (this is how VMs get their permissions managed)
https://cloud.google.com/iam/docs/creating-managing-service-accounts

gcloud iam service-accounts list

gcloud iam service-accounts create pi-mgmt-pr-slicer-main \
    --description="pi-mgmt-pr-slicer-main" \
    --display-name="pi-mgmt-pr-slicer-main"

# Which makes: <EMAIL>

# Create with yaml
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create lpec5009slicr01 --config config_lpec5009slicr01.yaml --project mac-mgmt-pr-cah

At some point, edit the boot disk in the gcp consolem and add the tag
"snapshots" with a value of "h0_d10-2", to enable hourly snapshots of the disk.

# May be needed: get added to the "A-MacSysAdmins" as a member (Russ L. owns it, Matt W. asked for it for David F.)

# If the "Remote into it via SSH" in below here fails, then toggle the oslogin on then off, like:
gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=FALSE

# Remote into it via SSH
gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip


# Get my instance ip address
ip addr | fgrep inet

***********

# --------------------------------------------
# Install to support ldap login
# pip install flask-ldap3-login
sudo yum install -y python-ldap

# --------------------------------------------
sudo yum install -y nmap

# --------------------------------------------
sudo yum install -y pytz

# --------------------------------------------
sudo yum install -y numpy scipy

# --------------------------------------------
sudo yum install -y python-tornado

# --------------------------------------------
# Install libraries
sudo yum install -y python-requests

# --------------------------------------------
# 2023.09.05 new ldap method
sudo yum install openldap-clients

sudo chown -R apache:apache /var/www/slicer
sudo chown -R apache:apache /var/www/slicer/login

# --------------------------------------------
# Security Issue: PolicyKit, Polkit vulnerability (PwnKit) (CVE-2021-4034-(PWNKIT)
# --------------------------------------------
# 2022.01.16
https://www.linuxquestions.org/questions/linux-newbie-8/what%60s-all-that-rwsr-xr-x-387068/
https://apple.news/AjkpIn6MzQJKdp_KidQmBtA
ls -l /usr/bin/pkexec
-rwsr-xr-x. 1 root root 23576 Apr  1  2020 /usr/bin/pkexec

# "fixed it" by disabling the privilege escalation
sudo chmod 0755 /usr/bin/pkexec
ls -l /usr/bin/pkexec
-rwxr-xr-x. 1 <USER> <GROUP> 23576 Apr  1  2020 /usr/bin/pkexec


# --------------------------------------------
# Install apache
# https://www.digitalocean.com/community/tutorials/how-to-install-the-apache-web-server-on-centos-7
sudo yum update httpd
sudo yum install httpd

sudo systemctl enable httpd
sudo systemctl start httpd
sudo systemctl status httpd

cat /etc/httpd/conf.d/welcome.conf

sudo su
echo "Slicer Server" > /var/www/html/index.html
chmod 644 /var/www/html/index.html
ls -l /var/www/html/index.html
exit

browse to:
http://***********
https://***********

Load up our slicer_wsgi_* modules.

(The following DirectoryIndex entry will use "index index.html" to first look to the index wsgi page,
and if not found, look to the index.html page)
sudo vi /etc/httpd/conf/httpd.conf
######################################################
######### make this section look like this #########
######################################################
#
# DirectoryIndex: sets the file that Apache will serve if a directory
# is requested.
#
<IfModule dir_module>
    DirectoryIndex index index.html
</IfModule>
######################################################

sudo systemctl restart httpd


# -----------------------------------------
# Self signed TLS cert for initial config:
https://www.linode.com/docs/guides/create-a-self-signed-tls-certificate/
https://unix.stackexchange.com/questions/451207/how-to-trust-self-signed-certificate-in-curl-command-line

sudo su
cd ~
mkdir /root/certs && cd /root/certs
openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out slicer.crt -keyout slicer.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1"

Answers:
US
Ohio
Dublin
CardinalHealth
ClientEngineering
slicer.cardinalhealth.net
<EMAIL>

sudo yum install gnutls-utils

certtool -i < slicer.crt

certtool -p --outfile localhost.key
certtool -s --load-privkey localhost.key --outfile localhost.crt

Answers:
slicer.cardinalhealth.net
CardinalHealth
ClientEngineering
CardinalHealth
Dublin
Ohio
US
cardinalhealth
net
(empty crlf)
<EMAIL>
(empty crlf, to take the default)
365
y
(empty crlf, to take the default)
y
y
y
slicer.cardinalhealth.net
slicer.cardinalhealth.net
***********
n
n
n
n
N
N
N
(empty crlf, to take the default)
y

cp localhost.crt /etc/pki/tls/private/slicer.cert
cp localhost.key /etc/pki/tls/private/slicer.key


#cp slicer.crt /etc/pki/tls/private/slicer.cert
#cp slicer.key /etc/pki/tls/private/slicer.key

exit

# -----------------------------------------
# Configure Apache
https://www.namecheap.com/support/knowledgebase/article.aspx/9821/38/apache-redirect-to-https/
https://httpd.apache.org/docs/2.4/mod/mod_ssl.html
https://httpd.apache.org/docs/2.4/ssl/ssl_howto.html

httpd -S

sudo yum install mod_ssl

cat /etc/httpd/conf/httpd.conf
(the last line points to loading all in /etc/httpd/conf.d)

sudo vi /etc/httpd/conf.d/slicer.conf

----- start copy -----
LoadModule ssl_module /usr/lib64/httpd/modules/mod_ssl.so

<VirtualHost *:80>
ServerName slicer.cardinalhealth.net
Redirect permanent / https://slicer.cardinalhealth.net/
</VirtualHost>

<VirtualHost _default_:443>
ServerName slicer.cardinalhealth.net
DocumentRoot /var/www/htmlfiles
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/slicer_cabundle.pem"
</VirtualHost>
----- end copy -----

sudo chmod 644 /etc/httpd/conf.d/slicer.conf

sudo mkdir /var/www/htmlfiles
sudo chown -R apache:apache /var/www/htmlfiles

# check syntax
/usr/sbin/httpd -t

sudo service httpd restart

# check failures:
systemctl status httpd.service
journalctl -xe

# test connection from another computer
curl http://slicer.cardinalhealth.net
curl https://slicer.cardinalhealth.net

# if cert is self signed:
curl -k https://slicer.cardinalhealth.net



# --------------------------------------------
DNS:

nslookup ***********

responds with:
** server can't find ***********.in-addr.arpa.: NXDOMAIN

nslookup slicer.cardinalhealth.net

responds with:
Server:		************
Address:	************#53

** server can't find slicer.cardinalhealth.net: NXDOMAIN

# Fill out Service Now form:
https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=9e9f167cdbf0e344be676165ca961963

# with guidance from:
https://collab.cardinalhealth.net/sites/ADForms/Shared%20Documents/DNS_Guide-ServiceNow.pdf

What: Add
Type: A Record
ASAP: Yes
Host: slicer
Domain: cardinalhealth.net
FQDN: slicer.cardinalhealth.net
IP: ***********
: Internal

https://cardinal.service-now.com/gith?id=sc_request&is_new_order=true&table=sc_request&sys_id=5568567c1b9724508562da03b24bcbb9

# --------------------------------------------
Email Group (to be used for the Cert):

https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=a4eb7b5165bfd0007f8e897709ddc533
(you can search for Email Group Mailbox to find it if the link is not working)

Requestor's Enterprise ID: david.ferguson
What: Create new
name: GMB-EIT-RaspberryPi
owner: David Ferguson
people: David Ferguson, Russell Lobuzzetta, mailto:<EMAIL>

To add the account to your Outlook:
On Mac:
https://www.canr.msu.edu/news/adding-a-shared-mailbox-in-outlook-for-mac
On Windows:
https://support.microsoft.com/en-us/office/open-a-shared-mail-calendar-or-people-folder-in-outlook-for-mac-6ecc39c5-5577-4a1d-b18c-bbdc92972cb2



# --------------------------------------------
Cert:

https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=8f306fc72bf235000a05533219da153c

Project: Standard
Name: https://slicer.cardinalhealth.net
Server Type: Unix
Hosting Server: GCP lpec5009slicr01 in project mac-mgmt-pr-cah
IP: ***********
Audience: Internal
Environment: Production
Application: Slicer
Owner: Russell Lobuzzetta
Group mailbox: GMB-EIT-RaspberryPi
Port: 443

# when cert is issued:
https://www.ssls.com/knowledgebase/how-to-install-an-ssl-certificate-on-apache/


Cert arrived as a compressed file "slicercardinalhealthnet.7z", with a password sent in a separate email.
Install on Mac, the app "The Unarchiver" from the app store (free).
Install on Mac, the app "Extractor - Unarchive Files" from the app store (free).
(one of them worked, I forget which; once I got a clean file and password)
In terminal on mac, go to where the pfx file is unzipped:
cd /Users/<USER>/Documents/slicerCert
xxd slicercardinalhealthnet.pfx
(prints text. copy all in terminal, paste into a text document, clean up the start and end to remove prompt lines)

on slicer server:

sudo su
mkdir /etc/pki/tls/private/20210404
cd /etc/pki/tls/private/20210404
vi slicercardinalhealthnet.txt
(insert the cleaned up content)
cat slicercardinalhealthnet.txt | xxd -r >slicercardinalhealthnet.pfx

# make cert and key files from pfx
# https://stackoverflow.com/questions/8774574/how-can-i-convert-a-pfx-certificate-file-for-use-with-apache-on-a-linux-server
# https://www.jasonheckman.com/technology/converting-a-pfx-certificate-bag-to-use-with-apache-ssl/

openssl pkcs12 -info -in slicercardinalhealthnet.pfx


openssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert
(use the password that was supplied in the email for this cert)

openssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes  -out slicer.key
(use the password that was supplied in the email for this cert)

openssl rsa -in slicer.key -outform PEM -out slicer_pem.key
openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem
(use the password that was supplied in the email for this cert)

openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys  -nodes -out domain-ca.crt
(use the password that was supplied in the email for this cert)


cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt
cp slicer.cert /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
cp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem

chmod 600 /etc/pki/tls/private/slicer.cert
chmod 600 /etc/pki/tls/private/slicer.key
chmod 600 /etc/pki/tls/private/domain-cabundle.pem

openssl verify /etc/pki/tls/private/slicer.cert

gets:
/etc/pki/tls/private/slicer.cert: C = US, ST = Ohio, L = Dublin, O = Cardinal Health, OU = EIT, CN = slicer.cardinalhealth.net
error 20 at 0 depth lookup:unable to get local issuer certificate

# restart apache
service httpd restart
exit

Check valid dates: (Does not immediately return)
openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2>/dev/null | openssl x509 -noout -dates

notBefore=Apr  5 15:37:06 2021 GMT
notAfter=Apr  5 15:37:06 2022 GMT


Open primary site in Chrome to see that there are no complaints with the certificate:
https://slicer.cardinalhealth.net

ok.

From a pi:
curl https://slicer.cardinalhealth.net

fails:
curl: (60) SSL certificate problem: unable to get local issuer certificate
More details here: https://curl.haxx.se/docs/sslcerts.html

curl failed to verify the legitimacy of the server and therefore could not
establish a secure connection to it. To learn more about this situation and
how to fix it, please visit the web page mentioned above.

From pi:
openssl s_client -connect slicer.cardinalhealth.net:443

Need to add root trust cert for Cardinal?
https://raspberrypi.stackexchange.com/questions/76419/entrusted-certificates-installation

# !!!!!!!!!!!!!!!!!!!!!!!!
# https://askubuntu.com/questions/645818/how-to-install-certificates-for-command-line
# on pi, to add trusted cert
# put the cert in
/usr/local/share/ca-certificates/

sudo rm /etc/ssl/certs/ca-certificates.crt

sudo apt-get update && sudo apt-get install -y --reinstall ca-certificates

#sudo dpkg-reconfigure ca-certificates

# and re-run
sudo update-ca-certificates

# !!!!!!!!!!!!!!!!!!!!!!!!



# --------------------------------------------

cat /etc/httpd/conf/httpd.conf
cp

# --------------------------------------------
# install python3 and any needed libraries

sudo yum update -y
sudo yum install -y python3
python3 --version

sudo yum install traceroute
sudo yum install sshpass


# --------------------------------------------
# can not see it with the internal address (from any place other than the server itself)
ping lpec5009slicr01.c.mac-mgmt-pr-cah.internal

# Firewall rules for GCP instances
https://wiki.cardinalhealth.net/GCP_Naming_Standards/FirewallRules
gcloud config set project mac-mgmt-pr-cah
gcloud deployment-manager deployments list
gcloud deployment-manager deployments describe lpec5009slicr01

gcloud compute firewall-rules list --project mac-mgmt-pr-cah --format="table(selfLink)"
(empty)

# network tags that provide rules:
https://wiki.cardinalhealth.net/GCP_Create_VM_Instance
remove the rule called 'edge' also 'glb' also 'internal-smb', save and reboot
(updated the yaml, to be clean for these tags)

# python on Apache:
https://www.howtoforge.com/tutorial/python-apache-mod_wsgi_ubuntu/

sudo yum install -y apache2-utils ssl-cert libapache2-mod-wsgi-py3

sudo vi /var/www/html/wsgy.py

----- start copy -----
import json

def application(environ,start_response):
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n' \
           '<div style="width: 100%; font-size: 40px; font-weight: bold; text-align: center;">\n' \
           'Welcome to mod_wsgi Test Page\n' \
           '</div>\n' \
           '</body>\n' \
           '</html>\n'
    response_header = [('Content-type','text/html')]
    start_response(status,response_header)
    return str(environ['QUERY_STRING'])
    return [html.encode()]
----- end copy -----

sudo chown apache:apache /var/www/html/wsgy.py

vi /etc/apache2/conf-available/wsgi.conf

sudo vi /etc/httpd/conf.d/python-wsgi.conf
----- start copy -----
WSGIScriptAlias /wsgi /var/www/html/wsgy.py
----- end copy -----

sudo systemctl restart httpd

# test
https://slicer.cardinalhealth.net/wsgi

https://slicer.cardinalhealth.net/wsgi?parametersGoHere


# --------------------------------------
# A receiver for monitor check-ins

(see the file slicer_wsgi_checkin.py)



# ================
# Connection to persistent disk in GCP VM
# ================
https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

(disc created in cloud console)

[david.ferguson2@lpec5009slicr01 html]$ lsblk
NAME   MAJ:MIN RM  SIZE RO TYPE MOUNTPOINT
sda      8:0    0   30G  0 disk
├─sda1   8:1    0  200M  0 part /boot/efi
└─sda2   8:2    0 29.8G  0 part /
sdb      8:16   0   60G  0 disk

sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb
y

sudo mkdir -p /mnt/disks/SSD
sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD
sudo chmod a+w /mnt/disks/SSD

sudo cp /etc/fstab /etc/fstab.backup
sudo blkid /dev/sdb
/dev/sdb: UUID="736ba877-71b1-43c5-93e3-e7676892e65b" TYPE="ext4"

sudo vi /etc/fstab
UUID=736ba877-71b1-43c5-93e3-e7676892e65b /mnt/disks/SSD ext4 discard,defaults,nofail 0 2


sudo chmod 755 /mnt
sudo chmod 755 /mnt/disks

sudo chown -R apache:apache /mnt/disks/SSD
sudo chmod 777 /mnt/disks/SSD

sudo mkdir /mnt/disks/SSD/var
sudo chown -R apache:apache /mnt/disks/SSD/var

# ================
# Set root password to never expire
# ================
sudo chage -M -1 root

sudo chage -M -1 david.ferguson



# ================
# data view and delete NCDU
# ================

https://unix.stackexchange.com/questions/3979/how-can-i-install-ncdu-on-red-hat

on mac:
https://dev.yorhel.nl/ncdu
download the gz
xxd ~/Downloads/ncdu-1.16.tar.gz > ncdu-1.16.tar.gz.xxd
cat ncdu-1.16.tar.gz.xxd
(Copy from terminal, paste into text editor, trim off extra lines)

On Slicer server:
mkdir /home/<USER>/install_ncdu
cd /home/<USER>/install_ncdu
vi ncdu-1.16.tar.gz.xxd
(Paste contents of the ncdu-1.16.tar.gz.xxd)
cat ncdu-1.16.tar.gz.xxd | xxd -r >ncdu-1.16.tar.gz
tar -xzvf ncdu-1.16.tar.gz
cd ncdu-1.16
sudo yum install -y gcc
./configure --prefix=/usr
make
sudo make install

use:
sudo ncdu /

sudo ncdu /var/log/slicer


sudo yum clean all
sudo rm -rf /var/cache/yum

# log is 14.4 GB on 2021.09.27
sudo rm /var/log/httpd/access_log

# Logs are supposed to rotate, but had not since June 2021.
https://electrictoolbox.com/changing-apache-log-rotation-behaviour/

sudo vi /etc/logrotate.conf
(Change to only 7 days of logs)
### old ###
# rotate log files weekly
weekly

# keep 4 weeks worth of backlogs
rotate 4
### new ###
# rotate log files daily
daily

# keep 7 days worth of backlogs
rotate 7
### end ###

sudo logrotate --force /etc/logrotate.conf


sudo cat /var/log/cron

Shows that Jun6 was the last clean run, which coincides with my login failing, and having to create a new
persona to log into gcloud to Slicer.

sudo chage -l root
sudo passwd root

[david.ferguson2@lpec5009slicr01 ~]$ sudo chage -l root
Last password change					: Mar 11, 2021
Password expires					: Jun 09, 2021
Password inactive					: never
Account expires						: never
Minimum number of days between password change		: 0
Maximum number of days between password change		: 90
Number of days of warning before password expires	: 7

jamf is set up differently:
[david.ferguson2@lpec5008jamfa01 ~]$ sudo chage -l root
Last password change					: Sep 20, 2021
Password expires					: never
Password inactive					: never
Account expires						: never
Minimum number of days between password change		: 0
Maximum number of days between password change		: -1
Number of days of warning before password expires	: 7


# ================
# monitoring
# ================
2022.02.08
NewRelic

Hello Project Owners,
Attached is a list of production cloud instances which are not currently being monitored by New Relic. Cardinal standard is that all production instances be monitored by New Relic. To bring these instances into compliance we are submitting a change control to push the New Relic client to these instances on 2/19. This should be transparent to the instance and does not require a reboot unless the instance is running 32-bit windows OS in which case the instance will be rebooted.
If the timing of this activity is in conflict with other activities planned for the instance you may use the self installation tools located here https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts. If there are any issues with the installation please submit a request to EITSS-MONITORING.
If there is a technical reason where New Relic cannot be installed on this instance please reach out to the Infrastructure Security and Governance team to document this issue in as an Archer item. Any other questions can be sent directly to me.
Thank you for your compliance.
-Rich Robinson
Sr. Engineer, Infrastructure Security and Governance

License key
mac-mgmt-pr-cah
8919c2c426b3b41c45e5bb2f0b429d8bb4beNRAL

installed with information from:
https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts

view at:
https://one.newrelic.com/nr1-core?account=3414131&state=86ac0ddb-8f36-c143-731d-abe2c824da73
david.ferguson
Nrbud+1!

# ================
# Notes
# ================

# ================
# Connection to bucket storage
# ================
(on the slicer server instance)
gcloud init

choose the account "<EMAIL>"


https://cloud.google.com/dataprep/docs/concepts/gcs-buckets?hl=en_US&_ga=2.*********.-**********.**********




# ================
# Firestore set up
# ================

You must have these permissions in GCP to see the interface in GCP:

appengine.applications.get
datastore.entities.get
datastore.entities.list










































# ================
# Things that do not work yet
# ================

# One liner create fails, but gives us useful information
As of 2021.03.26, this gets us a small CentOS7 server, in np (non-production):

# Point to the correct project
gcloud config set project mac-mgmt-np-cah

# Create the VM with command line (no yaml):
gcloud deployment-manager deployments create ldec5009slicr01 --composite-type management-cah/composite:vm --properties name:ldec5009slicr01,region:'us-central1' --project mac-mgmt-np-cah

# This creates it, but not in a way that can be ssh into, but does show us the service account in the GUI
<EMAIL>

# Check details
gcloud deployment-manager deployments describe ldec5009slicr01


!!!!
To remove it:
!!!!
Commented out for safety:
# gcloud deployment-manager deployments delete ldec5009slicr01

# gcloud deployment-manager deployments delete lpec5009slicr01
!!!!


# sudo access
?? Request permission from ServiceNow

Service account that is on the Chef production server: (Where dave does have sudo)
<EMAIL>

Has david.ferguson ssh key in the GCP gui panel
sudo works for free


"Using OS Login user [david_ferguson_cardinalhealth_co] instead of requested user [david.ferguson]"

Edit VM, enter ssh key (copy and paste from prod):
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDVkewljS/EuebHOO7fTjX3TSsdjYPqxBnEhzjXxDiNw3PNXRNnaSc8h4KcdUR+QUNjlVhSHbOmJzMkFPjSy1XOADi9ax+DsG64NlKcTOvS7NeNFBw1f3UqV58lQTH7xQhL7TcGhJUhopCRt/O54GfUD4qiku8Be9D+XluyJsMBNaTJX2dmnseSp+8GKq2JUQn/jBv13Of43lyUpDTxmzKq4yyOANwKyWDcwoQ3zu3f57qkTij5tTQm9OCSq+oBXYC4iMnWMwtnltY2mhZda0BTGu/8wmf+TRDQAr6F9Env7HPzhRow6inU6qUoEch/orIOyvITFv0PA5C2TWEjG7+cyPPpilVrJ67+kDx7bEu2/LrEhlUH6myj58K4Jg7yCFR7lzBe/C9E0OqinLoeqfHowBDvImAtDx3QXwVBorMPe0PaKuwAVuOY8aXmxUh+S9cSbkIPOxVCraCyjmIyIhAdkkrPbmMn6/ewmHM4xJzoCr2YctXj5mgISRkMEbSc+l8= <EMAIL>

permissions:
https://cloud.google.com/compute/docs/instances/managing-instance-access

python on Apache:
https://www.unixmen.com/how-to-set-up-python-scripting-for-apache/
https://stackoverflow.com/questions/26829387/easiest-way-to-run-python-script-from-apache

websockets in javascript:
https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_client_applications


# --------------------------------------
Firewall and iptables notes (not used):
sudo firewall-cmd --list-ports

sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --remove-service=http
sudo firewall-cmd --permanent --remove-port=80/tcp

sudo iptables -L
sudo iptables -A INPUT -p tcp --destination-port 80 -j DROP

sudo firewall-cmd --reload



# --------------------------------------
Apache

https://www.namecheap.com/support/knowledgebase/article.aspx/9821/38/apache-redirect-to-https/https://www.guru99.com/apache.html
??? not used:
vi /etc/httpd/conf/httpd.conf

(add to end)
ServerSignature Off
ServerTokens Prod


# --------------------------------------

sudo yum install mod_wsgi
sudo systemctl restart httpd
sudo httpd -M | grep wsgi












# --------------------------------------
Old development server notes

# ================
# New development VM
# ================
In a terminal window on the PC (only do this to create a new instance):

https://cloud.google.com/deployment-manager/docs/quickstart

# Point to the correct project
gcloud config set project mac-mgmt-np-cah

Make a name for the instance:
https://wiki.cardinalhealth.net/GCP_Naming_Standards/Hostnames

ldec5009slicr01

# Manage Service Accounts (this is how VMs get their permissions managed)
https://cloud.google.com/iam/docs/creating-managing-service-accounts

gcloud iam service-accounts list

gcloud iam service-accounts create pi-mgmt-np-slicer-master \
    --description="pi-mgmt-np-slicer-master" \
    --display-name="pi-mgmt-np-slicer-master"

# Which makes: <EMAIL>

# Create with yaml
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create ldec5009slicr01 --config ldec5009slicr01.yaml --project mac-mgmt-np-cah

# May be needed: get added to the "A-MacSysAdmins" as a member (Russ L. owns it, Matt W. asked for it for David F.)

# If the remote in below here fails, then toggle the oslogin on then off, like:
gcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=FALSE

# Remote into it via SSH
gcloud compute ssh david.ferguson@ldec5009slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip

# --------------------------------------------
# Install apache
# https://www.digitalocean.com/community/tutorials/how-to-install-the-apache-web-server-on-centos-7
sudo yum update httpd
sudo yum install httpd

sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload

sudo systemctl start httpd
sudo systemctl status httpd

cat /etc/httpd/conf.d/welcome.conf

sudo su
echo "empty" > /var/www/html/index.html
exit

# --------------------------------------------
cert:
https://www.digitalocean.com/community/tutorials/how-to-create-an-ssl-certificate-on-apache-for-centos-7
https://www.thesslstore.com/knowledgebase/ssl-install/apache-openssl-installation/
https://www.thesslstore.com/knowledgebase/ssl-generate/
https://www.getpagespeed.com/server-setup/ssl-directory

openssl req -new -newkey rsa:2048 -nodes -keyout server.key -out server.csr

Answers:
US
Ohio
Dublin
CardinalHealth
ClientEngineering
dslicer.cardinalhealth.net
<EMAIL>
thisisslicer
(empty)

cat server.csr

# send off the CSR to AD team (never happened):
https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=8f306fc72bf235000a05533219da153c


sudo cp server.key /etc/pki/tls/private/dslicer.cardinalhealth.net
sudo chown root:root /etc/pki/tls/private/dslicer.cardinalhealth.net
sudo chmod 0600 /etc/pki/tls/private/dslicer.cardinalhealth.net


cat /etc/httpd/conf/httpd.conf

# --------------------------------------------
# Should be able to reach at internal DNS:
ping ldec5009slicr01.c.mac-mgmt-np-cah.internal
# but it does not work out of the box

Current address in GCP is ************

nslookup ************

Already in use, by ldec5009c1ter13.cardinalhealth.net


# To get DNS entry added:
https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=9e9f167cdbf0e344be676165ca961963


# --------------------------------------------
# install python3 and any needed libraries

sudo yum update -y
sudo yum install -y python3
python3 --version


# --------------------------------------
# Flask to run python
https://medium.com/@thishantha17/build-a-simple-python-rest-api-with-apache2-gunicorn-and-flask-on-ubuntu-18-04-c9d47639139b
https://dev.to/sm0ke/flask-deploy-with-apache-on-centos-minimal-setup-2kb7
https://flask.palletsprojects.com/en/1.1.x/deploying/mod_wsgi/
https://mitchjacksontech.github.io/How-To-Flask-Python-Centos7-Apache-uWSGI/
https://www.liquidweb.com/kb/how-to-setup-a-python-virtual-environment-on-centos/

#sudo yum install epel-release
#sudo pip3 uninstall virtualenv
#sudo pip3 install virtualenv

sudo pip3 install --upgrade pip
sudo pip3 install virtualenv flask

# !!!!!!!!!!!!!!!!!
# hitme example:
# https://dev.to/sm0ke/flask-deploy-with-apache-on-centos-minimal-setup-2kb7

cd /var/www
sudo mkdir hitme

sudo vi /var/www/hitme/run.py

----- start copy -----
import os
from app import app

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
----- end copy -----

sudo mkdir /var/www/hitme/app
sudo vi /var/www/hitme/app/__init__.py

----- start copy -----
from flask import Flask
app = Flask(__name__)

@app.route("/")
def hello():
    return "Hello world!"
----- end copy -----

cd /var/www
sudo python3 -m venv hitme

cd /var/www/hitme
source bin/activate
pip install flask

export FLASK_APP=run.py
flask run

# !!!!!!!!!!!!!!!!!

# notes only after here
sudo virtualenv --python=python3 hitme # the venv is created inside the app folder
sudo cd /var/www/hitme
source bin/activate
pip install --upgrade pip
pip freeze
pip install -U pip requests
pip freeze
deactivate

sudo vi /var/www/hitme/wsgi.py
----- start copy -----
#!/usr/bin/env python

import sys
import site

site.addsitedir('/var/www/hitme/lib/python3.6/site-packages')

sys.path.insert(0, '/var/www/hitme')

from app import app as application
----- end copy -----


sudo vi /etc/httpd/conf.d/flask-hitme.conf

----- start copy -----
<VirtualHost _default_:443>

     WSGIDaemonProcess hitme user=apache group=apache threads=2
     WSGIScriptAlias /hitme /var/www/hitme/wsgi.py
     <Directory /var/www/hitme>
         Require all granted
     </Directory>
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/domain-cabundle.pem"
</VirtualHost>

----- end copy -----

sudo systemctl restart httpd


# --------------------------------------
Extra dev server, with 5008:

# Create with yaml
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create ldec5008slicr01 --config ldec5008slicr01.yaml --project mac-mgmt-np-cah

# May be needed: get added to the "A-MacSysAdmins" as a member (Russ L. owns it, Matt W. asked for it for David F.)

# If the remote in below here fails, then toggle the oslogin on then off, like:
gcloud config set project mac-mgmt-np-cah
gcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=FALSE

# Remote into it via SSH
gcloud compute ssh david.ferguson@ldec5008slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip

# --------------------------------------
known hosts:

/root/.ssh/known_hosts

# --------------------------------------
To make a user login attempt become the user setting:

cp /var/log/slicer/login/throw/david.ferguson /var/log/slicer/login/keep/david.ferguson
sudo chown apache:apache /var/log/slicer/login/keep/david.ferguson


# --------------------------------------
Tornado (Server side)
https://itekblog.com/centos-tornado-installation/

https://www.tornadoweb.org/en/stable/wsgi.html

http://blog.zaremba.ch/2013/01/25/tornado___the_best_web_framework.html

https://github.com/tornadoweb/tornado/blob/master/tornado/ioloop.py#L52

https://www.velotio.com/engineering-blog/python-tornado-guide

https://codeahoy.com/compare/flask-vs-tornado



_ = """
This file gets loaded to:
/var/www/html/tornado_server.py

using:
sudo vi /var/www/html/tornado_server.py

Run with:
sudo python /var/www/html/tornado_server.py

"""
import tornado.ioloop
import tornado.web
class MainHandler(tornado.web.RequestHandler):
   def get(self):
      self.write("Hello, world")

application = tornado.web.Application([
   (r"/", MainHandler),
])

if __name__ == "__main__":
   application.listen(8888)
   tornado.ioloop.IOLoop.instance().start()

# --------------------------------------
2023.01.25
After updating to Mac os Ventura 13.2, ssh breaks.

https://apple.stackexchange.com/questions/450392/ssh-issue-after-updating-mac-os-13

~/.ssh/config

Host *
PubkeyAcceptedKeyTypes=+ssh-rsa


# --------------------------------------
CHG0321708

Step-by-step

Cert arrived as a compressed file "slicercardinalhealthnet.7z", with a password sent in a separate email.
Install on Mac, the app "Extractor - Unarchive Files" from the app store (free).
In terminal on mac, go to where the pfx file is unzipped:
cd /Users/<USER>/Downloads/20240112
xxd slicercardinalhealthnet.pfx
(prints text. copy all in terminal, paste into a text document, clean up the start and end to remove prompt lines)

on slicer server:

sudo su
mkdir /etc/pki/tls/private/20240112
cd /etc/pki/tls/private/20240112
vi slicercardinalhealthnet.txt
(insert the cleaned up content)
cat slicercardinalhealthnet.txt | xxd -r >slicercardinalhealthnet.pfx

# make cert and key files from pfx
# https://stackoverflow.com/questions/8774574/how-can-i-convert-a-pfx-certificate-file-for-use-with-apache-on-a-linux-server
# https://www.jasonheckman.com/technology/converting-a-pfx-certificate-bag-to-use-with-apache-ssl/

openssl pkcs12 -info -in slicercardinalhealthnet.pfx

P6JRmMdGjGPGDFnJ

openssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert
(use the password that was supplied in the email for this cert)

openssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes  -out slicer.key
(use the password that was supplied in the email for this cert)

openssl rsa -in slicer.key -outform PEM -out slicer_pem.key
openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem
(use the password that was supplied in the email for this cert)

openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys  -nodes -out domain-ca.crt
(use the password that was supplied in the email for this cert)


cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt
cp slicer.cert /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
cp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem

chmod 600 /etc/pki/tls/private/slicer.cert
chmod 600 /etc/pki/tls/private/slicer.key
chmod 600 /etc/pki/tls/private/domain-cabundle.pem

openssl verify /etc/pki/tls/private/slicer.cert

gets:
/etc/pki/tls/private/slicer.cert: C = US, ST = Ohio, L = Dublin, O = Cardinal Health, OU = EIT, CN = slicer.cardinalhealth.net
error 20 at 0 depth lookup:unable to get local issuer certificate

# restart apache
service httpd restart
exit

Check valid dates: (Does not immediately return)
openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2>/dev/null | openssl x509 -noout -dates

notBefore=Dec 12 01:34:32 2023 GMT
notAfter=Dec 11 01:34:32 2024 GMT

Open primary site in Chrome to see that there are no complaints with the certificate:
https://slicer.cardinalhealth.net

ok.


# --------------------------------------
# --------------------------------------
# --------------------------------------
# --------------------------------------
# --------------------------------------
# --------------------------------------
# --------------------------------------
