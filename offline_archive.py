_ = """

Archive:
cd /Users/<USER>/Documents/GIT/
zip -r cs_sp_pi_slicer_20230303_074700.zip cs_sp_pi_slicer -x "*.git*" -x "*.DS_Store"

zip -r test.zip cs_sp_pi_slicer -x "*.git*" -x "*.DS_Store"

OR

cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/
python3 offline_archive.py


Test:
python3 -m unittest offline_archive


"""

import datetime
import os
import shlex
import subprocess
import time
import unittest


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# -------------------------------
def get_zip_command_line(datetime_now):
    # -------------------------------
    return_value = ''

    date_string = datetime_now.strftime('%Y%m%d%H%M%S')

    return_value = 'zip -r cs_sp_pi_slicer-' + date_string[0:8] + '_' + date_string[
                                                                        8:8 + 6] + '.zip cs_sp_pi_slicer -x "*.git*" -x "*.DS_Store"'

    return return_value


# -------------------------------
def main():
    # -------------------------------

    content = '#!/bin/sh' + '\n'
    content += 'cd ..' + '\n'
    content += get_zip_command_line(datetime.datetime.now()) + '\n'

    output_file = "tempscript_archive"
    with open(output_file, 'w') as f:
        f.write(content)

    pass_string, fails = do_one_command('chmod +x ' + output_file)
    print(pass_string, fails)

    pass_string, fails = do_one_command('./' + output_file)
    print(pass_string, fails)

    os.remove(output_file)


if __name__ == '__main__':
    main()

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_get_date_formatted(self):
        """
        (fill in here)
        """
        expected = '20230303081332'
        datetime_now = datetime.datetime.strptime(expected, '%Y%m%d%H%M%S')
        # datetime_now = datetime.datetime.now()
        actual = datetime_now.strftime('%Y%m%d%H%M%S')
        self.assertEqual(expected, actual)

    def test_get_zip_command_line(self):
        datetime_now = datetime.datetime.strptime('20230303081332', '%Y%m%d%H%M%S')

        expected = 'zip -r cs_sp_pi_slicer-20230303_081332.zip cs_sp_pi_slicer -x "*.git*" -x "*.DS_Store"'
        actual = get_zip_command_line(datetime_now)
        self.assertEqual(expected, actual)

# End of File
