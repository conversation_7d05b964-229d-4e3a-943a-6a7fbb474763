site_name: "Slicer Documentation"
plugins:
  - search

nav:
  - Home: index.md
  - Getting Started:
    - Knowledge: getting-started/knowledge.md
    - Marketing: getting-started/marketing.md
    - Generic Config: getting-started/generic-config.md
    - GCP:
      - Rocky Server: getting-started/servers/gcp-rocky-server.md
      - Servers:
        - Setup: getting-started/servers/gcp/gcp-servers.md
        - Configuration: getting-started/servers/gcp/configuration.md
        - Install Packages: getting-started/servers/gcp/packages.md
        - Apache: getting-started/servers/gcp/apache.md
        - Certificates: getting-started/servers/gcp/certificates.md
        - Additional Config: getting-started/servers/gcp/additional-config.md
        - Monitoring Setup: getting-started/servers/gcp/monitoring-setup.md
        - Bucket Storage: getting-started/servers/gcp/bucket-storage.md
        - Sudo Access: getting-started/servers/gcp/sudo-access.md
        - Python Apache: getting-started/servers/gcp/python-apache.md
        - Old Server Documentation: getting-started/servers/gcp/old-server-docs.md
  - Imaging:
    - SD Image Building: imaging/sd-image-building.md
    - SD Image Building QCAM (Deprecated): imaging/sd-image-building-qcam.md
  - How-To:
    - Burn an Image: how-tos/how-to-burn.md
    - Check MD5: how-tos/how-to-check-md5.md
    - Show PowerPoint: how-tos/how-to-show-ppt.md
  - Raspberry Pi:
      - Applications: pi/applications.md
      - Run Book: pi/run-book.md
  - Reference:
    - Datastore: reference/datastore.md
    - Data Migration: reference/data-migration.md
    - Filesystem: reference/filesystem.md
    - Python Porting: reference/python3porting.md
    - Security Compliance: reference/security-compliance-review.md
    - Ubuntu22 Servers: reference/ubuntu22_servers.md
    - Coding Bible: reference/coding-bible.md
    - Branches:
      - Overview: reference/branches.md
      - Branch Lists:
        - 2024-12-16: reference/branches/2024-12-16.md
        - 2024-12-11: reference/branches/2024-12-11.md

  - Release Notes:
    - Notes: releases/release-notes.md
    - SP.59 (2025-06-06): releases/2025-06-06_SP.59.md
    - SP.58 (2025-05-18): releases/2025-05-18_SP.58.md
    - SP.57 (2025-04-30): releases/2025-04-30_SP.57.md
    - SP.56 (2025-03-12): releases/2025-03-12_SP.56.md
    - SP.55 (2025-01-24): releases/2025-01-24_SP.55.md
    - SP.54 (2025-01-24): releases/2025-01-24_SP.54.md
    - SP.53 (2024-12-16): releases/2024-12-16_SP.53.md
    - SP.52 (2024-11-12): releases/2024-11-12_SP.52.md
    - SP.51 (2024-04-18): releases/2024-04-18_SP.51.md
    - SP.50 (2024-04-01): releases/2024-04-01_SP.50.md
    - SP.49 (2024-03-12): releases/2024-03-12_SP.49.md
    - SP.48 (2024-03-11): releases/2024-03-11_SP.48.md
    - SP.47 (2024-02-06): releases/2024-02-06_SP.47.md
    - SP.46 (2023-10-25): releases/2023-10-25_SP.46.md
    - SP.45 (2023-09-18): releases/2023-09-18_SP.45.md
    - SP.44 (2023-08-21): releases/2023-08-21_SP.44.md
    - SP.43 (2023-07-03): releases/2023-07-03_SP.43.md
    - SP.42 (2023-05-31): releases/2023-05-31_SP.42.md
    - SP.41 (2023-05-02): releases/2023-05-02_SP.41.md
    - SP.40 (2023-04-03): releases/2023-04-03_SP.40.md
    - SP.39 (2023-02-07): releases/2023-02-07_SP.39.md
    - SP.38 (2023-01-27): releases/2023-01-27_SP.38.md
    - SP.37 (2022-12-07): releases/2022-12-07_SP.37.md
    - SP.36 (2022-12-05): releases/2022-12-05_SP.36.md
    - SP.35 (2022-10-11): releases/2022-10-11_SP.35.md
    - SP.34 (2022-08-24): releases/2022-08-24_SP.34.md
    - SP.33 (2022-06-28): releases/2022-06-28_SP.33.md
    - SP.32 (2022-06-01): releases/2022-06-01_SP.32.md
    - SP.31 (2022-04-25): releases/2022-04-25_SP.31.md
    - SP.30 (2022-04-22): releases/2022-04-22_SP.30.md
    - SP.29 (2022-04-20): releases/2022-04-20_SP.29.md
    - SP.28 (2022-03-11): releases/2022-03-11_SP.28.md
    - SP.27 (2022-03-07): releases/2022-03-07_SP.27.md
    - SP.26 (2022-03-02): releases/2022-03-02_SP.26.md
    - SP.25 (2022-02-21): releases/2022-02-21_SP.25.md
    - SP.24 (2022-01-14): releases/2022-01-14_SP.24.md
    - SP.23 (2021-12-21): releases/2021-12-21_SP.23.md
    - SP.22 (2021-11-29): releases/2021-11-29_SP.22.md
    - SP.21 (2021-10-14): releases/2021-10-14_SP.21.md
    - SP.20 (2021-08-31): releases/2021-08-31_SP.20.md
    - SP.19 (2021-08-30): releases/2021-08-30_SP.19.md
    - SP.18 (2021-08-26): releases/2021-08-26_SP.18.md
    - SP.17 (2021-08-24): releases/2021-08-24_SP.17.md
    - SP.16 (2021-08-20): releases/2021-08-20_SP.16.md
    - SP.15 (2021-08-18): releases/2021-08-18_SP.15.md
    - SP.14 (2021-08-17): releases/2021-08-17_SP.14.md
    - SP.13 (2021-08-14): releases/2021-08-14_SP.13.md
    - SP.12 (2021-07-22): releases/2021-07-22_SP.12.md
    - SP.11 (2021-07-20): releases/2021-07-20_SP.11.md
    - SP.10 (2021-07-12): releases/2021-07-12_SP.10.md
    - SP.9 (2021-07-03): releases/2021-07-03_SP.9.md
    - SP.8 (2021-07-02): releases/2021-07-02_SP.8.md
    - SP.7 (2021-07-02): releases/2021-07-02_SP.7.md
    - SP.6 (2021-06-24): releases/2021-06-24_SP.6.md
    - SP.5 (2021-06-23): releases/2021-06-23_SP.5.md
    - SP.4 (2021-06-21): releases/2021-06-21_SP.4.md
    - SP.3 (2021-05-28): releases/2021-05-28_SP.3.md
    - SP.2 (2021-05-19): releases/2021-05-19_SP.2.md
    - SP.1 (2021-05-17): releases/2021-05-17_SP.1.md
    - 2.1.6 (2021-07-13): releases/2021-07-13_2.1.6.md
    - 2.0.6 (2021-05-18): releases/2021-05-18_2.0.6.md
    - 2.0.4 (2021-05-06): releases/2021-05-06_2.0.4.md
    - 2.0.3 (2021-05-05): releases/2021-05-05_2.0.3.md
    - 2.0.2 (2021-05-05): releases/2021-05-05_2.0.2.md
    - 2.0.1 (2021-05-05): releases/2021-05-05_2.0.1.md
    - 2.0.0 (2021-05-01): releases/2021-05-01_2.0.0.md
    - Versioning: releases/versioning.md

theme:
  name: "material"  # Optional, but recommended
  icon:
    previous: fontawesome/solid/angle-left
    next: fontawesome/solid/angle-right
    admonition:
      note: octicons/tag-16
      abstract: octicons/checklist-16
      info: octicons/info-16
      tip: octicons/squirrel-16
      success: octicons/check-16
      question: octicons/question-16
      warning: octicons/alert-16
      failure: octicons/x-circle-16
      danger: octicons/zap-16
      bug: octicons/bug-16
      example: octicons/beaker-16
      quote: octicons/quote-16
  palette:
    # Palette toggle for automatic mode
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode
    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to system preference
  features:
    - content.code.copy
    - content.code.select
    - content.code.annotate
    - navigation.footer
    - search.suggest
    - search.highlight
    - navigation.breadcrumbs
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.progress
    - navigation.tracking
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.top
markdown_extensions:
  # Highlighting and code features
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - pymdownx.details
  - pymdownx.tabbed:
      alternate_style: true
  - toc:
      permalink: true
      permalink_title: "Permalink"
  # Admonition and table features
  - admonition
  - tables
  - attr_list
  # Emoji support
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  # Other extensions
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.keys
  - pymdownx.mark
  - pymdownx.tilde
