# A dataport for slicer page services

service = 'dataport'
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/dataport.py

using:
sudo vi /var/www/html/dataport.py

It also requires:

sudo vi /etc/httpd/conf.d/python-dataport.conf
----- start copy -----
WSGIScriptAlias /dataport /var/www/html/dataport.py
----- end copy -----

sudo chown apache:apache /var/www/html/dataport.py

sudo systemctl restart httpd

test on Slicer server with:
cd /var/www/html
sudo python -c "import dataport; print(dataport.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/dataport



"""

import cgi
import copy
import traceback
import json
import os
import sys
import textwrap
import time

import shlex
import subprocess
import unittest

from tempfile import TemporaryFile

startup_exceptions = ''
service_config = {}

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if environ['REQUEST_METHOD'] == 'POST':
        body, other = make_body_POST(environ)
        permissions.log_page_allowed(environ, service, other)
        return body, other

    if permissions.permission_prefix_allowed(environ, service + '_'):
        try:
            if environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
                permissions.log_page_allowed(environ, service, other)
                return body, other
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ):
    # ====================================
    # https://stackoverflow.com/questions/14355409/getting-the-dataport-file-content-to-wsgi
    # https://stackoverflow.com/questions/14544696/python-simple-wsgi-file-dataport-script-what-is-wrong/14590585

    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body += 'method = POST<br>'

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)
    # body += str(field_storage) # FieldStorage(None, None, [FieldStorage('file', 'ChromeZoom.xlsx', 'PK\x03\x...
    # FieldStorage(None, None, [MiniFieldStorage('file_download_allowed', 'testfile1.txt'), MiniFieldStorage('file_download_allowed_allowed', 'Yes')])

    if len(field_storage.list):
        for item in field_storage.list:
            if item.filename:
                body += 'filename, ' + str(item.filename)
                file_content = item.file.read().decode('utf-8')

                for line_read in file_content.split('\n'):
                    splits = line_read.split('\t')
                    if len(splits) == 2:
                        datastore.set_value(splits[0], splits[1])

    else:
        return str(field_storage), other

    return make_get_content(environ)


# ====================================
def make_body_GET(environ):
    # ====================================
    return make_get_content(environ)


# ====================================
def make_get_content(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += "<br><br>"
        body += '<center>'
        body += 'Use this to upload a datastore_snapshot, <br>in order to add/overwrite the local datastore.'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += """
<form id="dataport" name="dataport" method=post enctype=multipart/form-data>
<input type=file name=file>"""
        body += '</td>'
        body += '<td>'
        body += """
<input type=submit value=dataport>
</form>
"""
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'
        body += '<br><br>'

        body += '<br><br>'

        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n<body>\n'

    try:
        body, other = make_body(environ)
        html += body
    except Exception as e:
        html += 'exception: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
