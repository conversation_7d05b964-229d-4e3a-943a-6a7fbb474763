# A receiver for monitor check-ins

service = 'reports'
version = service + '.0.7'

_ = """
This file gets loaded to:
/var/www/html/reports.py

using:
sudo vi /var/www/html/reports.py

or for development gets loaded to:

sudo vi /var/www/html/reportsd.py


It also requires:

sudo vi /etc/httpd/conf.d/python-reports.conf
----- start copy -----
WSGIScriptAlias /reports /var/www/html/reports.py
----- end copy -----

sudo chown apache:apache /var/www/html/reports.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import reports; print(reports.make_body({'REQUEST_METHOD':'GET','QUERY_STRING':'serial=10000000e3669edf','REMOTE_ADDR':'***********'}))"



!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
For dev:

sudo vi /var/www/html/devreports.py


sudo vi /etc/httpd/conf.d/python-dev-reports.conf
----- start copy -----
WSGIScriptAlias /devreports /var/www/html/devreports.py
----- end copy -----

sudo chown apache:apache /var/www/html/devreports.py

sudo systemctl restart httpd

https://slicer.cardinalhealth.net/devreports


test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import devreports; print(devreports.make_body({'REQUEST_METHOD':'GET','QUERY_STRING':'serial=10000000e3669edf','REMOTE_ADDR':'***********'}))"

sudo tail -f /var/log/httpd/error_log



!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/reports

https://slicer.cardinalhealth.net/reports?siteid=PR005

https://slicer.cardinalhealth.net/reports?serial=100000002a5da842

https://slicer.cardinalhealth.net/reports?monitorNot=M.1.2
https://slicer.cardinalhealth.net/reports?monitor=M.2.2

https://slicer.cardinalhealth.net/reports?profile=profile_PR005-a

# to look up by ip address, and push monitor for first time. (Pulling forward from 1.x image)
https://slicer.cardinalhealth.net/reports?serial=*************

It pulls the checkin content from:
/var/log/slicer/checkin/json/id/<id>

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_reports

https://slicer.cardinalhealth.net/reports?summary_view=(whitelist)

https://slicer.cardinalhealth.net/reportsd?summary_view=(whitelist)


"""

_time_series_data_notes = """

InfluxDB:
https://www.influxdata.com/influxcloud-trial/?utm_source=google&utm_medium=cpc&utm_campaign=2020-09-03_Cloud_Traffic_Brand-InfluxDB_NA&utm_term=influxdb&gclid=EAIaIQobChMIi-uTpPTd_QIVwDizAB0SJQFFEAAYASAAEgIwL_D_BwE

"""

import copy
import datetime
import hashlib
import traceback
import json
import os
import shutil
import stat
import sys
import time
import unittest

startup_exceptions = ''

sites_to_drop = {}

try:
    import thirdparty
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import profiles
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

s_do_use_management_id = True

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    checkin_file_root = service_config['checkin_file_root']
    datadrop_save_path = service_config['datadrop_save_path']
    statistics_save_path = service_config['statistics_save_path']

    if 'sites_to_drop' in service_config:
        sites_to_drop = service_config['sites_to_drop']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    import address2location
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import algorithms
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import codeupload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import config
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datamine
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import devicecommand
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import deviceupload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import intake
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import management
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import rings
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import tasks
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import upload
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

# The following print statement ends up in "/var/log/httpd/error_log", so stop doing it.
# It was getting triggered every time the report page was requested.
# print ('startup_exceptions: ', startup_exceptions)
try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# Globals
color_red_warning = "(255, 0, 0, 0.3)"
color_yellow_caution = "(255, 255, 100, 0.3)"
color_green = "(0, 255, 0, 0.3)"
color_clear = "(0, 0, 0, 0.0)"
color_purple = "(255, 0, 255, 0.3)"

color_feature_not_available = color_purple

s_threshold_for_stuck_keys = 800
s_seconds_for_current = 35 * 4 * 60  # make it 4 reporting cycles, to allow for drops

network_utilization_save_path = '/var/log/slicer/network/id/(id)/'

# s_pi_images

s_all_basic_settings = [('device_name_', ''),
                        ('device_tag_', ''),
                        ('device_collection_', ''),
                        ('device_browser_timeout_', ''),
                        ('device_ring_assignment_', ''),
                        ('device_browser_zoom_', ''),
                        ('device_screen_resolution_', ''),
                        ('device_special_menu_', ''),
                        ('device_clockview_', ''),
                        ('conf_wifi_connect_', ''),
                        ('conf_wifi_hopper_', ''),
                        ('conf_wifi_coaster_', ''),
                        ('device_bluetooth_', ''),
                        ('device_profile_', 'profile_Blank'),
                        ('device_extra_boots_', ''),
                        ('device_retired_', ''),
                        ]

# ----------------------------
def sort_service_versions(versions):
    # ----------------------------
    return_value = []

    expanded_versions = {}
    for item in versions:
        sub_items = []
        for sub_item in item.split('.'):
            sub_items.append(sub_item.zfill(10))
        key = '.'.join(sub_items)
        expanded_versions[key] = item

    for key in sorted(expanded_versions.keys(), reverse=True):
        return_value.append(expanded_versions[key])

    return return_value

# ----------------------------
def make_json_dumps_aligned(dumps):
    # ----------------------------
    return_value = ''

    max_right_half = 0
    max_left_half = 0
    for line in dumps.split('\n'):
        if ':' in line:
            splits = line.split(':')
            len_left = len(splits[0])
            len_right = len(splits[1])
            if len_left > max_left_half:
                max_left_half = len_left
            if len_right > max_right_half:
                max_right_half = len_right

    for line in dumps.split('\n'):
        if return_value:
            return_value += '\n'

        if ':' in line:
            splits = line.split(':')
            space_to_pad = max_left_half - len(splits[0])
            return_value += splits[0] + ' ' * space_to_pad + ' : ' + splits[1]
        else:
            return_value += line

    return return_value

# ----------------------------
def make_device_parameters():
    # ----------------------------
    return_value = []

    # ---------------
    #
    # ---------------
    try:
        d = {}
        d['title'] = 'Management Block'
        d['option_value'] = "device_management_block"
        d['datastore_prefix'] = 'device_managementID_'
        d['values_to_show'] = ['']
        d['requires'] = []
        d['text_for_info_box'] = ''
        d['color_for_info_box'] = color_clear
        return_value.append(copy.deepcopy(d))
    except:
        pass

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Name'
    d['option_value'] = "name_text_set"
    d['text_field_name'] = "selected_parameter"
    d['datastore_prefix'] = 'device_name_'
    d['values_to_show'] = None
    d['requires'] = [{'pi_runner': 'R.1.4'}, {'pi_hmi': 'H.1.2'}]
    d['text_for_info_box'] = '(Note: Use vertical bar as a line break.)'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Tag'
    d['option_value'] = "tag_text_set"
    d['text_field_name'] = "selected_parameter"
    d['datastore_prefix'] = 'device_tag_'
    d['values_to_show'] = None
    d['requires'] = []
    d['text_for_info_box'] = '(Note: Used for making collections.)'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Screen Scale'
    d['option_value'] = "screen_scale_selection"
    d['datastore_prefix'] = 'device_browser_zoom_'
    d['current_value_to_show_rule'] = 'screen_zoom'
    d['values_to_show'] = ['41', '50', '65', '71', '75', '80', '100', '135', '150', '175', '200', '225',
                           '250']
    d['requires'] = [{'pi_runner': 'R.1.4'}]
    d[
        'text_for_info_box'] = 'Changing the zoom will cause a pi browser exit/restart, <br>and potential loss of work in progress!'
    d['color_for_info_box'] = color_yellow_caution
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Bluetooth Enable'
    d['option_value'] = "device_bluetooth_selection"
    d['datastore_prefix'] = 'device_bluetooth_'
    d['values_to_show'] = ['', 'No', 'Yes']
    d['requires'] = [{'pi_bluetooth': 'B.1.0'}, {'pi_runner': 'R.1.6'}, {'pi_hmi': 'H.1.3'}]
    d['text_for_info_box'] = ''
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Clock View Enable'
    d['option_value'] = "device_clockview_selection"
    d['datastore_prefix'] = 'device_clockview_'
    d['values_to_show'] = ['', 'No', 'MMDDYYYYhhmmap', 'hhmmapMMDDYYYY']
    d['requires'] = [{'pi_runner': 'R.1.8'}, {'pi_hmi': 'H.1.4'}]
    d['text_for_info_box'] = ''
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Forced Screen Resolution'
    d['option_value'] = "screen_resolution_selection"
    d['datastore_prefix'] = 'device_screen_resolution_'

    # '(name) hdmi_group mode'

    d['values_to_show'] = ['']
    d['values_to_show'].append('VGA(640x480) 1 1')

    d['values_to_show'].append('XGA(1024x768) 2 16')

    d['values_to_show'].append('720p(1280x720) 1 4')
    d['values_to_show'].append('1080i(1920x1080) 1 5')
    d['values_to_show'].append('1080p(1920x1080) 1 16')
    d['values_to_show'].append('4K-rb(2560x1600) 2 76')
    d['values_to_show'].append('4K(2560x1600) 2 77')
    d['values_to_show'].append('(1360x768) 2 39')
    d['values_to_show'].append('(1360x768)rb 2 40')

    # for Dave's walk around device
    # https://forums.raspberrypi.com/viewtopic.php?t=266440
    d['values_to_show'].append('24fps(1366x768) 2 87 1366,768,24')
    d['values_to_show'].append('25fps(1366x768) 2 87 1366,768,25')
    d['values_to_show'].append('30fps(1366x768) 2 87 1366,768,30')
    d['values_to_show'].append('60fps(1366x768) 2 87 1366,768,60')

    d['values_to_show'].append('(1600x900)rb 2 83')
    # https://sysadmin-central.com/2019/08/14/raspberry-pi-how-to-output-4k-resolution-to-a-tv/
    # requires runner R.8.8 or higher
    #                   d['values_to_show'].append('4Kuhd(3840x2160,30) 2 87 3840,2160,30 3840 2160 400000000')
    d['values_to_show'].append('4Kuhd24fps(3840x2160) 2 87 3840,2160,24')
    d['values_to_show'].append('4Kuhd25fps(3840x2160) 2 87 3840,2160,25')
    d['values_to_show'].append('4Kuhd30fps(3840x2160) 2 87 3840,2160,30')

    d['name_to_show_rule'] = 'screen_settings'
    d['requires'] = [{'image': '2.1.5', 'pi_hmi': 'H.1.6'}]
    d[
        'text_for_info_box'] = '!!! Changing the resolution will cause a pi reboot !!!<br>Set to the top (blank) setting to allow the pi to automatically detect the screen resolution.'
    d['color_for_info_box'] = color_red_warning
    return_value.append(copy.deepcopy(d))

    # ref: https://elinux.org/RPi_Configuration
    # ref: https://www.raspberrypi.org/documentation/configuration/config-txt/video.md
    # https://en.wikipedia.org/wiki/Display_resolution
    # Name hdmi_group hdmi_mode hdmi_cvt_comma_separated
    on_device_test = """
set to VGA (1 1)

Then, to see mode 1 and mode 2 options, and current setting:
/opt/vc/bin/tvservice -m CEA
/opt/vc/bin/tvservice -m DMT
/opt/vc/bin/tvservice -s
fbset -s
    """

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Allow Special Menu'
    d['option_value'] = "special_menu_selection"
    d['datastore_prefix'] = 'device_special_menu_'
    d['values_to_show'] = ['', 'No', 'Yes']
    d['requires'] = [{'image': '2.1.5', 'pi_hmi': 'H.1.6'}]
    d['text_for_info_box'] = 'Set to Yes to allow Menu for shutdown and reboot on the pi.'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Request Browser Reset<br>(Cleared when pulled)'
    d['option_value'] = "reset_request_selection"
    d['datastore_prefix'] = 'device_reset_request_'
    d['requires'] = [{'pi_runner': 'R.6.8'}]
    d['text_for_info_box'] = '!!! This will cause a broswer reset!!!<br> The value is image:boot_count, so that it only applies to the current boot. This value will automatically be cleared when it is pulled by the device.'
    d['color_for_info_box'] = color_yellow_caution
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Request Reboot<br>(Cleared when pulled)'
    d['option_value'] = "reboot_request_selection"
    d['datastore_prefix'] = 'device_reboot_request_'
    d['requires'] = [{'image': '2.1.7', 'pi_runner': 'R.2.1'}]
    d['text_for_info_box'] = '!!! This will cause a pi reboot!!!<br> The value is image:boot_count, so that it only applies to the current boot. This value will automatically be cleared when it is pulled by the device.'
    d['color_for_info_box'] = color_red_warning
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Request Screen Grab<br>(Cleared when pulled)'
    d['option_value'] = "screengrab_request_selection"
    d['datastore_prefix'] = 'device_screengrab_request_'
    d['requires'] = [{'image': '2.2.6', 'pi_runner': 'R.2.4'}]
    d['text_for_info_box'] = 'This will cause a pi screen grab.<br> The value is image:grab_count, so that it only applies to the current count. This value will automatically be cleared when it is pulled by the device.'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    d['title'] = 'Request OS Updates<br>(Cleared when pulled)'
    d['option_value'] = "osupdates_request_selection"
    d['datastore_prefix'] = 'device_osupdates_request_'
    d['values_to_show'] = ['', 'update_at_next_chance']
    d['requires'] = [{'image': '2.2.8', 'pi_security': 'S.1.2'}]
    d['text_for_info_box'] = 'This will cause the device to perform OS updates. This value will automatically be cleared when it is pulled by the device. (30 minute cycle)'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'Browser Inactivity Timeout<br>(minutes)'
    d['option_value'] = "browser_timeout_selection"
    d['datastore_prefix'] = 'device_browser_timeout_'
    d['values_to_show'] = ['', '0', '3', '30', '60', '90', '120', '240']
    d['requires'] = [{'image': '2.2.8', 'pi_runner': 'R.3.7'}]
    d['text_for_info_box'] = '!!! Setting this timeout will auto log out the browser after the number of minutes of inactivity, and may cause loss of work in progress!. Use empty, or zero, to disable.'
    d['color_for_info_box'] = color_yellow_caution
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'Ring Assignment'
    d['option_value'] = "ring_assignment_selection"
    d['datastore_prefix'] = 'device_ring_assignment_'
    d['values_to_show'] = ['', 'M', '0', '1', '2']
    d['requires'] = [{'pi_runner': 'R.3.0'}]
    d['text_for_info_box'] = 'M is manual, Ring 0 is development, Ring 1 is early adopters, Ring 2 is pre production; all else with runner version R.3.0 and above is Ring 3 production, and all else is ring 4 production (needs a runner pushed to it, to get to Ring 3).'
    d['color_for_info_box'] = color_yellow_caution
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'WiFi ssid <br><br>(Caution: the device must be set up in Clearpass first, before iot will function on the device)'
    d['option_value'] = "wifi_connection_assignment_selection"
    d['datastore_prefix'] = 'conf_wifi_connect_'
    d['values_to_show'] = ['', 'corp', 'iot']
    d['requires'] = [{'pi_network': 'N.4.0'}, {'pi_runner': 'R.5.3'}, {'pi_hmi': 'H.3.2'}]
    d['text_for_info_box'] = 'Be sure that the device MAC address(es) has/have been added to the IOT whitelist before setting this to iot. If the MACs are not in the whitelist, then the device will not be able to access any WiFi connectivity; you would have to go to the pi, click into the ID screen, and select an alternate to iot.'
    d['color_for_info_box'] = color_yellow_caution
    return_value.append(copy.deepcopy(d))

    # ---------------
    # conf_ gets loaded to the device because "wifi_hopper" in datadrop, and on device by pi_runner
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'WiFi hopper'
    d['option_value'] = "wifi_connection_hopper_selection"
    d['datastore_prefix'] = 'conf_wifi_hopper_'
    d['values_to_show'] = ['', 'no-hop', 'hop']
    d['requires'] = [{'pi_network': 'N.5.3'}, {'pi_hmi': 'H.4.1'}]
    d['text_for_info_box'] = 'The hopper will allow the WiFi network to hop to a stronger access point when it becomes available.'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    # ---------------
    # conf_ gets loaded to the device because "wifi_hopper" in datadrop, and on device by pi_runner
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'WiFi coaster'
    d['option_value'] = "wifi_connection_coaster_selection"
    d['datastore_prefix'] = 'conf_wifi_coaster_'
    d['values_to_show'] = ['0', '5000', '10000', '15000']
    d['requires'] = [{'pi_network': 'N.5.4'}]
    d['text_for_info_box'] = 'The coaster will keep the connection settings across hops (milliseconds). Changing this value will perform a network reset, with potential loss of work in progress!. A setting of zero disables the coasting.'
    d['color_for_info_box'] = color_red_warning
    return_value.append(copy.deepcopy(d))

    # ---------------
    #
    # ---------------
    d = {}
    # d['is_development'] = True
    d['title'] = 'Extra boots'
    d['option_value'] = "extra_boots_selection"
    d['datastore_prefix'] = 'device_extra_boots_'
    d['values_to_show'] = ['0', '1', '2', '3', '4']
    d['requires'] = [{'pi_runner': 'R.9.8'}]
    d['text_for_info_box'] = 'How many extra boots to do during a cold/warm boot situation.'
    d['color_for_info_box'] = color_clear
    return_value.append(copy.deepcopy(d))

    d = {}
    # d['is_development'] = True
    d['title'] = 'Retired'
    d['option_value'] = "retired_selection"
    d['datastore_prefix'] = 'device_retired_'
    d['values_to_show'] = ['', 'yes']
    d['requires'] = [{'pi_monitor': 'M.0.0'}] # does not actually depend on the device services
    d['text_for_info_box'] = 'Mark this as yes to say that the device is retired (no longer in active use/inventory). This will remove it from most views.'
    d['color_for_info_box'] = color_red_warning
    return_value.append(copy.deepcopy(d))

    return return_value

# ----------------------------
def make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management):
    # ----------------------------
    return_value = False

    if permit_to_device_edit:
        if permit_to_locations_all:
            return_value = True
        elif user_management:
            return_value = True
        else:
            if (not device_management) or ('(unassigned)' == device_management):
                if user_site:
                    return_value = True


    return return_value

# ----------------------------
def make_csv_to_xlsx_and_other(csv_body):
    # ----------------------------
    file_to_get = 'reports.xlsx'
    body, size = make_csv_to_xlsx(csv_body)
    other = {'status': '200 OK',
        'response_header': [('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                            ('Content-length', str(size)),
                            ('Content-Disposition', 'attachment; filename=' + file_to_get)
                            ], 'add_wrapper': False}
    return body,other

# ----------------------------
def make_csv_to_xlsx(csv_body):
    # ----------------------------
    # https://www.geeksforgeeks.org/stringio-and-bytesio-for-managing-data-as-file-object/
    import pandas
    import io
    from io import StringIO
    from io import BytesIO
    read_file = pandas.read_csv(StringIO(csv_body.replace('<br>','\n')))
    binary_buffer = BytesIO()
    read_file.to_excel(binary_buffer, index=None, header=True)

    the_buffer = binary_buffer.getbuffer()
    size = len(the_buffer)

    return the_buffer.tobytes(), size

# ----------------------------
def make_sort_from_version(raw_value):
    # ----------------------------
    new_list = []
    for item in raw_value.split('.'):
        try:
            new_list.append('0'*(10-len(item)) + item)
        except:
            pass
    return_value = '.'.join(new_list)

    return return_value

# ----------------------------
def make_sort_from_screen_resolution(raw_value):
    # ----------------------------
    new_list = []
    for item in raw_value.split('x'):
        try:
            new_list.append('0'*(10-len(item)) + item)
        except:
            pass
    return_value = 'x'.join(new_list)

    return return_value

# ----------------------------
def split_content_by_chunk_size(content, chunk_size):
    # ----------------------------
    output = []

    for line_found in content.split('\n'):
        words = line_found.split()
        line_words = []
        for word in words:
            line_words.append(word)

            line_out = " ".join(line_words)
            if len(line_out) > chunk_size:
                if len(line_words) == 1:
                    output.append(line_out)
                    line_words = []
                else:
                    line_out = " ".join(line_words[:-1])
                    output.append(line_out)
                    line_words = [line_words[-1]]

        if line_words:
            line_out = " ".join(line_words)
            output.append(line_out)

    return "\n".join(output)


# ----------------------------
def get_key_definitions():
    # ----------------------------
    return_value = {}

    return_value['event_types'] = {}

    return_value['event_types']['-1'] = 'START_LOGGING'
    return_value['event_types']['0'] = 'EV_SYN'
    return_value['event_types']['1'] = 'EV_KEY'
    return_value['event_types']['2'] = 'EV_REL'
    return_value['event_types']['3'] = 'EV_ABS'
    return_value['event_types']['4'] = 'EV_MSC'
    return_value['event_types']['5'] = 'EV_SW'
    return_value['event_types']['17'] = 'EV_LED'
    return_value['event_types']['18'] = 'EV_SND'
    return_value['event_types']['20'] = 'EV_REP'
    return_value['event_types']['21'] = 'EV_FF'
    return_value['event_types']['22'] = 'EV_PWR'
    return_value['event_types']['23'] = 'EV_FF_STATUS'
    return_value['event_types']['31'] = 'EV_MAX'

    return_value['msc_codes'] = {}
    return_value['msc_codes']['458789'] = '(unknownYet)'

    return_value['msc_codes']['458829'] = 'KEY_END'
    return_value['msc_codes']['458831'] = 'KEY_Right_Arrow'
    return_value['msc_codes']['458832'] = 'KEY_Left_Arrow'
    return_value['msc_codes']['458833'] = 'KEY_Down_Arrow'
    return_value['msc_codes']['458834'] = 'KEY_Up_Arrow'

    return_value['msc_codes']['458912'] = 'left_mouse_button'  # MEX09
    return_value['msc_codes']['458913'] = 'right_mouse_button'  # MEX09

    return_value['key_codes'] = {}
    return_value['key_codes']['0'] = {'name': 'KEY_RESERVED', 'value': ''}
    return_value['key_codes']['1'] = {'name': 'KEY_ESC', 'value': ''}
    return_value['key_codes']['2'] = {'name': 'KEY_1', 'value': '1', 'shft-value': '!'}
    return_value['key_codes']['3'] = {'name': 'KEY_2', 'value': '2', 'shft-value': '@'}
    return_value['key_codes']['4'] = {'name': 'KEY_3', 'value': '3', 'shft-value': '#'}
    return_value['key_codes']['5'] = {'name': 'KEY_4', 'value': '4', 'shft-value': '$'}
    return_value['key_codes']['6'] = {'name': 'KEY_5', 'value': '5', 'shft-value': '%'}
    return_value['key_codes']['7'] = {'name': 'KEY_6', 'value': '6', 'shft-value': '^'}
    return_value['key_codes']['8'] = {'name': 'KEY_7', 'value': '7', 'shft-value': '&'}
    return_value['key_codes']['9'] = {'name': 'KEY_8', 'value': '8', 'shft-value': '*'}
    return_value['key_codes']['10'] = {'name': 'KEY_9', 'value': '9', 'shft-value': '('}
    return_value['key_codes']['11'] = {'name': 'KEY_0', 'value': '0', 'shft-value': ')'}
    return_value['key_codes']['12'] = {'name': 'KEY_MINUS', 'value': '-'}
    return_value['key_codes']['13'] = {'name': 'KEY_EQUAL', 'value': '='}
    return_value['key_codes']['14'] = {'name': 'KEY_BACKSPACE', 'value': '<bsp>'}
    return_value['key_codes']['15'] = {'name': 'KEY_TAB', 'value': '<tab>'}
    return_value['key_codes']['16'] = {'name': 'KEY_Q', 'value': 'q'}
    return_value['key_codes']['17'] = {'name': 'KEY_W', 'value': 'w'}
    return_value['key_codes']['18'] = {'name': 'KEY_E', 'value': 'e'}
    return_value['key_codes']['19'] = {'name': 'KEY_R', 'value': 'r'}
    return_value['key_codes']['20'] = {'name': 'KEY_T', 'value': 't'}
    return_value['key_codes']['21'] = {'name': 'KEY_Y', 'value': 'y'}
    return_value['key_codes']['22'] = {'name': 'KEY_U', 'value': 'u'}
    return_value['key_codes']['23'] = {'name': 'KEY_I', 'value': 'i'}
    return_value['key_codes']['24'] = {'name': 'KEY_O', 'value': 'o'}
    return_value['key_codes']['25'] = {'name': 'KEY_P', 'value': 'p'}
    return_value['key_codes']['26'] = {'name': 'KEY_LEFTBRACE', 'value': '[', 'shft-value': '{'}
    return_value['key_codes']['27'] = {'name': 'KEY_RIGHTBRACE', 'value': ']', 'shft-value': '}'}
    return_value['key_codes']['28'] = {'name': 'KEY_ENTER', 'value': '<enter>'}
    return_value['key_codes']['29'] = {'name': 'KEY_LEFTCTRL', 'value': '<lctrl>'}
    return_value['key_codes']['30'] = {'name': 'KEY_A', 'value': 'a'}
    return_value['key_codes']['31'] = {'name': 'KEY_S', 'value': 's'}
    return_value['key_codes']['32'] = {'name': 'KEY_D', 'value': 'd'}
    return_value['key_codes']['33'] = {'name': 'KEY_F', 'value': 'f'}
    return_value['key_codes']['34'] = {'name': 'KEY_G', 'value': 'g'}
    return_value['key_codes']['35'] = {'name': 'KEY_H', 'value': 'h'}
    return_value['key_codes']['36'] = {'name': 'KEY_J', 'value': 'j'}
    return_value['key_codes']['37'] = {'name': 'KEY_K', 'value': 'k'}
    return_value['key_codes']['38'] = {'name': 'KEY_L', 'value': 'l'}
    return_value['key_codes']['39'] = {'name': 'KEY_SEMICOLON', 'value': ';', 'shft-value': ':'}

    return_value['key_codes']['40'] = {'name': 'KEY_APOSTROPHE', 'value': "'", 'shft-value': '"'}
    return_value['key_codes']['41'] = {'name': 'KEY_GRAVE', 'value': '<grave>'}
    return_value['key_codes']['42'] = {'name': 'KEY_LEFTSHIFT', 'value': '<lshift>'}
    return_value['key_codes']['43'] = {'name': 'KEY_BACKSLASH', 'value': '\\'}
    return_value['key_codes']['44'] = {'name': 'KEY_Z', 'value': 'z'}
    return_value['key_codes']['45'] = {'name': 'KEY_X', 'value': 'x'}
    return_value['key_codes']['46'] = {'name': 'KEY_C', 'value': 'c'}
    return_value['key_codes']['47'] = {'name': 'KEY_V', 'value': 'v'}
    return_value['key_codes']['48'] = {'name': 'KEY_B', 'value': 'b'}
    return_value['key_codes']['49'] = {'name': 'KEY_N', 'value': 'n'}

    return_value['key_codes']['50'] = {'name': 'KEY_M', 'value': 'm'}
    return_value['key_codes']['51'] = {'name': 'KEY_COMMA', 'value': ','}
    return_value['key_codes']['52'] = {'name': 'KEY_DOT', 'value': '.'}
    return_value['key_codes']['53'] = {'name': 'KEY_SLASH', 'value': '/', 'shft-value': '?'}
    return_value['key_codes']['54'] = {'name': 'KEY_RIGHTSHIFT', 'value': '<rshift>'}
    return_value['key_codes']['55'] = {'name': 'KEY_KPASTERISK', 'value': '<astr>'}
    return_value['key_codes']['56'] = {'name': 'KEY_LEFTALT', 'value': '<lalt>'}
    return_value['key_codes']['57'] = {'name': 'KEY_SPACE', 'value': ' '}
    return_value['key_codes']['58'] = {'name': 'KEY_CAPSLOCK', 'value': '<capslock>'}
    return_value['key_codes']['59'] = {'name': 'KEY_F1', 'value': '<F1>'}

    return_value['key_codes']['60'] = {'name': 'KEY_F2', 'value': '<F2>'}
    return_value['key_codes']['61'] = {'name': 'KEY_F3', 'value': '<F3>'}
    return_value['key_codes']['62'] = {'name': 'KEY_F4', 'value': '<F4>'}
    return_value['key_codes']['63'] = {'name': 'KEY_F5', 'value': '<F5>'}
    return_value['key_codes']['64'] = {'name': 'KEY_F6', 'value': '<F6>'}
    return_value['key_codes']['65'] = {'name': 'KEY_F7', 'value': '<F7>'}
    return_value['key_codes']['66'] = {'name': 'KEY_F8', 'value': '<F8>'}
    return_value['key_codes']['67'] = {'name': 'KEY_F9', 'value': '<F9>'}
    return_value['key_codes']['68'] = {'name': 'KEY_F10', 'value': '<F10>'}
    return_value['key_codes']['69'] = {'name': 'KEY_NUMLOCK', 'value': '<numlock>'}

    return_value['key_codes']['70'] = {'name': 'KEY_SCROLLLOCK', 'value': '<scrollock>'}
    return_value['key_codes']['71'] = {'name': 'KEY_KP7', 'value': '7'}
    return_value['key_codes']['72'] = {'name': 'KEY_KP8', 'value': '8'}
    return_value['key_codes']['73'] = {'name': 'KEY_KP9', 'value': '9'}
    return_value['key_codes']['74'] = {'name': 'KEY_KPMINUS', 'value': '-'}
    return_value['key_codes']['75'] = {'name': 'KEY_KP4', 'value': '4'}
    return_value['key_codes']['76'] = {'name': 'KEY_KP5', 'value': '5'}
    return_value['key_codes']['77'] = {'name': 'KEY_KP6', 'value': '6'}
    return_value['key_codes']['78'] = {'name': 'KEY_KPPLUS', 'value': '+'}
    return_value['key_codes']['79'] = {'name': 'KEY_KP1', 'value': '1'}

    return_value['key_codes']['80'] = {'name': 'KEY_KP2', 'value': '2'}
    return_value['key_codes']['81'] = {'name': 'KEY_KP3', 'value': '3'}
    return_value['key_codes']['82'] = {'name': 'KEY_KP0', 'value': '0'}
    return_value['key_codes']['83'] = {'name': 'KEY_KPDOT', 'value': '.'}
    return_value['key_codes']['84'] = {'name': 'KEY_ZENKAKUHANKAKU', 'value': '<ZEN>'}
    return_value['key_codes']['85'] = {'name': 'KEY_102ND', 'value': '<102ND>'}
    return_value['key_codes']['86'] = {'name': 'KEY_F11', 'value': '<F11>'}
    return_value['key_codes']['87'] = {'name': 'KEY_F12', 'value': '<F12>'}

    return_value['key_codes']['102'] = {'name': 'KEY_HOME', 'value': '<Home>'}
    return_value['key_codes']['103'] = {'name': 'KEY_UP', 'value': '<up_arrow>'}
    return_value['key_codes']['104'] = {'name': 'KEY_PAGEUP', 'value': '<Page Up>'}
    return_value['key_codes']['105'] = {'name': 'KEY_LEFT', 'value': '<left arrow>'}
    return_value['key_codes']['106'] = {'name': 'KEY_RIGHT', 'value': '<right arrow>'}
    return_value['key_codes']['107'] = {'name': 'KEY_END', 'value': '<End>'}
    return_value['key_codes']['108'] = {'name': 'KEY_DOWN', 'value': '<down_arrow>'}

    return_value['key_codes']['125'] = {'name': 'KEY_LEFTMETA', 'value': '<lmeta>'}  # Windows key, between Fn and Alt
    return_value['key_codes']['126'] = {'name': 'KEY_RIGHTMETA', 'value': '<rmeta>'}

    # https://www.spinics.net/lists/linux-input/msg24319.html
    _ = """
MEX09
['1633636309', '4', '4', '458913'] EV_MSC
['1633636309', '1', '240', '1'] EV_KEY KEY_UNKNOWN:active
['1633636309', '4', '4', '458913'] EV_MSC
['1633636309', '1', '240', '0'] EV_KEY KEY_UNKNOWN:inactive

tail -f /cardinal/log/pi_logging/20211103/device_event0.txt | fgrep "458829"

"""
    # 458913, on MEX09 keyboard, this is the right mouse button
    return_value['key_codes']['240'] = {'name': 'KEY_UNKNOWN', 'value': '<!!!key_unknown!!!>'}

    return_value['key_codes']['272'] = {'name': 'KEY_KPAD_LEFT_BTN', 'value': ''}
    return_value['key_codes']['273'] = {'name': 'KEY_KPAD_RIGHT_BTN', 'value': ''}

    return_value['key_actions'] = {}
    return_value['key_actions']['0'] = {'name': 'inactive'}
    return_value['key_actions']['1'] = {'name': 'active'}
    return_value['key_actions']['2'] = {'name': 'held'}

    return return_value


# ----------------------------
def stuckkeys_report_to_human(stuckkeys_report, key_definitions, threshold=0):
    # ----------------------------
    return_value = ''

    try:
        split_events = stuckkeys_report.split('-')
        for split_event in split_events:
            if split_event:
                splits = split_event.split('_')

                ev_source = splits[0].strip()
                ev_type = splits[1].strip()
                ev_code = splits[2].strip()
                ev_value = splits[3].strip()
                ev_count = splits[4].strip()

                if int(ev_count) > threshold:
                    ev_type_name = key_definitions['event_types'][ev_type]
                    if ev_type_name == 'EV_SYN':
                        pass
                    elif ev_type_name == 'EV_KEY':
                        if return_value:
                            return_value += ', '
                        return_value += ev_source
                        return_value += ':'
                        return_value += key_definitions['key_codes'][ev_code]['name']
                        return_value += ':'
                        return_value += key_definitions['key_actions'][ev_value]['name']
    except:
        pass

    return return_value


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = ''

    try:
        home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    except:
        pass

    return home_url


# ----------------------------
def get_ssid_from_dash_string(raw_value):
    # ----------------------------
    return_value = raw_value.replace('cah-iot', 'cahiot').replace('level2-ng', 'level2ng').split('-')[0]

    if return_value == '(not_on_wifi)':
        return_value = ''

    return return_value


# ----------------------------
def mac_is_raspberrypi(mac_to_test):
    # ----------------------------
    return_value = False

    if "DC-A6-32" == mac_to_test[0:8]:
        return_value = True

    if "28-CD-C1" == mac_to_test[0:8]:
        return_value = True

    if "B8-27-EB" == mac_to_test[0:8]:
        return_value = True

    if "E4-5F-01" == mac_to_test[0:8]:
        return_value = True

    return return_value


# ----------------------------
def comms_report(dict_of_comms):
    # ----------------------------
    return_value = ''

    try:
        if float(dict_of_comms['monitor_time']) < s_seconds_for_current:
            if float(dict_of_comms['runner_time']) > s_seconds_for_current:
                return_value = '1'
    except:
        pass  # might have hit int('(missing)')

    return return_value


# ----------------------------
def format_mac_address(input_mac):
    # ----------------------------
    return input_mac.replace(':', '-').upper()


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        shutil.move(temp_name, output_file)


# ====================================
def get_all_reported_ids():
    # ====================================
    pass
    id_files = os.listdir(checkin_file_root)

    id_datadrop = os.listdir(datadrop_save_path)
    for id in id_datadrop:
        if not id in id_files:
            id_files.append(id)

    return id_files


# ====================================
def get_browser_reset_value(id):
    # ====================================
    datadrop_directory = datadrop_save_path + id + '/'
    runner_drop = datadrop_directory + 'runner'
    content = open(runner_drop, 'r').read()
    runner_d = json.loads(content)

    return_value = ''

    if ('image' in runner_d) and ('boot_count' in runner_d):
        return_value = str(runner_d['image'] + ":" + runner_d['boot_count'])

    return return_value


# ====================================
def get_top_level_issues(site_to_summarize=''):
    # ====================================
    # The actual display order is determined in dashboard.py, so go there to arrange them.
    return_value = {
        # metrics
        'new_image_percent': -1,
        'recent_service_pack_percent': -1,
        'on_iot_percent': -1,
        # warnings
        'on_corp_count': 0,
        'on_corp_and_not_service_pack_count': 0,
        'outages': 0,
        'disk_high': 0,
        'inspect_comms': 0,
        'certificates_flags': 0,
        'unassigned_management_ID': 0,
        'stuck_keys': 0,
        'high_temperature': 0,
        'screen_threshold_exceeded': 0,
        'retired_device_current':0,
        # watches
        'browser_wants_reset': 0,
        'disk_warning': 0,
        'location_warning': 0,
        'load_report_high': 0, 'load_report_warning': 0,
        'mem_free_low': 0,
        # for displaying what the criteria was based on
        'service_pack_key_recent': '',
        'current_count': 0,
        'user_active_count': 0,
        'total_count': 0,
        'time_now': 0,
    }

    time_now = time.time()
    return_value['time_now'] = time_now
    built = {}

    id_files = get_all_reported_ids()

    data_store_content = datastore.all_datastore()
    content_of_versions = rings.get_content_of_versions()
    service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
    service_pack_key_recent = rings.extract_service_pack_target(content_of_versions)
    return_value['service_pack_key_recent'] = service_pack_key_recent

    current_count = 0
    user_active_count = 0
    total_count = 0
    new_image_count = 0
    recent_service_pack_count = 0
    on_iot_count = 0
    unassigned_management_ID = 0
    stuck_keys = 0
    high_temperature = 0

    try:
        import certificates
        if certificates.any_flags():
            return_value['certificates_flags'] = 1
    except:
        pass

    ids = {}
    data_store_content = datastore.all_datastore()
    key_definitions = get_key_definitions()
    for id in id_files:
        retired = datastore.get_value_stored(data_store_content, 'device_retired_' + id)
        if retired == 'yes':
            runner_file = datadrop_save_path + id + '/' + 'runner'
            try:
                runner_report = json.loads(open(runner_file, 'r').read())
            except:
                runner_report = {}
            if 'time' in runner_report:
                runner_seconds = str(int(time_now - float(runner_report['time'])))
                if float(runner_seconds) < s_seconds_for_current:
                    # is current
                    return_value['retired_device_current'] += 1
            break   # get out of this device for the for loop

        management_id = datastore.get_value_stored(data_store_content, 'device_managementID_' + id)
        if not management_id:
            unassigned_management_ID += 1

        # ---------------------
        # get the reports
        # ---------------------
        checkin_file = checkin_file_root + id
        try:
            with open(checkin_file, 'r') as f:
                checkin_report = json.loads(f.read())
        except:
            checkin_report = {}

        device_address = ''
        if 'address' in checkin_report:
            device_address = checkin_report['address']

        use_this_device = True

        if s_do_use_management_id:
            this_site = datastore.get_value_stored(data_store_content, 'device_managementID_' + id)
        else:
            this_site = address2location.location_stored(data_store_content, device_address, returnName=False)

        if site_to_summarize:
            use_this_device = False
            if site_to_summarize == this_site:
                use_this_device = True

        if this_site in sites_to_drop:  # drop Cordis from all reporting?
            use_this_device = False

        if use_this_device:
            runner_file = datadrop_save_path + id + '/' + 'runner'
            try:
                runner_report = json.loads(open(runner_file, 'r').read())
            except:
                runner_report = {}

            services_file = datadrop_save_path + id + '/' + 'service_versions'
            try:
                with open(services_file, 'r') as f:
                    service_versions = json.loads(f.read())
            except:
                service_versions = {}

            monitor_file = datadrop_save_path + id + '/' + 'monitor'
            try:
                with open(monitor_file, 'r') as f:
                    monitor_report = json.loads(f.read())
            except:
                monitor_report = {}

            network_file = datadrop_save_path + id + '/' + 'network'
            try:
                with open(network_file, 'r') as f:
                    network_report = json.loads(f.read())
            except:
                network_report = {}

            is_current = False
            built['calc_runner_seconds'] = '(missing)'
            built['calc_seconds'] = '(missing)'
            if 'time' in runner_report:
                built['calc_runner_seconds'] = str(int(time_now - float(runner_report['time'])))
                if float(built['calc_runner_seconds']) < s_seconds_for_current:
                    is_current = True

            if 'time' in checkin_report:
                built['calc_seconds'] = str(int(time_now - float(checkin_report['time'])))
                if float(built['calc_seconds']) < s_seconds_for_current:
                    is_current = True

            stats_file = statistics_save_path + id
            try:
                stats = json.loads(open(stats_file, 'r').read())
            except:
                stats = {}

            # ---------------------
            # pull the data out of the reports
            # ---------------------
            total_count += 1

            if is_current:
                current_count += 1

                built['inspect_comms'] = comms_report(
                    {'monitor_time': built['calc_seconds'], 'runner_time': built['calc_runner_seconds']})
                if built['inspect_comms']:
                    return_value['inspect_comms'] += 1

                if 's_logging_userinactive' in runner_report:
                    try:
                        # This gets used by dashboard, to report for the hour, so let it be any activity for the last 60 minutes
                        if float(runner_report['s_logging_userinactive']) < 60 * 60:
                            user_active_count += 1
                    except:
                        pass

                has_an_outage = False
                for service_name in service_versions:
                    if '*' in service_versions[service_name]:
                        has_an_outage = True
                if has_an_outage:
                    return_value['outages'] += 1

                if 's_logging_stuckkeys' in runner_report:
                    calc_stuckkeys_human = stuckkeys_report_to_human(runner_report['s_logging_stuckkeys'],
                                                                     key_definitions,
                                                                     threshold=s_threshold_for_stuck_keys)
                    if calc_stuckkeys_human:
                        stuck_keys += 1

                if 'temperature' in runner_report:
                    try:
                        if float(runner_report['temperature']) > 70.0:
                            high_temperature += 1
                    except:
                        pass

                if 'image' in runner_report:
                    if runner_report['image']:
                        try:
                            if int(runner_report['image'].split('.')[0]) > 1:
                                new_image_count += 1
                        except:
                            pass

                on_recent_service_pack_or_higher = False
                services_file = datadrop_save_path + id + '/' + 'service_versions'
                try:
                    with open(services_file, 'r') as f:
                        service_versions = json.loads(f.read())
                except:
                    service_versions = {}
                service_pack = rings.calculate_service_pack(service_versions, service_pack_list)
                if service_pack >= service_pack_key_recent:
                    recent_service_pack_count += 1
                    on_recent_service_pack_or_higher = True

                on_corp = False
                if 'ssid' in network_report:
                    if network_report['ssid'] == 'corp':
                        return_value['on_corp_count'] += 1
                        on_corp = True

                    if 'lan' in network_report:
                        if 'wlan' in network_report['lan']:
                            if 'cah-iot' == network_report['ssid']:
                                on_iot_count += 1

                if on_corp and not on_recent_service_pack_or_higher:
                    return_value['on_corp_and_not_service_pack_count'] += 1

                if 'brRsWd' in runner_report:
                    try:
                        if float(runner_report['brRsWd']) > 0.5:
                            return_value['browser_wants_reset'] += 1
                    except:
                        pass

                if 'load_report' in stats:
                    try:
                        if float(stats['load_report']) > 6.0:
                            return_value['load_report_high'] += 1
                        elif float(stats['load_report']) > 4.0:
                            return_value['load_report_warning'] += 1
                    except:
                        pass

                if 'Memory:MemFree' in runner_report:
                    try:
                        if float(runner_report['Memory:MemFree']) / 1000000000.0 < 0.5:
                            return_value['mem_free_low'] += 1
                    except:
                        pass

                if 'disk_use' in runner_report:
                    try:
                        if float(runner_report['disk_use']) > 75.0:
                            return_value['disk_warning'] += 1

                        if float(runner_report['disk_use']) > 90.0:
                            return_value['disk_high'] += 1
                            return_value['disk_warning'] -= 1
                    except:
                        pass

                if 's_logging_screenreport' in runner_report:
                    profile = datastore.get_value_stored(data_store_content, 'device_profile_' + id)
                    profile_screen_threshold = profiles.make_profile_screen_threshold(data_store_content, profile)
                    try:
                        if int(runner_report['s_logging_screenreport']) > profile_screen_threshold:
                            return_value['screen_threshold_exceeded'] += 1
                    except:
                        pass

            # ===================================================
            # These count for current and "not current" devices:
            # ===================================================
            if 'address' in checkin_report:
                site = address2location.location_stored(data_store_content, checkin_report['address'], returnName=False)
                if site == '(unassigned)':
                    return_value['location_warning'] += 1
            else:
                if 'address' in monitor_report:
                    site = location_stored(data_store_content, monitor_report['address'], returnName=False)
                    if site == '(unassigned)':
                        return_value['location_warning'] += 1


    return_value['current_count'] = current_count
    return_value['user_active_count'] = user_active_count
    return_value['total_count'] = total_count
    return_value['unassigned_management_ID'] = unassigned_management_ID
    return_value['stuck_keys'] = stuck_keys
    return_value['high_temperature'] = high_temperature

    if current_count:
        return_value['new_image_percent'] = 100.0 * float(new_image_count) / float(current_count)
        return_value['recent_service_pack_percent'] = 100.0 * float(recent_service_pack_count) / float(current_count)
        denominator = return_value['on_corp_count'] + float(on_iot_count)
        if denominator > 0:
            return_value['on_iot_percent'] = 100.0 * float(on_iot_count) / float(denominator)


    return return_value


# ====================================
def build_format_note(input_content, tab=4, width=25):
    # ====================================
    return_value = ''

    for line in input_content.split('\n'):
        if return_value:
            return_value += '\n'

        line_so_far = ''
        splits = line.split()
        for item in splits:
            if not line_so_far:
                line_so_far += ' ' * tab + item
            else:
                if len(line_so_far + ' ' + item) > width:
                    return_value += line_so_far + '\n'
                    line_so_far = ''
                    line_so_far += ' ' * tab
                    line_so_far += item
                else:
                    line_so_far += ' ' + item

        return_value += line_so_far

    return return_value


# ====================================
def clear_basic_settings_for_id(id, who):
    # ====================================

    for (the_setting, the_value) in s_all_basic_settings:
        datastore.set_value(the_setting + str(id), the_value, who=who)


# ====================================
def count_basic_settings_for_id(data_store_content, id):
    # ====================================
    the_count = 0

    for (the_setting, the_value) in s_all_basic_settings:
        the_full_setting = the_setting + str(id)
        if the_full_setting in data_store_content:
            if data_store_content[the_full_setting]:
                the_count += 1

    return the_count


# ====================================
def human_to_seconds(time_in_human):
    # ====================================
    seconds = 0
    sign = 1

    for item in time_in_human.split():
        if item == '-':
            sign = -1
        elif 's' in item:
            seconds += int(item.replace('s', ''))
        elif 'm' in item:
            seconds += 60 * int(item.replace('m', ''))
        elif 'h' in item:
            seconds += 60 * 60 * int(item.replace('h', ''))
        elif 'd' in item:
            seconds += 24 * 60 * 60 * int(item.replace('d', ''))

    return sign * seconds


# ====================================
def convert_seconds_to_human(time_in_seconds):
    # ====================================
    the_report = ''

    time_to_use = int(time_in_seconds)

    if time_to_use < 0:
        the_report += '-'
        time_to_use = abs(time_to_use)

    days = int(time_to_use / (3600 * 24))
    time_to_use -= days * 3600 * 24

    hours = int(time_to_use / 3600)
    time_to_use -= hours * 3600

    minutes = int(time_to_use / 60)
    time_to_use -= minutes * 60

    seconds = time_to_use % 60

    if days > 0:
        if the_report:
            the_report += ' '
        the_report += str(days) + 'd'

    if hours > 0:
        if the_report:
            the_report += ' '
        the_report += str(hours) + 'h'

    if minutes > 0:
        if the_report:
            the_report += ' '
        the_report += str(minutes) + 'm'

    if (not the_report) or (seconds > 0):
        if the_report:
            the_report += ' '
        the_report += str(seconds) + 's'

    return the_report


# ====================================
def get_filters_from_data_dictionary(data_dictionary):
    # ====================================
    # finds x=y and builds fast look up index
    return_value = {}
    for key in data_dictionary.keys():
        if 'filter' in data_dictionary[key]:
            if '=' in data_dictionary[key]['filter']:
                splits = data_dictionary[key]['filter'].split('=')
                return_value[splits[0]] = splits[1].replace('(', '').replace(')', '')
    return return_value


# ====================================
def get_profile_preliminary(profile):
    # ====================================
    return_value = ""

    try:
        profile_content = json.loads(datastore.get_value(profile))
        if 'preliminary' in profile_content:
            return_value = profile_content['preliminary']
    except:
        print(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))

    return str(return_value)


# ====================================
def get_profile_description(profile):
    # ====================================
    desc = ""

    try:
        profile_content = json.loads(datastore.get_value(profile))
        if 'description' in profile_content:
            desc = profile_content['description']
    except:
        print(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))

    return str(desc)


# ====================================
def is_feature_available(image_first_available, image_version):
    # ====================================
    return_value = True

    if image_version:
        if image_first_available > image_version:
            return_value = False

    else:
        return_value = False

    return return_value


# ====================================
def convert_serial_to_serialr(serial):
    # ====================================
    # make an alternate serial number formatting
    try:
        decimal = int(serial, 16)
        string = str(decimal)[::-1]
        build = ""
        count = -1
        for item in string:
            count += 1
            if count > 0:
                if count % 4 == 0:
                    build += '-'
            build += item
    except:
        build = 'error'
    return build


# ====================================
def make_body(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
    body = ''

    try:
        if 'REQUEST_METHOD' in environ:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        else:
            body, other = make_body_GET(environ)
    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
    action_report = ''

    body = ''

    body += 'method = POST'

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    # return str(d)

    the_who = login.get_current_user(environ)

    body += json.dumps(d)
    # method = POST{"serial": ["10000000e3669edf"], "the_selection": ["profile_selection"]}
    # method = POST{"profile": ["profile_MEX09-a"], "serial": ["10000000e3669edf"], "the_selection": ["profile_selection"]}

    # method = POST{"serial": ["10000000e3669edf"], "the_selection": ["service_version_selection"]}
    # method = POST{"service_pi_hmi": ["H.0.6"], "serial": ["10000000e3669edf"], "the_selection": ["service_version_selection"], "service_pi_runner": ["R.1.0"]}

    _ = """
???? make this be the source of all that is needed to show the options alos, so that the "prompt" that
        gets used on the display, can also be shown in the log, and then the datastore_prefix
        can be shown on parens as a detail
    """

    all_d_definitions = make_device_parameters()

    process_patterns = {}
    process_patterns['name_text_set'] = {'datastore_prefix':'device_name_'}
    process_patterns['tag_text_set'] = {'datastore_prefix':'device_tag_'}
    process_patterns['browser_timeout_selection'] = {'datastore_prefix':'device_browser_timeout_'}
    process_patterns['ring_assignment_selection'] = {'datastore_prefix':'device_ring_assignment_'}
    process_patterns['screen_scale_selection'] = {'datastore_prefix':'device_browser_zoom_'}
    process_patterns['screen_resolution_selection'] = {'datastore_prefix':'device_screen_resolution_'}
    process_patterns['special_menu_selection'] = {'datastore_prefix':'device_special_menu_'}
    process_patterns['reset_request_selection'] = {'datastore_prefix':'device_reset_request_'}
    process_patterns['reboot_request_selection'] = {'datastore_prefix':'device_reboot_request_'}
    process_patterns['screengrab_request_selection'] = {'datastore_prefix':'device_screengrab_request_'}
    process_patterns['osupdates_request_selection'] = {'datastore_prefix':'device_osupdates_request_'}
    process_patterns['device_clockview_selection'] = {'datastore_prefix':'device_clockview_'}
    process_patterns['wifi_connection_assignment_selection'] = {'datastore_prefix':'conf_wifi_connect_'}
    process_patterns['wifi_connection_hopper_selection'] = {'datastore_prefix':'conf_wifi_hopper_'}
    process_patterns['wifi_connection_coaster_selection'] = {'datastore_prefix':'conf_wifi_coaster_'}
    process_patterns['device_bluetooth_selection'] = {'datastore_prefix':'device_bluetooth_'}
    process_patterns['profile_selection'] = {'datastore_prefix':'device_profile_'}
    process_patterns['device_management_block'] = {'datastore_prefix':'device_managementID_'}
    process_patterns['extra_boots_selection'] = {'datastore_prefix':'device_extra_boots_'}
    process_patterns['retired_selection'] = {'datastore_prefix':'device_retired_'}

    if 'the_selection' in d:
        selection_key = str(d['the_selection'][0])
        if action_report:
            action_report += '\n'
        action_report += 'selection_key: ' + '"' + selection_key + '"'

        if selection_key in process_patterns:
            try:
                for id in d['serial']:
                    value_to_use = ''
                    key_to_use = 'selected_parameter'
                    if key_to_use in d:
                        value_to_use = d[key_to_use][0]
                        replacers = [("\\", ""),("<", ""),(">",""),("|", "<br>")]
                        if 'replacers' in process_patterns[selection_key]:
                            replacers = process_patterns[selection_key]['replacers']
                        for replacer_pair in replacers:
                            value_to_use = value_to_use.replace(replacer_pair[0],replacer_pair[1])
                    data_store_value_name = process_patterns[selection_key]['datastore_prefix'] + str(id)
                    previous_value = datastore.get_value(data_store_value_name)

#                    if previous_value != value_to_use:
                    if True:
                        prompt_name = ''
                        for search_item in all_d_definitions:
                            if process_patterns[selection_key]['datastore_prefix'] == search_item['datastore_prefix']:
                                prompt_name = '"' + search_item['title'].replace('<br>','->') + '"' + ' '
                                break

                        if action_report:
                            action_report += '\n'
                        action_report += 'setting ' + prompt_name + \
                                         '(' + process_patterns[selection_key]['datastore_prefix'] + ')' + \
                                         ' for device id ' + '"' + str(id) + '"' + \
                                         ' from ' + '"' + previous_value + '"' + \
                                         ' to ' + '"' + value_to_use + '"'
                        datastore.set_value(data_store_value_name, value_to_use, who=the_who)

                    if 'browser_reset' in d: # typically only accompanies the 'profile_selection'
                        if d['browser_reset'][0] == 'browser_reset':
                            value_to_use = get_browser_reset_value(id)
                            if action_report:
                                action_report += '\n'
                            action_report += 'setting ' + 'browser_reset' + ' for device id ' + '"' + str(id) + '"'
                            datastore.set_value('device_reset_request_' + str(id), str(value_to_use), who=the_who)
            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

            _ = """
        # this one is already covered by the patterns, just keep it as an old example
        elif 'name_text_set' == selection_key:
            try:
                for id in d['serial']:
                    name_to_use = ''
                    if 'selected_parameter' in d:
                        # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                        name_to_use = d['selected_parameter'][0].replace("\\", "").replace("<", "").replace(">","").replace("|", "<br>")

                    datastore.set_value('device_name_' + str(id), name_to_use, who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other
"""

        elif 'service_version_push_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_values = {}
                for item in d:
                    if "service_pi_" in item:
                        the_values[item.replace('service_pi_', 'pi_')] = str(d[item][0])

                action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])
                action_report += '\n' + 'version(s) : ' + \
                    make_json_dumps_aligned(json.dumps(the_values, indent=4, separators=(',', ':')))

                for id in d['serial']:
                    datastore.set_value('device_service_push_' + str(id),
                                        json.dumps(the_values, indent=4, separators=(',', ':')), who=the_who)
                    tasks.build_one_request({"task": "push", "serial": id, "what": the_values})
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'service_version_pull_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_values = {}
                for item in d:
                    if "service_pi_" in item:
                        the_values[item.replace('service_pi_', 'pi_')] = str(d[item][0])

                action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])
                action_report += '\n' + 'version(s) : ' + \
                    make_json_dumps_aligned(json.dumps(the_values, indent=4, separators=(',', ':')))

                for id in d['serial']:
                    for item in the_values:
                        datastore.set_value('device_service_request_' + str(id) + '_last_' + item, the_values[item],
                                            who=the_who)
                        datastore.set_value('device_service_request_' + str(id) + '_pull_' + item, the_values[item],
                                            who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'service_ring_pull_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_ring = ''
                for item in d:
                    if "ring_selected" in item:
                        the_ring = str(d[item][0])

                the_values = {}
                if the_ring:
                    services_available = codeupload.get_pi_services()
                    service_names = services_available.keys()
                    for service_name in sorted(service_names):
                        ring_assigned_version = datastore.get_value(
                            'device_ringed_service' + the_ring + '_' + service_name)
                        if ring_assigned_version:
                            the_values[service_name] = ring_assigned_version

                if the_values:
                    action_report += '\n' + 'set to ring: ' + str(the_ring) + ' services'
                    action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])
                    action_report += '\n' + 'version(s) : ' + \
                        make_json_dumps_aligned(json.dumps(the_values, indent=4, separators=(',', ':')))

                    for id in d['serial']:
                        for item in the_values:
                            datastore.set_value('device_service_request_' + str(id) + '_last_' + item, the_values[item],
                                                who=the_who)
                            datastore.set_value('device_service_request_' + str(id) + '_pull_' + item, the_values[item],
                                                who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other


        elif 'service_spr_pull_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_ring = ''
                for item in d:
                    if "spr_selected" in item:
                        the_spr = str(d[item][0])

                the_values = {}
                if the_spr:
                    if '_' in the_spr:
                        the_ring = the_spr.split('_')[1]
                        services_available = codeupload.get_pi_services()
                        service_names = services_available.keys()
                        for service_name in sorted(service_names):
                            ring_assigned_version = datastore.get_value(
                                'device_ringed_service' + the_ring + '_' + service_name)
                            if ring_assigned_version:
                                the_values[service_name] = ring_assigned_version
                    else:
                        # just go by the service pack
                        content_of_versions = rings.get_content_of_versions()
                        service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
                        if the_spr in service_pack_list:
                            for service_name in service_pack_list[the_spr]:
                                the_values[service_name] = service_pack_list[the_spr][service_name]

                # debug
                # return json.dumps(the_values)

                if the_values:
                    action_report += '\n' + 'set to service pack: ' + str(the_spr) + ' services'
                    action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])
                    action_report += '\n' + 'version(s) : ' + \
                        make_json_dumps_aligned(json.dumps(the_values, indent=4, separators=(',', ':')))

                    for id in d['serial']:
                        for item in the_values:
                            datastore.set_value('device_service_request_' + str(id) + '_last_' + item, the_values[item],
                                                who=the_who)
                            datastore.set_value('device_service_request_' + str(id) + '_pull_' + item, the_values[item],
                                                who=the_who)
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'request_collection_browser_reset' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key
            action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])

            try:
                reset_option_selected = ''
                for item in d:
                    if "reset_option_selected" in item:
                        reset_option_selected = str(d[item][0])

                if reset_option_selected == 'yes':
                    for id in d['serial']:
                        # get the image and boot info
                        the_value = get_browser_reset_value(id)

                        # set the value
                        datastore.set_value('device_reset_request_' + str(id), str(the_value), who=the_who)

            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'request_collection_name_assignment' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key
            action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])

            try:
                text_entered = ''
                for item in d:
                    if "request_collection_name_text" in item:
                        text_entered = str(d[item][0])

                for id in d['serial']:
                    the_value = text_entered

                    # set the value
                    datastore.set_value('device_collection_' + str(id), str(the_value), who=the_who)

            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other


        elif 'request_collection_device_command' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key
            action_report += '\n' + 'to id(s): ' + ','.join(d['serial'])

            try:
                command_entered = ''
                command_timeout = ''
                for item in d:
                    if "request_collection_device_command_text" in item:
                        command_entered = str(d[item][0])
                    if "request_collection_device_command_timeout" in item:
                        command_timeout = str(d[item][0])

                action_report += '\n' + 'cmd: ' + '"' + command_entered + '"'
                action_report += '\n' + 'timeout: ' + '"' + command_timeout + '"'

                if command_entered:
                    command_number = devicecommand.command_save(command_entered, command_timeout, d['serial'])

                    return_string = ''

                    return_string += '<br><br>'

                    for id in d['serial']:
                        return_string += id + ','
                    return_string += 'command_entered: ' + command_entered + ' -> ' + str(
                        command_number) + ' timeout: ' + command_timeout

                    return_string += '<br><br>'
                    return_string += '<a href="/devicecommand?cmd=' + str(command_number) + ',style=side,col=pass_investigate"  target="_blank">Click to open command response in a new tab</a>'

                    return_string += '<br><br>'
                    return_string += '(Reload this page, to get the collection back)'

                    other['action_report'] = action_report
                    return return_string, other

            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'device_comment_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_values = {}
                for item in d:
                    if "device_comment_text" in item:
                        the_values['comment'] = str(d[item][0])
                    if "when_did_it_happpen" in item:
                        the_values['when'] = str(d[item][0])
                    if "device_comment_time" in item:
                        the_values['localtime'] = str(d[item][0])

                the_values['who'] = str(the_who)

                TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
                # 20210406201829131704

                for id in d['serial']:
                    datastore.set_value('device_comment_' + str(id) + '_' + TS + '_json',
                                        json.dumps(the_values, indent=4, separators=(',', ':')), who=the_who)

            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other


        elif 'device_comment_review_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                the_values = {}
                device_comment_review_name = ''
                for item in d:
                    if "device_comment_review_text" in item:
                        the_values['review'] = str(d[item][0])

                    if "device_comment_review_name" in item:
                        device_comment_review_name = str(d[item][0])

                the_values['who'] = str(the_who)

                if device_comment_review_name:
                    for id in d['serial']:
                        datastore.set_value(device_comment_review_name,
                                            json.dumps(the_values, indent=4, separators=(',', ':')), who=the_who)

            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'all_settings_reset' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                for id in d['serial']:
                    clear_basic_settings_for_id(id, who=the_who)
            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

        elif 'collection_selection' == selection_key:
            if action_report:
                action_report += '\n'
            action_report += 'action: ' + selection_key

            try:
                return build_collection_report(d, environ), other
            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

            _old = """


        elif 'datamine_user_active_collection_selection' == selection_key:
            try:
                plot_selected = ''
                for item in d:
                    if "plot_selected" in item:
                        plot_selected = str(d[item][0])

                if plot_selected:
                    return json.dumps(d['serial'])
                    # https://slicer.cardinalhealth.net/datamine?serial=100000000bf70e2b+100000000bf70e2b,runner_raw=s_logging_userinactive,view=summary,plot_algorithm=value_less_than_360,minimum=,maximum=,scale=,to_plot_as_number=
#                    for id in d['serial']:

            except:
                pass
                return str(traceback.format_exc().replace("\n","<br>").replace("\"","'"))
"""


        else:
            return ("selection not found: " + selection_key), other

    else:
        return ("missing a selection"), other

    body, other = make_body_GET(environ)
    other['action_report'] = action_report
    return body, other


# ====================================
def make_new_filter(original_filter='', new_terms='', rotate_terms='', rotate_key='', items_to_puncture=''):
    # ====================================

    return_dictionary = {}

    original_primary_key = ''
    new_primary_key = ''

    splits = original_filter.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            return_dictionary[pieces[0]] = pieces[1]
            if rotate_key:
                if pieces[0] == rotate_key:
                    original_primary_key = pieces[1]

    splits = new_terms.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            return_dictionary[pieces[0]] = pieces[1]
            if rotate_key:
                if pieces[0] == rotate_key:
                    new_primary_key = pieces[1]

    # look to do the rotation
    splits = rotate_terms.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            options = pieces[1].split('|')
            current_rotation = ''
            if pieces[0] in return_dictionary:
                current_rotation = return_dictionary[pieces[0]]

            if rotate_key and new_primary_key != original_primary_key:
                # check to see if this primary key has changed, and if so, then clear, in order to get the default
                return_dictionary[pieces[0]] = options[0]  # the first one is the default
            else:
                # do the rotation
                if current_rotation in options:
                    if current_rotation == options[0]:
                        return_dictionary[pieces[0]] = options[1]
                    else:
                        return_dictionary[pieces[0]] = options[0]
                else:
                    return_dictionary[pieces[0]] = options[0]  # the first one is the default

    splits = items_to_puncture.split(',')
    for item in splits:
        if item in return_dictionary:
            del return_dictionary[item]

    # build the result
    return_filter = ''
    for key in sorted(return_dictionary):  # sort only to make the test cases simpler to write
        if return_filter:
            return_filter += ','
        return_filter += key + '=' + return_dictionary[key]

    return return_filter


# ====================================
def get_profile_counts(ids, data_store_content):
    # ====================================
    return_value = {}

    for id_value in ids:
        key = 'device_profile_' + id_value
        if key in data_store_content:
            profile_name = data_store_content[key]
            if not profile_name in return_value:
                return_value[profile_name] = 0
            return_value[profile_name] += 1

    return return_value


# ====================================
def build_whitelist_report(environ):
    # ====================================
    body = ''

    try:
        ids = get_all_reported_ids()

        whitelist_urls = {}

        body += '<center>'
        body += 'whitelist report'
        body += '<br>'
        body += '<br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'url'
        body += '</td>'
        body += '<td>'
        body += 'devices with this in the whitelist'
        body += '</td>'
        body += '</tr>'

        for id in sorted(ids):
            profile = datastore.get_value('device_profile_' + id)
            if profile:
                white_list_items_in_this_device = {}
                response_d = json.loads(datastore.get_value(profile))
                # response_d = {"timezone": "America/Denver", "bookmarks": {"(id)": {"1": {"url": "https://mex03-dms.cardinalhealth.net/?linea=124&celula=CLA", "autolaunch": true, "whitelist": ["mex03-dms.cardinalhealth.net", "cdn.datatables.net", "cdn.jsdelivr.net", "cdnjs.cloudflare.com", "code.jquery.com", "gateway.zscalertwo.net"], "title": "line 124"}}}}
                try:
                    bookmarks = response_d['bookmarks']
                    lines = bookmarks[
                        '(id)']  # {"1": {"url": "https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login", "logo": "cordis", "whitelist": ["edhr-na-jz.cardinalhealth.net"], "title": "MES V8"}}
                    for line_item in lines:
                        for url_item in lines[line_item]['whitelist']:
                            if url_item:
                                if not url_item in white_list_items_in_this_device:
                                    white_list_items_in_this_device[str(url_item)] = 0
                                white_list_items_in_this_device[str(url_item)] += 1

                    for url_item in white_list_items_in_this_device:
                        if url_item:
                            if not url_item in whitelist_urls:
                                whitelist_urls[str(url_item)] = 0
                            whitelist_urls[str(url_item)] += 1
                except:
                    pass

        for whitelist_url in sorted(whitelist_urls):
            body += '<tr>'
            body += '<td>'
            body += whitelist_url
            body += '</td>'
            body += '<td>'
            body += str(whitelist_urls[whitelist_url])
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body


# ====================================
def make_body_GET(environ):
    # ====================================
    try:
        return do_make_body_GET(environ)
    except:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = (str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))
        return body, other


# ====================================
def update_sub_time(sub_times, name):
    # ====================================
    time_now = time.time()

    if 'last_call_name' in sub_times:
        if not sub_times['last_call_name'] in sub_times:
            sub_times[sub_times['last_call_name']] = 0

        sub_times[sub_times['last_call_name']] += time_now - sub_times['last_call_time']

    sub_times['last_call_name'] = name
    sub_times['last_call_time'] = time_now


# ====================================
def do_make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    query = ''
    if 'QUERY_STRING' in environ:
        query = environ['QUERY_STRING']

    REQUEST_URI = ''
    REQUEST_FILTER = ''

    if 'REQUEST_URI' in environ:
        REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]

        if '?' in environ['REQUEST_URI']:
            REQUEST_FILTER = str(environ['REQUEST_URI']).split('?')[1]

    time_stamps = []
    sub_times = {}

    time_stamps.append(['start', time.time()])

    data_dictionary_complete = datamine.build_data_dictionary(s_seconds_for_current)
    data_dictionary = data_dictionary_complete['data_items']
    s_all_pi_services = data_dictionary_complete['s_all_pi_services']

    data_dictionary_filters = get_filters_from_data_dictionary(data_dictionary)

    data_store_content = datastore.all_datastore()
    time_stamps.append(['got datastore', time.time()])

    permit_to_device_edit = permissions.permission_allowed(environ, 'device_edit')
    permit_to_locations_all = permissions.permission_site_allowed(environ, '(all)')
    time_stamps.append(['got common permissions', time.time()])
    all_locations = address2location.get_all_locations_stored(data_store_content)

    data_store_content_address2location_site = {}
    device_comments = {}
    for item in data_store_content:
        if 'device_comment_' in item:
            device_comments[item] = data_store_content[item]
        if 'address2location_site_' in item:
            data_store_content_address2location_site[item] = data_store_content[item]

    management_id_d = {}
    for siteid in management.get_management_block_list(data_store_content):
        management_id_d[siteid] = True
    management_id_d['(retired)'] = True
    management_id_d['(all)'] = True

    for siteid in all_locations:
        management_id_d[siteid] = True

    site_permissions = {}
    for site in management_id_d.keys():
        site_permissions[site] = permissions.permission_site_allowed(environ, site)



    csv_body = ''
    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    the_who = login.get_current_user(environ)

    if the_who:
        name_to_show = "Home (logged in as " + the_who + ")"
    else:
        name_to_show = "Home (not logged in)"

    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    rings_vs_service_pack = get_rings_vs_service_pack()

    content_of_versions = rings.get_content_of_versions()
    service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
    service_pack_release_notes = rings.extract_service_pack_release_notes(content_of_versions)
    try:
        service_pack_highest = sorted(service_pack_release_notes.keys())[-1]
    except:
        service_pack_highest = ''
    try:
        services_available = rings.get_services_from_list_of_sp(rings_vs_service_pack, service_pack_list)
    except:
        services_available = codeupload.get_pi_services()

    try:
        with open('/dev/shm/rings_report.txt', 'r') as f:
            rings_report = json.loads(f.read())
    except:
        rings_report = {}

    query_items = {}
    query_items_order = []
    for item in query.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]
            query_items_order.append(parms[0])

    summary_view = '(main)'
    if 'summary_view' in query_items:
        summary_view = query_items['summary_view']

    if summary_view == '(reset)':
        summary_view = '(main)'
        REQUEST_FILTER = ''
        query_items = {}
        query_items_order = []
    elif summary_view == '(whitelist)':
        return build_whitelist_report(environ), other

    device_view = '(main)'
    if 'device_view' in query_items:
        device_view = query_items['device_view']

    refresh = ''
    if 'refresh' in query_items:
        refresh = query_items['refresh']
        body += '<head>'
        body += '<meta http-equiv="refresh" content="' + refresh + '" >'
        body += '</head>'

    devices_managed_only = False
    if 'devices_managed' in query_items:
        if query_items['devices_managed'] == 'True':
            devices_managed_only = True

    time_stamps.append(['ready', time.time()])

    try:
        show_summary = True
        ids = {}
        time_now = time.time()

        id_files = get_all_reported_ids()

        if 'serial' in query_items:
            single_id = query_items['serial']
            if not single_id in id_files:  # like for entering an IP address instead of an actual id
                id_files.append(single_id)
        time_stamps.append(['got serials', time.time()])

        time_stamps.append(['got device_comments', time.time()])

        permit2_id = {}
        key_definitions = get_key_definitions()

        for id in id_files:
            update_sub_time(sub_times, '010-checkin')

            # need to do timing based on check points within this loop

            checkin_file = checkin_file_root + '/' + id
            try:
                with open(checkin_file, 'r') as f:
                    checkin = json.loads(f.read())
            except:
                checkin = {}

            update_sub_time(sub_times, '020-runner')
            runner_file = datadrop_save_path + id + '/' + 'runner'
            try:
                with open(runner_file, 'r') as f:
                    runner_report = json.loads(f.read())
            except:
                runner_report = {}

            update_sub_time(sub_times, '020-services')
            services_file = datadrop_save_path + id + '/' + 'service_versions'
            try:
                with open(services_file, 'r') as f:
                    service_versions = json.loads(f.read())
            except:
                service_versions = {}

            update_sub_time(sub_times, '030-built')
            # build up this ones content (tag:20220404a)
            built = {}

            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            # defaults
            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            built['screen'] = ''
            built['screen_reported'] = ''
            built['uptime'] = '0'
            built['browseruptime'] = '0'
            built['temperature'] = ''
            built['timediff'] = ''
            built['boot_count'] = ''
            #            built['loadavg'] = ''
            built['disk_use'] = ''
            built['disk_size'] = ''
            built['inode_use'] = ''
            built['screengrab_count'] = ''
            built['calc_seconds'] = '(missing)'
            built['calc_counts'] = '0'
            built['calc_runner_seconds'] = '(missing)'
            built['security_index'] = ''
            built['ring'] = ''
            built['service_pack'] = ''
            built['tag'] = ''
            built['device_collection'] = ''
            built['ring_updates'] = ''
            built['wlan0mac'] = ''
            built['wlan1mac'] = ''
            built['oldestreport'] = ''
            built['calc_user_can_do_service_updates_full'] = ''
            built['outage'] = ''
            built['device_extra_boots'] = ''
            built['device_retired'] = ''

            # task built stats_ in datadrop: (like: stats_load_report)
            stats_file = statistics_save_path + id
            stats = {}
            try:
                stats = json.loads(open(stats_file, 'r').read())
            except:
                pass

            for stats_key in stats.keys():
                built['stats_' + stats_key] = str(stats[stats_key])

            for data_item in data_dictionary.keys():
                if 'source' in data_dictionary[data_item]:
                    if 'default' in data_dictionary[data_item]:
                        built[data_dictionary[data_item]['source']] = data_dictionary[data_item]['default']
                    else:
                        built[data_dictionary[data_item]['source']] = ''
                else:
                    built[data_item] = ''

            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            # get anything that is in the datastore for this one
            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            built['id'] = id
            built['IP'] = '(missing)'
            built['monitor_version'] = '(missing)'
            built['datadrop_runner_image_version'] = ''

            built['my_device_comment_keys'] = []
            built['my_device_comment_has_review_keys'] = []

            update_sub_time(sub_times, '035-built_from_datastore')

            if True:
                item = 'device_name_' + id
                where = 'name'
                if item in data_store_content:
                    built[where] = data_store_content[item]

                item = 'device_first_date_' + id
                where = 'first_date'
                if item in data_store_content:
                    built[where] = data_store_content[item]

                item = 'device_first_iot_date_' + id
                where = 'first_iot_date'
                if item in data_store_content:
                    built[where] = data_store_content[item]

                for item in device_comments:
                    if ('device_comment_' + id in item) and ('_json' in item):
                        built['my_device_comment_keys'].append(item)

                    if ('device_comment_' + id in item) and ('_review' in item):
                        try:
                            the_review = json.loads(data_store_content[item])
                            if 'review' in the_review:
                                if the_review['review']:
                                    # a non-empty review string, count it
                                    built['my_device_comment_has_review_keys'].append(item)
                        except:
                            pass

            else:
                for item in data_store_content:
                    if ('device_name_' + id in item):
                        built['name'] = data_store_content[item]

                    if ('device_comment_' + id in item) and ('_json' in item):
                        built['my_device_comment_keys'].append(item)

                    if ('device_comment_' + id in item) and ('_review' in item):
                        try:
                            the_review = json.loads(data_store_content[item])
                            if 'review' in the_review:
                                if the_review['review']:
                                    # a non-empty review string, count it
                                    built['my_device_comment_has_review_keys'].append(item)
                        except:
                            pass

            update_sub_time(sub_times, '037a-built_from_services')

            for service_name in s_all_pi_services:
                built['service_versions_' + service_name + '_version'] = ''
                built['service_versions_' + service_name + '_version:color'] = ''

            built['settings_count'] = count_basic_settings_for_id(data_store_content, id)

            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            # done with defaults, start filling in data by calculated results
            # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            update_sub_time(sub_times, '037b-built_from_services')
            built['idr'] = convert_serial_to_serialr(id)

            update_sub_time(sub_times, '037c-built_from_services')
            pre_load_ring = ''
            if 'device_ring_assignment_' + id in data_store_content:
                pre_load_ring = data_store_content['device_ring_assignment_' + id]
            built['ring'] = rings.device_ring_calculated(id, pre_load_ring)
            if not built['ring']:
                built['ring'] = '3'

            update_sub_time(sub_times, '037d-built_from_services')
            if id in rings_report:
                built['ring_updates'] = str(len(rings_report[id]))

            update_sub_time(sub_times, '039-built_from_checkin')

            # Pull data from the monitor checkin (includes the uptime value, but only at 30 minute intervals)
            for key in checkin:
                built[key] = checkin[key]  # built['address'] gets loaded here?

            if 'address' in checkin:
                built['IP'] = checkin['address']

            # get address from datadrop as a "fresher" reading
            if 'address' in runner_report:
                built['IP'] = runner_report['address']

            # 2022.02.21
            if id[:3] == '10.':
                built['address'] = id

            # Pull data from the runner datadrop (most recent)
            update_sub_time(sub_times, '040-datadrop_list')
            datadrop_directory = datadrop_save_path + id + '/'

            #            service_versions
            for service_name in s_all_pi_services:
                if service_name in service_versions:
                    built['service_versions_' + service_name + '_version'] = service_versions[service_name]

                    if '*' in service_versions[service_name]:
                        try:
                            built['outage'] = str(int(built['outage']) + 1)
                        except:
                            built['outage'] = '1'

                    if id in rings_report:
                        if service_name in rings_report[id]:
                            built['service_versions_' + service_name + '_version:color'] = config.color(
                                'color_update_highlight')
            built['service_pack'] = rings.calculate_service_pack(service_versions, service_pack_list)

            built['oldestreport'] = '0'
            time_of_scan = time.time()
            runner_d = {}
            try:
                update_sub_time(sub_times, '041-datadrop_os')
                if os.path.isdir(datadrop_directory):
                    datadrop_items = os.listdir(datadrop_directory)
                else:
                    datadrop_items = []
                update_sub_time(sub_times, '042-datadrop_items')
                for item in sorted(datadrop_items):
                    file_to_use = datadrop_directory + item
                    try:
                        fileStatsObj = os.stat(file_to_use)
                    except:
                        # it might have been deleted already (temp files used for atomic writes)
                        fileStatsObj = None

                    if fileStatsObj:
                        last_modified_time = fileStatsObj[stat.ST_MTIME]
                        age_of_file = time_of_scan - last_modified_time

                        if age_of_file > 60 * 60 * 24:
                            try:
                                # os.remove(file_to_use) # This removes stale datadrop reports, as well as the version summary
                                # 2022.06.24: Liza requests that it no longer forget...
                                pass
                            except:
                                pass
                            age_of_file = 0

                        if age_of_file > int(built['oldestreport']):
                            built['oldestreport'] = str(int(age_of_file))
                            built['oldestreport'] = '0' * (8 - len(built['oldestreport'])) + built['oldestreport']

                    if item == 'security':
                        update_sub_time(sub_times, '043-datadrop_security')
                        try:
                            content = ''
                            with open(datadrop_directory + item, 'r') as f:
                                content = f.read()
                            the_d = json.loads(content)
                            if 'hardening_index' in the_d:
                                built['security_index'] = the_d['hardening_index']
                        except:
                            pass
                    elif item == 'runner':
                        update_sub_time(sub_times, '044a-datadrop_runner')
                        try:
                            content = '{}'
                            try:
                                with open(datadrop_directory + item, 'r') as f:
                                    content = f.read()
                            except:
                                pass
                            runner_d = json.loads(content)
                            update_sub_time(sub_times, '044b-datadrop_runner')

                            if 'image' in runner_d:
                                built['datadrop_runner_image_version'] = runner_d['image']

                            update_sub_time(sub_times, '044d-datadrop_runner')

                            if 'screen_width' in runner_d and 'screen_height' in runner_d:
                                built['screen'] = runner_d['screen_width'] + 'x' + runner_d['screen_height']
                            if 'screen_geometry' in runner_d:
                                built['screen_reported'] = runner_d['screen_geometry']

                            if 'uptime' in runner_d:  # If runner is giving us uptime, then pull it here, to get it updated once a minute
                                built['uptime'] = runner_d['uptime']
                            if 'browseruptime' in runner_d:  # If runner is giving us uptime, then pull it here, to get it updated once a minute
                                built['browseruptime'] = runner_d['browseruptime']
                            if 'boot_count' in runner_d:
                                built['boot_count'] = runner_d['boot_count']
                            #                            if 'loadavg' in runner_d:
                            #                                built['loadavg'] = runner_d['loadavg']
                            if 'disk_use' in runner_d:
                                built['disk_use'] = runner_d['disk_use']
                            if 'one_K' in runner_d:
                                try:
                                    built['disk_size'] = "{0:.1f}".format(float(runner_d['one_K']) * 0.000001)
                                except:
                                    pass
                            if 'inode_use' in runner_d:
                                built['inode_use'] = runner_d['inode_use']
                            if 'screengrab_count' in runner_d:
                                built['screengrab_count'] = runner_d['screengrab_count']
                            if 'temperature' in runner_d:
                                built['temperature'] = runner_d['temperature']
                            if 'timediff' in runner_d:
                                built['timediff'] = runner_d['timediff']

                            update_sub_time(sub_times, '044e-datadrop_runner')

                            for key in sorted(data_dictionary.keys()):
                                data_item = data_dictionary[key]

                                if 'runner_raw' in data_item:
                                    if data_item['runner_raw'] in runner_d:
                                        # default is a copy
                                        built[key] = runner_d[data_item['runner_raw']]  # 'address' gets loaded here?
                                        if 'parse' in data_item:
                                            # 0.13_0.19_0.23_1/262_26560 # one, five, and 15 minutes.
                                            if data_item['parse'] == 'loadavg_parse':
                                                built[key] = runner_d[data_item['runner_raw']].split('_')[0]

                                            if data_item['parse'] == 'time_since':
                                                delta = '(no time given)'
                                                try:
                                                    time_given = int(runner_d[data_item['runner_raw']])
                                                    if time_given:  # the default from the device is a zero
                                                        delta = str(int(time_of_scan - time_given))

                                                        if 'format' in data_item:
                                                            if data_item['format'] == 'human_time':
                                                                delta = convert_seconds_to_human(delta)
                                                except:
                                                    pass

                                                built[key] = delta

                                # return '(' + datadrop_directory + item + ')  ' + json.dumps(runner_d)
                                if 'service_versions' in data_item:
                                    if data_item['service_versions'] in service_versions:
                                        built[key] = service_versions[data_item['service_versions']]

                            if 'wlan0mac' in runner_d:
                                built['wlan0mac'] = format_mac_address(runner_d['wlan0mac'])
                            if 'wlan1mac' in runner_d:
                                built['wlan1mac'] = format_mac_address(runner_d['wlan1mac'])

                        except:
                            return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other
                            pass
                    else:
                        update_sub_time(sub_times, '045-datadrop_other')
                update_sub_time(sub_times, '046-datadrop_end')
            except:
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other
                pass

            update_sub_time(sub_times, '047-location')
            #            built['calc_site'], built['calc_site_name'] = address2location.location_stored(data_store_content_address2location_site, built['address'], True)
            built['calc_site'], built['calc_site_name'] = address2location.location_stored(
                data_store_content_address2location_site, built['IP'], True)

            if True:
                built['device_retired'] = datastore.get_value_stored(data_store_content, 'device_retired_' + id)

                # built['calc_management_block'] = built['calc_site']
                built['calc_management_block'] = datastore.get_value_stored(data_store_content, 'device_managementID_' + id)
                if not built['calc_management_block']:
                    built['calc_management_block'] = '(unassigned)'

                if built['device_retired'] == 'yes':
                    built['calc_management_block'] = '(retired)'

                management_block = built['calc_management_block']
                device_management = management_block

            if True:
                user_site = False
                site = str(built['calc_site'])
                if site in site_permissions:
                    user_site = site_permissions[site]

                user_management = False
                if management_block in site_permissions:
                    user_management = site_permissions[management_block]
                permit2 = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
            else:
                permit2 = False
                if s_do_use_management_id:
                    if management_block:
                        id_to_use = management_block
                    else:
                        id_to_use = str(built['calc_site'])

                    if id_to_use in site_permissions:
                        permit2 = site_permissions[id_to_use] or permit_to_locations_all
                    else:
                        permit2 = permit_to_locations_all
                else:
                    # if site, or tag allowed by user, then mark it
                    site = str(built['calc_site'])
                    update_sub_time(sub_times, '049-permissions2')
                    if site in site_permissions:
                        update_sub_time(sub_times, '049-permissions2b')
                        permit2 = site_permissions[site] or permit_to_locations_all
                    else:
                        update_sub_time(sub_times, '049-permissions2c')
                        permit2 = permit_to_locations_all

            permit2_id[id] = permit2
            update_sub_time(sub_times, '049-permissions4')

            update_sub_time(sub_times, '048-tag')
            built['tag'] = datastore.get_value_stored(data_store_content, 'device_tag_' + id)

            built['device_collection'] = datastore.get_value_stored(data_store_content, 'device_collection_' + id)
            built['device_extra_boots'] = datastore.get_value_stored(data_store_content, 'device_extra_boots_' + id)

            update_sub_time(sub_times, '049-permissions')
            built['calc_user_can_do_service_updates_full'] = ''

            if permit_to_device_edit and permit2:
                built['calc_user_can_do_service_updates_full'] = 'yes'
            update_sub_time(sub_times, '049-permissions5')

            update_sub_time(sub_times, '051-seconds')
            if 'time' in runner_report:
                built['calc_runner_seconds'] = str(int(time_now - float(runner_report['time'])))

            if 'time' in checkin:
                built['calc_seconds'] = str(int(time_now - float(checkin['time'])))

                try:
                    if int(str(built['calc_runner_seconds'])) < int(str(built['calc_seconds'])):
                        built['calc_seconds'] = str(built['calc_runner_seconds'])
                except:
                    pass

            try:
                built['calc_seconds_human'] = convert_seconds_to_human(int(built['calc_seconds']))
            except:
                built['calc_seconds_human'] = ''

            try:
                built['calc_stuckkeys_human'] = stuckkeys_report_to_human(built['stuckkeys'], key_definitions,
                                                                          threshold=s_threshold_for_stuck_keys)

                # runner_report
            except:
                built['calc_stuckkeys_human'] = ''

            update_sub_time(sub_times, '053-network')
            built['ssid_chan_sig'] = ''
            built['ssid'] = ''
            built['lan'] = ''
            built['calc-iot'] = ''

            built['bluetooth'] = str(datastore.get_value_stored(data_store_content, 'device_bluetooth_' + id))
            built['Menu'] = str(datastore.get_value_stored(data_store_content, 'device_special_menu_' + id))
            built['forced'] = str(datastore.get_value_stored(data_store_content, 'device_screen_resolution_' + id))
            built['zoom'] = str(datastore.get_value_stored(data_store_content, 'device_browser_zoom_' + id))

            network_data_file = datadrop_save_path + '/' + id + '/' + 'network'
            try:
                with open(network_data_file, 'r') as f:
                    network_data = json.loads(f.read())

                    update_sub_time(sub_times, '053b-network')
                    if 'lan' in network_data:
                        built['lan'] = network_data['lan']

                    if 'ssid' in network_data:
                        built['ssid_chan_sig'] = network_data['ssid']
                        if network_data['chan'] or network_data['signal']:
                            if network_data['ssid'] == '--':
                                built['ssid_chan_sig'] = '(hidden)' + '-' + network_data['chan'] + '-' + network_data[
                                    'signal']
                            else:
                                built['ssid_chan_sig'] = network_data['ssid'] + '-' + network_data['chan'] + '-' + \
                                                         network_data['signal']

                        built['ssid'] = get_ssid_from_dash_string(network_data['ssid'])

                    if 'cah-iot' in network_data:
                        built['calc-iot'] = network_data['cah-iot']

            except:
                pass

            update_sub_time(sub_times, '055-profile')
            built['profile'] = datastore.get_value_stored(data_store_content, 'device_profile_' + id)
            built['profile_screen_threshold'] = profiles.make_profile_screen_threshold(data_store_content, built['profile'])

            built['screen_over_threshold'] = 0
            if 'ScreenReport' in built:
                try:
                    if int(built['ScreenReport']) > built['profile_screen_threshold']:
                        built['screen_over_threshold'] = 1
                except:
                    built['screen_over_threshold'] = -1
            else:
                built['screen_over_threshold'] = -1

            built['hopper'] = datastore.get_value_stored(data_store_content, 'conf_wifi_hopper_' + id)
            built['coaster'] = datastore.get_value_stored(data_store_content, 'conf_wifi_coaster_' + id)

            built['inspect_comms'] = comms_report(
                {'monitor_time': built['calc_seconds'], 'runner_time': built['calc_runner_seconds']})

            update_sub_time(sub_times, '059-filter')

            # --------------------------------------------
            # now filter based on the built information
            # --------------------------------------------
            if query_items:
                to_be_included_overall = True

                # Do them in the order of the url had them.
                operation = 'and'
                for item_to_test in query_items_order:
                    item_tested = False
                    to_be_included = True

                    # tag: Implement Filters here

                    # Keep these alphabetical
                    if 'brRsWd' == item_to_test:
                        item_tested = True
                        if query_items['brRsWd'] == built['BrowserRestartWanted']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'image' == item_to_test:
                        item_tested = True
                        if query_items['image'] == built['datadrop_runner_image_version']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'monitor' == item_to_test:
                        item_tested = True
                        if query_items['monitor'] == built['monitor_version']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'monitorNot' == item_to_test:
                        item_tested = True
                        if query_items['monitorNot'] == built['monitor_version']:
                            to_be_included = False
                        else:
                            to_be_included = True

                    if 'name' == item_to_test:
                        item_tested = True
                        if query_items['name'].replace('%20', ' ') != str(
                                datastore.get_value_stored(data_store_content, 'device_name_' + built['id'])):
                            to_be_included = False
                        else:
                            to_be_included = True

                    if 'profile' == item_to_test:
                        item_tested = True
                        if query_items['profile'] == built['profile']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'profile_screen_threshold' == item_to_test:
                        item_tested = True
                        if query_items['profile_screen_threshold'] == built['profile_screen_threshold']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'ring' == item_to_test:
                        item_tested = True
                        if query_items['ring'] == built['ring']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'serial' == item_to_test:
                        item_tested = True
                        show_summary = False
                        if query_items['serial'] == built['id']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'service_logging' == item_to_test:
                        item_tested = True
                        if query_items['service_logging'] == built['service_versions_pi_logging_version']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'service_pack' == item_to_test:
                        item_tested = True
                        if query_items['service_pack'] != built['service_pack']:
                            to_be_included = False

                    if 'service_runner' == item_to_test:
                        item_tested = True
                        if query_items['service_runner'] == built['service_versions_pi_runner_version']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'siteid' == item_to_test:
                        item_tested = True
                        if query_items['siteid'] != built['calc_site']:
                            to_be_included = False

                    if 'mgmntblock' == item_to_test:
                        item_tested = True
                        if query_items['mgmntblock'] != built['calc_management_block']:
                            to_be_included = False

                    if 'tag' == item_to_test:
                        item_tested = True
                        if query_items['tag'] == built['tag']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    if 'device_collection' == item_to_test:
                        item_tested = True
                        if query_items['device_collection'] == built['device_collection']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    # limit to status match
                    if 'status' in query_items:
                        if query_items['status'] == 'current':
                            try:
                                if float(built['calc_seconds']) > s_seconds_for_current:
                                    to_be_included = False
                            except:
                                to_be_included = False

                    # find PR005 level2-ng with
                    # https://slicer.cardinalhealth.net/reportsd?ipaddr=10.216.20
                    if 'ipaddr' == item_to_test:
                        item_tested = True
                        if query_items['ipaddr'] in built['IP']:
                            to_be_included = True
                        else:
                            to_be_included = False

                    # all other filters here:
                    if not item_tested:
                        # Do a look up in the data_dictionary provided filters
                        if item_to_test in data_dictionary_filters.keys():
                            if item_to_test in built:
                                filter_field = data_dictionary_filters[item_to_test]
                                # replace html link padding with blank character
                                test_value = query_items[item_to_test].replace('%20', ' ')
                                if test_value == '(not_empty)':
                                    if built[filter_field]:
                                        to_be_included = True
                                    else:
                                        to_be_included = False
                                else:
                                    if test_value == built[filter_field]:
                                        to_be_included = True
                                    else:
                                        to_be_included = False

                    if operation == 'and':
                        to_be_included_overall = to_be_included_overall and to_be_included
                    else:
                        to_be_included_overall = to_be_included_overall or to_be_included

                if devices_managed_only:
                    if built['calc_user_can_do_service_updates_full'] != 'yes':
                        to_be_included_overall = False

            else:
                to_be_included_overall = True

            update_sub_time(sub_times, '059b-copy into set')

            # 2022.12.09 Completely drop the devices for sites_to_drop
            if str(built['calc_site']) in sites_to_drop:
                to_be_included_overall = False

            if to_be_included_overall:
                ids[id] = copy.copy(built)
                # ids[id] = copy.deepcopy(built)

            update_sub_time(sub_times, '060-end')

        time_stamps.append(['processed all ids 0xx total', time.time()])

        # --------------------------------------------------
        filter_display_string = ''
        if REQUEST_FILTER:
            filter_display_string += '<center>'
            filter_display_string += '<br><br>'
            filter_display_string += '<B>Filter: </B>'
            filter_display_string += REQUEST_FILTER.replace(',', ', ')
            filter_display_string += '</center>'

        if False:
            body += '<center>'
            body += '<br><br>'
            body += '<B>Data Filters: </B>'
            body += json.dumps(data_dictionary_filters).replace(',', ', ')
            body += '</center>'

        if show_summary:
            body += '<br>'
            body += '<br>'
            body += '<center>'

            body += '<table border="1" cellpadding="5">'

            columns = []
            columns.append(
                {'header': 'Reset<br>View', 'view': '(reset)', 'tooltip': 'Clear/reset all filters and view settings'})
            columns.append({'header': 'None', 'view': '(None)', 'tooltip': 'Do not show the summary'})
            columns.append({'header': 'Summary', 'view': '(main)', 'tooltip': 'Reset all filters.'})

            columns.append({'header': 'Image', 'view': '(site_image)', 'tooltip': 'Base Image report.'})
            columns.append({'header': 'SP', 'view': '(site_sp)', 'tooltip': 'Service Pack counts.'})

            columns.append({'header': 'need SP', 'view': '(site_need_sp)', 'tooltip': 'Need SP report.'})

            # columns.append({'header':'Runner+', 'view':'(runner)', 'tooltip':'Service version details for runner.'})

            body += '<tr>'
            for column in columns:
                if summary_view == column['view']:
                    color_to_use = color_green
                    if 'tooltip' in column:
                        body += '<td style="background-color:rgba' + color_to_use + '" title="' + column[
                            'tooltip'] + '">'
                    else:
                        body += '<td style="background-color:rgba' + color_to_use + '>'
                else:
                    if 'tooltip' in column:
                        body += '<td title="' + column['tooltip'] + '">'
                    else:
                        body += '<td>'

                url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                   'summary_view=' + column['view'])

                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + \
                        column['header'] + """</a>"""
                body += '</td>'
            body += '</tr>'
            body += '</table>'
            body += '<br>'

            body += filter_display_string

            sites = {}
            current = {}
            site_corp = {}
            current_corp = {}

            site_iot = {}
            current_iot = {}
            percent_new_image_site = {}
            percent_recent_service_pack_site = {}

            site_iot_detected = {}

            runner_versions = {}
            service_pack_versions = {service_pack_highest: 0}
            site = ''
            id = ''
            for id in sorted(ids):
                if s_do_use_management_id:
                    site = str(ids[id]['calc_management_block'])
                else:
                    site = str(ids[id]['calc_site'])

                if not site in sites:
                    sites[site] = {'count': 0, 'runner_versions': {},
                                   'service_pack_versions': {service_pack_highest: 0}}
                sites[site]['count'] += 1

                if summary_view == '(site_image)':
                    if not site in percent_new_image_site:
                        issues = get_top_level_issues(site_to_summarize=site)
                        percent_new_image_site[site] = issues['new_image_percent']
                        percent_recent_service_pack_site[site] = issues['recent_service_pack_percent']

                try:
                    if 'corp' in ids[id]['ssid_chan_sig']:
                        if not site in site_corp:
                            site_corp[site] = 0
                        site_corp[site] += 1
                    if 'iot' in ids[id]['ssid_chan_sig']:
                        if not site in site_iot:
                            site_iot[site] = 0
                        site_iot[site] += 1

                    if 'Yes' in ids[id]['calc-iot']:
                        if not site in site_iot_detected:
                            site_iot_detected[site] = 0
                        site_iot_detected[site] += 1
                except:
                    pass

                try:
                    r_v = str(ids[id]['service_versions_pi_runner_version'])
                    if not r_v:
                        r_v = '(missing)'
                    if not r_v in runner_versions:
                        runner_versions[r_v] = 0
                    runner_versions[r_v] += 1

                    if not r_v in sites[site]['runner_versions']:
                        sites[site]['runner_versions'][r_v] = 0
                    sites[site]['runner_versions'][r_v] += 1
                except:
                    pass

                try:
                    s_p = str(ids[id]['service_pack'])
                    if not s_p:
                        s_p = '(missing)'
                    if not s_p in service_pack_versions:
                        service_pack_versions[s_p] = 0
                    service_pack_versions[s_p] += 1

                    if not s_p in sites[site]['service_pack_versions']:
                        sites[site]['service_pack_versions'][s_p] = 0
                    sites[site]['service_pack_versions'][s_p] += 1
                except:
                    pass

                try:
                    if float(ids[id]['calc_seconds']) < s_seconds_for_current:
                        if not site in current:
                            current[site] = 0
                        current[site] += 1

                        try:
                            if 'corp' in ids[id]['ssid_chan_sig']:
                                if not site in current_corp:
                                    current_corp[site] = 0
                                current_corp[site] += 1
                            if 'iot' in ids[id]['ssid_chan_sig']:
                                if not site in current_iot:
                                    current_iot[site] = 0
                                current_iot[site] += 1
                        except:
                            pass
                except:
                    pass

            # Define the data columns for summary view
            columns = []

            columns.append(
                {'type': 'string', 'filter': 'prefix', 'source': 'dashboard_for_site', 'name': 'Dashboard<br>link',
                 'help': 'Click this to get to a site specific dashboard view.'})

            columns.append({'type': 'number', 'filter': 'prefix', 'source': 'count', 'name': 'count',
                            'help': 'The count of the number of devices that were last seen at this site.'})
            columns.append({'type': 'number', 'filter': 'prefix', 'source': 'current', 'name': 'current',
                            'help': 'The count of the number of devices that have reported in from this site in the last 2 hours.',
                            'value_help': 'Click this number, to filter by this site, and only the current devices'})
            # columns.append({'type':'number','filter':'prefix','source':'count on corp','name':'count<br>on corp', 'help':'The count of the number of devices that were last seen at this site, that were seen as using corp WiFi network.'})
            columns.append(
                {'type': 'number', 'filter': 'prefix', 'source': 'current on corp', 'name': 'current<br>on corp',
                 'help': 'Caution: The count of the number of devices that have reported in from this site in the last 2 hours, that were seen as using corp WiFi network. We need to transition off corp, and onto IOT.',
                 'highlight_non_zero_color': color_yellow_caution})

            if summary_view == '(site_need_sp)':
                columns.append({'type': 'number', 'filter': 'prefix', 'source': 'current on corp need SP',
                                'name': 'current<br>on corp<br>need SP',
                                'help': 'Warning: The count of the number of devices that have reported in from this site in the last 2 hours, that were seen as using corp WiFi network, and need the SP update, or they lose connection on March 1, 2023',
                                'highlight_non_zero_color': color_red_warning})

            # columns.append({'type':'string','filter':'prefix','source':'iot planning date','name':'IOT<br>Planning', 'help':'The date that IOT WiFi network is expected to be available at this site.'})
            # columns.append({'type':'number','filter':'prefix','source':'iot detected','name':'IOT<br>possible', 'help':'The count of the number of devices that have reported that iot is present.'})

            columns.append(
                {'type': 'number', 'filter': 'prefix', 'source': 'current on iot', 'name': 'current<br>on IOT',
                 'help': 'The count of the number of devices that have reported in from this site in the last 2 hours, that were seen as using cah-iot WiFi network.'})

            if summary_view == '(site_sp)':
                for s_p in sorted(service_pack_versions, reverse=True):
                    columns.append({'source': 'service_pack=' + s_p, 'name': s_p,
                                    'help': 'The count of the number of devices that have reported in with this service pack.'})

            if summary_view == '(site_image)':
                columns.append({'type': 'number', 'filter': 'prefix', 'source': 'percent new image',
                                'name': 'percent<br>new image',
                                'help': 'The percent of devices that are on the new image.'})
                columns.append({'type': 'number', 'filter': 'prefix', 'source': 'percent recent SP',
                                'name': 'percent<br>recent SP',
                                'help': 'The percent of devices that are on the target SP.'})

            # ?? not used
            if summary_view == '(runner)':
                for r_v in sorted(runner_versions):
                    columns.append({'source': 'runner=' + r_v, 'name': r_v,
                                    'help': 'The count of the number of devices that have reported in with this version of the runner service.'})

            totals_values = {}
            for column in columns:
                totals_values[column['name']] = 0

            time_stamps.append(['built summary setup', time.time()])

            # show it
            if not summary_view == '(None)':
                body += '<br>'
                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td title="The management ID">'

                #                body += 'managementID'
                #                body += '<br>'

                value = 'managementID'
                url_to_use_here = url_to_use + REQUEST_URI + "?devices_managed=True"
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + str(
                    value) + """</a>"""

                body += '</td>'

                for column in columns:
                    if 'help' in column:
                        body += '<td title="' + column['help'] + '">'
                    else:
                        body += '<td>'

                    if 'filter' in column:
                        if column['type'] == 'number':
                            if 'siteid' in REQUEST_FILTER:
                                body += '<B>(Filtered)</B><br>'
                            else:
                                body += '<B>Total</B><br>'

                    body += column['name']
                    body += '</td>'

                body += '</tr>'

                planning_dates = {}
                planning_dates['TX015'] = '8/28/21'
                planning_dates['PR005'] = '10/30/21'
                planning_dates['MEX03'] = 'Q3/Q4 TBD'
                planning_dates['MEX04'] = 'Q3/Q4 TBD'
                planning_dates['MEX05'] = 'Q3/Q4 TBD'
                planning_dates['DOM02'] = 'Q3/Q4 TBD'
                planning_dates['OH085'] = 'Pi will be<br>decommissioned'
                planning_dates['MEX09'] = 'Cordis<br>Divestiture'

                time_stamps.append(['built summary columns', time.time()])
                update_sub_time(sub_times, '100-start')

                for site in sorted(sites):
                    update_sub_time(sub_times, '110-address2location')
                    site_name = address2location.site_name_stored(data_store_content, site)
                    update_sub_time(sub_times, '120-process')
                    body += '<tr>'
                    body += '<td title="' + site_name + '">'
                    if s_do_use_management_id:
                        url_to_use_here = url_to_use + REQUEST_URI + "?mgmntblock=" + site
                    else:
                        url_to_use_here = url_to_use + REQUEST_URI + "?siteid=" + site
                    onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + site + """</a>"""
                    body += '</td>'

                    # fill out all the data
                    for column in columns:
                        color_to_use_if_not_zero = '(255,255,255,0.0)'
                        if 'highlight_non_zero_color' in column:
                            color_to_use_if_not_zero = column['highlight_non_zero_color']

                        value = 0
                        if column['source'] == 'count':
                            value = int(sites[site]['count'])
                        if site in current:
                            if column['source'] == 'current':
                                value = int(current[site])
                        if site in site_corp:
                            if column['source'] == 'count on corp':
                                value = int(site_corp[site])
                        if site in current_corp:
                            if column['source'] == 'current on corp':
                                value = int(current_corp[site])

                        if column['source'] == 'current on corp need SP':
                            issues = get_top_level_issues(site_to_summarize=site)
                            if 'on_corp_and_not_service_pack_count' in issues:
                                value = issues['on_corp_and_not_service_pack_count']

                        if site in current_iot:
                            if column['source'] == 'current on iot':
                                value = int(current_iot[site])

                        if site in percent_new_image_site:
                            if column['source'] == 'percent new image':
                                try:
                                    value = percent_new_image_site[site]
                                    if value < 0:
                                        value = ''
                                    else:
                                        value = "{:.1f}".format(percent_new_image_site[site])
                                except:
                                    value = -3

                        if site in percent_new_image_site:
                            if column['source'] == 'percent recent SP':
                                try:
                                    value = percent_recent_service_pack_site[site]
                                    if value < 0:
                                        value = ''
                                    else:
                                        value = "{:.1f}".format(percent_recent_service_pack_site[site])
                                except:
                                    value = -3

                        if site in site_iot_detected:
                            if column['source'] == 'iot detected':
                                value = int(site_iot_detected[site])

                        if 'iot planning date' == column['source']:
                            if site in planning_dates:
                                value = planning_dates[site]

                        if 'dashboard_for_site' == column['source']:
                            value = 'dashboard'

                        if 'runner=' in column['source']:
                            r_v = column['source'].split('=')[1]
                            if r_v in sites[site]['runner_versions']:
                                value = int(sites[site]['runner_versions'][r_v])

                        if 'service_pack=' in column['source']:
                            s_p = column['source'].split('=')[1]
                            if s_p in sites[site]['service_pack_versions']:
                                value = int(sites[site]['service_pack_versions'][s_p])

                        try:
                            totals_values[column['name']] += value
                        except:
                            pass

                        # then show it

                        value_help = ''
                        if 'value_help' in column:
                            value_help = column['value_help']

                        if value_help:
                            if value:
                                body += '<td style="background-color:rgba' + color_to_use_if_not_zero + '" title="' + value_help + '">'
                            else:
                                body += '<td title="' + value_help + '">'
                        else:
                            if value:
                                body += '<td style="background-color:rgba' + color_to_use_if_not_zero + '">'
                            else:
                                body += '<td>'

                        if column['source'] == 'current':
                            if site in current:
                                if s_do_use_management_id:
                                    url_to_use_here = url_to_use + REQUEST_URI + "?mgmntblock=" + site + ',status=current'
                                else:
                                    url_to_use_here = url_to_use + REQUEST_URI + "?siteid=" + site + ',status=current'
                                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + str(
                                    value) + """</a>"""
                        elif column['source'] == 'dashboard_for_site':
                            if s_do_use_management_id:
                                url_to_use_here = url_to_use + '/dashboard' + "?mgmntblock=" + site
                            else:
                                url_to_use_here = url_to_use + '/dashboard' + "?siteid=" + site
                            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + str(
                                value) + """</a>"""
                        else:
                            if value:
                                body += str(value)

                        body += '</td>'
                    body += '</tr>'

                # gap line
                body += '<tr>'
                body += '<td>'
                body += '<B>---------</B>'
                body += '</td>'
                body += '</tr>'

                body += '<tr>'
                body += '<td>'
                body += '<B>Totals</B>'
                body += '</td>'

                for column in columns:
                    color_to_use_if_not_zero = '(255,255,255,0.0)'
                    if 'highlight_non_zero_color' in column:
                        color_to_use_if_not_zero = column['highlight_non_zero_color']

                    value = totals_values[column['name']]

                    if value:
                        body += '<td style="background-color:rgba' + color_to_use_if_not_zero + '">'
                    else:
                        body += '<td>'

                    _ = """
                    if column['type'] == 'number':
                        body += str(value)
                        """

                    if (column['source'] == 'current'):
                        url_to_use_here = url_to_use + REQUEST_URI + "?status=current"
                        onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + str(
                            value) + """</a>"""
                    elif 'dashboard_for_site' == column['source']:
                        value = 'dashboard'

                        url_to_use_here = url_to_use + '/dashboard'
                        onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + str(
                            value) + """</a>"""

                    else:
                        if value:
                            body += str(value)  # + '[' + column['source'] + ']'

                    body += '</td>'

                body += '</tr>'

                body += '</table>'
                body += '</center>'

            body += '<br><br>'

            update_sub_time(sub_times, '190-complete')
            time_stamps.append(['built summary', time.time()])

        else:

            body += filter_display_string

            body += '<center>'
            url_to_use_here = url_to_use + REQUEST_URI
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + 'Report' + """</a>"""
            body += '</center>'

        count_current = 0
        for id in ids:
            try:
                if float(ids[id]['calc_seconds']) < s_seconds_for_current:
                    count_current += 1
            except:
                pass

        # --------------------------------------------------
        # now display the device by device details
        body += '<center>'

        body += '<table border="1" cellpadding="5">'

        columns = []
        columns.append({'header': 'Device report', 'view': '(main)', 'tooltip': 'Reset ALL filters.'})
        columns.append({'header': 'Device report+', 'view': '(details)', 'tooltip': 'More details.'})
        columns.append(
            {'header': 'Device report+v', 'view': '(services)', 'tooltip': 'Report on the versions of services.'})
        columns.append({'header': 'Device report+n', 'view': '(network)', 'tooltip': 'Report on networking details.'})
        columns.append(
            {'header': 'Device report+I', 'view': '(iot_network)', 'tooltip': 'Report on IOT networking details.'})
        columns.append({'header': 'Device report+m', 'view': '(memory)', 'tooltip': 'Report on memory details.'})
        columns.append({'header': 'Device report+c', 'view': '(comments)', 'tooltip': 'Report on comment details.'})
        columns.append({'header': 'Device report+M1', 'view': '(MAC1)', 'tooltip': 'Report on device MAC addresses.'})
        columns.append(
            {'header': 'Device report+M2', 'view': '(MAC2)', 'tooltip': 'Report on MAC addresses (omitting MEX09).'})
        columns.append(
            {'header': 'Device report+S', 'view': '(Special)', 'tooltip': 'Report on special test attributes.'})
        columns.append({'header': 'Device report+C', 'view': '(config)', 'tooltip': 'Report on config changes.'})
        columns.append({'header': 'Device report+T', 'view': '(thirdparty)', 'tooltip': 'Report on thirdparty apps.'})
        columns.append({'header': 'Device report+L', 'view': '(logging)', 'tooltip': 'Report on logging details.'})
        columns.append({'header': 'Device report+s', 'view': '(scrn_res)', 'tooltip': 'Report on screen details.'})

        body += '<tr>'
        for column in columns:
            tool_tip_to_use = ''
            if 'tooltip' in column:
                tool_tip_to_use = column['tooltip']

            if device_view == column['view']:
                color_to_use = color_green
                if tool_tip_to_use:
                    body += '<td style="background-color:rgba' + color_to_use + '" title="' + tool_tip_to_use + '">'
                else:
                    body += '<td style="background-color:rgba' + color_to_use + '>'
            else:
                if tool_tip_to_use:
                    body += '<td title="' + tool_tip_to_use + '">'
                else:
                    body += '<td>'
            # url_to_use = "https://slicer.cardinalhealth.net" + REQUEST_URI + '?device_view=' + column['view']
            url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                               'device_view=' + column['view'],
                                                                               items_to_puncture='sort,sortd')

            onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + column[
                'header'] + """</a>"""
            body += '</td>'
        body += '</tr>'
        body += '</table>'

        time_stamps.append(['built tabs table', time.time()])

        # Get all items into the data_dictionary, so that all column definitions in one place.

        columns = []
        view_by_rows_is = 'by_id'

        if device_view == '(main)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'idr'})
            columns.append({'data_dictionary': 'management_block'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'tag'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'service_pack'})
            columns.append({'data_dictionary': 'ring'})
            columns.append({'data_dictionary': 'ring_updates'})
            columns.append({'data_dictionary': 'temperature'})
            columns.append({'data_dictionary': 'ssid'})
            columns.append({'data_dictionary': 'ssid_chan_sig'})
            columns.append({'data_dictionary': 'hopper'})
            columns.append({'data_dictionary': 'coaster'})
            columns.append({'data_dictionary': 'device_extra_boots'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'data_dictionary': 'profile'})
            columns.append({'data_dictionary': 'IP'})

        elif device_view == '(details)':  # Device report+
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'idr'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'UserInActive'})
            columns.append({'data_dictionary': 'BrowserRestartWanted'})
            columns.append({'data_dictionary': 'bluetooth'})
            columns.append({'data_dictionary': 'Menu'})

            columns.append({'header': 'Seconds Since (m)', 'value': '(seconds_monitor)',
                            'tooltip': 'The number of seconds since this device last checked in. The monitor checks in every 1800 seconds.',
                            'thresholds': ["(0, 255, 0, 0.3)", 1800.0, "(255, 255, 100, 0.3)", 1900.0,
                                           "(255, 0, 0, 0.3)"], 'sort': 'integer'})
            columns.append({'header': 'Seconds Since (r)', 'value': '(seconds_runner)',
                            'tooltip': 'The runner checks in every 60 seconds.',
                            'thresholds': ["(0, 255, 0, 0.3)", 60.0, "(255, 255, 100, 0.3)", 70.0, "(255, 0, 0, 0.3)"],
                            'sort': 'integer'})

            columns.append({'data_dictionary': 'inspect_comms'})

            columns.append(
                {'header': 'Index', 'value': '(Index)', 'tooltip': 'The last reported security screening index',
                 'sort': 'integer'})
            columns.append({'header': 'Boot', 'value': '(Boot)',
                            'tooltip': 'The count of boot cycles, since this image was installed.', 'sort': 'integer'})
            columns.append({'header': 'Grab', 'value': '(Grab)',
                            'tooltip': 'The count of screen grabs, since this image was installed.', 'sort': 'integer'})
            columns.append(
                {'header': 'Uptime', 'value': '(Uptime)', 'tooltip': 'The uptime at the device last checked in.',
                 'sort': 'integer'})
            columns.append({'header': 'monitor counts', 'value': '(monitor_counts)',
                            'tooltip': 'The monitor check in counts (lifetime).', 'sort': 'integer'})

            columns.append({'data_dictionary': 'forced'})
            columns.append({'data_dictionary': 'screen'})
            columns.append({'data_dictionary': 'zoom'})
            columns.append({'data_dictionary': 'profile'})

            # SN link to serial number
            # https://cardinal.service-now.com/nav_to.do?uri=%2Falm_hardware_list.do%3Fsysparm_first_row%3D1%26sysparm_query%3DGOTOserial_numberLIKE10000000e3669edf%26sysparm_query_encoded%3DGOTOserial_numberLIKE10000000e3669edf%26sysparm_view%3D

            # daves pi in SNOW (service now)
            # https://cardinal.service-now.com/nav_to.do?uri=%2Fcmdb_ci_computer.do%3Fsys_id%3De6977f18872f3c182dd921b2debb3596

        elif device_view == '(services)':  # +v versions
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'header': 'Can update', 'value': '(calc_user_can_do_service_updates_full)',
                            'tooltip': 'Can the currently logged in user perform service updates for this device.'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'tag'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'header': 'ssid-chan-sig', 'value': '(ssid_chan_sig)',
                            'tooltip': 'The WiFi network name (ssid), channel number, and signal strength. If the connected access point does not broadcast its SSID, then we show (hidden) here. Channel number tells what access point is making the connection. Signal strength is best at 100, and worst at zero. (Sort is based on signal strength)',
                            'sort': 'third_split_dash_integer'})

            columns.append({'data_dictionary': 'kernel'})
            columns.append({'data_dictionary': 'chromium'})

            columns.append({'data_dictionary': 'service_pack'})
            columns.append({'header': 'Base<br>Image', 'value': '(Image)', 'tooltip': 'The software image version.',
                            'filter_to_use': 'image=(Image)'})

            columns.append({'data_dictionary': 'hopper'})

            columns.append({'data_dictionary': 'ring'})
            columns.append({'header': 'ring<br>updates<br>available', 'value': '(ring_updates)',
                            'tooltip': 'The device has updates available. After a change, it may take 5 to 6 minutes for this field to be refreshed.',
                            'sort': 'integer', 'thresholds': ["(255, 255, 255, 0.0)", 0.5, "(100, 100, 255, 0.3)"]})

            columns.append({'data_dictionary': 'outage'})

            for service_name in s_all_pi_services:
                columns.append({'data_dictionary': 'service_versions_' + service_name + '_version'})

        elif device_view == '(network)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'service_pack'})
            for service_name in ['pi_network']:
                columns.append({'header': service_name, 'value': '(' + service_name + ')',
                                'tooltip': 'The version number of the service.'})

            columns.append({'data_dictionary': 'lan'})

            columns.append({'header': 'Tdiff', 'value': '(timediff)',
                            'tooltip': 'The time difference between pi and Slicer (positive is that pi is ahead)'})
            columns.append({'data_dictionary': 'timelocal'})
            columns.append({'data_dictionary': 'net_rx_errors'})
            columns.append({'data_dictionary': 'net_tx_errors'})
            columns.append({'data_dictionary': 'net_rx_dropped'})
            columns.append({'data_dictionary': 'net_tx_dropped'})
            columns.append({'data_dictionary': 'wifibitrate'})
            columns.append({'data_dictionary': 'wififrequency'})
            columns.append({'header': 'iot<br>seen', 'value': '(calc-iot)', 'tooltip': 'IOT is seen by this device.'})
            columns.append({'data_dictionary': 'network_connects'})
            columns.append({'data_dictionary': 'network_device_changes'})
            columns.append({'data_dictionary': 'network_device_number'})
            columns.append({'data_dictionary': 'network_interface'})
            columns.append({'data_dictionary': 'network_nic_change_count'})
            columns.append({'data_dictionary': 'network_corp_cert'})

        elif device_view == '(iot_network)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'idr'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'tag'})
            columns.append({'data_dictionary': 'device_collection'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'header': 'iot<br>seen', 'value': '(calc-iot)', 'tooltip': 'IOT is seen by this device.'})
            columns.append({'data_dictionary': 'service_pack'})
            columns.append({'data_dictionary': 'ssid'})
            columns.append({'data_dictionary': 'ssid_chan_sig'})
            columns.append(
                {'header': 'mac0 ClearPass', 'value': '(iot0)', 'tooltip': 'Has been added to the ClearPass iot list.'})
            columns.append(
                {'header': 'mac1 ClearPass', 'value': '(iot1)', 'tooltip': 'Has been added to the ClearPass iot list.'})
            columns.append({'header': 'mac0', 'value': '(wlan0mac)', 'tooltip': ''})
            columns.append({'header': 'mac1', 'value': '(wlan1mac)', 'tooltip': ''})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'data_dictionary': 'IP'})

        elif device_view == '(memory)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})

            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})

            columns.append({'data_dictionary': 'service_pack'})

            service_name = 'pi_logging'
            columns.append({'data_dictionary': 'service_versions_' + service_name + '_version'})

            columns.append({'header': 'Disk', 'value': '(disk_use)',
                            'tooltip': 'The worst disk load for the device as percentage.', 'sort': 'integer',
                            'thresholds': ["(0, 255, 0, 0.3)", 75.0, "(255, 255, 100, 0.3)", 90.0, "(255, 0, 0, 0.3)"]})
            columns.append({'header': 'Size', 'value': '(disk_size)',
                            'tooltip': 'The size of the disk of worst disk load for the device as GB.',
                            'sort': 'float'})  # , 'thresholds':["(255, 0, 0, 0.3)", 3.0, "(255, 255, 100, 0.3)", 4.0, "(0, 255, 0, 0.3)"]})
            columns.append({'header': 'iNode', 'value': '(inode_use)',
                            'tooltip': 'The worst inode load for the device as percentage.', 'sort': 'integer',
                            'thresholds': ["(0, 255, 0, 0.3)", 60.0, "(255, 255, 100, 0.3)", 80.0, "(255, 0, 0, 0.3)"]})
            columns.append({'data_dictionary': 'memfree'})
            columns.append({'data_dictionary': 'membuffer'})
            columns.append({'data_dictionary': 'memcache'})
            columns.append({'data_dictionary': 'memtotal'})
            columns.append({'header': 'Configs', 'value': '(settings_count)',
                            'tooltip': 'The count of non-default configuration items.', 'sort': 'float'})

            columns.append({'data_dictionary': 'loadavg'})
            columns.append(
                {'header': 'Load', 'value': '(stats_load_report)', 'tooltip': 'Recent (hours) loading of the device.',
                 'sort': 'integer',
                 'thresholds': ["(0, 255, 0, 0.3)", 4.0, "(255, 255, 100, 0.3)", 6.0, "(255, 0, 0, 0.3)"]})

        elif device_view == '(comments)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'service_pack'})
            columns.append(
                {'header': 'Comments', 'value': '(comments_count)', 'tooltip': 'The number of comments on this device'})
            columns.append({'header': 'Comments<br>need reviewed', 'value': '(comments_need_review_count)',
                            'tooltip': 'The number of comments on this device not yet reviewed by support person'})
            #            columns.append({'data_dictionary':'UserInactive'})
            columns.append({'data_dictionary': 'UserInActive'})

        elif device_view == '(MAC1)':
            view_by_rows_is = 'by_mac1'
            columns.append({'header': 'id', 'value': 'id', 'tooltip': ''})
            columns.append({'header': 'name', 'value': 'name', 'tooltip': ''})
            columns.append({'header': 'mac0', 'value': '(mac0)', 'tooltip': ''})
            columns.append(
                {'header': 'mac0 IOT', 'value': '(iot0)', 'tooltip': 'Has been added to the ClearPass iot list.'})
            columns.append({'header': 'mac1', 'value': '(mac1)', 'tooltip': ''})
            columns.append(
                {'header': 'mac1 IOT', 'value': '(iot1)', 'tooltip': 'Has been added to the ClearPass iot list.'})

        #            columns.append({'data_dictionary':'id'})
        #            columns.append({'header':'wlan0mac', 'value':'(wlan0mac)', 'tooltip':''})
        #            columns.append({'header':'wlan1mac', 'value':'(wlan1mac)', 'tooltip':''})

        elif device_view == '(MAC2)':
            view_by_rows_is = 'by_mac2'
            columns.append({'header': 'device', 'value': '(device)', 'tooltip': ''})
            columns.append({'header': 'mac', 'value': '(mac)', 'tooltip': ''})
            columns.append({'header': 'id', 'value': 'id', 'tooltip': ''})
            columns.append({'header': 'Site', 'value': '(siteID)', 'tooltip': ''})
            #            if not 'csv_view=yes' in REQUEST_FILTER:
            columns.append(
                {'header': 'in ClearPass', 'value': '(iot)', 'tooltip': 'Has been added to the ClearPass iot list.'})

        elif device_view == '(MAC3)':
            view_by_rows_is = 'by_mac2'
            columns.append({'header': 'mac', 'value': '(mac)', 'tooltip': ''})
            columns.append({'header': 'id', 'value': 'id', 'tooltip': ''})
            columns.append({'header': 'Site', 'value': '(siteID)', 'tooltip': ''})

        elif device_view == '(Special)':
            columns.append({'data_dictionary': 'id'})
            columns.append({'header': 'oldest report', 'value': '(oldestreport)', 'tooltip': ''})

        elif device_view == '(config)':
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'data_dictionary': 'configchanges'})
            columns.append({'data_dictionary': 'configtime'})
            columns.append({'data_dictionary': 'configtime_human'})

        elif device_view == '(thirdparty)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'data_dictionary': 'management_block'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'thirdparty_report'})

        elif device_view == '(logging)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append({'data_dictionary': 'management_block'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'UserInActive'})
            columns.append({'data_dictionary': 'stuckkeys_human'})
            columns.append({'data_dictionary': 'stuckkeys'})
            columns.append({'data_dictionary': 'userdevicelastactive'})
            #            columns.append({'data_dictionary':'s_logging_HID'})
            #            columns.append({'data_dictionary':'s_logging_RS6000'})
            columns.append({'data_dictionary': 'IP'})
            columns.append({'data_dictionary': 'ssid'})
            columns.append({'data_dictionary': 'first_date'})
            columns.append({'data_dictionary': 'first_iot_date'})

        elif device_view == '(scrn_res)':
            columns.append({'header': 'row', 'value': '(row)'})
            columns.append({'data_dictionary': 'id'})
            columns.append({'data_dictionary': 'seconds_since'})
            columns.append({'data_dictionary': 'seconds_since_human'})
            columns.append(
                {'header': 'Uptime', 'value': '(Uptime)', 'tooltip': 'The uptime at the device last checked in.',
                 'sort': 'integer'})
            columns.append({'data_dictionary': 'browseruptime'})
            columns.append({'data_dictionary': 'management_block'})
            columns.append({'data_dictionary': 'site_name'})
            columns.append({'data_dictionary': 'name'})
            columns.append({'data_dictionary': 'ScreenReport'})
            columns.append({'data_dictionary': 'forced'})
            columns.append({'data_dictionary': 'screen'})
            columns.append({'data_dictionary': 'zoom'})
            columns.append({'data_dictionary': 'profile'})
            columns.append({'data_dictionary': 'profile_screen_threshold'})
            columns.append({'data_dictionary': 'screen_over_threshold'})


        # do all the lookups
        time_stamps.append(['start lookups', time.time()])
        column_values_by_id = {}
        column_sort_values_by_id = {}
        column_url_by_id = {}
        column_color_by_id = {}
        can_do_service_updates_full = {}
        unique_ids = []
        macs_in_iot_d = intake.get_list_of_mac_in_iot()

        if view_by_rows_is == 'by_mac1':
            for id in ids:
                unique_ids.append(id)
                column_values_by_id[id] = []
                column_sort_values_by_id[id] = ''
                column_url_by_id[id] = []
                column_color_by_id[id] = []

                for column in columns:
                    value_to_use = ''
                    url_to_use_here = ''
                    color = ''
                    if column['value'] == 'id':
                        value_to_use = id
                    if column['value'] == 'name':
                        try:
                            value_to_use = ids[id]['name']
                        except:
                            value_to_use = ''
                    if column['value'] == '(mac0)':
                        try:
                            value_to_use = str(ids[id]['wlan0mac'])
                        except:
                            pass
                    if column['value'] == '(mac1)':
                        try:
                            value_to_use = str(ids[id]['wlan1mac'])
                        except:
                            pass
                    if column['value'] == '(iot0)':
                        mac_address = str(ids[id]['wlan0mac'])
                        if mac_address:
                            if mac_address in macs_in_iot_d:
                                value_to_use = 'yes'
                                color = color_green
                            else:
                                value_to_use = 'no'
                                color = color_yellow_caution

                    if column['value'] == '(iot1)':
                        mac_address = str(ids[id]['wlan1mac'])
                        if mac_address:
                            if mac_address in macs_in_iot_d:
                                value_to_use = 'yes'
                                color = color_green
                            else:
                                value_to_use = 'no'
                                color = color_yellow_caution

                    column_values_by_id[id].append(value_to_use)
                    column_url_by_id[id].append(url_to_use_here)
                    column_color_by_id[id].append(color)


        elif view_by_rows_is == 'by_mac2':
            for id in ids:
                mac_list = []

                if not ids[id]['calc_site'] in sites_to_drop:
                    try:
                        mac_to_test = str(ids[id]['wlan0mac'])
                        if mac_is_raspberrypi(mac_to_test):
                            mac_list.append(mac_to_test)
                    except:
                        pass
                    try:
                        mac_to_test = str(ids[id]['wlan1mac'])
                        if mac_is_raspberrypi(mac_to_test):
                            mac_list.append(mac_to_test)
                    except:
                        pass

                for mac_address in mac_list:
                    if mac_address:
                        unique_ids.append(mac_address)
                        column_values_by_id[mac_address] = []
                        column_sort_values_by_id[mac_address] = ''
                        column_url_by_id[mac_address] = []
                        column_color_by_id[mac_address] = []

                        for column in columns:
                            value_to_use = ''
                            url_to_use_here = ''
                            color = ''
                            if column['value'] == 'id':
                                value_to_use = id

                            if column['value'] == '(device)':
                                value_to_use = str(ids[id]['device'])

                            if column['value'] == '(siteID)':
                                value_to_use = str(ids[id]['calc_site'])

                            if column['value'] == '(mgmntblock)':
                                value_to_use = str(ids[id]['calc_management_block'])

                            if column['value'] == '(mac)':
                                value_to_use = mac_address
                            if column['value'] == '(iot)':
                                if mac_address in macs_in_iot_d:
                                    value_to_use = 'yes'
                                    color = color_green
                                else:
                                    value_to_use = 'no'
                                    color = color_yellow_caution

                            column_values_by_id[mac_address].append(value_to_use)
                            column_url_by_id[mac_address].append(url_to_use_here)
                            column_color_by_id[mac_address].append(color)


        else:  # if view_by_rows_is == 'by_id':
            for id in ids:
                unique_ids.append(id)
                device_ring = rings.device_ring_calculated(id)
                try:
                    image_version = str(ids[id]['datadrop_runner_image_version'])
                except:
                    image_version = ''
                try:
                    runner_version = str(ids[id]['service_versions_pi_runner_version'])
                except:
                    runner_version = ''
                try:
                    logging_version = str(ids[id]['service_versions_pi_logging_version'])
                except:
                    logging_version = ''
                try:
                    network_version = str(ids[id]['service_versions_pi_network_version'])
                except:
                    network_version = ''

                replacers = {}
                try:
                    replacers['(id)'] = id
                    replacers['(idr)'] = convert_serial_to_serialr(id)
                    replacers['(name)'] = str(datastore.get_value_stored(data_store_content, 'device_name_' + id))
                    replacers['(tag)'] = str(datastore.get_value_stored(data_store_content, 'device_tag_' + id))
                    replacers['(IP)'] = str(ids[id]['IP'])
                    replacers['(ring)'] = device_ring
                    replacers['(Index)'] = str(ids[id]['security_index'])
                    replacers['(Boot)'] = str(ids[id]['boot_count'])
                    replacers['(Image)'] = image_version
                    replacers['(Grab)'] = str(ids[id]['screengrab_count'])
                    replacers['(Uptime)'] = str(ids[id]['uptime'])
                    replacers['(Monitor)'] = str(ids[id]['monitor_version'])
                    replacers['(seconds_monitor)'] = str(ids[id]['calc_seconds'])
                except:
                    pass

                try:
                    replacers['(stats_load_report)'] = str(ids[id]['stats_load_report'])
                except:
                    replacers['(stats_load_report)'] = ''

                try:
                    replacers['(wlan0mac)'] = str(ids[id]['wlan0mac'])
                except:
                    replacers['(wlan0mac)'] = ''

                try:
                    replacers['(wlan1mac)'] = str(ids[id]['wlan1mac'])
                except:
                    replacers['(wlan1mac)'] = ''

                try:
                    replacers['(oldestreport)'] = str(ids[id]['oldestreport'])
                except:
                    replacers['(oldestreport)'] = ''

                try:
                    replacers['(calc_user_can_do_service_updates_full)'] = str(
                        ids[id]['calc_user_can_do_service_updates_full'])
                except:
                    replacers['(calc_user_can_do_service_updates_full)'] = ''

                if replacers['(calc_user_can_do_service_updates_full)'] == 'yes':
                    can_do_service_updates_full[id] = True

                for service_name in s_all_pi_services:
                    replacers['(' + service_name + ')'] = str(ids[id]['service_versions_' + service_name + '_version'])

                replacers['(Runner)'] = runner_version
                replacers['(Logging)'] = logging_version
                replacers['(Network)'] = network_version
                try:
                    replacers['(seconds_runner)'] = str(ids[id]['calc_runner_seconds'])
                    replacers['(monitor_counts)'] = str(ids[id]['calc_counts'])
                    replacers['(siteID)'] = str(ids[id]['calc_site'])
                    replacers['(mgmntblock)'] = str(ids[id]['calc_management_block'])

                    replacers['(seconds_last)'] = str(ids[id]['calc_seconds'])
                    replacers['(seconds_last_human)'] = str(ids[id]['calc_seconds_human'])

                    replacers['(comments_count)'] = str(len(ids[id]['my_device_comment_keys']))
                    if replacers['(comments_count)'] == '0':
                        replacers['(comments_count)'] = ''
                    replacers['(comments_need_review_count)'] = str(
                        len(ids[id]['my_device_comment_keys']) - len(ids[id]['my_device_comment_has_review_keys']))

                    if replacers['(comments_need_review_count)'] == '0':
                        replacers['(comments_need_review_count)'] = ''

                    replacers['(settings_count)'] = str(ids[id]['settings_count'])

                    replacers['(screen_reported)'] = str(ids[id]['screen_reported'])

                    replacers['(timediff)'] = str(ids[id]['timediff'])
                    replacers['(loadavg)'] = str(ids[id]['loadavg'])
                    replacers['(disk_use)'] = str(ids[id]['disk_use'])
                    replacers['(disk_size)'] = str(ids[id]['disk_size'])
                    replacers['(inode_use)'] = str(ids[id]['inode_use'])
                    replacers['(calc-iot)'] = str(ids[id]['calc-iot'])
                except:
                    pass

                # do all the rest from the dataDictionary
                for key in data_dictionary.keys():
                    source = key
                    if 'source' in data_dictionary[key]:
                        source = data_dictionary[key]['source']
                    source_paren = '(' + source + ')'
                    if not source_paren in replacers:
                        replacers[source_paren] = str(ids[id][source])

                # --------------------------------------------

                column_values_by_id[id] = []
                column_sort_values_by_id[id] = ''
                column_url_by_id[id] = []
                column_color_by_id[id] = []
                for column in columns:
                    if 'data_dictionary' in column:
                        # new way
                        name_to_use = column['data_dictionary']
                        source_to_use = name_to_use

                        if name_to_use in data_dictionary:
                            if 'source' in data_dictionary[name_to_use]:
                                source_to_use = data_dictionary[name_to_use]['source']

                        try:
                            raw_value = str(ids[id][source_to_use])
                        except:
                            raw_value = ''

                        # default is copy
                        value_to_use = str(raw_value)

                        if name_to_use in data_dictionary:
                            if 'display' in data_dictionary[name_to_use]:
                                display_to_use = data_dictionary[name_to_use]['display']

                                if display_to_use == 'html2text':
                                    value_to_use = str(raw_value.replace('%20', ' '))
                                if 'divideby_' in display_to_use:
                                    try:
                                        value_to_use = str(
                                            float(raw_value) / float(display_to_use.replace('divideby_', '')))
                                    except:
                                        pass

                        column_values_by_id[id].append(value_to_use)

                        url_to_use_here = ''
                        if 'filter' in data_dictionary[name_to_use]:
                            filter_to_use = data_dictionary[name_to_use]['filter']
                            if filter_to_use:
                                url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                                   filter_to_use)

                                # If there is a "sort=(item),", copy it out, and at the end, put it back..
                                if 'sort=' in url_to_use_here:
                                    special_string = "thisIsAverySpecialString"
                                    original_sort_string = 'sort=' + url_to_use_here.split('sort=')[1].split(',')[0]
                                    url_to_use_here = url_to_use_here.replace(original_sort_string, special_string)
                                    for replacer in replacers:
                                        url_to_use_here = url_to_use_here.replace(replacer, replacers[replacer])
                                    url_to_use_here = url_to_use_here.replace(special_string, original_sort_string)
                                else:
                                    for replacer in replacers:
                                        url_to_use_here = url_to_use_here.replace(replacer, replacers[replacer])
                        column_url_by_id[id].append(url_to_use_here)

                        color = ''
                        if 'thresholds' in data_dictionary[name_to_use]:
                            if data_dictionary[name_to_use]['thresholds']:
                                try:
                                    float_value = float(value_to_use)
                                    color = data_dictionary[name_to_use]['thresholds'][0]
                                    threshold_index = 1
                                    while len(data_dictionary[name_to_use]['thresholds']) > threshold_index + 1:
                                        if float_value > float(
                                                data_dictionary[name_to_use]['thresholds'][threshold_index]):
                                            color = data_dictionary[name_to_use]['thresholds'][threshold_index + 1]
                                        threshold_index += 2

                                except:
                                    # do a string based compare
                                    string_value = str(value_to_use)
                                    if string_value:
                                        color = data_dictionary[name_to_use]['thresholds'][0]
                                        threshold_index = 1
                                        while len(column['thresholds']) > threshold_index + 1:
                                            if string_value > data_dictionary[name_to_use]['thresholds'][
                                                threshold_index]:
                                                color = data_dictionary[name_to_use]['thresholds'][threshold_index + 1]
                                            threshold_index += 2

                        if 'value_color' in data_dictionary[name_to_use]:
                            string_to_find = str(value_to_use)
                            if string_to_find in data_dictionary[name_to_use]['value_color']:
                                color = data_dictionary[name_to_use]['value_color'][string_to_find]

                        if name_to_use + ':color' in ids[id]:
                            color = ids[id][name_to_use + ':color']
                        column_color_by_id[id].append(color)

                    else:
                        # old way
                        value_to_use = column['value']
                        for replacer in replacers:
                            value_to_use = value_to_use.replace(replacer, replacers[replacer])

                        url_to_use_here = ''

                        if column['value'] == '(iot0)':
                            value_to_use = ''
                            mac_address = str(ids[id]['wlan0mac'])
                            if mac_address:
                                if mac_address in macs_in_iot_d:
                                    value_to_use = 'yes'
                                    color = color_green
                                else:
                                    value_to_use = 'no'
                                    color = color_yellow_caution

                        if column['value'] == '(iot1)':
                            value_to_use = ''
                            mac_address = str(ids[id]['wlan1mac'])
                            if mac_address:
                                if mac_address in macs_in_iot_d:
                                    value_to_use = 'yes'
                                    color = color_green
                                else:
                                    value_to_use = 'no'
                                    color = color_yellow_caution

                        if 'filter_to_use' in column:
                            filter_to_use = column['filter_to_use']
                            if filter_to_use:
                                url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                                   filter_to_use)

                                # If there is a "sort=(item),", copy it out, and at the end, put it back..
                                if 'sort=' in url_to_use_here:
                                    special_string = "thisIsAverySpecialString"
                                    original_sort_string = 'sort=' + url_to_use_here.split('sort=')[1].split(',')[0]
                                    url_to_use_here = url_to_use_here.replace(original_sort_string, special_string)
                                    for replacer in replacers:
                                        url_to_use_here = url_to_use_here.replace(replacer, replacers[replacer])
                                    url_to_use_here = url_to_use_here.replace(special_string, original_sort_string)
                                else:
                                    for replacer in replacers:
                                        url_to_use_here = url_to_use_here.replace(replacer, replacers[replacer])

                        color = ''
                        if 'thresholds' in column:
                            try:
                                float_value = float(value_to_use)
                                color = column['thresholds'][0]
                                threshold_index = 1
                                while len(column['thresholds']) > threshold_index + 1:
                                    if float_value > float(column['thresholds'][threshold_index]):
                                        color = column['thresholds'][threshold_index + 1]
                                    threshold_index += 2

                            except:
                                # do a string based compare
                                string_value = str(value_to_use)
                                if string_value:
                                    color = column['thresholds'][0]
                                    threshold_index = 1
                                    while len(column['thresholds']) > threshold_index + 1:
                                        try:
                                            if string_value > column['thresholds'][threshold_index]:
                                                color = column['thresholds'][threshold_index + 1]
                                        except:
                                            pass
                                        threshold_index += 2

                        column_values_by_id[id].append(value_to_use)
                        column_url_by_id[id].append(url_to_use_here)
                        column_color_by_id[id].append(color)

        time_stamps.append(['end lookups and start sort', time.time()])

        # set up the sort
        # use the first column definition as the default column to sort
        if 'data_dictionary' in columns[0]:
            # new
            sort_key = columns[0]['data_dictionary']
        else:
            # old
            sort_key = columns[0]['value']
            if sort_key == '(row)':  # don't sort by row, advance to what ever is defined next
                if 'data_dictionary' in columns[1]:
                    sort_key = columns[1]['data_dictionary']
                else:
                    sort_key = columns[1]['value']

        sortd = 'fwd'
        if 'sort' in query_items:
            sort_key = query_items['sort']
        if 'sortd' in query_items:
            sortd = query_items['sortd']

        sort_counts = {}
        sort_column = None
        sort_type = 'upper_case'
        for column_index in range(0, len(columns)):
            name_to_sort_on = ''
            way_to_sort = ''
            if 'value' in columns[column_index]:
                name_to_sort_on = columns[column_index]['value']
                if 'sort' in columns[column_index]:
                    way_to_sort = columns[column_index]['sort']
            if 'data_dictionary' in columns[column_index]:
                name_to_sort_on = columns[column_index]['data_dictionary']
                if 'sort' in data_dictionary[columns[column_index]['data_dictionary']]:
                    way_to_sort = data_dictionary[columns[column_index]['data_dictionary']]['sort']

            if name_to_sort_on:
                if name_to_sort_on == sort_key:
                    sort_column = column_index
                    if way_to_sort:
                        sort_type = way_to_sort

        sortable_d = {}
        for id in unique_ids:
            try:
                raw_value = column_values_by_id[id][sort_column]
            except:
                raw_value = '(value_exception)'

            if sort_type == 'float':
                try:
                    value = '%020.3f' % float(raw_value)
                except:
                    value = '%020.3f' % -1.0
                    value = ''
            elif sort_type == 'human_time':
                try:
                    value = '%020.3f' % float(human_to_seconds(raw_value))
                except:
                    value = ''

            elif sort_type == 'bitrate':
                try:
                    raw_number = float(raw_value.strip().replace('%20', ' ').split()[0])
                    if 'Kb/s' in raw_value:
                        raw_number *= 1000
                    if 'Mb/s' in raw_value:
                        raw_number *= 1000000
                    if 'Gb/s' in raw_value:
                        raw_number *= 1000000000
                    value = '%020.3f' % float(raw_number)
                except:
                    value = '%020.3f' % -1.0
                    value = ''
            elif sort_type == 'integer':
                try:
                    value = '%020.3f' % float(raw_value)
                except:
                    #                    value =  '%020.3f' % -1.0
                    value = ''
            elif sort_type == 'dotted_version':
                value = make_sort_from_version(raw_value)
            elif sort_type == 'screen_resolution':
                value = make_sort_from_screen_resolution(raw_value)
            elif sort_type == 'third_split_dash_integer':
                # corp-68-72, want 000000072
                # cah-iot-68-72, want 000000072
                if raw_value == '(not_on_wifi)':
                    value = '%020.3f' % -1.0
                    value = ''
                else:
                    try:
                        value = '%020.3f' % float(
                            raw_value.replace('cah-iot', 'cahiot').replace('level2-ng', 'level2ng').split('-')[2])
                    except:
                        value = '%020.3f' % -2.0
                        value = ''
            elif sort_type == 'first_split_dash_string':
                value = get_ssid_from_dash_string(raw_value)
            elif sort_type == 'float_empty_to_top':
                value = str(raw_value).upper()
                if not value:
                    value = '-'
            else:
                value = str(raw_value).upper()

            value = value.strip()  # pull leading and trailing spaces off

            if not value in sort_counts:
                sort_counts[value] = 0
            sort_counts[value] += 1
            column_sort_values_by_id[id] = value

            if not value in sortable_d:
                sortable_d[value] = []
            sortable_d[value].append(id)

        ids_by_sort_criteria = []
        empties = []
        row_count = 0
        row_max = 9999999999
        rows_were_limited = False
        if 'max_rows' in query_items:
            row_max = int(query_items['max_rows'])

        if sortd == 'rev':
            for sorted_value in sorted(sortable_d, reverse=True):
                if sorted_value:
                    for id in sortable_d[sorted_value]:
                        if row_count < row_max:
                            ids_by_sort_criteria.append(id)
                            row_count += 1
                        else:
                            rows_were_limited = True
                else:
                    for id in sortable_d[sorted_value]:
                        empties.append(id)
        else:
            for sorted_value in sorted(sortable_d):
                if sorted_value:
                    for id in sortable_d[sorted_value]:
                        if row_count < row_max:
                            ids_by_sort_criteria.append(id)
                            row_count += 1
                        else:
                            rows_were_limited = True
                else:
                    for id in sortable_d[sorted_value]:
                        empties.append(id)

        for id in empties:
            if row_count < row_max:
                ids_by_sort_criteria.append(id)
                row_count += 1
            else:
                rows_were_limited = True
        time_stamps.append(['sort complete', time.time()])

        # Render the table
        body += '<br>'
        body += 'total found: ' + str(len(ids)) + '<br>'
        body += 'total current: ' + str(count_current) + '<br><br>'

        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        column_index_for_iot = -1
        table_content = ''
        table_rows = 0
        table_as_csv_clicker = ''

        table_content += '<br><br>'
        table_content += '<table border="1" cellpadding="5">'

        trust_string = ''
        if not datastore.trust():
            trust_string = '<B>Not Trusted</B><br>'

        if True:
            # https://superuser.com/questions/318420/formatting-a-comma-delimited-csv-to-force-excel-to-interpret-value-as-a-string
            csv_cell_start = '\t'
            csv_cell_end = ''
        else:
            csv_cell_start = '"\t'
            csv_cell_end = '"'

        csv_line = []
        table_content += '<tr>'
        column_index_counting = -1
        column_index_of_sort = 0
        for column in columns:
            column_index_counting += 1
            tip_to_show = ''
            if 'tooltip' in column:
                tip_to_show = column['tooltip']
            if 'data_dictionary' in column:
                if 'help' in data_dictionary[column['data_dictionary']]:
                    tip_to_show = data_dictionary[column['data_dictionary']]['help']

            color_to_use = ''
            name_to_sort_on = ''

            if 'value' in column:
                name_to_sort_on = column['value']
            if 'data_dictionary' in column:
                name_to_sort_on = column['data_dictionary']

            if name_to_sort_on:
                if sort_key == name_to_sort_on:
                    color_to_use = color_green
                    column_index_of_sort = column_index_counting

            # tip_to_show += '[' + sort_key + ']'
            if color_to_use:
                if tip_to_show:
                    table_content += '<td style="background-color:rgba' + color_to_use + '" title="' + tip_to_show + '">'
                else:
                    table_content += '<td style="background-color:rgba' + color_to_use + '" >'
            else:
                if tip_to_show:
                    table_content += '<td title="' + tip_to_show + '">'
                else:
                    table_content += '<td>'

            name_to_sort_on = ''
            if 'value' in column:
                name_to_sort_on = column['value']
            if 'data_dictionary' in column:
                name_to_sort_on = column['data_dictionary']

            if name_to_sort_on:
                url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                   'sort=' + name_to_sort_on,
                                                                                   rotate_terms='sortd=fwd|rev',
                                                                                   rotate_key='sort')
            else:
                url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                   rotate_terms='sortd=fwd|rev',
                                                                                   rotate_key='sort')

            header_to_use = ''
            if 'header' in column:
                header_to_use = column['header']

            if 'data_dictionary' in column:
                if 'header' in data_dictionary[column['data_dictionary']]:
                    needs_trust = False
                    if 'needs_trust' in data_dictionary[column['data_dictionary']]:
                        if data_dictionary[column['data_dictionary']]['needs_trust'] == True:
                            needs_trust = True

                    if needs_trust:
                        header_to_use = trust_string + data_dictionary[column['data_dictionary']]['header']
                    else:
                        header_to_use = data_dictionary[column['data_dictionary']]['header']

            if name_to_sort_on != '(iot)':
                csv_line.append(csv_cell_start + header_to_use.replace('<br>', '') + csv_cell_end)
            else:
                column_index_for_iot = column_index_counting

            if url_to_use_here:
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                table_content += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">"""
                table_content += header_to_use
                table_content += """</a>"""
            else:
                table_content += header_to_use
            table_content += '</td>'
        table_content += '</tr>'
        csv_body += ','.join(csv_line) + '<br>'

        row_count = 0

        can_do_service_updates = []
        for id in ids_by_sort_criteria:
            if id in can_do_service_updates_full:
                can_do_service_updates.append(id)
            row_count += 1

            if True:
                csv_line = []
                table_content += '<tr>'
                table_rows += 1

                for column_index in range(0, len(columns)):
                    count_to_show = ''
                    if '(row)' in column_values_by_id[id][column_index]:
                        value_to_use = column_values_by_id[id][column_index].replace('(row)', str(row_count))
                        url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER,
                                                                                           'max_rows=' + str(row_count))
                        color = ''
                    else:
                        value_to_use = column_values_by_id[id][column_index]
                        url_to_use_here = column_url_by_id[id][column_index]
                        color = column_color_by_id[id][column_index]

                        if column_index == column_index_of_sort:
                            if column_sort_values_by_id[id] in sort_counts:
                                count_to_show = "Number of devices with this value is " + str(
                                    sort_counts[column_sort_values_by_id[id]])

                    if count_to_show:
                        if color:
                            table_content += '<td title="' + count_to_show + '" style="background-color:rgba' + color + '">'
                        else:
                            table_content += '<td title="' + count_to_show + '" >'
                    else:
                        if color:
                            table_content += '<td style="background-color:rgba' + color + '">'
                        else:
                            table_content += '<td>'
                    if url_to_use_here:
                        onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                        table_content += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">"""
                        table_content += value_to_use
                        table_content += """</a>"""
                    else:
                        table_content += value_to_use

                    if column_index != column_index_for_iot:
                        csv_line.append(csv_cell_start + value_to_use.replace('<br>', '') + csv_cell_end)

                    table_content += '</td>'

                table_content += '</tr>'
                csv_body += ','.join(csv_line) + '<br>'

        table_content += '</table>'
        table_content += '</center>'

        url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER, 'csv_view=yes')
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
        #        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" +  "view table as csv" + """</a>"""
        table_as_csv_clicker += """<a href="" onclick=""" + onclick + """ style="color:inherit">""" + "view table as csv of " + str(
            table_rows) + " rows" + """</a>"""
        body += table_as_csv_clicker

        # https://www.w3schools.com/tags/att_a_download.asp
        url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER, 'csv_download=yes')
        table_as_csv_clicker = '<a href="' + url_to_use_here + '" download="reports.csv"> download as csv</a>'
        body += '<br><br>'
        body += table_as_csv_clicker

        _ = """
        url_to_use_here = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER, 'xlsx_download=yes')
        table_as_csv_clicker = '<a href="' + url_to_use_here + '" download="reports.xlsx"> download as xlsx</a>'
        body += '<br><br>'
        body += table_as_csv_clicker
        """

        body += table_content

        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

        time_stamps.append(['device table complete', time.time()])

        # 2022.03.01 Start making a 'collection' based on the current filter
        body += '<form method="post" action="">'

        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="collection_selection" selected>' + 'collection_selection' + '</option>'
        body += '</select>'
        body += '<select name="serial" id="serial" hidden>'
        body += '<option value="' + id + '" selected>' + id + '</option>'
        body += '</select>'

        if rows_were_limited:
            body += '<br>!!! Rows were limited based on the max_rows filter value !!!'

        body += '<br>Number of IDs shown is ' + str(len(ids_by_sort_criteria))

        number_user_can_update = len(can_do_service_updates)
        body += '<br>The current user can do service updates on ' + str(number_user_can_update) + ' of them'

        body += """<input hidden type="text" size=35 id="collection" name="collection" value=\"""" + ','.join(
            sorted(can_do_service_updates)) + """\">"""
        body += '<br>'
        body += '<br>'
        if permissions.permission_allowed(environ, 'device_collections_edit'):
            if number_user_can_update:
                body += '<input type="submit" value="Open Collection of ' + str(len(can_do_service_updates)) + ' IDs">'
            else:
                body += '(No collection content to open)'
        else:
            body += 'The current user does not have permissions for collections'
        body += '</form>'

        time_stamps.append(['collections complete', time.time()])

        if 'serial' in query_items:  # this means that there is just one single item, so open up some new content to show
            time_stamps.append(['start single device', time.time()])
            # we are filtered down to a single device, so show config and edit options, if we are allowed

            try:
                site = str(ids[id]['calc_site'])
            except:
                site = '(unknown)'  # for when an ip address is given as the ID

            versions_available = {}
            try:
                # what has this device reported it has running
                datadrop_directory = datadrop_save_path + query_items['serial'] + '/'
                with open(datadrop_directory + 'service_versions', 'r') as f:
                    content = f.read()
                    versions_available = json.loads(content)
            except:
                pass

            try:
                versions_available['image'] = image_version
            except:
                # When ID is the ip address
                image_version = ''
                versions_available['image'] = ''

            if permissions.permission_allowed(environ, 'device_configuration') and permit2_id[id]:
                body += '<br><br>'
                body += '<center>'
                body += trust_string + '<B>Device Configuration: ' + query_items['serial'] + '</B>'
                body += '<br><br>'

                if not trust_string:
                    body += '<table border="1" cellpadding="5">'

                    body += '<tr>'
                    body += '<td>'
                    body += '<B>Category</B>'
                    body += '</td>'
                    body += '<td>'
                    body += '<B>Fielded<br>value</B>'
                    body += '</td>'
                    body += '<td>'
                    body += '<B>Options</B>'
                    body += '</td>'
                    body += '<td title="The minimum image version, or the minimum version of certain service(s). If not supported, this field will be colored purple.">'
                    body += '<B>Version<br>first<br>available</B>'  # image/other first available
                    body += '</td>'
                    body += '<td>'
                    body += '</td>'
                    body += '<td>'
                    body += '<B>Notes</B> (Submitted changes here can take up to one minute to show on the remote device)'
                    body += '</td>'
                    body += '</tr>'

                    # ---------------
                    # Profile
                    # ---------------
                    body += '<tr>'
                    body += '<form method="post" action="">'
                    body += '<td>'
                    body += 'Profile'
                    body += '</td>'

                    id = query_items['serial']
                    body += '<select name="the_selection" id="the_selection" hidden>'
                    body += '<option value="profile_selection" selected>' + 'profile_selection' + '</option>'
                    body += '</select>'
                    body += '<select name="serial" id="serial" hidden>'
                    body += '<option value="' + id + '" selected>' + id + '</option>'
                    body += '</select>'

                    current_value = datastore.get_value_stored(data_store_content, 'device_profile_' + id)
                    preliminary_value = get_profile_preliminary(current_value)

                    body += '<td>'
                    if preliminary_value:
                        body += "(" + preliminary_value + "): "
                    body += current_value
                    body += '</td>'
                    body += '<td>'

                    values_to_show = ['']
                    descriptions_to_show = {}
                    preliminary_to_show = {}
                    permission_to_edit_profiles = permissions.permission_allowed(environ, 'profiles_edit')
                    for key_name in sorted(datastore.get_keys_starting_with('profile_')):
                        # depend on profiles_edit permission, if there is a preliminary mark on the profile.
                        allowed_to_see_it = True
                        preliminary_value = get_profile_preliminary(key_name)
                        if preliminary_value:  # there is some reason this is marked as preliminary
                            # only show to people with edit permission
                            allowed_to_see_it = False
                            if permission_to_edit_profiles:
                                allowed_to_see_it = True

                        if allowed_to_see_it:
                            values_to_show.append(key_name)
                            descriptions_to_show[key_name] = get_profile_description(key_name)
                            preliminary_to_show[key_name] = preliminary_value

                    body += '<select name="selected_parameter" id="selected_parameter">'

                    for key_name in values_to_show:
                        prelim = ''

                        try:
                            prelim = preliminary_to_show[key_name]
                            if prelim:
                                prelim = '(' + prelim + ') '
                        except:
                            pass

                        desc = ''
                        try:
                            if descriptions_to_show[key_name]:
                                desc = ' (' + descriptions_to_show[key_name] + ')'
                        except:
                            pass

                        if current_value == key_name:
                            body += '<option value="' + key_name + '" selected>' + prelim + key_name + desc + '</option>'
                        else:
                            body += '<option value="' + key_name + '">' + prelim + key_name + desc + '</option>'

                    body += '</select>'

                    body += ''
                    body += '</td>'

                    image_first_available = '2.0.0'
                    if is_feature_available(image_first_available, image_version):
                        body += '<td>'
                    else:
                        body += '<td style="background-color:rgba' + color_feature_not_available + '">'
                    body += image_first_available
                    body += '</td>'

                    body += '<td>'
                    body += '<input type="checkbox" value="browser_reset" id="browser_reset" name="browser_reset"/><br>Also do<br>browser<br>reset'
                    body += '<br><br>'
                    body += '<input type="submit" value="Submit">'
                    body += '</td>'
                    body += '<td style="background-color:rgba' + color_yellow_caution + '">'
                    body += 'Image 2.1.5 and later: Changing the profile from "standard" to one with "tabs" will cause a pi browser exit/restart,<br>and potential loss of work in progress!<br>Images before that will not restart, and will not correctly show tabs.'
                    body += '<br><br>' + 'Image 2.1.7 and later: Changing the profile from "standard" to one with "-keys" will cause a pi browser exit/restart,<br>and potential loss of work in progress!<br>Images before that will not restart, and will not correctly allow full keyboard access.'
                    body += '<br><br>' + 'Image 2.2.0 and later: Changing the profile from "standard" to one with "-keys" or "-dk" will cause a pi browser exit/restart,<br>and potential loss of work in progress!<br>Images before that will not correctly disable kiosk mode.'
                    body += '</td>'

                    body += '</form>'
                    body += '</tr>'

                    # ---------------
                    # Define the rows
                    # ---------------
                    all_d_definitions = make_device_parameters()

                    # ---------------
                    # Fill in the dynamic data
                    # ---------------
                    all_d_list = []
                    for def_item in all_d_definitions:

                        # current_value
                        if 'device_ring_assignment_' == def_item['datastore_prefix']:
                            def_item['current_value'] = rings.device_ring_calculated(id)
                        else:
                            def_item['current_value'] = datastore.get_value_stored(data_store_content, def_item['datastore_prefix'] + id)

                        # values_to_show
                        if 'device_managementID_' == def_item['datastore_prefix']:
                            def_item['values_to_show'] = [''] + management.get_management_block_list(data_store_content)
                        elif 'device_reset_request_' == def_item['datastore_prefix']:
                            try:
                                def_item['values_to_show'] = ['', str(ids[id]['datadrop_runner_image_version']) + \
                                    ":" + str(ids[id]['boot_count'])]
                            except:
                                def_item['values_to_show'] = ''
                        elif 'device_reboot_request_' == def_item['datastore_prefix']:
                            try:
                                def_item['values_to_show'] = ['', str(ids[id]['datadrop_runner_image_version']) + ":" + str(ids[id]['boot_count'])]
                            except:
                                def_item['values_to_show'] = ''
                        elif 'device_screengrab_request_' == def_item['datastore_prefix']:
                            try:
                                def_item['values_to_show'] = ['', str(ids[id]['datadrop_runner_image_version']) + ":" + str(ids[id]['screengrab_count']).zfill(6)]
                            except:
                                def_item['values_to_show'] = ''

                        # reported_value
                        if 'device_screen_resolution_' == def_item['datastore_prefix']:
                            try:
                                def_item['reported_value'] = str(ids[id]['screen_reported'])
                            except:
                                def_item['reported_value'] = ''

                        all_d_list.append(def_item)

                    # ---------------
                    # Build the content
                    # ---------------
                    for d in all_d_list:
                        development_marker = ''
                        use_it = True
                        if 'is_development' in d:
                            use_it = False
                            if d['is_development']:
                                if permissions.permission_allowed(environ, 'development_read'):
                                    use_it = True
                                    development_marker = '--- Development ---<br>'

                        if use_it:
                            body += '<tr>'
                            body += '<form method="post" action="">'
                            body += '<td>'
                            body += development_marker
                            body += d['title']
                            body += '</td>'

                            id = query_items['serial']
                            body += '<select name="the_selection" id="the_selection" hidden>'
                            body += '<option value="' + d['option_value'] + '" selected>' + d[
                                'option_value'] + '</option>'
                            body += '</select>'
                            body += '<select name="serial" id="serial" hidden>'
                            body += '<option value="' + id + '" selected>' + id + '</option>'
                            body += '</select>'

                            current_value = d['current_value']
                            if 'current_value_to_show_rule' in d:
                                if d['current_value_to_show_rule'] == 'screen_zoom':
                                    if not current_value:
                                        current_value = "100"
                            if not current_value:
                                current_value = ""

                            body += '<td>'
                            body += development_marker
                            body += current_value

                            body += '</td>'
                            body += '<td>'
                            body += development_marker

                            if d['values_to_show'] is None:
                                body += '<input type="text" size=25 name="' + d[
                                    'text_field_name'] + '" value="' + current_value + '\">'
                            else:
                                body += '<select name="selected_parameter" id="selected_parameter">'
                                for key_name in d['values_to_show']:
                                    name_to_show = ''
                                    if key_name:
                                        if 'name_to_show_rule' in d:
                                            if d['name_to_show_rule'] == 'screen_settings':
                                                name_to_show = key_name.split()[0].replace('(', ' (')
                                            else:
                                                name_to_show = key_name
                                        else:
                                            name_to_show = key_name

                                    if current_value == key_name:
                                        body += '<option value="' + key_name + '" selected>' + name_to_show + '</option>'
                                    else:
                                        body += '<option value="' + key_name + '">' + name_to_show + '</option>'
                                body += '</select>'

                            reported_value = ''
                            if 'reported_value' in d:
                                reported_value = d['reported_value']
                            if reported_value:
                                body += '   {last report = ' + reported_value + '}'

                            body += ''
                            body += '</td>'

                            if algorithms.allowed_by_versions(d['requires'], versions_available):
                                body += '<td>'
                            else:
                                body += '<td style="background-color:rgba' + color_feature_not_available + '">'
                            body += development_marker

                            requires = ""
                            for list_of_ored_values in d['requires']:
                                ored_list = ""
                                for version_id in list_of_ored_values:
                                    if ored_list:
                                        ored_list += "<br>OR "
                                    ored_list += list_of_ored_values[version_id] + ' : ' + version_id
                                if requires:
                                    requires += "<br>AND<br>"
                                requires += ored_list
                            body += requires
                            body += '</td>'

                            body += '<td>'
                            body += development_marker
                            body += '<input type="submit" value="Submit">'
                            body += '</td>'
                            body += '<td style="background-color:rgba' + d['color_for_info_box'] + '">'
                            body += development_marker
                            body += d['text_for_info_box']
                            body += '</td>'

                            body += '</form>'
                            body += '</tr>'

                    # ---------------
                    # End
                    # ---------------
                    body += '</table>'
                    body += '</center>'

            time_stamps.append(['device_upload_single_device', time.time()])
            if deviceupload.has_correct_permissions(environ, query_items['serial']):
                try:
                    # -----------------------
                    url_to_use_download = url_to_use
                    body += '<br><br>'
                    body += '<center>'
                    body += '<a href="' + url_to_use_download + '/deviceupload' + '?serial=' + query_items[
                        'serial'] + '"> '
                    body += '<B>Click to review Device file content: ' + query_items['serial'] + '</B>'
                    body += '</a>'
                    body += '</center>'
                except:
                    body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            try:
                site = str(ids[id]['calc_site'])
                management_block = str(ids[id]['calc_management_block'])
            except:
                # make it work for id = '10.226.171.139'
                site = ''
                management_block = ''

            try:
                # -----------------------
                body += '<br><br>'
                body += '<center>'
                url_to_use_here = url_to_use + REQUEST_URI + "?serial=" + id
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + '[click here to refresh this page]' + """</a>"""
                body += '</center>'
            except:
                pass

            try:
                # -----------------------
                body += '<br><br>'
                body += '<center>'
                body += '<B>Screen Grabs: ' + query_items['serial'] + '</B>'
                body += '<br>(90 day retention)'
                body += '<br><br>'

                if True:
                    body += '<table border="1" cellpadding="5">'

                    body += '<tr>'
                    body += '<td>'
                    body += '<B>Grabbed</B>'
                    body += '</td>'
                    body += '<td>'
                    body += '<B>View</B>'
                    body += '</td>'
                    body += '</tr>'

                    # ---------------
                    # Data Items
                    # ---------------
                    fields_to_show, base = upload.get_upload_files_and_base(filter='screen_' + query_items['serial'])

                    url_to_use_download = url_to_use
                    try:
                        for file_name in fields_to_show:
                            body += '<tr>'
                            body += '<td>'
                            body += file_name
                            body += '</td>'

                            body += '<td>'
                            file_extensions = ['.txt', '.png']
                            if file_name[-4:] in file_extensions:
                                # the target="_blank" makes the click open a new tab for the result
                                body += '<a href="' + url_to_use_download + '/download' + '?filetoview=' + file_name + '" target="_blank"> ' + 'view' + '</a>'
                            body += '</td>'

                            body += '</tr>'

                    except:
                        pass

                    body += '</table>'
                body += '</center>'
            except:
                pass

            try:
                """
{
    "service:pi_monitor":"M.2.2",
    "service:pi_runner":"R.2.1",
    "image":"2.1.8",
    "Memory:MemAvailable":"3656785920",
    "service:pi_hmi":"H.1.9",
    "timezone":"UTC",
    "serial":"10000000e3669edf",
    "eth0mac":"dc:a6:32:96:56:c2",
    "uptime":"205",
    "service:pi_bluetooth":"B.1.5",
    "temperature":"41.8",
    "Memory:Cached":"313511936",
    "hostname":"cah-rp-10000000e3669edf",
    "source":"runner",
    "version":"R.2.1",
    "service:pi_network":"N.1.2",
    "Memory:Buffers":"18247680",
    "screen_width":"1280",
    "boot_count":"2",
    "wlan0mac":"dc:a6:32:96:56:c4",
    "Memory:MemFree":"3470680064",
    "time":1626972764.163872,
    "Memory:MemTotal":"4013187072",
    "screen_height":"720"
}                """
                # -----------------------
                body += '<br><br>'
                body += '<center>'
                body += trust_string + '<B>Device Runner Report: ' + query_items['serial'] + '</B>'
                body += '<br><br>'

                body_table = ''
                if not trust_string:
                    body_table += '<table border="1" cellpadding="5">'

                    body_table += '<tr>'
                    body_table += '<td>'
                    body_table += '<B>Field</B>'
                    body_table += '</td>'
                    body_table += '<td>'
                    body_table += '<B>Value</B>'
                    body_table += '</td>'
                    body_table += '</tr>'

                    # ---------------
                    # Data Items
                    # ---------------
                    fields_to_show = []
                    fields_to_show.append(['wlan0', 'wlan0mac'])
                    fields_to_show.append(['wlan1', 'wlan1mac'])
                    fields_to_show.append(['eth0', 'eth0mac'])
                    fields_to_show.append(['eth1', 'eth1mac'])
                    try:
                        # what has this device reported it has running
                        datadrop_runner_file = datadrop_save_path + query_items['serial'] + '/runner'

                        runner_content = json.loads(open(datadrop_runner_file, 'r').read())

                        for field in fields_to_show:
                            body_table += '<tr>'
                            body_table += '<td>'
                            body_table += field[0]
                            body_table += '</td>'

                            body_table += '<td>'
                            if field[1] in runner_content:
                                body_table += format_mac_address(str(runner_content[field[1]]))
                            body_table += '</td>'
                            body_table += '</tr>'
                    except:
                        pass
                    body_table += '</table>'
                    body += body_table

                body_table = ''
                if not trust_string:
                    body += '<br><br><br>'
                    body_table += '<table border="1" cellpadding="5">'
                    body_table += '<tr>'
                    body_table += '<td>'
                    body_table += 'MAC: ' + format_mac_address(str(runner_content['wlan0mac'])) + '<br>'
                    body_table += 'Host: cah-rp-' + query_items['serial'] + '<br>'
                    body_table += 'Group: RASPBERRY_PI' + '<br>'
                    body_table += 'POLICY: CORPORATE' + '<br>'
                    body_table += 'SITE-ID: ' + str(ids[id]['calc_site']) + '<br>'

                    body_table += '</td>'
                    body_table += '</tr>'
                    body_table += '</table>'
                body += body_table

                body += '</center>'
            except:
                pass

            try:
                # -----------------------
                body += '<br><br>'
                body += '<center>'
                body += trust_string + '<B>Devices Connected Report: ' + query_items['serial'] + '</B>'
                body += '<br><br>'

                if not trust_string:
                    body += '<table border="1" cellpadding="5">'

                    body += '<tr>'
                    body += '<td>'
                    body += '<B>Field</B>'
                    body += '</td>'
                    body += '<td>'
                    body += '<B>Value</B>'
                    body += '</td>'
                    body += '</tr>'

                    # ---------------
                    # Data Items
                    # ---------------
                    fields_to_show = []
                    fields_to_show.append(['event0', 's_logging_device_event0'])
                    fields_to_show.append(['event1', 's_logging_device_event1'])
                    fields_to_show.append(['event2', 's_logging_device_event2'])
                    fields_to_show.append(['event3', 's_logging_device_event3'])
                    fields_to_show.append(['event4', 's_logging_device_event4'])
                    fields_to_show.append(['event5', 's_logging_device_event5'])
                    fields_to_show.append(['event6', 's_logging_device_event6'])

                    fields_to_show.append(['mouse0', 's_logging_device_mouse0'])
                    fields_to_show.append(['mouse1', 's_logging_device_mouse1'])
                    fields_to_show.append(['mouse2', 's_logging_device_mouse2'])
                    fields_to_show.append(['mouse3', 's_logging_device_mouse3'])
                    fields_to_show.append(['mouse4', 's_logging_device_mouse4'])
                    fields_to_show.append(['mouse5', 's_logging_device_mouse5'])
                    fields_to_show.append(['mouse6', 's_logging_device_mouse6'])

                    try:
                        # what has this device reported it has running
                        datadrop_runner_file = datadrop_save_path + query_items['serial'] + '/runner'

                        runner_content = json.loads(open(datadrop_runner_file, 'r').read())

                        for field in fields_to_show:
                            if field[1] in runner_content:
                                body += '<tr>'
                                body += '<td>'
                                body += field[0]
                                body += '</td>'

                                body += '<td>'
                                body += (str(runner_content[field[1]]))
                                body += '</td>'

                                body += '</tr>'

                    except:
                        pass

                    body += '</table>'
                    body += '</center>'
            except:
                pass

            time_stamps.append(['device_edit_single_device', time.time()])

            if permissions.permission_allowed(environ, 'device_edit') and permit2_id[id]:
                # -----------------------
                because_of_permission = ' (device_edit)'

                device_ring = rings.device_ring_calculated(id)

                body += '<br><br>'
                body += '<center>'
                body += trust_string + '<B>Single Device Service Pull List: ' + query_items[
                    'serial'] + '</B>' + because_of_permission
                body += '<br><br>'

                if not trust_string:
                    runner_first_available = 'R.3.0'

                    field_service_versions = {}
                    try:
                        # what has this device reported it has running
                        datadrop_directory = datadrop_save_path + query_items['serial'] + '/'
                        with open(datadrop_directory + 'service_versions', 'r') as f:
                            content = f.read()
                            field_service_versions = json.loads(content)
                    except:
                        pass

                    is_available = False
                    if 'pi_runner' in field_service_versions:
                        if field_service_versions['pi_runner'] >= runner_first_available:
                            is_available = True

                    if is_available:
                        body += '<table border="1" cellpadding="5">'

                        # list out the rings
                        body += '<tr>'
                        body += '<td>'
                        body += '<center>'
                        body += '<B>Ring</B>'
                        body += '</center>'
                        body += '</td>'

                        body += '<td>'
                        body += '<form method="post" action="">'
                        body += '<select name="the_selection" id="the_selection" hidden>'
                        body += '<option value="service_ring_pull_selection" selected>' + 'service_ring_pull_selection' + '</option>'
                        body += '</select>'

                        body += '<select multiple name="serial" id="serial" hidden>'
                        for id in sorted(ids):
                            body += '<option value="' + id + '" selected>' + id + '</option>'
                        body += '</select>'

                        body += '<select name="' + 'ring_selected' + '" id="' + 'ring_selected' + '">'
                        body += '<option value="" selected>' + '' + '</option>'
                        for ring in rings.get_all_rings():
                            body += '<option value="' + ring + '">' + ring + '</option>'

                        body += '</select>'

                        body += '</td>'

                        body += '<td>'
                        body += '<input type="submit" value="Submit for Pull">'
                        body += '</form>'
                        body += '</td>'
                        body += '</tr>'

                        body += '<tr>'
                        body += '<td>'
                        body += '<center>--- OR ---</center>'
                        body += '</td>'
                        body += '</tr>'

                        # list out the service packs
                        body += '<tr>'
                        body += '<td>'
                        body += '<center>'
                        body += '<B>Service Pack</B>'
                        body += '</center>'
                        body += '</td>'

                        body += '<td>'
                        body += '<form method="post" action="">'
                        body += '<select name="the_selection" id="the_selection" hidden>'
                        body += '<option value="service_spr_pull_selection" selected>' + 'service_spr_pull_selection' + '</option>'
                        body += '</select>'

                        body += '<select multiple name="serial" id="serial" hidden>'
                        for id in sorted(ids):
                            body += '<option value="' + id + '" selected>' + id + '</option>'
                        body += '</select>'

                        body += '<select name="' + 'spr_selected' + '" id="' + 'spr_selected' + '">'
                        body += '<option value="" selected>' + '' + '</option>'
                        for sp, ring in rings_vs_service_pack:
                            if ring:
                                body += '<option value="' + sp + '_' + ring + '">' + sp + ' (Ring ' + ring + ')' + '</option>'
                            else:
                                body += '<option value="' + sp + '">' + sp + '</option>'

                        body += '</select>'

                        body += '</td>'

                        body += '<td>'
                        body += '<input type="submit" value="Submit for Pull">'
                        body += '</form>'
                        body += '</td>'

                        body += '<td>'

                        body += '<select name="' + 'release_notes' + '" id="' + 'release_notes' + '">'
                        body += '<option>' + 'Release Notes:---------------' + '</option>'
                        for service_pack_release in sorted(service_pack_release_notes, reverse=True):
                            body += '<option>' + '</option>'
                            body += '<option>' + service_pack_release + ' (' + \
                                    service_pack_release_notes[service_pack_release]['date'] + ') </option>'
                            for item_line in build_format_note(service_pack_release_notes[service_pack_release]['note'],
                                                               tab=4, width=60).split('\n'):
                                body += '<option>' + item_line.replace('    ', '<nbsp><nbsp><nbsp><nbsp>') + '</option>'
                        body += '</select>'

                        body += '</td>'

                        body += '</tr>'

                        body += '<tr>'
                        body += '<td>'
                        body += '<center>--- OR ---</center>'
                        body += '</td>'
                        body += '</tr>'

                        body += '<tr>'
                        body += '<td title="The service running on the device.">'
                        body += '<center>'
                        body += '<B>Service</B>'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="This is the version of the service that is currently running on the device.">'
                        body += '<center>'
                        body += '<B>On Device</B>'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="This is the version that the assigned ring says that we should be using.">'
                        body += '<center>'
                        body += '<B>Ring Assigned Version' + '<br>' + ' (ring ' + device_ring + ')' + '</B>'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="This is the version that was last requested to be pulled to the device.">'
                        body += '<center>'
                        body += '<B>Last Request</B>'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="This is the version that is currently being marked as the next pull request.">'
                        body += '<center>'
                        body += '<B>Current Pull Request</B><br>(Cleared when pulled)'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="These are the versions that are available to be marked as the current pull request.">'
                        body += '<center>'
                        body += '<B>Options</B><br>(SP limited list)'
                        body += '</center>'
                        body += '</td>'
                        body += '<td title="Use the submit button, to assign all values at once. If the pi is checking in, then within a minute or two, the update will get pulled, and new versions will be reported a few minutes after thatn.">'
                        body += '(only if runner ' + runner_first_available + ' or higher)'
                        body += '</td>'
                        body += '<td title="Click on the release notes, to see the complete version history for each service.">'
                        body += 'Release Notes'
                        body += '</td>'
                        body += '</tr>'

                        # ---------------
                        # Services
                        # ---------------
                        _ = """

    keep a list of
    1) what is on the device
    2) what has been asked to be on the device.
        When asked, what was
            A) the existing image version
            B) boot count
            C) requested service setting (Version number)

    Keep the last request, as a marker of history.
    Make the pickup point be cleared when grabbed by the device (like screen grab trigger), and show that also.


                        """

                        try:
                            # what has this device reported it has running
                            datadrop_directory = datadrop_save_path + query_items['serial'] + '/'
                            field_service_versions = {}
                            try:
                                with open(datadrop_directory + 'service_versions', 'r') as f:
                                    content = f.read()
                                    field_service_versions = json.loads(content)
                            except:
                                pass

                            # list out the services
                            # { "pi_runner":{ "R.1.0":"/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py" } }
                            service_names = services_available.keys()

                            number_of_services = len(service_names)
                            count = -1
                            body += '<form method="post" action="">'
                            body += '<select name="the_selection" id="the_selection" hidden>'
                            body += '<option value="service_version_pull_selection" selected>' + 'service_version_pull_selection' + '</option>'
                            body += '</select>'

                            body += '<select name="serial" id="serial" hidden>'
                            body += '<option value="' + id + '" selected>' + id + '</option>'
                            body += '</select>'

                            try:
                                current_values = json.loads(
                                    datastore.get_value_stored(data_store_content, 'device_service_' + id))
                            except:
                                current_values = {}

                            for service_name in sorted(service_names):
                                count += 1
                                versions = services_available[service_name].keys()
                                values_to_show = ['']
                                for key_name in sort_service_versions(versions):
                                    values_to_show.append(key_name)

                                service_tag = 'service_' + service_name

                                ring_assigned_version = datastore.get_value_stored(data_store_content,
                                                                                   'device_ringed_service' + device_ring + '_' + service_name)

                                last_request = datastore.get_value_stored(data_store_content,
                                                                          'device_service_request_' + id + '_last_' + service_name)
                                ready_to_pull = datastore.get_value_stored(data_store_content,
                                                                           'device_service_request_' + id + '_pull_' + service_name)

                                current_value = None
                                if service_name in current_values:
                                    current_value = current_values[service_name]

                                field_value = ''
                                if service_name in field_service_versions:
                                    field_value = str(field_service_versions[service_name])

                                body += '<tr>'
                                body += '<td>'
                                body += '<center>'
                                body += service_name
                                body += '</center>'
                                body += '</td>'

                                body += '<td>'
                                body += '<center>'
                                body += field_value
                                body += '</center>'
                                body += '</td>'

                                if ring_assigned_version:
                                    if ring_assigned_version > field_value:
                                        color = "(100, 100, 255, 0.3)"
                                        body += '<td style="background-color:rgba' + color + '">'
                                    else:
                                        body += '<td>'
                                else:
                                    body += '<td>'
                                body += '<center>'
                                body += ring_assigned_version
                                body += '</center>'
                                body += '</td>'

                                body += '<td>'
                                body += '<center>'
                                body += last_request
                                body += '</center>'
                                body += '</td>'

                                body += '<td>'
                                body += '<center>'
                                body += ready_to_pull
                                body += '</center>'
                                body += '</td>'

                                body += '<td>'
                                body += '<select name="' + service_tag + '" id="' + service_tag + '">'
                                for key_name in values_to_show:
                                    if current_value is None:
                                        current_value = field_value

                                    if False:
                                        if current_value == key_name:
                                            body += '<option value="' + key_name + '" selected>' + key_name + '</option>'
                                        else:
                                            body += '<option value="' + key_name + '">' + key_name + '</option>'
                                    else:
                                        body += '<option value="' + key_name + '">' + key_name + '</option>'

                                body += '</td>'

                                if count == 0:
                                    body += '<td rowspan="' + str(number_of_services) + '">'
                                    body += '<input type="submit" value="Submit for Pull">'
                                    body += '</td>'

                                body += '<td>'
                                body += '<select name="' + 'release_notes' + '" id="' + 'release_notes' + '">'
                                for key_name in codeupload.get_pi_service_formatted_release_notes(service_name):
                                    body += '<option>' + key_name + '</option>'
                                    # body += '<option disabled>' + key_name + '</option>'
                                body += '</select>'
                                body += '</td>'

                                body += '</tr>'
                            body += '</form>'

                        except:
                            pass

                        body += '</table>'
                        body += '</center>'

                    else:
                        body += '<center>'
                        body += 'Not available if runner before ' + runner_first_available + " -> " + json.dumps(
                            field_service_versions)
                        body += '</center>'

                # -----------------------
                body += '<br><br>'
                body += '<center>'

                body += '<B>Create a Device Comment: ' + query_items['serial'] + '</B>' + because_of_permission

                body += '<form method="post" action="">'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'

                body += '<td>'
                TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
                # 20210406201829131704
                body += '<B>When did it happen ?</B>'
                body += '<br>Current local time is<br>'
                body += """<input type="text" size=35 id="device_comment_time" name="device_comment_time" value=\"""" + '' + """\">"""
                body += '<script>var interval = setInterval(function() {var dt = new Date();document.getElementById("device_comment_time").setAttribute("value",dt);}, 2000);</script>'
                body += '</td>'
                body += '<td>'
                body += '<B>Comment</B>'
                body += '</td>'
                body += '<td>'
                body += '</td>'
                body += '</tr>'

                body += '<tr>'

                id = query_items['serial']
                body += '<select name="the_selection" id="the_selection" hidden>'
                body += '<option value="device_comment_selection" selected>' + 'device_comment_selection' + '</option>'
                body += '</select>'
                body += '<select name="serial" id="serial" hidden>'
                body += '<option value="' + id + '" selected>' + id + '</option>'
                body += '</select>'

                body += '<td>'
                body += '<input type="datetime-local" id="when_did_it_happpen" name="when_did_it_happpen">'
                body += '</td>'

                body += '<td>'
                body += """<input type="text" size=35 name="device_comment_text" value=\"""" + '' + """\">"""
                body += '</td>'

                body += '<td>'
                body += '<input type="submit" value="Submit">'
                body += '</td>'

                body += '</tr>'

                body += '</table>'
                body += '</form>'
                body += '</center>'

                # -----------------------
                body += '<br><br>'
                body += '<center>'

                body += '<B>Device Comments: (most recent at top) ' + query_items[
                    'serial'] + '</B>' + because_of_permission

                body += '<table border="1" cellpadding="5">'
                body += '<tr>'

                body += '<td>'
                body += '<B>Ident</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Comment</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Review</B>'
                body += '</td>'
                body += '</tr>'

                if id in ids:
                    for item in sorted(ids[id]['my_device_comment_keys'], reverse=True):
                        body += '<tr>'
                        body += '<td>'
                        body += item
                        body += '</td>'
                        body += '<td>'
                        try:
                            body += data_store_content[item].replace(',', ',<br>')
                        except:
                            pass
                        body += '</td>'

                        body += '<td>'
                        body += '<form method="post" action="">'
                        id = query_items['serial']
                        body += '<select name="the_selection" id="the_selection" hidden>'
                        body += '<option value="device_comment_review_selection" selected>' + 'device_comment_review_selection' + '</option>'
                        body += '</select>'
                        body += '<select name="serial" id="serial" hidden>'
                        body += '<option value="' + id + '" selected>' + id + '</option>'
                        body += '</select>'

                        review_name = item.replace('_json', '_review')
                        body += """<input hidden type="text" size=55 name="device_comment_review_name" value=\"""" + review_name + """\">"""
                        body += '<br>'

                        current_value = ''
                        if review_name in data_store_content:
                            try:
                                the_review = json.loads(data_store_content[review_name])
                                if 'review' in the_review:
                                    current_value = str(the_review['review'])
                            except:
                                pass

                        body += """<input type="text" size=55 name="device_comment_review_text" value=\"""" + current_value + """\">"""
                        body += '<input type="submit" value="Submit">'

                        body += '</form>'
                        body += '</td>'

                        body += '</tr>'
                    body += '</table>'
                    body += '</center>'

            if permissions.permission_allowed(environ, 'datamine_edit') and permit2_id[id]:
                chunk_size = 70

                # -----------------------
                because_of_permission = ' (datamine_edit)'

                # -----------------------
                body += '<br><br>'
                body += '<center>'

                body += '<B>Device Datamine summaries: ' + query_items['serial'] + '</B>' + because_of_permission

                data_mining_list = []
                data_mining_list.append({'title': 'daily wifi', 'category': 'wifidaily',
                                         'description_long': 'This shows the WiFi channels that are active. If the channels change a lot, that means the device is moving throughout a space, and picking up different access points. Normal reports is 2 per hour. \nIf there are more than that, it means that there was a change, and extra reports are sent when the channel changes.'})
                data_mining_list.append({'title': 'daily lan', 'category': 'landaily'})

                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += '<B>Summaries</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Description</B>'
                body += '</td>'
                body += '</tr>'

                for data_mining_item in data_mining_list:
                    body += '<tr>'
                    body += '<td>'
                    url_to_use_here = url_to_use + "/datamine?serial=" + query_items['serial'] + ',category=' + \
                                      data_mining_item['category']
                    onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + \
                            data_mining_item['title'] + """</a>"""
                    body += '</td>'

                    body += '<td>'
                    if 'description_long' in data_mining_item:
                        body += split_content_by_chunk_size(data_mining_item['description_long'], chunk_size).replace(
                            '\n', '<br>')
                    body += '</td>'

                    body += '</tr>'

                body += '</table>'
                body += '</center>'

                # -----------------------
                body += '<br><br>'
                body += '<center>'

                body += '<B>Device Datamining raw data: ' + query_items['serial'] + '</B>' + because_of_permission

                data_mining_list = []

                # all of these need to come from Data dictionary only
                if False:
                    data_mining_list.append({'title': 'disk', 'runner_raw': 'disk_use'})
                    data_mining_list.append({'title': 'inode', 'runner_raw': 'inode_use'})
                    data_mining_list.append({'title': 'Temperature', 'runner_raw': 'temperature'})
                    data_mining_list.append({'title': 'Network Use', 'runner_raw': 'networkuse', 'scale': 'log'})
                    data_mining_list.append({'title': 'Response Time', 'runner_raw': 'responsetime', 'scale': 'log'})
                    data_mining_list.append({'title': 'Response Exceptions', 'runner_raw': 'responsexce'})
                    data_mining_list.append(
                        {'title': 'Bluetooth_connected', 'runner_raw': 's_bluetooth_connected', 'scale': 'log'})
                    data_mining_list.append({'title': 'Bluetooth_selected', 'runner_raw': 's_bluetooth_selected'})
                    data_mining_list.append({'title': 'Bluetooth_specials', 'runner_raw': 's_bluetooth_specials'})
                    data_mining_list.append({'title': 'Logging_HID', 'runner_raw': 's_logging_HID'})
                    data_mining_list.append({'title': 'Logging_RS6000',
                                             'runner_raw': 's_logging_RS6000'})  # on pi these are /dev/shm/shared_logging_RS6000
                    data_mining_list.append({'title': 'Timediff', 'runner_raw': 'timediff'})
                    data_mining_list.append({'title': 'Uptime', 'runner_raw': 'uptime'})

                for key in data_dictionary_complete['description_short_sorted_keys']:

                    #                for key in sorted(data_dictionary.keys()):
                    data_item = data_dictionary[key]

                    if 'runner_raw' in data_item:
                        to_plot_as_number = ''
                        scale = ''
                        minimum = ''
                        maximum = ''
                        plot_algorithm = ''
                        help_text = ''
                        description_text = ''
                        title_text = ''
                        if 'description_short' in data_item:
                            title_text = data_item['description_short']

                        if 'description_long' in data_item:
                            description_text = data_item['description_long']
                        elif 'help' in data_item:
                            description_text = data_item['help']
                        if 'help' in data_item:
                            help_text = data_item['help']
                        if 'scale' in data_item:
                            scale = data_item['scale']
                        if 'to_plot_as_number' in data_item:
                            to_plot_as_number = data_item['to_plot_as_number']
                        if 'minimum' in data_item:
                            minimum = data_item['minimum']
                        if 'maximum' in data_item:
                            maximum = data_item['maximum']
                        if 'plot_algorithm' in data_item:
                            plot_algorithm = data_item['plot_algorithm']

                        data_mining_list.append({'title': title_text,
                                                 'runner_raw': data_item['runner_raw'],
                                                 'maximum': maximum,
                                                 'minimum': minimum,
                                                 'scale': scale,
                                                 'help': help_text,
                                                 'description_long': description_text,
                                                 'to_plot_as_number': to_plot_as_number,
                                                 'plot_algorithm': plot_algorithm})

                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += '<B>Raw Data<br>(minute by minute)</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Hourly Data<br>(minute data<br>rolled up to be hour by hour)</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Description</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Hourly Data<br>(table)</B>'
                body += '</td>'
                body += '</tr>'

                for data_mining_item in data_mining_list:
                    if True:
                        body += '<tr>'

                        scale = ''
                        to_plot_as_number = ''
                        minimum = ''
                        maximum = ''
                        plot_algorithm = ''
                        if 'scale' in data_mining_item:
                            scale = data_mining_item['scale']
                        if 'to_plot_as_number' in data_mining_item:
                            to_plot_as_number = data_mining_item['to_plot_as_number']
                        if 'minimum' in data_mining_item:
                            minimum = data_mining_item['minimum']
                        if 'maximum' in data_mining_item:
                            maximum = data_mining_item['maximum']
                        if 'plot_algorithm' in data_mining_item:
                            plot_algorithm = data_mining_item['plot_algorithm']

                        body += '<td>'
                        if 'runner_raw' in data_mining_item:
                            if data_mining_item['title']:
                                url_to_use_here = url_to_use + "/datamine?serial=" + query_items[
                                    'serial'] + ',runner_raw=' + data_mining_item[
                                                      'runner_raw'] + ',plot_algorithm=' + plot_algorithm + ',minimum=' + minimum + ',maximum=' + maximum + ',scale=' + scale + ',to_plot_as_number=' + to_plot_as_number
                                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + \
                                        data_mining_item['title'] + """</a>"""
                        body += '</td>'

                        body += '<td>'
                        if 'runner_raw' in data_mining_item:
                            if data_mining_item['title']:
                                url_to_use_here = url_to_use + "/datamine?serial=" + query_items[
                                    'serial'] + ',runner_raw=' + data_mining_item[
                                                      'runner_raw'] + ',view=summary' + ',plot_algorithm=' + plot_algorithm + ',minimum=' + minimum + ',maximum=' + maximum + ',scale=' + scale + ',to_plot_as_number=' + to_plot_as_number

                                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + \
                                        data_mining_item['title'] + """</a>"""
                        body += '</td>'

                        body += '<td>'
                        if 'description_long' in data_mining_item:
                            body += split_content_by_chunk_size(data_mining_item['description_long'],
                                                                chunk_size).replace('\n', '<br>')
                        body += '</td>'

                        body += '<td>'
                        if 'runner_raw' in data_mining_item:
                            if data_mining_item['title']:
                                url_to_use_here = url_to_use + "/datamine?serial=" + query_items[
                                    'serial'] + ',runner_raw=' + data_mining_item[
                                                      'runner_raw'] + ',view=summary' + ',plot_algorithm=' + plot_algorithm + ',minimum=' + minimum + ',maximum=' + maximum + ',scale=' + scale + ',to_plot_as_number=' + to_plot_as_number + ',view_format=data_'

                                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + \
                                        data_mining_item['title'] + """</a>"""
                        body += '</td>'

                        body += '</tr>'

                body += '</table>'
                body += '</center>'

            time_stamps.append(['device_push_single_device', time.time()])
            if permissions.permission_allowed(environ, 'device_edit') and permit2_id[id]:
                # -----------------------
                because_of_permission = ' (device_edit)'

                # -----------------------
                body += '<br><br>'
                body += '<center>'
                body += '<B>Device Push List: ' + query_items['serial'] + '</B>' + because_of_permission
                body += '<br><br>'

                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += '<B>Category</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>Current Value</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>New Value</B>'
                body += '</td>'
                body += '</tr>'

                try:
                    # what has this device reported it has running
                    datadrop_directory = datadrop_save_path + query_items['serial'] + '/'
                    field_service_versions = {}
                    try:
                        with open(datadrop_directory + 'service_versions', 'r') as f:
                            content = f.read()
                            field_service_versions = json.loads(content)
                    except:
                        pass

                    # list out the services
                    services_available = codeupload.get_pi_services()
                    # { "pi_runner":{ "R.1.0":"/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py" } }
                    service_names = services_available.keys()

                    number_of_services = len(service_names)
                    count = -1
                    body += '<form method="post" action="">'
                    body += '<select name="the_selection" id="the_selection" hidden>'
                    body += '<option value="service_version_push_selection" selected>' + 'service_version_push_selection' + '</option>'
                    body += '</select>'

                    body += '<select name="serial" id="serial" hidden>'
                    body += '<option value="' + id + '" selected>' + id + '</option>'
                    body += '</select>'

                    try:
                        current_values = json.loads(
                            datastore.get_value_stored(data_store_content, 'device_service_' + id))
                    except:
                        current_values = {}

                    for service_name in sorted(service_names):
                        # if service_name in ['pi_runner', 'pi_monitor', 'pi_network', 'pi_hmi']:
                        count += 1
                        body += '<tr>'
                        body += '<td>'
                        body += service_name
                        body += '</td>'

                        versions = services_available[service_name].keys()
                        values_to_show = ['']
                        for key_name in sorted(versions, reverse=True):
                            values_to_show.append(key_name)

                        service_tag = 'service_' + service_name

                        current_value = None

                        field_value = ''
                        if service_name in field_service_versions:
                            field_value = str(field_service_versions[service_name])

                        body += '<td>'
                        body += field_value
                        body += '</td>'

                        body += '<td>'
                        body += '<select name="' + service_tag + '" id="' + service_tag + '">'
                        for key_name in values_to_show:
                            # no longer show the fielded as the push. We don't really want to push over again.
                            # if current_value is None:
                            #    current_value = field_value
                            if current_value == key_name:
                                body += '<option value="' + key_name + '" selected>' + key_name + '</option>'
                            else:
                                body += '<option value="' + key_name + '">' + key_name + '</option>'

                        body += '</td>'

                        if count == 0:
                            body += '<td rowspan="' + str(number_of_services) + '">'
                            body += '<input type="submit" value="Submit for Push">'
                            body += '</td>'

                        body += '</tr>'
                    body += '</form>'

                except:
                    pass

                body += '</table>'
                body += '</center>'

                try:
                    datadrop_directory = datadrop_save_path + query_items['serial'] + '/'
                    datadrop_items = os.listdir(datadrop_directory)
                    time_of_scan = time.time()

                    # -----------------------
                    body += '<br><br>'
                    body += '<center>'
                    body += '<B>Device DataDrop: ' + query_items['serial'] + '</B>'
                    body += '<br><br>'
                    body += '<table border="1" cellpadding="5">'

                    for item in sorted(datadrop_items):
                        last_modified_time = time_of_scan + 1  # make it report -1 if something goes wrong
                        try:
                            file_to_use = datadrop_directory + item
                            content = ''
                            with open(file_to_use, 'r') as f:
                                content = f.read()
                            fileStatsObj = os.stat(file_to_use)
                            last_modified_time = fileStatsObj[stat.ST_MTIME]
                        except:
                            pass

                        body += '<tr>'
                        body += '<td title="The number below the name is the age of the data.">'
                        body += item + '<br><br>' + convert_seconds_to_human(
                            time_of_scan - last_modified_time)  # + ';' + str(last_modified_time) + ';' + str(time_of_scan - last_modified_time)
                        body += '</td>'
                        body += '<td>'
                        body += json.dumps(json.loads(content), indent=4, separators=(',', ':')).replace(' ',
                                                                                                         '&nbsp').replace(
                            '\n', '<br>')
                        body += '</td>'
                        body += '</tr>'

                    body += '</table>'
                    body += '</center>'


                except:
                    pass

            time_stamps.append(['device_network_single_device', time.time()])
            because_of_permission = ' (device_edit)'
            if (permissions.permission_allowed(environ, 'development_read')) or permit2:
                body += '<br><br>'
                body += '<center>'

                name_to_show = '<B>Network Usage : ' + query_items[
                    'serial'] + '</B>' + because_of_permission + '(development_read)'
                url_to_use_here = url_to_use + "/chart?chart=network,serial=" + query_items['serial']
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use_here + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

                body += '<br><br>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<td>'
                body += '<B>Day</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>bytes Rx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>bytes Tx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>kbytes Rx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>kbytes Tx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>mbytes Rx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>mbytes Tx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>gbytes Rx</B>'
                body += '</td>'
                body += '<td>'
                body += '<B>gbytes Tx</B>'
                body += '</td>'
                body += '</tr>'

                days = {}
                data_dir_network = network_utilization_save_path.replace('(id)', id)
                try:
                    for file_found in os.listdir(data_dir_network):
                        with open(data_dir_network + file_found, 'r') as f:
                            for line_read in f.readlines():
                                try:
                                    d = json.loads(line_read)
                                    date = datetime.datetime.fromtimestamp(float(d['time'])).strftime('%Y.%m.%d')
                                    if not date in days:
                                        days[date] = {'rx': 0, 'tx': 0}
                                    splits_rxtx = d['usage'].replace('(', '').split(')')
                                    days[date]['rx'] += int(splits_rxtx[0])
                                    days[date]['tx'] += int(splits_rxtx[1])
                                except:
                                    pass
                except:
                    pass

                for day in sorted(days):
                    body += '<tr>'
                    body += '<td>'
                    body += day
                    body += '</td>'
                    body += '<td>'
                    body += str(days[day]['rx'])
                    body += '</td>'
                    body += '<td>'
                    body += str(days[day]['tx'])
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['rx'] / 1024))
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['tx'] / 1024))
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['rx'] / 1024 / 1024))
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['tx'] / 1024 / 1024))
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['rx'] / 1024 / 1024 / 1024))
                    body += '</td>'
                    body += '<td>'
                    body += str(int(days[day]['tx'] / 1024 / 1024 / 1024))
                    body += '</td>'
                    body += '</tr>'
                body += '</table>'
                body += '</center>'

            # Back to things all people can see

            time_stamps.append(['device_config_single_device', time.time()])
            body += '<br><br>'
            body += '<center>'
            body += '<B>Device configuration change logs (most recent at top): ' + query_items['serial'] + '</B>'
            body += '<br><br>'

            item = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
            body += 'Current time: '
            body += item[0:4] + '.' + item[4:6] + '.' + item[6:8] + ' ' + item[8:10] + ':' + item[10:12] + ':' + item[
                                                                                                                 12:14]
            body += '<br><br>'

            serial = query_items['serial']
            logs = datastore.get_logs_by_key('_' + serial)
            time_stamps.append(['device_config_single_logs_by_key', time.time()])
            TS = sorted(logs, reverse=True)

            if True:
                logs_string = ''
                logs_string += '<select name="config_change" id="config_change">'
                for item in TS:
                    key_name = ''
                    key_name += item[0:4] + '.' + item[4:6] + '.' + item[6:8] + ' ' + item[8:10] + ':' + item[
                                                                                                         10:12] + ':' + item[
                                                                                                                        12:14] + ' '
                    key_name += str(logs[item]['key']).replace('_' + serial, '').replace('device_', '') + ' '
                    try:
                        key_name += str(logs[item]['value']) + ' '
                    except:
                        key_name += '(exception in value)'
                    key_name += str(logs[item]['who'])

                    logs_string += '<option value="none"' + '">' + key_name + '</option>'
                logs_string += '</select>'

                body += logs_string
            else:
                body += '<table border="1" cellpadding="5">'

                body += '<tr>'
                body += '<td>'
                body += 'when'
                body += '</td>'
                body += '<td>'
                body += 'which'
                body += '</td>'
                body += '<td>'
                body += 'what'
                body += '</td>'
                body += '<td>'
                body += 'who'
                body += '</td>'
                body += '</tr>'

                for item in TS:
                    body += '<tr>'
                    body += '<td>'
                    try:
                        body += item[0:4] + '.' + item[4:6] + '.' + item[6:8] + ' ' + item[8:10] + ':' + item[
                                                                                                         10:12] + ':' + item[
                                                                                                                        12:14]
                    except:
                        body += item

                    body += '</td>'
                    body += '<td>'
                    try:
                        body += str(logs[item]['key']).replace('_' + serial, '').replace('device_', '')
                    except:
                        body += '(exception)'
                    body += '</td>'
                    body += '<td>'
                    try:
                        body += str(logs[item]['value'])
                    except:
                        body += '(exception)'
                    body += '</td>'
                    body += '<td>'
                    try:
                        body += str(logs[item]['who'])
                    except:
                        body += '(exception)'
                    body += '</td>'
                    body += '</tr>'

                body += '</table>'
            body += '</center>'
            time_stamps.append(['end_single_device', time.time()])

        time_stamps.append(['end_all_devices', time.time()])

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")) + '<br>extra_content:'
        csv_body = body

        time_stamps.append(['exception hit time', time.time()])

    if the_who == 'david.ferguson':
        time_stamps.append(['start report', time.time()])
        dave_report = ''
        dave_report += '<center>'
        dave_report += '<table border="1" cellpadding="5">'
        previous_time = None
        total_time = 0
        for index in range(0, len(time_stamps)):
            dave_report += '<tr>'
            dave_report += '<td>'
            dave_report += time_stamps[index][0]
            dave_report += '</td>'

            dave_report += '<td>'
            if previous_time:
                dave_report += "{:.3f}".format(time_stamps[index][1] - previous_time)
                total_time += time_stamps[index][1] - previous_time
            dave_report += '</td>'
            previous_time = time_stamps[index][1]
            dave_report += '</tr>'

        dave_report += '<tr>'
        dave_report += '<td>'
        dave_report += '<B>Total</B>'
        dave_report += '</td>'
        dave_report += '<td>'
        dave_report += "{:.3f}".format(total_time)
        dave_report += '</td>'
        dave_report += '</tr>'
        dave_report += '</table>'
        dave_report += '</center>'

        dave_report += '<br>'

        dave_report += '<center>'
        dave_report += '<table border="1" cellpadding="5">'
        for item in sorted(sub_times):
            dave_report += '<tr>'
            dave_report += '<td>'
            dave_report += item
            dave_report += '</td>'
            dave_report += '<td>'
            try:
                dave_report += "{:.3f}".format(sub_times[item])
            except:
                dave_report += str(sub_times[item])
            dave_report += '</td>'
            dave_report += '</tr>'
        dave_report += '</table>'
        dave_report += '</center>'

        dave_report += '<br>'
        dave_report += '<br>'

        try:
            dave_report += "json.dumps(site_permissions)"
            dave_report += '<br>'
            dave_report += json.dumps(site_permissions)
        except:
            pass

        if False:
            dave_report += '<br>'
            dave_report += 'id = permit2_id[id]'
            dave_report += '<br>'
            for id in permit2_id.keys():
                dave_report += str(id) + ' = ' + str(permit2_id[id])
                dave_report += '<br>'

    if 'csv_view=yes' in REQUEST_FILTER:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': False}
        return csv_body, other
    elif 'csv_download=yes' in REQUEST_FILTER:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': False}
        return csv_body.replace('<br>','\n'), other
    elif 'xlsx_download=yes' in REQUEST_FILTER:

        try:
            return make_csv_to_xlsx_and_other(csv_body)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
            return body, other
    else:
        return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']

        if other['add_wrapper']:
            html += '<html>\n'
            if 'head' in other:
                html += '<head>\n'
                html += other['head']
                html += '</head>\n'
            html += '<body>\n'
            html += body
            html += '</body>\n'
            html += '</html>\n'
        else:
            html += body
    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ====================================
def get_rings_vs_service_pack():
    # ====================================
    content_of_versions = rings.get_content_of_versions()
    service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
    service_pack_release_notes = rings.extract_service_pack_release_notes(content_of_versions)
    rings_to_service_pack = rings.lookup_rings_to_service_pack()
    try:
        lowest_service_pack = rings.extract_service_pack_target(content_of_versions, sp_type='Rollback SP')
    except:
        lowest_service_pack = ''
    rings_vs_service_pack = rings.calculate_rings_vs_service_pack(rings_to_service_pack, service_pack_list,
                                                                  lowest_service_pack)

    return rings_vs_service_pack


# ====================================
def build_collection_report(d, environ):
    # ====================================
    # d = {'serial': ['20000000cf60766f'], 'the_selection': ['collection_selection'], 'collection': ['10000000026fa89a,1000000002a07fa5,1000000003169039,1000000003351485,100000000448eb77,10000000093efeab,10000000095fa9c7,100000000bf70e2b,100000000e1b6dff,100000000e82f6ac,1000000014ce4fbf,10000000161df25b,100000001656e627,10000000175d1987,1000000018f0251d,100000001e129bf9,1000000020fa8928,10000000218604df,1000000025e30077,100000002a57be8d,100000002a5da842,100000002c328987,100000002eaf904b,10000000306639bc,1000000030d0ad72,1000000033a26063,1000000037bfba87,100000003913e7d7,100000003c44579f,100000003d5d742b,100000003e437405,100000003ebbfffd,100000004060b871,1000000040d54f48,100000004a44136d,100000004a744af3,100000004e86abb8,100000004ec58923,10000000509fb5ea,1000000050bea3de,1000000051bf2558,10000000540f418e,10000000546a215a,1000000055a56ad5,100000005af2147b,100000005c8109db,100000005cbb2c64,100000005e2ead4a,10000000606d1648,10000000608c80a8,1000000060a246e2,1000000061656b91,1000000063507a26,1000000064d2ea8f,10000000671fbaf5,10000000716b352a,100000007777dce0,100000007995bdf5,100000007bea0aa4,100000007d44d737,1000000083aa7f23,1000000083ea732f,1000000084825ff2,1000000085b5f23f,100000008619ec5a,100000008afffa2d,100000008cebd65a,1000000090d8c2f3,100000009219cb12,100000009276bc09,10000000947e3e3f,10000000950f66f3,1000000096a5f1df,1000000098a3c105,10000000990bea80,100000009a840ea6,100000009b01af02,100000009b5ff260,100000009b73be38,100000009c798dd5,100000009dbf6712,100000009e6347bb,10000000a19ac037,10000000a1d20231,10000000a24f6050,10000000a499ee0d,10000000a4de5562,10000000ab2686b7,10000000ac3b9364,10000000acb6d012,10000000ade183ef,10000000b0c505e0,10000000b101ff58,10000000b3257600,10000000b36ee03b,10000000b39fa448,10000000b5ef2cdf,10000000b7578519,10000000bad98461,10000000bbd250c5,10000000beaea70a,10000000c209b555,10000000c2e788cd,10000000c4732448,10000000c7306045,10000000c77ade6f,10000000c8697fd3,10000000ca7549de,10000000cbfe9767,10000000ccaf9e07,10000000ccb814ad,10000000cd754ca2,10000000ce31c7e7,10000000ceeb4ce4,10000000cf60766f,10000000d039d581,10000000d081fc0f,10000000d2b50123,10000000d41b6110,10000000d86aa7e9,10000000dc01d970,10000000e1932647,10000000e2f1760a,10000000e322f070,10000000e4a78b8c,10000000e7a9f827,10000000ea70b88a,10000000eba385b9,10000000f2ccf8ca,10000000f4944082,10000000f9ab2afe,10000000fc093c22,10000000fc5317fa,10000000fdc50e96,10000000fdde2ff8,10000000ff3da03e,10000000ffb9c63a,20000000cf60766f']}

    url_to_use = make_home_url_from_environ(environ)

    data_store_content = datastore.all_datastore()

    permit_to_device_command = permissions.permission_allowed(environ, 'device_command')

    rings_vs_service_pack = get_rings_vs_service_pack()

    content_of_versions = rings.get_content_of_versions()
    service_pack_list = rings.extract_service_pack_build_list(content_of_versions)
    service_pack_release_notes = rings.extract_service_pack_release_notes(content_of_versions)
    try:
        services_available = rings.get_services_from_list_of_sp(rings_vs_service_pack, service_pack_list)
    except:
        services_available = codeupload.get_pi_services()

    body = ''

    try:
        ids = d['collection'][0].split(',')
        serials = '+'.join(sorted(ids))
        serials_md5 = hashlib.md5(serials.encode()).hexdigest()
        do_atomic_write_if_different('/dev/shm/collection_serials/' + serials_md5, serials)

        body += '<br><br>'
        body += '<center>'
        body += '<B>Collection Device Service Pull List</B>'
        body += '<br><br>'

        body += '<table border="1" cellpadding="5">'

        # list out the rings
        body += '<tr>'
        body += '<td>'
        body += '<center>'
        body += '<B>Ring</B>'
        body += '</center>'
        body += '</td>'

        body += '<td>'
        body += '<form method="post" action="">'
        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="service_ring_pull_selection" selected>' + 'service_ring_pull_selection' + '</option>'
        body += '</select>'

        body += '<select multiple name="serial" id="serial" hidden>'
        for id in sorted(ids):
            body += '<option value="' + id + '" selected>' + id + '</option>'
        body += '</select>'

        body += '<select name="' + 'ring_selected' + '" id="' + 'ring_selected' + '">'
        body += '<option value="" selected>' + '' + '</option>'
        for ring in rings.get_all_rings():
            body += '<option value="' + ring + '">' + ring + '</option>'

        body += '</select>'

        body += '</td>'

        body += '<td>'
        body += '<input type="submit" value="Submit for Pull">'
        body += '</form>'
        body += '</td>'
        body += '</tr>'

        body += '<tr>'
        body += '<td>'
        body += '<center>--- OR ---</center>'
        body += '</td>'
        body += '</tr>'

        # list out the service packs
        body += '<tr>'
        body += '<td>'
        body += '<center>'
        body += '<B>Service Pack / Ring</B>'
        body += '</center>'
        body += '</td>'

        body += '<td>'
        body += '<form method="post" action="">'
        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="service_spr_pull_selection" selected>' + 'service_spr_pull_selection' + '</option>'
        body += '</select>'

        body += '<select multiple name="serial" id="serial" hidden>'
        for id in sorted(ids):
            body += '<option value="' + id + '" selected>' + id + '</option>'
        body += '</select>'

        body += '<select name="' + 'spr_selected' + '" id="' + 'spr_selected' + '">'
        body += '<option value="" selected>' + '' + '</option>'
        for sp, ring in rings_vs_service_pack:
            if ring:
                body += '<option value="' + sp + '_' + ring + '">' + sp + ' (Ring ' + ring + ')' + '</option>'
            else:
                body += '<option value="' + sp + '">' + sp + '</option>'

        body += '</select>'

        body += '</td>'

        body += '<td>'
        body += '<input type="submit" value="Submit for Pull">'
        body += '</form>'
        body += '</td>'

        body += '<td>'

        body += '<select name="' + 'release_notes' + '" id="' + 'release_notes' + '">'
        body += '<option>' + 'Release Notes:---------------' + '</option>'
        for service_pack_release in sorted(service_pack_release_notes, reverse=True):
            body += '<option>' + '</option>'
            body += '<option>' + service_pack_release + ' (' + service_pack_release_notes[service_pack_release][
                'date'] + ') </option>'
            for item_line in build_format_note(service_pack_release_notes[service_pack_release]['note'], tab=4,
                                               width=60).split('\n'):
                body += '<option>' + item_line.replace('    ', '<nbsp><nbsp><nbsp><nbsp>') + '</option>'
        body += '</select>'

        body += '</td>'

        body += '</tr>'

        body += '<tr>'
        body += '<td>'
        body += '<center>--- OR ---</center>'
        body += '</td>'
        body += '</tr>'

        body += '<tr>'
        body += '<td title="The service running on the device.">'
        body += '<center>'
        body += '<B>Service</B>'
        body += '</center>'
        body += '</td>'
        body += '<td title="These are the versions that are available to be marked as the current pull request.">'
        body += '<center>'
        body += '<B>Options</B><br>(SP limited list)'
        body += '</center>'
        body += '</td>'
        body += '<td title="Use the submit button, to assign all values at once. If the pi is checking in, then within a minute or two, the update will get pulled, and new versions will be reported a few minutes after thatn.">'
        body += '(only if runner ' + 'R.3.0' + ' or higher)'
        body += '</td>'
        body += '<td title="Click on the release notes, to see the complete version history for each service.">'
        body += 'Release Notes'
        body += '</td>'
        body += '</tr>'

        # list out the services
        # { "pi_runner":{ "R.1.0":"/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py" } }
        service_names = services_available.keys()

        number_of_services = len(service_names)
        count = -1
        body += '<form method="post" action="">'
        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="service_version_pull_selection" selected>' + 'service_version_pull_selection' + '</option>'
        body += '</select>'

        body += '<select multiple name="serial" id="serial" hidden>'
        for id in sorted(ids):
            body += '<option value="' + id + '" selected>' + id + '</option>'
        body += '</select>'

        for service_name in sorted(service_names):
            count += 1
            versions = services_available[service_name].keys()
            values_to_show = ['']
            for key_name in sort_service_versions(versions):
                values_to_show.append(key_name)
            service_tag = 'service_' + service_name

            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += service_name
            body += '</center>'
            body += '</td>'

            body += '<td>'
            body += '<select name="' + service_tag + '" id="' + service_tag + '">'
            body += '<option value="" selected>' + '' + '</option>'
            for key_name in values_to_show:
                body += '<option value="' + key_name + '">' + key_name + '</option>'
            body += '</td>'

            if count == 0:
                body += '<td rowspan="' + str(number_of_services) + '">'
                body += '<input type="submit" value="Submit for Pull">'
                body += '</td>'

            body += '<td>'
            body += '<select name="' + 'release_notes' + '" id="' + 'release_notes' + '">'
            for key_name in codeupload.get_pi_service_formatted_release_notes(service_name):
                body += '<option>' + key_name + '</option>'
                # body += '<option disabled>' + key_name + '</option>'
            body += '</select>'
            body += '</td>'

            body += '</tr>'
        body += '</form>'

        body += '<tr>'
        body += '<td>'
        body += '<center>--- OTHER ---</center>'
        body += '</td>'
        body += '</tr>'

        # list out the options
        body += '<tr>'
        body += '<td>'
        body += '<center>'
        body += '<B>Browser Reset</B>'
        body += '</center>'
        body += '</td>'

        body += '<td>'
        body += '<form method="post" action="">'
        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="request_collection_browser_reset" selected>' + 'request_collection_browser_reset' + '</option>'
        body += '</select>'

        body += '<select multiple name="serial" id="serial" hidden>'
        for id in sorted(ids):
            body += '<option value="' + id + '" selected>' + id + '</option>'
        body += '</select>'

        body += '<select name="' + 'reset_option_selected' + '" id="' + 'reset_option_selected' + '">'
        body += '<option value="" selected>' + '' + '</option>'
        body += '<option value="' + 'yes' + '">' + 'yes' + '</option>'

        body += '</select>'

        body += '</td>'

        body += '<td>'
        body += '<input type="submit" value="Submit for Browser Reset">'
        body += '</form>'
        body += '</td>'

        body += '<td>'
        body += '</td>'

        body += '</tr>'

        # ----------------------------------
        try:
            command_number = devicecommand.get_most_recent_command_number()
            command_number = int(datastore.get_value('device_command_last_number'))
        except:
            command_number = 0

        command_number_formatted = "{:06d}".format(command_number)

        if permit_to_device_command:
            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += '<B>Device Command</B>'
            body += '</center>'
            body += '</td>'

            body += '<td>'
            body += '<form method="post" action="">'
            body += '<select name="the_selection" id="the_selection" hidden>'
            body += '<option value="request_collection_device_command" selected>' + 'request_collection_device_command' + '</option>'
            body += '</select>'

            body += '<select multiple name="serial" id="serial" hidden>'
            for id in sorted(ids):
                body += '<option value="' + id + '" selected>' + id + '</option>'
            body += '</select>'

            body += '<select name="command_number" id="serial" hidden>'
            body += '<option value="' + command_number_formatted + '" selected>' + command_number_formatted + '</option>'
            body += '</select>'

            body += 'command:'
            body += '<input type="text" size=25 name="' + 'request_collection_device_command_text' + '" value="' + '' + '\">'
            body += '<br>'
            body += 'timeout:'
            body += '<input type="text" size=5 name="' + 'request_collection_device_command_timeout' + '" value="' + '15' + '\">'

            body += '</td>'

            body += '<td>'

            body += '<input type="submit" value="Submit for Device Command">'

            body += '</form>'
            body += '</td>'

            body += '<td>'
            body += 'most recent command number ' + command_number_formatted
            body += '<br><br>'
            body += 'Responses will be in the upload page; like zzz_done_command_(id)_(cmd_num)'
            body += '<br>and also on the devicecommand page; like ' + url_to_use + '/devicecommand?cmd=(cmd_num)'
            body += '</td>'

            body += '</tr>'

            # --------------------------------------
            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += '<B>Profile Log retrieve</B>'
            body += '</center>'
            body += '</td>'

            body += '<td>'
            body += '<form method="post" action="">'
            body += '<select name="the_selection" id="the_selection" hidden>'
            body += '<option value="request_collection_device_command" selected>' + 'request_collection_device_command' + '</option>'
            body += '</select>'

            body += '<select multiple name="serial" id="serial" hidden>'
            for id in sorted(ids):
                body += '<option value="' + id + '" selected>' + id + '</option>'
            body += '</select>'

            body += '<select name="command_number" id="serial" hidden>'
            body += '<option value="' + command_number_formatted + '" selected>' + command_number_formatted + '</option>'
            body += '</select>'

            body += 'command:'
            #the_command = 'cat /cardinal/log/privoxy/privoxy_report.json'
            the_command = 'curl localhost:7020?show_raw,delete_logs'
            body += '<input type="text" size=45 name="' + 'request_collection_device_command_text' + '" value="' + the_command + '\">'
            body += '<br>'
            body += 'timeout:'
            body += '<input type="text" size=5 name="' + 'request_collection_device_command_timeout' + '" value="' + '15' + '\">'

            body += '</td>'

            body += '<td>'

            body += '<input type="submit" value="Submit for Device Command">'

            body += '</form>'
            body += '</td>'

            body += '<td>'
            body += 'most recent command number ' + command_number_formatted
            body += '<br><br>'
            body += 'Responses will be in the upload page; like zzz_done_command_(id)_(cmd_num)'
            body += '<br>and also on the devicecommand page; like ' + url_to_use + '/devicecommand?cmd=(cmd_num)'
            body += '</td>'

            body += '</tr>'

        # ----------------------------------
        if permit_to_device_command:
            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += '<B>Name Collection</B>'
            body += '</center>'
            body += '</td>'

            body += '<td>'
            body += '<form method="post" action="">'
            body += '<select name="the_selection" id="the_selection" hidden>'
            body += '<option value="request_collection_name_assignment" selected>' + 'request_collection_name_assignment' + '</option>'
            body += '</select>'

            body += '<select multiple name="serial" id="serial" hidden>'
            for id in sorted(ids):
                body += '<option value="' + id + '" selected>' + id + '</option>'
            body += '</select>'

            body += 'name:'
            body += '<input type="text" size=25 name="' + 'request_collection_name_text' + '" value="' + '' + '\">'

            body += '</td>'

            body += '<td>'

            body += '<input type="submit" value="Submit for Collection Name">'

            body += '</form>'
            body += '</td>'

            body += '<td>'
            body += 'This over rides any previously assigned collection name.'
            body += '</td>'

            body += '</tr>'

        # ----------------------------------
        if permit_to_device_command:
            body += '<tr>'
            body += '<td>'
            body += '<center>'
            body += '<B>Management Block for Collection</B>'
            body += '</center>'
            body += '</td>'

            body += '<td>'
            body += '<form method="post" action="">'
            body += '<select name="the_selection" id="the_selection" hidden>'
            body += '<option value="device_management_block" selected>' + 'device_management_block' + '</option>'
            body += '</select>'

            body += '<select multiple name="serial" id="serial" hidden>'
            for id in sorted(ids):
                body += '<option value="' + id + '" selected>' + id + '</option>'
            body += '</select>'

            values_to_show = [''] + management.get_management_block_list(data_store_content)

            body += '<select name="selected_parameter" id="selected_parameter">'
            for key_name in values_to_show:
                body += '<option value="' + key_name + '">' + key_name + '</option>'
            body += '</select>'

            body += '</td>'

            body += '<td>'

            body += '<input type="submit" value="Submit for Collection Management Block ID">'

            body += '</form>'
            body += '</td>'

            body += '<td>'
            body += 'This over rides any previously assigned Management ID.'
            body += '</td>'

            body += '</tr>'

        body += '</table>'
        body += '</center>'

        # datamine options
        body += '<br><br>'
        body += '<center>'
        body += '<B>Collection Datamine</B>'
        body += '<br><br>'

        body += '<table border="1" cellpadding="5">'

        body += '<tr>'
        body += '<td>'
        body += '<center>'
        body += '<B>Datamine Plot Summary</B>'
        body += '</center>'
        body += '</td>'
        body += '</tr>'

        # https://slicer.cardinalhealth.net/datamine?serial=100000000bf70e2b+100000000bf70e2b,runner_raw=s_logging_userinactive,view=summary,plot_algorithm=value_less_than_360,minimum=,maximum=,scale=,to_plot_as_number=
        url_to_use = make_home_url_from_environ(environ)
        for item, url_to_link in [('user_active',
                                   url_to_use + '/datamine?show_title=User_active_average,show_minmax=0,runner_raw=s_logging_userinactive,view=summary,plot_algorithm=value_less_than_360,minimum=0,maximum=,scale=,to_plot_as_number=,serial=md5_' + serials_md5),
                                  ('runner_active',
                                   url_to_use + '/datamine?show_title=Runner_Active,show_minmax=0,runner_raw=uptime,view=summary,plot_algorithm=count_occurances_non_zero,minimum=0,maximum=,scale=,to_plot_as_number=,serial=md5_' + serials_md5)]:

            body += '<tr>'
            body += '<td>'
            body += '<center>'
            if url_to_link:
                body += '<a href="' + url_to_link + '">' + item + """</a>"""
            else:
                body += item
            body += '</center>'
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'

        # show the ids, to confim the list
        body += '<br><br>'
        body += '<center>'
        body += '<B>The Collection IDs</B>'
        body += '<br><br>total number of IDs = ' + str(len(ids)) + '<br><br>'
        body += '<table border="1" cellpadding="5">'
        for id in ids:
            body += '<tr>'
            body += '<td>'
            body += id
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'


    except:
        body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return str(body)


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_make_new_filter_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(make_new_filter(), '')
        self.assertEqual(make_new_filter('test1=1'), 'test1=1')
        self.assertEqual(make_new_filter('test1=1', 'test2=2'), 'test1=1,test2=2')
        self.assertEqual(make_new_filter('test1=1,test2=2', 'test2=3'), 'test1=1,test2=3')
        self.assertEqual(make_new_filter('test1=1,test2=2', 'test1=4'), 'test1=4,test2=2')

        # for toggling the sortd between fwd and rev
        self.assertEqual(make_new_filter('sort=1', '', 'sortd=fwd|rev'), 'sort=1,sortd=fwd')
        self.assertEqual(make_new_filter('sort=1', '', 'sortd=rev|fwd'), 'sort=1,sortd=rev')

        # first toggle (rotation)
        self.assertEqual(make_new_filter('sort=1,sortd=fwd', '', 'sortd=fwd|rev'), 'sort=1,sortd=rev')
        self.assertEqual(make_new_filter('sort=1,sortd=rev', '', 'sortd=fwd|rev'), 'sort=1,sortd=fwd')
        self.assertEqual(make_new_filter('sort=1', 'sort=1', 'sortd=rev|fwd'), 'sort=1,sortd=rev')
        self.assertEqual(make_new_filter('sort=1,sortd=rev', 'sort=1', 'sortd=fwd|rev'), 'sort=1,sortd=fwd')

        # if the sorted item changes, then grab its default, do not propagate the sortd of the previous item
        self.assertEqual(make_new_filter('sort=1,sortd=fwd', 'sort=2', 'sortd=fwd|rev', rotate_key='sort'),
                         'sort=2,sortd=fwd')

        # punture
        self.assertEqual(make_new_filter('sort=1,sortd=rev', 'view=device_network', items_to_puncture='sort,sortd'),
                         'view=device_network')
        self.assertEqual(
            make_new_filter('view=test123,sort=1,sortd=rev', 'view=device_network', items_to_puncture='sort,sortd'),
            'view=device_network')
        self.assertEqual(
            make_new_filter('subview=test123,sort=1,sortd=rev', 'view=device_network', items_to_puncture='sort,sortd'),
            'subview=test123,view=device_network')

    def test_time_formatting(self):
        self.assertEqual(convert_seconds_to_human(0), '0s')
        self.assertEqual(convert_seconds_to_human(1), '1s')
        self.assertEqual(convert_seconds_to_human(2), '2s')
        self.assertEqual(convert_seconds_to_human(60), '1m')
        self.assertEqual(convert_seconds_to_human(61), '1m 1s')
        self.assertEqual(convert_seconds_to_human(3600), '1h')
        self.assertEqual(convert_seconds_to_human(3600 * 24), '1d')
        self.assertEqual(convert_seconds_to_human(3600 * 24 + 1), '1d 1s')
        self.assertEqual(convert_seconds_to_human(3600 * 24 + 3600), '1d 1h')

        self.assertEqual(convert_seconds_to_human(3601), '1h 1s')
        self.assertEqual(convert_seconds_to_human(3661), '1h 1m 1s')
        self.assertEqual(convert_seconds_to_human(36061), '10h 1m 1s')

        self.assertEqual(convert_seconds_to_human(2495942.09321), '28d 21h 19m 2s')

        self.assertEqual(convert_seconds_to_human(-1), '- 1s')
        self.assertEqual(convert_seconds_to_human(-2495942.09321), '- 28d 21h 19m 2s')

    def test_time_formatting_human_to_seconds(self):
        self.assertEqual(human_to_seconds('0s'), 0)
        self.assertEqual(human_to_seconds('1s'), 1)
        self.assertEqual(human_to_seconds('3s'), 3)
        self.assertEqual(human_to_seconds('- 3s'), -3)
        self.assertEqual(human_to_seconds('1m 3s'), 63)
        self.assertEqual(human_to_seconds('2h 1m 3s'), 2 * 60 * 60 + 60 + 3)
        self.assertEqual(human_to_seconds('3d 2h 1m 3s'), 3 * 24 * 60 * 60 + 2 * 60 * 60 + 60 + 3)
        self.assertEqual(human_to_seconds('- 3d 2h 1m 3s'), -1 * (3 * 24 * 60 * 60 + 2 * 60 * 60 + 60 + 3))

    def test_settings_access(self):
        for (the_setting, the_value) in s_all_basic_settings:
            print(the_setting, the_value)
            pass

    def test_format_note(self):
        input_content = '123456789'
        expected = '    123456789'
        actual = build_format_note(input_content, tab=4, width=25)
        self.assertEqual(actual, expected)

    def test_format_note2(self):
        input_content = '123456789 A'
        expected = '    123456789 A'
        actual = build_format_note(input_content, tab=4, width=15)
        self.assertEqual(actual, expected)

    def test_format_note3(self):
        input_content = '123456789 A'
        expected = '    123456789\n    A'
        actual = build_format_note(input_content, tab=4, width=14)
        self.assertEqual(actual, expected)

    def test_format_note4(self):
        input_content = '123456789 A\nB'
        expected = '    123456789\n    A\n    B'
        actual = build_format_note(input_content, tab=4, width=14)
        self.assertEqual(actual, expected)

    def test_format_note5(self):
        input_content = '123456789 ABC DEF GHI'
        expected = '    123456789 ABC DEF GHI'
        actual = build_format_note(input_content, tab=4, width=25)
        self.assertEqual(actual, expected)

    def test_count_profile_usage(self):
        ids = ['abc', 'cde']
        data_store_content = {'device_profile_abc': 'profile_123', 'device_profile_cde': 'profile_123'}
        expected = {'profile_123': 2}

        # data_store_content = datastore.all_datastore()
        actual = get_profile_counts(ids, data_store_content)
        self.assertEqual(actual, expected)

    def test_data_dictionary(self):
        data_dictionary = {}
        data_dictionary['hopper'] = {
            'source': 'hopper',
            'header': 'hopper',
            'title': 'hopper',
            'needs_trust': True,
            'to_plot_as_number': '',
            'display': '',
            'help': 'The configuration of the wifi hopper setting.',
            'filter': 'hopper=(hopper)'
        }

        actual = get_filters_from_data_dictionary(data_dictionary)
        expected = {'hopper': 'hopper'}
        self.assertEqual(actual, expected)

    def test_mac_address_formatting(self):
        input_mac = "aa:bb:cc:11:22:33"
        expected = "AA-BB-CC-11-22-33"
        actual = format_mac_address(input_mac)
        self.assertEqual(actual, expected)

    def test_comms_report(self):
        expected = ''
        actual = comms_report({'monitor_time': 0, 'runner_time': 0})
        self.assertEqual(actual, expected)

        expected = ''
        actual = comms_report({'monitor_time': '0', 'runner_time': '0'})
        self.assertEqual(actual, expected)

        expected = '1'
        actual = comms_report({'monitor_time': 0, 'runner_time': 10000})
        self.assertEqual(actual, expected)

        expected = '1'
        actual = comms_report({'monitor_time': 0, 'runner_time': '10000'})
        self.assertEqual(actual, expected)

        expected = ''
        actual = comms_report({'monitor_time': '(missing)', 'runner_time': 0})
        self.assertEqual(actual, expected)

        expected = ''
        actual = comms_report({'monitor_time': '0', 'runner_time': '(missing)'})
        self.assertEqual(actual, expected)

    def test_mac_is_raspberrypi(self):
        mac_to_test = 'DC-A6-32-07-E2-D1'
        expected = True
        actual = mac_is_raspberrypi(mac_to_test)
        self.assertEqual(actual, expected)

        mac_to_test = '9C-EF-D5-FA-B4-0A'
        expected = False
        actual = mac_is_raspberrypi(mac_to_test)
        self.assertEqual(actual, expected)

        # other current know raspberry pi prefixes
        # https://udger.com/resources/mac-address-vendor-detail?name=raspberry_pi_foundation
        mac_to_test = '28-CD-C1-07-E2-D1'
        expected = True
        actual = mac_is_raspberrypi(mac_to_test)
        self.assertEqual(actual, expected)

        mac_to_test = 'B8-27-EB-07-E2-D1'
        expected = True
        actual = mac_is_raspberrypi(mac_to_test)
        self.assertEqual(actual, expected)

        mac_to_test = 'E4-5F-01-07-E2-D1'
        expected = True
        actual = mac_is_raspberrypi(mac_to_test)
        self.assertEqual(actual, expected)

    def test_get_ssid_from_dash_string(self):
        raw_value = 'corp-68-72'
        expected = 'corp'
        actual = get_ssid_from_dash_string(raw_value)
        self.assertEqual(expected, actual)

        raw_value = 'cah-iot-68-72'
        expected = 'cahiot'
        actual = get_ssid_from_dash_string(raw_value)
        self.assertEqual(expected, actual)

        raw_value = 'level2-ng-68-72'
        expected = 'level2ng'
        actual = get_ssid_from_dash_string(raw_value)
        self.assertEqual(expected, actual)

        raw_value = '(not_on_wifi)'
        expected = ''
        actual = get_ssid_from_dash_string(raw_value)
        self.assertEqual(expected, actual)

    def test_get_key_definitions(self):
        key_definitions = get_key_definitions()

        expected = 'EV_SYN'
        actual = key_definitions['event_types']['0']
        self.assertEqual(expected, actual)

    def test_stuckkeys_report_to_human(self):
        key_definitions = get_key_definitions()

        stuckkeys_report = ''
        expected = ''
        actual = stuckkeys_report_to_human(stuckkeys_report, key_definitions)
        self.assertEqual(expected, actual)

        stuckkeys_report = 'event2_1_58_2_933'
        expected = 'event2:KEY_CAPSLOCK:held'
        actual = stuckkeys_report_to_human(stuckkeys_report, key_definitions)
        self.assertEqual(expected, actual)

        stuckkeys_report = 'event1_0_0_0_512-event2_0_0_1_937-event2_1_58_2_933'
        expected = 'event2:KEY_CAPSLOCK:held'
        actual = stuckkeys_report_to_human(stuckkeys_report, key_definitions)
        self.assertEqual(expected, actual)

        stuckkeys_report = 'event2_1_58_2_933-event2_1_11_0_162'
        expected = 'event2:KEY_CAPSLOCK:held, event2:KEY_0:inactive'
        actual = stuckkeys_report_to_human(stuckkeys_report, key_definitions)
        self.assertEqual(expected, actual)

        stuckkeys_report = 'event2_1_58_2_933-event2_1_11_0_162'
        expected = 'event2:KEY_CAPSLOCK:held'
        actual = stuckkeys_report_to_human(stuckkeys_report, key_definitions, threshold=200)
        self.assertEqual(expected, actual)

    def test_split_content_by_chunk_size(self):
        content = 'abcdez'
        chunk_size = 22
        expected = 'abcdez'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = 'zbcdez g'
        chunk_size = 22
        expected = 'zbcdez g'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = 'zbcdez g'
        chunk_size = 5
        expected = 'zbcdez\ng'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = 'zbcdez g h'
        chunk_size = 5
        expected = 'zbcdez\ng h'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345'
        chunk_size = 10
        expected = '12345\n12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345 12345 12345'
        chunk_size = 17
        expected = '12345 12345 12345\n12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345 12345 12345'
        chunk_size = 10
        expected = '12345\n12345\n12345\n12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345 12345 12345'
        chunk_size = 11
        expected = '12345 12345\n12345 12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345 12345 12345'
        chunk_size = 16
        expected = '12345 12345\n12345 12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        content = '12345 12345 12345 12345'
        chunk_size = 17
        expected = '12345 12345 12345\n12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

        # with newline in the input
        content = '12345\n12345 12345 12345'
        chunk_size = 17
        expected = '12345\n12345 12345 12345'
        actual = split_content_by_chunk_size(content, chunk_size)
        self.assertEqual(expected, actual)

    def test_make_sort_from_version(self):
        raw_value = '92.0.4515.98'
        expected = '0000000092.0000000000.0000004515.0000000098'
        actual = make_sort_from_version(raw_value)
        self.assertEqual(expected, actual)

        test_list = []
        raw_value = '192.0.4515.98'
        test_list.append(make_sort_from_version(raw_value))
        raw_value = '92.0.4515.98'
        test_list.append(make_sort_from_version(raw_value))
        expected = ['0000000092.0000000000.0000004515.0000000098','0000000192.0000000000.0000004515.0000000098']
        actual = sorted(test_list)
        self.assertEqual(expected, actual)

        test_list = []
        raw_value = '92.1.4515.98'
        test_list.append(make_sort_from_version(raw_value))
        raw_value = '92.0.4515.98'
        test_list.append(make_sort_from_version(raw_value))
        expected = ['0000000092.0000000000.0000004515.0000000098','0000000092.0000000001.0000004515.0000000098']
        actual = sorted(test_list)
        self.assertEqual(expected, actual)

    def test_make_sort_from_screen_resolution(self):
        resolution = '1x2'
        expected = '0000000001x0000000002'
        actual = make_sort_from_screen_resolution(resolution)
        self.assertEqual(expected, actual)


    def test_make_user_can_manage(self):
        # -----------------------------------
        permit_to_device_edit = False
        permit_to_locations_all = False
        device_management = ''
        user_site = False
        user_management = False
        expected = False

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = True
        device_management = ''
        user_site = False
        user_management = False
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = False
        device_management = ''
        user_site = False
        user_management = False
        expected = False

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = True
        device_management = ''
        user_site = False
        user_management = False
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = False
        device_management = 'VPN1'
        user_site = False
        user_management = True
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = False
        device_management = ''
        user_site = True
        user_management = False
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = False
        device_management = '(unassigned)'
        user_site = True
        user_management = False
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = True
        device_management = '(retired)'
        user_site = True
        user_management = False
        expected = True

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

        # -----------------------------------
        permit_to_device_edit = True
        permit_to_locations_all = False
        device_management = 'VPN1'
        user_site = True
        user_management = False
        expected = False

        actual = make_user_can_manage(permit_to_device_edit, permit_to_locations_all, device_management, user_site, user_management)
        self.assertEqual(expected, actual)

    def test_make_json_dumps_aligned(self):
        dumps = """{
    "1234":1,
    "2":123
}"""
        expected = """{
    "1234" : 1,
    "2"    : 123
}"""
        actual = make_json_dumps_aligned(dumps)
        self.assertEqual(expected, actual)





    def test_make_csv_to_xlsx(self):
        csv_body = """a,b,c
1,2,3
4,5,6"""
#        actual = make_csv_to_xlsx(csv_body)

#        body, other = make_csv_to_xlsx_and_other(csv_body)


    def test_sort_service_versions(self):
        versions = ['R.1.1', 'R.1.2']
        expected = ['R.1.2', 'R.1.1']
        actual = sort_service_versions(versions)
        self.assertEqual(expected, actual)

        versions = ['R.1.1', 'R.1.10']
        expected = ['R.1.10', 'R.1.1']
        actual = sort_service_versions(versions)
        self.assertEqual(expected, actual)

        versions = ['R.1.2', 'R.1.10']
        expected = ['R.1.10', 'R.1.2']
        actual = sort_service_versions(versions)
        self.assertEqual(expected, actual)
















# end of source file
