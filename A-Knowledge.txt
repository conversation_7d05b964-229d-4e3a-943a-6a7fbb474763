File: A-Knowledge

A place to hold the key pieces of information about the project.

The readme.md is meant to be viewed in Github (.md is markdown formatting)

https://github.com/CardinalHealth/cs_sp_pi_slicer
login with the CAH github specific user name, like "cah-david-ferguson"

gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=FALSE
gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=TRUE


APMs:
Raspberry Pi is APM0027587
Slicer is APM0028269

Filename prefixes:

AA-*
    These are documentation for developers.
    They are named AA so that they alphabetically are at the top of the file list.

config_*
    Files needed to configure the project.

datastore_*
    Captures of the Slicer datastore content.

offline_*
    Tools used not in pi or Slicer (meant for developer use)

pi_*
    Source code files that get loaded onto the pi. (See AA-pi-applications.txt for details)

read_*
    Text files for people to read:
        how_to_*
            These are instructional, and get posted to Slicer uploads, for
                user downloads.

        release_notes_*
            These are for publishing release notes for people to understand what changed.

            read_release_notes_slicer_pi.txt
                The pi services release notes and definitions of service packs (SP)


slicer_wsgi_*
    Source code files loaded onto the main Slicer server.




# end of file