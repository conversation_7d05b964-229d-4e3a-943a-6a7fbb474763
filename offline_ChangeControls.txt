Slicer (Business Application) APM0028269

https://cardinal.service-now.com/now/nav/ui/classic/params/target/change_request.do%3Fsys_id%3Dc55090474709299834f3ba67436d436e%26sysparm_view%3D%26sysparm_domain%3Dnull%26sysparm_domain_scope%3Dnull

===================
2022.03.14
===================
CHG0248873
Slicer Certificate Update

===================
2023.02.07
===================
CHG0283577
Slicer Certificate Update



===================
2023.02.20
===================
----------
CHG0284889
----------
Cancel this false start, that had the wrong CI selected

----------
CHG0284928
----------
Update devices to SP.39
CI: Slicer (Business Application)
CTASK0122627
(Was created as Special Platforms. <PERSON> moved it to Client Engineering, then
assigned david.ferguson as "Assigned TO"

Change Acknowledgement = "Acknowledges - No Concerns", then save

===================
2023.03.14
===================
CHG0287466
Move Raspberry Pi from corp to IOT (101 devices)

===================
2024.01.12
===================
----------
CHG0321708
----------
slicer.cardinalhealth.net cert update
Go to SNOW, find the CTASK in the lower tabs "Related Links"
CTASK0162348

Look to file "AA-gcp-servers.txt",
    and search for this change control number, to find the Step-by-step

All went ok... close the change, and the task
2024.01.27 Started 8am, Complete 8:35am

Go to the CTASK, enter notes in the "Work notes" area, click "Update".
Set status to "Closed Complete", and click "Save"

Go to the Change, go to tab "Post-Implementation",
enter "Actual Start Time" and "Actual End Time",
set Status to "Implemented", click "Save",
Then under "Post-Implementation", click the "Close" button.
The change control top visual bar should move from "Implementation" to "Closed".

===================
2024.02.13
===================
----------
CHG0325560
----------
Pi services package update to SP.47 on Feb 17, 2024

Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Non-emergency Patch/Upgrade
Short description: Pi services update to SP.47 on Feb 17, 2024
Description: (same as short description)
Configuration item: Raspberry Pi
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)
* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)
Implementation plan: Set devices to pull SP.47 content
Rollback plan: Set devices to pull SP.40 content
Post - Implementation: check device reporting status
Pre-implementation testing: Yes (load to  development devices, and check that loads, runs, restarts)
Peer Reviewer Name: (go over it with someone)
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes
Save
(after peer review, then Request Approval)

2024.02.17
Go to the CTASK, enter notes in the "Work notes" area, click "Update".
Set status to "Closed Complete", and click "Save"

Go to the Change, go to tab "Post-Implementation",
enter "Actual Start Time" and "Actual End Time",
set Status to "Implemented", click "Save",
Then under "Post-Implementation", click the "Close" button.
The change control top visual bar should move from "Implementation" to "Closed" (now in green).


===================
2024.02.15
===================
Point to new server

DNS change 1:
point to new: ***********

DNS change2:
revert to old: ***********

Start here:
https://cardinal.service-now.com/gith?id=sp_index
"Request something"
Search "DNS", find "DNS Request, catalog item"
What: Modify
Type: A Record
As soon as possible: No
When: 2024.02.24 09:00:00 (EST)
Host: slicer
Domanin: Cardinalhealth.net
old: ***********
new: ***********
in/ext: Internal
Add to cart

(Another, just with the reverse IP, and time of 21:00:00)
REQ3757759 - 2 items
RITM5800063
RITM5800064

Change Control
----------
CHG0326318
----------
Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Non-emergency Patch/Upgrade
Short description: Move Slicer Server on Feb 24, 2024
Description: (same as short description)
Configuration item: Slicer
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)
* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)

Implementation plan: Update DNS to point to new server in GCP
Rollback plan: Update DNS to point to existing server in GCP
Post - Implementation: check device reporting status
Pre-implementation testing: Yes (load to  development devices, and check that loads, runs, restarts)
Peer Reviewer Name: (go over it with someone)
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes
Save
(after peer review, then Request Approval)

Thursday Feb 22:
	In the LATAM meeting chat: “Reminder: This Saturday, February 24, we will be migrating the Slicer server to a new setup. The manual data migration will happen between 8am and 9am EST (13:00 to 14:00UTC), and the automated DNS change to point slicer.cardinalhealth.net to the IP address of the new instance will occur starting at 9am EST. At that moment, when the raspberry pi devices check in, they will be hitting the new server, with all the same data. There should be no change at the devices, since they will just see the same settings being served up.“
	“For your reference, the change control for this move is CHG0326318”

IF Needed:
SRT:
https://cardinal.service-now.com/kb_view.do?sysparm_article=KB0046597

Click here (link above) for instructions on using SARA (SRT Automated Response Assistant).
If there are issues utilizing SARA please call (614) 757-2500, select option 1.
When an SRT is being requested be prepared to provide the following information:

Name of person calling in the SRT.
Which application requires attention?
What is the issue? Specifically what action can the customer or client not complete.

Note - For P3 Self-service, what functionality may be impacted if the issue is not addressed proactively.
What is the impact? Business, Customer.

Note - For P3 Self-service, if this is business or customer impact a SRT Manager will join the line to assess if the incident should be escalated to a P1 or P2.
When did the issue begin?
What business segment(s) are impacted?
What location(s) are impacted?
What team(s) do you need on the SRT call
The SRT Admin will assign the SRT line and open the bridge, then focus on capturing the information needed to enter the Incident in ServiceNow.

2024.02.25
Go to the CTASK, enter notes in the "Work notes" area, click "Update".
Set status to "Closed Complete", and click "Save"

Go to the Change, go to tab "Post-Implementation",
enter "Actual Start Time" and "Actual End Time",
set Status to "Implemented", click "Save",
Then under "Post-Implementation", click the "Close" button.
The change control top visual bar should move from "Implementation" to "Closed" (now in green).

Can not ssh to pi from 04 server:
on pi:
sudo apt-get install dnsutils -y
nslookup slicer.cardinalhealth.net
nslookup ***********

https://www.cyberciti.biz/faq/tcp-wrappers-hosts-allow-deny-tutorial/
sudo apt-get install tcpd
tcpdmatch sshd slicer.cardinalhealth.net
tcpdchk -v

2024.02.27
Make PTR record modification, for
***********
to point to
slicer.cardinalhealth.net

Start here:
https://cardinal.service-now.com/gith?id=sp_index
"Request something"
Search "DNS", find "DNS Request, catalog item"
What: Modify
Type: PTR Record
As soon as possible: Yes
Host: lpec5009slicr04
Domain: Cardinalhealth.net
IP address: ***********
new FQDN name: slicer.cardinalhealth.net
in/ext: Internal
Submit Now

REQ REQ3772287

Jeremy Smith helped get it changed,
after we checked with Colin White from the GCP team that they did not need the old PTR setting.

The next day it reverted... checking if there was a reason why.

From Jeremy 2024.02.29:
Sorry, I forgot about this portion, which is why we typically don’t go this route.
This is the reason why the PTR record reverted.
The highlighted area. When a server is joined to the domain, it is always granted authority over the linked PTR record.
So in this case the A record saw that the PTR record didn’t match and reverted the changes.
This is a standard Microsoft DNS configuration that we cannot change.
(Screen grab of a box titles "lpec5009slicr04 Properties",
under a tab called "Host (A)",
that has a check box that reads "Update associated pointer (PTR) record")
I have un-checked the box and re-created the PTR record so that it points back to slicer.cardinalhealth.net

2024.03.02
The next day, the PTR is still good, and also the day after. Rebooting the server, and it is still good; no revert yet.

-------------------------------
dig slicer.cardinalhealth.net

; <<>> DiG 9.10.6 <<>> slicer.cardinalhealth.net
;; global options: +cmd
;; Got answer:
;; ->>HEADER<<- opcode: QUERY, status: NOERROR, id: 56555
;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1

;; OPT PSEUDOSECTION:
; EDNS: version: 0, flags:; udp: 4096
;; QUESTION SECTION:
;slicer.cardinalhealth.net.	IN	A

;; ANSWER SECTION:
slicer.cardinalhealth.net. 51	IN	A	***********

;; Query time: 54 msec
;; SERVER: ************#53(************)
;; WHEN: Thu Feb 29 15:18:02 EST 2024
;; MSG SIZE  rcvd: 70

-------------------------------
dig ***********

; <<>> DiG 9.10.6 <<>> ***********
;; global options: +cmd
;; Got answer:
;; ->>HEADER<<- opcode: QUERY, status: NXDOMAIN, id: 64133
;; flags: qr rd ra; QUERY: 1, ANSWER: 0, AUTHORITY: 1, ADDITIONAL: 1

;; OPT PSEUDOSECTION:
; EDNS: version: 0, flags:; udp: 512
;; QUESTION SECTION:
;***********.			IN	A

;; AUTHORITY SECTION:
.			86400	IN	SOA	a.root-servers.net. nstld.verisign-grs.com. ********** 1800 900 604800 86400

;; Query time: 64 msec
;; SERVER: ************#53(************)
;; WHEN: Thu Feb 29 15:25:41 EST 2024
;; MSG SIZE  rcvd: 115

nslookup ***********


Server:		************
Address:	************#53

Non-authoritative answer:
***********.in-addr.arpa	name = lpec5009slicr04.cardinalhealth.net.

Authoritative answers can be found from:

-------------------------------

===================
2024.08.19
===================
Update Slicer server code to v2.1.1
Permissions for users: fix showing too much on the index page

Change Control
----------
CHG0350940
----------
Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Non-emergency Patch/Upgrade
Short description: Update Slicer to v2.1.1 on August 28, 2024
Description: (same as short description)
Configuration item: Slicer
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)
* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)

Implementation plan: Install from the v2.1.1 zip
Rollback plan: Install from the v2.1.0 zip
Post - Implementation: check user view for single user with 'explicit' set on multimedia
Pre-implementation testing: Yes (load to  development devices, and check that loads, runs, restarts)
Peer Reviewer Name: (go over it with someone)
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes
Save
Do peer review
Then go back in and "Request Approval"
Then do "Save"



===================
2024.09.25
===================
Restrict Slicer to TLS1.2 and TLS1.3 only:

Change Control
----------
CHG0355824
----------
Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Non-emergency Patch/Upgrade
Short description: Restrict Slicer to TLS1.3 only
Description: (same as short description)
Configuration item: Slicer
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)
* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)
Implementation plan: edit "/etc/httpd/conf.d/ssl.conf" to include "SSLProtocol +TLSv1.3", then "service httpd restart"
Rollback plan: edit "/etc/httpd/conf.d/ssl.conf" to remove "SSLProtocol +TLSv1.3", then "service httpd restart"
Post - Implementation: check with Cabello, Arnold <<EMAIL>> to see that the requirements are met, in the Tanium report
Pre-implementation testing: Yes, (changed lpec5009slicr05, and confirmed in Tanium report.)
Peer Reviewer Name: Jack Vincent Balcita
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes
Save
Do peer review
Then go back in and "Request Approval"
Then do "Save"


===================
2024.10.02
===================
Close Archer for lpec5009slicr01

https://cardinalhealth.archerirm.us/default.aspx?requestUrl=..%2fGenericContent%2fRecord.aspx%3fid%3d2141810%26moduleId%3d167

ISSUE-13843

REQ4050364

2024.10.07
Got email from
<EMAIL>

Saying that the Archer is closed.

===================
2024.11.19
===================
CHG0362905
Slicer Certificate Update

Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Website
Short desc: SSL Cert update for slicer.cardinalhealth.net
Desc: we will be rotating the SSL cert for slicer.cardinalhealth.net
Configuration item: Slicer
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)

* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)
Implementation plan: Upload new certificate
Rollback plan: rollback to the existing certificate
Post - Implementation: check the TLS is using the new certificate
Pre-implementation testing: Yes (load to  development devices, and check that loads, runs, restarts)
Peer Reviewer Name: (go over it with someone)
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes
Save
(after peer review, then Request Approval)











# End of file
