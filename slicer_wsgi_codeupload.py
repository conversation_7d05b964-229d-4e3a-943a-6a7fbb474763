# A codeupload for slicer page services

service = 'codeupload'
version = service + '.0.4'

release_notes = """
2023.04.10
codeupload.0.4

Ability to load versioned service files


"""

_ = """
This file gets loaded to:
/var/www/html/codeupload.py

using:
sudo vi /var/www/html/codeupload.py

It also requires:

sudo vi /etc/httpd/conf.d/python-codeupload.conf
----- start copy -----
WSGIScriptAlias /codeupload /var/www/html/codeupload.py
----- end copy -----

sudo chown apache:apache /var/www/html/codeupload.py

sudo systemctl restart httpd
OR
sudo systemctl restart apache2

test on Slicer server with:
cd /var/www/html
sudo python -c "import codeupload; print(codeupload.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/codeupload



"""

import cgi
import copy
import traceback
import json
import os
import sys
import textwrap
import time

import shlex
import subprocess
import unittest

from tempfile import TemporaryFile

startup_exceptions = ''
service_config = {}

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_codeupload_path = service_config['base_codeupload_path']
    base_upload_path = service_config['base_upload_path']
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
# Taken from reports... it is tested there
def sort_service_versions(versions):
    # ----------------------------
    return_value = []

    expanded_versions = {}
    for item in versions:
        sub_items = []
        for sub_item in item.split('.'):
            sub_items.append(sub_item.zfill(10))
        key = '.'.join(sub_items)
        expanded_versions[key] = item

    for key in sorted(expanded_versions.keys(), reverse=True):
        return_value.append(expanded_versions[key])

    return return_value



# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, service + '_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def get_pi_service_formatted_release_notes(service, width=30):
    # ====================================
    release_notes_to_show = []

    header_line = "Release Notes:"

    try:
        #    if width > len(header_line):
        #        header_line = header_line + "-" * (width - len(header_line))
        header_line = header_line + "-" * (3 + width - len(header_line))

        release_notes_to_show.append(header_line)
        release_notes_for_service = get_pi_service_release_notes(service)
        for line_of_content in release_notes_for_service.split('\n'):
            if line_of_content:
                for chunk in textwrap.wrap(line_of_content, width):
                    release_notes_to_show.append(chunk)
            else:
                # blank line
                release_notes_to_show.append('')
    except:
        release_notes_to_show.append(
            service + '=>' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))

    return release_notes_to_show


# ====================================
def get_pi_service_release_notes(service):
    # ====================================
    release_notes = ''
    # look to the most recent release to pull content

    services = get_pi_services()
    if service in services:
        most_recent_version = sort_service_versions(services[service])[0]

        content = get_pi_service_version(service, most_recent_version)

        in_release_notes = False
        for line_content in content.split('\n'):
            if 'release_notes' in line_content:
                in_release_notes = True
            else:
                if in_release_notes:
                    if '"""' in line_content:
                        break
                    else:
                        release_notes += line_content + '\n'

    return release_notes


# ====================================
def get_pi_services():
    # ====================================
    services_available = {}
    files_found = os.listdir(base_codeupload_path)

    for file_found in files_found:
        if ('pi_service?' in file_found) and (not '.md5' in file_found):
            service = 'pi_' + file_found.split('?')[1]
            vers = file_found.split('?')[3]

            if not service in services_available:
                services_available[service] = {}
            services_available[service][vers] = file_found

    return services_available


# ====================================
def get_pi_service_version_filename(service, vers):
    # ====================================
    # get file name
    return base_codeupload_path + 'pi_service?' + service.replace('pi_', '') + '?pi_version?' + vers  # the replace pi_  is to take legacy requests, and fit the new model.


# ====================================
def get_pi_service_version(service, vers):
    # ====================================
    # get file content
    content = ''
    code_file = get_pi_service_version_filename(service, vers)
    if os.path.isfile(code_file):
        with open(code_file, 'rb') as f:
            content = f.read().decode()
    return content


# ====================================
def make_body_POST(environ):
    # ====================================
    # https://stackoverflow.com/questions/14355409/getting-the-codeupload-file-content-to-wsgi
    # https://stackoverflow.com/questions/14544696/python-simple-wsgi-file-codeupload-script-what-is-wrong/14590585

    body = ''

    body += 'method = POST<br>'

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)
    # body += str(field_storage) # FieldStorage(None, None, [FieldStorage('file', 'ChromeZoom.xlsx', 'PK\x03\x...
    # FieldStorage(None, None, [MiniFieldStorage('file_download_allowed', 'testfile1.txt'), MiniFieldStorage('file_download_allowed_allowed', 'Yes')])

    try:
        file_name = field_storage.getvalue('file_download_allowed')
        allow_to_allow = field_storage.getvalue('file_download_allowed_allowed')
        datastore.set_value('download_permission_' + file_name, allow_to_allow)
    except:
        pass

    try:
        file_name = field_storage.getvalue('file_delete_allowed')
        allow_to_allow = field_storage.getvalue('file_delete_allowed_key')
        if 'delete_it_now' == allow_to_allow:
            try:
                os.remove(base_codeupload_path + file_name)
            except:
                pass
            try:
                os.remove(base_codeupload_path + file_name + '.md5')
            except:
                pass
    except:
        pass

    if len(field_storage.list):
        open('/dev/shm/running_exceptions_' + service, 'w').write('storage')

        upload_service_content = {}
        # --------------
        # get content
        # --------------
        for item in field_storage.list:
            filename = ''
            if item.filename:
                filename = item.filename

            open('/dev/shm/running_exceptions_' + service, 'w').write('item: ' + filename)

            use_it = False
            if ('pi_' in filename) and ('.py' == filename[-3:]):
                use_it = True
            if ('read_' in filename) and ('.txt' == filename[-4:]):
                use_it = True
            if ('pi_service_' in filename) and ('pi_version_' in filename):
                use_it = True

            if use_it:
                file_content = item.file.read()
                upload_service_content[filename] = file_content

        # --------------
        # restrict content in a batch upload
        # --------------
        if len(upload_service_content.keys()) > 1:
            files_to_remove = []
            for filename in upload_service_content.keys():
                pass
            #                    if 'index' in filename:
            #                        files_to_remove.append(filename)
            for filename in files_to_remove:
                del upload_service_content[filename]

        # --------------
        # look for multiple settings files
        # --------------
        files_for_network = []
        for filename in upload_service_content.keys():
            if ('pi_service_' in filename) and ('pi_version_' in filename):
                pass
                open('/dev/shm/running_exceptions_' + service, 'w').write(filename)

            else:
                if '_network_' in filename:
                    files_for_network.append(filename)
        if len(files_for_network) > 1:
            for filename in files_for_network:
                del upload_service_content[filename]

        files_for_organization = []
        for filename in upload_service_content.keys():
            if ('pi_service_' in filename) and ('pi_version_' in filename):
                pass
            else:
                if '_settings_' in filename:
                    files_for_organization.append(filename)
        if len(files_for_organization) > 1:
            for filename in files_for_organization:
                del upload_service_content[filename]

        # --------------
        # put content
        # --------------
        for filename in upload_service_content.keys():
            raw_file_name = filename.replace(' ', '_')
            output_file = base_codeupload_path + raw_file_name

            if ('read_' in filename) and ('.txt' == filename[-4:]):
                output_file = base_upload_path + raw_file_name

            if ('pi_service_' in filename) and ('pi_version_' in filename):
                # These already are the versioned files, to be saved directly
                output_file = base_codeupload_path + filename.replace('pi_service_', 'pi_service?').replace(
                    '_pi_version_', '?pi_version?')
            else:
                # This is a raw current file... discover the service and version
                pi_service = ''
                pi_version = ''
                if len(raw_file_name) > 3:
                    if ('pi_' == raw_file_name[0:3]) and ('.py' == raw_file_name[-3:]):
                        # look in the content for a service and a version string, and pull them out
                        # service = 'runner'
                        # version = 'R.2.4'
                        splits = upload_service_content[filename].decode('utf-8').split('\n')
                        for item in splits:
                            if 'service = ' in item:
                                try:
                                    pi_service = item.split('service = ')[1].replace("'", "").strip()
                                except:
                                    pass
                            if 'version = ' in item:
                                try:
                                    pi_version = item.split('version = ')[1].replace("'", "").strip()
                                except:
                                    pass
                            if pi_version and pi_service:
                                output_file = base_codeupload_path + 'pi_service?' + pi_service + '?pi_version?' + pi_version
                                break

            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            # write the file
            try:
                output_file_content = upload_service_content[filename].decode()
            except:
                output_file_content = upload_service_content[filename]
            with open(output_file, 'w') as f:
                f.write(output_file_content)

            # build the md5sum
            command = 'md5sum ' + output_file
            md5_file = output_file + '.md5'
            mem_string, fails = do_one_command(command)
            with open(md5_file, 'w') as f:
                f.write(mem_string.split()[0])

    else:
        return str(field_storage), other

    return make_get_content(environ)


# ====================================
def make_body_GET(environ):
    # ====================================
    return make_get_content(environ)


# ====================================
def get_codeupload_files_and_base(filter=''):
    # ====================================
    return_value = []

    if not os.path.exists(os.path.dirname(base_codeupload_path)):
        os.makedirs(os.path.dirname(base_codeupload_path))

    if filter:
        found = os.listdir(base_codeupload_path)
        for item in found:
            if filter in item:
                if not '.md5' in item:
                    return_value.append(item)
    else:
        return_value = os.listdir(base_codeupload_path)

    return sorted(return_value), base_codeupload_path


# ====================================
def make_get_content(environ):
    # ====================================

    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += "<br><br>"
        body += '<center>'
        body += "<B>Use this page to upload pi_ and read_ files</B>"
        body += '<br>'
        body += "(The read_ files go to the upload spot, but can be uploaded here also)"
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += """
<form id="codeupload" name="codeupload" method=post enctype=multipart/form-data>
<input type=file name=file multiple="multiple">"""
        body += '</td>'
        body += '<td>'
        body += """
<input type=submit value=codeupload>
</form>
"""
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'
        body += '<br><br>'

        body += '<br><br>'

        delete_permission = permissions.permission_allowed(environ, 'codeupload_delete')

        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'file'
        body += '</td>'
        body += '<td>'
        body += 'size'
        body += '</td>'
        if delete_permission:
            body += '<td>'
            body += 'Delete option<br>(must not be available for download)'
            body += '</td>'
        body += '<td>'
        body += 'Download'
        body += '</td>'
        body += '<td>'
        body += 'md5'
        body += '</td>'

        body += '</tr>'

        files, base_to_use = get_codeupload_files_and_base()
        the_site_to_call = make_home_url_from_environ(environ)

        for file_name in files:
            if not '.md5' in file_name:
                body += '<tr>'
                body += '<td>'
                body += file_name
                body += '</td>'

                body += '<td>'
                body += make_file_size_human_readable(base_to_use + file_name)
                body += '</td>'

                is_allowed = False
                result = datastore.get_value('download_permission_' + file_name)
                if result == 'Yes':
                    is_allowed = True

                if is_allowed:
                    the_string = "Yes"
                    next_Value = "No"
                    color = "(0, 255, 0, 0.3)"
                else:
                    the_string = "No"
                    next_Value = "Yes"
                    color = "(255, 0, 0, 0.3)"

                if delete_permission:
                    body += '<td>'
                    if not is_allowed:
                        next_Value = 'delete_it_now'
                        body += '<form method="post" action="">'
                        body += '<select name="file_delete_allowed" id="file_delete_allowed" hidden>'
                        body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                        body += '</select>'

                        body += '<select name="file_delete_allowed_key" id="file_delete_allowed_key" hidden>'
                        body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                        body += '</select>'
                        body += '<center>'
                        body += '<input type="submit" value="' + next_Value + '">'
                        body += '</center>'
                        body += '</form>'
                    body += '</td>'

                body += '<td>'
                pieces = file_name.split('?')
                if len(pieces) == 4:
                    service_name = pieces[1]
                    service_version = pieces[3]
                    the_request_url = the_site_to_call + '/codeupload?' + 'download=yes,service=' + service_name + ',version=' + service_version
                    body += '<a href="' + the_request_url + '"> download </href>'
                body += '</td>'

                body += '<td>'
                md5_file = base_to_use + file_name + '.md5'
                md5_value = ''
                if os.path.isfile(md5_file):
                    with open(md5_file, 'r') as f:
                        md5_value = f.read()
                body += md5_value
                body += '</td>'

                body += '</tr>'

        body += '</table>'
        body += '</center>'




    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n<body>\n'

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    do_download = False

    if 'download' in query_items:
        if ('service' in query_items) and ('version' in query_items):
            do_download = True

    if do_download:
        zOutFilename = get_pi_service_version_filename(query_items['service'], query_items['version'])
        block_size = 1024
        size = os.path.getsize(zOutFilename)

        file_to_get = zOutFilename.replace(base_codeupload_path, '')

        # allow non wrapped response
        start_response("200 OK", [('Content-Type', 'text/html'), ('Content-length', str(size)),
                                  ('Content-Disposition', 'attachment; filename=' + file_to_get)])
        filelike = open(zOutFilename, 'rb')

        if 'wsgi.file_wrapper' in environ:
            return environ['wsgi.file_wrapper'](filelike, block_size)
        else:
            return iter(lambda: filelike.read(block_size), '')


    else:
        try:
            body, other = make_body(environ)
            html += body
        except Exception as e:
            html += 'exception: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

        html += '</body>\n' \
                '</html>\n'
        response_header = [('Content-type', 'text/html')]

        try:
            html = organization.wrap_page_with_session(environ, html)
            start_response(status, response_header)
        except:
            # still on slicer01
            # allow non wrapped response
            start_response(status, response_header)

        return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

# end of file
