How to burn an image to an SD card

Get the latest tools from <PERSON><PERSON>:
https://www.balena.io/etcher/

Use <PERSON><PERSON>er, to directly load the zip file (It will open it up). and flash to card.

Note, there are some Cardinal Health computer use policies that may have disabled
your computers ability to write to external media. If this is the case, then we will
need to get you those permissions. On a Mac, this is done through a Self-Service
'Remove DLP-15.0' program, that is not normally available, and must be modified
in the JAMF configuration tool that manages the Mac applications.

To get into the exception group, to be allowed to write to USB:

<EMAIL>

To get admin privileges, to allow <PERSON><PERSON> to work,
contact the Mac administrator in the Client Engineering group (<PERSON>),
and get the "Privileges" app installed on your system.
Open the Privileges app, request for software install permission, and then
quickly open <PERSON>lena etcher, and follow the steps below.
(The permissions only last for 5 minutes).

Steps:

Click 'Flash from file' button. Find the downloaded image zip file, and click 'Open'.

Click 'Select target' button. Check the box for the storage Media to be used.

Click the 'Flash!' button.

Pull the SD card from the computer, and install into Raspberry Pi.

Plug into an HDMI monitor (HDMI0 port, the one closest to the power connector).

Plug in power.

The pi will reboot once as part of the process, and then will end on a home page,
that shows device ID and other information.

Once the device registers with Slicer (one minute), it will show in the list at:

https://slicer.cardinalhealth.net/reports

Use the "Find" in your browser, and do a search by ID or IDr, and then click on the ID
to load a page of just that one device. If you are logged in to Slicer, and have permissions,
you will be able to configure the device from this page.

