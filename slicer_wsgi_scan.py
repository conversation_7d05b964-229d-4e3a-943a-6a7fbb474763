# A scan for slicer page services

service = 'scan'
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/scan.py

using:
sudo vi /var/www/html/scan.py

It also requires:

sudo vi /etc/httpd/conf.d/python-scan.conf
----- start copy -----
WSGIScriptAlias /scan /var/www/html/scan.py
----- end copy -----

sudo chown apache:apache /var/www/html/scan.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/scan-runner
sudo chmod +x /var/www/html/scan-runner

# ===== begin: start file
#!/usr/bin/env python
import scan
scan.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/scan-runner.service
sudo systemctl daemon-reload
sudo systemctl stop scan-runner.service
sudo systemctl start scan-runner.service
sudo systemctl enable scan-runner.service

systemctl status scan-runner.service

sudo systemctl restart scan-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep scan-runner

OR

tail -f /var/log/syslog | fgrep scan-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/scan-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import scan; print(scan.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/scan

https://slicer.cardinalhealth.net/scan?siteid=PR005

https://slicer.cardinalhealth.net/scan?serial=100000002a5da842

https://slicer.cardinalhealth.net/scan?monitorNot=M.1.2


"""

nmap_scan_notes = """

----------------------------------------------
Look for devices with port 22 open:
nmap -p 22 ************/24
----------------------------------------------


----------------------------------------------
Nmap scan report for ************56
Host is up (0.050s latency).
PORT   STATE SERVICE
22/tcp open  ssh

Nmap scan report for ************61
Host is up (0.065s latency).
PORT   STATE    SERVICE
22/tcp filtered ssh

Nmap scan report for ************85
Host is up (0.066s latency).
PORT   STATE  SERVICE
22/tcp closed ssh
----------------------------------------------


nmap -sP 22 ************-90

----------------------------------------------
# nmap_scans.py

# nmap -p 22 10.235.0-255.0-255 --max-retries 2 --host-timeout 30s --max-rate 100 > nmapResults.235.txt
# python3 nmap_scans.py


import os
import subprocess


list_of_dir = os.listdir('.')

print(list_of_dir)


indexes = [193,234,216]


for index in range(0,256):
    indexes.append(index)

for index in indexes:
    output_file = "nmapResults."+str(index)+".txt"
    if output_file in list_of_dir:
        print('skipping', output_file)
    else:
        print('building', output_file)
        command = "nmap -p 22 10."+str(index)+".0-255.0-255 --max-retries 2 --host-timeout 30s --max-rate 100"
        command_splits = command.split(" ")
        print ("command_splits", command_splits)

        doit = subprocess.Popen (command_splits, universal_newlines=True,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        (mem_string, fail) = doit.communicate()

        f = open("nmapResults."+str(index)+".txt", 'w')
        f.write(mem_string)
        f.close()

        code = doit.wait()


----------------------------------------------
----------------------------------------------
----------------------------------------------
----------------------------------------------
----------------------------------------------
----------------------------------------------
----------------------------------------------
----------------------------------------------


"""
import copy
import traceback
import json
import os
import sys
import time

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    base_scan_path = service_config['base_scan_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def get_all_scan_items(item_type):
    # ====================================
    return_value = []

    scan_items_path = base_scan_path + item_type + '/'
    if not os.path.exists(os.path.dirname(scan_items_path)):
        os.makedirs(os.path.dirname(scan_items_path))

    try:
        requests_list = os.listdir(scan_items_path)
        for item in requests_list:
            return_value.append(scan_items_path + item)
    except:
        pass

    return return_value


# ====================================
def make_scan_item(item_type, item, content):
    # ====================================
    scan_items_path = base_scan_path + item_type + '/'
    if not os.path.exists(os.path.dirname(scan_items_path)):
        os.makedirs(os.path.dirname(scan_items_path))

    try:
        scan_item_file = scan_items_path + item
        open(scan_item_file, 'w').write(content)
    except:
        pass


# ====================================
def peel_nmap_report(content):
    # ====================================
    result = ''

    in_address = ''

    for item in content.split('\n'):
        if 'Nmap scan report for ' in item:
            in_address = item.replace('Nmap scan report for ', '')

            if result:
                result += ', '
            result += in_address

    return result


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(scan status)'

    status = os.system('systemctl is-active --quiet scan-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "scan-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1
        time.sleep(15)

        try:
            all_requests = get_all_scan_items('requests')
            all_results = get_all_scan_items('results')

            for request_item in all_requests:
                the_matching_result = request_item.replace('requests', 'results')
                if not the_matching_result in all_results:
                    content = '(test)'

                    import shlex
                    import subprocess
                    command = "nmap -n -p 22  " + str(open(request_item, 'r').read())
                    command_splits = shlex.split(command)
                    # command_splits = command.split(" ")

                    doit = subprocess.Popen(command_splits, universal_newlines=True,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE)
                    (mem_string, fails) = doit.communicate()

                    content = str(mem_string) + str(fails)

                    try:
                        doit.terminate()
                    except:
                        pass

                    make_scan_item('results', the_matching_result.split('/')[-1], content)
        except:
            print(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))


# ====================================
def make_body_POST(environ):
    # ====================================
    # do work on content

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    if 'the_selection' in d:
        if 'scan_request_selection' == str(d['the_selection'][0]):
            item_type = 'requests'
            item = ''
            content = ''
            if 'request_id' in d:
                item = str(d['request_id'][0])
            if 'request_content' in d:
                content = str(d['request_content'][0])

            if item and content:
                make_scan_item(item_type, item, content)

    # then return what GET would have done
    return make_body_GET(environ)


# ====================================
def make_body_GET(environ):
    # ====================================

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        all_requests = get_all_scan_items('requests')
        all_results = get_all_scan_items('results')

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'Request ID'
        body += '</td>'
        body += '<td>'
        body += 'Request'
        body += '</td>'
        body += '<td>'
        body += '</td>'
        body += '</tr>'

        body += '<form method="post" action="">'

        body += '<select name="the_selection" id="the_selection" hidden>'
        body += '<option value="scan_request_selection" selected>' + 'scan_request_selection' + '</option>'
        body += '</select>'

        body += '<tr>'
        body += '<td>'

        import datetime
        TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S')

        body += """<input type="text" size=25 name="request_id" value=\"""" + TS + """\">"""
        body += '</td>'
        body += '<td>'
        body += """<input type="text" size=25 name="request_content" value=\"""" + '************/24' + """\">"""
        body += '</td>'
        body += '<td>'
        body += '<input type="submit" value="Submit">'
        body += '</td>'
        body += '</tr>'
        body += '</form>'

        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'Request ID'
        body += '</td>'
        body += '<td>'
        body += 'Request'
        body += '</td>'
        body += '<td>'
        body += 'Result'
        body += '</td>'
        body += '</tr>'

        for request_item in sorted(all_requests):
            the_matching_result = request_item.replace('requests', 'results')

            body += '<tr>'
            body += '<td>'
            body += request_item.split('/')[-1]
            body += '</td>'
            body += '<td>'
            try:
                body += str(open(request_item, 'r').read())
            except:
                pass
            body += '</td>'
            body += '<td>'
            try:
                body += str(peel_nmap_report(open(the_matching_result, 'r').read()))
            except:
                pass
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body


# ====================================
def make_body(environ):
    # ====================================
    body = ''

    if permissions.permission_prefix_allowed(environ, 'scan_') or permissions.permission_prefix_allowed(environ,
                                                                                                        'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body


# ====================================
def create_html_body(environ):
    try:
        body_content = make_body(environ)
    except:
        body_content = str(sys.version_info)
    html_body = f"<html>\n<body>\n{body_content}\n</body>\n</html>\n"
    return html_body


def application(environ, start_response):
    status = '200 OK'
    html = create_html_body(environ)
    response_header = [('Content-type', 'text/html')]
    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
