To build a new image:

############################
2023.10.30
Pi5:
https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-10-10/2023-10-10-raspios-bookworm-armhf-lite.img.xz?_gl=1*17vv57o*_ga****************************.*_ga_22FD70LWDS*******************************************..
in
/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/Starting_image/2023-10-10-raspios-bookworm-armhf-lite.img.xz

# added 2024.03.12
2024.03.12
https://www.raspberrypi.com/software/operating-systems/
https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-12-11/2023-12-11-raspios-bookworm-armhf-lite.img.xz?_gl=1*eds2o2*_ga******************************_ga_22FD70LWDS*******************************************..

# added 2024.04.17
https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2024-03-15/2024-03-15-raspios-bookworm-armhf-lite.img.xz?_gl=1*wv5zqd*_ga******************************_ga_22FD70LWDS*******************************************..


############################



(test on pi at Dave's house)
(ID=10000000e3669edf)
(IDr=1530-0022-4805-1292-511)

ssh pi@10.226.171.139

############################
Original:
Download the "Raspberry Pi OS Lite" install (32bit version):
https://www.raspberrypi.org/software/operating-systems/#raspberry-pi-os-32-bit

This was then loaded to GCP bucket in pi-mgmt-pr-cah-distribution, and can be downloaded from:
https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/raw_image_starts?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

############################
Or:
gs://pi-mgmt-pr-cah-distribution/raw_image_starts/2021-03-04-raspios-buster-armhf-lite.zip

Release date: March 4th 2021
Kernel version: 5.10
Size: 442MB
SHA256: ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39

Most recent release note:
2021-03-04:
  * Thonny upgraded to version ********
  * SD Card Copier made compatible with NVMe devices; now built against GTK+3 toolkit
  * Composite video options removed from Raspberry Pi 4 in Raspberry Pi Configuration
  * Boot order options in raspi-config adjusted for more flexibility
  * Recommended Software now built against GTK+3 toolkit
  * Fix for crash in volume plugin when using keyboard could push value out of range
  * Fix for focus changing between windows in file manager when using keyboard to navigate directory view
  * Fix for Raspberry Pi 400 keyboard country not being read correctly in startup wizard
  * Armenian and Japanese translations added to several packages
  * Automatically load aes-neon-bs on ARM64 to speed up OpenSSL
  * Raspberry Pi firmware fcf8d2f7639ad8d0330db9c8db9b71bd33eaaa28
  * Linux kernel 5.10.17

verify sha with (on Mac):
shasum -a 256 ~/Downloads/2021-03-04-raspios-buster-armhf-lite.zip
OR
shasum -a 256 ~/Downloads/raw_image_starts-2021-03-04-raspios-buster-armhf-lite.zip

ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39

Leave the file zipped. The Balena Etcher program can work from the zip file as input.

If Mac has been forced to not allow USB memory, then go to
SelfService, find the "Remove DLP-15.0", and run the remove.
(This is a not-normal remove, and must be made visible by using Jamf tools to show it)

# --------------------------------------
Use balenaEtcher to Flash a 16GB sd card:

"Flash fron file"
(use the zip file)
"Select target"
(point to sd card)
"Flash!"
(might need to enter pc password, to allow access to the sd card, on Mac)

# --------------------------------------

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!! Disconnect pi from wired network !!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

Connect keyboard, load sd card into pi and then apply power.

# added 2024.03.12
Select keyboard as English, US (no sub option)

Old: ====================
Log in as user:
pi
pass:
raspberry

change the password with:
passwd
(Use the cah pi default password, that is not raspberry)
Old: ====================
New: ====================
set new user as pi, (Use the cah pi default password, that is not raspberry)
log in as the pi user
New: ====================

Enable and start ssh:
sudo systemctl start ssh

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!! Ok to plug into wired network now !!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

type "ip addr" to get the ip address

---- At this point, you no longer need the keyboard, and can do the rest from another station
ssh pi@<addr>

sudo systemctl enable ssh


############################
# set up the hardware
############################
OLD:
sudo vi /etc/default/keyboard
(change "gb" to "us")


############################
# set up new admin user
############################
(Started 2021.07.23 with Image version 2.2.0
sudo adduser cah-pi-su
(vault lookup?)
(leave all entries blank, and keep hitting enter until it saves)

# adm group gets us the journalctl log prints for all
sudo usermod -a -G video,adm cah-pi-su
sudo adduser cah-pi-su sudo

# to avoid typing password at each restart of sudo permission window
sudo vi /etc/sudoers.d/010_cah-pi-su
(add:
cah-pi-su ALL=(ALL) NOPASSWD: ALL
)

exit
(log out of pi user)

log in as cah-pi-su user

sudo vi /etc/shadow
(Find "pi:$6$rseVWwvM7AfG7bKY$TV6WN558gwmBs.idUmnkzO9PlOgYJtenJM5oBJeDO4FcGAo.qEWdkHAw4CBDmYR.q3HZxRkO8fVPEa69t2cGo1:18690:0:99999:7:::"
and delete the entire line


############################
# set up worker user
############################
sudo adduser worker
(normal camel case password, cap first)

# printing (long install) (Must do it now, to set permissions just once, which include lpadmin)

# added 2024.03.12
sudo apt-get update

# this is the long time to install one
sudo apt-get install -y cups

sudo usermod -a -G video,input,lpadmin worker

# to see the settings:
ls -l /dev/input/
groups worker

############################
# set up printer user
############################
sudo adduser printer
(Pass: printer)

sudo usermod -a -G lpadmin printer

############################
Wlan Pi5 setting:
############################
in sudo raspi-config, localization options, there is a choice for wlan legal settings.

Localization..., WLAN Country..., US

# FixMe: figure out what we need to set for wlan legal.
# FixMe: Does this also impact bluetooth?

############################
# get updates and required installs
############################
# https://blog.r0b.io/post/minimal-rpi-kiosk/
# sudo apt-get update # now in pi_hmi

# for browser and X environment
#sudo apt-get install -y --no-install-recommends xserver-xorg-video-all \
#  xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils \
#  chromium-browser unclutter # now in pi_hmi

# for managing chromium trust store
# sudo apt-get install -y libnss3-tools # now in pi_hmi

# for making QR codes in pi_hmi
#sudo apt-get install -y python3-pip
#sudo pip3 install MyQR
#sudo apt-get install libopenjp2-7 # now in pi_hmi


# for whitelisting
# sudo apt-get install -y privoxy # now in pi_hmi

# still do it here, for now, until hmi can be debugged.

log in as cah-pi-su user

##########################
Start: Installs (one at a time)
##########################
sudo apt -y update

# This next one takes a while
sudo apt -y upgrade

sudo dpkg --configure -a
sudo apt-get install -y privoxy
sudo apt-get install -y python3-pip

# added 2024.03.12
# https://www.makeuseof.com/fix-pip-error-externally-managed-environment-linux/
ls -l /usr/lib/ | fgrep python3
# modify the following line based on what version you find from the line above
sudo rm /usr/lib/python3.11/EXTERNALLY-MANAGED

sudo pip3 install MyQR
OR (If the above line fails)
sudo apt-get install -y cmake
sudo apt-get install -y libblas-dev
sudo apt install -y libblas3 liblapack3 liblapack-dev libblas-dev
https://pillow.readthedocs.io/en/latest/installation.html
sudo apt install -y libjpeg-dev zlib1g-dev
sudo pip3 install MyQR --break-system-packages

# test
sudo python3 -c "from MyQR import myqr as mq"
(Should be no errors)

sudo apt-get install libopenjp2-7
sudo apt-get install -y --no-install-recommends xserver-xorg-video-all xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils chromium-browser unclutter
sudo apt-get install -y libnss3-tools

# screen grabs
sudo apt-get install -y scrot

# first needed for pi_bluetooth (now pi_runner too)
sudo pip3 install apscheduler --break-system-packages
sudo apt-get install -y tmux
sudo apt-get install -y nmon
#(use 'nmon' at command line)

##########################
End: Installs
##########################

##########################
# Edits to configure:
##########################
sudo vi /etc/privoxy/config
(find line that starts "#debug  1024", and uncomment that line)

log is "/var/log/privoxy/logfile", and starts automatically based on the config change.
Uncommenting this line provides us the ability to collect the details about what is blocked.

sudo vi /etc/cups/cupsd.conf
(change the line "DefaultAuthType Basic" to "DefaultAuthType None")

sudo systemctl restart cups


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Security audit
# https://cisofy.com/lynis/

sudo apt-get install -y lynis

# Skip doing this test, unless you want to know the numbers now
#sudo lynis audit system -Q

initial result 2021.07.24:
  Hardening index : 55 [###########         ]
  Tests performed : 213
  Plugins enabled : 1

sudo apt update
sudo apt upgrade -y

# ----------
# Hardening
# ----------

# Now done in pi_security:
# sudo vi /etc/ssh/sshd_config

sudo vi /etc/init.net
  ********************************************************************************
  This computer system and associated networks are the property of and for the
  sole business use of Cardinal Health, Inc. authorized users.  Cardinal Health
  reserves the right to monitor computer and network usage, and your use of
  Cardinal systems constitutes consent to such monitoring.  The company's
  computers and the proprietary data and information stored on them remain at all
  times the property of Cardinal Health, Inc.  Unauthorized access to this system
  is strictly forbidden.  This server and your actions after login are monitored.
  ********************************************************************************

sudo chmod o-rx /usr/bin/gcc
sudo chmod o-rx /usr/bin/g++

# https://www.cyberciti.biz/faq/linux-disable-modprobe-loading-of-usb-storage-driver/
sudo vi /etc/modprobe.d/blacklist.conf
blacklist usb-storage


# ----------
# Skip doing this test, unless you want to know the numbers now
sudo lynis audit system -Q

  Hardening index : 63 [############        ]
  Tests performed : 213
  Plugins enabled : 1

# ----------
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

# network manager
sudo apt-get update
sudo apt -y --fix-broken install
sudo apt-get install -y network-manager


# Setup network manager to be the primary
sudo vi /etc/dhcpcd.conf

(add to end)
denyinterfaces wlan0
denyinterfaces wlan1

# Configure Network Manager to not randomize the wifi mac address
# https://www.raspberrypi.org/forums/viewtopic.php?t=237623

sudo vi /etc/NetworkManager/conf.d/100-disable-wifi-mac-randomization.conf
[connection]
wifi.mac-address-randomization=1
[device]
wifi.scan-rand-mac-address=no

sudo reboot # to fix the randomized wlan0 mac address

############################
# config browser
############################
# https://pimylifeup.com/raspberry-pi-kiosk/

sudo vi /home/<USER>/.xinitrc
*** start ***
#!/usr/bin/env sh
xset -dpms
xset s off
xset s noblank

screen_width="$(fbset -s | awk '$1 == "geometry" {print $2}')"
screen_height="$(fbset -s | awk '$1 == "geometry" {print $3}')"

# https://stackoverflow.com/questions/5637530/disable-shortcuts-in-google-chrome
# diasable ctrl, alt, and others

unclutter -idle 2 &

#export DISPLAY=localhost.0.0
xmodmap -e "keycode 37="
xmodmap -e "keycode 67="
xmodmap -e "keycode 105="
xmodmap -e "keycode 133="
xmodmap -e "keycode 134="

rm -rf /home/<USER>/.config/chromium/Singleton*

while true; do
    /cardinal/browserstart
done;
*** end ***
sudo chown -R worker:worker /home/<USER>/.xinitrc


# https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login


sudo mkdir /cardinal
sudo mkdir /cardinal/localhtml
sudo su

echo "blank page" > /cardinal/localhtml/index.html
exit

sudo vi /cardinal/browserstart_default
# The following file is also maintained by pi_runner, so make matching changes there.
*** start ***
    cp /cardinal/localhtml/chromium_Default_Preferences /home/<USER>/.config/chromium/Default/Preferences

    # https://peter.sh/experiments/chromium-command-line-switches/
    chromium-browser \
      --proxy-server="https=127.0.0.1:8118;http=127.0.01:8118" \
      --window-size=$screen_width,$screen_height \
      --window-position=0,0 \
      --start-fullscreen \
      --incognito \
      --noerrdialogs \
      --disable-translate \
      --no-first-run \
      --fast \
      --fast-start \
      --disable-infobars \
      --disable-features=TranslateUI \
      --disable-features=Translate \
      --disk-cache-dir=/dev/null \
      --overscroll-history-navigation=0 \
      --disable-pinch \
      --kiosk \
      file:///cardinal/localhtml/index.html
*** end ***
sudo chown -R worker:worker /cardinal/browserstart_default
sudo chmod +x /cardinal/browserstart_default
sudo cp /cardinal/browserstart_default /cardinal/browserstart




############################
# startx issues on Pi5
############################
# added 2024.03.12
# -bash: startx: command not found

# https://forums.raspberrypi.com/viewtopic.php?t=361722
sudo apt install gldriver-test

# https://iiab.me/kiwix/raspberrypi.stackexchange.com_en_all_2022-11/questions/84804/how-to-start-gui-with-startx-command-not-found
sudo apt-get install xinit -y

############################
# 2024.03.12 Runner fails
############################
Mar 13 07:57:07 raspberrypi pi-runner[945]: zoneinfo._common.ZoneInfoNotFoundError: 'Multiple conflicting time zone configurations found:\n/etc/timezone: Europe/London\n/etc/localtime is a symlink to: America/New_York\nFix the configuration, or set the time zone in a TZ environment variable.\n'

# cat /etc/timezone
Europe/London

# ls -l /etc/localtime
lrwxrwxrwx 1 root root 38 Mar 12 15:12 /etc/localtime -> ../usr/share/zoneinfo/America/New_York

sudo rm /etc/localtime


############################
Disable Pi5 power button:
############################
# added 2024.03.12
https://forums.raspberrypi.com/viewtopic.php?p=2188834&hilit=power+button+disable#p2188834
sudo vi /etc/systemd/logind.conf
**** start ****
[Login]
HandlePowerKey=ignore
HandlePowerKeyLongPress=ignore
**** end ****

# FixMe: Does this setting get modified when we do update/upgrade activity?
If so, then do it at the end, or better yet, auto fix it if anyone changes it.


############################
Shapshot:
############################

sudo shutdown -h now

# Make image of the 16GB card
# 2024.03.28 using pi os 2023-12-11 -> (ApplePi Baker, turn off shrink, downloads/20240328/backup3)
# 2024.04.17 using pi os 2024-03-15 -> (ApplePi Baker, turn off shrink, downloads/20240417/backup1)
# 2024.04.23 using pi os 2024-03-15 -> (ApplePi Baker, turn off shrink, downloads/20240417/backup1a)
# 2024.11.25 Downloads/pi_image_2024.11.25.zip

#!#!#!#
#!#!#!#
# Feel free to stop here, reboot, manually log in as worker, then manually do a startx at the terminal
#!#!#!#
#!#!#!#

# On the device:
startx

# on a remote ssh terminal
ps ax | fgrep chromium

#!#!#!#
#!#!#!#

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!!!!! Start: a development test.... not production !!!!!
############################
# Bluetooth issue building a Pi4/5 image
############################
https://forums.debian.net/viewtopic.php?t=151557
(Near the bottom of the post)

sudo apt-get install pulseaudio-module-bluetooth -y
pulseaudio --kill
pulseaudio --start

sudo journalctl --vacuum-time=1m
journalctl -u pi-bluetooth.service --no-pager

# -----------------------------------
better, but still not working:

Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/vcp.c:vcp_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init vcp plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/mcp.c:mcp_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init mcp plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/bap.c:bap_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init bap plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: Bluetooth management interface 1.22 initialized
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/sap/server.c:sap_server_register() Sap driver initialization failed.
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: sap-server: Operation not permitted (1)
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: Failed to set privacy: Rejected (0x0b)

https://forums.debian.net/viewtopic.php?t=154544

sudo apt-get install libspa-0.2-bluetooth -y

sudo apt-get remove pulseaudio-module-bluetooth -y

systemctl --user start pulseaudio.socket


https://www.reddit.com/r/archlinux/comments/yu9az9/bluetooth_errors_since_2_days_ago/
Hi, i had the same problem, i fixed it by running these commands as sudo:

sudo rmmod btusb
sudo rmmod btintel
sudo modprobe btintel
sudo modprobe btusb

2024.04.17

////////////////////////////
Try:
https://pimylifeup.com/raspberry-pi-bluetooth/

sudo apt install -y bluetooth pi-bluetooth bluez blueman

no help...
////////////////////////////

Need versions of things:
bluetoothctl --version
bluetoothd --version

hciuart


----------------------
old (SP.40 image):
bluetoothctl --version
bluetoothd --version

bluetoothctl: 5.50
5.50

dmesg | tee | fgrep Bluetooth
(empty) # But maybe only empty of bluetooth, because it was pushed out the beginning of the log...

uname -a
Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux


dpkg --status bluez | fgrep Version
Version: 5.50-1.2~deb10u3+rpt1

RS6000 works like expected

----------------------
old (SP.40 image):
sudo apt-get update

reboot

bluetoothctl --version
bluetoothd --version

bluetoothctl: 5.50
5.50

RS6000 works like expected
----------------------
old (SP.40 image):
sudo apt-get upgrade

reboot

bluetoothctl --version
bluetoothd --version

bluetoothctl: 5.50
5.50

cah-pi-su@cah-rp-10000000e3669edf:~ $ uname -a
Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux

dpkg --status bluez | fgrep Version
Version: 5.50-1.2~deb10u4

RS6000 Does not work

Update to latest pi-bluetooth, to handle tmux changes:

Still fails.

dmesg | tee | fgrep Bluetooth
[    8.455095] Bluetooth: Core ver 2.22
[    8.455217] Bluetooth: HCI device and connection manager initialized
[    8.455342] Bluetooth: HCI socket layer initialized
[    8.455403] Bluetooth: L2CAP socket layer initialized
[    8.455454] Bluetooth: SCO socket layer initialized
[    8.491685] Bluetooth: HCI UART driver ver 2.3
[    8.491710] Bluetooth: HCI UART protocol H4 registered
[    8.491849] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    8.506279] Bluetooth: HCI UART protocol Broadcom registered
[    9.130041] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    9.130061] Bluetooth: BNEP filters: protocol multicast
[    9.130106] Bluetooth: BNEP socket layer initialized
[ 1385.452174] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[ 1385.452213] Bluetooth: HIDP socket layer initialized

----------------------
middle (SP.47 image):

bluetoothctl --version
bluetoothd --version

bluetoothctl: 5.50
5.50

dmesg | tee | fgrep Bluetooth

[    7.884677] Bluetooth: Core ver 2.22
[    7.884794] Bluetooth: HCI device and connection manager initialized
[    7.884821] Bluetooth: HCI socket layer initialized
[    7.884840] Bluetooth: L2CAP socket layer initialized
[    7.884874] Bluetooth: SCO socket layer initialized
[    7.934892] Bluetooth: HCI UART driver ver 2.3
[    7.934918] Bluetooth: HCI UART protocol H4 registered
[    7.935024] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    7.935448] Bluetooth: HCI UART protocol Broadcom registered
[    8.494406] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    8.494432] Bluetooth: BNEP filters: protocol multicast
[    8.494466] Bluetooth: BNEP socket layer initialized


----------------------
Pi5 image:
bluetoothctl --version
bluetoothctl: 5.66
bluetoothd --version
5.66

uname -a
Linux cah-rp-ec885c8aa5f46f0d 6.6.20+rpt-rpi-v8 #1 SMP PREEMPT Debian 1:6.6.20-1+rpt1 (2024-03-07) aarch64 GNU/Linux

dpkg --status bluez | fgrep Version
Version: 5.66-1+rpt1+deb12u1

dmesg | tee | fgrep -i bluetooth

[    5.664370] Bluetooth: Core ver 2.22
[    5.664402] NET: Registered PF_BLUETOOTH protocol family
[    5.664404] Bluetooth: HCI device and connection manager initialized
[    5.664411] Bluetooth: HCI socket layer initialized
[    5.664415] Bluetooth: L2CAP socket layer initialized
[    5.664420] Bluetooth: SCO socket layer initialized
[    5.674567] Bluetooth: HCI UART driver ver 2.3
[    5.674575] Bluetooth: HCI UART protocol H4 registered
[    5.674609] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    5.674840] Bluetooth: HCI UART protocol Broadcom registered
[    6.042366] Bluetooth: hci0: BCM: chip id 107
[    6.043130] Bluetooth: hci0: BCM: features 0x2f
[    6.044254] Bluetooth: hci0: BCM4345C0
[    6.044261] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0000
[    6.047494] Bluetooth: hci0: BCM4345C0 'brcm/BCM4345C0.raspberrypi,5-model-b.hcd' Patch
[    6.759113] Bluetooth: hci0: BCM: features 0x2f
[    6.760571] Bluetooth: hci0: BCM43455 37.4MHz Raspberry Pi 3+-0190
[    6.760583] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0382
[    6.761807] Bluetooth: hci0: BCM: Using default device address (43:45:c0:00:1f:ac)
[   13.840787] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[   13.840794] Bluetooth: BNEP filters: protocol multicast
[   13.840800] Bluetooth: BNEP socket layer initialized
[   13.842897] Bluetooth: MGMT ver 1.22



!!!!! End: a development test.... not production !!!!!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!


############################
# Set image (must be ready before runner install)
############################
sudo su

# This set the base image value that gets reported.
echo "Image version: 2.4.1" > /cardinal/image_ver.txt
# 2024.03.13 Bookworm Build for Pi5
echo "Image version: 2.4.2" > /cardinal/image_ver.txt

exit


############################
# Fix numpy
############################
sudo pip3 uninstall numpy -y
sudo apt install python3-numpy


############################
# load runner
############################

# to collect serial number like: 10000000e3669edf
cat /proc/cpuinfo | fgrep Serial



(Manually set up this one, according to the instructions inside it)
pi_runner

like:
sudo vi /cardinal/pi_runner.py
(copy and paste the content of that source file)
cd /cardinal
sudo python3 pi_runner.py install

!!! Once running, and checking into Slicer, if there is an update to settings
!!!    ready for it in slicer, then there may be a reboot that happens.... this is normal.

(see that it shows up:
https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf
)


############################
# Load through Slicer interface to the device (Runner must have been loaded and running first)
############################
pi_bluetooth
pi_config
pi_hmi
pi_logging
pi_monitor
pi_network
pi_organization
pi_security
pi_settings

sudo reboot

#!#!#!#
#!#!#!#
Stop here, and manually test with logging in as worker, then do 'startx'
#!#!#!#
#!#!#!#


############################
# Change the autologin user to the worker
############################
# !!!This just gets to the command line prompt...
#       do not expect auto startx or anything...

After 2023.10.30: (Pi5 running latest OS)
https://www.nixcraft.com/t/how-to-configure-autologin-on-the-raspbian-buster-console/3922/2
sudo vi /etc/systemd/logind.conf
replace "#NAutoVTs=6" with "NAutoVTs=1"

sudo <NAME_EMAIL>

#sudo vi /etc/systemd/system/<EMAIL>.d

add (near the top... above "### Lines below this comment will be discarded"):
[Service]
ExecStart=
ExecStart=-/usr/sbin/agetty --autologin worker --noclear %I $TERM

then type ctrl-x, Y, enter




!!!! Start: OLD !!!!
sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf
*** start ***
[Service]
ExecStart=
ExecStart=-/sbin/agetty --autologin worker --noclear %I \$TERM
*** end ***
sudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>

# Test auto login:
sudo reboot
!!!! End: OLD !!!!

############################
# Add action to the worker login
############################
sudo vi /home/<USER>/.bash_profile
*** start ***
if [ -z $DISPLAY ] && [ $(tty) = /dev/tty1 ]
then
  startx
fi
*** end ***
sudo chown -R worker:worker /home/<USER>/.bash_profile


############################
# make sure the hmi install is complete
#    (tempscript no longer there, may take 10 minutes):
############################
# ls -l /cardinal/pi_hmi_tempscript


############################
# save test image
############################
2024.04.17 -> backup2
2024.04.23 -> starting from backup1b, worked to here, then save as -> 20240417/backup2b



############################
# test for GUI performance
############################
sudo reboot


#----------------------------
# Try to break out of browser
#----------------------------
# https://www.howtogeek.com/408289/what-your-function-keys-do-in-google-chrome/

---------
Blocked:
---------
Should be covered by disabling certain keys:
crtl o (open file)
ctrl alt F2 (opens terminal)
ctrl w (close window)

(dont want to allow F1, but can not block F1)
Should be covered by privoxy blacklist:
F1 (opens help, which should show a fail page)

Alt esc (? incognito)

---------
Allowed:
---------

Should be covered by do while loop:
Alt F4 (exit) (use this to get out of the F1 result)


#----------------------------
# Load bookmarks via Slicer interface
#----------------------------
Test those links work on the pi.


#----------------------------
# second pi test
#----------------------------
Load the card into a second pi, and see that there is not a warning about
a duplicate chromium profile

1) Prepare the current card
sudo shutdown -h now

2) put into new raspberry pi, and see that it is not interrupted by any warnings.


#----------------------------
# bluetooth scanner test
#----------------------------
0) Configure two devices to have bluetooth enabled (Slicer interface to set the attribute)
1) Start will all devices powered off. Power on raspberry pi devices first.
2) See that devices report "(no devices seen)"
3) Cold boot the scanner (press in the side button while plugging in the battery, and hold until it beeps),
    then scan the one time configuration bar code
    (A copy of this is in the CLSY (EITSS) run-book for Raspberry Pi, under a heading "RS6000 Scanner Interface").
    Confirm that both devices show a bar code for pairing after 15 to 30 seconds.
4) Pair to device 1 (it may take two scans of the barcode, over 30 to 40 seconds)
5) Pair to device 2 (see that it unpairs from device 1 after 30 to 60 seconds)
6) Pull the battery from the scanner, and let sit for more than 5 minutes; go 10 minutes to be sure.
    Device 2 should transition quickly, device 1 has a 5 minute timeout that has to expire.
7) See that both devices revert to "(searching..)"
8) Power up the scanner. It might reconnect to device 2, by itself (with lots of beeps), that would be a fail.
9) Pair to device 1.
10) Pair to device 2.
11) Leave scanner powered, reboot device 2 and 1, and see that no automatic pairing occurs; give it 10 minutes.


############################
# Finish up the close out of the image
############################
sudo su

# All of these as a single copy and paste into the terminal:

apt-get clean
apt-get -y autoremove --purge

# All of these as a single copy and paste into the terminal (to the next empty line):
rm -rf /Downloads
rm -rf /home/<USER>/*
rm -rf /cardinal/save_values

# All of these as a single copy and paste into the terminal (to the next empty line):
systemctl stop pi-bluetooth.service
systemctl stop pi-config.service
systemctl stop pi-hmi.service
systemctl stop pi-logging.service
systemctl stop pi-monitor.service
systemctl stop pi-network.service
systemctl stop pi-organization.service
systemctl stop pi-runner.service
systemctl stop pi-security.service
systemctl stop pi-settings.service

# All of these as a single copy and paste into the terminal (to the next empty line):
find /var/log -type f -regex ".*\.gz$" -delete
find /var/log -type f -regex ".*\.[0-9]$" -delete
rm /var/log/*
echo 'yes' > /cardinal/needsexpand
echo -n "0" > /cardinal/boot_count.txt
echo -n "0" > /cardinal/grab_count.txt
cp /cardinal/browserstart_default /cardinal/browserstart
rm -rf /cardinal/localhtml/*
rm -rf /cardinal/log/*
rm -rf /cardinal/config_*
rm /cardinal/call_home_locations.txt
rm /cardinal/wifi_ssid_psk.txt
rm /cardinal/screen_resolution.txt
rm /cardinal/screen_zoom.txt
rm /cardinal/wifi_config.txt
rm /cardinal/call_home_locations.txt
mkdir /cardinal/localhtml/
echo '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>' > /cardinal/localhtml/index.html
sudo shutdown -h now


############################
# Make an image of the system and shrink it
############################

# Current:
Once: Install ApplePiBaker
https://www.tweaking4all.com/software/macosx-software/applepi-baker-v2/#DownloadApplePiBaker
    using:
    https://www.tweaking4all.com/downloads/ApplePi-Baker-v2.dmg

    Run ApplePi-Baker by opening the Applications folder, and double clicking the ApplePiBaker app.
    Select the external disk (just to get the permissions pop up)
    (follow the prompts to allow full disk access)

    Reboot Mac (to make changes apply correctly/Fully)

Caution:
On a MacIntosh computer, there is a JAMF setting that blocks all USB memory card access.
If you do not see any USB memory attached when you plug in an SD card reader, with sd card,
then reach out to the Mac person (Steven Murphy), and he them re-enable the Self Service
item 'Remove DLP-15.0'; then, run that program once. (Search for DLP)

Each Time:
Run ApplePiBaker from the Applications folder

1) Select disk.
2) In the lower right corner of the window, to the right of the word "Options", make blue the
one that does the shrink (2nd one in from left to right)
3) Click "Backup", make name like "cah_pi_raw_image_2.4.1.SP.33"
(for 64 GB card, takes 11 to 12 minutes)
(No matter the size of the card, the zip should be under ~1.5 to 2.5 GB in size.
If it is larger, then you must have missed the 'shrink' setting in step 2)

This has now generated a zip file.

# Future:
http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php

############################
# Burn a test SD card
############################
Use Balena etcher to flash a card from the newly created zip file.
If this fails due to CrowdStrike DLP blocking, <NAME_EMAIL>,
and ask for an exception.

Put card in a pi, and see that it boots correctly.

############################
# Add to Slicer upload folder
############################
Upload to Slicer.

Mark it as 'available for download.

############################
# Chat the "LATAM Raspberry Pi" group that the new version is ready
############################
Pi image version 2.Y.Z is available.

https://slicer.cardinalhealth.net
Log in
Go to the "download" page to view version text file, and to download images.


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Below here are notes that have been tested, but not yet in the build process
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
############################
# Upload compressed image file to GCP
############################
https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

############################
# revert any old "roll forward" "device_service_" in the datastore
############################
These may accidentally roll back versions of a new release.

Use the "image" version report to notice that it has been re-imaged, and to
ignore any old forced settings?

############################
# Share download link (add users to be allowed to use the link, in the file permissions)
############################
https://storage.cloud.google.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.2.zip

Added <EMAIL> as a reader at the file level (did not work)
Added <EMAIL> as a reader at the bucket level for pi-mgmt-pr-cah-distribution


############################
# USB
############################

# for power control of USB in the pi_hmi service
https://snapcraft.io/install/hub-ctrl/raspbian
sudo apt install -y snapd
sudo snap install hub-ctrl

https://github.com/mvp/uhubctl
sudo apt-get install -y libusb-1.0-0-dev
sudo apt-get install -y git
git clone https://github.com/mvp/uhubctl
cd uhubctl
make
sudo make install


############################
# Upload to GCP bucket
############################
https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

https://wiki.cardinalhealth.net/GCP_Cloud_Storage/PublicBuckets#Requesting_an_Exception

Upload the zip.
Once uploaded, click into the file.
Edit Permissions.
Choose Entity=Public, Name=allUsers, Access=Reader, Save.
Right click the public url, and save it, like:
https://storage.googleapis.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.0.zip



############################
Goal is to set up a generic new Lite image (not NOOBS),
and only expand to 1 GB (do this in VM?)
Connect pi to network.
curl -k slicer.cardinalhealth.net/build
That kicks off an ssh to do initial monitor install, and allows for full compliance push.
Then, shrink the file system, and on next boot re-expand
(https://github.com/qrti/shrink)
(https://blog.febo.com/?p=283)
(https://raspberrypi.stackexchange.com/questions/29947/reverse-the-expand-root-fs)

Do a clean shutdown.
Make an iso (or equivalent) of the card.
Burn to new card.
Let the new startup process expand the card to full size.
(expand:
http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php

sudo raspi-config --expand-rootfs
sudo reboot
)


############################
raspi-config --expand-rootfs




############################
############################
############################
By hand:

############################
- Change the password
passwd

############################
- create a new admin user
# https://www.raspberrypi.org/documentation/linux/usage/users.md
# https://raspberrypi.stackexchange.com/questions/54723/how-to-make-admin-account
sudo adduser pisuperuser
(vault lookup?)
super user

sudo usermod -a -G video pisuperuser
sudo adduser pisuperuser sudo

############################
- create a worker user
sudo adduser worker
(vault lookup?)
(worker)

sudo usermod -a -G video worker

#2024.05.09
sudo usermod -a -G audio worker


############################
- Change the autologin user to the worker
# https://www.raspberrypi.org/forums/viewtopic.php?t=201604

sudo vi /etc/lightdm/lightdm.conf

replace:
autologin-user=pi

with:
autologin-user=worker

############################
Chromium root CAH-ca:

cd /usr/local/share/ca-certificates
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt
sudo update-ca-certificates

sudo apt-get update
sudo apt -y --fix-broken install
sudo apt-get install -y libnss3-tools
certutil -d sql:$HOME/.pki/nssdb -L

certutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i CAH-Root-CA-PR1.crt

Restart the browser

# add for worker user
sudo chmod 644 /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt

#sudo mkdir -p /home/<USER>/.pki/nssdb
#sudo certutil -d /home/<USER>/.pki/nssdb -N --empty-password

sudo su worker
(password)
certutil -d sql:$HOME/.pki/nssdb -L
certutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
exit

############################
- Do not blank the screen: (applies after the next reboot)
# https://www.raspberrypi.org/documentation/configuration/
# https://www.raspberrypi.org/documentation/configuration/screensaver.md

sudo vi /boot/cmdline.txt

(add to the end of the existing line)
consoleblank=0

# https://raspberrypi.stackexchange.com/questions/752/how-do-i-prevent-the-screen-from-going-blank
sudo vi /etc/lightdm/lightdm.conf

change:
#xserver-command=X

to:
xserver-command=X -s 0 dpms


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Below here are planning, and have not been tested yet
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



############################
(step out the rest of the "step1" list)

############################
Auto start browser

############################
Disable the browser warning pop up about being ready to do an update

############################
VNC
# https://mike632t.wordpress.com/2013/04/18/remote-desktop-vnc/

VNC viewer:
https://www.realvnc.com/en/connect/download/viewer/

############################
- Remove pi user:
# https://www.raspberrypi.org/documentation/configuration/security.md

############################
in /etc/rc.local use:
update-ca-certificates

http://manpages.ubuntu.com/manpages/xenial/man8/update-ca-certificates.8.html

all *.crt in /usr/local/share/ca-certificates are trusted

cd /usr/local/share/ca-certificates

openssl x509 -inform PEM  -in entrust_l1k.cer -outform PEM -out entrust_l1k.crt

sudo openssl x509 -inform DER -in CAH-Dev-Root01.cer -out CAH-Dev-Root01.crt
sudo openssl x509 -inform DER -in CAH-DMZ-Root01.cer -out CAH-DMZ-Root01.crt
sudo openssl x509 -inform DER -in CAH-Issuing01.cer -out CAH-Issuing01.crt
sudo openssl x509 -inform DER -in CAH-Issuing02.cer -out CAH-Issuing02.crt
sudo openssl x509 -inform DER -in CAH-Issuing03.cer -out CAH-Issuing03.crt
sudo openssl x509 -inform DER -in CAH-Issuing-CA-PR1.cer -out CAH-Issuing-CA-PR1.crt
sudo openssl x509 -inform DER -in CAH-Issuing-CA-PR2.cer -out CAH-Issuing-CA-PR2.crt
sudo openssl x509 -inform DER -in CAH-Root01.cer -out CAH-Root01.crt
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt
sudo openssl x509 -inform DER -in CAIssuing3.cer -out CAIssuing3.crt
sudo openssl x509 -inform DER -in CAIssuing4.cer -out CAIssuing4.crt
sudo openssl x509 -inform DER -in 'Cardinal Health JSS Built-in Certificate Authority.cer' -out 'Cardinal Health JSS Built-in Certificate Authority.crt'
sudo openssl x509 -inform DER -in 'CAROOT 1.cer' -out 'CAROOT 1.crt'
sudo openssl x509 -inform DER -in CARoot.cer -out CARoot.crt
sudo openssl x509 -inform DER -in 'COMODO ECC Certification Authority.cer' -out 'COMODO ECC Certification Authority.crt'
sudo openssl x509 -inform DER -in 'COMODO RSA Certification Authority.cer' -out 'COMODO RSA Certification Authority.crt'
sudo openssl x509 -inform DER -in 'COMODO RSA Organization Validation Secure Server CA.cer' -out 'COMODO RSA Organization Validation Secure Server CA.crt'


# (did not help) sudo update-ca-certificates

or just the one:
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt


In Chromium, settings -> Advanced -> Privacy and security -> Manage certificates -> Authorities -> Import -> Other Locations -> Computer -> /usr/local/share/ca-certificates
-> CAH-Root-CA-PR1.crt -> Open

Then refresh the browser page with the url that had the issue.

OR

https://chromium.googlesource.com/chromium/src/+/master/docs/linux/cert_management.md
https://wiki.archlinux.org/index.php/Chromium

~/.pki/nssdb



/home/<USER>/.config/chromium/Default/Preferences

(binary) '/home/<USER>/.config/chromium/Default/Current Session'

Chromium command line options:
https://peter.sh/experiments/chromium-command-line-switches/
https://www.chromium.org/developers/how-tos/run-chromium-with-flags
https://www.google.com/search?client=safari&rls=en&q=chromium-browser+command+line+options&sa=X&ved=2ahUKEwj_79jW_P7vAhVLKVkFHQKzCskQ1QIwEnoECCEQAQ&biw=1509&bih=883
https://desertbot.io/blog/raspberry-pi-touchscreen-kiosk-setup



confirm with:
curl https://slicer.cardinalhealth.net



To test from ssh command line:
# https://cects.com/using-the-lynx-web-browser/

sudo apt-get install -y lynx

lynx https://slicer.cardinalhealth.net
lynx https://slicer.cardinalhealth.net/reports


# openssl x509 -in /etc/ssl/certs/ca-certificates.crt -text -noout
# restart:
# sudo rm /etc/ssl/certs/ca-certificates.crt
############################
CVE discussion, and kernel update:
https://www.raspberrypi.org/documentation/raspbian/updating.md

############################
# disable keys
############################
# https://stackoverflow.com/questions/50646587/making-xmodmap-changes-permanent-on-raspberry-pi

sudo vi /usr/share/X11/xkb/keycodes/evdev

(add // to front of certain lines)
*** start ***
//      <LALT> = 64;
//      <LCTL> = 37;
        <SPCE> = 65;
//      <RCTL> = 105;
//      <RALT> = 108;
        // Microsoft keyboard extra keys
//      <LWIN> = 133;
//      <RWIN> = 134;

        <ESC> = 9;
//      <FK01> = 67;
//      <FK02> = 68;
//      <FK03> = 69;
//      <FK04> = 70;
//      <FK05> = 71;
//      <FK06> = 72;
//      <FK07> = 73;
//      <FK08> = 74;
//      <FK09> = 75;
//      <FK10> = 76;
//      <FK11> = 95;
//      <FK12> = 96;

//      <VOL-> = 122;

*** end ***



############################
start up blanking and splash screen
############################
sudo vi /home/<USER>/.xinitrc

add tvservice:
(We could do this inside of /cardinal/browserstart, which is managed in pi_runner

while true; do
    tvservice -p
    /cardinal/browserstart
done;

############################
# Clean up the boot sequence
############################
https://scribles.net/customizing-boot-up-screen-on-raspberry-pi/

sudo vi /boot/cmdline.txt
make it console=tty3

add line:
splash quiet logo.nologo vt.global_cursor_default=0

Add custom boot logo:
https://raspberry-projects.com/pi/pi-operating-systems/raspbian/custom-boot-up-screen

sudo apt-get update
sudo apt-get install -y fbi

sudo vi /etc/systemd/system/splashscreen.service

-------------start
[Unit]
Description=Splash screen
DefaultDependencies=no
After=local-fs.target

[Service]
ExecStart=/usr/bin/fbi -d /dev/fb0 --noverbose -a /opt/splash.png
StandardInput=tty
StandardOutput=tty

[Install]
WantedBy=sysinit.target
-------------end

sudo systemctl enable splashscreen

############################
############################
############################
############################
Tests to run on a new image:

- Power button
PASS    - press power button, make sure it does not shut off
PASS    - long press the power button, make sure it does not shut off/reboot

- Profiles/settings (Pass/Fail on Pi4/Pi5)
F4P5    - set to an mp4 profile, make sure video plays with sound
            - sound issues on Pi4:
                https://www.omglinux.com/raspberry-pi-os-bookworm/

PASS    - set to GIF profile, make sure it works
        - Set bluetooth enabled, connect to scanner, see that it works
F4P5    - autodetect a 720p screen (really 1360x768 on the walk around test device)
FAIL    - set to 720p

- connectivity
        - Connect to non-NAC ethernet and see that check-in happens
P4?5    - Connect to iot WiFi and see that check-in happens
?4P5    - try ssh from laptop to pi, expect failure
?4P5    - try ssh from slicer to pi, expect success

?4F5    - wifi reporting from scan data shows correct ssid

- Bluetooth
FAIL    - connect scanner, scan the test barcode


############################
2024.04.22
Bookwork changes:

https://raspberrypi.stackexchange.com/questions/144876/forcing-hdmi-output-on-bookworm

https://www.raspberrypi.com/documentation/computers/config_txt.html#hdmi-mode

The Raspberry Pi 5 has H.265 (HEVC) hardware decoding. This decoding is enabled by default, so a hardware codec licence key is not needed.


############################
2024.05.09
Bookworm no HDMI audio on a pi4

raspinfo
https://github.com/raspberrypi/bookworm-feedback/issues/233

https://dietpi.com/forum/t/raspberry-pi-5-bookworm-aplay-l-no-soundcards-found/19760
cat /proc/asound/cards

https://www.raspberrypi.com/documentation/computers/configuration.html
Audio config
Use this option to switch between the PulseAudio and PipeWire audio backends. The PipeWire backend was introduced in Bookworm. PulseAudio was used in older versions of Rasperry Pi OS.

not yet answered:
https://forums.raspberrypi.com/viewtopic.php?t=365650

sudo apt-get install inxi -y
inxi -ACSxxz

---------------
"hdmi_drive=2" gets the sound devices to show up for "aplay -l"
sudo cat /boot/config.txt
dtparam=audio=on
disable_overscan=1
hdmi_force_hotplug=1
hdmi_drive=2

[pi4]
dtoverlay=vc4-fkms-v3d
max_framebuffers=2

---------------
https://askubuntu.com/questions/1129013/speakers-not-working-sunrise-point-lp-hd-audio

sudo apt-get install -y pavucontrol
pavucontrol

make sure auto-mute disabled

---------------
---------------
---------------


############################
############################
############################
############################
############################
















# end of file
