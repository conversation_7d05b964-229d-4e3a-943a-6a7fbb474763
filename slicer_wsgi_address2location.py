# A address2location for slicer page services

service = "address2location"
version = service + '.0.2'

_ = """
This file gets loaded to:
/var/www/html/address2location.py

using:
sudo vi /var/www/html/address2location.py

Test:
on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/
python -m unittest slicer_wsgi_address2location


It also requires:

sudo vi /etc/httpd/conf.d/python-address2location.conf
----- start copy -----
WSGIScriptAlias /address2location /var/www/html/address2location.py
----- end copy -----

sudo chown apache:apache /var/www/html/address2location.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/address2location-runner
sudo chmod +x /var/www/html/address2location-runner

# ===== begin: start file
#!/usr/bin/env python
import address2location
address2location.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/address2location-runner.service
sudo systemctl daemon-reload
sudo systemctl stop address2location-runner.service
sudo systemctl start address2location-runner.service
sudo systemctl enable address2location-runner.service

systemctl status address2location-runner.service

sudo systemctl restart address2location-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep address2location-runner

OR

tail -f /var/log/syslog | fgrep address2location-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/address2location-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import address2location; print(address2location.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/address2location

https://slicer.cardinalhealth.net/address2location?siteid=PR005

https://slicer.cardinalhealth.net/address2location?serial=100000002a5da842

https://slicer.cardinalhealth.net/address2location?monitorNot=M.1.2


"""

import copy
import traceback
import json
import os
import sys
import time
import unittest
from tempfile import TemporaryFile

try:  # for unittest to work
    import cgi
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

service_config = {}

startup_exceptions = ''

try:
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ------------------------------------------
# From the original implementation: Start
# ------------------------------------------

s_allow_legacy_results = False

locations = {}

if s_allow_legacy_results:
    locations['(all)'] = {}
    locations['(all)']['name'] = ['Place holder for permissions for all sites']
    locations['(all)']['subnets'] = []

    locations['?????'] = {}
    locations['?????']['name'] = ['Place holder for missing sites']
    locations['?????']['subnets'] = []

    # ? Daves house
    siteID = 'VPN1'
    sitename = 'VPN at David Fergusons house at least, and potentially other VPN connections.'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = ['10.226.171']

    # Troy McDaniel test
    # 10.226.106.95
    siteID = 'AZ032'
    sitename = 'Chandler AZ'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = ['10.226.106']

    siteID = 'CA182'
    sitename = 'Ontario CA'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = ['10.227.220']

    # From DHCP comments
    locations['MEX??'] = {}
    locations['MEX??']['subnets'] = ['10.234.3', '10.234.7']

    # From DHCP comments
    locations['IDF??'] = {}
    locations['IDF??']['subnets'] = ['10.234.83']

    _ = """
    Template:

    siteID = ''
    sitename = ''
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('')
    """

    siteID = 'MS001'
    sitename = 'MS001 Madison, MS'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.161')
    locations[siteID]['subnets'].append('10.209.162')
    locations[siteID]['subnets'].append('10.209.163')
    locations[siteID]['subnets'].append('10.209.164')
    locations[siteID]['subnets'].append('10.209.165')
    locations[siteID]['subnets'].append('10.209.166')
    locations[siteID]['subnets'].append('10.209.167')

    siteID = 'TX001'
    sitename = 'STD1 Stafford, TX'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.9')
    locations[siteID]['subnets'].append('10.209.10')
    locations[siteID]['subnets'].append('10.209.11')
    locations[siteID]['subnets'].append('10.209.12')
    locations[siteID]['subnets'].append('10.209.13')
    locations[siteID]['subnets'].append('10.209.14')
    locations[siteID]['subnets'].append('10.209.15')

    siteID = 'DEN2'
    sitename = 'DEN2 Denver, CO'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.73')
    locations[siteID]['subnets'].append('10.209.74')
    locations[siteID]['subnets'].append('10.209.75')
    locations[siteID]['subnets'].append('10.209.76')
    locations[siteID]['subnets'].append('10.209.77')
    locations[siteID]['subnets'].append('10.209.78')
    locations[siteID]['subnets'].append('10.209.79')

    siteID = 'TX010'
    sitename = 'X010 Roanoke, TX'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.129')
    locations[siteID]['subnets'].append('10.209.130')
    locations[siteID]['subnets'].append('10.209.131')
    locations[siteID]['subnets'].append('10.209.132')
    locations[siteID]['subnets'].append('10.209.133')
    locations[siteID]['subnets'].append('10.209.134')
    locations[siteID]['subnets'].append('10.209.135')

    siteID = 'MO001'
    sitename = 'MO001 Kansas City, MO'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.169')
    locations[siteID]['subnets'].append('10.209.170')
    locations[siteID]['subnets'].append('10.209.171')
    locations[siteID]['subnets'].append('10.209.172')
    locations[siteID]['subnets'].append('10.209.173')
    locations[siteID]['subnets'].append('10.209.174')
    locations[siteID]['subnets'].append('10.209.175')

    siteID = 'SDS2'
    sitename = 'SDS2 Swedesboro, NJ NSDS2L3MDF1A01'
    locations[siteID] = {}
    locations[siteID]['name'] = sitename
    locations[siteID]['subnets'] = []
    locations[siteID]['subnets'].append('10.209.233')
    locations[siteID]['subnets'].append('10.209.234')
    locations[siteID]['subnets'].append('10.209.235')
    locations[siteID]['subnets'].append('10.209.236')
    locations[siteID]['subnets'].append('10.209.237')
    locations[siteID]['subnets'].append('10.209.238')
    locations[siteID]['subnets'].append('10.209.239')

    locations['TYS2'] = {}
    locations['TYS2']['name'] = 'TYS2 Knoxville, TN NTYS2L3MDF1A01'
    locations['TYS2']['subnets'] = []
    locations['TYS2']['subnets'].append('10.209.177')
    locations['TYS2']['subnets'].append('10.209.178')
    locations['TYS2']['subnets'].append('10.209.179')
    locations['TYS2']['subnets'].append('10.209.180')

    locations['MEX03'] = {}
    locations['MEX03']['name'] = 'Juarez'
    locations['MEX03']['subnets'] = []
    locations['MEX03']['subnets'].append('10.234.8')
    locations['MEX03']['subnets'].append('10.234.9')
    locations['MEX03']['subnets'].append('10.234.10')
    locations['MEX03']['subnets'].append('10.234.11')
    locations['MEX03']['subnets'].append('10.234.12')
    locations['MEX03']['subnets'].append('10.234.13')
    locations['MEX03']['subnets'].append('10.234.14')
    locations['MEX03']['subnets'].append('10.234.15')
    locations['MEX03']['subnets'].append('10.234.16')
    locations['MEX03']['subnets'].append('10.234.17')
    locations['MEX03']['subnets'].append('10.234.87')
    locations['MEX03']['subnets'].append('10.234.19')
    locations['MEX03']['subnets'].append('10.234.20')
    locations['MEX03']['subnets'].append('10.234.21')
    locations['MEX03']['subnets'].append('10.234.22')
    locations['MEX03']['subnets'].append('10.234.23')

    locations['MEX04'] = {}
    locations['MEX04']['name'] = 'Cuauhtemoc'
    locations['MEX04']['subnets'] = []
    locations['MEX04']['subnets'].append('10.234.0')
    locations['MEX04']['subnets'].append('10.234.1')
    locations['MEX04']['subnets'].append('10.234.2')
    locations['MEX04']['subnets'].append('10.234.3')
    locations['MEX04']['subnets'].append('10.193.174')
    locations['MEX04']['subnets'].append('10.234.4')
    locations['MEX04']['subnets'].append('10.234.5')
    locations['MEX04']['subnets'].append('10.234.6')
    locations['MEX04']['subnets'].append('10.234.7')
    locations['MEX04']['subnets'].append('10.234.208')
    locations['MEX04']['subnets'].append('10.234.209')
    locations['MEX04']['subnets'].append('10.234.210')
    locations['MEX04']['subnets'].append('10.234.211')
    locations['MEX04']['subnets'].append('10.234.212')
    locations['MEX04']['subnets'].append('10.234.213')
    locations['MEX04']['subnets'].append('10.234.214')
    locations['MEX04']['subnets'].append('10.234.215')
    locations['MEX04']['subnets'].append('10.234.216')
    locations['MEX04']['subnets'].append('10.234.217')
    locations['MEX04']['subnets'].append('10.234.218')
    locations['MEX04']['subnets'].append('10.234.219')
    locations['MEX04']['subnets'].append('10.234.220')
    locations['MEX04']['subnets'].append('10.234.221')
    locations['MEX04']['subnets'].append('10.234.222')
    locations['MEX04']['subnets'].append('10.234.223')

    locations['MEX05'] = {}
    locations['MEX05']['name'] = 'Delicias'
    locations['MEX05']['subnets'] = ['10.193.206', '10.193.80', '10.193.81', '10.193.82', '10.193.83', '10.193.84',
                                     '10.193.85', '10.193.86', '10.193.87']
    locations['MEX05']['subnets'].append('10.234.80')
    locations['MEX05']['subnets'].append('10.234.81')
    locations['MEX05']['subnets'].append('10.234.82')
    locations['MEX05']['subnets'].append('10.234.83')
    locations['MEX05']['subnets'].append('10.193.206')
    locations['MEX05']['subnets'].append('10.234.84')
    locations['MEX05']['subnets'].append('10.234.85')
    locations['MEX05']['subnets'].append('10.234.86')
    locations['MEX05']['subnets'].append('10.234.87')
    locations['MEX05']['subnets'].append('10.234.233')
    locations['MEX05']['subnets'].append('10.234.232')
    locations['MEX05']['subnets'].append('10.234.228')

    # Paco: MEX005 10.234.82. & 10.234.83

    locations['MEX09'] = {}
    locations['MEX09']['name'] = 'Juarez Salvacar II'
    locations['MEX09']['subnets'] = []
    locations['MEX09']['subnets'].append('10.17.204')
    locations['MEX09']['subnets'].append('10.17.242')
    locations['MEX09']['subnets'].append('10.234.32')
    locations['MEX09']['subnets'].append('10.234.33')
    locations['MEX09']['subnets'].append('10.234.34')
    locations['MEX09']['subnets'].append('10.234.35')
    locations['MEX09']['subnets'].append('10.234.36')
    locations['MEX09']['subnets'].append('10.234.37')
    locations['MEX09']['subnets'].append('10.234.38')
    locations['MEX09']['subnets'].append('10.234.39')
    locations['MEX09']['subnets'].append('10.234.88')
    locations['MEX09']['subnets'].append('10.234.89')
    locations['MEX09']['subnets'].append('10.234.90')
    locations['MEX09']['subnets'].append('10.234.91')
    locations['MEX09']['subnets'].append('10.234.92')
    locations['MEX09']['subnets'].append('10.234.93')
    locations['MEX09']['subnets'].append('10.234.94')
    locations['MEX09']['subnets'].append('10.234.95')
    locations['MEX09']['subnets'].append('10.234.104')
    locations['MEX09']['subnets'].append('10.234.105')
    locations['MEX09']['subnets'].append('10.234.106')
    locations['MEX09']['subnets'].append('10.234.108')
    locations['MEX09']['subnets'].append('10.234.109')
    locations['MEX09']['subnets'].append('10.234.110')
    locations['MEX09']['subnets'].append('10.234.111')

    _ = """
    [2:32 PM] Sanchez, Mario (EIT)
        MEX09 - Juarez,
    ***********/24
    10.234.35.0/24
    10.234.92.0/24
    10.234.91.0/24
    10.234.36.0/24
    10.234.37.0/24

    """

    locations['MEX15'] = {}
    locations['MEX15']['name'] = 'Tijuana'
    locations['MEX15']['subnets'] = []
    locations['MEX15']['subnets'].append('10.131.112')
    locations['MEX15']['subnets'].append('10.131.113')
    locations['MEX15']['subnets'].append('10.131.114')
    locations['MEX15']['subnets'].append('10.131.116')
    locations['MEX15']['subnets'].append('10.131.117')
    locations['MEX15']['subnets'].append('10.131.118')
    locations['MEX15']['subnets'].append('10.131.119')
    locations['MEX15']['subnets'].append('10.131.120')
    locations['MEX15']['subnets'].append('10.131.121')

    locations['MEX18'] = {}
    locations['MEX18']['name'] = 'Ceva'
    locations['MEX18']['subnets'] = []
    locations['MEX18']['subnets'].append('10.234.169')
    locations['MEX18']['subnets'].append('10.234.170')
    locations['MEX18']['subnets'].append('10.234.171')
    locations['MEX18']['subnets'].append('10.234.172')
    locations['MEX18']['subnets'].append('10.234.173')
    locations['MEX18']['subnets'].append('10.234.174')

    # Dublin South
    locations['OH085'] = {}
    locations['OH085']['subnets'] = ['10.206.112', '10.206.118', '10.206.116']

    locations['DOM02'] = {}
    locations['DOM02']['name'] = ''
    locations['DOM02']['subnets'] = ['10.193.192', '10.193.64', '10.193.65', '10.193.66', '10.193.67', '10.193.68',
                                     '10.193.69', '10.193.70', '10.193.71']
    locations['DOM02']['subnets'].append('10.234.28')
    locations['DOM02']['subnets'].append('10.234.64')
    locations['DOM02']['subnets'].append('10.234.65')
    locations['DOM02']['subnets'].append('10.234.66')
    locations['DOM02']['subnets'].append('10.234.67')
    locations['DOM02']['subnets'].append('10.234.68')
    locations['DOM02']['subnets'].append('10.234.69')
    locations['DOM02']['subnets'].append('10.234.70')
    locations['DOM02']['subnets'].append('10.234.71')

    locations['PR005'] = {}
    locations['PR005']['name'] = 'Guanynabo Bldg 10'
    locations['PR005']['subnets'] = []
    locations['PR005']['subnets'].append('10.216.193')
    locations['PR005']['subnets'].append('10.216.194')
    locations['PR005']['subnets'].append('10.216.195')
    locations['PR005']['subnets'].append('10.216.199')
    locations['PR005']['subnets'].append('10.216.201')
    locations['PR005']['subnets'].append('10.216.203')
    locations['PR005']['subnets'].append('10.216.204')
    locations['PR005']['subnets'].append('10.216.208')
    locations['PR005']['subnets'].append('10.216.209')
    locations['PR005']['subnets'].append('10.216.210')
    locations['PR005']['subnets'].append('10.216.211')
    locations['PR005']['subnets'].append('10.216.212')
    locations['PR005']['subnets'].append('10.216.213')
    locations['PR005']['subnets'].append('10.216.215')
    locations['PR005']['subnets'].append('10.216.243')
    locations['PR005']['subnets'].append('10.216.246')
    locations['PR005']['subnets'].append('10.216.247')
    locations['PR005']['subnets'].append('10.216.248')

    locations['PR006'] = {}
    locations['PR006']['name'] = ''
    locations['PR006']['subnets'] = []
    locations['PR006']['subnets'].append('10.234.73')
    locations['PR006']['subnets'].append('10.234.74')
    locations['PR006']['subnets'].append('10.234.75')
    locations['PR006']['subnets'].append('10.234.76')
    locations['PR006']['subnets'].append('10.234.77')
    locations['PR006']['subnets'].append('10.234.78')
    locations['PR006']['subnets'].append('10.234.79')

    locations['PR010'] = {}
    locations['PR010']['name'] = 'Guanynabo Bldg 3'
    locations['PR010']['subnets'] = ['10.216.230']
    locations['PR010']['subnets'] = ['10.216.236']
    locations['PR010']['subnets'] = ['10.216.237']

    locations['PR010']['subnets'] = ['10.216.240']
    locations['PR010']['subnets'] = ['10.216.241']
    locations['PR010']['subnets'] = ['10.216.242']
    locations['PR010']['subnets'] = ['10.216.252']

    locations['DOM02'] = {}
    locations['DOM02']['subnets'] = ['10.193.192', '10.234.64', '10.234.65', '10.234.66', '10.234.67', '10.234.68',
                                     '10.234.69', '10.234.70', '10.234.71']

    site = 'CostaRica'
    locations[site] = {}
    locations[site]['name'] = 'CostaRica'
    locations[site]['subnets'] = []
    locations[site]['subnets'].append('10.131.96')
    locations[site]['subnets'].append('10.131.97')
    locations[site]['subnets'].append('10.131.98')
    locations[site]['subnets'].append('10.131.100')
    locations[site]['subnets'].append('10.131.101')
    locations[site]['subnets'].append('10.131.102')
    locations[site]['subnets'].append('10.131.104')
    locations[site]['subnets'].append('10.131.105')
    locations[site]['subnets'].append('10.131.110')


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Helper functions
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ----------------------------
def extract_duplicate_subnets(data_store_content):
    # ----------------------------
    found_subnets = {}
    found_duplicates = {}

    for item in data_store_content:
        try:
            if 'address2location_site_' in item:
                subnets = data_store_content[item].split(',')
                for subnet in subnets:
                    if subnet:
                        site_id = item.split('_')[2]
                        if subnet in found_subnets:
                            if not subnet in found_duplicates:
                                found_duplicates[subnet] = [found_subnets[subnet]]
                            found_duplicates[subnet].append(site_id)
                        else:
                            found_subnets[subnet] = site_id  # first found site_id
        except:
            pass

    return found_duplicates


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_all_locations_stored(data_store_content):
    # ----------------------------
    response = []

    response.append('(all)')

    for item in data_store_content:
        try:
            if 'address2location_site_' in item:
                site = item.split('address2location_site_')[1].split('_')[0]
                if not site in response:
                    response.append(site)
        except:
            pass

    return response


# ----------------------------
def site_name_stored(data_store_content, location):
    # ----------------------------
    try:
        response = data_store_content['address2location_site_' + location + '_name']
    except:
        response = ''
    return response


# ----------------------------
def subnets_stored(data_store_content, location):
    # ----------------------------
    try:
        response = data_store_content['address2location_site_' + location + '_subnets']
    except:
        response = ''
    return response


# ----------------------------
def location_stored(data_store_content, address, returnName=False):
    # ----------------------------
    response = '(unassigned)'
    name = ''

    subnet_of_addr = '.'.join(address.split('.')[0:3])

    for item in data_store_content:
        try:
            if 'address2location_site_' in item:
                site = item.split('address2location_site_')[1].split('_')[0]

                if item == 'address2location_site_' + site + '_subnets':
                    subnets = data_store_content[item]
                    for subnet in subnets.split(','):
                        if subnet:
                            if subnet == subnet_of_addr:  # address[:len(subnet)]:
                                response = site
                                if 'address2location_site_' + site + '_name' in data_store_content:
                                    name = data_store_content['address2location_site_' + site + '_name']
        except:
            pass

    if returnName:
        return response, name

    else:
        return response


# --- old ---

# ----------------------------
def get_all_locations():  # use get_all_locations_stored instead, and pass in the data_store_content
    # ----------------------------
    response = []

    try:
        import datastore
        response = get_all_locations_stored(datastore.all_datastore())

    except:
        print(traceback.format_exc())
        for item in locations:
            if not item in response:
                response.append(item)

    return response


# ----------------------------
def site_name(location):
    # ----------------------------
    name_found = ''

    try:
        import datastore
        name_found = site_name_stored(datastore.all_datastore(), location)
    except:
        print(traceback.format_exc())
        #        try:
        #            name_found = locations[location]['name']
        #        except:
        #            pass
        pass

    return name_found


# ----------------------------
def location(address, returnName=False):
    # ----------------------------
    location_found = '(unassigned)'

    if s_allow_legacy_results:
        subnet_of_addr = '.'.join(address.split('.')[0:3])

        name_found = ''
        for location in locations:
            for subnet in locations[location]['subnets']:
                if subnet == subnet_of_addr:
                    location_found = location
                    try:
                        name_found = locations[location]['name']
                    except:
                        pass

        if returnName:
            return location_found, name_found
        else:
            return location_found

    try:
        import datastore
        data_store_content = datastore.all_datastore(sub_string='address2location_site_')

        if returnName:
            return location_stored(data_store_content, address, returnName=returnName)
        else:
            return location_stored(data_store_content, address, returnName=returnName)
    except:
        print(traceback.format_exc())
        subnet_of_addr = '.'.join(address.split('.')[0:3])

        name_found = ''
        for location in locations:
            for subnet in locations[location]['subnets']:
                if subnet == subnet_of_addr:
                    location_found = location
                    try:
                        name_found = locations[location]['name']
                    except:
                        pass

    if returnName:
        return location_found, name_found
    else:
        return location_found


# ----------------------------
def deprecated_subnets(location):
    # ----------------------------
    result = []

    if location:
        if location in locations:
            result = locations[location]['subnets']
    else:
        for location in locations:
            for subnet in locations[location]['subnets']:
                result.append(subnet)

    return result


# ------------------------------------------
# From the original implementation: End
# ------------------------------------------

# ====================================
def sort_as_ip_address(address_list):
    # ====================================
    d = {}
    for address in address_list:
        key = ''
        for item in address.split('.'):
            try:
                key += "%03d" % int(item)
            except:
                key += '000'
        # fill out to full length
        while len(key) < 12:
            key += '000'
        d[key] = address

    response = []
    for key in sorted(d):
        response.append(d[key])

    return response


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(address2location status)'

    status = os.system('systemctl is-active --quiet address2location-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "address2location-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ):
    # ====================================
    import login

    body = ''

    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    the_who = login.get_current_user(environ)

    # body += json.dumps(d)

    # use cgi module to read data
    #    body_of_form = read(environ)
    #    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

    if 'address2location_siteid_create' in d:
        try:
            the_site_to_create = d['address2location_siteid_create'][0].replace(' ', '')  # no spaces

            if the_site_to_create:
                import datastore
                current_sites = get_all_locations_stored(datastore.all_datastore())
                # body += '<br>' + json.dumps(current_sites)

                if the_site_to_create in current_sites:
                    body += '<br> site ' + the_site_to_create + ' already exists, and was not created'
                else:
                    body += '<br> site ' + the_site_to_create + ' is new, and was created (see the table below)'
                    datastore_name_key = 'address2location_site_' + the_site_to_create + '_name'
                    datastore.set_value(datastore_name_key, '(new)', who=the_who)
        except:
            body += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    try:
        if 'the_selection' in d:
            body += '<br>' + json.dumps(d)
            if 'name_text_set' == str(d['the_selection'][0]):
                site_id = d['serial'][0]
                if 'address2location_site_update_name' in d:
                    name_to_set = d['address2location_site_update_name'][0]
                    body += '<br>' + 'Name for ' + site_id + ' set to: ' + name_to_set
                    datastore_name_key = 'address2location_site_' + site_id + '_name'
                    import datastore
                    datastore.set_value(datastore_name_key, name_to_set, who=the_who)
            if 'subnets_text_set' == str(d['the_selection'][0]):
                site_id = d['serial'][0]
                if 'address2location_site_update_subnets' in d:
                    try:
                        subnets_content = d['address2location_site_update_subnets'][0].replace('\n', '').replace('\r',
                                                                                                                 '')
                        body += '<br>subnets_content: ' + subnets_content
                        d_to_use = json.loads(subnets_content)
                        subnets_to_set = str(','.join(d_to_use))
                        body += '<br>subnet content = ' + subnets_to_set

                        datastore_subnets_key = 'address2location_site_' + site_id + '_subnets'
                        body += "<br>setting key: " + datastore_subnets_key + ", to " + subnets_to_set

                        import datastore
                        datastore.set_value(datastore_subnets_key, subnets_to_set, who=the_who)

                    except:
                        body += '<br>Exception reading subnets content'
                        body += '<br>' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))


    except:
        body += '<br>' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    #    return body

    return make_body_GET(environ, body)


# ====================================
def make_body_GET(environ, extra_content=''):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    import permissions

    l_permission_create = False
    l_permission_update = False

    # user_permission_david.ferguson_address2location_read
    if permissions.permission_prefix_allowed(environ, 'address2location_create'):
        l_permission_create = True
    if permissions.permission_prefix_allowed(environ, 'address2location_update'):
        l_permission_update = True

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        import datastore
        data_store_content = datastore.all_datastore()
        sites = get_all_locations_stored(data_store_content)
        duplicate_subnets = extract_duplicate_subnets(data_store_content)

        if extra_content:
            body += '<center>'
            body += extra_content
            body += '</center>'
            body += '<br>'

        body += '<center>'
        if duplicate_subnets:
            body += '<br><br>' + '<B>!!!! ' + 'Duplicate subnets found:' + str(
                sorted(list(duplicate_subnets.keys()))) + ' !!!</B>' + '<br><br>'

            body += '<center>'
            body += 'Subnet multi-listed report'
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            body += '<B>Subnet</B>'
            body += '</td>'
            body += '<td>'
            body += '<B>Usage</B>'
            body += '</td>'
            body += '</tr>'

            for subnet in sorted(duplicate_subnets.keys()):
                body += '<tr>'
                body += '<td>'
                body += subnet
                body += '</td>'
                body += '<td>'
                body += str(sorted(duplicate_subnets[subnet]))
                body += '</td>'
                body += '</tr>'

            body += '</table>'
            body += '</center>'
            body += '<br><br>'

        else:
            body += '<br><br>' + 'No duplicate subnets found.' + '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        if l_permission_create:
            body += '<tr>'
            body += '<td>'
            body += '<B>Create</B>'
            body += '</td>'

            body += '<td>'
            body += '<form method="post" action="">'
            body += 'New Site ID: '
            body += """<input type="text" size=25 name="address2location_siteid_create" value=\"""" + '' + """\">"""
            body += '</td>'
            body += '<td>'
            body += '<input type="submit" value="Submit">'
            body += '</form>'
            body += '</td>'
            body += '</tr>'

        body += '</table>'
        body += '</center>'

        body += '<br><br>'

        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += '<B>Site</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>Name/description</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>Subnets</B>'
        body += '</td>'
        body += '</tr>'

        for site in sorted(sites):
            body += '<tr>'
            body += '<td>'
            body += site
            body += '</td>'

            body += '<td>'

            if l_permission_update:
                body += '<form method="post" action="">'
                body += '<select name="the_selection" id="the_selection" hidden>'
                body += '<option value="name_text_set" selected>' + 'name_text' + '</option>'
                body += '</select>'
                body += '<select name="serial" id="serial" hidden>'
                body += '<option value="' + site + '" selected>' + site + '</option>'
                body += '</select>'

                body += """<input type="text" size=50 name="address2location_site_update_name" value=\"""" + site_name_stored(
                    data_store_content, site) + """\">"""

                body += '<input type="submit" value="Submit">'
                body += '</form>'
            else:
                body += site_name_stored(data_store_content, site)

            body += '</td>'

            body += '<td>'
            subnets = subnets_stored(data_store_content, site)

            sorted_subnets = sort_as_ip_address(subnets.split(','))
            #            sorted_subnets = subnets.split(',')

            if l_permission_update:
                content_dump = json.dumps(sorted_subnets, indent=4, separators=(',', ':'))

                body += '<form method="post" action="">'
                body += '<select name="the_selection" id="the_selection" hidden>'
                body += '<option value="subnets_text_set" selected>' + 'name_text' + '</option>'
                body += '</select>'
                body += '<select name="serial" id="serial" hidden>'
                body += '<option value="' + site + '" selected>' + site + '</option>'
                body += '</select>'

                body += """<textarea rows=""" + str(
                    len(sorted_subnets) + 2) + """ cols=50 name="address2location_site_update_subnets">""" + content_dump + "</textarea>"

                body += '<input type="submit" value="Submit">'
                body += '</form>'

            else:
                for subnet in sorted_subnets:
                    body += subnet + '<br>'
            body += '</td>'

            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<br><br>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    import permissions

    if permissions.permission_prefix_allowed(environ, service + '_') or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'

    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
def test_afunction_throws_exception(self):
    """
    (fill in here)
    """
    afunction = None
    expected_exception = None
    self.assertRaises(expected_exception, afunction)


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_datastore_content(self):
        """
        (fill in here)
        """
        data_store_content = {}
        data_store_content['address2location_site_MEX09_subnets'] = '10.234.32,10.234.34,10.234.35,10.234.91,10.234.92'
        self.assertEqual('MEX09' in get_all_locations_stored(data_store_content), True)
        self.assertEqual('PR005' in get_all_locations_stored(data_store_content), False)

        data_store_content = {}
        data_store_content['address2location_site_MEX09_name'] = 'Juarez Salvacar II'
        self.assertEqual('MEX09' in get_all_locations_stored(data_store_content), True)
        self.assertEqual('PR005' in get_all_locations_stored(data_store_content), False)

        data_store_content = {}
        data_store_content['address2location_site_MEX09_name'] = 'Juarez Salvacar II'
        data_store_content['address2location_site_MEX09_subnets'] = '10.234.32,10.234.34,10.234.35,10.234.91,10.234.92'
        self.assertEqual('PR005' in get_all_locations_stored(data_store_content), False)
        self.assertEqual('MEX09' in get_all_locations_stored(data_store_content), True)
        self.assertEqual(location_stored(data_store_content, '***********'), 'MEX09')
        self.assertEqual(location_stored(data_store_content, ''), '(unassigned)')

        self.assertEqual(location_stored(data_store_content, '***********', True), ('MEX09', 'Juarez Salvacar II'))

    def test_name(self):
        data_store_content = {}
        data_store_content['address2location_site_MEX09_name'] = 'Juarez Salvacar II'
        data_store_content['address2location_site_MEX09_subnets'] = '10.234.32,10.234.34,10.234.35,10.234.91,10.234.92'
        self.assertEqual(site_name_stored(data_store_content, 'MEX09'), 'Juarez Salvacar II')
        self.assertEqual(site_name_stored(data_store_content, 'PR005'), '')

    def test_address_sort(self):
        address_list = ['10.2.100', '10.10.100']  # keep these in the expected order
        self.assertEqual(sort_as_ip_address(address_list), address_list)
        address_list = ['10.2.100', '***********']  # keep these in the expected order
        self.assertEqual(sort_as_ip_address(address_list), address_list)

        self.assertEqual(sort_as_ip_address(['***********', '10.2.100']), ['10.2.100', '***********'])

        # test empty
        self.assertEqual(sort_as_ip_address(['']), [''])

    def test_extract_duplicate_subnets(self):
        data_store_content = {}
        expected = {}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content['address2location_site_MEX09_subnets'] = '10.234.32,10.234.34,10.234.35,10.234.91,10.234.92'
        expected = {}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content['address2location_site_PR005_subnets'] = '10.234.32'
        expected = {'10.234.32': ['MEX09', 'PR005']}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content['address2location_site_PR005_subnets'] = '10.234.34,10.234.32'
        expected = {'10.234.32': ['MEX09', 'PR005'], '10.234.34': ['MEX09', 'PR005']}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content['address2location_site_PR010_subnets'] = ''
        expected = {'10.234.32': ['MEX09', 'PR005'], '10.234.34': ['MEX09', 'PR005']}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content['address2location_site_PR015_subnets'] = ''
        expected = {'10.234.32': ['MEX09', 'PR005'], '10.234.34': ['MEX09', 'PR005']}
        actual = extract_duplicate_subnets(data_store_content)
        self.assertEqual(expected, actual)

# End of source file
