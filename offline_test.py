# offline_test.py

universe = 'cah'

_ = """
on DWF laptop:

---------------------------
once: (May not work on corp, so try on byod)
---------------------------
# ModuleNotFoundError: No module named 'numpy'
python3 -m pip install numpy
OR
brew install numpy

python3 -m pip install requests

---------------------------
Try building a virtual environment for testing, to be able to try modules:
---------------------------
https://www.studytonight.com/post/python-virtual-environment-setup-on-mac-osx-easiest-way

python3 -m venv slicer_tests
source slicer_tests/bin/activate
pip install --upgrade pip
python3 -m pip install numpy
python3 -m pip install numpy
(yes, second time was the charm)
python3 -m pip install pandas
python3 -m pip install openpyxl

cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/
python3 offline_test.py
Ctrl C
exit


---------------------------
each time:
---------------------------
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 offline_test.py


"""

_python_version_on_daves_mac = """
2023.08.17
python3 --version
Python 3.8.2

Steve mentioned that he will start "patching" python, on Macs.
    - python can have breaking changes when rolling a version:
        - will there be a way to roll back, and to know what was there, the know what to roll back to?


https://www.python.org/doc/versions/

3.8.2 released 24 Feb 2020

Virtual environment, built into python3

https://www.section.io/engineering-education/introduction-to-virtual-environments-and-dependency-managers/
https://docs.python.org/3/library/venv.html



"""

_notes = """

do_one_command('python3 -m unittest device')
('device.TestAllMethods.test_new_works: 0.000\n', '.\n----------------------------------------------------------------------\nRan 1 test in 0.000s\n\nOK\n')


('device.TestAllMethods.test_new_works: 0.000\n', 'F\n======================================================================\nFAIL: test_new_works (device.TestAllMethods)\n(fill in here)\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File "/Users/<USER>/Documents/GIT/cs_sp_pi_slicer/device.py", line 713, in test_new_works\n    self.assertEqual(False, True)\nAssertionError: False != True\n\n----------------------------------------------------------------------\nRan 1 test in 0.001s\n\nFAILED (failures=1)\n')

"""

_javascript_notes = """

NPM:
https://radixweb.com/blog/installing-npm-and-nodejs-on-windows-and-mac

JEST (test driven javascript):
https://superuser.com/questions/1394610/how-to-install-jest-on-macos-mojave



"""

import copy
import datetime
import os
import shlex
import stat
import subprocess
import time
import traceback

# https://www.lihaoyi.com/post/BuildyourownCommandLinewithANSIescapecodes.html
s_text_color_start_black = '\x1b[1;30m'
s_text_color_start_red = '\x1b[1;31m'
s_text_color_start_green = '\x1b[1;32m'
s_text_color_start_yellow = '\x1b[1;33m'
s_text_color_start_blue = '\x1b[1;34m'
s_text_color_start_magenta = '\x1b[1;35m'
s_text_color_start_cyan = '\x1b[1;36m'
s_text_color_start_white = '\x1b[1;37m'

s_text_color_end = '\x1b[0m'

_ = """

import sys
for i in range(0, 16):
    for j in range(0, 16):
        code = str(i * 16 + j)
        sys.stdout.write(u"\u001b[38;5;" + code + "m " + code.ljust(4))
    print (u"\u001b[0m")

"""
s_method_prefix_list = [
    # https://management.buffalo.edu/content/dam/mgt/Career-Resources/Documents/ActionVerbs.pdf

    # required to be exactly these names?
    'application',

    # FixMe: need refactor these to a new pattern
    'main',

    # Verbs that impact operation
    'restart',

    # verbs to define a runnable thread item
    'run',

    # verbs that create data from scratch (hard-coded data/formatting) (might take arguments to guide the build)
    'build',

    # verbs that work only on the given inputs (pure dependency injection)
    'calculate',
    'clean',
    'convert',
    'count',
    'determine',
    'extract',
    'is',
    'does',
    'make',
    'parse',
    'sort',
    'split',
    'test',  # only use for unit testing. If mainline code, use "is_"

    # https://langeek.co/en/grammar/course/1742/pick-vs-choose-vs-select
    'pick',  # any from those available
    'choose',  # based on some criteria
    'select',  # based on specific criteria

    # verbs that go to an outside data source
    'check',  # get a thing, and then run analysis to decide something
    'get',  # for volatile reads, and may reformat based on the data
    'increment',  # read, increment, write
    'load',  # for less likely to be volatile (like config file reads)
    'read',  # get the exact value from a thing

    # verbs that deliver to an outside data store
    'log',
    'save',
    'set',
    'write',

    # verbs that need classified
    'clean',
    'clear',
    'do',
    'find',
    'kill',
    'prune',
    'push',
    'remove',
    'trim',
    'update',

    # verbs we might want to pick a different verb for
    'call',

    # public calls that require careful full project update after refactoring
    'all',
    'any',
    'permission',
    'site',
    'status',

    # for testing... might want to eliminate these, and refactor
    'setUp',
    'tearDown',
]

# ====================================
def make_file_list_from_commits_data(commits_files_list):
    # ====================================
    return_value = {}

    for files_list in commits_files_list:
        for file in files_list:
            if not file in return_value:
                return_value[file] = 0
            return_value[file] += 1

    return return_value

# ====================================
def find_modified_on_master(result_parse_files_from_show_commit):
    # ====================================
    return_value = []

    for item_d in result_parse_files_from_show_commit:
        if not item_d['a'] in return_value:
            return_value.append(item_d['a'])
        if not item_d['b'] in return_value:
            return_value.append(item_d['b'])

    return return_value

# ====================================
def parse_files_from_show_commit(show_commit):
    # ====================================
    return_value = []

    for line in show_commit.split('\n'):
        if len(line) > 11:
            if 'diff --git ' == line[0:11]:
                a_and_b = line.replace('diff --git ', '')
                a = a_and_b.split(' ')[0].replace('a/','')
                b = a_and_b.split(' ')[1].replace('b/','')
                return_value.append({'a':a,'b':b})

    return return_value

# ====================================
def parse_commits_from_log(git_log_master):
    # ====================================
    return_value = {}

    last_commit = ''
    lines = git_log_master.split('\n')
    for line_index in range(0,len(lines)):
        line = lines[line_index]
        if len(line) > 7:
            if line[0:7] == 'commit ':
                last_commit = line.split(' ')[1]
                return_value[last_commit] = lines[line_index + 4].strip()

    return return_value

# ====================================
def file_syntax_issues(content):
    # ====================================
    return_value = ''

    line_number = 0
    ignore_line_count_down = 0
    for line_found in content.split('\n'):
        line_number += 1
        ignore_line_count_down -= 1

        if ignore_line_count_down == 0:
            # ignore it
            pass
        else:
            stripped_line = line_found.strip()
            if stripped_line:
                if stripped_line[0] == '#':
                    # this is a comment line
                    pass
                elif '===' in line_found:
                    # This is not a boolean test
                    pass
                else:
                    if ('if ' in line_found) or ('while ' in line_found):
                        # these are expected to have boolean tests in them
                        pass
                    else:
                        if ('==' in line_found) or ('!=' in line_found):
                            if ' = ' in line_found.split('==')[0].split('!=')[0]:
                                # this is ok, there is an assignment happening
                                pass
                            else:
                                return_value += 'line ' + str(
                                    line_number) + ': test without if: ' + line_found.strip() + '\n'

        if 'ignore_line_count_down_for_' + 'syntax_checking=' in line_found:
            ignore_line_count_down = int(line_found.split('=')[1])

        elif 'ignore_line_count_down_for_syntax_checking' in line_found:
            ignore_line_count_down = 1

    return return_value


# ====================================
def get_definition_from_line(df_line):
    # ====================================
    return_value = ''

    if df_line.strip()[0] == '#':
        # line is commented, count it as ok.
        return_value = ''
    else:
        splits = df_line.split('de' + 'f ')
        if len(splits) > 1:
            just_the_name = splits[1].split('(')[0]

            if just_the_name:
                return_value = just_the_name

    return return_value


# ====================================
def get_definition_prefix_from_line(df_line):
    # ====================================
    return_value = ''

    just_the_name = get_definition_from_line(df_line)

    if just_the_name:
        if just_the_name[0] == '_':
            # private
            return_value = just_the_name.split('_')[1]
        else:
            # public
            return_value = just_the_name.split('_')[0]

    return return_value


# ====================================
def check_if_method_conforms(df_line):
    # ====================================
    return_value = False

    prefix = get_definition_prefix_from_line(df_line)

    if prefix:
        if prefix in s_method_prefix_list:
            return_value = True
    else:
        return_value = True

    return return_value


# ====================================
def convert_seconds_to_human(time_in_seconds):
    # ====================================
    the_report = ''
    time_to_use = int(time_in_seconds)
    if time_to_use < 0:
        the_report += '-'
        time_to_use = abs(time_to_use)
    days = int(time_to_use / (3600 * 24))
    time_to_use -= days * 3600 * 24
    hours = int(time_to_use / 3600)
    time_to_use -= hours * 3600
    minutes = int(time_to_use / 60)
    time_to_use -= minutes * 60
    seconds = time_to_use % 60
    if days > 0:
        if the_report:
            the_report += ' '
        the_report += str(days) + 'd'
    if hours > 0:
        if the_report:
            the_report += ' '
        the_report += str(hours) + 'h'
    if minutes > 0:
        if the_report:
            the_report += ' '
        the_report += str(minutes) + 'm'
    if seconds > 0:
        if the_report:
            the_report += ' '
        the_report += str(seconds) + 's'
    return the_report


# ----------------------------
def get_seconds_diff_from_string_date_to_TS(string_date, TS):
    # ----------------------------
    try:
        datetime_one = get_string_date_to_datetime(string_date)

        if datetime_one:
            datetime_two = get_ts_to_datetime(TS)
            diff_time = datetime_two - datetime_one
            return diff_time.total_seconds()
        else:
            return None
    except:
        return None

# ----------------------------
def get_string_date_to_datetime(string_date):
    # ----------------------------
    # like 2020.08.16
    try:
        return datetime.datetime(int(string_date[0:4]), int(string_date[5:7]), int(string_date[8:10]))
    except:
        return None


# ----------------------------
def get_ts_to_datetime(TS):
    # ----------------------------
    return datetime.datetime(int(TS[0:4]), int(TS[4:6]), int(TS[6:8]), int(TS[8:10]), int(TS[10:12]), int(TS[12:14]),
                             int(TS[14:]))


# ----------------------------
def get_TS():
    # ----------------------------
    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    # 20210406201829131704
    return TS


# ----------------------------
def get_manual_test_most_recent_run_date(content):
    # ----------------------------
    return_value = ''

    for line in content.split('\n'):
        if 'manual_tests_completed = ' in line:
            new_value = line.split('manual_tests_completed = ')[1]
            if return_value:
                if new_value > return_value:
                    return_value = new_value
            else:
                return_value = new_value

    return return_value


# ----------------------------
def versioned_apps_not_captured(versioned_os_list, versions_found):
    # ----------------------------
    return_value = []

    for version_found in versions_found.keys():
        appname = version_found.replace('pi_', '').replace('.py', '')
        appname_splits = appname.split('_')
        if len(appname_splits) > 1:
            if universe == appname_splits[1]:
                appname = appname_splits[0]
            else:
                appname = ''

        if appname == 'template':
            appname = ''

        if appname:
            appversion = versions_found[version_found]
            app_for_this_version = 'pi_service_(appname)_pi_version_(appversion)'.replace('(appname)', appname).replace(
                '(appversion)', appversion)
            if not app_for_this_version in versioned_os_list:
                return_value.append(app_for_this_version)

    return return_value


# ----------------------------
def check_if_all_are_in_a_family(app_names):
    # ----------------------------
    all_same_family = True

    if len(app_names) > 1:
        family_name = ''

        for app_name in app_names:
            splits = app_name.split('_')
            if len(splits) > 1:
                family_name_test = splits[0] + '_' + splits[1]

                if not family_name:
                    family_name = family_name_test
                if not family_name == family_name_test:
                    all_same_family = False

    return all_same_family


# ----------------------------
def count_project_references_precentage(imports, project):
    # ----------------------------
    project_files = imports.keys()

    project_counts = 0
    for test_project_file in sorted(project_files):
        if project in imports[test_project_file].keys():
            project_counts += 1

    try:
        return_value = project_counts * 100.0 / (len(project_files) - 1)
    except:
        return_value = 0

    return return_value


# ----------------------------
def build_import_report(imports):
    # ----------------------------
    # work on how to diagram the results
    # https://www.python-graph-gallery.com/chord-diagram/

    return_value = '\n'
    import_loops_count = 0

    project_files = imports.keys()

    project_use_percentages = {}
    for project_file in sorted(project_files):
        project_use_percentages[project_file] = count_project_references_precentage(imports, project_file)

    for project_file in sorted(project_files):
        return_value += project_file + '\n'
        for sub_key in sorted(imports[project_file].keys()):
            if sub_key in project_files:
                project_counts = project_use_percentages[sub_key]
                return_value += '    ' + '{:3d}'.format(int(project_counts)) + ' ' + sub_key

                if sub_key in imports:
                    if project_file in imports[sub_key]:
                        return_value += ' (!!! loop !!!)'
                        import_loops_count += 1

                return_value += '\n'

    return return_value, import_loops_count


# ----------------------------
def get_imports_found(content):
    # ----------------------------
    return_value = {}

    in_start_block = False
    for line in content.split('\n'):
        if '# ===== begin: start file' in line:
            in_start_block = True
        if '# ===== end: start file' in line:
            in_start_block = False

        if not in_start_block:
            if 'import ' == line.strip()[:7]:
                the_import = line.split('import')[1].split()[0]
                return_value[the_import] = ''

    return return_value


# ----------------------------
def clear_screen():
    # ----------------------------

    # for windows
    if os.name == 'nt':
        _ = os.system('cls')

    # for mac and linux(here, os.name is 'posix')
    else:
        _ = os.system('clear')


# ----------------------------
def cow_fail():
    # ----------------------------

    return """____
< Fail >
  ----
         \   ^__^
          \  (oo)\_______
             (__)\       )\\/\\
                 ||----w |
                 ||     ||"""


# ----------------------------
def cow_pass():
    # ----------------------------
    # https://cowsay.morecode.org/say?message=Pass&format=html
    return """  ____
< Pass >
  ----
         \   ^__^
          \  (oo)\_______
             (\\/)\       )\/\\
                 ||----w |
                 ||     ||"""


# ----------------------------
def get_result_art(failing=True, no_tests=False, duplicates=False, duplicate_primary=False, any_warning=False):
    # ----------------------------
    return_value = ''
    return_color = s_text_color_start_green

    # color text: https://stackoverflow.com/questions/39473297/how-do-i-print-colored-output-with-python-3

    if failing:
        return_value += s_text_color_start_red
        return_value += cow_fail()
        #        return_value += ('FAIL ' * 10 + '\n') * 10
        return_value += s_text_color_end
    else:
        if duplicates or duplicate_primary:
            if duplicates:
                return_value += s_text_color_start_yellow
                return_value += ('DUPLICATE TESTS ' * 5 + '\n') * 10
                return_value += s_text_color_end
            if duplicate_primary:
                return_value += s_text_color_start_yellow
                return_value += ('DUPLICATE PRIMARY ' * 5 + '\n') * 10
                return_value += s_text_color_end
        else:
            if no_tests:
                return_value += s_text_color_start_yellow
                return_value += ('MISSING ' * 10 + '\n') * 10
                return_value += s_text_color_end
            else:
                if any_warning:
                    return_value += s_text_color_start_magenta
                else:
                    return_value += s_text_color_start_green
                return_value += cow_pass()
                #                return_value += ('PASS ' * 10 + '\n') * 10
                return_value += s_text_color_end

    return return_value


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# -------------------------------
def get_all_testable_files():
    # -------------------------------
    all_py_files = []
    all_files = os.listdir('.')
    all_testable_files = {}
    for item in all_files:
        do_capture_file = False

        if item[-3:] == '.py':
            do_capture_file = True

        if item == 'offline_test_manually_results.txt':
            do_capture_file = True

        if do_capture_file:
            fileStatsObj = os.stat(item)
            modified_time = fileStatsObj[stat.ST_MTIME]
            all_py_files.append((item, modified_time))

    return all_py_files


# -------------------------------
def get_reason_not_slicer_wsgi_compliant(content):
    # -------------------------------
    reason = ''

    lines_of_code = content.split('\n')

    for line_index in range(1, len(lines_of_code)):
        if 'start_response' == lines_of_code[line_index].strip()[:14]:
            line_before = lines_of_code[line_index - 1].strip()

            start_response_compliant = False
            if line_before == '# allow non wrapped response':
                start_response_compliant = True

            if line_before == 'html = organization.wrap_page_with_session(environ, html)':
                start_response_compliant = True

            if line_before == 'html = wrap_page_with_session(environ, html)':
                start_response_compliant = True

            if not start_response_compliant:
                reason = 'start_response not preceeded by (html = organization.wrap_page_with_session(environ, html)'

    return reason


# -------------------------------
def is_ascii(s):
    # -------------------------------
    return all(ord(c) < 128 for c in s)


# -------------------------------
def main():
    # -------------------------------
    print('running all tests...')

    pass_count = 0


    last_branches = {'branches': subprocess.check_output(['git','branch']),
                        'git_log':subprocess.check_output('git log'.split()).decode("utf-8").split('\n'),
                        }

    try:
        while True:
            any_duplicate_tests = False
            all_the_test_names = {}
            any_duplicate_primary = False
            all_the_primary_names = {}
            all_testable_files = {}
            imports = {}
            pass_count += 1
            longest_running = {'name': '', 'run_time': -1}
            versions_found = {}
            any_fail = False
            any_warning = False
            methods_not_conforming_summary = {}
            in_class = None

            for item, modified_time in get_all_testable_files():
                all_testable_files[item] = {'has_tests': False,
                                            'test_result': 'not_run',
                                            'report': '',
                                            'failures': [],
                                            'run_time': -1,
                                            'cmd': '',
                                            'modified': modified_time,
                                            'fixes': [],
                                            'nbsp': [],
                                            'methods_not_conforming': [],
                                            'fail': '(No fail report found for this item)',

                                            'project_type': '',
                                            'project_non_compliance': '',
                                            'import_list': [],
                                            }

                content = open(item, 'r').read()

                all_testable_files[item]['file_syntax_issues'] = file_syntax_issues(content)

                is_after_start_of_tests = False
                count_lines = 0
                for line in content.split('\n'):
                    count_lines += 1

                    if line.startswith('class '):
                        in_class = line.split(' ')[1].split('(')[0]
                    if line.startswith('def '):
                        in_class = None

                    if True:
                        if not is_ascii(line):
                            all_testable_files[item]['nbsp'].append('line ' + str(count_lines) + ' ' + line.strip())
                    else:
                        if '\xa0' in line.lower():  # using split here, so again I do not find myself
                            all_testable_files[item]['nbsp'].append('line ' + str(count_lines) + ' ' + line.strip())

                    if 'f' + 'ixme:'.lower() in line.lower():  # using split here, so again I do not find myself
                        all_testable_files[item]['fixes'].append('line ' + str(count_lines) + ' ' + line.strip())

                    if 'f' + 'ixme:'.lower() in line.lower():  # using split here, so again I do not find myself
                        all_testable_files[item]['fixes'].append('line ' + str(count_lines) + ' ' + line.strip())

                    if 'de' + 'f '.lower() in line.lower():  # using split here, so again I do not find myself
                        if not check_if_method_conforms(line):
                            all_testable_files[item]['methods_not_conforming'].append(
                                'line ' + str(count_lines) + ' ' + line.strip())
                            prefix = get_definition_prefix_from_line(line)
                            if not prefix in methods_not_conforming_summary:
                                methods_not_conforming_summary[prefix] = 0
                            methods_not_conforming_summary[prefix] += 1

                    if 'pi_' in item:
                        if len(line) > 10:
                            if 'version = ' == line[0:10]:
                                version_found = line.replace('version = ', '').replace("'", '')
                                versions_found[item] = version_found

                    if 'c' + 'lass TestAllMethods' in line:
                        is_after_start_of_tests = True

                    if 'de' + 'f ' in line:
                        # make sure 'def' is at beginning of the strip-ed line
                        if line.strip()[0:3] == 'def':
                            # the_name = line.split('def')[1].strip().split('(')[0] + ' in ' + item
                            the_name = get_definition_from_line(line) + ' in ' + item
                            # print (item, line, the_name)

                            if in_class:
                                the_name = in_class + '.' + the_name

                            if is_after_start_of_tests:
                                if not the_name in all_the_test_names:
                                    all_the_test_names[the_name] = 1
                                else:
                                    # first duplicate
                                    any_duplicate_tests = True
                                    all_the_test_names[the_name] += 1
                            else:
                                # This is a primary definition in the module
                                if not the_name in all_the_primary_names:
                                    all_the_primary_names[the_name] = 1
                                else:
                                    # first duplicate
                                    any_duplicate_primary = True
                                    all_the_primary_names[the_name] += 1

                if item == 'offline_test_manually_results.txt':
                    all_testable_files[item]['has_tests'] = True

                    last_run_date = get_manual_test_most_recent_run_date(content)
                    TS = get_TS()
                    days_old_to_allow = 21
                    seconds_old = get_seconds_diff_from_string_date_to_TS(last_run_date, TS)
                    if seconds_old != None:
                        if seconds_old > days_old_to_allow * 24 * 60 * 60:
                            # too long, make it fail
                            all_testable_files[item]['test_result'] = 'FAIL'
                            all_testable_files[item]['fail'] = ('FAIL: It has been longer than '
                                                                + str(days_old_to_allow)
                                                                + ' days since the manual tests have been run. ---> Tests are in offline_test_manually.txt and results need to be put in the top of offline_test_manually_results.txt')
                        else:
                            # fresh enough
                            all_testable_files[item]['test_result'] = 'OK'
                    else:
                        all_testable_files[item]['test_result'] = 'FAIL'
                        all_testable_files[item]['fail'] = 'FAIL: No date given, or malformed date found in offline_test_manually_results.txt for the line "manual_tests_completed = ".'

                if 'c' + 'lass TestAllMethods' in content or 'I' + 'gnore_for_mass_testing' in content:  # splitting the strings, so that I do not find myself
                    all_testable_files[item]['has_tests'] = True

                    all_testable_files[item]['cmd'] = 'python3 -m unittest ' + item.replace('.py', '')

                    if not 'I' + 'gnore_for_mass_testing' in content:  # split the strings, so that I do not find myself
                        # Run the tests here
                        time_start = time.time()
                        run_test, fails = do_one_command(all_testable_files[item]['cmd'])
                        time_end = time.time()

                        all_testable_files[item]['run_time'] = time_end - time_start

                        if all_testable_files[item]['run_time'] > longest_running['run_time']:
                            longest_running['run_time'] = all_testable_files[item]['run_time']
                            longest_running['name'] = all_testable_files[item]['cmd']

                        all_testable_files[item]['report'] = run_test
                        all_testable_files[item]['test_result'] = fails.split('\n')[-2]
                        if all_testable_files[item]['test_result'] != 'OK':
                            for line in fails.split('\n'):
                                if 'FAIL:' in line:
                                    all_testable_files[item]['failures'].append(line)
                            all_testable_files[item]['fail'] = fails
                        all_testable_files[item]['fail']

                if 'slicer_wsgi_' in item:
                    all_testable_files[item]['project_type'] = 'slicer_wsgi_'

                    # check for any self compliance items that we dream up
                    all_testable_files[item]['project_non_compliance'] = get_reason_not_slicer_wsgi_compliant(content)

                    # do
                    # get_imports_found(content)
                    imports[item.replace('slicer_wsgi_', '').replace('.py', '')] = get_imports_found(content)

            # show results
            clear_screen()
            print('--------------------\nduplicate tests:\n--------------------')
            for item in sorted(all_the_test_names):
                if all_the_test_names[item] > 1:
                    print(str(all_the_test_names[item]) + '-> ' + s_text_color_start_yellow + item + s_text_color_end)

            print('--------------------\nduplicate primary names:\n--------------------')
            for item in sorted(all_the_primary_names):
                if all_the_primary_names[item] > 1:
                    print(
                        str(all_the_primary_names[item]) + '-> ' + s_text_color_start_yellow + item + s_text_color_end)

            print('--------------------\nmissing tests:\n--------------------')
            any_no_tests = False
            for item in sorted(all_testable_files):
                if not all_testable_files[item]['has_tests']:
                    print(s_text_color_start_yellow + item + s_text_color_end)
                    any_no_tests = True

            print(
                '--------------------\nignored: (Does not have "' + 'c' + 'lass TestAllMethods"' + ' OR contains the string "' + 'I' + 'gnore_for_mass_testing' + '")\n--------------------')
            report = ''
            for item in sorted(all_testable_files):
                if all_testable_files[item]['has_tests']:
                    if all_testable_files[item]['test_result'] == 'not_run':
                        if report:
                            report += ', '
                        report += item
            print(report)

            print('--------------------\ntests passing:\n--------------------')
            report = ''
            for item in sorted(all_testable_files):
                if all_testable_files[item]['has_tests']:
                    if all_testable_files[item]['test_result'] == 'OK':
                        if report:
                            report += ', '
                        report += item
            print(report)

            print('--------------------\nimports:\n--------------------')
            report, import_loops_count = build_import_report(imports)
            print(report)

            non_compliance_string = 'PASS'
            report = ''
            for item in sorted(all_testable_files):
                project_non_compliance = all_testable_files[item]['project_non_compliance']
                if project_non_compliance:
                    report += s_text_color_start_yellow + item + ' -> ' + project_non_compliance + '\n' + s_text_color_end
                    non_compliance_string = 'NONCOMPLIANT'
            if report:
                print('--------------------\nFiles that are project-non-compliant:\n--------------------')
                print(report)

            fixes_string = 'PASS'
            report = ''
            for item in sorted(all_testable_files):
                for fix in all_testable_files[item]['fixes']:
                    report += s_text_color_start_yellow + item + ' -> ' + fix + '\n' + s_text_color_end
                    fixes_string = 'FIXES'
            if report:
                print('--------------------\nFiles that have fixes needed:\n--------------------')
                print(report)

            methods_string = 'PASS'
            report = ''
            for item in sorted(all_testable_files):
                for methods_not_conforming in all_testable_files[item]['methods_not_conforming']:
                    report += s_text_color_start_yellow + item + ' -> ' + methods_not_conforming + '\n' + s_text_color_end
                    methods_string = 'METHODS'
            if report:
                print('--------------------\nFiles that have method name updates needed:\n--------------------')
                print(report)

                #                for prefix in sorted(methods_not_conforming_summary.keys()):
                #                    print (methods_not_conforming_summary[prefix], prefix)
                for (k, v) in sorted(methods_not_conforming_summary.items(), key=lambda kv: (kv[1], kv[0])):
                    print(v, k)

                print('\n' + 'count of methods with non-conforming names = ' + str(len(report.split('\n'))))

            print('\n' + 'count of import loops = ' + str(import_loops_count) + '\n' + '\n')

            nbsp_string = 'PASS'
            report = ''
            for item in sorted(all_testable_files):
                for line_with_issue in all_testable_files[item]['nbsp']:
                    report += s_text_color_start_yellow + item + ' -> ' + line_with_issue + '\n' + s_text_color_end
                    nbsp_string = 'NBSP'
            if report:
                print('--------------------\nFiles that have nbsp to be removed:\n--------------------')
                print(report)

            for item in sorted(all_testable_files):
                if all_testable_files[item]['has_tests']:
                    if all_testable_files[item]['test_result'] != 'OK' and all_testable_files[item]['test_result'] != 'not_run':
                        print(item, all_testable_files[item], '\n')
                        any_fail = True

            for item in sorted(all_testable_files):
                if 'file_syntax_issues' in all_testable_files[item]:
                    if all_testable_files[item]['file_syntax_issues']:
                        print("-----------------------------------")
                        print(item)
                        print("-----------------------------------")
                        line_to_print = 'FAIL: ' + all_testable_files[item]['file_syntax_issues']
                        print(s_text_color_start_red + line_to_print + s_text_color_end)
                        print("-----------------------------------")
                        any_fail = True

            versioned_os_list = []
            try:
                versioned_os_list = os.listdir('./pi_versioned_' + universe)
            except:
                pass

            version_prefixes_found = {}
            for item in versions_found.keys():
                version_prefix_found = versions_found[item].split('.')[0]
                if not version_prefix_found in version_prefixes_found:
                    version_prefixes_found[version_prefix_found] = []
                version_prefixes_found[version_prefix_found].append(item)

            for version_prefix_found in sorted(version_prefixes_found.keys()):
                if len(version_prefixes_found[version_prefix_found]) > 1:
                    if check_if_all_are_in_a_family(version_prefixes_found[version_prefix_found]):
                        line_to_print = 'info: Family version prefix found: ' + version_prefix_found + ' in ' + str(
                            version_prefixes_found[version_prefix_found])
                        print(s_text_color_start_yellow + line_to_print + s_text_color_end)
                    else:
                        any_fail = True
                        line_to_print = 'Duplicate version prefix found: ' + version_prefix_found + ' in ' + str(
                            version_prefixes_found[version_prefix_found])
                        print(s_text_color_start_red + line_to_print + s_text_color_end)

            # print ('versions_found', versions_found)

            line_to_print = "\n".join(versioned_apps_not_captured(versioned_os_list, versions_found))
            if len(line_to_print) > 0:
                any_warning = True
                print(s_text_color_start_magenta + 'versioned_apps_not_captured: \n' + line_to_print + s_text_color_end)

            if not any_fail:
                print('--------------------\ntests failing:\n--------------------')
                print('(none)', '\n')
            else:
                print(
                    s_text_color_start_red + '--------------------\ntests failing:\n--------------------' + s_text_color_end)
                for item in sorted(all_testable_files):
                    if all_testable_files[item]['has_tests']:
                        if all_testable_files[item]['test_result'] != 'OK' and all_testable_files[item]['test_result'] != 'not_run':
                            print(s_text_color_start_yellow + item + s_text_color_end)

                            for printable in all_testable_files[item]['failures']:
                                print(s_text_color_start_white + printable + s_text_color_end)

                            line_report = ''
                            for line in all_testable_files[item]['fail'].split('\n'):
                                if 'line' in line:
                                    line_report = ''
                                if line:
                                    line_report += '\n'
                                line_report += line

                            if line_report:
                                print(s_text_color_start_red + line_report + s_text_color_end)
                                print('\n\n')

            print(get_result_art(any_fail, any_no_tests, any_duplicate_tests, any_duplicate_primary,
                                 any_warning=any_warning).replace('PASS', fixes_string))

            if any_fail:
                for i in range(0, 5):
                    print('\a')  # beep
                    time.sleep(0.25)
            elif any_warning or any_duplicate_tests:
                for i in range(0, 2):
                    print('\a')  # beep
                    time.sleep(0.25)
            else:
                for i in range(0, 1):
                    print('\a')  # beep
                    time.sleep(0.25)

#            print('pass_count', pass_count)

            try:
                branches = subprocess.check_output(['git','branch']).decode("utf-8")
                print ('--------------------')
                print ('Branches:')
                for branch in branches.split('\n'):
                    if '*' in branch:
                        # "* dwf_github_account"
                        branch_name = branch.split('*')[1].strip()
                        cmd1 = 'git diff --name-only ' + branch_name + ' master'
                        cmd2 = 'git diff --name-only ' + branch_name
                        files_changed_and_commited = subprocess.check_output(cmd1.split()).decode("utf-8").split('\n')
                        files_changed_and_not_commited = subprocess.check_output(cmd2.split()).decode("utf-8").split('\n')

                        cmd_to_get_master_commits = 'git log ..master'
                        git_log_master = subprocess.check_output(cmd_to_get_master_commits.split()).decode("utf-8")
                        master_commits_behind = parse_commits_from_log(git_log_master)
                        commits_files_list = []
                        for item in master_commits_behind.keys():
                            cmd_to_get_commit_files = 'git show ' + item
                            show_commit = subprocess.check_output(cmd_to_get_commit_files.split()).decode("utf-8")
                            commits_files_list.append(find_modified_on_master(parse_files_from_show_commit(show_commit)))
                        files_in_master_commits = make_file_list_from_commits_data(commits_files_list)

                        if any_fail:
                            text_color_to_use = s_text_color_start_red
                        elif any_warning or any_duplicate_tests:
                            text_color_to_use = s_text_color_start_yellow
                        else:
                            text_color_to_use = s_text_color_start_green

                        content = text_color_to_use + branch + s_text_color_end
                        left_overs = []

                        all_touches = copy.copy(files_changed_and_commited)
                        for item in files_changed_and_not_commited:
                            if not item in all_touches:
                                all_touches.append(item)

                        for item in sorted(all_touches):
                            if item:
                                content += '\n' + text_color_to_use + '      ' + item + s_text_color_end
                                if item in files_changed_and_not_commited:
                                    content += s_text_color_start_yellow + '  ... ' + 'not yet committed' + s_text_color_end

                                if item in files_in_master_commits:
                                    content += s_text_color_start_magenta + '  ... ' + 'master merge wanted' + s_text_color_end

                        print(content)
                    else:
                        if branch:
                            print(branch)
                print ('--------------------')
            except:
                pass

            if longest_running['run_time'] > 0.5:
                print('--------------------\ntest running long:\n--------------------')
                print(longest_running['name'] + '\n' + str(longest_running['run_time']))

#            print('\n waiting for file changes...')
            loop_count = 0
            divider = 10
            time_of_wait_start = time.time()
            any_change = False
            while not any_change:
                # be sure to run once a day, to catch the expiration of any manual test requirements
                time_of_wait_now = time.time()
                time_until_next_auto_run = 24 * 60 * 60 - (time_of_wait_now - time_of_wait_start)
                if time_until_next_auto_run < 0:
                    any_change = True

                time.sleep(0.5 / divider)
                loop_count += 1
                loop_count %= 20

                print(
                    u"\u001b[1000D" + '  ' + s_text_color_start_white + 'running automatic in ' + convert_seconds_to_human(
                        int(time_until_next_auto_run)) + ' : looking for changed files always ' + '*' * (
                            1 + loop_count) + s_text_color_end + ' ' * (22 - loop_count) + u"\u001b[1000D", end='',
                    flush=True)

                if loop_count == 0:
                    # -----------------------------------------------
                    # trigger on git activity
                    current_branches = {'branches': subprocess.check_output(['git','branch']),
                                        'git_log':subprocess.check_output('git log'.split()).decode("utf-8").split('\n'),
                                        }
                    if current_branches != last_branches:
                        last_branches = current_branches
                        any_change = True

                    # -----------------------------------------------
                    # Do forward lookup to detect file content change
                    all_files_reread = get_all_testable_files()
                    did_check = {}
                    for item, modified_time in all_files_reread:
                        if not item in all_testable_files:
                            print(item)
                            any_change = True
                        else:
                            if all_testable_files[item]['modified'] != modified_time:
                                print(item)
                                any_change = True
                            did_check[item] = True

                    # do reverse look up to notice files have been removed
                    for item in all_testable_files:
                        if not item in did_check:
                            any_change = True
                            print(item)

            print('')
            print(s_text_color_start_magenta + ('running... ' * 10 + '\n') * 10 + s_text_color_end)

    except:
        print("exception", traceback.format_exc())


if __name__ == '__main__':
    not_done = True

    main()

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    # ----------------------------------------------------
    def test_get_reason_not_slicer_wsgi_compliant(self):
        # ----------------------------------------------------
        """
        (fill in here)
        """
        content = """
    html = organization.wrap_page_with_session(environ, html)
    start_response(status,response_header)
    """
        expected = ''
        actual = get_reason_not_slicer_wsgi_compliant(content)
        self.assertEqual(expected, actual)

        content = """
    html = wrap_page_with_session(environ, html)
    start_response(status,response_header)
    """
        expected = ''
        actual = get_reason_not_slicer_wsgi_compliant(content)
        self.assertEqual(expected, actual)

        content = """
    # allow non wrapped response
    start_response(status,response_header)
    """
        expected = ''
        actual = get_reason_not_slicer_wsgi_compliant(content)
        self.assertEqual(expected, actual)

        content = """
def application(environ,start_response):

    html = organization.wrap_page_with_session(environ, html)
    start_response(status,response_header)
    """
        expected = ''
        actual = get_reason_not_slicer_wsgi_compliant(content)
        self.assertEqual(expected, actual)

        content = """
    html = organization.wrap_page_with_session(environ, html)

    some_other_line_of_code = True
    start_response(status,response_header)
    """
        expected = 'start_response not preceeded by (html = organization.wrap_page_with_session(environ, html)'
        actual = get_reason_not_slicer_wsgi_compliant(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_get_imports_found(self):
        # ----------------------------------------------------
        content = ''
        expected = {}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

        content = 'import os'
        expected = {'os': ''}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

        content = 'import_os'
        expected = {}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

        content = """
import os
import datastore
"""
        expected = {'os': '', 'datastore': ''}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_get_imports_not_if_commented(self):
        # ----------------------------------------------------
        content = '#import os'
        expected = {}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_get_imports_yes_if_tabbed(self):
        # ----------------------------------------------------
        content = '    import os'
        expected = {'os': ''}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_get_imports_not_if_in_start_block(self):
        # ----------------------------------------------------
        content = """
# ===== begin: start file
#!/usr/bin/env python
import watchdog
watchdog.main()
# ===== end: start file
        """
        expected = {}
        actual = get_imports_found(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_count_project_references_precentage(self):
        # ----------------------------------------------------
        imports = {'datastore': {'os': ''}}
        project = 'datastore'
        expected = 0
        actual = count_project_references_precentage(imports, project)
        self.assertEqual(expected, actual)

        imports = {'datastore': {'os': '', 'blah': ''}, 'blah': {}}
        project = 'blah'
        expected = 100
        actual = count_project_references_precentage(imports, project)
        self.assertEqual(expected, actual)

        imports = {'datastore': {'os': '', 'blah': ''}, 'blah': {}, 'yam': {}}
        project = 'blah'
        expected = 50
        actual = count_project_references_precentage(imports, project)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_build_import_report_normal(self):
        # ----------------------------------------------------
        # do not report non project imports
        imports = {'datastore': {'os': ''}}
        expected = """
datastore
"""
        actual, loop_count = build_import_report(imports)
        self.assertEqual(expected, actual)

        # do report in project imports
        imports = {'datastore': {'os': '', 'blah': ''}, 'blah': {}}
        expected = """
blah
datastore
    100 blah
"""
        actual, loop_count = build_import_report(imports)
        #        self.assertEqual(expected, actual)

        # do report in project imports
        imports = {'datastore': {'os': '', 'blah': ''}, 'blah': {}, 'yam': {}}
        expected = """
blah
datastore
     50 blah
yam
"""
        actual, loop_count = build_import_report(imports)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_build_import_report_loops(self):
        # ----------------------------------------------------
        imports = {'datastore': {'foxtrot': ''}, 'foxtrot': {'datastore': ''}}
        expected = """
datastore
    100 foxtrot (!!! loop !!!)
foxtrot
    100 datastore (!!! loop !!!)
"""
        actual, loop_count = build_import_report(imports)
        self.assertEqual(expected, actual)
        self.assertEqual(2, loop_count)

    # ----------------------------------------------------
    def test_build_import_report_linked_loops(self):
        # ----------------------------------------------------
        imports = {'datastore': {'foxtrot': ''}, 'foxtrot': {'zeta': ''}, 'zeta': {'datastore': ''}}
        expected = """
datastore
    100 foxtrot (!!! loop !!!)
foxtrot
    100 zeta (!!! loop !!!)
zeta
    100 datastore (!!! loop !!!)
"""
        actual, loop_count = build_import_report(imports)
        # FixMe: Need to catch deep loops

    #        self.assertEqual(expected, actual)
    #        self.assertEqual(3, loop_count)

    # ----------------------------------------------------
    def test_check_if_all_are_in_a_family(self):
        # ----------------------------------------------------
        app_names = ['pi_test']
        expected = True
        actual = check_if_all_are_in_a_family(app_names)
        self.assertEqual(expected, actual)

        app_names = ['pi_test', 'pi_not']
        expected = False
        actual = check_if_all_are_in_a_family(app_names)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_versioned_apps_not_captured(self):
        # ----------------------------------------------------
        versioned_os_list = ['pi_service_security_pi_version_S.1.0']
        versions_found = {'pi_security.py': 'S.1.0'}
        expected = []
        actual = versioned_apps_not_captured(versioned_os_list, versions_found)
        self.assertEqual(expected, actual)

        versioned_os_list = ['pi_service_security_pi_version_S.1.0']
        versions_found = {'pi_security.py': 'S.1.1'}
        expected = ['pi_service_security_pi_version_S.1.1']
        actual = versioned_apps_not_captured(versioned_os_list, versions_found)
        self.assertEqual(expected, actual)

        #        versions_found  = {'pi_organization.py': 'O.1.1', 'pi_settings_cordis.py': 's.0.5', 'pi_template.py': 't.0.2', 'pi_settings_world.py': 's.0.5', 'pi_network_generic.py': 'N.6.8', 'pi_beyondtrust.py': 'T.1.0', 'pi_hmi.py': 'H.4.8', 'pi_thirdparty.py': 'P.0.1', 'pi_settings_cah.py': 's.0.7', 'pi_runner.py': 'R.8.3', 'pi_security.py': 'S.2.0', 'pi_network_cah.py': 'N.7.2', 'pi_monitor.py': 'M.2.4', 'pi_config.py': 'C.1.2', 'pi_logging.py': 'L.3.1', 'pi_bluetooth.py': 'B.2.6'}

        versioned_os_list = ['pi_service_settings_pi_version_s.0.7']
        versions_found = {'pi_settings_cah.py': 's.0.7'}
        expected = []
        actual = versioned_apps_not_captured(versioned_os_list, versions_found)
        self.assertEqual(expected, actual)

        versioned_os_list = ['pi_service_settings_pi_version_s.0.7']
        versions_found = {'pi_settings_cah.py': 's.0.7', 'pi_settings_world.py': 's.1.7'}
        expected = []
        actual = versioned_apps_not_captured(versioned_os_list, versions_found)
        self.assertEqual(expected, actual)

        # ignore the template
        versioned_os_list = []
        versions_found = {'pi_template.py': 'S.1.0'}
        expected = []
        actual = versioned_apps_not_captured(versioned_os_list, versions_found)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_offline_test_manually_results(self):
        # ----------------------------------------------------
        content = ''
        expected = ''
        actual = get_manual_test_most_recent_run_date(content)
        self.assertEqual(expected, actual)

        content = 'manual_tests_completed = 2023.05.23'
        expected = '2023.05.23'
        actual = get_manual_test_most_recent_run_date(content)
        self.assertEqual(expected, actual)

        content = """
manual_tests_completed = 2023.05.23
manual_tests_completed = 2023.04.01
"""
        expected = '2023.05.23'
        actual = get_manual_test_most_recent_run_date(content)
        self.assertEqual(expected, actual)

    # ----------------------------------------------------
    def test_check_time_for_manual_test_run(self):
        # ----------------------------------------------------
        TS = '20210406201829131704'

        expected = datetime.datetime(2021, 4, 6, 20, 18, 29, 131704)
        actual = get_ts_to_datetime(TS)
        self.assertEqual(expected, actual)

        date_one = datetime.datetime(2020, 8, 15)
        date_two = datetime.datetime(2020, 8, 16)
        diff_time = date_two - date_one

        expected = 1 * 24 * 60 * 60
        actual = diff_time.total_seconds()
        self.assertEqual(expected, actual)

        string_date = '2020.08.16'
        expected = datetime.datetime(2020, 8, 16)
        actual = get_string_date_to_datetime(string_date)
        self.assertEqual(expected, actual)

        # make a mal-formed one, to be robust (this is user input content)
        string_date = '2020.8.16'
        expected = None
        actual = get_string_date_to_datetime(string_date)
        self.assertEqual(expected, actual)

        # all together now, like it will be in the main body of code
        string_date = '2020.08.16'
        TS = get_TS()
        TS = '20200817000000000000'  # override for test
        expected = 1 * 24 * 60 * 60
        actual = get_seconds_diff_from_string_date_to_TS(string_date, TS)
        self.assertEqual(expected, actual)

        # malformed
        string_date = '2020.08.0'
        TS = get_TS()
        TS = '20200817000000000000'  # override for test
        expected = None
        actual = get_seconds_diff_from_string_date_to_TS(string_date, TS)
        self.assertEqual(expected, actual)

    def test_get_definition_from_line(self):
        df_line = "def test_is_true()"  # public
        expected = 'test_is_true'
        actual = get_definition_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = "def test_definition_is_true()"  # try to trip it up with another 'def' in the line
        expected = 'test_definition_is_true'
        actual = get_definition_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = '    def test_dummy(self):'
        expected = 'test_dummy'
        actual = get_definition_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = '    def test_get_definition_from_line(self):'
        expected = 'test_get_definition_from_line'
        actual = get_definition_from_line(df_line)
        self.assertEqual(expected, actual)

    def test_get_definition_prefix_from_line_no_issues(self):
        df_line = "de" + "f test_is_true()"  # public
        expected = 'test'
        actual = get_definition_prefix_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f _test_is_true()"  # private
        expected = 'test'
        actual = get_definition_prefix_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = "#de" + "f why_is_true()"  # commented out
        expected = ''
        actual = get_definition_prefix_from_line(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f " + "def (make_file_extension_lower(filename):"  # extra parens on the line, don't crash
        expected = ''
        actual = get_definition_prefix_from_line(df_line)
        self.assertEqual(expected, actual)

    def test_check_if_method_conforms(self):
        df_line = "de" + "f test_is_true()"
        expected = True
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f why_is_true()"
        expected = False
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

        df_line = "#de" + "f why_is_true()"
        expected = True
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f get_is_true()"
        expected = True
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f _get_is_true()"  # private
        expected = True
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

        df_line = "de" + "f application(environ, start_response)"
        expected = True
        actual = check_if_method_conforms(df_line)
        self.assertEqual(expected, actual)

    def test_file_syntax_issues(self):
        content = """
            if a == b:
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            while a == b:
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            a =""" + """= b
        """
        expected = 'line 2: test without if: a == b\n'
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            a !""" + """= b
        """
        expected = 'line 2: test without if: a != b\n'
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
        # =====
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
        ===
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
        a = (b == c)
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
        a = (b != c)
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            # ignore_line_count_down_for_syntax_checking
            a !""" + """= b
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            # ignore_line_count_down_for_syntax_checking=1
            a !""" + """= b
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

        content = """
            # ignore_line_count_down_for_syntax_checking=2
            # a line to be skipped
            a !""" + """= b
        """
        expected = ''
        actual = file_syntax_issues(content)
        self.assertEqual(expected, actual)

    def test_parse_commits_from_log(self):
        # from "git log ..master"
        git_log_master = """commit 6b655936710c2c3dd423428a9730541a8944d589 (origin/master, origin/HEAD, master)
Author: cah-david-ferguson <<EMAIL>>
Date:   Fri Oct 4 08:40:59 2024 -0400

    Another direct commit

commit 873aa33e9f5796257b763d3c1b006346d70af4da
Author: cah-david-ferguson <<EMAIL>>
Date:   Thu Oct 3 15:48:06 2024 -0400

    test commit direct to master
    """
        expected = {'6b655936710c2c3dd423428a9730541a8944d589':'Another direct commit',
                    '873aa33e9f5796257b763d3c1b006346d70af4da':'test commit direct to master'}
        actual = parse_commits_from_log(git_log_master)
        self.assertEqual(expected, actual)

    def test_parse_files_from_show_commit(self):
        # from "git show 6b655936710c2c3dd423428a9730541a8944d589"
        show_commit = """commit 6b655936710c2c3dd423428a9730541a8944d589 (origin/master, origin/HEAD, master)
Author: cah-david-ferguson <<EMAIL>>
Date:   Fri Oct 4 08:40:59 2024 -0400

    Another direct commit

diff --git a/AA-videos.txt b/AA-videos.txt
index d883dcc..5b17b0a 100644
--- a/AA-videos.txt
+++ b/AA-videos.txt
@@ -16,4 +16,3 @@ iMovie:
 #################

 https://www.youtube.com/watch?v=aRLT9L_L1Pw"""
        expected = [{'a':'AA-videos.txt', 'b':'AA-videos.txt'}]
        actual = parse_files_from_show_commit(show_commit)
        self.assertEqual(expected, actual)

    def test_two_parse_files_from_show_commit(self):
        show_commit = """commit e51f7c66c4a90674caf8e0368c37f5f671858677 (origin/master, origin/HEAD, master)
Author: cah-david-ferguson <<EMAIL>>
Date:   Fri Oct 4 09:39:33 2024 -0400

    Two files changed in one test commit

diff --git a/AA-coding_bible.txt b/AA-coding_bible.txt
index 761cef9..430609f 100644
--- a/AA-coding_bible.txt
+++ b/AA-coding_bible.txt
@@ -35,3 +35,4 @@ style       A code that is related to styling.
 test        Adding new test or making changes to existing test.


+
diff --git a/AA-data_migration.txt b/AA-data_migration.txt
index 25913dd..101650b 100644
--- a/AA-data_migration.txt
+++ b/AA-data_migration.txt
@@ -12,3 +12,4 @@ Last step:
 - On the old server, get a snapshot of the datastore, and check it into GIT, then load to
     the new server

+"""
        expected = [{'a':'AA-coding_bible.txt', 'b':'AA-coding_bible.txt'},
                        {'a':'AA-data_migration.txt', 'b':'AA-data_migration.txt'}]
        actual = parse_files_from_show_commit(show_commit)
        self.assertEqual(expected, actual)

    def test_find_modified_on_master(self):
        result_parse_files_from_show_commit = [{'a':'AA-videos.txt', 'b':'AA-videos.txt'}]
        expected = ['AA-videos.txt']
        actual = find_modified_on_master(result_parse_files_from_show_commit)
        self.assertEqual(expected, actual)

        result_parse_files_from_show_commit = [{'a':'AA-videos.txt', 'b':'AA-videos2.txt'}]
        expected = ['AA-videos.txt', 'AA-videos2.txt']
        actual = find_modified_on_master(result_parse_files_from_show_commit)
        self.assertEqual(expected, actual)

    def test_make_file_list_from_commits_data(self):
        commits_files_list = [ ['AA-videos.txt'],
                            ['AA-videos.txt', 'AA-videos2.txt'] ]

        expected = {'AA-videos.txt':2, 'AA-videos2.txt':1}
        actual = make_file_list_from_commits_data(commits_files_list)
        self.assertEqual(expected, actual)















# End of file
