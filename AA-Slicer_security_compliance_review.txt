As of 2021.08.26, this is a review of the current state of the recommended security notes.

The input to this review comes from <PERSON>, in an email on August 12, 2021, and is attached to Jira card EUDE-773. To goal of this section is to list out each requirement, and to provide a place for response/evidence that the requirement is either currently met, or is being worked on.

Security Controls / Requirements

1) "We must have controls in place that ensure the device itself is not stolen, see Global Security for physical security controls."

Response: This is an open item, and is anticipated to be addressed by providing a complete 'kit' as an order-able item.

2) "We must use a centralized management tool for device governance including, but not limited to: OS Management, Device Enrollment, Patching, Updates, Enforce Encryption, Inventory."

Response: The site https://slicer.cardinalhealth.net provides the central communication and control point for the configuration and update of the raspberry pis. Currently, the device enrollment is fully automatic. Encryption is enforced through the use of https traffic as the sole communications path. The patching and updates is the topic of an active backlog item EUDE-1328.

3) "We must maintain an inventory of these devices."

Response: ??? This is outside the scope of software

4) "We must be able to monitor these devices and their behavior on the network."

Response: The devices check in to the slicer central site every minute. Status is available there. A card EUDE-1329 will add monitoring of network behaviour.

5) "No Cardinal Health information is stored on the device in anyway."

Response: The operation of the raspberry pi uses the chromium browser, in incognito mode, which means that when the browser is closed/restarted the active information is not saved. There is no other source of viewing new material, no saving, no downloading, or any other means for Cardinal Health information to be added to the device.

6) "We need to ensure that the OS cannot be compromised. Theft prevention of any and all media that is installed on the device, i.e. SD card, etc. Prevent unauthorized OS modification."

Response: ??? This is outside the scope of software

7) "Machine level certificates must be leveraged in order to identify the device."

Response: Card EUDE-1331 shows email from Andrew Beardsley confirming that this requirement is out of date.

8) "Must ensure that controls are in place to ensure that devices cannot communicate with each other directly."

Response: In discussions with Jeff Paugh, he was under the understanding that devices on the IOT network would have network level controls to that would fulfill this requirement.

9) "Least Privilege access must be followed the Pi can access only what is needed to and nothing else (sic)"

Response: The chromium browser is in incognito mode, so there will be no history saved.
The traffic to and from the browser is run through a proxy (privoxy) that enforces an allowlist only set of sites.
The white list is managed on a use case basis, so each use case only has access to what that use case requires, and no more.

10) "No password local accounts that can be used to authenticate to the device."

Response: Card in the backlog EUDE:1333

11) "Leverage CIS OS Baselines / Workstation Hardening"

Response: The current build includes a pi_security service, which used "lynis" as a report of "hardening".
Card EUDE-1350 will work on further improving the hardening score.




