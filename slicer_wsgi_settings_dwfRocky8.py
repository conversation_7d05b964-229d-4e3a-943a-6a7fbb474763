# dwf specific items

service = "settings"
version = service + '.dwf.0.1'


# ====================================
def get():
    # ====================================
    return_value = {
        'home_url': 'http://192.168.1.25',
        'trust_list': ['david.ferguson'],
        'data_drop_base': '/var/www/slicer/log/slicer/',
        'ram_disk_path': '/dev/shm/',
        'datastore_save_path': '/var/log/slicer/',
        'days_to_keep': 4,
        'sites_to_drop': {'MEX09': '(if key exists, then it will be dropped)'},
    }

    return_value['login_authentication'] = {
        'authentication_type': 'blind_trust',
    }

    # --------------------
    # Apache settings:
    # --------------------
    # https://stackoverflow.com/questions/44335970/apache-invalid-command-sslengine-perhaps-misspelled-or-defined-by-a-module-n

    files_path = '/var/www/htmlfiles'
    return_value['apache_files_path'] = files_path

    content_80 = """<VirtualHost *:80>
	ServerAdmin webmaster@localhost
	DocumentRoot """ + files_path + """
</VirtualHost>
"""

    content_443 = """
<VirtualHost *:443>
ServerName slicer.dwf.com
DocumentRoot """ + files_path + """
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/slicer_cabundle.pem"
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
"""

    return_value['apache_config_content'] = content_80  # + content_443

    return return_value


import time


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

# end of file
