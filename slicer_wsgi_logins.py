# A logins for slicer page services

service = "logins"
version = service + '.0.2.e'

release_notes = """
2023.02.01
logins.0.2

add live data table

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/logins.py

using:
sudo vi /var/www/html/logins.py

It also requires:

sudo vi /etc/httpd/conf.d/python-logins.conf
----- start copy -----
WSGIScriptAlias /logins /var/www/html/logins.py
----- end copy -----

sudo chown apache:apache /var/www/html/logins.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/logins-runner
sudo chmod +x /var/www/html/logins-runner

# ===== begin: start file
#!/usr/bin/env python
import logins
logins.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/logins-runner.service
sudo systemctl daemon-reload
sudo systemctl stop logins-runner.service
sudo systemctl start logins-runner.service
sudo systemctl enable logins-runner.service

systemctl status logins-runner.service

sudo systemctl restart logins-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep logins-runner

OR

tail -f /var/log/syslog | fgrep logins-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/logins-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import logins; print(logins.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/logins

https://slicer.cardinalhealth.net/logins?siteid=PR005

https://slicer.cardinalhealth.net/logins?serial=100000002a5da842

https://slicer.cardinalhealth.net/logins?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_logins


"""

import copy
import traceback
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import unittest

startup_exceptions = ''


service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    login_service_config = organization.get_config('login')
    base_log_path = login_service_config['base_log_path']

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0

# ----------------------------
def make_ldap_codes_text():
    # ----------------------------
    # https://dev.dotcms.com/docs/active-directory-error-codes
    content = []
    content.append(['525','User not found'])
    content.append(['52e','invalid password/credential'])
    content.append(['530','not permitted to logon at this time'])
    content.append(['531','not permitted to logon  from this workstation'])
    content.append(['532','password expired'])
    content.append(['533','account disabled'])
    content.append(['701','account expired'])
    content.append(['773','user must reset password'])
    content.append(['775','account lockedout'])

    body = ''
    body += '<table border="1" cellpadding="5">'
    for item in content:
        body += '<tr>'
        for sub_item in item:
            body += '<td>'
            body += sub_item
            body += '</td>'
        body += '</tr>'
    body += '</table>'
    body += '<br><br>'

    return body

# ----------------------------
def make_reportables(user_allowed_logs, dictionary_of_users_original):
    # ----------------------------
    by_date = {}
    dictionary_of_users = copy.deepcopy(dictionary_of_users_original)

    # if the user is not in the dictionary_of_users,
    #   or is the logs is earlier than the login time, then add a zero time of day entry
    for user in user_allowed_logs:
        if not user in dictionary_of_users:
            dictionary_of_users[user] = {'logs':[]} # This should cover the IP address named items
        for allowed_log in user_allowed_logs[user]:
            for date_time in user_allowed_logs[user].keys():
                date_midnight = date_time[0:8] + '0' * 12
            if not date_midnight in dictionary_of_users[user]['logs']:
                # for each day found, add a midnight time stamp...
                #    if there is no data for that item, we will remove it later in this call
                dictionary_of_users[user]['logs'].append(date_midnight)

    for user in sorted(dictionary_of_users.keys()):
        reports_by_log_time_stamp = {}

        # get the raw data assigned
        if user in user_allowed_logs:
            sorted_user_allowed_logs_user = sorted(user_allowed_logs[user].keys())
            log_index = 0

            for log_time_stamp in sorted(dictionary_of_users[user]['logs']):
                reports_by_log_time_stamp[log_time_stamp] = {'raw':[],'report':'', 'pages':{}, 'raw_actions':[]}

            sorted_reports_by_log_time_stamp = sorted(reports_by_log_time_stamp.keys())
            sorted_reports_by_log_time_stamp.append('99999999999999999999') # pad the end with one entry
            reports_index = 0

            while (log_index < len(sorted_user_allowed_logs_user)) and (reports_index < len(sorted_reports_by_log_time_stamp)-1):
                if sorted_user_allowed_logs_user[log_index] > sorted_reports_by_log_time_stamp[reports_index+1]:
                    reports_index += 1
                elif sorted_user_allowed_logs_user[log_index] < sorted_reports_by_log_time_stamp[reports_index]:
                    log_index += 1
                else:
                    content = user_allowed_logs[user][sorted_user_allowed_logs_user[log_index]].split('\n')[0]
                    reports_by_log_time_stamp[sorted_reports_by_log_time_stamp[reports_index]]['raw'].append(content)

                    if '\n' in user_allowed_logs[user][sorted_user_allowed_logs_user[log_index]]:
                        action = '\n'.join(user_allowed_logs[user][sorted_user_allowed_logs_user[log_index]].split('\n')[1:])
                        reports_by_log_time_stamp[sorted_reports_by_log_time_stamp[reports_index]]['raw_actions'].append(action)

                    log_index += 1

        # do the reports
        for log_time_stamp in reports_by_log_time_stamp.keys():
            for raw_report in reports_by_log_time_stamp[log_time_stamp]['raw']:
                page, method = raw_report.split(',')
                if not page in reports_by_log_time_stamp[log_time_stamp]['pages']:
                    reports_by_log_time_stamp[log_time_stamp]['pages'][page] = {}
                if not method in reports_by_log_time_stamp[log_time_stamp]['pages'][page]:
                    reports_by_log_time_stamp[log_time_stamp]['pages'][page][method] = 0
                reports_by_log_time_stamp[log_time_stamp]['pages'][page][method] += 1

            report = ''
            for page in sorted(reports_by_log_time_stamp[log_time_stamp]['pages'].keys()):
                if report:
                    report += ', '
                report += page + ' '
                for method in sorted(reports_by_log_time_stamp[log_time_stamp]['pages'][page].keys()):
                    report += '(' + str(reports_by_log_time_stamp[log_time_stamp]['pages'][page][method]) + ' ' + method + ')'
            reports_by_log_time_stamp[log_time_stamp]['report'] = report

            for action_report in reports_by_log_time_stamp[log_time_stamp]['raw_actions']:
                if not 'action_report' in reports_by_log_time_stamp[log_time_stamp]:
                    reports_by_log_time_stamp[log_time_stamp]['action_report'] = []
                reports_by_log_time_stamp[log_time_stamp]['action_report'].append(action_report)


        for log_time_stamp in sorted(dictionary_of_users[user]['logs']):
            day = log_time_stamp[0:8]
            if not day in by_date:
                by_date[day] = {}

            action_report = None

            if log_time_stamp in reports_by_log_time_stamp:
                report = reports_by_log_time_stamp[log_time_stamp]['report']
                if 'action_report' in reports_by_log_time_stamp[log_time_stamp]:
                    action_report = reports_by_log_time_stamp[log_time_stamp]['action_report']
            else:
                report = ''

            # here is where we will take away an empty midnight time stamp
            include_it = True
            if not report:
                if log_time_stamp[8:] == '0' * 12:
                    include_it = False

            if include_it:
                if not log_time_stamp in by_date[day]:
                    by_date[day][log_time_stamp] = []

                if action_report:
                    by_date[day][log_time_stamp].append({'user':user, 'report':report, 'action_report':action_report})
                else:
                    by_date[day][log_time_stamp].append({'user':user, 'report':report})

    return by_date

# ----------------------------
def make_timestamp_human_readable(timestamp):
# ----------------------------
    return timestamp[0:4] + '.' + timestamp[4:6] + '.' + timestamp[6:8] + ' ' + timestamp[8:10] + ':' + timestamp[10:12] + ':' + timestamp[12:14] + '.' + timestamp[14:]

# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['user', 'seconds remaining']
    live_data['data'] = []

    import login
    list_names = login.get_active_users()
    for user_name in sorted(list_names.keys()):
        live_data['data'].append({'user': user_name, 'seconds remaining': list_names[user_name]})

    if False:
        live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

        live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

        live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
        live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # later python 3
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    url_to_use = make_home_url_from_environ(environ)

    get_content = ''
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':
            the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
            the_type = ''
            if 'type' in query_items:
                the_type = query_items['type']

            if the_type == 'issues':
                live_data = get_live_data()

                the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

                the_data['headers'] = live_data['headers']

                for item in live_data['data']:
                    row_content = []
                    row_links = []
                    row_colors = []
                    for header_name in the_data['headers']:
                        try:
                            item_content = item[header_name]
                        except:
                            item_content = ''

                        try:
                            item_link = item[header_name + '_link']
                        except:
                            item_link = ''

                        try:
                            item_color = item[header_name + '_color']
                        except:
                            item_color = ''

                        row_content.append(item_content)
                        row_links.append(item_link)
                        row_colors.append(item_color)

                    the_data['rows'].append(row_content)
                    the_data['links'].append(row_links)
                    the_data['color'].append(row_colors)
            else:
                # echo it back out, so that we can see it
                for key in query_items.keys():
                    the_data['headers'].append(key)
                    the_data['rows'].append([query_items[key]])

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other
        else:
            if 'contents' in query_items:
                view = 'user'
                if 'view' in query_items:
                    view = query_items['view']

                log_to_write = query_items['contents']
                user_path = base_log_path + '/attempts/' + log_to_write + '/'

                if ('user' in query_items) and ('log' in query_items):
                    body = ''

                    # put some (hopefully) useful text here
                    body += make_ldap_codes_text()

                    path_to_file = user_path + query_items['user'] + '/' + query_items['log']
                    if os.path.isfile(path_to_file):
                        body += open(path_to_file, 'r').read().replace('\n','<br>')
                    else:
                        body += 'path not found: ' + path_to_file
                else:
                    users_list = os.listdir(user_path)
                    dictionary_of_users = {}
                    for user in users_list:
                        dictionary_of_users[user] = {'logs':os.listdir(user_path + user)}
                    log_url = url_to_use + '/' + service + '?contents=' + log_to_write

                    body = ''
                    body += '<center>'

                    if view == 'user':
                        body += '<table border="1" cellpadding="5">'
                        for user in sorted(dictionary_of_users.keys()):
                            body += '<tr>'
                            body += '<td>'
                            body += user + '<br>' + '(' + str(len(dictionary_of_users[user]['logs'])) + ')'
                            body += '</td>'
                            body += '<td>'
                            for log_time_stamp in sorted(dictionary_of_users[user]['logs']):
                                log_to_pull_url = log_url + ',user=' + user + ',log=' + log_time_stamp
                                body += '<a href="' + log_to_pull_url + '" style="text-decoration:none;color:inherit">' + make_timestamp_human_readable(log_time_stamp) + '</a>' + '<br>'
                            body += '</td>'
                            body += '</tr>'

                    if view == 'date':
                        body += 'by date, most recent at the top' + '<br><br>'
                        body += '<table border="1" cellpadding="5">'
                        if log_to_write == 'valid':
                            user_allowed_logs = permissions.get_user_allowed_logs()
                        else:
                            user_allowed_logs = {}
                        by_date = make_reportables(user_allowed_logs, dictionary_of_users)

                        for day in sorted(by_date.keys(),reverse=True):
                            body += '<tr>'
                            body += '<td>'

                            body += '<table border="1" cellpadding="5">'
                            for log_time_stamp in sorted(by_date[day].keys(),reverse=True):
                                for user_d in by_date[day][log_time_stamp]:
                                    user = user_d['user']
                                    report = user_d['report'].replace(', ','<br>')
                                    if 'action_report' in user_d:
                                        action_report = '\n'.join(user_d['action_report']).replace('\n','<br>').replace(' ','&nbsp;')
                                    else:
                                        action_report = ''

                                    body += '<tr>'
                                    body += '<td>'
                                    log_to_pull_url = log_url + ',user=' + user + ',log=' + log_time_stamp
                                    body += '<a href="' + log_to_pull_url + '" style="text-decoration:none;color:inherit">' + make_timestamp_human_readable(log_time_stamp) + '</a>' + '<br>'
                                    body += '</td>'
                                    body += '<td>'
                                    body += user
                                    body += '</td>'
                                    body += '<td>'
                                    body += report
                                    body += '</td>'
                                    body += '<td>'
                                    body += '<tt>'
                                    body += action_report
                                    body += '</tt>'
                                    body += '</td>'
                                    body += '</tr>'

                            body += '</table>'

                            body += '</td>'
                            body += '</tr>'

                    body += '</table>'
                    body += '</center>'
                    body += '<br>'

                    if False: # only make this true for debug
                        body += '<tt>'
                        for day in sorted(by_date.keys(),reverse=True):
                            for log_time_stamp in sorted(by_date[day].keys(),reverse=True):
                                for user_d in by_date[day][log_time_stamp]:
                                    user = user_d['user']
                                    body += '<br>' + day + '    ' + log_time_stamp + "    " + user

                        body += '<br><br>'
                        body += '<br>-----------------------------------------------------<br>'

                        for user in sorted(user_allowed_logs.keys()):
#                            if (user != '***********') and (user != 'david.ferguson'):
                            if (user != '***********'):
                                body += '<br><br>' + user + '    ' + json.dumps(user_allowed_logs[user])
#                            else:
#                                del(user_allowed_logs[user])

                        body += '<br><br>'
                        body += '<br>-----------------------------------------------------<br>'

                    if False:
                        for user in sorted(dictionary_of_users.keys()):
#                            if (user != '***********') and (user != 'david.ferguson'):
                            if (user != '***********'):
                                body += '<br><br>' + user + '    ' + json.dumps(dictionary_of_users[user])
#                            else:
#                                del(dictionary_of_users[user])

                        body += '<br><br>'
                        body += '<br>-----------------------------------------------------<br>'
                        by_date = make_reportables(user_allowed_logs, dictionary_of_users)

                        body += '<br><br>' + json.dumps(by_date, indent=4, separators=(',', ':')).replace('\n','<br>').replace(' ','&nbsp')

                        body += '</tt>'



            else:
                # main page
                other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

                load_url = url_to_use + '/' + service + '?get=data,type=issues'

                live_table_content_d = make_live_table_content(load_url)
                other['head'] = live_table_content_d['head']

                body = ''

                body += """
            <script>

            function URLjump(jumpLocation) {
                location.href = jumpLocation;
            }

            </script>
                """

                name_to_show = "Home"
                onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
                body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

                body += """
            <script>

            function URLjump(jumpLocation) {
                location.href = jumpLocation;
            }

            </script>
                """

                #    name_to_show = "Home"
                #    url_to_use = "https://slicer.cardinalhealth.net"
                #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
                #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

                body += '<center>'
                body += version
                body += '</center>'

                body += '<br><br>'
                body += '<center>'
                body += '<a href="' + url_to_use + '/' + service + '?contents=notvalid' + '">' + 'list not-valid' + '</a>'
                body += '<br><br>'
                body += '<a href="' + url_to_use + '/' + service + '?contents=notvalid,view=date' + '">' + 'list not-valid by date' + '</a>'
                body += '<br><br>'
                body += '<a href="' + url_to_use + '/' + service + '?contents=valid' + '">' + 'list valid' + '</a>'
                body += '<br><br>'
                body += '<a href="' + url_to_use + '/' + service + '?contents=valid,view=date' + '">' + 'list valid by date' + '</a>'
                body += '</center>'

                # ------------------------------------
                # Live dashboard view
                # ------------------------------------
                dashboard = ''
                dashboard += live_table_content_d['body']
                dashboard += live_table_content_d['javascript']

                body += dashboard

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    # if permissions.permission_prefix_allowed(environ, service + '_'): # or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)

        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_logins(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_make_timestamp_human_readable(self):
        timestamp = '20240423204414272967'
        expected = '2024.04.23 20:44:14.272967'
        actual = make_timestamp_human_readable(timestamp)
        self.assertEqual(expected, actual)

    def test_make_reportables(self):
        self.maxDiff = None

        user_allowed_logs = {}
        dictionary_of_users = {}
        expected = {}
        actual = make_reportables(user_allowed_logs, dictionary_of_users)
        self.assertEqual(expected, actual)

        user_allowed_logs = {}
        dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456']}}
        expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':''}]}}
        actual = make_reportables(user_allowed_logs, dictionary_of_users)
        self.assertEqual(expected, actual)

        # Want to show the activity on the day it happens, even if there was a prior login
        user_allowed_logs = {'david.ferguson':{'20250119181109123455':'template,GET'}}
        dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456']}}
        expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':''}]}, '20250119':{'20250119000000000000':[{'user':'david.ferguson','report':'template (1 GET)'}]}}
        actual = make_reportables(user_allowed_logs, dictionary_of_users)
        self.assertEqual(expected, actual)

        if True:

            user_allowed_logs = {}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':''}], '20250110181109123458':[{'user':'david.ferguson','report':''}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            # start adding allowed logs
            user_allowed_logs = {'david.ferguson':{'20250110181109123455':'template,GET'}} # time is before a login that was found
            # change our mind, and want this to show, as you might have been logged in from the day before, and working past the rollover
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110000000000000':[{'user':'david.ferguson','report':'template (1 GET)'}], '20250110181109123456':[{'user':'david.ferguson','report':''}], '20250110181109123458':[{'user':'david.ferguson','report':''}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson':{'20250110181109123457':'template,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':'template (1 GET)'}], '20250110181109123458':[{'user':'david.ferguson','report':''}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson':{'20250110181109123459':'template,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':''}], '20250110181109123458':[{'user':'david.ferguson','report':'template (1 GET)'}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson':{'20250110181109123457':'template2,POST', '20250110181109123459':'template,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':'template2 (1 POST)'}], '20250110181109123458':[{'user':'david.ferguson','report':'template (1 GET)'}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson':{'20250110181109123457':'template,POST', '20250110181109123459':'template,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123460']}}
            expected = {'20250110':{'20250110181109123456':[{'user':'david.ferguson','report':'template (1 GET)(1 POST)'}], '20250110181109123460':[{'user':'david.ferguson','report':''}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson': {'20250113193038194189': 'template,GET', '20250113193359119240': 'address2location,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123460']}}
            expected = {"20250110": {"20250110181109123460": [{"user": "david.ferguson", "report": ""}]}, "20250113": {"20250113000000000000": [{"user": "david.ferguson", "report": "address2location (1 GET), template (1 GET)"}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            user_allowed_logs = {'david.ferguson': {'20250113193038194189': 'template,GET', '20250113193359119240': 'address2location,GET\nquery: test123\n  junk123'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123460']}}
            #expected = {'20250110':{'20250110181109123460':{'user':'david.ferguson','report':'address2location (1 GET), template (1 GET)', 'action_report':['query: test123\n  junk123']}}}
            expected = {"20250110": {"20250110181109123460": [{"user": "david.ferguson", "report": ""}]}, "20250113": {"20250113000000000000": [{"user": "david.ferguson", "report": "address2location (1 GET), template (1 GET)", "action_report": ["query: test123\n  junk123"]}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            # We need the IP address to appear as a user, with a midnight login timestamp
            user_allowed_logs = {'*************':{'20250110181109123455':'template,GET'}}
            dictionary_of_users = {'david.ferguson':{'logs':['20250110181109123456','20250110181109123458']}}
            expected = {'20250110':{'20250110000000000000':[{'user':'*************','report':'template (1 GET)'}], '20250110181109123456':[{'user':'david.ferguson','report':''}], '20250110181109123458':[{'user':'david.ferguson','report':''}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)


            # We need multiple IP address to appear as a user, with a midnight login timestamp
            user_allowed_logs = {'*************':{'20250110181109123455':'template,GET'}, '*************':{'20250110181109123456':'template,GET'}}
            dictionary_of_users = {}
            expected = {'20250110':{'20250110000000000000':[{'user':'*************','report':'template (1 GET)'}, {'user':'*************','report':'template (1 GET)'}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
            self.assertEqual(expected, actual)

            # We need IP address to appear as a user, with a midnight login timestamp, and a user
            user_allowed_logs = {'*************':{'20250110181109123455':'template,GET'}, 'david.ferguson': {'20250110181109123455': 'template,GET'}}
            dictionary_of_users = {}
            expected = {'20250110':{'20250110000000000000':[{'user':'*************','report':'template (1 GET)'}, {'user':'david.ferguson','report':'template (1 GET)'}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
    #        self.assertEqual(json.dumps(expected), json.dumps(actual))
            self.assertEqual(expected, actual)

            # We need multiple IP address to appear as a user, with a midnight login timestamp, and a user
            user_allowed_logs = {'*************':{'20250110181109123455':'template,GET'}, '***********':{'20250110181109123456':'template,GET'}, 'david.ferguson': {'20250110181109123455': 'template,GET'}}
            dictionary_of_users = {}
            expected = {'20250110':{'20250110000000000000':[{'user':'*************','report':'template (1 GET)'}, {'user':'***********','report':'template (1 GET)'}, {'user':'david.ferguson','report':'template (1 GET)'}]}}
            actual = make_reportables(user_allowed_logs, dictionary_of_users)
    #        self.assertEqual(json.dumps(expected), json.dumps(actual))
            self.assertEqual(expected, actual)



# End of source file
