Sections:
1) Known Issues
2) Configuration Notes
3) Release Notes

------------------------
1) Known Issues:
------------------------
2023.11.01
Screen resolution not correctly automatically detecting the screen attached:
    Sometimes the default of "automatically detect" results in the screen being oversized,
        undersized, and generally not correct. This can happen, as a test, by unplugging the
        hdmi cable from the pi, power off, then back on, then ater a minute, plug the hdmi
        cable back in.
    The fix, is to reboot the device, twice. This can be accomplished remotely, in the Slicer
        interface: browse to the device configuration page, like,
        https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf

        then find the "Request Reboot" line, find the drop down, select the reboot marker value,
        then click "Submit". Wait a few minutes, refresh the Slicer page, to get a new reboot
        marker number, then request another reboot. Wait a few minutes, and then check that
        the device has the correct screen showing (filled to edges, not over-filled).

------------------------
2) Configuration notes:
------------------------

To configure printing on the pi (base image 2.3.5 and later)
------------------------
On main page, click the bold "ID" in upper left corner.
On the line with "Printer", click the bold "configure".
Administration -> Add Printer -> Username "printer" -> password "printer".
When done, press Alt F4 to return.
Click bold "ID", to return to main page.

------------------------
3) Target Service Pack
------------------------
2024.02.17 Bring all up to this level.
Target SP = SP.47

optional_service = pi_thirdparty, P.0.0 to P.9.9

------------------------
3) Rollback Service Pack
------------------------
Rollback SP = SP.47

The rollback service pack is the lowest version that is allowed to be rolled back to,
both for the bunch/collection, as well as individual services.

------------------------
4) Release Notes (Most recent at the top)
------------------------
2025.06.06
(SP.59)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
On start, check if cleanup of old chromium data needs to occur
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.7 - pi_logging
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.11 - pi_runner - On start, check if cleanup of old chromium data needs to occur
    S.2.2 - pi_security
    s.1.1 - pi_settings

2025.05.18
(SP.58)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Increment screen grab, even if there are errors on sending the image
Logging change to report and clear the profile log
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.7 - pi_logging - Logging change to report and clear the profile log
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.10 - pi_runner
    S.2.2 - pi_security
    s.1.1 - pi_settings

2025.04.30
(SP.57)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Increment screen grab, even if there are errors on sending the image
More logging cleanups, multiple reboots option, screen reporting double check on large values,
report browser up time.
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.6 - pi_logging
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.10 - pi_runner - Option for multiple boots on each startup; Screen report refinement, to reduce false positive 90 or greater reports. Report browser up time.
    S.2.2 - pi_security
    s.1.1 - pi_settings

2025.03.12
(SP.56)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Increment screen grab, even if there are errors on sending the image
Screen Grab counting fix
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.5 - pi_logging
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.7 - pi_runner - Increment screen grab, even if there are errors on sending the image
    S.2.2 - pi_security
    s.1.1 - pi_settings

2025.01.24
(SP.55)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Remove temporary files before writing new ones
Vacuum the journal down to 5M, periodically.
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi
    L.3.5 - pi_logging - Vacuum the journal down to 5M, periodically.
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.6 - pi_runner
    S.2.2 - pi_security
    s.1.1 - pi_settings

2025.01.24
(SP.54)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Remove temporary files before writing new ones
Allow for text based dynamic content page to be built (Display Board)
    B.2.8 - pi_bluetooth
    C.1.3 - pi_config
    H.5.2 - pi_hmi - allow dynamic page content to have slash (/) and question mark (?)
    L.3.4 - pi_logging
    M.2.8 - pi_monitor
    N.7.9 - pi_network
    O.1.2 - pi_organization
    R.9.6 - pi_runner
    S.2.2 - pi_security
    s.1.1 - pi_settings

2024.12.16
(SP.53)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
    - Remove temporary files before writing new ones
Allow for text based dynamic content page to be built (Display Board)
    B.2.8 - pi_bluetooth - support new/latest pi OS
    C.1.3 - pi_config
    H.5.1 - pi_hmi - allow for text based dynamic content page to be built; remove temporary files before writing new ones
    L.3.4 - pi_logging
    M.2.8 - pi_monitor
    N.7.9 - pi_network - remove temporary files before writing new ones
    O.1.2 - pi_organization
    R.9.6 - pi_runner - remove temporary files before writing new ones
    S.2.2 - pi_security
    s.1.1 - pi_settings

2024.11.12
(SP.52)
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
Change the time setting to set localtime instead of timezone
    B.2.8 - pi_bluetooth - support new/latest pi OS
    C.1.3 - pi_config
    H.5.0 - pi_hmi
    L.3.4 - pi_logging
    M.2.8 - pi_monitor
    N.7.8 - pi_network
    O.1.2 - pi_organization
    R.9.5 - pi_runner - Change how the timezone gets set
    S.2.2 - pi_security
    s.1.1 - pi_settings

2024.04.18
(SP.51)
Built on new base image 2.4.2
Known issues:
    - MP4 playback audio does not work to HDMI when using a Pi4, but does work with a Pi5
    - Screen resolution is not yet settable from a Slicer configuration.
Support screen reporting, looking for resolution issues
    B.2.8 - pi_bluetooth - support new/latest pi OS
    C.1.3 - pi_config
    H.5.0 - pi_hmi
    L.3.4 - pi_logging
    M.2.8 - pi_monitor
    N.7.8 - pi_network
    O.1.2 - pi_organization
    R.9.4 - pi_runner - send back OS version, and bluez version to Slicer
    S.2.2 - pi_security
    s.1.1 - pi_settings

2024.04.01
(SP.50)
Support screen reporting, looking for resolution issues
    B.2.8 - pi_bluetooth - support new/latest pi OS (Backport from later release)
    C.1.3 - pi_config
    H.5.0 - pi_hmi
    L.3.4 - pi_logging
    M.2.8 - pi_monitor
    N.7.8 - pi_network - Support new NetworkManger handling of wired connection
    O.1.2 - pi_organization
    R.9.3 - pi_runner - Screen reporting, clear timezone, installs for Pi5
    S.2.2 - pi_security
    s.1.1 - pi_settings

2024.03.12
(SP.49)
Services support for Raspberry Pi5 (python3.x)
    B.2.8 - pi_bluetooth - support new/latest pi OS (Backport from later release)
    C.1.3 - pi_config - Support Raspberry Pi5
    H.5.0 - pi_hmi - Support Raspberry Pi5
    L.3.4 - pi_logging - Support Raspberry Pi5
    M.2.8 - pi_monitor - Support Raspberry Pi5
    N.7.7 - pi_network - Support Raspberry Pi5
    O.1.2 - pi_organization - Support Raspberry Pi5
    R.9.1 - pi_runner
    S.2.2 - pi_security - Support Raspberry Pi5
    s.1.1 - pi_settings - Support Raspberry Pi5

2024.03.11
(SP.48)
Support mp4 for multimedia shows
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.9 - pi_hmi
    L.3.3 - pi_logging
    M.2.7 - pi_monitor
    N.7.6 - pi_network
    O.1.1 - pi_organization
    R.9.1 - pi_runner - support mp4 for shows content
    S.2.1 - pi_security
    s.1.0 - pi_settings - clean out old settings

2024.02.06
(SP.47)
Runner update to report IP address
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.9 - pi_hmi - show management block ID; IP address source change
    L.3.3 - pi_logging
    M.2.7 - pi_monitor -  handle python 3.x; IP address source change
    N.7.6 - pi_network
    O.1.1 - pi_organization
    R.9.0 - pi_runner - handle python 3.x; report IP address on periodic update
    S.2.1 - pi_security
    s.0.7 - pi_settings

2023.10.25
(SP.46)
Network connection monitoring, and elimination of corp WiFi from the codebase.
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.3 - pi_logging
    M.2.4 - pi_monitor
    N.7.6 - pi_network - manage network connection state, and eliminate corp WiFi from codebase
    O.1.1 - pi_organization
    R.8.8 - pi_runner - Support extended display setting values
    S.2.1 - pi_security
    s.0.7 - pi_settings

2023.09.18
(SP.45)
Network service now reporting the count of nic (network interface connection) changes between internal and external
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.3 - pi_logging
    M.2.4 - pi_monitor
    N.7.4 - pi_network - report nic change count (since imaged)
    O.1.1 - pi_organization
    R.8.7 - pi_runner
    S.2.1 - pi_security
    s.0.7 - pi_settings

2023.08.21
(SP.44)
Activity tracking now correctly ignores input devices that are in a non-connected state.
Logging will now report the device names for all found devices.
On network config page, show connection rate with the channel number
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.3 - pi_logging - report device names
    M.2.4 - pi_monitor
    N.7.3 - pi_network
    O.1.1 - pi_organization
    R.8.7 - pi_runner - activity monitoring ignore disconnected inputs, and start with age of one day
    S.2.1 - pi_security
    s.0.7 - pi_settings

2023.07.03
(SP.43)
On network config page, show connection rate with the channel number
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.2 - pi_logging
    M.2.4 - pi_monitor
    N.7.3 - pi_network - show connection speeds
    O.1.1 - pi_organization
    R.8.4 - pi_runner
    S.2.1 - pi_security
    s.0.7 - pi_settings

2023.05.31
(SP.42)
Update bluetooth configuration 2D barcode, to ignore caps lock
    B.2.6 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.2 - pi_logging - clean up /var/log/wtmp also
    M.2.4 - pi_monitor
    N.7.2 - pi_network
    O.1.1 - pi_organization
    R.8.4 - pi_runner - add logging of the last active user device
    S.2.1 - pi_security - support older base images
    s.0.7 - pi_settings

2023.05.02
(SP.41)
Update bluetooth configuration 2D barcode, to ignore caps lock
    B.2.6 - pi_bluetooth - updated configuration 2D barcode
    C.1.2 - pi_config
    H.4.7 - pi_hmi
    L.3.1 - pi_logging
    M.2.4 - pi_monitor
    N.7.2 - pi_network
    O.1.1 - pi_organization
    R.8.3 - pi_runner
    S.2.0 - pi_security
    s.0.7 - pi_settings

2023.04.03
(SP.40)
Remove corp WiFi.
    B.2.5 - pi_bluetooth
    C.1.2 - pi_config
    H.4.7 - pi_hmi - remove option to switch to corp
    L.3.1 - pi_logging - Periodically clear chromium history to save drive space
    M.2.4 - pi_monitor
    N.7.2 - pi_network - Remove corp wifi as a connection option
    O.1.1 - pi_organization
    R.8.3 - pi_runner - Log and report configuration changes
    S.2.0 - pi_security
    s.0.7 - pi_settings - Remove corp wifi cert

2023.02.07
(SP.39)
Support remote commands (2023.02.15 bumped R.8.1 to R.8.2 to handle upgrade from really early images)
    B.2.5 - pi_bluetooth
    C.1.2 - pi_config
    H.4.6 - pi_hmi
    L.2.9 - pi_logging
    M.2.4 - pi_monitor
    N.7.1 - pi_network - corp wifi cert to 2024.02.06
    O.1.1 - pi_organization
    R.8.2 - pi_runner - remote commands from Slicer, reset corp wifi on forced browser reset, install apscheduler if it is missing
    S.2.0 - pi_security
    s.0.6 - pi_settings - corp wifi cert to 2024.02.06

2023.01.27
(SP.38)
Support remote commands
    B.2.5 - pi_bluetooth
    C.1.2 - pi_config
    H.4.6 - pi_hmi
    L.2.9 - pi_logging
    M.2.4 - pi_monitor
    N.6.9 - pi_network
    O.1.1 - pi_organization
    R.8.0 - pi_runner - remote commands from Slicer
    S.2.0 - pi_security
    s.0.5 - pi_settings

2022.12.07
(SP.37)
Better ssh rules definition for the case of multiple address configuration.

    B.2.5 - pi_bluetooth
    C.1.2 - pi_config - support manual configuration of a generic image
    H.4.6 - pi_hmi - if manual settings are present, then use them, otherwise default to original
    L.2.9 - pi_logging - make a privoxy log by day, that is viewable on the pi
    M.2.4 - pi_monitor
    N.6.8 - pi_network - if manual network settings are found, then use those
    O.1.0 - pi_organization - set the company specifics from settings
    R.7.7 - pi_runner - handle manual settings, handle multiple call_homes cleanly, add timeout on get_cpu_temperature
    S.2.0 - pi_security - pull from settings, to add sshd target(s) and allowed user(s)
    s.0.5 - pi_settings - the one source of configuration settings

2022.12.05
(SP.36)
Support manual image configuration at the pi.

    B.2.5 - pi_bluetooth
    C.1.2 - pi_config - support manual configuration of a generic image
    H.4.6 - pi_hmi - if manual settings are present, then use them, otherwise default to original
    L.2.9 - pi_logging - make a privoxy log by day, that is viewable on the pi
    M.2.4 - pi_monitor
    N.6.8 - pi_network - if manual network settings are found, then use those
    O.0.3 - pi_organization - set the company logo
    R.7.6 - pi_runner - handle manual settings, handle multiple call_homes cleanly, add timeout on get_cpu_temperature
    S.1.9 - pi_security - handle multiple call_homes cleanly

2022.10.11
(SP.35)
External Radio improved support.

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.1 - pi_config - handle multiple call home entries
    H.4.4 - pi_hmi - show more network details on page 2, and int/ext on page1
    L.2.7 - pi_logging
    M.2.4 - pi_monitor
    N.6.7 - pi_network - better external radio support
    R.7.4 - pi_runner - report which radio is being used
    S.1.8 - pi_security - allow ssh from any configure call home entry

2022.08.24
(SP.34)
Runner reports Chromium version.

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.0 - pi_config
    H.4.2 - pi_hmi - zero reset when updated
    L.2.7 - pi_logging
    M.2.4 - pi_monitor
    N.5.4 - pi_network
    R.7.0 - pi_runner - report Chromium Browser version
    S.1.7 - pi_security

2022.06.28
(SP.33)
Runner tracks user activity more accurately. (drops removed devices)

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.0 - pi_config
    H.4.2 - pi_hmi - zero reset when updated
    L.2.7 - pi_logging
    M.2.4 - pi_monitor
    N.5.4 - pi_network
    R.6.9 - pi_runner - zero reset when updated
    S.1.7 - pi_security

2022.06.01
(SP.32)
Home page shows if a browser reset is wanted.
Logging cleans up old logs.

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.0 - pi_config
    H.4.2 - pi_hmi - zero reset when updated
    L.2.7 - pi_logging
    M.2.4 - pi_monitor
    N.5.4 - pi_network
    R.6.8 - pi_runner - zero reset when updated
    S.1.7 - pi_security

2022.04.25
(SP.31)
WiFi coaster on access point change (hold network settings for a time period)

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.0 - pi_config
    H.4.1 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.4 - pi_network
    R.6.4 - pi_runner
    S.1.7 - pi_security

2022.04.22
(SP.30)
Bluetooth 4 digit list, and bluetooth rescan link

Includes following versions of pi_* services:
    B.2.5 - pi_bluetooth
    C.1.0 - pi_config
    H.4.1 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.3 - pi_network
    R.6.1 - pi_runner
    S.1.7 - pi_security

2022.04.20
(SP.29)
Network WiFi Hopper option to jump to closest access point

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.4.1 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.3 - pi_network
    R.6.1 - pi_runner
    S.1.7 - pi_security

2022.03.11
(SP.28)
Updates on HMI to show Service Pack as SP, instead of showing image version.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.4.0 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.6.1 - pi_runner
    S.1.7 - pi_security

2022.03.07
2.4.1 (SP.27)
Networking service now starts from zero connections each startup, to get clean onto corp.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.2 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security

Image:
    (no change)

2022.03.02
2.4.0 (SP.26)
Networking service now has a new certificate to connect to corp WiFi, that expires March 2, 2023

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.3.9 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.5.0 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security

Image:
    (no change)

2022.02.21
2.3.5 (SP.25)
Wireless network connection to IOT supported as a manual configuration choice (internal and external radio).
New HMI options: Slideshow mode. Auto page refresh mode. Interact with local pi_config page.

Start with 2.3.4, and update with all current services.

Includes following versions of pi_* services:
    B.2.4 - pi_bluetooth
    C.1.0 - pi_config
    H.3.8 - pi_hmi
    L.2.4 - pi_logging
    M.2.3 - pi_monitor
    N.4.0 - pi_network
    R.5.9 - pi_runner
    S.1.7 - pi_security

Image: This includes CUPS (printing), which was stepped over in 2.3.3.

2022.01.14
2.3.4 (SP.24)
Wireless network connection to IOT supported as a manual configuration choice (internal and external radio).
New HMI options: Slideshow mode. Auto page refresh mode.

Includes following versions of pi_* services:
    B.2.3 - pi_bluetooth
    H.3.6 - pi_hmi
    L.2.0 - pi_logging
    M.2.2 - pi_monitor
    N.4.0 - pi_network
    R.5.7 - pi_runner
    S.1.6 - pi_security

Image: This re-includes CUPS (printing), which was stepped over previously.

2021.12.21
2.3.3 (SP.23)
External WiFi supported.
IOT temporarily disabled.
Printing support.

Includes following versions of pi_* services:
    B.2.2 - pi_bluetooth
    H.3.1 - pi_hmi
    L.1.8 - pi_logging
    M.2.2 - pi_monitor
    N.3.1 - pi_network
    R.5.0 - pi_runner
    S.1.6 - pi_security

Image: This re-includes CUPS (printing), which was stepped over previously.
Started with 2.3.1 image, and bring all services forward, except network, leaving it at N.3.1


------------------------
2021.11.29
2.3.2 (SP.22)
Network: If the identified lan changes (wlan0, wlan1, eth0), then send new status.
Network: Fix the reporting, to correctly show wlanX, when connected.
Network:Add support to allow wlan1 (external wifi adapter) to be configured
Network:Add to the network report: the adapter providing the access (wlan0, wlan1, eth0, ...).

Includes following versions of pi_* services:
    B.2.2 - pi_bluetooth
    H.3.1 - pi_hmi
    L.1.8 - pi_logging
    M.2.2 - pi_monitor
    N.3.6 - pi_network
    R.4.1 - pi_runner
    S.1.6 - pi_security

eth0 is the wired network ethernet port on the pi
wlan0 is the internal WiFi radio
wlan1 is any external WiFi radio

Image:
    CUPS installed. User printer:printer


------------------------
2021.10.14
2.3.1 (SP.21)
Allow Pi OS updates, on request in Slicer.
Collect network usage data.
Do not let USB devices sleep.
Support browser restart on inactivity.
OS update support.
Limit inbound to Slicer only.
System logging service active.
Bluetooth change to not show RSSI column, because we no longer collect that data.

Includes following versions of pi_* services:
    B.2.2 - pi_bluetooth (Do not show RSSI column, because we no longer collect that data)
    M.2.2 - pi_monitor   (same as before)
    R.3.8 - pi_runner    (Be able to pull service updates from Slicer. Do not let USB devices sleep. Support browser inactivity restart. Network monitoring.)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.5 - pi_security  (scan for updates. Allow Slicer to have us take updates. Limit inbound to Slicer only.)
    L.1.4 - pi_logging   (system activity logging)

Image:
    (no change)


------------------------
2021.08.31
2.2.7 (SP.20)
Make changes for bluetooth in crowded scanner environment, and many pis looking for connection.

Includes following versions of pi_* services:
    B.2.1 - pi_bluetooth (Eliminate info request during discovery, to not over communicate with devices.)
    M.2.2 - pi_monitor   (same as before)
    R.2.4 - pi_runner    (same as before)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.30
2.2.6 (SP.19)
Screen grab of pi display, with delivery back to Slicer.

Includes following versions of pi_* services:
    B.2.0 - pi_bluetooth (same as before)
    M.2.2 - pi_monitor   (same as before)
    R.2.4 - pi_runner    (Implement screen grab, and reporting back to Slicer.)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.26
2.2.5 (SP.18)
Bluetooth change to be more flexible when making a new connection.

Includes following versions of pi_* services:
    B.2.0 - pi_bluetooth (In area with lots of bluetooth devices, allow more flexibility on timing when making a new connection.)
    M.2.2 - pi_monitor   (same as before)
    R.2.3 - pi_runner    (same as before)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.24
2.2.4 (SP.17)
Bluetooth change to properly recognize the user click for the pairing request.

Includes following versions of pi_* services:
    B.1.9 - pi_bluetooth (Correctly look at all devices in the selection list, to see which one is picked.)
    M.2.2 - pi_monitor   (same as before)
    R.2.3 - pi_runner    (same as before)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.20
2.2.3 (SP.16)
Bluetooth change to limit number of device transactions; in test of PR005 scanners.

Includes following versions of pi_* services:
    B.1.8 - pi_bluetooth (Only enact trust on the one device we are try to pair to.)
    M.2.2 - pi_monitor   (same as before)
    R.2.3 - pi_runner    (same as before)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.18
2.2.2 (SP.15)
Backoff the timing for polling bluetooth, based on how many devices are found; 60 seconds max.

Includes following versions of pi_* services:
    B.1.7 - pi_bluetooth (Poll at 5 seconds until one device, then add 5 seconds for each device, max 60.)
    M.2.2 - pi_monitor   (same as before)
    R.2.3 - pi_runner    (same as before)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.17
2.2.1 (SP.14)
Adjust the screen size for the browser based on current 'fbset -s' size, instead of chromium screen size.
(Addresses the issue with Dell P2219H display)

Includes following versions of pi_* services:
    B.1.6 - pi_bluetooth (same as before)
    M.2.2 - pi_monitor   (same as before)
    R.2.3 - pi_runner    (Use fbset to adjust the browser size to fill the screen.)
    H.2.0 - pi_hmi       (same as before)
    N.2.0 - pi_network   (same as before)
    S.1.0 - pi_security  (same as before)

Image:
    (no change)

------------------------
2021.08.14
2.2.0 (SP.13)
Build second page with device info, by clicking on bolded ID title field.
Lock down the default account.
Add first round of security audit scanning.
Wait for screen resolution to be set before making screen settings on boot.
Allow support for disabling kiosk mode on the browser on the pi.
(This allows F11 to be used to switch back and forth to Full Screen mode)
(This allows the open keys to be used for 'ctrl tab' to move between tabs, even for the ERP key grabbed page at PR005, when exited from full screen mode)

Includes following versions of pi_* services:
    B.1.6 - pi_bluetooth (Extend the polling time, to be better where there are lots of devices)
    M.2.2 - pi_monitor   (same as before)
    R.2.2 - pi_runner    (Allow support for disabling kiosk mode. Add startup delay to let HMI settings settle.)
    H.2.0 - pi_hmi       (Show ID screen with MAC address, and other items. If not in kiosk mode, show prompt for F11 to exit full screen mode.)
    N.2.0 - pi_network   (Add support to show cah-iot ssid status on the ID screen. Not ready to connect to it yet, just supporting development.)
    S.1.0 - pi_security  (New service, to report on security issues)

Image:
    Install lynis security audit tool.
    Lock down the default account.

------------------------
2021.07.22
2.1.8 (SP.12)
Support logo options.

Includes following versions of pi_* services:
    B.1.5 - pi_bluetooth (same as before)
    M.2.2 - pi_monitor   (same as before)
    R.2.1 - pi_runner    (same as before)
    H.1.9 - pi_hmi       (Support logo options)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.07.20
2.1.7 (SP.11)
Remote Pi reboot from Slicer configuration page.
Allow full keyboard access, dependent on profile setting.
Build index page in a way that should always be able to load, to address the issue where it shows as missing sometimes.

Includes following versions of pi_* services:
    B.1.5 - pi_bluetooth (Add note to top of selection list, that only a single click is required)
    M.2.2 - pi_monitor   (same as before)
    R.2.1 - pi_runner    (Send more in checkin: uptime, boot_count. React to reboot request. React to setting of all keyboard access.)
    H.1.8 - pi_hmi       (If all keyboard access is allowed, show ctrl key hints in the tabbed homepage view.)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.07.13
2.1.6

Includes following versions of pi_* services:

Image:
    Fixes the issue where the HMI service was not correctly starting.

------------------------
2021.07.12
2.1.5 (SP.10)
Make a tab mode for the bookmarks and browser; go between using Alt 1, Alt 2, etc.
Allow Slicer to enable showing a Menu to show shutdown and reboot on the pi.
Screen resolution set by Slicer configuration.

Includes following versions of pi_* services:
    B.1.4 - pi_bluetooth (Once paired, turn off scan, to allow for fast data transfers)
    M.2.2 - pi_monitor   (same as before)
    R.2.0 - pi_runner    (Pulling out screen resolution setting, and show menu option)
    H.1.7 - pi_hmi       (Tabs mode for browser and Menu for shutdown/reboot)
    N.1.2 - pi_network   (same as before)

Image:
    Browser start pulled out of init, to all for it to be configured for tabs by pi_runner.


------------------------
2021.07.03
2.1.4 (SP.9)
More robust Bluetooth pairing and connection controls.
Bluetooth pairing is now initiated by the user picking address from a list.
Show name (serial number) in the pairing list.
Screen formatting changes, to work with many bookmarks, on a small screen.

Includes following versions of pi_* services:
    B.1.3 - pi_bluetooth (Connection method is now from a list, and status display is more compact)
    M.2.2 - pi_monitor   (same as before)
    R.1.8 - pi_runner    (same as before)
    H.1.6 - pi_hmi       (More compact for long bookmark lists)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.07.02
2.1.3 (SP.8)
More robust Bluetooth pairing and connection controls.
Bluetooth pairing is now initiated by the user picking address from a list.
Show name (serial number) in the pairing list.

Includes following versions of pi_* services:
    B.1.2 - pi_bluetooth (Connection method is now from a list)
    M.2.2 - pi_monitor   (same as before)
    R.1.8 - pi_runner    (same as before)
    H.1.5 - pi_hmi       (same as before)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.07.02
2.1.2 (SP.7)
More robust Bluetooth pairing and connection controls.
Bluetooth pairing is now initiated by the user picking from a list.

Includes following versions of pi_* services:
    B.1.1 - pi_bluetooth (Connection method is now from a list.)
    M.2.2 - pi_monitor   (same as before)
    R.1.8 - pi_runner    (same as before)
    H.1.5 - pi_hmi       (same as before)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.06.24
2.1.1 (SP.6)
Fix the clock reporting of am/pm time for the 12 hour rollover.

Includes following versions of pi_* services:
    B.1.0 - pi_bluetooth (same as before)
    M.2.2 - pi_monitor   (same as before)
    R.1.8 - pi_runner    (same as before)
    H.1.5 - pi_hmi       (Fix the AM/PM reporting in the 12 O'clock hours)
    N.1.2 - pi_network   (same as before)

Image:
    (no change)

------------------------
2021.06.23
2.1.0 (SP.5)
Add support for clock view on the landing page.

Includes following versions of pi_* services:
    B.1.0 - pi_bluetooth (same as before)
    M.2.2 - pi_monitor   (same as before)
    R.1.8 - pi_runner    (Summarize the allowlist violation log. Enable clock view. On timezone change, restart hmi.)
    H.1.4 - pi_hmi       (Add support for clock view. Add the screen zoom setting value to the screen resolution numbers.)
    N.1.2 - pi_network   (same as before)

Image:
    Set the keyboard to US, instead of the default GB.

------------------------
2021.06.21
2.0.9 (SP.4)
Adds support for the pairing of Zebra RS6000 bluetooth connected scanner.
This feature is hidden by default. Slicer must be set to enable Blue Tooth on each device..

Includes following versions of pi_* services:
    B.1.0 - pi_bluetooth (Support interfacing to Zebra RS6000 scanner)
    M.2.2 - pi_monitor (same as before)
    R.1.6 - pi_runner (Screen Height and Width sent to Slicer. Bluetooth Enable picked up from Slicer is saved locally.)
    H.1.3 - pi_hmi    (Add support for bluetooth pairing visual interface.)
    N.1.2 - pi_network (same as before)

Image:
    Adds the background scheduler required by the pi_bluetooth service.
    Drive HDMI output always. (Force hot-plug)
    Adds the screen capture driver scrot.

------------------------
2021.05.28
2.0.8  (SP.3)

Includes following versions of pi_* services:
    M.2.2 - pi_monitor (same as before)
    R.1.4 - pi_runner (extract a device name from the datadrop response)
                      (if the browser zoom level changes, then build the preferences, and
                        quit the browser, so that it restarts)
    H.1.2 - pi_hmi    (if name of device is given, then add a column next to ID, and show it)
    N.1.2 - pi_network (same as before)

Image:
    Enabled logging of proxy blocked sites. (log is "/var/log/privoxy/logfile", and starts automatically based on the config change)
    Move the deletion of the chrome Singleton to be at each browser start,
        this eliminates the error shown as
        "Profile appears to be in use by another Chromium process..."
    Add a file copy of browser preferences from a build preferences spot to the
        run preferences spot, just before launching the browser.

------------------------
2021.05.19
2.0.7 (SP.2)

Includes following versions of pi_* services:
    M.2.2 - pi_monitor (same as before)
    R.1.2 - pi_runner  (profile set timezone)
    H.1.1 - pi_hmi     (autolaunch of bookmark, after 25 seconds on the landing page)
    N.1.2 - pi_network (same as before)

The image creation now has the new google "disable translate" pop up setting enforced.
    This requires re-flashing the sd card.


------------------------
2021.05.18
2.0.6
Clear the chromium profile, before imaging, to not cause a conflict on new devices.

------------------------
2021.05.17
2.0.5 (SP.1)
Now with pi_runner installed, doing the bookmark pulls, instead of pi_network.
pi_runner also doing service pulls/installs, as given in the datadrop response from Slicer.
pi_network now actively managing configuring corp wifi, if not plugged to wired;
    if wired, remove corp. Actively remove any non-corp wifi, all the time.

Includes following versions of pi_* services:
    M.2.2 - pi_monitor
    R.1.1 - pi_runner
    H.1.0 - pi_hmi
    N.1.2 - pi_network

Image:
    Image fixed to have the worker user have permissions on the input devices, so that the keyboard and mouse work.

------------------------
2021.05.06
2.0.4
Now with the remote pull of bookmarks, using pi_network to perform that function.
It still requires David to manually put those bookmarks on the server, per device.

Includes following versions of pi_* services:

------------------------
2021.05.05
2.0.3
Clean up the hmi make of the allowlist for privoxy

Includes following versions of pi_* services:

------------------------
2021.05.05
2.0.2
Put the expansion of the filesystem back into pi_hmi

Includes following versions of pi_* services:

------------------------
2021.05.05
2.0.1
Now with allowlist enforced, and must be included in the bookmarks definition.

Includes following versions of pi_* services:

------------------------
2021.05.01
2.0.0
Initial test build

Includes following versions of pi_* services:
