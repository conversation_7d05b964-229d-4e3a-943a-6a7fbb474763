# A investigate service for slicer

service = "investigate"
version = service + '.0.1'

release_notes = """
2023.07.14
investigate.0.1

Start a project the take terminal dumps, and parse through, looking for interesting content.

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_ = """
This file gets loaded to:
/var/www/html/investigate.py

using:
sudo vi /var/www/html/investigate.py

It also requires:

sudo vi /etc/httpd/conf.d/python-investigate.conf
----- start copy -----
WSGIScriptAlias /investigate /var/www/html/investigate.py
----- end copy -----

sudo chown apache:apache /var/www/html/investigate.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/investigate-runner
sudo chmod +x /var/www/html/investigate-runner

# ===== begin: start file
#!/usr/bin/env python
import investigate
investigate.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/investigate-runner.service
sudo systemctl daemon-reload
sudo systemctl stop investigate-runner.service
sudo systemctl start investigate-runner.service
sudo systemctl enable investigate-runner.service

systemctl status investigate-runner.service

sudo systemctl restart investigate-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep investigate-runner

OR

tail -f /var/log/syslog | fgrep investigate-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/investigate-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import investigate; print(investigate.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/investigate

https://slicer.cardinalhealth.net/investigate?siteid=PR005

https://slicer.cardinalhealth.net/investigate?serial=100000002a5da842

https://slicer.cardinalhealth.net/investigate?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_investigate


"""

import copy
import traceback
import json
import os
import shlex
import shutil
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ----------------------------
def convert_site_to_star(site_raw):
    # ----------------------------
    site = site_raw.replace('https://', '').replace('http://', '')

    url = site.split(':')[0].split('/')[0]
    splits = url.split('.')

    count_items = len(splits)
    stars_to_make = count_items - 2

    is_all_digits = False
    if count_items == 4:
        is_all_digits = True
        for item in splits:
            if not item.isdigit():
                is_all_digits = False

    return_value = []
    if is_all_digits:
        return_value.append('.'.join(splits))
    else:
        if count_items == 1:
            # like wppr0059tterm11:8080
            return_value.append(site)

        if stars_to_make == 0:
            return_value.append(splits[count_items - 2] + '.' + splits[count_items - 1])
        else:
            for star in range(1, stars_to_make + 1):
                return_value.append('*.' * star + splits[count_items - 2] + '.' + splits[count_items - 1])

    return sorted(return_value)


# ----------------------------
def reduce_privoxy_day_report(the_day_report):
    # ----------------------------
    return_value = ''
    bypass_starring_sites = []

    starred_dict = {}
    for site in the_day_report.keys():
        starred = convert_site_to_star(site)
        for item in starred:
            starred_dict[item] = ''

        if 'storage.googleapis.com' in site:
            bypass_starring_sites.append(site.split(':')[0])

    for key in sorted(starred_dict.keys()):
        use_it = True
        if 'google' in key:
            use_it = False

        if 'gstatic' in key:
            use_it = False

        if use_it:
            if return_value:
                return_value += ','
            return_value += key

    for bypass_starring_site in bypass_starring_sites:
        if return_value:
            return_value += ','
        return_value += bypass_starring_site

    return return_value


# ----------------------------
def investigate_content(the_content, with_summary=False):
    # ----------------------------
    return_value = ''

    # right now, only consider privoxy log content buried somewhere in the text
    for line_found in the_content.split('\n'):

        if '{"days"' == line_found[0:7]:
            # this is a privoxy report
            all_reports = {}

            the_json_string = line_found.split('cah-pi-su')[0]
            the_json = json.loads(the_json_string)

            for day in sorted(the_json['days'].keys()):
                if return_value:
                    return_value += '\n'
                day_report = reduce_privoxy_day_report(the_json['days'][day])

                for result in day_report.split(','):
                    all_reports[result] = True

                return_value += day + ' => ' + day_report

            if with_summary:
                return_value += '\n'
                return_value += '\n'
                return_value += 'all: ' + ','.join(sorted(all_reports.keys()))

    return return_value


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    live_data = {}
    live_data['headers'] = ['param', 'value', 'test']
    live_data['data'] = []

    live_data['data'].append({'param': 's_get_count', 'value': s_get_count})

    live_data['data'].append({'param': 'link out', 'param_link': 'http://slicer.world'})

    live_data['data'].append({'param': 'color test yellow', 'param_color': '(255, 255, 100, 0.3)'})
    live_data['data'].append({'param': 'color test red', 'param_color': '(255, 100, 100, 0.3)'})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(' + service + ' status)'

    status = os.system('systemctl is-active --quiet ' + service + '-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "' + service + '-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    the_who = login.get_current_user(environ)

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    try:
        from cgi import parse_qs
    except:
        pass

    try:
        # After python 3.2, https://github.com/TileStache/TileStache/issues/367
        from urllib.parse import parse_qs
    except:
        pass

    d = parse_qs(request_body.decode('utf-8'))

    value_to_use = ''
    if 'the_selection' in d:
        if 'testvalue_text_set' == str(d['the_selection'][0]):
            try:
                if 'testvalue_text' in d:
                    # strip out any escape character, html markup open and close carrots, and turn vertical pipe into line break.
                    value_to_use = d['testvalue_text'][0]

                    result = ''
                    result += 'Result:\n'
                    result += '=========================================\n'
                    result += investigate_content(value_to_use, with_summary=True)
                    result += '\n=========================================\n'

                    return result.replace("\n", "<br>"), other
            except:
                pass
                return str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")), other

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_data(environ):
    # ====================================

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
    the_type = ''
    if 'type' in query_items:
        the_type = query_items['type']

    if the_type == 'issues':
        live_data = get_live_data()

        the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

        the_data['headers'] = live_data['headers']

        for item in live_data['data']:
            row_content = []
            row_links = []
            row_colors = []
            for header_name in the_data['headers']:
                try:
                    item_content = item[header_name]
                except:
                    item_content = ''

                try:
                    item_link = item[header_name + '_link']
                except:
                    item_link = ''

                try:
                    item_color = item[header_name + '_color']
                except:
                    item_color = ''

                row_content.append(item_content)
                row_links.append(item_link)
                row_colors.append(item_color)

            the_data['rows'].append(row_content)
            the_data['links'].append(row_links)
            the_data['color'].append(row_colors)
    else:
        # echo it back out, so that we can see it
        for key in query_items.keys():
            the_data['headers'].append(key)
            the_data['rows'].append([query_items[key]])

    return the_data


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count

    data_store_content = datastore.all_datastore()

    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    get_content = ''
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':
            the_data = make_body_data(environ)

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

            load_url = url_to_use + '/' + service + '?get=data,type=issues'

            live_table_content_d = make_live_table_content(load_url)
            other['head'] = live_table_content_d['head']

            body = ''

            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            #    name_to_show = "Home"
            #    url_to_use = "https://slicer.cardinalhealth.net"
            #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '<center>'
            body += version
            body += '</center>'

            if False:  # The live table view
                # ------------------------------------
                # Live dashboard view
                # ------------------------------------
                dashboard = ''
                dashboard += live_table_content_d['body']
                dashboard += live_table_content_d['javascript']

                body += dashboard

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    if False:
        body += '<br><br>'
        body += '<center>'
        body += 'environ'
        body += '<table border="1" cellpadding="5">'
        for item in sorted(environ.keys()):
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            body += '<td>'
            try:
                body += str(environ[item])
            except:
                body += '(na)'
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += 'logged in user'
        body += '<br><br>'
        body += str(login.get_current_user(environ))
        body += '<br><br>'
        body += '</center>'

    if False:
        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'test1'
        body += '</td>'
        body += '<td>'
        body += 'test2'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'

    if True:
        # =================================================
        # Configuration Items, with individual submit(s)
        # =================================================
        color_red_warning = "(255, 0, 0, 0.3)"
        color_yellow_caution = "(255, 255, 100, 0.3)"
        color_green = "(0, 255, 0, 0.3)"
        color_clear = "(0, 0, 0, 0.0)"
        color_purple = "(255, 0, 255, 0.3)"

        body_add = ''
        body_add += '<br><br>'
        if permissions.permission_prefix_allowed(environ, service + '_' + 'create'):
            all_d_list = []

            # ---------------
            # with submit
            # ---------------
            d = {}
            d['title'] = 'Log Data'
            d['option_value'] = "testvalue_text_set"
            d['text_field_name'] = "testvalue_text"
            d['current_value'] = ''
            d['field_type'] = 'textarea'
            d['values_to_show'] = None
            d['requires'] = []
            d['text_for_info_box'] = 'Paste log data into the field, then click run'
            d['color_for_info_box'] = color_clear
            d['submit_name'] = 'run'
            d['is_development'] = False
            all_d_list.append(copy.deepcopy(d))

            # ---------------
            # Build the content
            # ---------------
            body_add += '<center>'
            body_add += '<table border="1" cellpadding="5">'

            body_add += '<tr>'
            body_add += '<td>'
            body_add += '<B>Category</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Current<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>New<br>value</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>action</B>'
            body_add += '</td>'
            body_add += '<td>'
            body_add += '<B>Notes</B>'
            body_add += '</td>'
            body_add += '</tr>'

            for d in all_d_list:
                development_marker = ''
                use_it = True
                submit_name = 'Submit'
                if 'submit_name' in d:
                    submit_name = d['submit_name']

                if 'is_development' in d:
                    use_it = False
                    if d['is_development']:
                        if permissions.permission_allowed(environ, 'development_read'):
                            use_it = True
                            development_marker = '--- Development ---<br>'
                    else:
                        use_it = True

                if use_it:
                    body_add += '<tr>'
                    body_add += '<form method="post" action="">'
                    body_add += '<td>'
                    body_add += development_marker
                    body_add += d['title']
                    body_add += '</td>'

                    current_value = d['current_value']
                    if not current_value:
                        current_value = ''

                    body_add += '<td>'
                    body_add += development_marker
                    body_add += current_value
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker

                    body_add += '<select name="the_selection" id="the_selection" hidden>'
                    body_add += '<option value="' + d['option_value'] + '" selected>' + d['option_value'] + '</option>'
                    body_add += '</select>'
                    if d['values_to_show'] is None:
                        field_type = 'text'
                        if 'field_type' in d:
                            field_type = d['field_type']

                        if field_type == 'text':
                            body_add += '<input type="text" size=25 name="' + d[
                                'text_field_name'] + '" value="' + current_value + '\">'
                        if field_type == 'textarea':
                            body_add += '<textarea rows=5 cols = 40 name="' + d[
                                'text_field_name'] + '" >' + current_value.replace('"', "'") + '</textarea>'
                    else:
                        body_add += '<select name="profile" id="profile">'
                        for key_name in d['values_to_show']:
                            name_to_show = ''
                            if key_name:
                                if 'name_to_show_rule' in d:
                                    if d['name_to_show_rule'] == 'screen_settings':
                                        name_to_show = key_name.split()[0].replace('(', ' (')
                                    else:
                                        name_to_show = key_name
                                else:
                                    name_to_show = key_name

                            if current_value == key_name:
                                body_add += '<option value="' + key_name + '" selected>' + name_to_show + '</option>'
                            else:
                                body_add += '<option value="' + key_name + '">' + name_to_show + '</option>'

                        body_add += '</select>'

                    reported_value = ''
                    if 'reported_value' in d:
                        reported_value = d['reported_value']
                    if reported_value:
                        body_add += '   {last report = ' + reported_value + '}'

                    body_add += ''
                    body_add += '</td>'

                    body_add += '<td>'
                    body_add += development_marker
                    if submit_name:
                        body_add += '<input type="submit" value="' + submit_name + '">'
                    body_add += '</td>'
                    body_add += '<td style="background-color:rgba' + d['color_for_info_box'] + '">'
                    body_add += development_marker
                    body_add += d['text_for_info_box']
                    body_add += '</td>'

                    body_add += '</form>'
                    body_add += '</tr>'

            body_add += '</table>'
            body_add += '</center>'
        else:
            body_add += '<center>'
            body_add += '!!! No create content table shown !!!' + '<br>'
            body_add += 'Current user does not have ' + service + '_create permissions'
            body_add += '</center>'

        # ---------------
        # add the content
        # ---------------
        body += body_add

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += '<B>Examples to paste in command line</B>'
        body += '</td>'
        body += '</tr>'

        body += '<tr>'
        body += '<td>'
        body += 'cat /cardinal/log/privoxy/privoxy_report.json'
        body += '</td>'
        body += '</tr>'

        body += '</table>'
        body += '</center>'

        # =================================================
        #
        # =================================================

        body += '<br><br>'
        body += '<br><br>'

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ,
                                             service + '_'):  # or permissions.permission_prefix_allowed(environ, 'development_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'

    try:
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # still on slicer01
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_investigate(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

    def test_convert_site_to_star(self):
        site = 'fpc.msedge.net:443'
        expected = ['*.msedge.net']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

        site = 'app.powerbi.com:443'
        expected = ['*.powerbi.com']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

        site = 'start.app.powerbi.com:443'
        expected = ['*.*.powerbi.com', '*.powerbi.com']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

        site = 'mdsystems.com:443'
        expected = ['mdsystems.com']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

        site = '10.166.170.52:443'
        expected = ['10.166.170.52']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

    def test_reduce_privoxy_day_report(self):
        the_day_report = {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1,
                          "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5,
                          "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12,
                          "accounts.google.com:443": 4}
        expected = '*.msedge.net,*.powerbi.com'
        actual = reduce_privoxy_day_report(the_day_report)
        self.assertEqual(expected, actual)

        the_day_report = {"update.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5,
                          "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15,
                          "www.google.com:443": 16, "safebrowsing.googleapis.com:443": 1, "s-ring.msedge.net:443": 2,
                          "www.gstatic.com:443": 1}
        expected = '*.msedge.net'
        actual = reduce_privoxy_day_report(the_day_report)
        self.assertEqual(expected, actual)

        the_day_report = {"storage.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5,
                          "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15,
                          "www.google.com:443": 16, "safebrowsing.googleapis.com:443": 1, "www.gstatic.com:443": 1}
        expected = 'storage.googleapis.com'
        actual = reduce_privoxy_day_report(the_day_report)
        self.assertEqual(expected, actual)

    def test_reduce_privoxy_day_report_strange_site(self):
        the_day_report = {"mdsystems.com:443": 1}
        expected = 'mdsystems.com'
        actual = reduce_privoxy_day_report(the_day_report)
        self.assertEqual(expected, actual)

    def test_investigate_content(self):
        the_content = """
Linux cah-rp-100000009e6347bb 5.10.63-v7l+ #1496 SMP Wed Dec 1 15:58:56 GMT 2021 armv7l

The programs included with the Debian GNU/Linux system are free software;
the exact distribution terms for each program are described in the
individual files in /usr/share/doc/*/copyright.

Debian GNU/Linux comes with ABSOLUTELY NO WARRANTY, to the extent
permitted by applicable law.
Last login: Wed Jul 12 11:34:16 2023 from 10.50.40.47
cah-pi-su@cah-rp-100000009e6347bb:~ $ cat /cardinal/log/privoxy/privoxy_report.json
{"days": {"2023-05-21": {"www.google.com:443": 2878, "update.googleapis.com:443": 8, "www.googleapis.com:443": 4, "http://update.googleapis.com/service/update2/json": 4, "safebrowsing.googleapis.com:443": 1}, "2023-05-22": {"www.google.com:443": 2152, "update.googleapis.com:443": 12, "http://update.googleapis.com/service/update2/json": 6, "www.googleapis.com:443": 9, "content-autofill.googleapis.com:443": 90, "safebrowsing.googleapis.com:443": 6, "redirector.gvt1.com:443": 1, "accounts.google.com:443": 8, "http://ww12.mycardinalmsdspd.com/": 1, "js.monitor.azure.com:443": 1, "browser.events.data.microsoft.com:443": 2}, "2023-05-23": {"www.google.com:443": 1414, "safebrowsing.googleapis.com:443": 11, "update.googleapis.com:443": 16, "http://update.googleapis.com/service/update2/json": 8, "www.googleapis.com:443": 23, "content-autofill.googleapis.com:443": 392, "support.google.com:443": 6, "redirector.gvt1.com:443": 4, "accounts.google.com:443": 27, "http://ww12.mycardinalmsdspd.com/": 4, "js.monitor.azure.com:443": 4, "browser.events.data.microsoft.com:443": 8}, "2023-05-24": {"update.googleapis.com:443": 7, "http://update.googleapis.com/service/update2/json": 4, "www.googleapis.com:443": 8, "safebrowsing.googleapis.com:443": 6, "redirector.gvt1.com:443": 1, "accounts.google.com:443": 8, "www.google.com:443": 74, "http://ww12.mycardinalmsdspd.com/": 1, "content-autofill.googleapis.com:443": 90, "js.monitor.azure.com:443": 1, "browser.events.data.microsoft.com:443": 2}, "2023-06-09": {"redirector.gvt1.com:443": 3, "www.google.com:443": 14, "accounts.google.com:443": 13, "http://ww12.mycardinalmsdspd.com/": 1, "content-autofill.googleapis.com:443": 35, "update.googleapis.com:443": 5, "js.monitor.azure.com:443": 1, "http://config.privoxy.org/error-favicon.ico": 1, "www.googleapis.com:443": 7, "browser.events.data.microsoft.com:443": 9, "http://update.googleapis.com/service/update2/json": 2}, "2023-06-13": {"accounts.google.com:443": 8, "www.googleapis.com:443": 5, "content-autofill.googleapis.com:443": 30, "redirector.gvt1.com:443": 1, "www.google.com:443": 8, "update.googleapis.com:443": 2, "http://update.googleapis.com/service/update2/json": 1, "safebrowsing.googleapis.com:443": 1}, "2023-07-06": {"www.googleapis.com:443": 9, "accounts.google.com:443": 11, "update.googleapis.com:443": 3, "http://update.googleapis.com/service/update2/json": 2, "www.google.com:443": 20, "safebrowsing.googleapis.com:443": 5, "redirector.gvt1.com:443": 1, "content-autofill.googleapis.com:443": 35, "www.gstatic.com:443": 1}, "2023-07-07": {"www.googleapis.com:443": 14, "update.googleapis.com:443": 14, "http://update.googleapis.com/service/update2/json": 7, "safebrowsing.googleapis.com:443": 7, "www.google.com:443": 70, "redirector.gvt1.com:443": 2, "accounts.google.com:443": 13, "content-autofill.googleapis.com:443": 279, "translate.googleapis.com:443": 2, "www.mycardinalhealth.net:443": 9, "www.microsoft365.com:443": 8, "www.bing.com:443": 3, "fpc.msedge.net:443": 2, "arc.msn.com:443": 2, "na01.safelinks.protection.outlook.com:443": 2, "media2.giphy.com:443": 18, "media3.giphy.com:443": 25, "media4.giphy.com:443": 26, "media1.giphy.com:443": 19, "media0.giphy.com:443": 20, "*************:3478": 1, "www.gstatic.com:443": 1}, "2023-07-08": {"safebrowsing.googleapis.com:443": 2, "update.googleapis.com:443": 8, "http://update.googleapis.com/service/update2/json": 4, "www.googleapis.com:443": 4, "www.google.com:443": 20, "content-autofill.googleapis.com:443": 15, "accounts.google.com:443": 8, "www.gstatic.com:443": 1}, "2023-07-09": {"update.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15, "www.google.com:443": 16, "safebrowsing.googleapis.com:443": 1, "www.gstatic.com:443": 1}, "2023-07-10": {"update.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15, "www.google.com:443": 20, "safebrowsing.googleapis.com:443": 1, "www.gstatic.com:443": 1}, "2023-07-11": {"update.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15, "www.google.com:443": 16, "safebrowsing.googleapis.com:443": 1, "www.gstatic.com:443": 1}, "2023-07-12": {"update.googleapis.com:443": 10, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 4, "content-autofill.googleapis.com:443": 15, "www.google.com:443": 16, "safebrowsing.googleapis.com:443": 1, "s-ring.msedge.net:443": 2, "www.gstatic.com:443": 1}, "2023-07-13": {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $

        """

        the_content = """
Linux cah-rp-100000009e6347bb 5.10.63-v7l+ #1496 SMP Wed Dec 1 15:58:56 GMT 2021 armv7l

The programs included with the Debian GNU/Linux system are free software;
the exact distribution terms for each program are described in the
individual files in /usr/share/doc/*/copyright.

Debian GNU/Linux comes with ABSOLUTELY NO WARRANTY, to the extent
permitted by applicable law.
Last login: Wed Jul 12 11:34:16 2023 from 10.50.40.47
cah-pi-su@cah-rp-100000009e6347bb:~ $ cat /cardinal/log/privoxy/privoxy_report.json
{"days": {"2023-07-13": {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $

        """

        expected = '2023-07-13 => *.msedge.net,*.powerbi.com'
        actual = investigate_content(the_content)
        self.assertEqual(expected, actual)

        the_content = """
{"days": {"2023-07-12":{"test.junk.com":1},"2023-07-13": {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $
        """

        expected = """2023-07-12 => *.junk.com
2023-07-13 => *.msedge.net,*.powerbi.com"""
        actual = investigate_content(the_content)
        self.assertEqual(expected, actual)

    def test_investigate_content_google_allows(self):
        # 2023.08.02 Need some urls to show up, for OKTA to work now

        the_content = """
{"days": {"2023-07-12":{"storage.googleapis.com":1},"2023-07-13": {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $
        """

        expected = """2023-07-12 => storage.googleapis.com
2023-07-13 => *.msedge.net,*.powerbi.com"""
        actual = investigate_content(the_content)
#        self.assertEqual(expected, actual)

    def test_investigate_odd_site(self):
        the_content = """
{"days": {"2025-05-25": {"mdsystems.com:443": 1}}, "last_time_stamp": "2025-06-02 14:02:04.659"}
"""
        expected = """2025-05-25 => mdsystems.com"""
        actual = investigate_content(the_content)
        self.assertEqual(expected, actual)

    def test_investigate_content_google_allows_with_summary(self):
        # 2023.08.02 Need some urls to show up, for OKTA to work now

        the_content = """
{"days": {"2023-07-12":{"storage.googleapis.com":1},"2023-07-13": {"content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $
        """

        expected = """2023-07-12 => storage.googleapis.com
2023-07-13 => *.msedge.net,*.powerbi.com

all: *.msedge.net,*.powerbi.com,storage.googleapis.com"""
        actual = investigate_content(the_content, with_summary=True)
        self.assertEqual(expected, actual)

        # with a common link on both days, make sure it only shows up once in the all report
        the_content = """
{"days": {"2023-07-12":{"storage.googleapis.com":1},"2023-07-13": {"storage.googleapis.com":1, "content-autofill.googleapis.com:443": 60, "www.google.com:443": 44, "fpc.msedge.net:443": 1, "update.googleapis.com:443": 9, "http://update.googleapis.com/service/update2/json": 5, "www.googleapis.com:443": 7, "safebrowsing.googleapis.com:443": 2, "app.powerbi.com:443": 12, "accounts.google.com:443": 4}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $
        """

        expected = """2023-07-12 => storage.googleapis.com
2023-07-13 => *.msedge.net,*.powerbi.com,storage.googleapis.com

all: *.msedge.net,*.powerbi.com,storage.googleapis.com"""
        actual = investigate_content(the_content, with_summary=True)
        self.assertEqual(expected, actual)

    def test_pr005_incident_2024_01_10(self):
        site = 'wppr0059tterm11:8080'
        expected = ['wppr0059tterm11:8080']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

        the_day_report = {"wppr0059tterm11:8080": 5}
        expected = 'wppr0059tterm11:8080'
        actual = reduce_privoxy_day_report(the_day_report)
        self.assertEqual(expected, actual)

        the_content = """
{"days": {"2023-07-12":{"wppr0059tterm11:8080": 5}}, "last_time_stamp": "2023-07-13 20:51:06.802"}cah-pi-su@cah-rp-100000009e6347bb:~ $
        """
        expected = """2023-07-12 => wppr0059tterm11:8080

all: wppr0059tterm11:8080"""
        actual = investigate_content(the_content, with_summary=True)
        self.assertEqual(expected, actual)

        site = "http://ww7.mycardinalmsdspd.com/"
        expected = ['*.mycardinalmsdspd.com']
        actual = convert_site_to_star(site)
        self.assertEqual(expected, actual)

# End of source file
