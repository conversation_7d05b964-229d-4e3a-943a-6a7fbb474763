# A timezones for slicer page services

service = "timezones"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/timezones.py

using:
sudo vi /var/www/html/timezones.py

It also requires:

sudo vi /etc/httpd/conf.d/python-timezones.conf
----- start copy -----
WSGIScriptAlias /timezones /var/www/html/timezones.py
----- end copy -----

sudo chown apache:apache /var/www/html/timezones.py

sudo systemctl restart httpd


# May not be needed
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/timezones-runner
sudo chmod +x /var/www/html/timezones-runner

# ===== begin: start file
#!/usr/bin/env python
import timezones
timezones.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/timezones-runner.service
sudo systemctl daemon-reload
sudo systemctl stop timezones-runner.service
sudo systemctl start timezones-runner.service
sudo systemctl enable timezones-runner.service

systemctl status timezones-runner.service

sudo systemctl restart timezones-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep timezones-runner

OR

tail -f /var/log/syslog | fgrep timezones-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/timezones-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import timezones; print(timezones.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/timezones

https://slicer.cardinalhealth.net/timezones?siteid=PR005

https://slicer.cardinalhealth.net/timezones?serial=100000002a5da842

https://slicer.cardinalhealth.net/timezones?monitorNot=M.1.2

"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_timezones


"""

_notes = """

MEX09 was America/Chihuahua before 2022.03.13, set to America/Denver after
(Same for MEX03)

https://stackoverflow.com/questions/1301493/setting-timezone-in-python

You can use pytz as well..

import datetime
import pytz
#def utcnow():
    return datetime.datetime.now(tz=pytz.utc)

utcnow()
   datetime.datetime(2020, 8, 15, 14, 45, 19, 182703, tzinfo=<UTC>)

utcnow().isoformat()


utc_now = pytz.utc.localize(datetime.datetime.utcnow())
test1_now = utc_now.astimezone(pytz.timezone("America/Chihuahua"))
test2_now = utc_now.astimezone(pytz.timezone("America/Denver"))

print (utc_now)
print (test1_now)
print (test2_now)




https://serverfault.com/questions/804403/disable-daylight-saving-time-dst-changes-in-linux



"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest
import datetime

startup_exceptions = ''
s_file_for_timezone_details = '/dev/shm/timezone_details.txt'

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import pytz

    import permissions

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# ----------------------------
def get_valid_timezones():
    # ----------------------------
    options_for_timezones = {}
    current_time = pytz.utc.localize(datetime.datetime.utcnow())

    for key_long_name_listed in get_all_timezones():
        key_long_name = key_long_name_listed
        if key_long_name:
            key_name = key_long_name.split()[0]
            try:
                current_time_local = current_time.astimezone(pytz.timezone(key_name))
                show_time = str(current_time_local)
                key_long_name += ' (' + current_time_local.tzname() + ')'
            except:
                show_time = '(error)'
        else:
            key_name = ''
            show_time = ''

        if show_time:
            time_only = ''
            full_clock = show_time
            try:
                time_only = show_time.split()[1]
            except:
                pass
            #                options_for_timezones[key_long_name] = time_only
            options_for_timezones[key_long_name] = full_clock

    return options_for_timezones

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_all_timezones():
    # ----------------------------
    timezones = []
    try:
        pass_string, fail_string = do_one_command("timedatectl list-timezones --no-pager")
        for item in pass_string.split('\n'):
            if item:
                timezones.append(item)
    except:
        pass
    return timezones


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(timezones status)'

    status = os.system('systemctl is-active --quiet timezones-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "timezones-runner" that the service starts
# ====================================
def main():
    # ====================================
    try:
        # try to clean any existing, maybe old format, save file content
        os.remove(s_file_for_timezone_details)
    except:
        pass


    # one time
    open('/dev/shm/running_exceptions_' + service, 'w').write('Building timezone details...')
    timezone_details = {}
    options_for_timezones = get_valid_timezones()

    for key_long_name in sorted(options_for_timezones):
        pass_string, fail_string = do_one_command("zdump -v " + key_long_name)
        timezone_details[key_long_name] = {'zdump':pass_string}

    try:
        open(s_file_for_timezone_details, 'w').write(json.dumps(timezone_details))
    except:
        pass

    open('/dev/shm/running_exceptions_' + service, 'w').write('')
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_new_filter(original_filter='', new_terms='', rotate_terms='', rotate_key='', items_to_puncture=''):
    # ====================================

    return_dictionary = {}

    original_primary_key = ''
    new_primary_key = ''

    splits = original_filter.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            return_dictionary[pieces[0]] = pieces[1]
            if rotate_key:
                if pieces[0] == rotate_key:
                    original_primary_key = pieces[1]

    splits = new_terms.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            return_dictionary[pieces[0]] = pieces[1]
            if rotate_key:
                if pieces[0] == rotate_key:
                    new_primary_key = pieces[1]

    # look to do the rotation
    splits = rotate_terms.split(',')
    for item in splits:
        pieces = item.split('=')
        if len(pieces) > 1:
            options = pieces[1].split('|')
            current_rotation = ''
            if pieces[0] in return_dictionary:
                current_rotation = return_dictionary[pieces[0]]

            if rotate_key and new_primary_key != original_primary_key:
                # check to see if this primary key has changed, and if so, then clear, in order to get the default
                return_dictionary[pieces[0]] = options[0]  # the first one is the default
            else:
                # do the rotation
                if current_rotation in options:
                    if current_rotation == options[0]:
                        return_dictionary[pieces[0]] = options[1]
                    else:
                        return_dictionary[pieces[0]] = options[0]
                else:
                    return_dictionary[pieces[0]] = options[0]  # the first one is the default

    splits = items_to_puncture.split(',')
    for item in splits:
        if item in return_dictionary:
            del return_dictionary[item]

    # build the result
    return_filter = ''
    for key in sorted(return_dictionary):  # sort only to make the test cases simpler to write
        if return_filter:
            return_filter += ','
        return_filter += key + '=' + return_dictionary[key]

    return return_filter


# ====================================
def make_body_POST(environ):
    # ====================================
    # do work on content

    # then return what GET would have done
    return make_body_GET(environ)


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body = ''

    query = environ['QUERY_STRING']

    query_items = {}
    query_items_order = []
    for item in query.split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]
            query_items_order.append(parms[0])

    # https://slicer.cardinalhealth.net/timezones?show_zdump=1
    show_zdump = False
    if 'show_zdump' in query_items:
        show_zdump = True

    timezone_details = {}
    try:
        timezone_details = json.loads(open(s_file_for_timezone_details, 'r').read())
    except:
        pass

    REQUEST_URI = str(environ['REQUEST_URI']).split('?')[0]
    REQUEST_FILTER = ''
    if '?' in environ['REQUEST_URI']:
        REQUEST_FILTER = str(environ['REQUEST_URI']).split('?')[1]

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:

        body += '<center>'
        body += 'Timezones<br><br>'

        body += '<table border="1" cellpadding="5">'

        options_for_timezones = {}
        timezones_at_this_time = {}

        options_for_timezones = get_valid_timezones()

        for key_long_name in sorted(options_for_timezones):
            full_clock = options_for_timezones[key_long_name]
            if not full_clock in timezones_at_this_time:
                timezones_at_this_time[full_clock] = []
            timezones_at_this_time[full_clock].append(key_long_name)

        sort_by = 'timezone'
        if 'sort' in query_items:
            sort_by = query_items['sort']

        if sort_by == 'timezone':

            body += '<tr>'

            body += '<td>'
            body += 'timezone'
            body += '</td>'

            body += '<td>'
            url_to_use = make_home_url_from_environ(environ)
            url_to_use = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER, 'sort=time')
            name_to_show = "current time (click here to sort)"
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
            body += '</td>'

            body += '<td>'
            body += 'contains isdst=1'
            body += '</td>'

            if show_zdump:
                body += '<td>'
                body += 'zdump'
                body += '</td>'

            body += '</tr>'
            for key_long_name in sorted(options_for_timezones):
                body += '<tr>'

                body += '<td>'
                body += key_long_name
                body += '</td>'

                body += '<td>'
                body += options_for_timezones[key_long_name]
                body += '</td>'

                zdump = ''
                if key_long_name in timezone_details:
                    zdump = timezone_details[key_long_name]['zdump']

                body += '<td>'
                if zdump:
                    if 'isdst=1' in zdump:
                        body += 'DST'
                    else:
                        body += ''
                else:
                    body += 'unknown'
                body += '</td>'

                if show_zdump:
                    body += '<td>'
                    if key_long_name in timezone_details:
                        zdump = timezone_details[key_long_name]['zdump']
                    else:
                        zdump = '(not ready yet)'
                    body += zdump.replace('\n','<br>')
                    body += '</td>'

                body += '</tr>'

        if sort_by == 'time':
            body += '<tr>'

            body += '<td>'
            body += 'current time'
            body += '</td>'
            body += '<td>'

            url_to_use = make_home_url_from_environ(environ)
            url_to_use = url_to_use + REQUEST_URI + '?' + make_new_filter(REQUEST_FILTER, 'sort=timezone')
            name_to_show = 'timezone(s) (click here to sort)'
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '</td>'

            body += '</tr>'
            for key_long_name in sorted(timezones_at_this_time):
                body += '<tr>'

                body += '<td>'
                body += key_long_name
                body += '</td>'

                body += '<td>'
                body += str(sorted(timezones_at_this_time[key_long_name]))
                body += '</td>'

                body += '</tr>'

        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    #    if permissions.permission_prefix_allowed(environ, 'timezones_') or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other

# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_timezones(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
