# A speedtest for slicer page services

service = "speedtest"
version = service + '.0.1'

_ = """
This file gets loaded to:
/var/www/html/speedtest.py

using:
sudo vi /var/www/html/speedtest.py

It also requires:

sudo vi /etc/httpd/conf.d/python-speedtest.conf
----- start copy -----
WSGIScriptAlias /speedtest /var/www/html/speedtest.py
----- end copy -----

sudo chown apache:apache /var/www/html/speedtest.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/speedtest-runner
sudo chmod +x /var/www/html/speedtest-runner

# ===== begin: start file
#!/usr/bin/env python
import speedtest
speedtest.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/speedtest-runner.service
sudo systemctl daemon-reload
sudo systemctl stop speedtest-runner.service
sudo systemctl start speedtest-runner.service
sudo systemctl enable speedtest-runner.service

systemctl status speedtest-runner.service

sudo systemctl restart speedtest-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep speedtest-runner

OR

tail -f /var/log/syslog | fgrep speedtest-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/speedtest-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import speedtest; print(speedtest.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/speedtest

https://slicer.cardinalhealth.net/speedtest?siteid=PR005

https://slicer.cardinalhealth.net/speedtest?serial=100000002a5da842

https://slicer.cardinalhealth.net/speedtest?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_speedtest


"""

_notes = """
https://stackoverflow.com/questions/17699666/post-request-with-wget

wget --post-file=nature.jpg http://ipadress:8080/v1/AUTH_test/test/ --post-data="AUTH_1624582364932749DFHDD"


on Pi:

Download...
wget --no-check-certificate https://slicer.cardinalhealth.net/speedtest?size=10000000 -O testfile1
wget --no-check-certificate https://slicer.cardinalhealth.net/speedtest?size=100000000 -O testfile2

Upload....
curl -k -v -F filename=testfile -F upload=@testfile1 https://slicer.cardinalhealth.net/speedtest
curl -k -v -F filename=testfile -F upload=@testfile2 https://slicer.cardinalhealth.net/speedtest

rm testfile1
rm testfile2


"""

import cgi
import copy
import traceback
import json
import os
import random
import shlex
import string
import subprocess
import sys
import time
import unittest

from tempfile import TemporaryFile

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:  # for unittest to work
    import login
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(speedtest status)'

    status = os.system('systemctl is-active --quiet speedtest-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "speedtest-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ):
    # ====================================
    # do work on content

    time_start = time.time()

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

    if len(field_storage.list):
        for item in field_storage.list:
            if item.filename:
                file_content = item.file.read().decode('utf-8')

                time_end = time.time()
                delta_time = time_end - time_start
                the_rate = int(len(file_content) / (delta_time))

                body = 'size of file uploaded is ' + str(len(file_content)) + ' in time of ' + str(
                    delta_time) + ' seconds:'
                body += ' which gives rate of ' + str(the_rate) + ' Bps or ' + str(
                    "{:.2f}".format(the_rate / 1000.0)) + ' KBps or ' + str(
                    "{:.2f}".format(the_rate / 1000000.0)) + ' MBps'

                return (body)

    # then return what GET would have done
    return make_body_GET(environ)


# ====================================
def make_body_GET(environ):
    # ====================================

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'size' in query_items:
        # build a random string
        body = ''
        pick_from_string = string.ascii_uppercase + string.ascii_lowercase + string.digits
        for i in range(0, int(query_items['size'])):
            body += random.choice(pick_from_string)
        return body

    try:
        body += '<center>'
        body += 'method = GET'
        body += '</center>'

        body += '<center>'
        body += 'environ'

        body += '<table border="1" cellpadding="5">'
        for item in environ:
            body += '<tr>'
            body += '<td>'
            body += item
            body += '</td>'
            body += '<td>'
            try:
                body += str(environ[item])
            except:
                body += '(na)'
            body += '</td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        body += '<center>'
        body += 'result'
        body += '<br><br>'
        body += str(login.get_current_user(environ))
        body += '<br><br>'
        body += '</center>'

        body += '<center>'
        body += '<br><br>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'test1'
        body += '</td>'
        body += '<td>'
        body += 'test2'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body


# ====================================
def make_body(environ):
    # ====================================
    body = ''

    # if permissions.permission_prefix_allowed(environ, 'speedtest_') or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_speedtest(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
