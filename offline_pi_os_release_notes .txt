offline_pi_os_release_notes
https://downloads.raspberrypi.com/raspios_lite_armhf/release_notes.txt?_gl=1*1z0ixwv*_ga*MjEyMDE2MzIwMi4xNzA4OTc2ODQz*_ga_22FD70LWDS*MTcxMzM1NTA1OC4xLjEuMTcxMzM1NTExNC4wLjAuMA..

2024-03-15:
  * Audio streams will now not be interrupted when other audio devices are connected or disconnected
  * Keyboard shortcut to install <PERSON><PERSON> no longer prompts for password, and will now wait for clock synchronisation rather than failing silently
  * Orca screen reader updated to version 45 with various additional small bug fixes
  * Obsolete fbturbo video driver removed
  * Bug fix - saved display resolution settings not reloading under X
  * Raspberry Pi firmware 6e0ae774407d68659b50cfbeb9f493ed16718866
  * Linux kernel 6.6.20 - 6f16847710cc0502450788b9f12f0a14d3429668
2024-03-12:
  * Added setting of headless resolution to Screen Configuration
  * Removed setting of headless resolution for Wayland from Raspberry Pi Configuration
  * Improved handling of power button on Raspberry Pi 5
  * Popover windows from taskbar replaced with conventional windows
  * Shutdown assistant now closes all user processes when logging out
  * Wayvnc updated to improve compatibility with various VNC clients
  * Wayvnc now controlled by systemd
  * Audio icon on taskbar hidden if no audio devices connected
  * Alternative mouse cursor shown during drag-and-drop operations
  * raspi-config now allows EEPROM to be upgraded
  * Speed improvement when opening bluetooth and network menus
  * Tweaks to display of some widgets under dark theme
  * Improved compatibility with alternative window managers
  * Bug fix - prevent multiple file manager confirm dialogs being overlaid
  * Bug fix - drag-and-drop in file manager causing incorrect files to move
  * Bug fix - memory leaks in volume and bluetooth menus
  * Bug fix - GPU load sometimes not correctly reported in plugin and task manager
  * Bug fix - crash when closing windows with non-GTK headerbars
  * Bug fix - spurious button hover highlights on touchscreens
  * Bug fix - windows on other monitors being hidden from taskbar
  * Bug fix - corrected power monitoring brownout detection
  * Bug fix - wayfire keyboard layout settings sometimes not loading
  * Removed fbturbo xorg video driver as it is no longer useful
  * Chromium updated to 122.0.6261.89
  * Firefox updated to 123.0
  * Raspberry Pi firmware 6e0ae774407d68659b50cfbeb9f493ed16718866
  * Linux kernel 6.6.20 - 6f16847710cc0502450788b9f12f0a14d3429668
2023-12-11:
  * Fix Raspberry Pi Imager's WLAN configuration for lite images
2023-12-05:
  * Serial port switching in rc_gui and raspi-config modified to support Raspberry Pi 5
  * Touch screens now identified with unique per-device strings to enable correct association with display devices
  * Compatibility with RP1 displays added
  * Theme files monitored by pixdecor plugin to load changes on the fly
  * Main menu shortcut to The Magpi website restored
  * GTK+2 theme harmonised with GTK+3 theme to enable more uniform theming of Qt applications
  * Battery monitor plugin enabled
  * Taskbar Preferences menu item added to panel right-click menu
  * Better reloading of on-the-fly theme changes
  * Various improvements to Wayfire rendering
  * Dark GTK theme added
  * Bug fix - suppressed warning notifications when removing USB devices without mounted filesystems
  * Bug fix - volume keyboard shortcuts locked up on some devices
  * Bug fix - correctly handling multiple USB audio devices with same name
  * Bug fix - some translations not loading in panel plugins
  * Bug fix - window titlebars disappearing when tiled
  * Bug fix - local installer service failing to install local deb files
  * Bug fix - wizard not correctly setting locale when explicit UTF-8 character set required
  * Bug fix - system updates could fail if an update required the removal of an installed package
  * Bug fix - prevent file manager windows opening larger than screen size
  * Bug fix - GPU plugin displaying load percentage as -1
  * Bug fix - various window manager crashes associated with shadows on certain window types
  * Bug fix - allow VNC to be enabled if either RealVNC or WayVNC are installed
  * WayVNC - improved support for encrypted connections
  * Mathematica now works on Raspberry Pi 5 and 64-bit
  * Scratch 3 now works on Raspberry Pi 5 and 64-bit
  * Thonny updated to version 4.1.4
  * Chromium updated to 119.0.6045.171
  * Firefox updated to 119.0
  * gpiod binary tools included in lite images
  * python3-venv included in lite images
  * Japanese translations updated
  * German translation added to Appearance Settings
  * Raspberry Pi firmware 12af703dd07d7721c1f2f58c6f71d9fe66270838
  * Linux kernel 6.1.63
2023-10-10:
  * Based on Debian bookworm release
  * Support for Raspberry Pi 5
  * Desktop now runs on the Wayfire Wayland compositing window manager on Raspberry Pi 4 and 5 platforms; on X11 using the openbox window manager on older platforms
  * lxpanel replaced with new wf-panel-pi application when running Wayland; existing lxpanel plugins migrated; gpu performance and power plugins added
  * pcmanfm modified to use Wayland backend when running on Wayland
  * PipeWire used instead of PulseAudio as audio control subsystem; various changes made to volume control plugin to support this
  * NetworkManager used instead of dhcpcd as networking interface; various changes made to networking plugin to support this
  * Firefox browser added as alternative to Chromium; selection of default browser added to Raspberry Pi Configuration tool
  * WayVNC VNC server used instead of RealVNC when running on Wayland
  * All customisation and configuration applications modified to customise Wayfire environment as appropriate
  * grim used as screenshot tool instead of scrot when running on Wayland
  * eom image viewer used instead of gpicview
  * evince document viewer used instead of qpdfview
  * Chromium web browser updated to version 116
  * VLC media player updated to version 3.0.18
  * Magnifier program not available when running Wayland; Wayfire includes screen zoom capabilities
  * CustomPC and Wireframe removed from Bookshelf
  * Numerous small changes and bug fixes
  * Switched from raspberrypi-kernel to Debian-based kernel packaging (linux-image-rpi-*)
  * Switched from raspberrypi-bootloader to Debian based firmware packaging (raspi-firmware)
  * /boot mount point moved to /boot/firmware
2023-05-03:
  * 64-bit Mathematica added to rp-prefapps
  * Bug fix - occasional segfault in CPU temperature plugin
  * Bug fix - X server crash when changing screen orientation
  * Bug fix - X server DPMS not working
  * Mathematica updated to 13.2.1
  * Matlab updated to 23.1.0
  * Chromium updated to 113.0.5672.59
  * Raspberry Pi Imager updated to 1.7.4
  * RealVNC server updated to 7.0.1.49073
  * RealVNC viewer updated to 7.0.1.48981
  * Updated VLC HW acceleration patch
  * libcamera
    - Add generalised statistics handling.
    - Fix overflow that would cause incorrect calculations in the AGC algorithm.
    - Improve IMX296 sensor tuning.
  * libcamera-apps
    - Improve handling of audio resampling and encoding using libav
    - Improve performance of QT preview window rendering
    - Add support for 16-bit Bayer in the DNG writer
    - Fix for encoder lockup when framerate is set to 0
    - Improved thumbnail rendering
  * picamera2
    - MJPEG server example that uses the hardware MJPEG encoder.
    - Example showing preview from two cameras in a single Qt app.
    - H264 encoder accepts frame time interval for SPS headers.
    - H264 encoder should advertise correct profile/level.
    - H264 encoder supports constant quality parameter.
    - Exif DateTime and DateTimeOriginal tags are now added.
    - Various bug fixes (check Picamera2 release notes for more details).
  * Some translations added
  * Raspberry Pi firmware 055e044d5359ded1aacc5a17a8e35365373d0b8b
  * Linux kernel 6.1.21
2023-02-21:
  * glamor now disabled on all platforms other than Raspberry Pi 4 with legacy video driver
  * msdri3 video driver support added
  * KiCad added to Recommended Software
  * Support for new touchscreen driver added to Screen Resolution tool; minor UI tweaks
  * GTK message dialogs shown with right-justified buttons
  * Bug fix - updater plugin now does not clear icon when an update has failed
  * Bug fix - keyboard highlight now shown on GTK switch control
  * Some Korean and Brazilian translations added
  * Fix rpi-imager hidden ssid configuration
  * Install kms++-utils
  * Raspberry Pi firmware 78852e166b4cf3ebb31d051e996d54792f0994b0
  * Linux kernel 5.15.84
2022-09-22:
  * NodeRED removed from Recommended Software and full image - should only be installed via apt
  * Improved speed of startup of lxpanel network controller plugins
  * Improved detection of Bluetooth HID devices in first-boot wizard
  * Bug fix - splash screen version number and date incorrect
  * Bug fix - text entry in searchable main menu ignored while caps or num lock active
  * Bug fix - keyboard shortcuts to open Wi-fi and Bluetooth plugin menus not working in 64-bit builds
  * Bug fix - typo in Bluetooth device menu
  * Bug fix - crash when cycling windows in mutter
  * Bug fix - spurious text output in raspi-config network configuration selection
  * Bug fix - firstboot script skipped further steps if rootfs resize failed
  * Bug fix - firstboot script inadvertently wrote /test.log
  * Bug fix - typo in raspi-config resulted in empty file /2 being created
  * Raspberry Pi firmware 48cd70fe84432c5d050637b61e4b7b9c831c98bf
  * Linux kernel 5.15.61
2022-09-06:
  * lxpanel - new main menu plugin with text search
  * lxpanel - new separate audio input plugin with microphone volume and input select
  * lxpanel - keyboard shortcuts to open wifi and bluetooth plugins added
  * lxpanel - notifications now shown with short delay after startup and between each
  * rc_gui - only allows valid hostnames to be set
  * piwiz - no longer allows "root" as a user name
  * gtk3 - menus can now be resized after being drawn
  * raspi-config - option to switch between dhcpcd and Network Manager added
  * lxpanel - new network plugin compatible with Network Manager added
  * piwiz - compatibility with Network Manager added
  * Bug fix - 100% CPU usage in file manager when desktop item unmounted
  * Bug fix - window manager was preventing switching between international keyboard layouts
  * Bug fix - system tray redrawing made more robust
  * Bug fix - translations not being shown in various lxpanel plugins
  * Bug fix - updater plugin failing on x86 images
  * Bug fix - force power on for Bluetooth hardware when unblocked with rfkill
  * Bug fix - message boxes in rc_gui not centred correctly
  * Bug fix - switching sessions between Wayland and X11 made more robust
  * Bug fix - switching of ALSA devices in raspi-config made compatible with third-party devices
  * Install NetworkManager (disabled)
  * Install OpenJDK 17 rather than OpenJDK 11 on full images
  * Install picamera2
  * Format the root partition with the metadata_csum option
  * Format the boot partition with 4 sectors per cluster for a slight performance boost
  * Remove 'flush' mount option from the boot partition
  * Raspberry Pi firmware 48cd70fe84432c5d050637b61e4b7b9c831c98bf
  * Linux kernel 5.15.61
2022-04-04:
  * Default "pi" user has been removed; the first-boot wizard enforces the creation of a new user account
  * rename-user script added to allow existing users to be renamed by temporarily rebooting to cut-down first-boot wizard
  * Overscan now managed by xrandr under KMS, can be set independently for both monitors, and takes effect on the fly rather than requiring reboot
  * GTK3 switch control now used in place of paired radio buttons throughout
  * piwiz - first-boot wizard now runs in a separate session as a different user with different graphics
  * piwiz - first-boot wizard now has automatic pairing for discoverable Bluetooth mice and keyboards
  * lxinput - keyboard delay and repeat settings now persist across reboots under mutter
  * raspi-config / rc_gui - removed pixel doubling option when KMS driver in use
  * raspi-config - removed composition manager option when legacy driver in use
  * arandr - restored support for interlaced displays
  * mutter - implemented more intuitive window and application cycling behaviour
  * pi-greeter - rebuilt for GTK3
  * Bug fix - graphical corruption in system tray icons
  * Bug fix - desktop items vanishing when dragged
  * Bug fix - terminal windows not focussed correctly when launched
  * Bug fix - crash after multiple update checks in updater plugin
  * Bug fix - Raspberry Pi keyboard auto-detect by wizard was broken in previous release
  * Bug fix - spurious "connected" dialog box shown when reconnecting to Bluetooth LE devices on boot
  * Support for experimental Wayland backend added - can be enabled as an advanced option in raspi-config
  * Various small bug fixes and graphical tweaks
  * Chromium upgraded to version 98.0.4758.106
  * FFmpeg HW acceleration improved
  * OpenJDK 17 now defaults to 'client' JVM for ARMv6 compatibility
  * Raspberry Pi firmware 69277bc713133a54a1d20554d79544da1ae2b6ca
  * Linux kernel 5.15.30
2022-01-28:
  * Policykit CVE-2021-4034 fix
  * rc_gui - add combo box to allow resolution to be set for VNC connections
  * rc_gui - camera interface switch removed
  * lxpanel - remove appearance settings from preferences dialog; instead add menu option to open general Appearance Settings application
  * lxpanel - add ellipses to menu items which open dialogs
  * lxinput - read current mouse acceleration directly from xinput
  * lxinput - use device IDs rather than names to cope with devices changing when powered-down
  * lxinput - remove redundant changes to openbox config file
  * plymouth - set KillMode to mixed to suppress warning message
  * raspi-config - add option to switch composite video
  * raspi-config - add option to switch to legacy camera mode
  * raspi-config - add option to set resolution for headless connections
  * raspberrypi-ui-mods - disable mutter when VNC server is running and fall back to openbox
  * pipanel - add command-line option to open on arbitrary tab
  * lxplug-network - suppress â€™scan receivedâ€™ logging message
  * raspberrypi-ui-mods - set hover colour for taskbar items based on taskbar colour, not system highlight colour
  * Legacy camera applications and libraries reinstalled (32-bit only)
  * Bug fix - lxinput - lxsession config file not being written on first attempt
  * Bug fix - lxinput - set timer for file write to prevent slider slowing down
  * Bug fix - lxinput - write values to gsettings as well as xinput and xsettings to take effect within mutter
  * Bug fix - lxinput - fix failure to parse and write non-English numeric formats
  * Bug fix - arandr - various fixes to parsing of non-standard EDID blocks to enable model and serial to be correctly extracted
  * Bug fix - arandr - refresh rate calculated to 3 decimal places for monitors which require it
  * Bug fix - arandr - enable setting of left and right orientation
  * Bug fix - arandr - add compatibility with new touchscreen driver
  * Bug fix - arandr - apply settings correctly to DSI and composite displays
  * Bug fix - lxplug-magnifier - fix crash when opening preferences without required magnifier package installed
  * Bug fix - piwiz - launch screen reader install prompt as a new process to prevent audio lockups crashing wizard
  * Bug fix - lxpanel - not loading some plugins (cpufreq, minimise all windows) due to icon loading code not compatible with GTK+3
  * Bug fix - gtk+3 - disabled new GDK touch events to enable double-clicks to be detected on touchscreen
  * Bug fix - xrdp - included backports from bookworm version of xrdp and xorgxrdp to restore window frames with mutter over xrdp connections
  * Update various translations
  * udisks2 added to lite image
  * mkvtoolnix added to lite image
  * 7z and zip support added to lite image
  * gnome-keyring added to desktop images
  * Raspberry Pi firmware c6d56567ff6ef17fd85159770f22abcf2c5953ed
  * Linux kernel 5.10.92
2021-10-30:
  * Based on Debian version 11 (bullseye)
  * Desktop components (lxpanel and all plugins, libfm, pcmanfm) now built against GTK+3
  * Applications (piwiz, pipanel, rc_gui, lxinput) now built against GTK+3
  * PiXflat GTK+3 theme updated with numerous changes to support the above
  * GTK+3 : toolbar icon size setting added
  * GTK+3 : ability to request client-side decoration on windows added
  * GTK+3 : setting for indent for frame labels in custom style added
  * mutter window manager used instead of openbox on devices with 2GB or more of RAM
  * mutter : title bar icon behaviour and appearance modified to match openbox
  * mutter : additional keyboard shortcuts added
  * mutter : various performance enhancements
  * mutter compatibility added to screen magnifier
  * Numerous changes to Appearance Settings application to support GTK+3 and mutter
  * Updater plugin added to lxpanel to detect and install software updates
  * File manager view options simplified to either list or icons, with separate menu option for thumbnails
  * New file manager toolbar icons
  * KMS used as default display driver
  * Modifications to HDMI audio output selection to support the above
  * xcompmgr enabled when openbox is running under KMS
  * New default camera subsystem based on libcamera
  * New camera demo applications (libcamera-still and libcamera-vid) have replaced raspistill and raspivid
  * Legacy camera subsystem removed from 64-bit RPi OS (still available on 32-bit)
  * Chromium upgraded to version 92.0.4515.98
  * VLC media player upgraded to version 3.0.16
  * Spurious drive removal warning after use of SD card copier removed
  * Bookshelf application now includes Custom PC magazine
  * Various translation updates - Italian, Korean, Polish, German, Armenian
  * Startup wizard now installs Japanese fonts if needed
  * Progress and information dialog boxes for lxpanel plugins now common to lxpanel, rather than in individual plugins
  * Icon handling code for lxpanel plugins now common to lxpanel
  * Package with 4K version of Raspberry Pi wallpaper added to Recommended Software
  * Python Games and Minecraft removed from Recommended Software - neither is compatible with bullseye
  * Bluetooth pairing and connection dialogs updated for compatibility with more devices
  * Bluetooth devices always disconnected before removal to speed up removal process
  * Bluetooth pairing dialog now only shows devices which offer services which are usable by Pi
  * Separate Bluetooth unpair dialog removed - unpair now an option for each individual device
  * Bug fix - mutter : header bar colours not updating when theme is changed
  * Bug fix - GTK+3 : tooltips being displayed incorrectly at bottom of screen
  * Bug fix - lxpanel : crash when using keyboard shortcut to enable magnifier when magnifier not installed
  * Bug fix - lxpanel : lockup in Bluetooth plugin when connecting to certain devices
  * Bug fix - lxpanel : discoverable mode icon could get out of sync with underlying Bluetooth system state
  * Bug fix - piwiz : missing cities in timezone list
  * Bug fix - piwiz : country-specific language packages not being installed
  * Bug fix - bookshelf : now waits for longer between packets before timing out
  * Bug fix - accented characters now displayed correctly in localisation dialogs
  * Raspberry Pi firmware e2bab29767e51c683a312df20014e3277275b8a6
  * Linux kernel 5.10.63
2021-05-07:
  * Chromium upgraded to version 88.0.4324.187
  * NuScratch upgraded to version 20210507
  * Node-RED upgraded to version 1.3.4
  * pigpio upgraded to version 1.79
  * Thonny upgraded to version 3.3.6
  * Icelandic and Italian translations updated for several packages
  * piclone: Remove hiding of application in other desktops
  * agnostics: Remove hiding of app in other desktops
  * rp-bookshelf:
    - Remove hiding of app in other desktops
    - GTK+3 version
  * lxplug-bluetooth:
    - Fix some memory leaks
    - Add authorisation dialog required by some BT-LE pairings
  * alsa-utils: Add custom init files for bcm2835 on Raspberry Pi to set volume correctly
  * rp-prefapps: Remove hiding of app in other desktops
  * OpenSSH and OpenSSL speed improvements
  * Install gpiozero in lite images
  * Raspberry Pi firmware 518ee7c871aaa9aaa88116953d57e73787ee6e43
  * Linux kernel 5.10.17
2021-03-04:
  * Thonny upgraded to version 3.3.5
  * SD Card Copier made compatible with NVMe devices; now built against GTK+3 toolkit
  * Composite video options removed from Raspberry Pi 4 in Raspberry Pi Configuration
  * Boot order options in raspi-config adjusted for more flexibility
  * Recommended Software now built against GTK+3 toolkit
  * Fix for crash in volume plugin when using keyboard could push value out of range
  * Fix for focus changing between windows in file manager when using keyboard to navigate directory view
  * Fix for Raspberry Pi 400 keyboard country not being read correctly in startup wizard
  * Armenian and Japanese translations added to several packages
  * Automatically load aes-neon-bs on ARM64 to speed up OpenSSL
  * Raspberry Pi firmware fcf8d2f7639ad8d0330db9c8db9b71bd33eaaa28
  * Linux kernel 5.10.17
2021-01-11:
  * Chromium version 86.0.4240.197 included
  * Screen reader support enabled in Chromium
  * Adobe have end-of-lifed Flash Player, so it has been removed
  * Scratch 2 required Flash, so it has been removed
  * Added Epson printer drivers
  * Added timeout to hide messages from USB device monitor after 5 seconds
  * Bug fix - PulseAudio output was in mono
  * Bug fix - brief audio interruptions at start of playback in VLC
  * Bug fix - old ALSA output settings being used instead of PulseAudio settings by some applications
  * Bug fix - crash in PulseAudio volume controller when used on multichannel devices
  * Bug fix - battery monitor failing to load on x86 platforms
  * Bug fix - setting of password in startup wizard failed if language was changed
  * Bug fix - Chromium video playback lockup on small number of devices
  * Bug fix - Chromium Google Maps 3D view artefacts
  * Slovak, Italian and Norwegian translations updated
  * Added Epson printer drivers
  * Raspberry Pi firmware 70f1581eec2c036b7e9309f1af41c651fb125447
  * Linux kernel 5.4.83
2020-12-02:
  * PulseAudio now included and running by default
  * Bluealsa Bluetooth interface removed - Bluetooth audio is now handled by PulseAudio
  * LXPanel volume control plugin replaced with PulseAudio version
  * Version 84.0.4147.105 of Chromium web browser included
  * Version 3.3.0 of Thonny included
  * Version 32.0.0.453 of Flash player included - note that this will be the final release of Flash, as it is end-of-lifed at the end of 2020
  * CUPS printer system included, along with system-config-printer CUPS GUI and HP printer drivers
  * raspi-config menu structure rearranged to match Raspberry Pi Configuration tabs
  * Control for GPIO-connected fans added to raspi-config and Raspberry Pi Configuration
  * Control for power / activity LED on Pi 400 and Pi Zero added to raspi-config and Raspberry Pi Configuration
  * Improved screen reader voice prompts in several applications
  * Added ctrl-alt-space shortcut to install Orca screen reader at any point
  * Low voltage warnings added to battery monitor plugin
  * Magnifier plugin zoom can now be changed with scroll wheel when pointer is over icon
  * Change to notification popups - now will only close when clicked on directly, not by clicking anywhere
  * Bookshelf now made compatible with translated versions of books and magazines, and will offer translated versions where available, based on system language setting
  * Bug fix - crash in CPU temperature plugin when throttling detection fails
  * Bug fix - if Orca is running, shutdown commands and shutdown dialog will force kill it to prevent it locking up the reboot or shutdown process
  * Various additional language translations added
  * Various minor bug fixes and UI tweaks
  * Raspberry Pi firmware b324aea801f669b6ab18441f970e74a5a7346684
  * Linux kernel 5.4.79
2020-08-20:
  * raspi-config - added selection of boot device order
  * raspi-config - added selection of boot EEPROM version
  * SD Card Copier - copy is now immediately aborted if drives are connected or disconnected while copying
  * Version 32.0.0.414 of Flash player included
  * User feedback survey removed from first run of Chromium
  * Recommended Software - now allows multiple install and reinstall operations without having to close between each one
  * Bug fix - misleading file browser from panel menu icon selection dialog - icons must now be in icon theme rather than arbitrary files
  * Bug fix - items in main menu not being translated
  * Bug fix - raspi-config not detecting audio devices in non-English locales
  * Bug fix - Bookshelf claiming no disk space in non-English locales
  * Bug fix - failed installation of both 32 and 64 bit versions of packages by Recommended Software on 64-bit images
  * Italian translations added (thanks to Emanuele Goldoni and the Italian translation team)
  * Raspberry Pi firmware ef72c17bcaaeb89093d87bcf71f3228e1b5e1fff
  * Linux kernel 5.4.51
2020-05-27:
  * Added Bookshelf application
  * Added Raspberry Pi Diagnostics application
  * Added magnifier plugin to taskbar - needs magnifier application installed from Recommended Software to enable
  * Added Magnifier application to Recommended Software
  * Added marketing questionnaire as initial Chromium tab
  * Version 0.25 of Scratch 2 included - uses external application to access IMU on SenseHAT
  * Version 1.0.5 of Scratch 3 included - uses external application to access IMU on SenseHAT
  * Version 32.0.0.371 of Flash player included
  * Version 1.0.6 of Node-RED included
  * Version 6.7.1 of VNC Server included
  * Version 6.20.113 of VNC Client included
  * Internal audio outputs enabled as separate ALSA devices
  * MagPi preinstall removed and replaced with Beginnerâ€™s Guide
  * MagPi weblink removed from main menu
  * Chromium made default application for PDF files
  * Common icon loading code for lxpanel plugins used
  * Italian translations added
  * Initial move of mouse pointer to menu button disabled
  * Padding at left of menu button removed
  * Focus behaviour changed so that focus moves to desktop if no windows are opened - improves reliability of Orca screen reader
  * Bug fix - focus bug in volume plugin
  * Bug fix - keyboard repeat interval bug in Mouse & Keyboard Settings
  * Bug fix - battery detection bug in battery plugin
  * Bug fix - spurious active areas on taskbar when plugins are hidden
  * Bug fix - occasional crash in file manager on file selection
  * Disk ID is now regenerated on first boot
  * Updated udev rules
    - Remove unused argon rule
    - Add vcsm-cma to video group
    - Add pwm to gpio group
  * i2cprobe: More flexible I2C/SPI alias mapping
  * Raspberry Pi firmware 21e1fe3477ffb708a5736ed61a924fd650031136
  * Linux kernel 4.19.118
2020-02-13:
  * Raspberry Pi Configuration - screen blanking setting disabled if Xscreensaver is installed
  * Bug fix - switch to turn off VNC server in Raspberry Pi Configuration has no effect
  * Bug fix - fix %20 characters in file names
  * Linux kernel 4.19.97
  * Raspberry Pi firmware 9a34efbf2fc6a27231607ce91a7cb6bf3bdbc0c5
    - gencmd: Fix measure_clock name for CLOCK_OUTPUT_108
    - mmal isp: Remote alignment requirements for RGB24 formats
    - Add missing flags for VC_IMAGE_PROP_YUVUV_4K_CHROMA_ALIGN
    - platform: Compromise on gpu overclock settings
2020-02-05:
  * Version 3.2.6 of Thonny included - significant improvements in speed, particularly when debugging
  * Version 1.0.4 of Scratch 3 included - adds new "display stage" and "display sprite" blocks to SenseHAT extension, and loading of files from command line
  * Version 32.0.0.314 of Flash player included
  * Version 1.0.3 of NodeRED included
  * Version 6.6.0 of RealVNC Server and version 6.19.923 of RealVNC Viewer included - adds support for audio
  * Version 78.0.3904.108 of Chromium included
  * Mesa updated to 19.3.2 for OpenGL ES 3.1 conformance
  * Pixel doubling option added in Raspberry Pi Configuration on platforms using FKMS display driver
  * Orca screen reader added to Recommended Software
  * Code The Classics Python games added to Recommended Software
  * File manager - new "places" pane added at top of sidebar to show mounted drives in simplified view; "new folder" icon added to taskbar; expanders in directory browser now correctly show state of subfolders
  * Multiple monitor support improved - alignment of icons on second desktop corrected, Appearance Settings opens on correct tab when launched from context menu
  * Raspberry Pi Touchscreen correctly aligned with display
  * System clock synchronised before installing new packages in startup wizard and Recommended Software
  * Mixer dialogs added to taskbar volume plugin; separate Audio Preferences application removed
  * Raspberry Pi Configuration - separate tab added for display options; screen blanking control added
  * Volume taskbar plugin and raspi-config modified to support separate ALSA devices for internal audio outputs (analogue and HDMI 1 and 2)
  * Robustness improvements in volume, ejecter and battery taskbar plugins
  * Movement of mouse pointer to menu button on startup now controlled by point_at_menu parameter in Global section of lxpanel configuration file
  * Ctrl-Alt-Del and Ctrl-Alt-End shortcuts added to open shutdown options box
  * Ctrl-Shift-Esc shortcut added to open task manager
  * Enabled NEON routines in OpenSSL
  * Linux kernel 4.19.97
  * Raspberry Pi firmware 149cd7f0487e08e148efe604f8d4d359541cecf4
2019-09-26:
  * rpi-eeprom included
    - This will automatically update the SPI EEPROM on the Raspberry Pi 4 to the latest stable version.
       See https://rpf.io/eeprom for more information.
  * New icon theme for file manager icons
  * Appearance Settings - option for identical desktop on both monitors
  * Appearance Settings - option to show different desktop icons on both monitors
  * Taskbar automatically moved to monitor 0 if monitor 1 not found at boot
  * Switching of audio output between two HDMI devices added to volume plugin
  * Switching of audio input devices added to volume plugin
  * .asoundrc (ALSA config file) now uses 'plug' values to support more devices
  * Audio Settings tool modified to integrate more closely with volume plugin to reduce duplicated code
  * Screen Configuration tool now shows separate menus for resolution and refresh rate
  * Primary and active monitor settings removed from Screen Configuration tool
  * Overscan support added for FKMS driver
  * New keyboard shortcuts added - Ctrl-Alt-End brings up shutdown menu; Ctrl-Alt-M moves taskbar between monitors
  * Latest changes to Bluez ALSA interface integrated to improve connection to Bluetooth audio devices
  * Mousepad used as simple text editor instead of leafpad
  * Version 3.2 of Thonny added
  * Version 74 of Chromium added
  * Version 3.0.8 of VLC added
  * Version 32.0.0.255 of Flash player added
  * Version 6.5.0 of RealVNC Server added
  * Version 6.19.715 of RealVNC Viewer added (full image only)
  * Version 12.0.1 of Mathematica added (full image only)
  * Version 0.20.8 of NodeRED added (full image only)
  * Version 3.1.0 of Sonic Pi added (full image only)
  * Scratch 3 added (full image only)
  * Bug fix - URL handling in Terminal
  * Bug fix - octal values in SSIDs in network plugin
  * Bug fix - remaining value in progress bar when transferring files
  * Bug fix - integration of xarchiver tool with file manager
  * Bug fix - start menu opening on incorrect monitor
  * Bug fix - minimised applications wrongly displayed on taskbar on second monitor
  * Bug fix - Bluetooth icon disappearing on x86 platforms when Bluetooth turned off
  * Bug fix - Screen Configuration tool not shown on x86 platforms and settings not being saved
  * Various translation updates
  * Various minor bug fixes
  * Epiphany/Web removed
  * ntfs-3g included
  * pciutils added
  * Linux kernel 4.19.75
  * Raspberry Pi firmware 01508e81ec1e918448227ca864616d56c430b46d
2019-07-10:
  * Clearer options for switching of Pi 4 video output in Raspberry Pi Configuration
  * Option added to Appearance Settings to move taskbar to second monitor
  * Option added to Recommended Software to restrict package installs by architecture
  * New version of Adobe Flash player (**********)
  * Selection of screen refresh rates added to Screen Configuration
  * Fix for missing text insertion cursor in LibreOffice on Pi 4
  * Fix for Wi-fi interruption when Wi-fi icon on taskbar is clicked
  * FIx for incorrect desktop background behind desktop login prompt
  * Fix for segmentation faults when launching obconf and lxapperarance
  * Fix for unclosed file pointer in Screen Configuration
  * Fix for Bluetooth plugin freeze when large numbers of devices detected
  * Fix for opening URLs not working in lxterminal
  * Fix for start menu opening on incorrect monitor when launched from keyboard
  * Fix for taskbar item not having [] removed when un-minimising on second monitor
  * Fix for Chromium video playback and WebGL performance on Pi 4
  * Remove 4kp60 option from Raspberry Pi Configuration
  * Rename hdmi_enable_4k to hdmi_enable_4kp60 in /boot/config.txt and raspi-config
  * Linux kernel 4.19.57
  * Raspberry Pi firmware 356f5c2880a3c7e8774025aa6fc934a617553e7b
2019-06-20:
  * Based on Debian Buster
  * Support for Raspberry Pi 4 hardware
  * FKMS OpenGL desktop graphics driver and xcompmgr compositing window manager used when running on Raspberry Pi 4
  * Screen Configuration application added for use with FKMS driver
  * Raspberry Pi 4 video output options added to Raspberry Pi Configuration
  * Uses new PiXflat UI theme for GTK and Openbox
  * CPU activity gauge plugin no longer shown on taskbar by default
  * CPU temperature gauge plugin added (not shown by default)
  * USB ejecter and Bluetooth taskbar icons hidden when not appropriate
  * Version 74.0.3729.157 of Chromium web browser included
  * Version 32.0.0.207 of Flash player included
  * IDLE Python IDE removed
  * Wolfram Mathematica removed temporarily due to incompatibility with Buster
  * Display of package sizes removed from Recommended Software
  * Appearance Settings modified to support independent settings for two monitors
  * Oracle Java 7 and 8 replaced with OpenJDK 11
  * Miscellaneous small bug fixes
  * On-board 5GHz WiFi blocked by rfkill by default
    The block is removed when taking one of the following actions:
    - Selecting a locale in the first run wizard
    - Setting the WiFi country in the Raspberry Pi Configuration tool or the Network Settings applet
    - Setting the WiFi country in raspi-config
    - Providing a wpa_supplicant.conf file through the boot partition
    - Running 'rfkill unblock wifi'
  * Boot partition size set to 256M
  * Linux kernel 4.19.50
  * Raspberry Pi firmware 88ca9081f5e51cdedd16d5dbc85ed12a25123201
2019-04-08:
  * Chromium browser updated to version 72
  * VLC media player updated to version 3.0.6
  * RealVNC Server updated to version 6.4.0
  * Flash player updated to version 32.0.0.156
  * Performance improvements to SDL library
  * Performance improvements to pixman library
  * Option to set display underscan added to startup wizard
  * Mounted external drives now displayed on desktop by default
  * Network plugin modified for improved compatibility with wpa_passphrase
  * SD Card Copier tweaks to reduce copy failures
  * Various minor bug fixes and appearance tweaks
  * Added ethtool
  * Added rng-tools
  * Add PINN restore support
  * Linux kernel 4.14.98
  * Raspberry Pi firmware f8939644f7bd3065068787f1f92b3f3c79cf3de9
2018-11-13:
  * Two versions of image created - "base" image has no optional software packages included; "full" image has all optional packages
    - Removed from "base" image - LibreOffice, Thonny, Scratch, Scratch 2, Sonic Pi, Minecraft, Python Games, SmartSim, SenseHAT Emulator
    - Added to "full" image - Mathematica, BlueJ, Greenfoot, Node-RED, Claws Mail, VNC Viewer
  * Python Games and SmartSim added to Recommended Software
  * VLC media player with VideoCore hardware acceleration included in image
  * Version 3.0.5 of Thonny included
  * Modifications to LXDE components to enable local configuration to override global configuration correctly
  * Modifications to Appearance Settings to support above configuration changes
  * Modifications to various initial config defaults and relevant package to support above configuration changes
  * Selecting default option in Appearance Settings now deletes relevant local configuration files
  * PiX theme modified so that all changes made in Appearance Settings are in override files rather than in theme files
  * Design of scrollbar buttons changed
  * Image Viewer moved into Graphics category on main menu
  * Recommended Software now installs LibreOffice language support files if needed, and suggests reboot if needed
  * Latest version of Pepper Flash plugin included
  * Chromium h264ify plugin permissions set correctly by default
  * Corrections to various MIME types so that files open in sensible default applications
  * Set default timezone to 'Europe/London'
  * Linux kernel 4.14.79
  * Raspberry Pi firmware 12e0bf86e08d6067372bc0a45d7e8a10d3113210
2018-10-09:
  * Raspberry Pi 3A+ support
  * In startup wizard, assign keyboard to country as per Debian installer recommendations
  * In startup wizard, add option to use US keyboard in preference to country-specific option
  * In startup wizard, show IP address on first page
  * In startup wizard, check for existing wifi network connection and show it if there is one
  * In startup wizard, install language support packages for LibreOffice and other applications
  * In startup wizard, improve operation with keyboard only and no mouse
  * Password change in Raspberry Pi Configuration and startup wizard now works properly if passwords contain shell characters
  * Battery indicator plugin modified to cope with Pi-top hardware monitor crashing
  * Networking plugin hides wifi password characters by default
  * In Scratch 2 GPIO plugin, set pin from dropdown list rather than free text
  * In Scratch 2 SenseHAT plugin, swap x and y axis values for LED array
  * Include latest Adobe Flash player (**********)
  * Include latest RealVNC Server (6.3.1)
  * Include libav-tools
  * Include ssh-import-id
  * Removed Mathematica
  * Merge in latest third-party code for Bluetooth ALSA interface
  * Add ability to prevent software update changing configuration files, by creating ~/.config/.lock file
  * Various other small bug fixes, tweaks and changes to text
  * Make dhcpcd work with 3G devices
  * Add hw acceleration to ffmpeg
  * Improved WiFi-BT coexistence parameters
  * Run fake-hwclock before systemd-fsck-root
  * Raspberry Pi PoE HAT support
  * Linux kernel 4.14.71
  * Raspberry Pi firmware 5b49caa17e91d0e64024380119ad739bb201c674
2018-06-27:
  * New first-boot configuration wizard added
  * Recommended Software installer added
  * Bluej, Greenfoot, NodeRED, Claws Mail, VNC Viewer removed from image - can now be installed from Recommended Applications
  * Qpdfview PDF viewer installed instead of Xpdf
  * Version 65.0 of Chromium browser included, with latest Flash player
  * Volume up / down keys now change by 5% increments and affect currently-selected output device rather than internal device only
  * Network plugin now remembers previously-entered WiFi network passwords when prompting for reconnection
  * Serial port and serial console can now be switched separately in Raspberry Pi Configuration
  * Lxkeymap keyboard language setting application removed - replaced with dialog within Raspberry Pi Configuration
  * Wifi country and keyboard language setting dialogs in Raspberry Pi Configuration now callable from other applications
  * New version of Piboto font included to render with correct weight under some rogue applications
  * Reconnection to Bluetooth audio devices on reboot improved
  * Disable click-to-rename behaviour in file manager if single-click selection enabled
  * Appearance Settings dialog makes config changes to some Qt files to match selected theme
  * MIME file type associations improved
  * Multiple desktop management options removed from mouse middle-click menu
  * Menu shortcuts to Raspberry Pi website amended
  * Python 2 IDLE menu link removed
  * Sample Magpi PDF installed in /home/<USER>/MagPi
  * Various minor tweaks, bug fixes and appearance changes
  * Bluetooth updates
    - Firmware with Bluetooth 4.2 features
    - SCO profile suppot added via bthelper.service
  * Linux kernel 4.14.50+
  * Raspberry Pi firmware 748fb17992426bb29d99224b93cb962fefbdc833
2018-04-18:
  * Fixed race between wifi-country.service and raspberrypi-net-mods.service
  * Linux kernel 4.14.34+
  * Raspberry Pi firmware 5db8e4e1c63178e200d6fbea23ed4a9bf4656658
2018-03-13:
  * Raspberry Pi 3 B+ support
  * WiFi is disabled until wireless regulatory domain is set (Pi 3 B+ only)
    - The domain can be done through 'Raspberry Pi Configuration' (rc_gui),
      'raspi-config' or by setting 'country=' to an appropriate ISO 3166
      alpha2 country code in /etc/wpa_supplicant/wpa_supplicant.conf.
  * Default wireless regulatory domain is now unset
  * Added support to desktop for different screen sizes and resolutions,
    including multiple preset options in Appearance Settings and pixel doubling
    option in Raspberry Pi Configuration
  * Version 2.1.16 of Thonny included
  * Version ********** of Adobe PepperFlash player included
  * Version 1.2.post1 of Pygame Zero included
  * Bluetooth plugin now supports connection to Bluetooth LE HID devices
  * Network plugin now indicates 5G-compatible APs
  * Latest changes to Bluez ALSA service merged
    - service now started on CLI boot as well as GUI boot
  * Latest changes to dhcpcd networking plugin merged
  * Improved support for running on pi-top devices
  * Small design changes to PiX theme and icons
  * Bug fix - hide spurious window resize handles
  * Bug fix - Scratch 2 remote GPIO state block now works correctly
  * Updated WiFi Firmware
    - brcmfmac43455-sdio 7.45.154
    - brcmfmac43430-sdio **********
  * New packages:
    - policykit-1
    - obconf
    - python-buttonshim python3-buttonshim
    - python-unicornhathd  python3-unicornhathd
    - python-pantilthat python3-pantilthat
  * Linux kernel 4.9.80+
  * Raspberry Pi firmware 3347884c7df574bbabeff6dca63caf686e629699
2017-11-29:
  * Added battery monitor plugin for taskbar - works on x86 images or first-generation Pi-Top
  * Added cutdown mode to PCManFM file manager to reduce complexity
  * Added ability to rename files in PCManFM by clicking name when selected
  * Bug fix in Bluetooth ALSA module to reduce truncation of audio at end of playback
  * Various small tweaks, bug fixes and theme modifications
  * New kernel and firmware
2017-09-07:
  * Disable predictable network interface names for Ethernet devices
  * Bug fix for keyboard settings dialog in Raspberry Pi Configuration
  * Bug fix for crash on some videos and animations in Chromium
  * Bug fix for taskbar crash when running RealVNC server
  * Bug fix for reloading projects with extensions in Scratch 2
  * Bug fix for MAC address problem in Bluetooth
  * Simple mode and new icons in Thonny
  * New Japanese translations in Raspberry Pi Configuration
  * Install fonts-droid-fallback for international fonts
2017-08-16:
  * Based on Raspbian Stretch (Debian version 9)
  * Version 60 of Chromium browser included
  * Version 3.0.1 of Sonic Pi included
  * Version 6.1.1 of RealVNC included
  * Version 0.17.4 of NodeRED included
  * Bluetooth audio routed via ALSA rather than Pulseaudio
  * SenseHAT extension added to Scratch 2
  * Various desktop applications modified to prompt for sudo password if needed
  * lxinput control options for mouse speed simplified
  * lxpanel plugins moved into separate packages
  * Wireless firmware for Pi 3 and Pi 0W modified to address Broadpwn exploit
  * Latest kernel and firmware
  * Various small tweaks, bug fixes and theme modifications
2017-07-05:
  * New kernel and firmware
  * Filesystem created without the metadata_csum feature
2017-06-21:
  * Scratch 2 application included
  * Thonny Python IDE included
  * New icons with thinner outlines
  * Volume control more linear in behaviour
  * Updated Flash player
  * Updated RealVNC server and viewer
  * Various tweaks and bugfixes
  * New kernel and firmware
2017-04-10:
  * Wolfram Mathematica updated to version 11.0.1
  * Adobe Flash Player updated to version **********
  * Use PARTUUID to support USB boot
2017-03-02:
  * Updated kernel and firmware (final Pi Zero W support)
  * Wolfram Mathematica updated to version 11
  * NOOBS installs now checks for presence of 'ssh' file on the NOOBS partition.
2017-02-16:
  * Chromium browser updated to version 56
  * Adobe Flash Player updated to version **********
  * RealVNC Server and Viewer updated to version 6.0.2 (RealVNC Connect)
  * Sonic Pi updated to version 2.11
  * Node-RED updated to version 0.15.3
  * Scratch updated to version 120117
  * Detection of SSH enabled with default password moved into PAM
  * Updated desktop GL driver to support use of fake KMS option
  * Raspberry Pi Configuration and raspi-config allow setting of fixed HDMI resolution
  * raspi-config allows enabling of serial hardware independent of serial terminal
  * Updates to kernel and firmware
  * Various minor bug fixes and usability and appearance tweaks
2017-01-11:
  * Re-release of the 2016-11-25 image with a FAT32-formatted boot partition
2016-11-25:
  * SSH disabled by default; can be enabled by creating a file with name "ssh" in boot partition
  * Prompt for password change at boot when SSH enabled with default password unchanged
  * Adobe Flash Player included
  * Updates to hardware video acceleration in Chromium browser
  * Greeter now uses background image from last set in Appearance Settings rather than pi user
  * Updated version of Scratch
  * Rastrack option removed from raspi-config and Raspberry Pi Configuration
  * Ability to disable graphical boot splash screen added to raspi-config and Raspberry Pi Configuration
  * Appearance Settings dialog made tabbed to work better on small screens
  * Raspberry Pi Configuration now requires current password to change password
  * Various small bug fixes
  * Updated firmware and kernel
2016-09-23:
  * New PIXEL desktop environment - new icon set, window design, desktop images, splash screen and greeter
  * Chromium web browser included
  * Infinality font rendering patches included
  * RealVNC server and viewer included
  * SenseHAT emulator included
  * Rfkill entries added to Wifi and Bluetooth panel plugins
  * Updates to various standard applications, including Scratch and NodeRED
  * Various bug fixes, tweaks and translation updates
  * Updated firmware and kernel (https://github.com/raspberrypi/firmware/commit/ad8608c08b122b2c228dba0ff5070d6e9519faf5)
2016-05-27:
  * Fixed crash of lxpanel when D-bus not accessible
  * Fixed permissions for D-bus Bluetooth access
  * Removed sudo from shutdown options
  * Appearance of tooltips updated in theme
  * Fixed ejecter plugin grabbing focus
  * raspi-config command line and GUI apps tidied; unnecessary reboots removed
  * More error detection in piclone; copying of volume names and IDs added
  * Updated translation files
2016-05-10:
  * New version of Scratch, which no longer requires sudo
  * New version of BlueJ
  * New version of NodeRED
  * New version of pypy
  * pigpio included
  * geany editor included
  * SD Card Copier added (can be used to duplicate or back up the Pi)
  * Bluetooth plugin added to taskbar
  * Volume control on taskbar now compatible with Bluetooth devices
  * New shutdown helper application
  * Mouse double-click speed setting added to mouse and keyboard preference application
  * Option to enable / disable 1-wire interface and remote access to pigpio added to Raspberry Pi config application
  * File system automatically expanded on first boot
  * Empty Wastebasket option added to right-click menu
  * Ctrl-Alt-T can be used to open a terminal window
  * Various small bug fixes and appearance tweaks
  * Updated firmware and kernel (https://github.com/raspberrypi/firmware/commit/cc6d7bf8b4c03a2a660ff9fdf4083fc165620866)
2016-03-18:
  * updated firmware and kernel (https://github.com/raspberrypi/firmware/commit/951799bbcd795ddf27769d14acf4813fdcbe53dc)
  * use serial0 in cmdline.txt
  * wpa_supplicant.conf country default to GB (allows use of channels 12 and 13)
2016-02-26:
  * Support added for Pi 3, including Wifi and Bluetooth
  * Option to set wifi country code added to raspi-config
2016-02-09:
  * dtb that uses mmc sdcard driver (fixes problems experienced with certain SD cards)
2016-02-03:
  * new version of Sonic Pi (2.9)
  * new version of Scratch (15/1/16)
  * new version of Node-Red (2.5)
  * new version of Wolfram (10.3)
  * optional experimental GL desktop driver (can be enabled using advanced options in command-line raspi-config)
  * new version of Java (1.8.0_65)
  * new version of WiringPi
  * raspi-gpio included
  * ping no longer requires sudo (except NOOBS installs)
  * support for more USB audio devices in lxpanel
  * bug fix for creation of new menus in Alacarte
  * various changes to raspi-config and GUI to tidy up board support and fix bugs, and updated translations
  * small tweaks to theme to support GL driver
2015-11-21:
  * Included IBM Node-RED IoT application
  * Included graphical package manager
  * Included accelerated pixman library
  * Updated Epiphany browser to improve video compatibility
  * Updated Scratch with performance improvements and bug fixes
  * Updated Raspberry Pi configuration to allow boot to pause while
    network is established
  * Various minor bug fixes
2015-09-25:
  * Based on Debian Jessie
  * Upgraded applications - Epiphany browser, Scratch and Sonic Pi
  * Included applications - LibreOffice, Claws Mail, Greenfoot, BlueJ
  * Included utilities - Alacarte menu editor, Lxkeymap, scrot, tree, pip
  * New GUI-based Raspberry Pi Configuration application
  * GPIO control now possible without need for sudo
  * Web link to Magpi magazine included
  * New taskbar plugin to eject mounted USB drives
  * Default boot is now to GUI not desktop
  * Look and feel now based on GTK+3 default theme
  * Print screen key launches scrot to produce screenshot
  * Common keyboards autodetected by GUI and drivers loaded accordingly
  * Numerous small tweaks and bugfixes
2015-05-05:
  * Updated UI changes
  * Updated firmware
  * Install raspberrypi-net-mods
  * Install avahi-daemon
  * Add user pi to new i2c and spi groups
  * Modified udev rules for i2c and spi devices
2015-02-16:
  * Newer firmware with various fixes
  * New Sonic Pi release
  * Pi2 compatible RPi.GPIO
  * Updated Wolfram Mathematica
2015-01-31:
  * Support for Pi2
  * Newer firmware
  * New Sonic Pi release
  * Updated Scratch
  * New Wolfram Mathematica release
  * Updated Epiphany
2014-12-24:
  * Fix regression with omission of python-pygame
2014-12-22:
  * New firmware with variosu fixes and improvements
  * New UI configuration for lxde
  * Various package updates
  * python3-pygame preinstalled
  * 'nuscratch', scratch running on the Cog StackVM
  * Misc other changes
2014-09-09:
  * New firmware with various fixes and improvements
  * Minecraft Pi pre-installed
  * Sonic Pi upgraded to 2.0
  * Include Epiphany browser work from Collabora
  * Switch to Java 8 from Java 7
  * Updated Mathematica
  * Misc minor configuration changes
2014-06-20:
  * New firmware with various fixes, and kernel bugfix
2014-06-02:
  * Many, many firmware updates with major USB improvements
  * pyserial installed by default
  * picamera installed by default
2014-01-07:
  * Firmware updated
  * Some space saved on the root filesystem
2013-12-20:
  * Firmware updated, includes V4L2 fixes
  * Update omxplayer
2013-12-18:
  * Firmware updated and now using kernel 3.10. Many, many improvements
  * fbturbo XOrg driver is now included and enabled by default. Thanks to
    ssvb https://github.com/ssvb/xf86-video-fbturbo
  * Update Scratch image with further bug fixes
  * Include Wolfram Mathematica
  * Update to PyPy 2.2
  * Update omxplayer
  * Include v4l-utils for use with experimental V4L2 Raspberry Pi camera driver
  * Update squeak-vm to fix issues with loading JPEGs
2013-09-25:
  * Update Scratch image for further performance improvements
  * Include Oracle JDK
  * At least a 4GiB SD card is now required (see above)
  * Include PyPy 2.1
  * Include base piface packages
  * Update raspi-config to include bugfix for inheriting language settings
    from NOOBS
2013-09-10:
  * Updated to current top of tree firmware
  * Update squeak-vm, including fastblit optimised for the Raspbery Pi
  * Include Sonic Pi and a fixed jackd2 package
  * Support boot to Scratch
  * Inherit keyboard and language settings from NOOBS