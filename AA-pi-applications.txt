
There are many "applications" that we run on the Raspberry Pi:

monitor: This is the simplest one, that should change the least.
        Its purpose is to call home to the server, every 30 minutes,
            and report the ip address of the device,
            so that we can remote back into it using ssh.

runner: This is the application that also checks into the server, once a minute,
        to check for new content: profiles, application updates, settings, ...



------------------------------
localhost web servers
------------------------------
(search for "class MyServer")

port    application
----    ------------
6999    <USER>
<GROUP>    config
7010    hmi H.5.1         to build profile specific dynamic pages, to be shown locally
7020    logging
7040    network_*
7080    bluetooth

file:///cardinal/localhtml/index.html   hmi

File interactions were the original, because it was simple to start.
    However, as more features were needed, (POST of data, or dynamic content), file
    access became very cumbersome. So, the "from http.server import" of HTTPServer
    became a lightweight way to go; and are only accessed on the device itself.


