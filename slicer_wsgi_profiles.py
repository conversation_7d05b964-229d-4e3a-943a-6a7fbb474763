# A profiles for slicer page services

service = "profiles"
version = service + '.0.1'

_ = """
need to build a full aware profile editor, that shows all items: bookmark order, bookmark title, url, allow list, hosts, bypass, fragile

These get consumed in pi_runner, and pi_hmi (fragile)

bypass was useful when the require host is internal, and does not have an X.Y.Z url format,
    but rather is like "http://wpec0409oeewb01/OEEAlert/DisplayView/Index"

"""

_ = """
This file gets loaded to:
/var/www/html/profiles.py

using:
sudo vi /var/www/html/profiles.py

It also requires:

sudo vi /etc/httpd/conf.d/python-profiles.conf
----- start copy -----
WSGIScriptAlias /profiles /var/www/html/profiles.py
----- end copy -----

sudo chown apache:apache /var/www/html/profiles.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import profiles; print(profiles.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/profiles



"""

import cgi
import datetime
from tempfile import TemporaryFile
import copy
import traceback
import json
import os
import sys
import time

startup_exceptions = ''

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import pytz

    import login
    import permissions
    import datastore
    import reports
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

color_yellow_caution = '(255, 255, 100, 0.3)'

# ----------------------------
def make_screen_threshold_name_from_profile_name(profile_name):
    # ----------------------------
    if 'profile_' in profile_name:
        return_value = profile_name.replace('profile_','screen_threshold_')
    else:
        return_value = 'screen_threshold_' + profile_name

    return return_value

# ----------------------------
def make_profile_screen_threshold(data_store_content, profile_name):
    # ----------------------------
    datastore_screen_threshold_name = make_screen_threshold_name_from_profile_name(profile_name)
    screen_threshold = datastore.get_value(datastore_screen_threshold_name)

    try:
        return_value = int(screen_threshold)
    except:
        return_value = 0

    return return_value

# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def get_all_timezones():
    # ----------------------------
    timezones = []
    try:
        pass_string, fail_string = do_one_command("timedatectl list-timezones --no-pager")
        for item in pass_string.split('\n'):
            if item:
                timezones.append(item)
    except:
        pass
    return timezones


# ----------------------------
def do_one_command(command):
    # ----------------------------
    import shlex
    import subprocess
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


s_timezone_override_list = [''] + get_all_timezones()  # ,'America/Chihuahua','America/Denver']
s_percent_list = []
for value_percent in range(0,100):
#    s_percent_list.append('{:03d}'.format(value_percent))
    s_percent_list.append(str(value_percent))


s_profile_items = []
#    s_profile_items['name'] = {'type':'string'}
s_profile_items.append({'field': 'preliminary',
                        'type': 'string',
                        'help': 'Any content here will mark this as non-production/preliminary.',
                        'restrict_fnc': 'clean_char_only_quote',
                        })

s_profile_items.append({'field': 'description',
                        'type': 'string',
                        'restrict_fnc': 'clean_char_only_quote',
                        })

s_profile_items.append({'field': 'timezone',
                        'prompt': 'timezone<br>(can be over-ridden)',
                        'type': 'string',
                        'restrict_fnc': 'clean_char_quote_and_space',
                        'help': 'Caution: If a timezone override is in place on the sites page, for any device using this profile, then that over-rides this setting here.',
                        'values_to_show':s_timezone_override_list,
                        'size':60,
                        })

s_profile_items.append({'field': 'screen_threshold', 'prompt': 'screen threshold', 'type': 'string',
                        'restrict_fnc': 'clean_char_quote_and_space',
                        'help': 'For this profile, what is the allowed percentage of the bottom of the screen that might be all black? NOTE: this is saved as its own datastore item, and does not go to the end device, and so changes here do not cause a need for browser restart.',
                        'values_to_show':s_percent_list,
                        'size':20,
                        })

s_profile_items.append({'field': 'bookmarks',
                        'type': 'bookmarks',
                        })

s_bookmark_items = []
s_bookmark_items.append({'field': 'autolaunch', 'type': 'radio_button',
                         'help': '[True/(blank)] Use this to mark if this link should auto launch.',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'format', 'type': 'string', 'help': 'Use the word tabs for tabbed mode display.',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append(
    {'field': 'title', 'type': 'string', 'help': 'The text to show in the browser as the link to be clicked.',
     'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'url', 'type': 'string', 'help': 'The url to go to when the link is clicked.',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'whitelist', 'prompt': 'proxy allow List', 'type': 'list_of_string',
                         'help': 'The list of sites required by the url.',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'bypass', 'prompt': 'bypass proxy', 'type': 'list_of_string',
                         'help': 'Use this list, instead of the allow list, for sites that can not suffer the proxy side effects.',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'hosts', 'type': 'list_of_string',
                         'help': 'Use this to enter ip address and name, for required sites, that are not in DNS (e.g.: ********** wpec5009scsql10)',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append(
    {'field': 'fragile', 'type': 'list_of_string', 'help': 'List of sites to add to the fragile section of the proxy (allows cookies to work).',
     'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append({'field': 'disablekiosk', 'type': 'string', 'help': 'set to (yes) to disable kiosk mode',
                         'restrict_fnc': 'clean_char_quote_and_space'})
s_bookmark_items.append(
    {'field': 'disableincognito', 'type': 'string', 'help': 'set to (yes) to disable incognito mode',
     'restrict_fnc': 'clean_char_quote_and_space'})


# s_bookmark_items.append({'field':'logo','type':'string', 'help':'', 'restrict_fnc':'clean_char_quote_and_space'})


# ====================================
class profile_class():
    # ====================================
    def __init__(self, starting_data='', submit_content={}):
        self.bookmarks = {}
        self.profile_top_level = {}
        self.__name = ''

        if starting_data:
            try:
                start_d = json.loads(starting_data)
                if 'bookmarks' in start_d:
                    self.bookmarks = start_d['bookmarks']['(id)']
            except:
                pass

        elif submit_content:
            if 'single_edit_profile_name' in submit_content:
                self.__name = submit_content['single_edit_profile_name']

            for profile_item in s_profile_items:
                key = profile_item['field']
                submit_key = 'single_edit_profile_' + key
                if submit_key in submit_content:
                    if submit_content[submit_key]:
                        if key == 'screen_threshold':
                            pass
                        elif key == 'timezone':
                            if submit_content[submit_key]:
                                self.profile_top_level[key] = submit_content[submit_key].split()[0]
                        else:
                            self.profile_top_level[key] = submit_content[submit_key]

            for bookmark_item in s_bookmark_items:
                key = bookmark_item['field']
                for submit_key in submit_content.keys():
                    if key in submit_key:
                        index_of_item = int(submit_key.split('_')[-1])
                        if submit_content[submit_key]:
                            properties = {key: submit_content[submit_key]}
                            self.set_bookmark_attribute(index_of_item, properties)

    def name(self):
        return self.__name

    def as_json(self):
        the_profile = {"bookmarks": {"(id)": self.bookmarks}}
        for key in self.profile_top_level:
            the_profile[key] = self.profile_top_level[key]
        return json.dumps(the_profile)

    def set_bookmark_attribute(self, id_int, properties):
        key_to_use = str(id_int)
        key_to_use = '0' * (2 - len(key_to_use)) + key_to_use
        for key in properties.keys():
            if not key_to_use in self.bookmarks:
                self.bookmarks[key_to_use] = {'title': '(missing)'}

            type_of_item = 'string'
            for item in s_bookmark_items:
                if key == item['field']:
                    type_of_item = item['type']
                    break

            if type_of_item == 'string':
                self.bookmarks[key_to_use][key] = properties[key]
            elif type_of_item == 'radio_button':
                if properties[key] == 'True':
                    self.bookmarks[key_to_use][key] = True
                else:
                    self.bookmarks[key_to_use][key] = False
            elif type_of_item == 'list_of_string':
                raw_items = properties[key].split(',')
                self.bookmarks[key_to_use][key] = []
                for raw_item in raw_items:
                    self.bookmarks[key_to_use][key].append(raw_item.strip())

    def add_bookmark(self, bookmark_content):
        key_to_use = str(len(self.bookmarks) + 1)
        key_to_use = '0' * (2 - len(key_to_use)) + key_to_use
        self.bookmarks[key_to_use] = json.loads(bookmark_content)

    def bookmark_list(self):
        return_value = []

        for key in sorted(self.bookmarks.keys()):
            bookmark_content = self.bookmarks[key]
            return_value.append(bookmark_content)
        return return_value


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')]}
    the_who = login.get_current_user(environ)

    try:
        # use cgi module to read data
        body_of_form = read(environ)
        field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

        # A structured page for a single profile, with many fields
        # https://slicer.cardinalhealth.net/profiles?profile=profile_zzz_bluetoothTest

        submit_content = {}
        screen_threshold = ''
        for item in field_storage.keys():
            if item == 'single_edit_profile_screen_threshold':
                screen_threshold = field_storage.getvalue(item)
            else:
                submit_content[item] = field_storage.getvalue(item)

        # submit_content = {'single_edit_profile_whitelist_1': '', 'profile': 'profile_Blank', 'single_edit_profile_hosts_1': '', 'single_edit_profile_bypass_1': '', 'single_edit_profile_name': 'profile_Blank', 'single_edit_profile_url_1': '', 'single_edit_profile_description': 'A blank profile, as a place holder.', 'dummy_profile_list[]': '', 'single_edit_profile_autolaunch_1': '', 'single_edit_profile_title_1': '', 'single_edit_profile_timezone': '', 'single_edit_profile_fragile_1': '1'}
        a_profile = profile_class(submit_content=submit_content)
        profile_string = a_profile.as_json()

        profile_name = 'profile_' + a_profile.name()
        datastore.set_value(profile_name, profile_string, who=the_who)

        datastore_screen_threshold_name = make_screen_threshold_name_from_profile_name(profile_name)
        datastore.set_value(datastore_screen_threshold_name, screen_threshold, who=the_who)

        # do redirect here, to make a name change be a clean page load (it also works even if the name did not change)
        url_to_use = make_home_url_from_environ(environ)
        redirect = url_to_use + '/profiles?profile=' + profile_name
        other = {'status': '303 See Other', 'response_header': [('Location', redirect)]}
        body = '(redirect)'

    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body_GET(environ):
    # ====================================

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'profile' in query_items:
        return make_body_GET_edit_single(environ)
    else:
        if permissions.permission_prefix_allowed(environ, 'profiles_edit'):
            return make_body_GET_edit(environ)

        else:
            return make_body_GET_view(environ)


# ====================================
def make_body_GET_edit_single(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    _ = """
watch inputs for change:
https://flexiple.com/disable-button-javascript/

    """

    try:
        query_items = {}
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]

        body = ''

        body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
        """

        name_to_show = "Home"
        url_to_use = make_home_url_from_environ(environ)
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

        profile_name = query_items['profile'][8:]
        json_string = datastore.get_value('profile_' + profile_name)

        if json_string:
            the_profile = profile_class(json_string)
            the_profile_d = json.loads(json_string)
        else:
            the_profile = profile_class('{}')
            the_profile_d = {}

        values_to_show = ['']
        for key_name in sorted(datastore.get_keys_starting_with('profile_')):
            values_to_show.append(key_name[8:])

        all_non_name_input_ids = []

        datastore_screen_threshold_name = make_screen_threshold_name_from_profile_name(profile_name)
        screen_threshold = datastore.get_value(datastore_screen_threshold_name)
        the_profile_d['screen_threshold'] = screen_threshold

        body += '<script>'

        # https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_code_values
        body += """function clean_char_quote_and_space(e){
            console.log('clean_char_quote_and_space:');
            console.log(e.code);
            if (e.code === 'Space')
                {
                    console.log('blocked');
                    return false;
                }
            if (e.code === 'Quote')
                {
                    console.log('blocked');
                    return false;
                }
            console.log('allowed');
            }"""

        body += """function clean_char_only_quote(e){
            console.log('clean_char_only_quote:');
            console.log(e.code);
            if (e.code === 'Quote')
                {
                    console.log('blocked');
                    return false;
                }
            console.log('allowed');
            }"""

        body += '</script>'

        body += '<center>'
        body += '<form method="post" action="">'
        body += '<table border="1" cellpadding="5">'

        # Hard code the name work
        body += '<tr>'
        body += '<td>'
        body += '<B>Name</B>'
        body += '</td>'
        body += '<td>'
        body += 'profile_'

        # https://stackoverflow.com/questions/14236873/disable-spaces-in-input-and-allow-back-arrow
        body += """<input id="input_name" class="input" type="text" size=45 onkeydown="return clean_char_quote_and_space(event)" name="single_edit_profile_name" value=\"""" + profile_name + """\">"""
        body += '</td>'
        body += '<td>'
        body += '<text id="input_name_output"></text>'
        body += '</td>'
        body += '<td>'
        body += 'existing profile names:<br>(Do not re-use)<br><br>'

        body += '<select name="dummy_profile_list[]" id="profile_list">'
        for key_name in values_to_show:
            body += '<option value="' + key_name + '">' + key_name + '</option>'
        body += '</select>'

        body += '</td>'
        body += '</tr>'

        copy_bookmark_commands = {}
        clear_bookmark_commands = {}
        moveup_bookmark_commands = {}

        for profile_item in s_profile_items:
            body += '<tr>'

            the_help = ''
            if 'help' in profile_item:
                the_help = profile_item['help']

            prompt_to_use = profile_item['field']
            if 'prompt' in profile_item:
                prompt_to_use = profile_item['prompt']

            body += '<td title="' + the_help + '">'
            body += '<B>' + prompt_to_use + '</B>'
            body += '</td>'

            if profile_item['type'] == 'string':
                the_value = ''
                if profile_item['field'] in the_profile_d:
                    the_value = str(the_profile_d[profile_item['field']])

                body += '<td>'

                the_id = 'input_' + profile_item['field']
                all_non_name_input_ids.append(the_id)

                current_time = pytz.utc.localize(datetime.datetime.utcnow())

                if 'values_to_show' in profile_item:
                    body += '<select name="single_edit_profile_' + profile_item['field'] + '" id="' + the_id + '">'
                    for key_name in profile_item['values_to_show']:
                        if the_value == key_name:
                            body += '<option value="' + key_name + '" selected>' + key_name + '</option>'
                        else:
                            body += '<option value="' + key_name + '">' + key_name + '</option>'
                    body += '</select>'

                else:
                    length = len(the_value) + 5
                    if length < 45:
                        length = 45

                    size_to_use = str(length)
                    if 'restrict_fnc' in profile_item:
                        body += '<input list="timezones" ' + \
                            'onkeydown="return ' + profile_item['restrict_fnc'] + '(event)" ' + \
                            'id="' + the_id + \
                            '" class="input" type="text" size=' + size_to_use + \
                            ' name="single_edit_profile_' + profile_item['field'] + \
                            '" value="' + the_value + '">'
                    else:
                        body += '<input list="timezones" ' + \
                            'id="' + the_id + \
                            '" class="input" type="text" size=' + size_to_use + \
                            ' name="single_edit_profile_' + profile_item['field'] + \
                            '" value="' + the_value + '">'

                _ = """
                elif profile_item['field'] == 'timezone':
                    size_to_use = str(60)

                    body += '<datalist id="timezones">'
                    for timezone_name in sorted(s_timezone_override_list):
                        if timezone_name:
                            key_name = timezone_name.split()[0]
                            try:
                                show_time = str(current_time.astimezone(pytz.timezone(key_name)))
                            except:
                                show_time = '(error)'
                        else:
                            show_time = ''

                        if show_time:
                            body += '<option>' + timezone_name + ' ' + show_time + '</option>'
                        else:
                            body += '<option>' + timezone_name + '</option>'
                    body += '</datalist>'

                    if 'restrict_fnc' in profile_item:
                        body += '<input list="timezones" ' + \
                            'onkeydown="return ' + profile_item['restrict_fnc'] + '(event)" ' + \
                            'id="' + the_id + \
                            '" class="input" type="text" size=' + size_to_use + \
                            ' name="single_edit_profile_' + profile_item['field'] + \
                            '" value="' + the_value + '">'
                    else:
                        body += '<input list="timezones" ' + \
                            'id="' + the_id + \
                            '" class="input" type="text" size=' + size_to_use + \
                            ' name="single_edit_profile_' + profile_item['field'] + \
                            '" value="' + the_value + '">'

                """

                body += '</td>'

            if profile_item['type'] == 'bookmarks':
                body += '<td>'
                body += '<table border="1" cellpadding="5">'

                entry_count = 0
                bookmarks_list = the_profile.bookmark_list()
                bookmarks_list.append([])  # make a blank one at the end, that can be filled in and saved.
                last_bookmark = len(bookmarks_list)

                for bookmark_entry in bookmarks_list:
                    entry_count += 1
                    body += '<tr>'
                    body += '<td>'
                    body += '<B>' + str(entry_count) + '</B>'

                    name_to_show = "<text title='Click this word, to make a duplicate copy of the original bookmark (not any unsaved edits), and place it at the end of this bookmarks list.'>Duplicate</text>"
                    sourceLocation = str(entry_count)
                    destinationLocation = str(last_bookmark)
                    command_name = 'CopyBookmark' + '_' + sourceLocation + '_' + destinationLocation
                    onclick = """\"""" + command_name + """();return false\""""
                    body += '<br><br>'
                    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
                    copy_bookmark_commands[command_name] = []
                    copy_bookmark_commands[command_name].append("""console.log('""" + command_name + """');""")

                    name_to_show = "<text title='Click this word, to clear all fields of this bookmark.'>Clear</text>"
                    sourceLocation = str(entry_count)
                    clear_command_name = 'ClearBookmark' + '_' + sourceLocation
                    onclick = """\"""" + clear_command_name + """();return false\""""
                    body += '<br><br>'
                    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
                    clear_bookmark_commands[clear_command_name] = []
                    clear_bookmark_commands[clear_command_name].append(
                        """console.log('""" + clear_command_name + """');""")

                    name_to_show = "<text title='Click this word, to move this bookmark up one position.'>Move Up</text>"
                    sourceLocation = str(entry_count)
                    moveup_command_name = ''
                    if entry_count > 1:
                        destinationLocation = str(entry_count - 1)
                        moveup_command_name = 'MoveUpBookmark' + '_' + sourceLocation + '_' + destinationLocation
                        onclick = """\"""" + moveup_command_name + """();return false\""""
                        body += '<br><br>'
                        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""
                        moveup_bookmark_commands[moveup_command_name] = []
                        moveup_bookmark_commands[moveup_command_name].append(
                            """console.log('""" + moveup_command_name + """');""")

                    body += '</td>'

                    body += '<td>'
                    body += '<table border="1" cellpadding="5">'
                    for bookmark_item in s_bookmark_items:

                        the_value = ''
                        if bookmark_item['type'] == 'string':
                            if bookmark_item['field'] in bookmark_entry:
                                the_value = str(bookmark_entry[bookmark_item['field']])

                        if bookmark_item['type'] == 'radio_button':
                            if bookmark_item['field'] in bookmark_entry:
                                the_value = str(bookmark_entry[bookmark_item['field']])

                        if bookmark_item['type'] == 'list_of_string':
                            try:
                                if bookmark_item['field'] in bookmark_entry:
                                    if bookmark_entry[bookmark_item['field']]:
                                        the_list = bookmark_entry[bookmark_item['field']]
                                        the_value = str(', '.join(the_list))
                            except:
                                the_value = 'exception on: ' + str(bookmark_item['field'])
                        body += '<tr>'

                        the_help = ''
                        if 'help' in bookmark_item:
                            the_help = bookmark_item['help']

                        prompt_to_use = bookmark_item['field']
                        if 'prompt' in bookmark_item:
                            prompt_to_use = bookmark_item['prompt']

                        body += '<td title="' + the_help + '">'
                        body += '<B>' + prompt_to_use + '</B>'
                        body += '</td>'

                        length = len(the_value) + 5
                        if length < 25:
                            length = 25

                        size_to_use = str(length)

                        body += '<td>'

                        the_id = 'input_' + bookmark_item['field'] + '_' + str(entry_count)
                        the_last_id = 'input_' + bookmark_item['field'] + '_' + str(last_bookmark)
                        the_up_id = 'input_' + bookmark_item['field'] + '_' + str(entry_count - 1)
                        all_non_name_input_ids.append(the_id)

                        # https://stackoverflow.com/questions/6003819/what-is-the-difference-between-properties-and-attributes-in-html/6004028#6004028
                        #                        copy_bookmark_commands[command_name].append("""document.getElementById(\"""" + the_last_id + """\").setAttribute('value',document.getElementById(\"""" + the_id + """\").getAttribute('value'));""")
                        copy_bookmark_commands[command_name].append(
                            """document.getElementById(\"""" + the_last_id + """\").setAttribute('value',document.getElementById(\"""" + the_id + """\").value);""")
                        #                        clear_bookmark_commands[clear_command_name].append("""document.getElementById(\"""" + the_id + """\").setAttribute('value','');""")
                        clear_bookmark_commands[clear_command_name].append(
                            """document.getElementById(\"""" + the_id + """\").value = '';""")

                        if moveup_command_name:
                            #                            moveup_bookmark_commands[moveup_command_name].append("""var this_val = document.getElementById(\"""" + the_id + """\").getAttribute('value');""")
                            moveup_bookmark_commands[moveup_command_name].append(
                                """var this_val = document.getElementById(\"""" + the_id + """\").value;""")
                            #                            moveup_bookmark_commands[moveup_command_name].append("""var up_val = document.getElementById(\"""" + the_up_id + """\").getAttribute('value');""")
                            moveup_bookmark_commands[moveup_command_name].append(
                                """var up_val = document.getElementById(\"""" + the_up_id + """\").value;""")

                            #                            moveup_bookmark_commands[moveup_command_name].append("""document.getElementById(\"""" + the_up_id + """\").setAttribute('value',this_val);""")
                            #                            moveup_bookmark_commands[moveup_command_name].append("""document.getElementById(\"""" + the_id + """\").setAttribute('value',up_val);""")

                            moveup_bookmark_commands[moveup_command_name].append(
                                """document.getElementById(\"""" + the_up_id + """\").value = this_val;""")
                            moveup_bookmark_commands[moveup_command_name].append(
                                """document.getElementById(\"""" + the_id + """\").value = up_val;""")

                        if 'restrict_fnc' in bookmark_item:
                            body += '<input onkeydown="return ' + bookmark_item[
                                'restrict_fnc'] + '(event)" id="' + the_id + '" class="input" type="text" size=' + size_to_use + ' name="single_edit_profile_' + \
                                    bookmark_item['field'] + '_' + str(entry_count) + '" value="' + the_value + '">'
                        else:
                            body += '<input id="' + the_id + '" class="input" type="text" size=' + size_to_use + ' name="single_edit_profile_' + \
                                    bookmark_item['field'] + '_' + str(entry_count) + '" value="' + the_value + '">'

                        body += '</td>'

                        body += '</tr>'

                    body += '</table>'
                    body += '</td>'
                    body += '</tr>'

                body += '</td>'
                body += '</table>'

            body += '</tr>'

        body += '<tr>'
        body += '<td>'
        body += '</td>'

        body += '<td>'
        body += ' '
        body += '</td>'
        body += '</tr>'

        body += '</table>'

        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        color = color_yellow_caution
        body += '<input id="mySubmit" type="hidden" value="not ready" style="background-color:rgba' + color + '">'
        body += '</form>'
        body += '</td>'
        body += '<td>'

        the_count = 0
        ids = reports.get_all_reported_ids()
        data_store_content = datastore.all_datastore()
        counts = reports.get_profile_counts(ids, data_store_content)
        if 'profile_' + profile_name in counts:
            the_count = counts['profile_' + profile_name]

        body += 'number of devices with original profile name = ' + str(the_count)

        body += '</td>'
        body += '</tr>'
        body += '</table>'

        body += '</center>'

        # https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event
        # https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event
        # https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
        # https://www.w3schools.com/js/tryit.asp?filename=tryjs_validation_check

        script_content = ''
        script_content += """
<script >
//document.getElementById("mySubmit").setAttribute('type', 'hidden');
//document.getElementById("mySubmit").setAttribute('value', 'not ready');

let input_name = document.getElementById("input_name");

var name_hit_counts = 0;
var allowed_to_save_name = true;
var any_name_change = false;
var any_non_name_input = false;
var prompt_to_show = 'original name';

//input_name.addEventListener("change", stateHandle);

// initial check of name reuse
input_name.addEventListener("input", name_unique);


function non_name_input() {
    any_non_name_input = true;
    stateHandle();
}

function stateHandle() {
    if (allowed_to_save_name && (any_name_change || any_non_name_input)) {
        document.getElementById("mySubmit").setAttribute('type', 'submit');
        document.getElementById("mySubmit").setAttribute('value', 'SAVE to: ' + prompt_to_show);

    } else {
        document.getElementById("mySubmit").setAttribute('type', 'hidden');
    }
}
//document.getElementById("value")="test";

function name_unique() {
//    name_hit_counts += 1;
    var is_unique = true;

    var original_value = document.getElementById("input_name").getAttribute('value'); // this one returns the original value
    var current_value = document.getElementById("input_name").value; // this one gets the live data as it is typed

    document.getElementById("input_name_output").innerText = "testing...";
    document.getElementById("input_name_output").innerText = current_value;
//    document.getElementById("input_name_output").innerText = name_hit_counts;

    var profile_names = document.getElementById('profile_list'), profile_list, i;
    for (i = 0; i < profile_names.length; i++)
    {
        var profile_name = profile_names[i].text;

        if (profile_name == current_value) {
            is_unique = false;
            }
    }

    if (is_unique) {
        prompt_to_show = "new unique name";
        allowed_to_save_name = true;
        any_name_change = true;
    } else {
        any_name_change = false;
        if (original_value == current_value) {
            prompt_to_show = "original name";
            allowed_to_save_name = true;
        } else {
            prompt_to_show = "DUPLICATE NAME !!!";
            allowed_to_save_name = false;
        }
    }
    document.getElementById("input_name_output").innerText = prompt_to_show
    stateHandle();
}
        """

        for all_id in all_non_name_input_ids:
            script_content += 'document.getElementById("' + all_id + '").addEventListener("input", non_name_input);'

        for copy_bookmark_command in copy_bookmark_commands.keys():
            script_content += 'function ' + copy_bookmark_command + '(){'
            for command_line in copy_bookmark_commands[copy_bookmark_command]:
                script_content += command_line
            script_content += 'non_name_input();'  # do a validation cycle
            script_content += '}'

        for clear_bookmark_command in clear_bookmark_commands.keys():
            script_content += 'function ' + clear_bookmark_command + '(){'
            for command_line in clear_bookmark_commands[clear_bookmark_command]:
                script_content += command_line
            script_content += 'non_name_input();'  # do a validation cycle
            script_content += '}'

        for moveup_bookmark_command in moveup_bookmark_commands.keys():
            script_content += 'function ' + moveup_bookmark_command + '(){'
            for command_line in moveup_bookmark_commands[moveup_bookmark_command]:
                script_content += command_line
            script_content += 'non_name_input();'  # do a validation cycle
            script_content += '}'

        script_content += """
</script>
        """

        body += script_content

        body += '---------------------------' + '<br>'
        body += '---------------------------' + '<br>'
        body += 'sudo tail -f -n 100 /var/log/privoxy/logfile' + '<br>'
        body += 'OR' + '<br>'
        body += 'cat /cardinal/log/privoxy/privoxy_report.json' + '<br>'
        body += '---------------------------' + '<br>'





    #        content_dump = json.dumps(json.loads(json_string), indent=4, separators=(',',':'))
    #        rows = content_dump.split('\n')
    #        for row in rows:
    #            body += row + '<br>'

    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body_GET_edit(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    try:
        ids = reports.get_all_reported_ids()
        data_store_content = datastore.all_datastore()
        counts = reports.get_profile_counts(ids, data_store_content)

        body = ''

        body += """
    <script>

    function URLjump(jumpLocation) {
        location.href = jumpLocation;
    }

    </script>
        """

        name_to_show = "Home"
        url_to_use = make_home_url_from_environ(environ)
        onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
        body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

        body += '<center>'
        body += '<br><br>'
        body += 'Profiles (edit mode)'
        body += '<br><br>'

        body += '<table border="1" cellpadding="5">'

        body += '<tr>'
        body += '<th>'
        body += 'Name (click to edit)'
        body += '</th>'
        body += '<th>'
        body += 'in use by device(s)'
        body += '</th>'
        body += '<th>'
        body += 'Preliminary'
        body += '</th>'
        body += '<th>'
        body += '<text title="The preliminary mark of the profile must be the word delete, and there must be no devices configured to use the profile.">Delete</text>'
        body += '</th>'
        body += '<th>'
        body += 'Description'
        body += '</th>'
        body += '</tr>'

        all_profiles = datastore.get_keys_starting_with('profile_')

        # make one for creating a blank profile:
        all_profiles.append('profile_')

        for key_name in sorted(all_profiles):

            the_count = 0
            if key_name in counts:
                the_count = counts[key_name]

            data_store_content = datastore.get_value(key_name)

            if data_store_content:
                profile_content = json.loads(data_store_content)
            else:
                profile_content = {}

            desc = ''
            if 'description' in profile_content:
                desc = str(profile_content['description'])

            the_preliminary_mark = ''
            if 'preliminary' in profile_content:
                the_preliminary_mark = str(profile_content['preliminary'])

            the_delete_option = ''
            if the_preliminary_mark == 'delete':
                if the_count > 0:
                    the_delete_option = 'need to remove<br>profile from device(s)'
                else:
                    the_delete_option = '--- out of play ---'

            body += '<tr>'

            text_to_use = key_name[8:]
            if not text_to_use:
                text_to_use = '(new)'

            body += '<td>'
            url_to_use = make_home_url_from_environ(environ)
            onclick = """\"""" + 'URLjump' + """('""" + url_to_use + "/profiles?profile=" + key_name + """');return false\""""
            body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + text_to_use + """</a>"""
            body += '</td>'

            body += '<td>'
            body += str(the_count)
            body += '</td>'

            body += '<td>'
            body += the_preliminary_mark
            body += '</td>'

            body += '<td>'
            body += the_delete_option
            body += '</td>'

            body += '<td>'
            body += desc
            body += '</td>'

            body += '</tr>'

        body += '</table>'
        body += '</center>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body_GET_view(environ):
    # ====================================
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += '<center>'
        body += '<br><br>'

        body += '<table border="1" cellpadding="5">'

        body += '<tr>'
        body += '<td>'
        body += '<B>'
        body += 'Profile Name'
        body += '</B>'
        body += '</td>'
        body += '<td>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += '<B>'
        body += 'Index'
        body += '</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>'
        body += 'Title'
        body += '</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>'
        body += 'URL'
        body += '</B>'
        body += '</td>'
        body += '<td>'
        body += '<B>'
        body += 'Whitelist'
        body += '</B>'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</td>'
        body += '</tr>'

        for key_name in sorted(datastore.get_keys_starting_with('profile_')):

            entry_content = ""
            try:
                profile_content = json.loads(datastore.get_value(key_name))
                desc = ''
                if 'description' in profile_content:
                    desc = str(profile_content['description'])

                preliminary_content = ''
                if 'preliminary' in profile_content:
                    preliminary_content = str(profile_content['preliminary'])

                if not preliminary_content == 'delete':
                    body += '<tr>'
                    body += '<td>'
                    body += key_name[8:]
                    if desc:
                        body += '<br><br>'
                        body += "(" + desc + ")"
                    if preliminary_content:
                        body += '<br><br>'
                        body += "[" + preliminary_content + "]"
                    body += '</td>'
                    body += '<td>'

                    if 'bookmarks' in profile_content:
                        entry_content += '<table border="1" cellpadding="5">'
                        for bookmark_index in sorted(profile_content['bookmarks']['(id)']):

                            entry_content += '<tr>'
                            entry_content += '<td>'
                            entry_content += str(bookmark_index)
                            entry_content += '</td>'

                            entry_content += '<td>'
                            entry_content += str(profile_content['bookmarks']['(id)'][bookmark_index]['title'])
                            entry_content += '</td>'

                            entry_content += '<td>'
                            entry_content += str(profile_content['bookmarks']['(id)'][bookmark_index]['url'])
                            entry_content += '</td>'

                            entry_content += '<td>'
                            try:
                                whitelist = profile_content['bookmarks']['(id)'][bookmark_index]['whitelist']
                                for list_item in whitelist:
                                    entry_content += str(list_item) + "<br>"
                            except:
                                pass
                            entry_content += '</td>'

                            entry_content += '</tr>'

                        entry_content += '</table>'

                        body += entry_content

                        body += '</td>'
                    body += '</tr>'
            except:
                pass

        body += '</table>'
        body += '</center>'


    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')]}
    body = ''

    if permissions.permission_prefix_allowed(environ, 'profiles_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            pass
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    try:
        body, other = make_body(environ)
        status = other['status']
        response_header = other['response_header']
    except:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    html = '<html>\n<body>\n'
    html += body
    html += '</body>\n</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.maxDiff = None
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_blank_profile(self):
        """
        (fill in here)
        """

        # just existing
        #        a_profile = profile_class()

        # fill in from a json
        #        a_profile = profile_class(json.dumps({}))

        # get json back out
        expected = json.dumps({"bookmarks": {"(id)": {}}})
        actual = profile_class().as_json()
        self.assertEqual(expected, actual)

    def test_filled_in_profile(self):
        """
        (fill in here)
        """

        # fill in from a json

        json_string = json.dumps({"bookmarks": {"(id)": {"01": {"title": "test123"}}}})
        a_profile = profile_class(json_string)

        expected = json_string
        actual = a_profile.as_json()
        self.assertEqual(expected, actual)

    def test_single_bookmark(self):
        a_profile = profile_class()
        a_profile.add_bookmark(json.dumps({'title': 'test123'}))
        expected = json.dumps({"bookmarks": {"(id)": {"01": {"title": "test123"}}}})
        actual = a_profile.as_json()
        self.assertEqual(expected, actual)

    def test_two_bookmarks(self):
        a_profile = profile_class()
        a_profile.add_bookmark(json.dumps({'title': 'test123'}))
        a_profile.add_bookmark(json.dumps({'title': 'test456'}))
        expected = json.dumps({"bookmarks": {"(id)": {"01": {"title": "test123"}, "02": {"title": "test456"}}}})
        actual = a_profile.as_json()
        self.assertEqual(expected, actual)

    def test_two_bookmarks_with_auto_launch(self):
        a_profile = profile_class()
        a_profile.add_bookmark(json.dumps({'title': 'test123', "autolaunch": True}))
        a_profile.add_bookmark(json.dumps({'title': 'test456'}))
        expected = json.dumps(
            {"bookmarks": {"(id)": {"01": {"title": "test123", "autolaunch": True}, "02": {"title": "test456"}}}})
        actual = a_profile.as_json()
        self.assertEqual(expected, actual)

    def test_get_bookmarks(self):
        a_profile = profile_class()
        a_profile.add_bookmark(json.dumps({'title': 'test123', "autolaunch": True}))
        a_profile.add_bookmark(json.dumps({'title': 'test456'}))

        expected = [{"title": "test123", "autolaunch": True}, {"title": "test456"}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

    def test_bookmarks_from_content(self):
        content = """{
    "timezone":"America/Manaus",
    "bookmarks":{
        "(id)":{
            "01":{
                "url":"http://*************:9091/produccion.aspx?pantalla=10011",
                "whitelist":[
                    "*************",
                    "code.jquery.com",
                    "code.ionicframework.com",
                    "fonts.googleapis.com",
                    "ssl.gstatic.com",
                    "maxcdn.bootstrapcdn.com",
                    "fonts.gstatic.com",
                    "gateway.zscalertwo.net"
                ],
                "title":"Line 1"
            }
        }
    }
}"""
        a_profile = profile_class(content)

        expected = [{"title": "Line 1", "url": "http://*************:9091/produccion.aspx?pantalla=10011",
                     "whitelist": ["*************", "code.jquery.com", "code.ionicframework.com",
                                   "fonts.googleapis.com", "ssl.gstatic.com", "maxcdn.bootstrapcdn.com",
                                   "fonts.gstatic.com", "gateway.zscalertwo.net"]}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

        expected_whitelist = ["*************", "code.jquery.com", "code.ionicframework.com", "fonts.googleapis.com",
                              "ssl.gstatic.com", "maxcdn.bootstrapcdn.com", "fonts.gstatic.com",
                              "gateway.zscalertwo.net"]
        self.assertEqual(expected_whitelist, actual[0]["whitelist"])

    def test_profile_pieces(self):
        """
        (fill in here)
        """

        # just existing
        a_profile = profile_class()

        a_profile.set_bookmark_attribute(1, {'title': 'test12345'})
        expected = [{"title": "test12345"}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

        a_profile.set_bookmark_attribute(1, {'title': 'Test6789'})
        expected = [{"title": "Test6789"}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

        a_profile.set_bookmark_attribute(2, {'title': 'this is completely new'})
        expected = [{"title": "Test6789"}, {"title": "this is completely new"}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

        a_profile.set_bookmark_attribute(1, {'title': 'New Title for 1'})
        expected = [{"title": "New Title for 1"}, {"title": "this is completely new"}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

    #        expected = [{"title":"test12345", "autolaunch":True}, {"title":"test456"}]
    #        actual = a_profile.bookmark_list()
    #        self.assertEqual(expected, actual)

    def test_profile_boolean(self):
        """
        (fill in here)
        """

        # just existing
        a_profile = profile_class()

        a_profile.set_bookmark_attribute(1, {'autolaunch': 'True'})
        expected = [{'title': '(missing)', "autolaunch": True}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

    def test_profile_list_of_string(self):
        """
        (fill in here)
        """

        # just existing
        a_profile = profile_class()

        a_profile.set_bookmark_attribute(1, {'whitelist': '*************, code.jquery.com'})
        expected = [{'title': '(missing)', "whitelist": ['*************', 'code.jquery.com']}]
        actual = a_profile.bookmark_list()
        self.assertEqual(expected, actual)

    def test_build_single_content_from_submit(self):
        submit_content = {
            'single_edit_profile_name': 'profile_DOM02-Line1',
            'single_edit_profile_timezone': 'America/Manaus',
        }

        expected = {
            "timezone": "America/Manaus",
            "bookmarks": {
                "(id)": {
                }
            }
        }

        a_profile = profile_class(submit_content=submit_content)

        actual = json.loads(a_profile.as_json())
        self.assertEqual(expected, actual)

        expected = 'profile_DOM02-Line1'
        actual = a_profile.name()
        self.assertEqual(expected, actual)

    def test_build_single_content_from_submit_bookmark_missing_title(self):
        submit_content = {
            'single_edit_profile_name': 'profile_DOM02-Line1',
            'single_edit_profile_timezone': 'America/Manaus',
            'single_edit_profile_url_1': 'mdsystems.com',
        }

        expected = {
            "timezone": "America/Manaus",
            "bookmarks": {
                "(id)": {
                    "01": {
                        "url": "mdsystems.com",
                        "title": "(missing)"
                    }
                }
            }
        }

        a_profile = profile_class(submit_content=submit_content)

        actual = json.loads(a_profile.as_json())
        self.assertEqual(expected, actual)

        expected = 'profile_DOM02-Line1'
        actual = a_profile.name()
        self.assertEqual(expected, actual)

    def test_build_all_content_from_submit(self):
        submit_content = {
            'single_edit_profile_whitelist_1': '*************, code.jquery.com, code.ionicframework.com, fonts.googleapis.com, ssl.gstatic.com, maxcdn.bootstrapcdn.com, fonts.gstatic.com, gateway.zscalertwo.net',
            'single_edit_profile_hosts_2': '',
            'single_edit_profile_hosts_1': '',
            'single_edit_profile_whitelist_2': '',
            'single_edit_profile_bypass_1': '',
            'single_edit_profile_name': 'profile_DOM02-Line1',
            'single_edit_profile_url_1': 'http://*************:9091/produccion.aspx?pantalla=10041',
            'single_edit_profile_bypass_2': '',
            'single_edit_profile_timezone': 'America/Manaus 2022-06-21 16:09:48.428717+00:00',
            'single_edit_profile_description': '',
            'dummy_profile_list[]': '',
            'single_edit_profile_autolaunch_1': 'True',
            'single_edit_profile_autolaunch_2': '',
            'profile': 'profile_DOM02-Line1',
            'single_edit_profile_title_1': 'Line 1',
            'single_edit_profile_title_2': '',
            'single_edit_profile_preliminary': '',
            'single_edit_profile_fragile_1': '',
            'single_edit_profile_fragile_2': '',
            'single_edit_profile_url_2': ''
            }

        expected = {
            "timezone": "America/Manaus",
            "bookmarks": {
                "(id)": {
                    "01": {
                        "url": "http://*************:9091/produccion.aspx?pantalla=10041",
                        "autolaunch": True,
                        "whitelist": [
                            "*************",
                            "code.jquery.com",
                            "code.ionicframework.com",
                            "fonts.googleapis.com",
                            "ssl.gstatic.com",
                            "maxcdn.bootstrapcdn.com",
                            "fonts.gstatic.com",
                            "gateway.zscalertwo.net"
                        ],
                        "title": "Line 1"
                    }
                }
            }
        }

        a_profile = profile_class(submit_content=submit_content)

        actual = json.loads(a_profile.as_json())
        self.assertEqual(expected, actual)

        expected = 'profile_DOM02-Line1'
        actual = a_profile.name()
        self.assertEqual(expected, actual)

    def test_make_screen_threshold_name_from_profile_name(self):
        profile_name = 'profile_IL002_multimedia_DisplayBoard_Dates'
        expected = 'screen_threshold_IL002_multimedia_DisplayBoard_Dates'
        actual = make_screen_threshold_name_from_profile_name(profile_name)
        self.assertEqual(expected, actual)

        profile_name = 'IL002_multimedia_DisplayBoard_Dates'
        expected = 'screen_threshold_IL002_multimedia_DisplayBoard_Dates'
        actual = make_screen_threshold_name_from_profile_name(profile_name)
        self.assertEqual(expected, actual)






# End of source file