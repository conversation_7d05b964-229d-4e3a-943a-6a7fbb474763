# A monitor for slicer page services

service = "monitor"
version = service + '.0.3.a'

release_notes = """
2023.02.01
monitor.0.3

Initial monitoring

2023.02.01
monitor.0.2

add live data table

"""

_ = """
This file gets loaded to:
/var/www/html/monitor.py

using:
sudo vi /var/www/html/monitor.py

It also requires:

sudo vi /etc/httpd/conf.d/python-monitor.conf
----- start copy -----
WSGIScriptAlias /monitor /var/www/html/monitor.py
----- end copy -----

sudo chown apache:apache /var/www/html/monitor.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/monitor-runner
sudo chmod +x /var/www/html/monitor-runner

# ===== begin: start file
#!/usr/bin/env python
import monitor
monitor.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/monitor-runner.service
sudo systemctl daemon-reload
sudo systemctl stop monitor-runner.service
sudo systemctl start monitor-runner.service
sudo systemctl enable monitor-runner.service

systemctl status monitor-runner.service

sudo systemctl restart monitor-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep monitor-runner

OR

tail -f /var/log/syslog | fgrep monitor-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/monitor-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import monitor; print(monitor.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/monitor

https://slicer.cardinalhealth.net/monitor?siteid=PR005

https://slicer.cardinalhealth.net/monitor?serial=100000002a5da842

https://slicer.cardinalhealth.net/monitor?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_monitor


"""

import copy
import traceback
import json
import os
import shlex
import subprocess
import sys
import time
import unittest

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# globals
s_get_count = 0


# ====================================
def convert_seconds_to_human(time_in_seconds):
    # ====================================
    the_report = ''

    time_to_use = int(time_in_seconds)

    if time_to_use < 0:
        the_report += '-'
        time_to_use = abs(time_to_use)

    days = int(time_to_use / (3600 * 24))
    time_to_use -= days * 3600 * 24

    hours = int(time_to_use / 3600)
    time_to_use -= hours * 3600

    minutes = int(time_to_use / 60)
    time_to_use -= minutes * 60

    seconds = time_to_use % 60

    if days > 0:
        if the_report:
            the_report += ' '
        the_report += str(days) + 'd'

    if hours > 0:
        if the_report:
            the_report += ' '
        the_report += str(hours) + 'h'

    if minutes > 0:
        if the_report:
            the_report += ' '
        the_report += str(minutes) + 'm'

    if (not the_report) or (seconds > 0):
        if the_report:
            the_report += ' '
        the_report += str(seconds) + 's'

    return the_report


# ----------------------------
def get_live_data():
    # ----------------------------
    global s_get_count
    s_get_count += 1

    active_users = login.get_active_users()

    live_data = {}
    live_data['headers'] = ['param', 'value']
    live_data['data'] = []

    for user in sorted(active_users.keys()):
        times_to_show = ''
        for seconds_remaining in sorted(active_users[user]):
            if times_to_show:
                times_to_show += ', '
            times_to_show += '(' + convert_seconds_to_human(seconds_remaining) + ')'

        live_data['data'].append({'param': 'user logged in: ' + user, 'value': times_to_show})

    return live_data


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(monitor status)'

    status = os.system('systemctl is-active --quiet monitor-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "monitor-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    # do work on content
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except (ValueError):
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)
    d = parse_qs(request_body.decode('utf-8'))

    # then return what GET would have done
    body, other = make_body_GET(environ)
    return body, other


# ====================================
def make_live_table_content(load_url):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable("' + load_url + '", document.getElementById("live_data_table"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, 5000);
"""

    return_value['javascript'] = """
<script>

document.getElementById("display_live_data").innerText = "";

async function loadIntoTable(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById("display_live_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {

                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById("display_live_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="display_live_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="live_data_table">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    global s_get_count
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    get_content = ''
    if 'get' in query_items:
        get_content = query_items['get']

    try:
        if get_content == 'data':
            the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
            the_type = ''
            if 'type' in query_items:
                the_type = query_items['type']

            if the_type == 'issues':
                live_data = get_live_data()

                the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}

                the_data['headers'] = live_data['headers']

                for item in live_data['data']:
                    row_content = []
                    row_links = []
                    row_colors = []
                    for header_name in the_data['headers']:
                        try:
                            item_content = item[header_name]
                        except:
                            item_content = ''

                        try:
                            item_link = item[header_name + '_link']
                        except:
                            item_link = ''

                        try:
                            item_color = item[header_name + '_color']
                        except:
                            item_color = ''

                        row_content.append(item_content)
                        row_links.append(item_link)
                        row_colors.append(item_color)

                    the_data['rows'].append(row_content)
                    the_data['links'].append(row_links)
                    the_data['color'].append(row_colors)
            else:
                # echo it back out, so that we can see it
                for key in query_items.keys():
                    the_data['headers'].append(key)
                    the_data['rows'].append([query_items[key]])

            other['add_wrapper'] = False
            other['response_header'] = [('Content-type', 'application/json')]
            return json.dumps(the_data), other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

            load_url = url_to_use + '/monitor?get=data,type=issues'

            live_table_content_d = make_live_table_content(load_url)
            other['head'] = live_table_content_d['head']

            body = ''

            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            #    name_to_show = "Home"
            #    url_to_use = "https://slicer.cardinalhealth.net"
            #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '<center>'
            body += version
            body += '</center>'

            # ------------------------------------
            # Live dashboard view
            # ------------------------------------
            dashboard = ''
            dashboard += live_table_content_d['body']
            dashboard += live_table_content_d['javascript']

            body += dashboard

    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    #    if permissions.permission_prefix_allowed(environ, 'monitor_') or permissions.permission_prefix_allowed(environ, 'development_'):
    if True:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def get_cookie_contents_from_environ(environ):
    # ====================================
    return_value = {}

    if 'HTTP_COOKIE' in environ:
        for each_item in environ['HTTP_COOKIE'].split(';'):
            item = each_item.split('=')
            return_value[item[0].strip()] = item[1].strip()

    return return_value


# ====================================
def set_cookie_header(name, value, days=365):
    # ====================================
    # cookies: https://stackoverflow.com/questions/14107260/set-a-cookie-and-retrieve-it-with-python-and-wsgi

    import datetime
    dt = datetime.datetime.now() + datetime.timedelta(days=days)
    fdt = dt.strftime('%a, %d %b %Y %H:%M:%S GMT')
    secs = days * 86400
    return ('Set-Cookie', '{}={}; Expires={}; Max-Age={}; Path=/'.format(name, value, fdt, secs))


# ====================================
def application(environ, start_response):
    # ====================================

    value_test = 0
    if 'HTTP_COOKIE' in environ:
        value_test = 1

    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    #        response_header.append(set_cookie_header('name_test', str(value_test)))
    #        response_header.append(set_cookie_header('name_test2', str(10+value_test)))

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '</body>\n' \
                '</html>\n'

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_monitor(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_get_cookie_contents_from_environ(self):
        environ = {}
        expected = {}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value'}
        expected = {'test_name': 'test_value'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

        environ = {'HTTP_COOKIE': 'test_name=test_value; test_name2=test_value2'}
        expected = {'test_name': 'test_value', 'test_name2': 'test_value2'}
        actual = get_cookie_contents_from_environ(environ)
        self.assertEqual(expected, actual)

# End of source file
