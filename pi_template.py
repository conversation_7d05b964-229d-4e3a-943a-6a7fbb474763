_ = """
sudo vi /cardinal/pi_template.py

sudo systemctl restart pi-template.service

watch -n 1 sudo cat /dev/shm/pi_template_datadrop.txt

cat /cardinal/log/pi_template_lastupdate.txt

logs:
https://www.digitalocean.com/community/tutorials/how-to-use-journalctl-to-view-and-manipulate-systemd-logs

journalctl -f

journalctl -p 3 -xb

journalctl -u pi-template.service --no-pager

sudo tail -n 1000 -f /var/log/syslog | fgrep pi-template


"""

s_my_service_port_assignment = 6999 # look to AA-pi-applications.txt, to pick a new one
service = 'template'
version = 't.0.4'
description = """
This is a pi service template.

"""

release_notes = """
2024.12.06
t.0.4
Add local web service, for an example of how to set one up: "build_and_start_web_server"

2024.03.12
t.0.3
Support Raspberry Pi5

2022.01.31
t.0.2
Add support for looking up the call_home_locations

2021.10.06
t.0.1
First template

"""

other_content = """
sudo vi /cardinal/pi-template
sudo chmod +x /cardinal/pi-template

# ===== begin: start file
#!/usr/bin/env python3
import pi_template
pi_template.main()
# ===== end: start file


sudo vi /lib/systemd/system/pi-template.service
sudo systemctl daemon-reload
sudo systemctl stop pi-template.service
sudo systemctl start pi-template.service
sudo systemctl enable pi-template.service

systemctl status pi-template.service

sudo systemctl restart pi-template.service

# template of std out
cat /var/log/syslog | fgrep pi-template

OR

tail -f /var/log/syslog | fgrep pi-template

# ===== begin: service file
[Unit]
Description=pi maintenance daemon
After=network.target

[Service]
ExecStart=/cardinal/pi-template
WorkingDirectory=/cardinal
StandardOutput=inherit
StandardError=inherit
Restart=always
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest pi_template


"""

import copy
import json
import math
import os

try:
    import requests
except:
    pass  # for unittest
import shutil
import socket
import subprocess
import sys
import threading
import time
import traceback
import unittest

from sys import version as python_version
from cgi import parse_header, parse_multipart

if python_version.startswith('3'):
    from urllib.parse import parse_qs
    from http.server import BaseHTTPRequestHandler, HTTPServer
else:
    from urlparse import parse_qs
    from BaseHTTPServer import BaseHTTPRequestHandler, HTTPServer


s_minutes_between_datadrops = 30

# ----------------------------
def build_and_start_web_server(port_number):
    # ----------------------------
    # be a web server
    # https://stackoverflow.com/questions/23264569/python-3-x-basehttpserver-or-http-server

    class MyServer(BaseHTTPRequestHandler):
        def do_GET(self):
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            response = "<html><head><title>" + service + " " + version + "</title></head><body>"

            if self.path == '/favicon.ico':
                pass
            else:
                path_splits = self.path.replace('/', '').replace('?', '').split(',')
                for item in path_splits:
                    try:
                        if 'test_parameter=' in item:
                            new_value = item.split('=')[1]
                            # do something with the new_value here
                            pass
                    except:
                        response += traceback.format_exc()

                response += build_get_page_body(self.path)
                self.wfile.write(bytes(response, "utf-8"))
                response = ''

            response += "</body></html>"

            self.wfile.write(bytes(response, "utf-8"))

    myServer = HTTPServer(("localhost", port_number), MyServer)

    try:
        myServer.serve_forever()
    except KeyboardInterrupt:
        pass

    myServer.server_close()


# ----------------------------
def build_get_page_body(the_path='/'):
    # ----------------------------
    time_now = time.time()

    split_path = {}
    for item in the_path.replace('/', '').replace('?', '').split(','):
        try:
            split_path[item.split('=')[0]] = item.split('=')[1]
        except:
            pass

    body = ''

    refresh_time = 1

    body += '<meta http-equiv="refresh" content="' + str(refresh_time) + '" >'

    body += '<br>'

    body += '<center>'

    body += service + ' ' + version

    body += '<center>'

    # add body content here as needed

    body += '</center>'

    return body


# ----------------------------
def example_code(content):
    # ----------------------------
    if content:
        return False
    else:
        return True


# ----------------------------
def save_shared_counts(the_counts):
    # ----------------------------
    # do reporting
    for the_count in the_counts:
        try:
            file_name = '/dev/shm/shared_' + service + '_' + the_count
            do_atomic_write_if_different(file_name, str(the_counts[the_count]))
        except:
            pass


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    did_write = False

    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        # flush all to disk
        #        os.sync()

        shutil.move(temp_name, output_file)

        did_write = True

    return did_write


# ----------------------------
def do_one_command(command):
    # ----------------------------
    import shlex
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ----------------------------
def do_one_time():
    # ----------------------------
    list_of_cmds = []
    #    list_of_cmds.append('sudo systemctl disable bluetooth.service')

    for cmd in list_of_cmds:
        try:
            do_one_command(cmd)
        except:
            pass


# ----------------------------
def get_serial():
    # ----------------------------
    serial = ''
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f.readlines():
                if 'Serial' in line:
                    if ':' in line:
                        serial = line.split(':')[1].strip()
    except:
        serial = 'noSerial'

    return serial


# ----------------------------
def do_datadrop_debug(the_string, do_reset=False):
    # ----------------------------
    the_file = '/dev/shm/pi_' + service + '_datadrop.txt'

    if do_reset:
        with open(the_file, 'w') as f:
            f.write(str(time.time()) + '\n')
    with open(the_file, 'a') as f:
        f.write(the_string + '\n')


# ----------------------------
def call_home_locations():
    # ----------------------------
    response = ['https://slicer.cardinalhealth.net']

    try:
        # like ["https://slicer.systems"]
        response = json.loads(open('call_home_locations.txt', 'r').read().replace("'", '"'))
    except:
        pass

    return response


# ----------------------------
def do_datadrop():
    # ----------------------------
    # do the datadrop of what we have currently, and get any new state information
    do_datadrop_debug(service + ' data drop: Start', True)

    do_datadrop_debug('get serial')
    serial = get_serial()
    do_datadrop_debug('found serial: ' + serial)

    try:
        the_data = []
        the_data.append('source=' + service)
        the_data.append('serial=' + serial)
        the_data.append('version=' + version)

        for call_home_location in call_home_locations():
            the_report_url = call_home_location + '/datadrop?' + ','.join(the_data)

            # check in with slicer
            try:
                do_datadrop_debug('Start ' + service + '...:' + the_report_url)
                r = requests.get(the_report_url, verify=False, timeout=15.0)
                url_result = r.text
                do_datadrop_debug(service + ' result: ' + url_result)

                try:
                    result_json = json.loads(
                        url_result)  # This will throw exception if the previous block passed 'exception'

                    if 'action_request' in result_json:
                        pass
                except:
                    do_datadrop_debug(traceback.format_exc())

            except:
                do_datadrop_debug(traceback.format_exc())
                url_result = 'exception'

    except:
        do_datadrop_debug(traceback.format_exc())

    do_datadrop_debug(service + ' data drop: End')


# ----------------------------
def do_maintenance():
    # ----------------------------
    # have each functional item run in its own try block, and report results as section in dictionary

    do_datadrop()


# ----------------------------
def main():
    # ----------------------------
    # by now, we are loaded, compiled, and in the cache
    try:
        from sys import version as python_version
        # Handle python3.x(x) environment.
        version_splits = python_version.split('.')
        binary_post_fix = version_splits[0] + version_splits[1]
        to_file_find = 'pi_' + service + '.cpython-' + binary_post_fix + '.pyc'
        shutil.copy2("/cardinal/__pycache__/" + to_file_find, "/cardinal/pi_" + service + ".pyc")
    except:
        pass

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string for " + service + ": " + version)

    # line up all of the worker threads
    daemon = threading.Thread(name='daemon_server',
                              target=build_and_start_web_server,
                              args=[s_my_service_port_assignment])
    daemon.setDaemon(True)  # Set as a daemon so it will be killed once the main thread is dead.
    daemon.start()

    if False:
        run_original_main_thread()
    else:
        from apscheduler.schedulers.background import BackgroundScheduler
        sched = BackgroundScheduler(daemon=True)  # Set as a daemon so it will be killed once the main thread is dead.
        job_id_thread1 = sched.add_job(run_original_main_thread, 'interval', seconds=1, coalesce=True)
        job_id_thread2 = sched.add_job(run_extra_main_thread, 'interval', seconds=1, coalesce=True)
        sched.start()
        while True:
            time.sleep(1)
        sched.shutdown()


# ----------------------------
def run_extra_main_thread():
    # ----------------------------
    try:

        # Do one cycle worth, and return; we will get called again
        pass

    except:
        # don't die on something silly
        pass


# ----------------------------
def run_original_main_thread():
    # ----------------------------
    """
    Get-er done
    """

    # by now, we are loaded, compiled, and in the cache
    shutil.copy2("/cardinal/__pycache__/pi_" + service + ".cpython-37.pyc", "/cardinal/pi_" + service + ".pyc")

    if os.path.isfile("/cardinal/pi_" + service + ".py"):
        os.remove("/cardinal/pi_" + service + ".py")

    try:
        with open('/dev/shm/pi_' + service + '_version.txt', 'w') as f:
            f.write(version)
    except:
        print("!!! failed to write version string")

    do_one_time()

    # For two different working examples of having many worker threads:
    #    look to pi_bluetooth, in this same spot (apscheduler)
    #    look to pi_network, in this same spot (built in threading)

    #    save_shared_counts({'name_of_value':0})

    wake_count = 0
    while True:
        # do system maintenance
        do_maintenance()

        time_now = time.time()
        while (abs(time_now - time.time()) < 60 * s_minutes_between_datadrops):
            # wake up each second, to allow for faster restarts and shutdowns. (Don't block for a long time)
            time.sleep(1)
            wake_count += 1
            try:
                with open('/dev/shm/pi_' + service + '_wake.txt', 'w') as f:
                    f.write(str(wake_count))
            except:
                print("!!! failed to write wake_count")


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_get_serial_exists(self):
        """
        (fill in here)
        """
        self.assertEqual(get_serial(), 'noSerial')

    def test_myself(self):
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_example_code(self):
        expected = True
        actual = example_code('')
        self.assertEqual(expected, actual)

        content = 'dave was here'
        expected = False
        actual = example_code(content)
        self.assertEqual(expected, actual)
