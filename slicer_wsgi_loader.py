# A loader for slicer page services

service = "loader"
version = service + '.1.3'

release_notes = """
1.3
2025.04.07

---- This helped, but did not fix creation in the /mnt/disks/SSD/var/log/slicer/ ----
https://docs.redhat.com/en/documentation/red_hat_enterprise_linux/7/html/selinux_users_and_administrators_guide/sect-security-enhanced_linux-working_with_selinux-selinux_contexts_labeling_files#sect-Security-Enhanced_Linux-SELinux_Contexts_Labeling_Files-Temporary_Changes_chcon

on slicer server, manually do:
sudo su
chcon -R -t httpd_sys_rw_content_t /mnt/disks/SSD/var/log/slicer/

then:

logrotate -f /etc/logrotate.d/rsyslog

to force a log rotation

ls -l /mnt/disks/SSD/var/log/slicer/tasks/
ls -Z -l /mnt/disks/SSD/var/log/slicer/tasks/
ls -Z -l /mnt/disks/SSD/var/log/slicer/
ls -Z -l /mnt/disks/SSD/var/log/

---- This helped a bunch ?, not really so much, when allowed to run a while ----
https://unix.stackexchange.com/questions/50639/httpd-cant-write-to-folder-file-because-of-selinux

sudo su
getsebool httpd_unified
setsebool -P httpd_unified 1

sudo systemctl restart loader-runner

getsebool httpd_unified

logrotate -f /etc/logrotate.d/rsyslog




---- did not seem to help ??? ----
https://stackoverflow.com/questions/22597482/configure-selinux-access-so-that-apache-can-access-mounted-directories

ausearch -c 'httpd' --raw | audit2allow -M my-httpd
semodule -X 300 -i my-httpd.pp


---- install a tool ----
https://unix.stackexchange.com/questions/116575/selinux-httpd-write-access-to-a-directory

dnf install policycoreutils-python-utils
(was already there)

semanage fcontext -a -t httpd_sys_rw_content_t "/mnt/disks/SSD/var/log/slicer(/.*)?"

restorecon -R /mnt/disks/SSD/var/log/slicer

logrotate -f /etc/logrotate.d/rsyslog


----
1.2
2025.02.05
Working to review the log files, looking for disk usage hogs.

1.1
2024.09.10

New setup, wants to already be a linked /var/www/html path, so that we never work
in a fixed (non linked) path ever again. Convert any current instances by hand to be a linked setup.
    Put any old style into a "0.0.0" folder name, then link to that.


Need loader install to setup the first linked folder "0.0.0", maybe with just:
    loader, settings, organization, index
    Then do a full load (from direct files (into 0.0.0), or from zip into an x.y.z).

Example full source zip file:
cs_sp_pi_slicer-2.1.0.zip


Done:

In progress:

To Do:

On loader start:
    Mark all slicer runners as 'not enabled' for automatic start, except loader runner.
    Stop any that are already running (only will happen on upgrade from old scheme)


For each unzipped (version) folder, show a drop down list selector;
    Have a mark thatshows which one is the currently active one.
    On selecting one, show the analysis of all the content (versions, runners, active/inactive marks...)
    Have a two step set of clicks (permission controlled), that initiates the conversion to this
        shown version to become the active version.

Accept a zip file, version string in the name:
    + save it
    + unzip it to the /var/www path
    + set apache user as the owner of the directory

Point to a new version to be the active version:
    - identify all wsgi runners currently running (ps ax)
    - stop all service runners (except loader, because that would stop us here)
    - stop httpd
    - set symbolic link to new directory
    - Need to configure/remove runners to match the current content (remove/add runners to match content)
    - start httpd
    - start all service runners
    - restart loader-runner
        - Somehow: (get from datastore or save to /var/www/???/(service)-runner.donotrun.txt)
            - which runners to not run
        - check all needed runners get started

    Like:
        cd /var/www
        sudo cp -r html 0.0.0
        sudo chown -R apache:apache 0.0.0
        sudo chmod 755 0.0.0

        (need to stop all services that link to /var/www/html)
        sudo systemctl stop watchdog-runner

        sudo systemctl stop httpd
        sudo mv html html_save


        sudo ln -sfn /var/www/0.0.0 /var/www/html
        sudo reboot

        sudo ln -sfn html_test html

Make a set of POST options, that act on a single service:
    (data persists in the datastore)
    Page load
        yes/no
    runner
        yes/no

    notes
        Default new services to "page yes, runner yes" ?
        Show as a check box on the interface?
        on page load no:
            do not have a config file for it
            remove it from the permissions search results
                so that it is removed it from the users permissions page
                so that it does not show up in the index

    Make the template main runner have a long sleep, so that we can watch it be in the
        stopping state for a long time, then transition to stopped.
    On changing page load, might require httpd restart, so mark httpd restart as 'dirty'
        and show the need for httpd restart near the top.
        Maybe make a simpler httpd restart interface too.

0.9
2024.05.07
Take out the reload httpd on Rocky, because it just leaves threads hanging... instead, have the user do a restart after loading all
0.8
2024.05.06
Add to httpd.conf, to allow /server-status to work. (Happens when index.py is loaded by this interface)

0.7
2023.01.25
Make the unlock get cleaned up after 3 minutes

0.5
2022.11.29
Add running_exception


"""

_ = """
This file gets loaded to:
/var/www/html/loader.py

using:

####################
For Rocky Linux:
####################
Test if python3 already there:
python3 --version

#################################
### if python3 missing: start ###
sudo dnf update -y
sudo dnf install python3 -y

### if python3 missing: end ###
#################################

Test if SELINUX=permissive set:
cat /etc/selinux/config

(look for SELINUX=permissive)

############################################
### if SELINUX=permissive not set: start ###
sudo vi /etc/selinux/config
--- start copy ---
# This file controls the state of SELinux on the system.
# SELINUX= can take one of these three values:
#     enforcing - SELinux security policy is enforced.
#     permissive - SELinux prints warnings instead of enforcing.
#     disabled - No SELinux policy is loaded.
SELINUX=permissive
# SELINUXTYPE= can take one of three values:
#     targeted - Targeted processes are protected,
#     minimum - Modification of targeted policy. Only selected processes are protected.
#     mls - Multi Level Security protection.
SELINUXTYPE=targeted
--- end copy ---

sudo reboot

# WHY permissive:
    # Fails to write files on Rocky Linux bare install
    # https://unix.stackexchange.com/questions/511929/website-cannot-write-to-files-or-create-directories
    # https://serverfault.com/questions/858675/centos7-apache-cannot-write-to-a-file-it-owns
    # sudo tail -n 100 -f /var/log/audit/audit.log
    # sudo cat /var/log/audit/audit.log | fgrep denied
    # getenforce
    # sestatus
    #    (orig slicer CentOS is permissive)

### if SELINUX=permissive not set: end ###
############################################

### Proceed with loader installation and setup ###

test if pip is there:
pip --version

If no pip:
sudo dnf install python3-pip -y

sudo python3 -m pip install pexpect

sudo mkdir -p /var/www/html
sudo vi /var/www/html/loader.py
::%d (to delete all existing content)
i (to get to insert mode)
(paste contents of this entire file)
<esc> :wq (to exit insert mode, write file, and quit vi)

sudo python3 /var/www/html/loader.py install

Then browse to:
http://(host)/loader

like:
http://lpec5009slicr05.cardinalhealth.net/loader

(unlock)

(2024.03.21 Stop here to test slicer05 over a weekend, to see if Saturday to Sunday leads to unresponsive http on Sunday)

Load modules in this order:

settings_XYZ

organization
(This defines all of the storage locations, and login methods)

index
(Wait for apache restart, about 10 to 15 seconds)
(How to confirm that the index actually loaded?)

(all)
(select all "slicer_wsgi_", then submit all at once)

(watch browser until installing list is gone, and no red flags, if it stalls, then do a "sudo reboot" at the command line)
(stalled, and /var/log/httpd/error_log showed "[Thu Jan 25 19:24:45.***********] [mpm_event:error] [pid 3569722:tid 3569722] AH03490: scoreboard is full, not at MaxRequestWorkers.Increase ServerLimit.")

login

users
    4-Setup
        dataport create

http://(host)/dataport

(upload the datastore snapshot content)

http://(host)
begin trust

users
    permissions
    sites


"""

_permissions = """
start_permissions
create:
read:
update:
delete:
end_permissions
"""

documentation = """
Public Calls:
    (none)

"""

release_notes = """
2022.11.18
L.0.2
Now able to load other services, and their runners

"""

s_location_root = "/var/www/"
s_location_base = s_location_root + "html/"
s_location_version_zips = '/var/www/versions/'
s_new_link_file_name = '/dev/shm/loader_new_link.txt'
s_delete_link_file_name = '/dev/shm/loader_delete_link.txt'
s_work_report_file = '/dev/shm/loader_server_config_report.txt'
s_loader_module_save_path = '/dev/shm/loader_modules.txt'
s_loader_change_log_path = '/var/www/loader-log/' # 20241101_102245.txt, with content of what happened at that second
s_loader_comm_queue = '/dev/shm/loader_comm/'

s_rgba_yellow_light = '(255, 255, 100, 0.3)'
s_rgba_red_light = '(255, 0, 0, 0.3)' # red
s_rgba_purple_light = '(255, 0, 255, 0.3)'
s_rgba_purple_bright = '(255,100,255,0.3)' # bright purple
s_rgba_green_light = '(0, 255, 0, 0.3)'

_cleanup_logs = """
  994  cd /var/log/httpd
  997  sudo rm *.gz
 1001  cd /var/log
 1003  sudo rm *.gz
 1006  sudo rm *.1
 1007  sudo rm *.2
 1008  sudo rm *.3
 1009  sudo rm *.4
 1015  sudo rm *.log-*
 1017  sudo rm *-*
"""

_old = """

sudo systemctl restart loader-runner.service
sudo systemctl status loader-runner.service

sudo systemctl status apache2


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/loader

https://slicer.cardinalhealth.net/loader?siteid=PR005

https://slicer.cardinalhealth.net/loader?serial=100000002a5da842

https://slicer.cardinalhealth.net/loader?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_loader


"""

_notes = """
This needs "post" submits for:

+ unlock

/ select file to upload

- Reboot
"""

_structure_change = """
- make a single runner in the loader install, that manages making all the runners,
    and starting them one by one, each boot.

    - also have this manage the apache service start, so that it is all coordinated.
    - Might want to have a check that the httpd "fails" to run for 5 minutes, and then
        do a revert to the previous known-good-configuration.
            - keep the last known-good as a list on the file system, so that we
                can stop the coordinating runner (which wants to stop all the services, and
                apache, and then we can edit that list, and restart.)


- Be able to make a zip of all project files (like a git release), upload that.
    - pick through the file to pull the ones we want
    - If there are multiple organization files, then prompt to
        pick which org to use. Same for any other double named file.
    - then, after all setups, do an apache restart

"""

_manage_version_as_link = """
cd /var/www
sudo cp -r html html_test
sudo chown -R apache:apache html_test

(need to stop all services that link to /var/www/html)
sudo systemctl stop watchdog-runner

sudo systemctl stop httpd
sudo mv html html_save

sudo ln -s html_test html

Need to configure/remove runners to match the current content

sudo reboot



"""

import cgi
import copy
import datetime
import getpass
import hashlib
import json
import os
import pickle
import platform
import shlex
import shutil
import subprocess
import sys
from tempfile import TemporaryFile
import time
import traceback
import unittest
import zipfile

s_host_name = platform.node() # 'lpec5009slicr05'
s_this_user = getpass.getuser()
s_human_lookup = {'K':1024.0,
                  'M':1024.0 * 1024.0,
                  'G':1024.0 * 1024.0 * 1024.0,
                  'T':1024.0 * 1024.0 * 1024.0 * 1024.0,
                  'P':1024.0 * 1024.0 * 1024.0 * 1024.0 * 1024.0,
                }

# https://httpd.apache.org/docs/2.4/logs.html

s_default_analyze_httpd_log_rotate = '\n'.join("""
# Note that logs are not compressed unless "compress" is configured,
# which can be done either here or globally in /etc/logrotate.conf.
/var/log/httpd/*log {
    missingok
    notifempty
    sharedscripts
    delaycompress
    postrotate
        /bin/systemctl reload httpd.service > /dev/null 2>/dev/null || true
    endscript
}
""".split('\n')[1:-1]) + '\n'

# https://docs.rackspace.com/docs/understanding-logrotate-utility
s_default_analyze_etc_logrotate_conf = '\n'.join("""
# see "man logrotate" for details

# global options do not affect preceding include directives

# rotate log files weekly
weekly

# keep 4 weeks worth of backlogs
rotate 4

# create new (empty) log files after rotating old ones
create

# use date as a suffix of the rotated file
dateext

# uncomment this if you want your log files compressed
#compress

# packages drop log rotation information into this directory
include /etc/logrotate.d

# system-specific logs may be also be configured here.
""".split('\n')[1:-1]) + '\n'


s_default_analyze_etc_anacrontab = '\n'.join("""
# /etc/anacrontab: configuration file for anacron

# See anacron(8) and anacrontab(5) for details.

SHELL=/bin/sh
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
# the maximal random delay added to the base delay of the jobs
RANDOM_DELAY=45
# the jobs will be started during the following hours only
START_HOURS_RANGE=3-22

#period in days   delay in minutes   job-identifier   command
1	5	cron.daily		nice run-parts /etc/cron.daily
7	25	cron.weekly		nice run-parts /etc/cron.weekly
@monthly 45	cron.monthly		nice run-parts /etc/cron.monthly
""".split('\n')[1:-1]) + '\n'



startup_exceptions = ''

try:
    # python 2 and some early python 3
    from cgi import parse_qs
except:
    pass

try:
    # later python 3
    from urllib.parse import parse_qs
except:
    pass

# startup_exceptions += 'test start exception'

try:  # for unittest to work
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")


try:
    if startup_exceptions:
        startup_exceptions = s_this_user + ': ' + startup_exceptions
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

if not s_location_base in str(sys.path):
    sys.path.append(s_location_base)

# ====================================
class class_log_monitor():
    # ====================================
    def __init__(self):
        self.__report = ''

    def report(self):
        return self.__report

    def process_log_file_content(self, the_content):
        self.__report = ''
        self.parsed = {}

        for line in the_content.split('\n'):
            if 'SELinux is preventing ' in line:
                data_on_line = line.split('SELinux is preventing ')[1]
                if 'on the file ' in data_on_line:
                    the_process = data_on_line.split(' ')[0]
                    the_file = data_on_line.split('on the file ')[1].split(' ')[0]
                    the_folder = '/'.join(the_file.split('/')[:-1]) + '/'
                    if not the_folder in self.parsed:
                        self.parsed[the_folder] = {}
                    if not the_process in self.parsed[the_folder]:
                        self.parsed[the_folder][the_process] = {'count':0, 'last_file':''}
                    self.parsed[the_folder][the_process]['count'] += 1
                    self.parsed[the_folder][the_process]['last_file'] = the_file.split('/')[-1]

                elif 'on the directory ' in data_on_line:
                    the_process = data_on_line.split(' ')[0]
                    the_file = data_on_line.split('on the directory ')[1].split(' ')[0]
                    the_folder = '/'.join(the_file.split('/')[:-1]) + '/'
                    if not the_folder in self.parsed:
                        self.parsed[the_folder] = {}
                    if not the_process in self.parsed[the_folder]:
                        self.parsed[the_folder][the_process] = {'count':0, 'last_file':''}
                    self.parsed[the_folder][the_process]['count'] += 1

        for the_folder in sorted(self.parsed.keys()):
            for the_process in sorted(self.parsed[the_folder]):
                self.__report += str(self.parsed[the_folder][the_process]['count']) + ', ' + \
                                    'SELinux is preventing, ' + the_process + ', ' + \
                                    the_folder + ', ' + \
                                    self.parsed[the_folder][the_process]['last_file'] + \
                                    '\n'

# ----------------------------
def do_unzip(item, the_who):
    # ----------------------------
    try:
        log_content = ''
        basefile = item.replace('.needunzip', '')
        input_zip_filename = s_location_version_zips + basefile
        destination_folder = s_location_root + basefile.replace('.zip','') + '/'

        save_to_loader_change_log('zip_unzip', 'starting unzip file: ' + basefile + ', save as: ' + destination_folder, the_who)


        if os.path.exists(os.path.dirname(destination_folder)):
            do_one_command('rm -rf ' + destination_folder)

        os.makedirs(os.path.dirname(destination_folder))

        # do the unzip
        with zipfile.ZipFile(input_zip_filename, 'r') as zip_ref:
            filenames_in_the_zip = zip_ref.namelist()

            replace_dict = {}
            for zipped_name in filenames_in_the_zip:
                #
                splits = zipped_name.split('/')
                if len(splits) > 3:
                    if splits[1] == 'site':
                        replace_dict[splits[2]] = True

            replace_list = sorted(list(replace_dict.keys()))
            log_content += 'replace_list: ' + str(replace_list) + '\n'

            for zipped_name in sorted(filenames_in_the_zip):
                splits = zipped_name.split('/')

                base_filename = splits[1]
                if 'slicer_wsgi_' in base_filename:
                    # zipped_name = 'cs_sp_pi_slicer-2.1.0/slicer_wsgi_deviceupload.py'
                    content = zip_ref.read(zipped_name).decode('utf-8')
                    output_name = base_filename.replace('slicer_wsgi_','')
                    output_file = destination_folder + output_name
                    open(output_file, 'w').write(content)
                    log_content += 'made WSGI content: ' + base_filename + '\n'
                elif '/site/' in zipped_name:
                    # documentation site content
                    # cs_sp_pi_slicer-2.2.13/site/releases/2023-10-25_SP.46/index.html
                    # cs_sp_pi_slicer-2.2.13/site/releases/2023-10-25_SP.46/
                    #   ? just make sure the folder exists?
                    if splits[-1] == '':
                        # just a folder
                        pass
                    else:
                        # content = zip_ref.read(zipped_name).decode('utf-8')
                        content = None
                        binary_content = False
                        try:
                            file_extension = ''
                            if '.' in zipped_name:
                                file_extension  = zipped_name.split('.')[-1]

                            if file_extension in ['png', 'jpg', 'pdf', 'gz']:
                                content = zip_ref.read(zipped_name)
                                binary_content = True
                            else:
                                content = zip_ref.read(zipped_name).decode('utf-8')
                        #content = '(debug empty content)'
                        except:
                            log_content += 'content read issue: ' + zipped_name + '\n'

                        if not content == None:
                            # be sure the folder exists, and make the file
                            output_name = '/'.join(splits[1:])

                            if not binary_content:
                                pass
                                # content = update_content_for_site_links(output_name, content, replace_list)

                            output_file = destination_folder + output_name
                            if not os.path.exists(os.path.dirname(output_file)):
                                os.makedirs(os.path.dirname(output_file))
                            if binary_content:
                                open(output_file, 'wb').write(content)
                            else:
                                open(output_file, 'w').write(content)
                            log_content += 'made site content: ' + output_file + '\n'

        # set apache user as the owner
        do_cleanup()

        # put this action into the loader log
        the_who = 'loader-runner'
        save_to_loader_change_log('zip_unzip', 'unzipped file: ' + basefile + ', saved as: ' + destination_folder + '\n' + log_content, the_who)

    except:
        last_exception = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        save_to_loader_change_log('zip_unzip', 'Exception: ' + last_exception, the_who)

    # mark that we did it, even if it failed
    os.remove(s_location_version_zips + item)
    os.remove(s_location_version_zips + basefile)

# ----------------------------
def update_content_for_site_links(output_name, content, replace_list):
    # ----------------------------
    return_value = content

    if False:
        splits = ('documentation/' + output_name).split('/')        # 'documentation/site/releases/2025-03-12_SP.56/index.html'
        path_of_output = '/'.join(splits[:-1]) # 'documentation/site/releases/2025-03-12_SP.56'
        split_path = path_of_output.split('/') # ['documentation', 'site', 'releases', '2025-03-12_SP.56']
        full_depth = len(split_path)           # 4
        if full_depth > 2:
            for depth in range(full_depth, 2, -1):  # do the '../../' before the '../' ones
                path_to_keep = '/'.join(split_path[:-(depth-2)]) + '/'
                return_value = return_value.replace('../' * (depth-2), path_to_keep)
    else:
        splits = (output_name).split('/')        # 'site/releases/2025-03-12_SP.56/index.html'
        path_of_output = '/'.join(splits[1:-1])   # 'releases/2025-03-12_SP.56'
        split_path = path_of_output.split('/') # ['releases', '2025-03-12_SP.56']
        full_depth = len(split_path)           # 2
        if full_depth > 0:
            for depth in range(full_depth, 0, -1):  # do the '../../' before the '../' ones
                path_remaining = '/'.join(split_path[:-(depth)])
                if path_remaining:
                    path_to_keep = path_remaining + '/'
                else:
                    path_to_keep = ''
                return_value = return_value.replace('../' * (depth), path_to_keep)

    # get them under documentation/site/
    for replacer in replace_list:
        return_value = return_value.replace(replacer + '/', 'documentation/site/' + replacer + '/')

    return return_value

# ----------------------------
def strip_comments(content):
    # ----------------------------
    return_value = []

    for line in content.split('\n'):
        left_portion = line.split('#')[0].strip()
        if left_portion:
            return_value.append(left_portion)

    return '\n'.join(return_value)

# ----------------------------
def locate_first_difference(content1, content2):
    # ----------------------------
    return_value = 0

    len1 = len(content1)
    len2 = len(content2)

    min_length = len1
    if len2 < len1:
        min_length = len2

    return_value = min_length

    for index in range(0,min_length):
        if content1[index] != content2[index]:
            return_value = index
            break

    return return_value

# ----------------------------
def analyze_log_rotate_conf(content):
    # ----------------------------
    default = s_default_analyze_etc_logrotate_conf

    return_value = '(matches the default)'
    if not content == default:
        return_value = '!!! Changed from the default of:' + '\n' + default + '\n'
    return return_value

# ----------------------------
def analyze_httpd_log_rotate(content):
    # ----------------------------
    default = s_default_analyze_httpd_log_rotate

    return_value = '(matches the default)'
    if not content == default:
        return_value = '!!! Changed from the default of:' + '\n' + default + '\n'
    return return_value

# ----------------------------
def do_cross_comm(effort, the_request='', time_out = 6.0):
    # ----------------------------
    # this one function handles all aspects of Apache (httpd user) thread
    #   interaction with command processing in
    #   the main thread (as root user), using the ram disk as the messaging path
    if effort == 'request':
        time_start = time.time()
        number = int(1000.0 * time_start)
        response_file = str(number) + '_response.txt'
        request_file = response_file.replace('_response', '_request')
        full_response_file = s_loader_comm_queue + response_file
        full_request_file = s_loader_comm_queue + request_file

        do_atomic_write(full_request_file, the_request)

        response = '(command not run)'
        done = False
        while not done:
            if time.time() - time_start > time_out:
                done = True
                response = '(timed out)'
            if os.path.isfile(full_response_file):
                try:
                    response = open(full_response_file, 'r').read()
                    os.remove(full_request_file)
                    done = True
                except:
                    # maybe the owner is not set yet
                    pass
            time.sleep(0.5)
        return response

    if effort == 'cleanup':     # as root
        try:
            # clean out any that are left over
            comm_files = os.listdir(s_loader_comm_queue)
            for comm_file in comm_files:
                os.remove(s_loader_comm_queue + comm_file)
        except:
            pass

    if effort == 'processor':   # as root
        try:
            try:
                comm_files = os.listdir(s_loader_comm_queue)
            except:
                # directory may not exist yet
                comm_files = []

            for comm_file in comm_files:
                if (not '.tmp' in comm_file) and (not '.swp' in comm_file):
                    # The apache thread owns the request file
                    if '_request' in comm_file:
                        output_file = comm_file.replace('_request','_response')
                        if not output_file in comm_files:
                            # ready to do the work here
                            the_command = open(s_loader_comm_queue + comm_file, 'r').read()

                            if 'get_files_and_size,' in the_command:
                                response = get_files_and_size(the_command.split(',')[1])

                            elif 'get_file_content,' in the_command:
                                try:
                                    response = open(the_command.split(',')[1], 'r').read()
                                except:
                                    response = str(traceback.format_exc())
                            else:
                                response = do_one_command_content(the_command)

                            # debug
#                            response = the_command

                            full_output_path = s_loader_comm_queue + output_file
                            do_atomic_write(full_output_path, response)
                            apache_user_name = get_apache_user_name()
                            command = 'chown ' + apache_user_name + ':' + apache_user_name + ' ' + full_output_path
                            do_one_command(command)

                    if '_response' in comm_file:
                        # FixMe: If there is a reponse, and the request has been removed, then delete the response file as a clean up operation
                        input_file = comm_file.replace('_response', '_request')
                        if not input_file in comm_files:
                            os.remove(s_loader_comm_queue + comm_file)

        except:
            do_atomic_write(s_loader_comm_queue + effort + '_exception.txt', traceback.format_exc())


# ----------------------------
def get_files_and_size(folder):
    # ----------------------------
    return_value = ''

    contents = {}
    files = os.listdir(folder)

    for file in files:
        full_path = folder + '/' + file

        if os.path.isfile(full_path):
            if True:
                size = int(do_one_command_content('du --block-size=1 ' + full_path).split()[0])
            else:
                size = os.path.getsize(full_path)

            if not size in contents:
                contents[size] = []
            contents[size].append(file)

    for size in sorted(contents.keys()):
        for file in sorted(contents[size], reverse=True):
            return_value += make_value_to_human(size) + '\t' + file + '\n'

    return return_value

# ----------------------------
def make_value_to_human(value):
    # ----------------------------
    return_value = str(value) # in case it is already 1K, then just return 1K

    # see if it should be an integer
    try:
        if float(value) < s_human_lookup['K']:
            return_value = str(int(float(value)))
    except:
        pass

    if not isinstance(value, str):
        for key in ['K','M','G','T','P']:
            if value >= s_human_lookup[key]:
                return_value = "{:.1f}".format(float(value)/s_human_lookup[key]) + key

    return return_value

# ----------------------------
def make_value(str_field):
    # ----------------------------
    return_value = None

    try:
        return_value = float(str_field)
    except:
        pass

    if return_value is None:
        try:
            return_value = float(str_field[:-1]) * s_human_lookup[str_field[-1]]
        except:
            return_value = -1

    return return_value

# ----------------------------
def make_sorted_human(content, threshold=0, factor=1):
    # ----------------------------
    return_value = ''

    d = {}

    for line in content.split('\n'):
        if line:
            value = make_value(line.split()[0]) * factor
            if not value in d:
                d[value] = []
            d[value].append(line)

    for value in sorted(d.keys()):
        if value == -1:
            for line in d[value]:
                if return_value:
                    return_value += '\n'
                return_value += line
        elif value >= threshold:
            for line in sorted(d[value], reverse=True):
                if return_value:
                    return_value += '\n'
                return_value +=  make_value_to_human(value) + '\t' + ' '.join(line.split()[1:])

    return return_value

# ----------------------------
def make_formatted_text(content):
    # ----------------------------
    # lifted from devicecommand

    # make sure that things don't look like html tags
    # https://www.geeksforgeeks.org/html-entities/
    # &lt;
    # &gt;
    return_value = content.replace('<','&lt;').replace('>','&gt;')

    try:
        # If if looks and feels like a json, then format it as a nice looking one
        return_value = json.dumps(json.loads(content), indent=4, separators=(',', ':'))
    except:
        if '\t' in content:
            return_value = '<table>'
            for line in content.split('\n'):
                tabbed_sections = line.split('\t')
                codded_tabbed_sections = []
                for tabbed_section in tabbed_sections:
                    codded_tabbed_sections.append(tabbed_section)
#                    codded_tabbed_sections.append('<pre>' + codded_tabbed_sections + '</pre>')
#                    codded_tabbed_sections.append('<code>' + codded_tabbed_sections + '</code>')
                return_value += '<tr><td>' + '</td><td>'.join(codded_tabbed_sections) + '</td></tr>'
            return_value +='</table>'


    # and, in any case, make all spaces hold their position
    return_value = return_value.replace('\n', '<br>').replace(' ','&nbsp;')

    return return_value

# ----------------------------
def make_sort_from_any_version(raw_value):
    # ----------------------------
    new_list = []
    for item in raw_value.split('.'):
        try:
            new_list.append('0'*(10-len(item)) + item)
        except:
            pass
    return_value = '.'.join(new_list)

    return return_value

# ----------------------------
def find_later_version(version_current, version_list):
    # ----------------------------
    return_value = ''

    is_dotted_version = '.' in version_current

    full_version = {}
    for raw_version in version_list:
        item = raw_version.replace('_modified','')
        if is_dotted_version:
            if '.' in item:
                full_version[make_sort_from_any_version(item)] = item
        else:
            if not '.' in item:
                full_version[make_sort_from_any_version(item)] = item

    if full_version.keys():
        last_version = full_version[sorted(full_version.keys())[-1]]
        if last_version != version_current.replace('_modified',''):
            return_value = last_version

    return return_value

# ----------------------------
def save_to_loader_change_log(the_kind, the_event, the_who):
    # ----------------------------
    datetime_now = datetime.datetime.now()
    output_file = s_loader_change_log_path + datetime_now.strftime('%Y%m%d_%H%M%S_%f') + '_' + the_kind # 20230303_081332_000000

    the_output_content = copy.copy(the_event)
    if the_who:
        the_output_content = the_who + ': ' + the_output_content
    do_atomic_write_if_different(output_file, the_output_content)

# ----------------------------
def get_current_linked_folder():
    # ----------------------------
    folder_listing, f = do_one_command('ls -l /var/www')
    current_linked = extract_current_linked_folder(folder_listing)
    return current_linked

# ----------------------------
def extract_current_linked_folder(contents):
    # ----------------------------
    return_value = ''
    for line in contents.split('\n'):
        if 'html -> ' in line:
            return_value = line.split('html -> ')[1].split('/')[-1]
    return return_value

# ----------------------------
def get_var_www_file_contents():
    # ----------------------------
    return_value = {}

    base_folder = '/var/www/'

    base_list = os.listdir(base_folder)
    for item in base_list:
        try:
            return_value[item] = os.listdir(base_folder + item)
        except:
            pass
            # not a folder?

    return return_value

# ----------------------------
def make_sort_from_version(raw_value):
    # ----------------------------
    new_list = []
    for item in raw_value.split('.'):
        try:
            if '_modified' in item:
                new_item = item.replace('_modified','')
                new_list.append('0'*(10-len(new_item)) + new_item + '_modified')
            else:
                new_list.append('0'*(10-len(item)) + item)
        except:
            pass
    return_value = '.'.join(new_list)

    return return_value

# ----------------------------
def extract_valid_possible_server_versions(folder_contents):
    # ----------------------------
    versions = {}

    for item in folder_contents.keys():
        if 'loader.py' in folder_contents[item]:
            versions[make_sort_from_version(item)] = item

    return_value = []
    for key in sorted(versions.keys()):
        if versions[key] != 'html': # this is the items in the currently linked folder, and we do not need that
            return_value.append(versions[key])

    return return_value

# ----------------------------
def calculate_wsgi_config_dir_for_os(os_name):
    # ----------------------------
    if os_name == 'ubuntu':
        return '/etc/apache2/conf-available/'
    else:
        return '/etc/httpd/conf.d/'

# ----------------------------
def parse_psax_all_wsgi_runners(content, do_not_include=[]):
    # ----------------------------
    return_value = []

    for line in content.split('\n'):
        if '/var/www/html/' in line:
            runner_name = line.split('/var/www/html/')[1]
            if not runner_name in do_not_include:
                return_value.append(runner_name)

    return return_value

# ----------------------------
def do_server_config():
    # ----------------------------
    start_time = time.time()
    # tail --retry -f /dev/shm/loader_server_config_report.txt
    # another window: watch -n 1 systemctl status httpd

    open(s_work_report_file, 'w').write('starting server configuration...' + '\n')

    try:
        server_content_is_linked = os.path.islink('/var/www/html')
        os_name = get_os_name()
        apache_user_name = get_apache_user_name()

        open(s_work_report_file, 'a').write('server_content_is_linked = ' + str(server_content_is_linked) + '\n')
        open(s_work_report_file, 'a').write('os_name = ' + os_name + '\n')
        open(s_work_report_file, 'a').write('apache_user_name = ' + apache_user_name + '\n')

        if server_content_is_linked:
            # ===========================
            # Collect data on current linked environment
            # ===========================
            services_found = {}
            all_files = os.listdir('.')
            for file in all_files:
                splits = file.split('.')
                if len(splits) == 2:
                    if splits[1] == 'py':
                        the_service = splits[0]
                        services_found[the_service] = {'runner':False}
                        file_content = open(file, 'r').read()
                        if '\ndef main' + '(' in file_content:
                            services_found[the_service]['runner'] = True

            # get list of services that do not want runner/web pages (from datastore, or /var/www/???/)
            list_of_services_to_not_allow = get_service_settings()
            open(s_work_report_file, 'a').write('list_of_services_to_not_allow = ' + str(list_of_services_to_not_allow) + '\n')


            # remove any that need removed
            #    set wsgi to false in services_found
            #    set runner to false in services_found
            pass

            for the_service in list_of_services_to_not_allow:
                if the_service in services_found:
                    services_found[the_service]['runner'] = False

            # ---------------------------
            # make sure all runners are stopped (except ourselves, of course)
            open(s_work_report_file, 'a').write('stopping all runners...' + '\n')
            content, f = do_one_command('ps ax')
            runners_to_stop = parse_psax_all_wsgi_runners(content, do_not_include=['loader-runner'])
            for runner_to_stop in sorted(runners_to_stop):
                # best effort here
                open(s_work_report_file, 'a').write('    stop: ' + runner_to_stop + '\n')
                p, f = do_one_command('systemctl stop ' + runner_to_stop)

            # ---------------------------
            # clean out any leftover status reports
            open(s_work_report_file, 'a').write('clean out any leftover status reports...' + '\n')
            ram_disk_folder = '/dev/shm/'
            ram_disk_content = os.listdir(ram_disk_folder)
            for item in ram_disk_content:
                if '_exceptions_' in item:
                    full_name = ram_disk_folder + item
                    try:
                        os.remove(full_name)
                    except:
                        pass

            # ---------------------------
            # delete all runner configs
            open(s_work_report_file, 'a').write('delete all runner configs...' + '\n')
            system_service_folder = '/lib/systemd/system/'
            all_service_files = os.listdir(system_service_folder)
            for service_file in sorted(all_service_files):
                if ('-runner.service' in service_file) and not ('loader' in service_file):
                    full_service_name = system_service_folder + service_file
                    os.remove(full_service_name)

            # ---------------------------
            # stop the apache process and configure it for 'not auto start' (these wait until complete)
            # Do this after stopping the runners, so that the watchdog process does not restart it
            stop_apache_time_start = time.time()
            open(s_work_report_file, 'a').write('stopping apache...' + '\n')
            do_one_command('systemctl stop apache2')
            do_one_command('systemctl stop httpd')

            do_one_command('systemctl disable apache2')
            do_one_command('systemctl disable httpd')
            stop_apache_time_end = time.time()
            open(s_work_report_file, 'a').write('apache stopped: ' +str(stop_apache_time_end - stop_apache_time_start) + ' seconds' + '\n')

            # clear out all reports
            for file_found in os.listdir('/dev/shm/'):
                if '_exceptions_' in file_found:
                    os.remove('/dev/shm/' + file_found)

            # remove all wsgi setups
            wsgi_config_path = calculate_wsgi_config_dir_for_os(os_name)
            all_wsgi_config_files = os.listdir(wsgi_config_path)
            for wsgi_config_file in sorted(all_wsgi_config_files):
                if ('python-' in wsgi_config_file) and ('.conf' in wsgi_config_file):
                    full_config_name = wsgi_config_path + wsgi_config_file
                    os.remove(full_config_name)

            # !!!!!!!!!!!!!!!!!!!!!!!!!!!!
            # !!!!!!!!!!!!!!!!!!!!!!!!!!!!
            # Now that all is clean, if we have a new version to switch to, then do it now
            if os.path.isfile(s_new_link_file_name):
                new_link_folder = open(s_new_link_file_name, 'r').read()
                open(s_work_report_file, 'a').write('\n' + '!!!! New link folder found ' + new_link_folder + '\n\n')

                the_command = 'sudo ln -sfn /var/www/' + new_link_folder + ' /var/www/html'
                do_one_command(the_command)

                the_service = 'loader'
                if True:
                    open(s_work_report_file, 'a').write('    build: ' + the_service + '\n')
                    item_time_start = time.time()
                    params = copy.deepcopy(services_found[the_service])
                    params['apache_user_name'] = apache_user_name
                    params['build_runner_for_linked'] = True
                    items_to_run = build_items_to_run_for_module(os_name, the_service, params)

                    item_time_intermediate = time.time()
                    if items_to_run:
                        # do the run items to build configs, and start runner
                        for item in items_to_run:
                            time_before = time.time()
                            result = run_an_item(item)
                            time_after = time.time()
    #                        open(s_work_report_file, 'a').write('        ' + str(int(1000000.0*(time_after - time_before))) + ' microseconds, ' + str(item) + '\n')
                    item_time_end = time.time()
                    open(s_work_report_file, 'a').write('        ' + str(int(1000.0*(item_time_end - item_time_intermediate))) + ' milliseconds, ' + str(int(1000.0*(item_time_end - item_time_start))) + ' milliseconds' + '\n')


                os.remove(s_new_link_file_name)
                open(s_work_report_file, 'a').write('\n' + '!!!! New link folder found ' + 'do the second restart...' + '\n\n')
                p, f = do_one_command('systemctl restart loader-runner')
                open(s_work_report_file, 'a').write('\n' + '!!!! p = ' + p + '\n\n')
                open(s_work_report_file, 'a').write('\n' + '!!!! f = ' + f + '\n\n')

            else:

                # ---------------------------
                # Do all runner and web configs
                open(s_work_report_file, 'a').write('build all runner and wsgi configs...' + '\n')
                config_time_start = time.time()

                for the_service in sorted(services_found.keys()):
                    open(s_work_report_file, 'a').write('    build: ' + the_service + '\n')
                    item_time_start = time.time()
                    params = copy.deepcopy(services_found[the_service])
                    params['apache_user_name'] = apache_user_name
                    params['build_runner_for_linked'] = True
                    items_to_run = build_items_to_run_for_module(os_name, the_service, params)

                    item_time_intermediate = time.time()
                    if items_to_run:
                        # do the run items to build configs, and start runner
                        for item in items_to_run:
                            time_before = time.time()
                            result = run_an_item(item)
                            time_after = time.time()
    #                        open(s_work_report_file, 'a').write('        ' + str(int(1000000.0*(time_after - time_before))) + ' microseconds, ' + str(item) + '\n')
                    item_time_end = time.time()
                    open(s_work_report_file, 'a').write('        ' + str(int(1000.0*(item_time_end - item_time_intermediate))) + ' milliseconds, ' + str(int(1000.0*(item_time_end - item_time_start))) + ' milliseconds' + '\n')
                    _ = """
        build: watchdog
            157 microseconds, {'name': 'service config', 'content': 'WSGIScriptAlias /watchdog /var/www/html/watchdog.py\nWSGIApplicationGroup %{GLOBAL}', 'fileout': '/etc/httpd/conf.d/python-watchdog.conf'}
            106840 microseconds, {'name': 'service permissions', 'command': 'chown apache:apache /var/www/html/watchdog.py', 'time_out': 10, 'expect': {'new': <class 'pexpect.exceptions.EOF'>, 'existing': 'Conf python-loader already enabled', 'fail': 'Unable to locate package'}}
            275 microseconds, {'name': 'watchdog:starter runner file', 'content': '#!/usr/bin/env python3\nimport watchdog\nwatchdog.main()\n', 'fileout': '/var/www/html/watchdog-runner'}
            106387 microseconds, {'name': 'watchdog:starter runner commands', 'command': 'chmod +x /var/www/html/watchdog-runner', 'time_out': 10, 'expect': {'new': <class 'pexpect.exceptions.EOF'>, 'existing': 'none', 'fail': 'none'}}
            274 microseconds, {'name': 'watchdog:service runner file', 'content': '[Unit]\nDescription=Slicer maintenance daemon\nAfter=network.target\nStartLimitBurst=5\nStartLimitIntervalSec=10\n\n[Service]\nExecStart=/var/www/html/watchdog-runner\nWorkingDirectory=/var/www/html/\nStandardOutput=inherit\nStandardError=inherit\nRestart=always\nRestartSec=1\nUser=root\n\n[Install]\nWantedBy=multi-user.target\n', 'fileout': '/lib/systemd/system/watchdog-runner.service'}
            214 milliseconds, 214 milliseconds
                    """

                config_time_end = time.time()
                open(s_work_report_file, 'a').write('configs built: ' + str(config_time_end - config_time_start) + ' seconds' + '\n')

                # get runners going
                open(s_work_report_file, 'a').write('start all runners...' + '\n')
                do_one_command('systemctl daemon-reload')
                for the_service in sorted(services_found.keys()):
                    open(s_work_report_file, 'a').write('    start: ' + the_service + '\n')
                    if services_found[the_service]['runner']:
                        do_one_command('systemctl start ' + the_service + '-runner.service')

                # get apache going (One will fail... that's fine, the other one will work)
                # (index load might/will have started apache, so do a restart here)
                open(s_work_report_file, 'a').write('restarting apache...' + '\n')
                do_one_command('systemctl restart apache2')
                do_one_command('systemctl restart httpd')
                open(s_work_report_file, 'a').write('apache restarted.' + '\n')


        #        open('/dev/shm/running_exceptions_' + service, 'w').write('services_found' + ':' + str(services_found))


        else:
            # Original config
            # The runners should be set to self start.
            # apache should self start.

            # don't worry about going from linked method back to the original method
            pass

        end_time = time.time()
        open(s_work_report_file, 'a').write('server configuration is complete' + '\n' + 'seconds: ' + str(end_time - start_time) + '\n')
        open(s_work_report_file, 'a').write('----------------------------------------' + '\n')
    except:
        open(s_work_report_file, 'a').write(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")) + '\n')


# ----------------------------
def get_zip_version_string_from_filename(filename):
    # ----------------------------
    return filename.split('-')[1].replace('.zip','')

# ----------------------------
def write_to_watchdog_log(the_entry):
    # ----------------------------
    try:
        import watchdog
        watchdog.write_to_log(the_entry)
    except:
        pass


# ----------------------------
def loader_live_data():
    # ----------------------------
    files_live_d = get_dir_list_and_contents('/dev/shm/', ['startup_exceptions_', 'running_exceptions_'])
    files_d = get_dir_list_and_contents(s_location_base)
    services_d = get_services()
    live_data = calc_loader_live_data(files_live_d, files_d, services_d)

    return live_data


# ----------------------------
def get_services():
    # ----------------------------
    p, f = do_one_command('systemctl list-units --type=service --state=active')

    result = {}
    for line in p.split('\n'):
        if line:
            splits = line.split()
            result[splits[0]] = line

    return result


# ----------------------------
def get_dir_list_and_contents(path, filter_list_to_get_content=[]):
    # ----------------------------
    return_value = {}
    files = os.listdir(path)

    for filename in files:
        return_value[filename] = {}  # {'content':filename}

        return_value[filename]['full_path'] = path + filename

        get_content = False
        for item in filter_list_to_get_content:
            if item in filename:
                get_content = True
                break
        if get_content:
            try:
                return_value[filename]['content'] = open(path + filename, 'r').read()
            except:
                pass

    return return_value


# ----------------------------
def calc_loader_live_data(live_content_d={}, location_base_content_d={}, services_d={}):
    # ----------------------------
    return_value = {}

    for item in live_content_d.keys():
        if 'loader_quick_work_' in item:
            process = item.replace('loader_quick_work_', '')
            if not process in return_value:
                return_value[process] = {}
            return_value[process]['loading'] = True

        if 'running_exceptions_' in item:
            process = item.replace('running_exceptions_', '')
            if not process in return_value:
                return_value[process] = {}
            if 'content' in live_content_d[item]:
                return_value[process]['running_exceptions_'] = live_content_d[item]['content']
            else:
                return_value[process]['running_exceptions_'] = 'no running content'

        if 'startup_exceptions_' in item:
            process = item.replace('startup_exceptions_', '')
            if not process in return_value:
                return_value[process] = {}
            if 'content' in live_content_d[item]:
                return_value[process]['startup_exceptions_'] = live_content_d[item]['content']
            else:
                return_value[process]['startup_exceptions_'] = 'no startup content'

    for item in location_base_content_d.keys():
        if ('.py' in item) and not ('settings' in item) and not ('.swp' in item):
            process = item.replace('.py', '')
            if not process in return_value:
                return_value[process] = {}
            return_value[process]['loaded'] = True

            if 'full_path' in location_base_content_d[item]:  # on a live system, with actual content
                import_report = ''
                color_to_use = ''
                try:
                    new_module = __import__(process)
                    import_report = new_module.version
                except:
                    import_report = '(failed) ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
                    color_to_use = s_rgba_red_light
                return_value[process]['import_report'] = {'text': import_report, 'color': color_to_use}

                service_report = '(failed)'
                color_to_use = s_rgba_red_light
                service_name = process + '-runner.service'
                if service_name in services_d:
                    if 'running' in services_d[service_name]:
                        service_report = 'ok'
                        color_to_use = ''
                return_value[process]['runner_report'] = {'text': service_report, 'color': color_to_use}

    return return_value


# ----------------------------
def get_cert_files_from_apache_config(content):
    # ----------------------------
    return_value = {}

    section_name = ''
    for item in content.split('\n'):
        if '<VirtualHost' in item:
            section_name = item

        splits = item.split()
        if len(splits) > 1:
            if 'SSLCertificateFile' == splits[0]:
                if not section_name in return_value:
                    return_value[section_name] = {'SSLCertificateFile': '', 'SSLCertificateKeyFile': ''}
                return_value[section_name]['SSLCertificateFile'] = splits[1].replace('"', '')
            if 'SSLCertificateKeyFile' == splits[0]:
                if not section_name in return_value:
                    return_value[section_name] = {'SSLCertificateFile': '', 'SSLCertificateKeyFile': ''}
                return_value[section_name]['SSLCertificateKeyFile'] = splits[1].replace('"', '')
    return return_value


# ----------------------------
def get_uptime():
    # ----------------------------
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.readline().split()[0])
            return int(uptime_seconds)
    except:
        return 0


# ----------------------------
def get_module_name_from_filename(filename):
    # ----------------------------
    module_name = filename.replace('slicer_wsgi_', '').split('.')[0]

    splits = module_name.split('_')
    if len(splits) > 1:
        module_name = splits[0]

    return module_name

# ----------------------------
def do_one_command_content(command):
    # ----------------------------
    p, f = do_one_command(command)
    return p

# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)
    # command_splits = command.split(" ")
    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()
    return (mem_string, fails)


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        if False:  # add a test here, that should show that we need some attention
            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(loader status)'

    status = os.system('systemctl is-active --quiet loader-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value

# ====================================
def do_log_cleanup():
    # ====================================

    report = ''

    patterns = ['/var/log/httpd/*.gz',
                '/var/log/*.gz',
                '/var/log/*.1',
                '/var/log/*.2',
                '/var/log/*.3',
                '/var/log/*.4',
                '/var/log/*.log-*',
                '/var/log/*-*',
                ]

    for pattern in patterns:
        try:
            report += 'try to remove: ' + pattern + '\n'
            p, f = do_one_command('sudo rm ' + pattern)
            report += 'removed: ' + pattern + '\n'
            report += p + '\n'
            report += f + '\n'
        except:
            report += traceback.format_exc().replace("\"", "'")
        report += '\n-------------------------------\n\n'

    try:
        open ('/dev/shm/loader_log_cleanup.txt', 'w').write(report)
    except:
        pass

# ====================================
def do_cleanup():
    # ====================================
    apache_user_name = get_apache_user_name()

    # Somehow the permissions got set to root. Do our best to keep it what we need
    command = 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + s_location_root
    try:
        do_one_command(command)
    except:
        pass

    # Somehow the permissions got set to root. Do our best to keep it what we need
    command = 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + s_location_base
    try:
        do_one_command(command)
    except:
        pass

    location_quick = '/var/www/loader_quick_work'
    try:
        os.makedirs(location_quick)
    except:
        pass
    command = 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + location_quick
    try:
        do_one_command(command)
    except:
        pass

    try:
        os.makedirs(s_loader_comm_queue)
    except:
        pass

    command = 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + s_loader_comm_queue
    try:
        do_one_command(command)
    except:
        pass


# ====================================
def do_quick_work(cleanup_unlock_time):
    # ====================================
    last_module = ''
    last_report = ''
    last_exception = ''

    # Look for some quick work to do
    try:
        command = open('/dev/shm/loader_cmd.txt', 'r').read()
        os.remove('/dev/shm/loader_cmd.txt')

        user_found = 'user.unknown'
        try:
            user_found = open('/dev/shm/loader_cmd.txt' + '.user.txt', 'r').read()
            os.remove('/dev/shm/loader_cmd.txt' + '.user.txt')
        except:
            pass
        if command == 'reboot':
            write_to_watchdog_log('loader initiated reboot by ' + user_found)
            do_one_command('reboot')
        if command == 'reload':
            write_to_watchdog_log('loader initiated reload apache by ' + user_found)
            do_one_command('systemctl reload apache2')
            do_one_command('systemctl reload httpd')
        if command == 'restart':
            write_to_watchdog_log('loader initiated restart apache by ' + user_found)
            do_one_command('systemctl restart apache2')
            do_one_command('systemctl restart httpd')
        _debug_notes = """
cd /etc/apache2
systemctl restart apache2.service
systemctl status apache2.service
journalctl -xeu apache2.service

apache2ctl configtest

bad:
AH00526: Syntax error on line 15 of /etc/apache2/sites-enabled/000-default.conf:
SSLCertificateFile: file '/etc/pki/tls/private/slicer.cert' does not exist or is empty
Action 'configtest' failed.
The Apache error log may have more information.

good:
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 127.0.1.1. Set the 'ServerName' directive globally to suppress this message
Syntax OK

        """
    except:
        pass

    try:
        files = os.listdir('/dev/shm/')
        server_content_is_linked = os.path.islink('/var/www/html')
        if 'config_unlock.txt' in files:
            if not cleanup_unlock_time:
                cleanup_unlock_time = time.time()
        if cleanup_unlock_time:
            if abs(time.time() - cleanup_unlock_time) > 3 * 60:
                os.remove('/dev/shm/config_unlock.txt')
                cleanup_unlock_time = None

        if True:
            if os.path.exists('/var/www/loader_quick_work/'):
                files = os.listdir('/var/www/loader_quick_work/')
                for file_name in files:
                    full_file_name = '/var/www/loader_quick_work/' + file_name

                    if 'loader_runner_stop_' in file_name:
                        module_name = open(full_file_name, 'r').read()
                        runner_to_stop = module_name + '-runner'

                        os.remove(
                            full_file_name)  # do this now, so that if we are the one being loaded, we clean up first

                        do_one_command('systemctl stop ' + runner_to_stop)

                    elif 'loader_runner_start_' in file_name:
                        module_name = open(full_file_name, 'r').read()
                        runner_to_start = module_name + '-runner'
                        os.remove(
                            full_file_name)  # do this now, so that if we are the one being loaded, we clean up first

                        do_one_command('systemctl start ' + runner_to_start)

                    elif 'loader_service_delete_' in file_name:
                        module_name = open(full_file_name, 'r').read()

                        os.remove(
                            full_file_name)  # do this now, so that if we are the one being loaded, we clean up first

                        try:
                            os.remove(module_name + '.py')
                        except:
                            pass


                    elif 'loader_quick_work_' in file_name:
                        last_module = file_name
                        items_to_run = None

                        module_name = open(full_file_name, 'r').read()
                        os_name = get_os_name()
                        params = {}
                        params['build_runner_for_linked'] = server_content_is_linked
                        params['runner'] = True

                        data_store_content = datastore.all_datastore()
                        the_key = 'service_loader_' + module_name + '_allow_runner'
                        the_value = datastore.get_value_stored(data_store_content, the_key)
                        if the_value == 'no':
                            params['runner'] = False

                        items_to_run = build_items_to_run_for_module(os_name, module_name, params)

                        os.remove(
                            full_file_name)  # do this now, so that if we are the one being loaded, we clean up first

                        if items_to_run:
                            reports = run_items(items_to_run)

                            last_report = ''
                            last_report += str(items_to_run)
                            last_report += '\n\n'
                            last_report += str(reports)
                            open(full_file_name.replace('loader_quick_work_', 'report_quick_work_'), 'w').write(
                                last_report)

                        # clear out all reports
                        for file_found in os.listdir('/dev/shm/'):
                            if '_exceptions_' in file_found:
                                os.remove('/dev/shm/' + file_found)

                        if server_content_is_linked:
                            # we need to do the daemon reload, and service restart here.
                            do_one_command('systemctl daemon-reload')

                            data_store_content = datastore.all_datastore()
                            the_key = 'service_loader_' + module_name + '_allow_runner'
                            the_value = datastore.get_value_stored(data_store_content, the_key)
                            if the_value == 'no':
                                do_one_command('systemctl stop ' + module_name + '-runner.service')
                            else:
                                do_one_command('systemctl restart ' + module_name + '-runner.service')

        else:
            files = os.listdir('/dev/shm/')
            for file_name in files:
                if 'loader_quick_work_' in file_name:
                    last_module = file_name
                    full_file_name = '/dev/shm/' + file_name
                    items_to_run = None

                    module_name = open(full_file_name, 'r').read()
                    os_name = get_os_name()
                    items_to_run = build_items_to_run_for_module(os_name, module_name)

                    os.remove(
                        full_file_name)  # do this now, so that if we are the one being loaded, we clean up first

                    if items_to_run:
                        reports = run_items(items_to_run)

                        last_report = ''
                        last_report += str(items_to_run)
                        last_report += '\n\n'
                        last_report += str(reports)
                        open(full_file_name.replace('loader_quick_work_', 'report_quick_work_'), 'w').write(
                            last_report)

    except:
        last_exception = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        last_exception += '\n' + last_module + '\n' + last_report + '\n'

    return last_exception, cleanup_unlock_time

def build_messages_report():
    body = ''
    try:
        the_content = open('/var/log/messages', 'r').read()
        the_class_object = class_log_monitor()
        the_class_object.process_log_file_content(the_content)

        body += '<center>/var/log/messages summary report</center><br><br>'

        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        for line in the_class_object.report().split('\n'):
            body += '<tr>'
            for item in line.split(','):
                body += '<td>'
                body += '<tt>'
                body += item

                body += '</tt>'
                body += '</td>'
                body += '<td>'
            body += '</tr>'
        body += '</table>'
        body += '</center>'

        # cleanup
        the_class_object = None
    except:
        body += traceback.format_exc().replace("\"", "'")
    do_atomic_write_if_different('/dev/shm/loader_messages_summary.txt', body)


# Main is the loop for the "loader-runner" that the service starts
# ====================================
def main():
    # ====================================

    do_server_config()

    do_cross_comm('cleanup')

    cleanup_unlock_time = None
    pass_count = 0

    minutes_between_cleanups = 10
    time_of_last_cleanup = 0
    last_exception = ''

    build_messages_report_time = 0
    unzip_pass_count = 0

    the_who = 'loader_main'
    while True:
        if time.time() - build_messages_report_time > 60:
            build_messages_report()
            build_messages_report_time = time.time()

        do_cross_comm('processor')

        last_exception_new, cleanup_unlock_time = do_quick_work(cleanup_unlock_time)
        if last_exception_new:
            last_exception = last_exception_new

        if abs(time.time() - time_of_last_cleanup) > minutes_between_cleanups * 60:
            do_cleanup()
            #do_log_cleanup()

            time_of_last_cleanup = time.time()

        try:
            versions_zips_content = os.listdir(s_location_version_zips)
        except:
            versions_zips_content = []

        try:
            for item in versions_zips_content:
                if '.needunzip' in item:
                    do_unzip(item, the_who)

        except:
            last_exception = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
            save_to_loader_change_log('zip_unzip', 'Exception: ' + last_exception, the_who)

        # look for a new linked version to switch to
        try:
            if os.path.isfile(s_new_link_file_name):
                # ok, at this point, we want to restart ourselves, and let the do_server_config
                #   find this new request, and handle it
                new_link_folder = open(s_new_link_file_name, 'r').read()
                open(s_work_report_file, 'a').write('\n' + '!!!! New link folder found ' + new_link_folder + '\n\n')
                open(s_work_report_file, 'a').write('\n' + '!!!! New link folder found ' + 'doing first restart...' + '\n\n')
                do_one_command('systemctl restart loader-runner')
        except:
            pass

        # look for a linked version to delete
        try:
            if os.path.isfile(s_delete_link_file_name):
                # ok, at this point, we want to restart ourselves, and let the do_server_config
                #   find this new request, and handle it
                delete_link_folder = open(s_delete_link_file_name, 'r').read()
                open(s_work_report_file, 'a').write('\n' + '!!!! Delete link folder found ' + delete_link_folder + '\n\n')
                os.remove(s_delete_link_file_name)
                do_one_command('rm -rf ' + s_location_root + delete_link_folder)
        except:
            pass

        try:
            do_atomic_write_if_different(s_loader_module_save_path, json.dumps(os.listdir()))
        except:
            pass

        pass_count += 1

        time.sleep(2)
        open('/dev/shm/running_exceptions_' + service, 'w').write(
            s_this_user + ': ' + 'pass_count : ' + str(pass_count) + '\n' + last_exception)


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    if existing_content != content:
        do_atomic_write(output_file, content)


# ----------------------------
def do_atomic_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'w') as f:
        f.write(content)

    shutil.move(temp_name, output_file)


# ----------------------------
def do_atomic_pickle_write(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    temp_name = os.path.dirname(output_file) + '/' + hashlib.md5(output_file.encode('utf-8')).hexdigest() + '.tmp'
    with open(temp_name, 'wb') as f:
        pickle.dump(content, f)

    shutil.move(temp_name, output_file)


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ----------------------------
def run_items(items_to_run, reports_input=[]):
    # ----------------------------

    reports = copy.deepcopy(reports_input)

    all_success = True
    for report in reports:
        if not report['success']:
            all_success = False

    if all_success:
        for item in items_to_run:
            print('--------------------')
            print('item', item)
            print('--------------------')
            result = run_an_item(item)
            reports.append(result)
            if not result['success']:
                print('!!!!!!!!!!!!!!!!!!!!')
                print('!!!!!!!!!!!!!!!!!!!!')
                print('result', result)
                print('!!!!!!!!!!!!!!!!!!!!')
                print('!!!!!!!!!!!!!!!!!!!!')
                break

    return reports


# ----------------------------
def build_items_to_run_for_module(os_name, module_name, params={}):
    # ----------------------------
    items_to_run = []
    if os_name == 'ubuntu':
        items_to_run = build_items_to_run_for_module_ubuntu(module_name, params)

    if os_name == 'rocky':
        items_to_run = build_items_to_run_for_module_rocky(module_name, params)

    return items_to_run


# ----------------------------
def build_items_to_run_for_module_rocky(module_name, params, os_name='rocky'):
    # ----------------------------
    import pexpect

    if 'apache_user_name' in params:
        apache_user_name = params['apache_user_name']
    else:
        apache_user_name = get_apache_user_name()

    do_build_runner = True
    # always build the runner config, just do not start it if not wanted.
    _ = """
    if 'runner' in params:
        do_build_runner = params['runner']
    """

    do_build_wsgi = True

    build_runner_for_linked = False
    if 'build_runner_for_linked' in params:
        build_runner_for_linked = params['build_runner_for_linked']

    items_to_run = []

    # ---------------------------
    # apache web page install
    # ---------------------------
    if do_build_wsgi:
        module_file = calculate_wsgi_config_dir_for_os(os_name) + "python-" + module_name + ".conf"

        source_path = s_location_base + module_name + '.py'
        module_config = "WSGIScriptAlias /" + module_name + " " + source_path

        # was getting errors about "only one interpreter per..."
        # https://stackoverflow.com/questions/63895870/python-numpy-interpreter-change-detected-this-module-can-only-be-loaded-into
        module_config += '\n' + 'WSGIApplicationGroup %{GLOBAL}'

        items_to_run.append({'name': 'service config',
                             'content': module_config,
                             'fileout': module_file,
                             })

        command = 'chown ' + apache_user_name + ':' + apache_user_name + ' ' + source_path
        items_to_run.append({'name': 'service permissions',
                             'command': command,
                             'time_out': 10,
                             'expect': {'new': pexpect.EOF,
                                        'existing': 'Conf python-loader already enabled',
                                        'fail': 'Unable to locate package'}})

    # ---------------------------
    # service runner install
    # ---------------------------
    if do_build_runner:
        runner_contents = build_runner_thread_content_for_module(module_name, params)

        for runner_content in runner_contents:
            full_description = module_name + ':' + runner_content['desc']

            items_to_run.append({'name': full_description + ' runner file',
                                 'content': runner_content['content'],
                                 'fileout': runner_content['file'],
                                 })

            for command in runner_content['commands']:
                items_to_run.append({'name': full_description + ' runner commands',
                                     'command': command,
                                     'time_out': 10,
                                     'expect': {'new': pexpect.EOF,
                                                'existing': 'none',
                                                'fail': 'none'}})

    # ---------------------------
    # index page extra work
    # ---------------------------
    if module_name == 'index':
        items_to_run.append({'name': module_name + ' apache index',
                             'content': '<Directory "/">\nDirectoryIndex index\n</Directory>\nGracefulShutdownTimeout 5\n',
                             'filecontains': '/etc/httpd/conf/httpd.conf',
                             })

        # curl http://localhost/server-status?auto
        # https://docstore.mik.ua/orelly/linux/apache/ch11_03.htm
        # https://support.cpanel.net/hc/en-us/articles/360052040234-Understanding-the-Apache-scoreboard#:~:text=S%20%E2%80%94%20The%20server%20is%20starting,%2Dalive%20(read)%20mode.
        # http://lpec5009slicr05.cardinalhealth.net/server-status?refresh

        # scoreboard is full, not at MaxRequestWorkers
        # https://httpd.apache.org/docs/2.4/mod/event.html

        items_to_run.append({'name': module_name + ' apache index',
                             'content': 'LoadModule status_module modules/mod_status.so\n<Location "/server-status">\n      SetHandler server-status\n      <RequireAll>\n            Require all granted\n      </RequireAll>\n</Location>',
                             'filecontains': '/etc/httpd/conf/httpd.conf',
                             })

        # https://stackoverflow.com/questions/40028497/import-pandas-on-apache-server-causes-timeout-error
        # WSGIApplicationGroup %{GLOBAL}
        # https://modwsgi.readthedocs.io/en/develop/configuration-directives/WSGIApplicationGroup.html



        # https://serverfault.com/questions/1058677/apache-2-4-37-reload-causes-error-scoreboard-is-full-not-at-maxrequestworkers
        # This ust delays the issue, it does not fix it
        _ = """
<IfModule mpm_event_module>
   LoadModule cgid_module modules/mod_cgid.so
   ServerLimit 48
   ThreadsPerChild 25
   MaxRequestWorkers 800
</IfModule>
        """

        # log rotate causing issues?
        # https://www.apachelounge.com/viewtopic.php?p=42473

        import organization
        apache_config = organization.get_apache_config()

        items_to_run.append({'name': 'htmlfiles create',
                             'command': 'mkdir -p ' + apache_config['apache_files_path'],
                             'time_out': 2,
                             'expect': {'new': pexpect.EOF,
                                        'existing': '(none)',
                                        'fail': '(none)'}})

        items_to_run.append({'name': 'configure htmlfiles permissions',
                             'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + apache_config[
                                 'apache_files_path'],
                             'time_out': 2,
                             'expect': {'new': pexpect.EOF,
                                        'existing': '(none)',
                                        'fail': '(none)'}})

        # inspect the config file, and if it references certificate files that do not exist, then
        #   do something about it (create self signed ones)
        _notes = """
openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out slicer.crt -keyout slicer.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1" -subj "/C=US/ST=Ohio/L=Columbus/O=(empty org)/OU=(empty unit)/CN=(empty name)"
cp slicer.crt /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
        """
        cert_files_required = get_cert_files_from_apache_config(apache_config['apache_config_content'])
        for section_name in cert_files_required.keys():
            cert_file_required = cert_files_required[section_name]['SSLCertificateFile']
            key_file_required = cert_files_required[section_name]['SSLCertificateKeyFile']

            build_needed = False
            if cert_file_required:
                if not os.path.isfile(cert_file_required):
                    build_needed = True
            if key_file_required:
                if not os.path.isfile(key_file_required):
                    build_needed = True

            if build_needed:
                items_to_run.append({'name': module_name + ' apache certs build step 1',
                                     'command': 'openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out self_cert.crt -keyout self_cert.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1" -subj "/C=US/ST=Ohio/L=Columbus/O=(empty org)/OU=(empty unit)/CN=(empty name)"',
                                     'time_out': 30,
                                     'expect': {'new': pexpect.EOF,
                                                'existing': 'none',
                                                'fail': 'none'}})

                if cert_file_required:
                    items_to_run.append({'name': module_name + ' apache certs build step 2 cert',
                                         'command': 'cp self_cert.crt ' + cert_file_required,
                                         'time_out': 30,
                                         'expect': {'new': pexpect.EOF,
                                                    'existing': 'none',
                                                    'fail': 'none'}})

                if key_file_required:
                    items_to_run.append({'name': module_name + ' apache certs build step 2 key',
                                         'command': 'cp self_cert.key ' + key_file_required,
                                         'time_out': 30,
                                         'expect': {'new': pexpect.EOF,
                                                    'existing': 'none',
                                                    'fail': 'none'}})

        items_to_run.append({'name': 'configure apache file',
                             'filematches': '/etc/httpd/conf.d/slicer.conf',
                             'content': apache_config['apache_config_content'],
                             })

        # sudo systemctl status httpd
        # sudo tail -n 30 /var/log/httpd/error_log

        if not build_runner_for_linked:
            items_to_run.append({'name': module_name + ' apache restart',
                                 'command': 'systemctl restart httpd',
                                 'time_out': 30,
                                 'expect': {'new': pexpect.EOF,
                                            'existing': 'none',
                                            'fail': 'none'}})
        # ---------------------------
        # end: index page extra work
        # ---------------------------

    return items_to_run


# ----------------------------
def build_items_to_run_for_module_ubuntu(module_name, params, os_name='ubuntu'):
    # ----------------------------
    import pexpect

    if 'apache_user_name' in params:
        apache_user_name = params['apache_user_name']
    else:
        apache_user_name = get_apache_user_name()

    do_build_runner = True
    if 'runner' in params:
        do_build_runner = params['runner']

    do_build_wsgi = True

    build_runner_for_linked = False
    if 'build_runner_for_linked' in params:
        build_runner_for_linked = params['build_runner_for_linked']

    items_to_run = []

    # ---------------------------
    # apache web page install
    # ---------------------------
    if do_build_wsgi:
        module_file = calculate_wsgi_config_dir_for_os(os_name) + "python-" + module_name + ".conf"
        module_config = "WSGIScriptAlias /" + module_name + " " + s_location_base + module_name + '.py'

        # was getting erros about "only one interpreter per..."
        # https://stackoverflow.com/questions/63895870/python-numpy-interpreter-change-detected-this-module-can-only-be-loaded-into
        module_config += '\n' + 'WSGIApplicationGroup %{GLOBAL}'

        items_to_run.append({'name': 'service config',
                             'content': module_config,
                             'fileout': module_file,
                             })

        command = 'a2enconf ' + module_file.split('/')[-1]

        items_to_run.append({'name': 'service load',
                             'command': command,
                             'time_out': 10,
                             'expect': {'new': pexpect.EOF,
                                        'existing': 'Conf python-loader already enabled',
                                        'fail': 'Unable to locate package'}})

        items_to_run.append({'name': 'service start',
                             'command': 'systemctl reload apache2',
                             'time_out': 10,
                             'expect': {'new': pexpect.EOF,
                                        'existing': 'none',
                                        'fail': 'Unable to locate package'}})

    # ---------------------------
    # service runner install
    # ---------------------------
    if do_build_runner:
        runner_contents = build_runner_thread_content_for_module(module_name, params)

        for runner_content in runner_contents:
            full_description = module_name + ':' + runner_content['desc']

            items_to_run.append({'name': full_description + ' runner file',
                                 'content': runner_content['content'],
                                 'fileout': runner_content['file'],
                                 })

            for command in runner_content['commands']:
                items_to_run.append({'name': full_description + ' runner commands',
                                     'command': command,
                                     'time_out': 10,
                                     'expect': {'new': pexpect.EOF,
                                                'existing': 'none',
                                                'fail': 'none'}})

    # ---------------------------
    # index page extra work
    # ---------------------------
    if module_name == 'index':
        items_to_run.append({'name': module_name + ' apache index',
                             'content': '<Directory "/">\nDirectoryIndex index\n</Directory>\n',
                             'filecontains': '/etc/apache2/apache2.conf',
                             })

        import organization
        apache_config = organization.get_apache_config()

        items_to_run.append({'name': 'htmlfiles create',
                             'command': 'mkdir -p ' + apache_config['apache_files_path'],
                             'time_out': 2,
                             'expect': {'new': pexpect.EOF,
                                        'existing': '(none)',
                                        'fail': '(none)'}})

        items_to_run.append({'name': 'configure htmlfiles permissions',
                             'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + apache_config[
                                 'apache_files_path'],
                             'time_out': 2,
                             'expect': {'new': pexpect.EOF,
                                        'existing': '(none)',
                                        'fail': '(none)'}})

        # inspect the config file, and if it references certificate files that do not exist, then
        #   do something about it (create self signed ones)
        _notes = """
openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out slicer.crt -keyout slicer.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1" -subj "/C=US/ST=Ohio/L=Columbus/O=(empty org)/OU=(empty unit)/CN=(empty name)"
cp slicer.crt /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
        """
        cert_files_required = get_cert_files_from_apache_config(apache_config['apache_config_content'])
        for section_name in cert_files_required.keys():
            cert_file_required = cert_files_required[section_name]['SSLCertificateFile']
            key_file_required = cert_files_required[section_name]['SSLCertificateKeyFile']

            build_needed = False
            if cert_file_required:
                if not os.path.isfile(cert_file_required):
                    build_needed = True
            if key_file_required:
                if not os.path.isfile(key_file_required):
                    build_needed = True

            if build_needed:
                items_to_run.append({'name': module_name + ' apache certs build step 1',
                                     'command': 'openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out self_cert.crt -keyout self_cert.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1" -subj "/C=US/ST=Ohio/L=Columbus/O=(empty org)/OU=(empty unit)/CN=(empty name)"',
                                     'time_out': 30,
                                     'expect': {'new': pexpect.EOF,
                                                'existing': 'none',
                                                'fail': 'none'}})

                if cert_file_required:
                    items_to_run.append({'name': module_name + ' apache certs build step 2 cert',
                                         'command': 'cp self_cert.crt ' + cert_file_required,
                                         'time_out': 30,
                                         'expect': {'new': pexpect.EOF,
                                                    'existing': 'none',
                                                    'fail': 'none'}})

                if key_file_required:
                    items_to_run.append({'name': module_name + ' apache certs build step 2 key',
                                         'command': 'cp self_cert.key ' + key_file_required,
                                         'time_out': 30,
                                         'expect': {'new': pexpect.EOF,
                                                    'existing': 'none',
                                                    'fail': 'none'}})

        items_to_run.append({'name': 'configure apache file',
                             'filematches': '/etc/apache2/sites-available/000-default.conf',
                             'content': apache_config['apache_config_content'],
                             })

        if not build_runner_for_linked:
            items_to_run.append({'name': module_name + ' apache restart',
                                 'command': 'systemctl restart apache2',
                                 'time_out': 30,
                                 'expect': {'new': pexpect.EOF,
                                            'existing': 'none',
                                            'fail': 'none'}})
        # ---------------------------
        # end: index page extra work
        # ---------------------------

    return items_to_run


# ----------------------------
def setup_module(os_name, module_name, reports_input=[]):
    # ----------------------------

    # build it
    items_to_run = build_items_to_run_for_module(os_name, module_name)

    # ---------------------------
    # do it
    # ---------------------------

    reports = run_items(items_to_run, reports_input)

    return reports


# ----------------------------
def set_content_from_form(configure_item, content, user_found):
    # ----------------------------
    action_report = ''
    try:
        file_location = configure_item['location']
        do_atomic_write_if_different(file_location, content)

        action_report = 'set ' + '"' + file_location + '"' + ' to ' + '"' + content + '"'

        do_atomic_write_if_different(configure_item['location'] + '.user.txt', user_found)

#        open(configure_item['location'], 'w').write(content)
#        open(configure_item['location'] + '.user.txt', 'w').write(user_found)
    except:
        pass

    return action_report

# ----------------------------
def get_content_for_form(configure_item):
    # ----------------------------
    return_value = configure_item['default_value']

    try:
        content = open(configure_item['location'], 'r').read()

        if configure_item['storage_type'] == 'json':
            return_value = json.loads(content.replace("'", '"'))
        if configure_item['storage_type'] == 'string':
            return_value = content
    except:
        pass

    return return_value


# ----------------------------
def get_config_list(environ):
    # ----------------------------
    item_count = 0
    items_to_configure = {}

    unlocked = False
    allowed_to_delete = False

    try:
        import permissions
        if permissions.permission_prefix_allowed(environ, 'loader_create'):
            unlocked = True
        if permissions.permission_prefix_allowed(environ, 'loader_update'):
            unlocked = True
        if permissions.get_is_admin(environ):
            unlocked = True
        if permissions.permission_prefix_allowed(environ, 'loader_delete'):
            allowed_to_delete = True

    except:
        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'unlock'
        items_to_configure[item_count]['location'] = '/dev/shm/config_unlock.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = '(expires after 3 minutes)'  # gets put in the description column
        items_to_configure[item_count]['input_type'] = 'password'

        if get_content_for_form(items_to_configure[item_count]) == 'unlock1234':
            unlocked = True

    _ = """
    item_count += 1
    items_to_configure[item_count] = {}
    items_to_configure[item_count]['name'] = 'unlock'
    items_to_configure[item_count]['location'] = '/dev/shm/config_unlock.txt'
    items_to_configure[item_count]['storage_type'] = 'string'
    items_to_configure[item_count]['default_value'] = ''
    items_to_configure[item_count]['help'] = '(expires after 3 minutes)' # gets put in the description column
    items_to_configure[item_count]['input_type'] = 'password'

    if get_content_for_form(items_to_configure[item_count]) == 'unlock1234':
        unlocked = True
"""

    if unlocked:
        #        item_count = 0
        #        items_to_configure = {}
        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'cmd'
        items_to_configure[item_count]['location'] = '/dev/shm/loader_cmd.txt'
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = '(reload, restart, reboot)'
        items_to_configure[item_count]['input_type'] = 'text'

        server_content_is_linked = os.path.islink('/var/www/html')

        item_count += 1
        items_to_configure[item_count] = {}
        items_to_configure[item_count]['name'] = 'wsgi_code_upload'
        items_to_configure[item_count]['location'] = s_location_base
        items_to_configure[item_count]['storage_type'] = 'string'
        items_to_configure[item_count]['default_value'] = ''
        items_to_configure[item_count]['help'] = 'Use this to upload slicer_wsgi_*.py files'
        if server_content_is_linked:
            items_to_configure[item_count]['help'] += ", or a *-x.y.z.zip file of the source"
        items_to_configure[item_count]['help'] += '<br>On first single file upload, a _modified build will get created.'
        items_to_configure[item_count]['help'] += '<br>You need to manually switch to the modified build.'
        items_to_configure[item_count]['help'] += '<br>Uploads to a _modified build will be immediately available and active.'
        items_to_configure[item_count]['input_type'] = 'file_upload'


        if server_content_is_linked:
            contents = get_var_www_file_contents()
            possible_linked = extract_valid_possible_server_versions(contents)

            current_linked = get_current_linked_folder()

            _ = """
            item_count += 1
            items_to_configure[item_count] = {}
            items_to_configure[item_count]['name'] = 'linked version'
            items_to_configure[item_count]['location'] = ''
            items_to_configure[item_count]['storage_type'] = 'string'
            items_to_configure[item_count]['default_value'] = ''
            items_to_configure[item_count]['help'] = current_linked
            items_to_configure[item_count]['input_type'] = ''
            """

            item_count += 1
            items_to_configure[item_count] = {}
            items_to_configure[item_count]['name'] = 'new_link'
            items_to_configure[item_count]['location'] = s_new_link_file_name
            items_to_configure[item_count]['storage_type'] = 'string'
            items_to_configure[item_count]['default_value'] = ''
            items_to_configure[item_count]['help'] = 'Caution: This will relink and restart the web server'
            items_to_configure[item_count]['input_type'] = 'dropdown'
            items_to_configure[item_count]['dropdown'] = possible_linked
            items_to_configure[item_count]['dropdown_current'] = '' # current_linked

            item_count += 1
            items_to_configure[item_count] = {}
            items_to_configure[item_count]['name'] = 'delete_link'
            items_to_configure[item_count]['location'] = s_delete_link_file_name
            items_to_configure[item_count]['storage_type'] = 'string'
            items_to_configure[item_count]['default_value'] = ''
            items_to_configure[item_count]['dropdown_current'] = ''
            if allowed_to_delete:
                possible_delete = copy.copy(possible_linked)
                possible_delete.remove(current_linked)
                possible_delete.append('<' + current_linked + '>')
                items_to_configure[item_count]['help'] = 'Caution: This will permanently delete this link (cannot undo).<br>The current linked version is shown, but can not be deleted.'
                items_to_configure[item_count]['input_type'] = 'dropdown'
                items_to_configure[item_count]['dropdown'] = possible_delete
            else:
                items_to_configure[item_count]['help'] = '(You do not have loader delete permission)'
                items_to_configure[item_count]['input_type'] = 'none'

    return items_to_configure


# ----------------------------
def process_post_data(post_d, environ):
    # ----------------------------
    action_report = ''
    items_to_configure = get_config_list(environ)

    if 'the_selection' in post_d:
        for item in sorted(items_to_configure.keys()):
            name = items_to_configure[item]['name']
            if post_d['the_selection'] == name + '_text_set':
                if 'device_value_text' in post_d:
                    content = post_d['device_value_text'].replace('%2C', ',').replace('%7B', '{').replace('%7D', '}')
                    user_found = 'user.unknown'
                    try:
                        import login
                        user_found = str(login.get_current_user(environ))
                    except:
                        pass
                    action_report = set_content_from_form(items_to_configure[item], content, user_found)
    return action_report

# ====================================
def get_system_info():
    # ====================================
    body = ''

    try_folders = ['/dev/shm/', '/var/', '/var/www/', '/var/www/html/']

    for try_folder in try_folders:
        try:
            file_path = try_folder + 'test.jnk'
            open(file_path, 'w').write('test')
            os.remove(file_path)
            body += '<br>Folder ok' + ', ' + try_folder + '<br>'
        except:
            body += 'tagZ: ' + '<br>Folder test exception' + ', ' + str(
                traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body


def get_apache_log_summary():
    _ = """
http://blog.codeasite.com/how-do-i-find-apache-http-server-log-files/

RHEL / Red Hat / CentOS / Fedora Linux Apache error file location -> /var/log/httpd/error_log
Debian / Ubuntu Linux Apache error log file location -> /var/log/apache2/error.log
FreeBSD Apache error log file location -> /var/log/httpd-error.log
To find exact apache log file location, you can use grep command:
# grep ErrorLog /usr/local/etc/apache22/httpd.conf
# grep ErrorLog /etc/apache2/apache2.conf
# grep ErrorLog /etc/httpd/conf/httpd.conf

Lookup Error Code:


    """

    return ''


# ====================================
def make_live_table_content(load_url, table_id, url_timeout_ms):
    # ====================================
    return_value = {}

    load_command = 'loadIntoTable_' + table_id + '("' + load_url + '", document.querySelector("#' + table_id + '"));'

    return_value['head'] = """<style type="text/css">'
    table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-collapse: collapse;
        font-family: 'Quicksand', sans-serif;
        overflow: hidden;
        font-weight: bold;
    }

    table thead th {
        background: #009578;
        color: #ffffff;
    }

    table td,
    table th {
        padding: 5px 10px;
    }

    table tbody tr:nth-of-type(even) {
        background: #eeeeee;
    }

    table tbody tr:last-of-type {
        border-bottom: 2px solid #009578
    }
</style>
                """

    # load table content from js data
    # https://www.youtube.com/watch?v=qBg8IB3u28s
    # https://www.w3schools.com/jsref/dom_obj_tabledata.asp
    script_fetch_content = """
// Get first load on page load
""" + load_command + """

setInterval(async () => {

    """ + load_command + """

}, """ + str(url_timeout_ms) + """);
"""

    # build a button:
    #     https://www.google.com/search?client=safari&rls=en&q=javascript+create+cell+with+form+inside&ie=UTF-8&oe=UTF-8&safe=active
    #     https://www.w3schools.com/jsref/dom_obj_pushbutton.asp
    #     https://www.w3schools.com/jsref/dom_obj_option.asp
    #     https://www.w3schools.com/html/html_forms.asp
    #     https://jkorpela.fi/forms/extraspace.html
    return_value['javascript'] = """
<script>

document.getElementById(""" + '"' + table_id + '_' + """display_text_data").innerText = "";

async function loadIntoTable_""" + table_id + """(url, table) {
    const tableHead = table.querySelector("thead");
    const tableBody = table.querySelector("tbody");

    try {
        document.getElementById(""" + '"' + table_id + '_' + """display_text_data").innerText = "";

        var response = await fetch(url);

        var {headers, rows, links , color, help, buttons} = await response.json();

        tableHead.innerHTML = "<tr></tr>";
        tableBody.innerHTML = "";

        for (const headerText of headers) {
            const headerElement = document.createElement("th");
            headerElement.textContent = headerText;
            tableHead.querySelector("tr").appendChild(headerElement);
        }

        for (var j = 0; j < rows.length; j++) {

            const rowElement = document.createElement("tr");
            for (var i = 0; i < rows[j].length; i++) {
                const cellText = rows[j][i];
                const link = links[j][i];
                const my_button = buttons[j][i];
                const color_to_use = color[j][i];
                const cellElement = document.createElement("td");

                if (cellText=="button") {
                    const form = document.createElement("form");
                    const button = document.createElement("button");
                    const select = document.createElement("select");
                    const option = document.createElement("option");

                    form.method = "post"
                    select.name = "the_selection"
                    select.style.visibility = "hidden"

                    option.value = my_button.option
                    option.selected = true;

                    select.appendChild(option)

                    form.appendChild(select)

                    button.type = "submit"
                    button.textContent = my_button.text_name

                    form.style = "display:inline"
                    form.appendChild(button)
                    cellElement.appendChild(form)
                }
                else if (link.length > 0) {
                    var link_click = document.createElement("a");
                    link_click.className = "someCSSclass";
                    link_click.setAttribute("href", link);
                    var link_text = document.createTextNode(cellText);
                    link_click.appendChild(link_text);
                    cellElement.appendChild(link_click);
                } else {
                    cellElement.textContent = cellText;
                }

                if (color_to_use.length > 0) {
                    cellElement.style.backgroundColor = "rgba" + color_to_use;
                }
                rowElement.appendChild(cellElement);
        }
            tableBody.appendChild(rowElement);
        }

    } catch (error) {
        document.getElementById(""" + '"' + table_id + '_' + """display_text_data").innerText = "Fetch error on " + url + "<br>" + error;
    }
};

""" + script_fetch_content + """

</script>
        """

    return_value['body'] = ''
    return_value['body'] += '<center><B>'
    return_value['body'] += '<text id="' + table_id + '_' + 'display_text_data"></text>'
    return_value['body'] += '<br><br>'
    return_value['body'] += '</B></center>'

    return_value['body'] += '<center>'
    return_value['body'] += '<table id="' + table_id + '">'
    return_value['body'] += '<thead></thead>'
    return_value['body'] += '<tbody></tbody>'
    return_value['body'] += '</table>'
    return_value['body'] += '</center>'

    return return_value


# ====================================
def make_body_GET(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': False}

    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']

    get_content = ''
    current_linked = get_current_linked_folder()

    allowed_to_delete = False
    try:
        import permissions
        if permissions.permission_prefix_allowed(environ, 'loader_delete'):
            allowed_to_delete = True
    except:
        pass

    query_items = {}
    if 'QUERY_STRING' in environ:
        for item in environ['QUERY_STRING'].split(','):
            parms = item.split('=')
            if len(parms) > 1:
                query_items[parms[0]] = parms[1]
    if 'get' in query_items:
        get_content = query_items['get']

    body = ''
    try:
        if get_content == 'server_files':
            threshold = 0
            if 'threshold' in query_items:
                try:
                    threshold = int(query_items['threshold'])
                except:
                    pass

            lines = []

            lines.append(['df -k', do_one_command_content('df -k')])

            # https://serverfault.com/questions/1101205/why-are-du-b-and-du-k-different-so-much
            #  --block-size=1
            content = do_cross_comm('request', 'du --block-size=1 -h --max-depth=1 --exclude=/proc --exclude=/dev --exclude=/boot --exclude=/run --exclude=/mnt /')
            lines.append(['du /', make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'du --block-size=1 -h --max-depth=1 --exclude=/proc --exclude=/dev --exclude=/boot --exclude=/run /var')
            lines.append(['du /var', make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'du --block-size=1 -h  --max-depth=1 /var/log')
            lines.append(['du /var/log', make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'du --block-size=1 -h  --max-depth=1 /var/log/slicer')
            lines.append(['du /var/log/slicer', make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'du --block-size=1 -h  --max-depth=1 /var/log/httpd')
            lines.append(['du /var/log/httpd',make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'du --block-size=1 -h  --max-depth=1 /var/log/dynatrace')
            lines.append(['du /var/log/dynatrace', make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'get_files_and_size,/var/log/')
            lines.append(['files in /var/log',make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'get_files_and_size,/var/log/httpd/')
            lines.append(['files in /var/log/httpd',make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'get_file_content,/etc/logrotate.conf')
            report = analyze_log_rotate_conf(content) + '\n\n' + \
                str(locate_first_difference(content, s_default_analyze_httpd_log_rotate)) + \
                ' ' + str(len(content)) + \
                ' ' + str(len(s_default_analyze_etc_logrotate_conf))
            lines.append(['/etc/logrotate.conf',content, report])

            content = do_cross_comm('request', 'get_files_and_size,/etc/logrotate.d/')
            lines.append(['files in /etc/logrotate.d/',make_sorted_human(content, threshold, 1)])

            content = do_cross_comm('request', 'get_file_content,/etc/logrotate.d/httpd')
            report = analyze_httpd_log_rotate(content) + '\n\n' + \
                str(locate_first_difference(content, s_default_analyze_httpd_log_rotate)) + \
                ' ' + str(len(content)) + \
                ' ' + str(len(s_default_analyze_httpd_log_rotate))
            lines.append(['/etc/logrotate.d/httpd',content, report])

            content = do_cross_comm('request', 'get_file_content,/etc/httpd/conf/httpd.conf')
            lines.append(['/etc/httpd/conf/httpd.conf',content, strip_comments(content).replace('<','\n<').replace('>','>\n')])

            if False:
                # /sbin/service anacron status
                # Unit anacron.service could not be found.

                content = do_cross_comm('request', 'get_file_content,/etc/anacrontab').replace('\t','        ')
                lines.append(['/etc/anacrontab',content, strip_comments(content).replace('<','\n<').replace('>','>\n')])


            # Apache log settings:
            # https://betterstack.com/community/guides/logging/how-to-view-and-configure-apache-access-and-error-logs/

            columns = 0
            for line in lines:
                length = len(line)
                if length > columns:
                    columns = length


            body += '<table border="1" cellpadding="5">'
            for line in lines:
                body += '<tr>'
                for column in range(0,columns):
                    body += '<td>'
                    body += '<tt>'
                    try:
                        body += make_formatted_text(line[column])
                    except:
                        pass
                    body += '</tt>'
                    body += '</td>'
                body += '</tr>'
            body += '</table>'

        elif get_content == 'messages_log':
            try:
                body += open('/dev/shm/loader_messages_summary.txt', 'r').read()
            except:
                body += traceback.format_exc().replace("\"", "'")


        elif get_content == 'log':
            if 'filename' in query_items:
                found_file = query_items['filename']
                body += '<tt>'
                body += found_file + '<br><br>'

                content = open(s_loader_change_log_path + found_file, 'r').read()
                body += make_formatted_text(content)
                body += '</tt>'
            else:
                # http://lpec5009slicr05.cardinalhealth.net/loader?get=log
                body += '<center>Loader log report, most recent at the top:</center><br><br>'

                try:
                    body += '<center>'
                    body += '<table border="1" cellpadding="5">'
                    for found_file in sorted(os.listdir(s_loader_change_log_path), reverse=True):
                        body += '<tr>'
                        body += '<td>'

                        body += '<a href="?get=log,filename=' + found_file + '" style="text-decoration:none;color:inherit">'
                        body += found_file
                        body += '</a>'

                        body += '</td>'
                        body += '<td>'
                        body += '<tt>'
                        content = open(s_loader_change_log_path + found_file, 'r').read()
                        body += make_formatted_text(content)
                        body += '</tt>'
                        body += '</td>'
                        body += '</tr>'
                    body += '</table>'
                    body += '</center>'

                except:
                    pass

            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
            return body, other
        elif get_content == 'data':
            # http://lpec5009slicr05.cardinalhealth.net/loader?get=data,type=issues
            unlocked = False
            try:
                import permissions
                if permissions.permission_prefix_allowed(environ, 'loader_create'):
                    unlocked = True
                if permissions.permission_prefix_allowed(environ, 'loader_update'):
                    unlocked = True
                if permissions.get_is_admin(environ):
                    unlocked = True
            except:
                pass


            the_data = {'headers': [], 'rows': [], 'links': [], 'color': []}
            the_type = ''
            if 'type' in query_items:
                the_type = query_items['type']

            if the_type == 'issues_status':
                the_data = {'headers': [], 'rows': [], 'links': [], 'color': [], 'buttons': []}

                the_data['headers'] = ['', '']

                contents = get_var_www_file_contents()
                possible_linked = extract_valid_possible_server_versions(contents)

                modified_link_value_found= False

                # Row 1
                the_name_to_use = 'current_linked'
                the_value_to_use = current_linked
                the_color_to_use = ''
                if current_linked + '_modified' in possible_linked:
                    modified_link_value_found = True
                    the_color_to_use = s_rgba_yellow_light

                the_data['rows'].append([the_name_to_use, the_value_to_use])
                the_data['links'].append(['', ''])
                the_data['color'].append(['', the_color_to_use])
                the_data['buttons'].append([{}, {}])

                # ? Row 2
                if modified_link_value_found:
                    the_name_to_use = ''
                    the_value_to_use = '!! There is a _modified build link available'
                    the_color_to_use = ''

                    the_data['rows'].append([the_name_to_use, the_value_to_use])
                    the_data['links'].append(['', ''])
                    the_data['color'].append(['', the_color_to_use])
                    the_data['buttons'].append([{}, {}])

                # ? Row 3
                later_version_available = find_later_version(current_linked, possible_linked)
                if later_version_available:
                    the_name_to_use = '!! There is a later version available'
                    the_value_to_use = later_version_available
                    the_color_to_use = s_rgba_yellow_light

                    the_data['rows'].append([the_name_to_use, the_value_to_use])
                    the_data['links'].append(['', ''])
                    the_data['color'].append(['', the_color_to_use])
                    the_data['buttons'].append([{}, {}])

                # Done
                other['add_wrapper'] = False
                other['response_header'] = [('Content-type', 'application/json')]
                return json.dumps(the_data), other


            if the_type == 'issues':
                time_start = time.time()
                debug_report = ''
                live_data = loader_live_data()

                debug_report += 'a,'
                debug_report += str(time.time()-time_start) + ', '
                list_of_services_to_not_allow = get_service_settings()

                try:
                    the_data = {'headers': [], 'rows': [], 'links': [], 'color': [], 'buttons': []}

                    the_data['headers'] = ['Filename', 'loading', 'exception', 'version', 'runner', 'module', 'file', 'run status']

                    names_to_use = copy.copy(sorted(live_data.keys()))

                    debug_report += 'b,'
                    debug_report += str(time.time()-time_start) + ', '
                    for name in names_to_use:
                        startup_exception_report = '(not found)'
                        running_exception_report = ''
                        loading_report = ''
                        loading_color = ''
                        file_color = ''
                        import_report = ''
                        runner_report = '(missing)'
                        runner_color = ''

                        if '_modified' in current_linked:
                            original_filename = s_location_root + current_linked.replace('_modified','') + '/' + name + '.py'
                            if os.path.isfile(original_filename):
                                original_content = open(original_filename, 'r').read()

                                current_filename = s_location_root + current_linked + '/' + name + '.py'
                                if os.path.isfile(current_filename):
                                    current_content = open(current_filename, 'r').read()
                                    if current_content != original_content:
                                        # yellow = changed
                                        file_color = s_rgba_yellow_light
                                else:
                                    # purple = current missing?
                                    file_color = s_rgba_purple_light
                            else:
                                # green: does not exist in the original
                                file_color = s_rgba_green_light

                        startup_ex_filename = 'startup_exceptions_' + name
                        running_ex_filename = 'running_exceptions_' + name
                        if 'startup_exceptions_' in live_data[name]:
                            startup_exception_report = live_data[name]['startup_exceptions_']
                        if 'running_exceptions_' in live_data[name]:
                            running_exception_report = live_data[name]['running_exceptions_']

                        is_loading = False
                        is_loaded_now = False
                        if 'loading' in live_data[name]:
                            if live_data[name]['loading']:
                                is_loading = True
                        if 'loaded' in live_data[name]:
                            if live_data[name]['loaded']:
                                is_loaded_now = True

                        if is_loading:
                            loading_report = 'LOADING...'
                            loading_color = s_rgba_yellow_light
                        elif is_loaded_now:
                            loading_report = 'loaded'
                        else:
                            loading_report = 'missing'
                            loading_color = s_rgba_red_light

                        if 'import_report' in live_data[name]:
                            import_report = live_data[name]['import_report']['text']
                            import_color = live_data[name]['import_report']['color']

                        if 'runner_report' in live_data[name]:
                            runner_report = live_data[name]['runner_report']['text']
                            runner_color = live_data[name]['runner_report']['color']

                        # -------------------------------
                        text_name = 'allowed'
                        runner_button_color = ''
                        if name in list_of_services_to_not_allow:
                            text_name = 'disabled'
                            runner_button_color = s_rgba_red_light

                            if runner_color:
                                # if failed, and disabled, then that is what is wanted
                                runner_color = s_rgba_purple_bright
                                runner_button_color = runner_color
                                runner_report = '(stopped)'

                        disable_runner_content = {'text_name':text_name, 'option':'disable_option_value_' + name}

                        if unlocked:
                            disable_runner_button_text = 'button'
                        else:
                            disable_runner_button_text = str(text_name)

                        if name == 'loader':
                            disable_runner_button_text = ''

                        # -------------------------------
                        text_name = 'delete'
                        delete_file_content = {'text_name':text_name, 'option':'delete_option_value_' + name}
                        delete_file_button_text = ''

                        if allowed_to_delete:
                            delete_file_button_text = 'button'
                        else:
                            delete_file_button_text = ''

                        # -------------------------------
                        the_data['rows'].append(
                            [name,
                             loading_report,
                             startup_exception_report,
                             import_report,
                             runner_report,
                             disable_runner_button_text,
                             delete_file_button_text,
                             running_exception_report])
                        the_data['links'].append(['', '', '', '', '', '', '', ''])
                        the_data['color'].append([file_color, loading_color, '', import_color, runner_color, runner_button_color,'', ''])
                        the_data['buttons'].append([{}, {}, {}, {}, {}, disable_runner_content, delete_file_content, {}])

                        debug_report += str(time.time()-time_start) + ', '


                    if False:
                        pre_dump = json.dumps(the_data)
                        debug_report += 'c,'
                        debug_report += str(time.time()-time_start) + ', '


                        the_data['rows'].append(
                            ['debug',
                             debug_report,
                             '',
                             '',
                             '',
                             '',
                             '',
                             ''])
                        the_data['links'].append(['', '', '', '', '', '', '', ''])
                        the_data['color'].append(['', '', '', '', '', '','', ''])
                        the_data['buttons'].append([{}, {}, {}, {}, {}, '', '', {}])

                except:
                    the_data = {'headers': [], 'rows': [], 'links': [], 'color': [], 'buttons': []}

                    the_data['headers'] = ['Exception']
                    the_data['rows'].append(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))
                    the_data['links'].append([''])
                    the_data['color'].append([''])
                    the_data['buttons'].append([{}])


                other['add_wrapper'] = False
                other['response_header'] = [('Content-type', 'application/json')]
                return json.dumps(the_data), other
            else:
                # echo it back out, so that we can see it
                for key in query_items.keys():
                    the_data['headers'].append(key)
                    the_data['rows'].append([query_items[key]])

                other['add_wrapper'] = False
                other['response_header'] = [('Content-type', 'application/json')]
                return json.dumps(the_data), other
        else:
            # main page
            other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

            load_url = home_url + '/loader?get=data,type=issues'
            load_status_url = home_url + '/loader?get=data,type=issues_status'
            log_url = home_url + '/loader?get=log'
            log_messages_url = home_url + '/loader?get=messages_log'
            server_files_url = home_url + '/loader?get=server_files,threshold=1'

            live_table_status_d = make_live_table_content(load_status_url, table_id='live_status_table', url_timeout_ms=10000)
            live_table_content_d = make_live_table_content(load_url, table_id='live_data_table', url_timeout_ms=5000)

            # only need one table worth's here
            other['head'] = live_table_content_d['head']

            live_data = loader_live_data()
            items_to_configure = get_config_list(environ)

            body = ''

            body += """
        <script>

        function URLjump(jumpLocation) {
            location.href = jumpLocation;
        }

        </script>
            """

            #    name_to_show = "Home"
            #    url_to_use = "https://slicer.cardinalhealth.net"
            #   onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
            #    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

            body += '<center>'
            body += version
            body += '<br><br>'
            body += 'host: ' + s_host_name
            body += '<br><br>'

#            body += 'user: ' +  str(os.getuid()) + ', ' + getpass.getuser()
#            body += '<br><br>'

            try:
                import login
                the_who = login.get_current_user(environ)
                if the_who:
                    body += 'logged in: ' +  the_who
                else:
                    body += '(not logged in)'
                body += '<br><br>'
            except:
                pass
            body += "<B>Use this page to upload slicer_ files</B><br>(settings_XYZ, organization, index (wait for restart), (all as a batch; allow refreshing until all installing prompts disappear)"
            body += '<br><br>'
            body += "<B>After loading, ask for a restart, to get them loaded completely.</B>"
            body += '<br><br>'

            body += ' ' + '<a href="' + log_messages_url + '">' + '(open /var/log/messages summary report page)' + '</a>'
            body += '<br><br>'
            body += ' ' + '<a href="' + log_url + '">' + '(open loader log report page)' + '</a>'
            body += '<br><br>'

            body += ' ' + '<a href="' + server_files_url + '">' + '(open server files report page)' + '</a>'
            body += '<br><br>'


            try:
                if items_to_configure:
                    body += '<table border="1" cellpadding="5">'
                    row = '<tr><td>Name</td><td>Description</td><td>Current Value</td><td>Edit value</td><td>Click to save</td></tr>'
                    body += row

                for item in sorted(items_to_configure.keys()):
                    configure_item = items_to_configure[item]

                    input_type = configure_item['input_type']
                    name = configure_item['name']

                    content = get_content_for_form(items_to_configure[item])
                    current_value = ''
                    help_value = ''
                    if configure_item['storage_type'] == 'json':
                        current_value = json.dumps(content)
                    if configure_item['storage_type'] == 'string':
                        current_value = content
                    if 'help' in configure_item:
                        help_value = configure_item['help']

                    row = ''
                    row += '<tr>'
                    row += '<td>'
                    row += name
                    row += '</td>'
                    row += '<td>'
                    row += help_value
                    row += '</td>'
                    row += '<td>'
                    if input_type == 'password':
                        row += "..."
                    else:
                        row += current_value.replace('"', "'")
                    row += '</td>'

                    if input_type == 'file_upload':
                        row += '<form id="upload" name="upload" method=post enctype=multipart/form-data>'
                    else:
                        row += '<form method="post" action="">'

                    row += '<td>'

                    row += '<select name="the_selection" id="the_selection" hidden>'
                    row += '<option value="' + name + '_text_set" selected>' + 'dummy' + '</option>'
                    row += '</select>'

                    show_submit = True
                    if input_type == 'text':
                        row += """<input type="text" size=""" + str(
                            len(current_value) + 10) + """ name="device_value_text" value=\"""" + current_value.replace(
                            '"', "'") + """\">"""
                    elif input_type == 'password':
                        row += """<input type="password" size=""" + str(
                            len(current_value) + 10) + """ name="device_value_text" value=\"""" + current_value.replace(
                            '"', "'") + """\">"""
                    elif input_type == 'textarea':
                        row += """<textarea rows=5 cols = 40 name="device_value_text" >""" + current_value.replace('"',
                                                                                                                   "'") + """</textarea>"""
                    elif input_type == 'dropdown':
                        row += '<select name="' + 'device_value_text' + '" id="' + 'dropdown' + '">'

                        # pad the top with one blank item
                        option_value = ''
                        row +=  '<option value="' + option_value + '"' + '">' + option_value + '</option>'
                        for option_value in configure_item['dropdown']:
                            if option_value == configure_item['dropdown_current']:
                                row +=  '<option value="' + option_value + '"' + '" selected>' + option_value + '</option>'
                            else:
                                row +=  '<option value="' + option_value + '"' + '">' + option_value + '</option>'
                        row += '</select>'

                    elif input_type == 'file_upload':
                        row += """<input type=file name=file multiple="multiple">"""
                    elif input_type == 'none':
                        show_submit = False


                    row += '</td>'

                    row += '<td>'
                    if show_submit:
                        row += '<input type="submit" value="Submit">'
                    row += '</td>'
                    row += '</form>'

                    row += '</tr>' + '\n'
                    body += row


            except Exception as e:
                body = 'tagA: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

            if items_to_configure:
                body += '</table>'
            body += '</center>'

            # ------------------------------------
            # Live dashboard view(s)
            # ------------------------------------
            dashboard = ''

            dashboard += live_table_status_d['body']
            dashboard += live_table_status_d['javascript']

            dashboard += live_table_content_d['body']
            dashboard += live_table_content_d['javascript']

            body += dashboard

            body += '<center>'
            body += '<br><br>'
            body += 'When you are in a _modified build:<br>'
            body += 'Filenames in yellow have content that is modified from the primary build file content.<br>'
            body += 'Filenames in green do not exist in the primary build.<br>'
            body += 'Filenames in purple do not exist in the modified build.<br>'
            body += '</center>'



    except Exception as e:
        other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
        body = 'tagC: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other

# ====================================
def get_service_settings():
    # ====================================
    list_of_services_to_not_allow = []
    try:
        import datastore
        data_store_content = datastore.all_datastore()

        for key in data_store_content.keys():
            if 'service_loader_' in key:
                the_service = key.replace('service_loader_','').split('_')[0]

                the_key = 'service_loader_' + the_service + '_allow_runner'
                the_value = datastore.get_value_stored(data_store_content, the_key)
                if the_value == 'no':
                    list_of_services_to_not_allow.append(the_service)
    except:
        pass

    return list_of_services_to_not_allow

# ====================================
def make_not_allow_list_from_datastore(data_store_content):
    # ====================================
    list_of_services_to_not_allow = []
    for key in data_store_content.keys():
        if 'service_loader_' in key:
            the_service = key.replace('service_loader_','').split('_')[0]
            the_key = 'service_loader_' + the_service + '_allow_runner'
            if the_key in data_store_content:
                the_value = data_store_content[the_key]
                if the_value == 'no':
                    list_of_services_to_not_allow.append(the_service)
    return list_of_services_to_not_allow

# ====================================
def make_body_POST(environ):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}
    action_report = ''

    # do work on content
    body = 'start upload'

    replacers = []
    replacers.append(('%5B', '['))
    replacers.append(('%5D', ']'))
    replacers.append(('%27', "'"))
    replacers.append(('%3A', ':'))
    replacers.append(('%2F', '/'))
    replacers.append(('+', ' '))
    replacers.append(('%40', '@'))
    replacers.append(('%24', '$'))
    replacers.append(('%25', '%'))
    replacers.append(('%21', '!'))
    replacers.append(('%23', '#'))

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

    body = 'upload read'

    # text inputs (like cmd)
    try:
        the_who = ''
        try:
            import login
            the_who = login.get_current_user(environ)
        except:
            pass

        the_selection = field_storage.getvalue('the_selection')
        if action_report:
            action_report += '\n'
        action_report += 'the_selection: ' + '"' + the_selection + '"'

        if 'disable_option_value_' in the_selection:
            the_service = the_selection.replace('disable_option_value_','')
            data_store_value_name = 'service_loader_' + the_service + '_allow_runner'
            previous_value = datastore.get_value(data_store_value_name)
            if previous_value == 'no':
                value_to_use = 'yes'
                datastore.set_value(data_store_value_name, value_to_use)
                # get our runner to handle the change
                do_atomic_write_if_different('/var/www/loader_quick_work/loader_runner_start_' + the_service,
                                             the_service)

                # put this action into the loader log
                save_to_loader_change_log('selected_action', 'requested start: ' + the_service, the_who)
            else:
                value_to_use = 'no'
                datastore.set_value(data_store_value_name, value_to_use)
                # get our runner to handle the change
                do_atomic_write_if_different('/var/www/loader_quick_work/loader_runner_stop_' + the_service,
                                             the_service)

                # put this action into the loader log
                save_to_loader_change_log('selected_action', 'requested stop: ' + the_service, the_who)

            if action_report:
                action_report += '\n'
            action_report += 'setting ' + data_store_value_name + ' from ' + '"' + previous_value + '"' + ' to ' + '"' + value_to_use + '"'

        elif 'delete_option_value_' in the_selection:
            the_service = the_selection.replace('delete_option_value_','')

            if not '<' in the_service: # ??? what was this protecting against?
                file_location = '/var/www/loader_quick_work/loader_service_delete_' + the_service
                do_atomic_write_if_different(file_location, the_service)

                if action_report:
                    action_report += '\n'
                action_report = 'set ' + '"' + file_location + '"' + ' to ' + '"' + the_service + '"'

                # put this action into the loader log
                save_to_loader_change_log('selected_action', 'requested delete: ' + the_service, the_who)

        else:
            # All the block of text, or file uploads, in the upper block
            content_found = field_storage.getvalue('device_value_text')
            for replacer in replacers:
                content_found = content_found.replace(replacer[0], replacer[1])
            post_d = {}
            post_d['the_selection'] = the_selection
            post_d['device_value_text'] = content_found

            # put this action into the loader log
            save_to_loader_change_log('selected_action', 'requested: ' + the_selection + ', to be: ' + content_found, the_who)

            # process the posted data
            if action_report:
                action_report += '\n'
            action_report += process_post_data(post_d, environ)

    except:
        pass

    # file uploads
    try:
        if len(field_storage.list):
            the_selection = field_storage.getvalue('the_selection')

            items_to_configure = get_config_list(environ)
            for config_item in sorted(items_to_configure.keys()):
                name = items_to_configure[config_item]['name']
                if the_selection == name + '_text_set':
                    upload_service_content = {}
                    # --------------
                    # get content
                    # --------------
                    for item in field_storage.list:
                        filename = ''
                        if item.filename:
                            filename = item.filename

                        if ('slicer_wsgi_' in filename) and ('.py' == filename[-3:]):
                            upload_service_content[filename] = item.file.read().decode('utf-8')
                        if ('-' in filename) and ('.zip' == filename[-4:]): # cs_sp_pi_slicer-2.1.1.zip
                            upload_service_content[filename] = item.file.read()


                    # --------------
                    # restrict content in a batch upload
                    # --------------
                    if len(upload_service_content.keys()) > 1:
                        files_to_remove = []
                        for filename in upload_service_content.keys():
                            if 'index' in filename:
                                files_to_remove.append(filename)
                            if 'loader' in filename:
                                files_to_remove.append(filename)
                        for filename in files_to_remove:
                            del upload_service_content[filename]

                    # --------------
                    # look for multiple settings files
                    # --------------
                    files_for_organization = []
                    for filename in upload_service_content.keys():
                        if 'settings' in filename:
                            files_for_organization.append(filename)
                    if len(files_for_organization) > 1:
                        for filename in files_for_organization:
                            del upload_service_content[filename]

                    # --------------
                    # put content
                    # --------------
                    needs_module_restart = False
                    for filename in upload_service_content.keys():
                        if ('slicer_wsgi_' in filename) and ('.py' == filename[-3:]):
                            current_linked = get_current_linked_folder()

                            module_name = get_module_name_from_filename(filename)
                            if not '_modified' in current_linked:

                                current_live_content_folder = items_to_configure[config_item]['location']
                                output_folder = s_location_root + current_linked + '_modified' + '/'

                                # any time we start in an unmodified one, always do a fresh duplication
                                duplication_log_content = 'Making _modified folder to put copy of content: copy from ' + current_live_content_folder + ', to ' + output_folder + '\n'
                                try:
                                    os.remove(output_folder)
                                except:
                                    pass
                                if not os.path.exists(os.path.dirname(output_folder)):
                                    os.makedirs(os.path.dirname(output_folder))

                                for file_to_copy in os.listdir(current_live_content_folder):
                                    source = current_live_content_folder + file_to_copy
                                    destination = output_folder + file_to_copy
                                    try:
                                        if '__pycache__' in source:
                                            duplication_log_content += 'skipping copy from ' + source + ' to ' + destination + '\n'
                                        else:
                                            if os.path.isdir(source):
                                                # like the site folder
                                                try:
                                                    shutil.rmtree(destination)
                                                except:
                                                    pass
                                                shutil.copytree(source, destination)
                                            else:
                                                shutil.copyfile(source, destination)
                                            duplication_log_content += 'copied from ' + source + ' to ' + destination + '\n'
                                    except:
                                        duplication_log_content += 'failed copy from ' + source + ' to ' + destination + '\n'
                                        duplication_log_content += '---------------------------------------------------------------------' + '\n'
                                        duplication_log_content += str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")) + '\n'
                                        duplication_log_content += '---------------------------------------------------------------------' + '\n'
                                save_to_loader_change_log('single_upload', duplication_log_content, the_who)

                            else:
                                output_folder = items_to_configure[config_item]['location']
                                needs_module_restart = True

                            output_file = output_folder + module_name + '.py'

                            if not os.path.exists(os.path.dirname(output_file)):
                                os.makedirs(os.path.dirname(output_file))

                            # write the wsgi file
                            output_file_content = upload_service_content[filename]
                            with open(output_file, 'w') as f:
                                f.write(output_file_content)

                            if action_report:
                                action_report += '\n'
                            action_report = 'uploaded ' + '"' + filename + '"' + ' to ' + '"' + output_file + '"'

                            # put this receipt into the loader log
                            the_who = ''
                            try:
                                import login
                                the_who = login.get_current_user(environ)
                            except:
                                pass
                            save_to_loader_change_log('single_upload', 'uploaded py file: ' + filename + ', saved as: ' + output_file, the_who)

                        if ('-' in filename) and ('.zip' == filename[-4:]):
                            version_zip_string = get_zip_version_string_from_filename(filename)
                            output_file = s_location_version_zips + version_zip_string + '.zip'
                            if not os.path.exists(os.path.dirname(output_file)):
                                os.makedirs(os.path.dirname(output_file))
                            output_file_content = upload_service_content[filename]
                            with open(output_file, 'wb') as f:
                                f.write(output_file_content)

                            # leave a marker file, so the main loop runner does the work
                            # 2.1.0.zip.needunzip
                            with open(output_file + '.needunzip', 'w') as f:
                                f.write('yes')

                            if action_report:
                                action_report += '\n'
                            action_report = 'uploaded ' + '"' + filename + '"' + ' to ' + '"' + output_file + '"'

                            # put this receipt into the loader log
                            the_who = ''
                            try:
                                import login
                                the_who = login.get_current_user(environ)
                                body += 'logged in: ' +  the_who
                                body += '<br><br>'
                            except:
                                pass
                            save_to_loader_change_log('zip_upload', 'uploaded zip file: ' + filename + ', saved as: ' + output_file, the_who)

                    # --------------
                    # start up the runners for this content
                    # --------------
                    for filename in upload_service_content.keys():
                        if ('.py' in filename) and needs_module_restart:
                            module_name = get_module_name_from_filename(filename)

                            # get our runner to set it up
                            if True:
                                do_atomic_write_if_different('/var/www/loader_quick_work/loader_quick_work_' + module_name,
                                                             module_name)
                            else:
                                do_atomic_write_if_different('/dev/shm/loader_quick_work_' + module_name, module_name)

                    upload_service_content = None
    except:
        body += 'tagD: ' + '<br>File upload exception' + ', ' + str(
            traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        return body, other

    # then return what GET would have done
    body, other = make_body_GET(environ)
    other['action_report'] = action_report
    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    #    allowed = permissions.permission_prefix_allowed(environ, 'loader_') or permissions.permission_prefix_allowed(environ, 'development_')

    allowed = True

    if allowed:
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            try:
                import permissions
                permissions.log_page_allowed(environ, service, other)
            except:
                pass
        except:
            body = 'tagE: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    response_header = [('Content-type', 'text/html')]

    html = ''
    try:
        body, other = make_body(environ)
        status = other['status']
        head = ''
        if 'head' in other:
            head = other['head']
        response_header = other['response_header']
        if other['add_wrapper']:
            html += '<html>\n'
            if head:
                html += '<head>\n'
                html += head
                html += '</head>\n'
            html += '<body>\n'
        html += body
        if other['add_wrapper']:
            html += '</body>\n'
            html += '</html>\n'

    except:
        html += '<html>\n' \
                '<body>\n'
        html += str(sys.version_info)
        html += '<br><br>'

        html += 'tagF: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
        html += '</body>\n' \
                '</html>\n'
    try:
        import organization
        html = organization.wrap_page_with_session(environ, html)
        start_response(status, response_header)
    except:
        # allow non wrapped response
        start_response(status, response_header)

    return [html.encode()]


# ====================================
def run_an_item(item):
    # ====================================
    import pexpect
    print('run_an_item(item)', item)

    time_start = time.time()

    time_out = 60
    if 'time_out' in item:
        time_out = item['time_out']

    return_value = {'success': False, 'reason': ''}

    # https://linuxhint.com/use-pexpect-python/
    # https://opensource.apple.com/source/lldb/lldb-112/test/pexpect-2.4/doc/index.template.html

    if 'filematches' in item:
        try:
            open(item['filematches'], 'w').write('\n' + item['content'])
            return_value = {'success': True, 'reason': str(0)}
        except:
            return_value = {'success': False, 'reason': str(2),
                            'trace': str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))}

    if 'filecontains' in item:
        # if the file does not contain this content, then append it to the end
        does_it_contain_the_content = False
        try:
            existing_content = open(item['filecontains'], 'r').read()
            if item['content'] in existing_content:
                does_it_contain_the_content = True
        except:
            pass

        if not does_it_contain_the_content:
            try:
                open(item['filecontains'], 'a').write('\n' + item['content'])
                return_value = {'success': True, 'reason': str(0)}
            except:
                return_value = {'success': False, 'reason': str(2),
                                'trace': str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))}
        else:
            return_value = {'success': True, 'reason': str(1)}

    if 'fileout' in item:
        try:
            open(item['fileout'], 'w').write(item['content'])
            return_value = {'success': True, 'reason': str(0)}
        except:
            return_value = {'success': False, 'reason': str(2)}

    if 'command' in item:
        try:
            child = pexpect.spawn(item['command'], timeout=time_out)

            # wait for it to complete
            # child.expect(pexpect.EOF)
            code = child.expect([item['expect']['new'], item['expect']['existing'], item['expect']['fail']])
            print('code', code)

            content = child.before
            print('+++++++++++++++++++++++++')
            print('content', content)
            print('+++++++++++++++++++++++++')

            if code in [0, 1]:
                return_value = {'success': True, 'reason': str(code)}



        except pexpect.exceptions.TIMEOUT:
            return_value = {'success': False, 'reason': 'timeout'}

            content = child.before
            print('!!!!!!!!!!!!!!!!!!!!!!!!!')
            print('content', content)
            print('!!!!!!!!!!!!!!!!!!!!!!!!!')

    time_end = time.time()

    print('time_delta', time_end - time_start)

    return_value['name'] = item['name']

    return_value['time_delta'] = time_end - time_start

    return return_value


# ====================================
def build_runner_thread_content_for_module(module_name, params={}):
    # ====================================
    content = []
    build_runner_for_linked = False
    if 'build_runner_for_linked' in params:
        build_runner_for_linked = params['build_runner_for_linked']

    # ----------------------------------
    file_desc = 'starter'
    file_name = s_location_base + module_name + '-runner'
    file_text = ''
    file_text += '#!/usr/bin/env python3' + '\n'
    file_text += 'import ' + module_name + '\n'
    file_text += module_name + '.main()' + '\n'
    file_cmds = ['chmod +x ' + file_name]

    content.append({'desc': file_desc, 'file': file_name, 'content': file_text, 'commands': file_cmds})

    # ----------------------------------
    file_desc = 'service'
    file_name = '/lib/systemd/system/' + module_name + '-runner.service'
    file_text = """[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=""" + s_location_base + module_name + """-runner
WorkingDirectory=""" + s_location_base + """
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
"""
    if build_runner_for_linked:
        # new as of 2024.10.17
        file_cmds = []
    else:
        # original way
        file_cmds = ['systemctl daemon-reload',
                     'systemctl enable ' + module_name + '-runner.service',
                     'systemctl restart ' + module_name + '-runner.service',
                     ]

    content.append({'desc': file_desc, 'file': file_name, 'content': file_text, 'commands': file_cmds})

    # ----------------------------------

    return content


# ====================================
def get_os_from_release(release):
    # ====================================
    return_value = ''

    for item in release.split('\n'):
        splits = item.split('=')
        if len(splits) > 1:
            if splits[0] == 'ID':
                return_value = splits[1].replace('"', '')

    return return_value


# ====================================
def get_items_to_run_OS(os_name):
    # ====================================
    items_to_run = []
    if os_name == 'ubuntu':
        items_to_run = get_items_to_run_ubuntu()

    if os_name == 'rocky':
        items_to_run = get_items_to_run_rocky()

    return items_to_run


# ====================================
def get_items_to_run_rocky():
    # ====================================
    import pexpect

    apache_user_name = get_apache_user_name()

    items_to_run = []

    #    items_to_run.append({'name':'test pass', 'command':'sleep 5', 'time_out':10, 'expect':{'new':pexpect.EOF,'existing':'existing','fail':'fail'}})

    #    items_to_run.append({'name':'test timeout', 'command':'sleep 20', 'time_out':10, 'expect':{'new':'','existing':'','fail':''}})

    #    items_to_run.append({'name':'test index',
    #                         'command':'date',
    #                         'time_out':10,
    #                         'expect':{'new':pexpect.EOF,'existing':'UTC','fail':'fail'}})

    items_to_run.append({'name': 'update',
                         'command': 'dnf update -y',
                         'time_out': 520,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'mod_ssl install',
                         'command': 'dnf install -y mod_ssl',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'apache install',
                         'command': 'dnf install -y httpd',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'apache start',
                         'command': 'systemctl start httpd',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'apache enable',
                         'command': 'systemctl enable httpd',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    # https://linuxconfig.org/how-to-open-http-port-80-on-redhat-7-linux-using-firewall-cmd
    items_to_run.append({'name': 'apache through firewall',
                         'command': 'firewall-cmd --zone=public --add-port=80/tcp --permanent',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Warning: ALREADY_ENABLED: 80:tcp',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'firewall reload',
                         'command': 'firewall-cmd --reload',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Warning: ALREADY_ENABLED: 80:tcp',
                                    'fail': 'Unable to locate package'}})

    # https://modwsgi.readthedocs.io/en/master/user-guides/checking-your-installation.html
    items_to_run.append({'name': 'mod-wsgi',
                         'command': 'dnf -y install python3-mod_wsgi',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    # sudo dnf install -y gcc

    #    items_to_run.append({'name':'ldap',
    #                         'command':'python3 -m pip install python3-ldap',
    #                         'time_out':120,
    #                         'expect':{'new':pexpect.EOF,
    #                                   'existing':'Requirement already satisfied:.',
    #                                   'fail':'Unable to locate package'}})

    items_to_run.append({'name': 'ldap-cmdln',
                         'command': 'dnf install -y openldap-clients',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    # -----------------------------
    # Install for VirtualBox Rocky8 was fine with pip method
    #
    # CAH GCP Rocky8 required the dfn method (on top of pip)
    #
    # Not sure if Virtual box Rocky8 is ok with both
    # -----------------------------

    #
    items_to_run.append({'name': 'numpy by pip',
                         'command': 'python3 -m pip install numpy',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    items_to_run.append({'name': 'numpy by dnf',
                         'command': 'dnf install -y python3-numpy',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    #
    items_to_run.append({'name': 'pytz by pip',
                         'command': 'python3 -m pip install pytz',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    items_to_run.append({'name': 'pytz by dnf',
                         'command': 'dnf install -y python3-pytz',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    #
    items_to_run.append({'name': 'requests by pip',
                         'command': 'python3 -m pip install requests',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    items_to_run.append({'name': 'requests by dnf',
                         'command': 'dnf install -y python3-requests',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    # -----------------------------
    # -----------------------------

    items_to_run.append({'name': 'scipy',
                         'command': 'python3 -m pip install scipy',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    items_to_run.append({'name': 'tornado',
                         'command': 'python3 -m pip install tornado',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    items_to_run.append({'name': 'nmap',
                         'command': 'dnf install -y nmap',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Nothing to do.',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'html permissions',
                         'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + s_location_base,
                         'time_out': 60,
                         'expect': {'new': pexpect.EOF,
                                    'existing': '(none)',
                                    'fail': '(none)'}})

    items_to_run.append({'name': 'html permissions conf',
                         'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' /etc/httpd/conf/httpd.conf',
                         'time_out': 60,
                         'expect': {'new': pexpect.EOF,
                                    'existing': '(none)',
                                    'fail': '(none)'}})

    # 403 error
    # ls -lZ /var/www/html/*.py

    # -rw-r--r--. 1 <USER> <GROUP> unconfined_u:object_r:var_t:s0               65066 Dec 27 14:00 /var/www/html/loader.py
    # -rw-r--r--. 1 <USER> <GROUP> unconfined_u:object_r:httpd_sys_content_t:s0 65066 Dec 27 14:03 /var/www/html/save.py

    # loader.py created before apache installed, which means it does not know about httpd_sys_content_t

    # https://manpages.ubuntu.com/manpages/bionic/man8/httpd_selinux.8.html
    # https://unix.stackexchange.com/questions/203326/how-to-understand-the-security-context-in-ls-z

    # sudo chcon -t httpd_sys_content_t /var/www/html/loader.py

    items_to_run.append({'name': 'httpd security context for loader',
                         'command': 'chcon -t httpd_sys_content_t /var/www/html/loader.py',
                         'time_out': 60,
                         'expect': {'new': pexpect.EOF,
                                    'existing': '(none)',
                                    'fail': '(none)'}})

    items_to_run.append({'name': 'h2 by pip',
                         'command': 'python3 -m pip install h2',
                         'time_out': 240,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Requirement already satisfied:',
                                    'fail': 'ERROR'}})

    return items_to_run


# ====================================
def get_items_to_run_ubuntu():
    # ====================================
    import pexpect

    apache_user_name = get_apache_user_name()

    items_to_run = []

    #    items_to_run.append({'name':'test pass', 'command':'sleep 5', 'time_out':10, 'expect':{'new':pexpect.EOF,'existing':'existing','fail':'fail'}})

    #    items_to_run.append({'name':'test timeout', 'command':'sleep 20', 'time_out':10, 'expect':{'new':'','existing':'','fail':''}})

    #    items_to_run.append({'name':'test index',
    #                         'command':'date',
    #                         'time_out':10,
    #                         'expect':{'new':pexpect.EOF,'existing':'UTC','fail':'fail'}})

    items_to_run.append({'name': 'update',
                         'command': 'apt update',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'remove needrestart',
                         'command': 'apt -y remove needrestart',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'apache',
                         'command': 'apt install -y apache2 apache2-utils ssl-cert',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'firewall',
                         'command': 'ufw allow "Apache Full"',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'Skipping adding existing rule',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'mod-wsgi',
                         'command': 'apt install -y libapache2-mod-wsgi-py3',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'ldap',
                         'command': 'apt install -y python3-ldap',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'ldap-cmdln',
                         'command': 'apt install -y ldap-utils',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'numpy',
                         'command': 'apt install -y python3-numpy',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'scipy',
                         'command': 'apt install -y python3-scipy',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'tornado',
                         'command': 'apt install -y python3-tornado',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'nmap',
                         'command': 'apt install -y nmap',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    items_to_run.append({'name': 'html permissions',
                         'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' ' + s_location_base,
                         'time_out': 60,
                         'expect': {'new': pexpect.EOF,
                                    'existing': '(none)',
                                    'fail': '(none)'}})

    items_to_run.append({'name': 'html permissions conf',
                         'command': 'chown -R ' + apache_user_name + ':' + apache_user_name + ' /etc/apache2/conf-available',
                         'time_out': 60,
                         'expect': {'new': pexpect.EOF,
                                    'existing': '(none)',
                                    'fail': '(none)'}})

    items_to_run.append({'name': 'a2enmod ssl',
                         'command': 'a2enmod ssl',
                         'time_out': 30,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'none',
                                    'fail': 'none'}})

    items_to_run.append({'name': 'apache restart',
                         'command': 'systemctl restart apache2',
                         'time_out': 30,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'none',
                                    'fail': 'none'}})

    items_to_run.append({'name': 'h2',
                         'command': 'apt install -y python3-h2',
                         'time_out': 120,
                         'expect': {'new': pexpect.EOF,
                                    'existing': 'is already the newest version',
                                    'fail': 'Unable to locate package'}})

    return items_to_run


# ====================================
def get_os_name():
    # ====================================
    os_release = open('/etc/os-release', 'r').read()
    os_name = get_os_from_release(os_release)

    return os_name


# ----------------------------
def get_apache_user_name():
    # ----------------------------
    os_name = get_os_name()

    apache_user_name = 'apache-user-missing'

    if os_name == 'ubuntu':
        apache_user_name = 'www-data'

    if os_name == 'rocky':
        apache_user_name = 'apache'

    return apache_user_name


# ====================================
def install_os_needed_content():
    # ====================================

    time_start = time.time()
    print('Do the install ...')

    os_name = get_os_name()

    items_to_run = get_items_to_run_OS(os_name)

    if items_to_run:
        print('Do install of system libraries...')
        # http://www.bx.psu.edu/~nate/pexpect/pexpect.html#:~:text=Pexpect%20is%20a%20Python%20module,package%20installations%20on%20different%20servers.

        reports = run_items(items_to_run)

        # -------------------
        # Loader Service in Apache
        # -------------------
        module_name = service
        reports = setup_module(os_name, module_name, reports)

        print('--------------------')
        print('\n\n\n')

        for report in reports:
            print(report)

            if not report['success']:
                print('!!!!!!!!!!!!!!!!!!!!!!')
                print('!!!!!!!!!!!!!!!!!!!!!!')
                print('Failed')
                print('!!!!!!!!!!!!!!!!!!!!!!')
                print('!!!!!!!!!!!!!!!!!!!!!!')

        print('--------------------')
        print('\n\n\n')
    else:
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('OS not yet handled: ' + os_name)
        print('!!!!!!!!!!!!!!!!!!!!!!!')
        print('!!!!!!!!!!!!!!!!!!!!!!!')

    time_end = time.time()
    print('total time ' + str(int(time_end - time_start)) + ' seconds')


# ====================================
def main_interactive():
    # ====================================

    if len(sys.argv) > 1:
        for arg in sys.argv:
            print(arg)
            if arg == 'install':
                install_os_needed_content()
    else:
        print('usage: sudo python3 loader.py install')


# ====================================
if __name__ == '__main__':
    # ====================================
    main_interactive()

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_loader(self):
        """
        (fill in here)
        """
        expected = True
        actual = True
        self.assertEqual(expected, actual)

    def test_module_name_from_filename(self):
        filename = "slicer_wsgi_organization_cah.py"
        expected = "organization"
        actual = get_module_name_from_filename(filename)
        self.assertEqual(expected, actual)

        filename = "slicer_wsgi_organization.py"
        expected = "organization"
        actual = get_module_name_from_filename(filename)
        self.assertEqual(expected, actual)

    def test_get_cert_files_from_apache_config(self):
        content = """<VirtualHost *:443>
ServerName slicer.world
DocumentRoot /var/www/htmlfiles
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
</VirtualHost>
        """
        expected = {'<VirtualHost *:443>': {
            'SSLCertificateFile': '/etc/pki/tls/private/slicer.cert',
            'SSLCertificateKeyFile': '/etc/pki/tls/private/slicer.key',
        }
        }
        actual = get_cert_files_from_apache_config(content)
        self.assertEqual(expected, actual)

    def test_os_is_ubuntu(self):
        os_release = """PRETTY_NAME="Ubuntu 22.04.1 LTS"
NAME="Ubuntu"
VERSION_ID="22.04"
VERSION="22.04.1 LTS (Jammy Jellyfish)"
VERSION_CODENAME=jammy
ID=ubuntu
ID_LIKE=debian
HOME_URL="https://www.ubuntu.com/"
SUPPORT_URL="https://help.ubuntu.com/"
BUG_REPORT_URL="https://bugs.launchpad.net/ubuntu/"
PRIVACY_POLICY_URL="https://www.ubuntu.com/legal/terms-and-policies/privacy-policy"
UBUNTU_CODENAME=jammy
"""

        expected = 'ubuntu'
        actual = get_os_from_release(os_release)
        self.assertEqual(expected, actual)

    def test_os_is_rocky(self):
        os_release = """NAME="Rocky Linux"
VERSION="8.7 (Green Obsidian)"
ID="rocky"
ID_LIKE="rhel centos fedora"
VERSION_ID="8.7"
PLATFORM_ID="platform:el8"
PRETTY_NAME="Rocky Linux 8.7 (Green Obsidian)"
ANSI_COLOR="0;32"
LOGO="fedora-logo-icon"
CPE_NAME="cpe:/o:rocky:rocky:8:GA"
HOME_URL="https://rockylinux.org/"
BUG_REPORT_URL="https://bugs.rockylinux.org/"
ROCKY_SUPPORT_PRODUCT="Rocky-Linux-8"
ROCKY_SUPPORT_PRODUCT_VERSION="8.7"
REDHAT_SUPPORT_PRODUCT="Rocky Linux"
REDHAT_SUPPORT_PRODUCT_VERSION="8.7"
"""

        expected = 'rocky'
        actual = get_os_from_release(os_release)
        self.assertEqual(expected, actual)

    def test_calc_loader_live_data(self):
        # loading
        live_content_d = {'loader_quick_work_tasks': {}}
        expected = {'tasks': {'loading': True}}
        actual = calc_loader_live_data(live_content_d=live_content_d)
        self.assertEqual(expected, actual)

        live_content_d = {'loader_quick_work_tasks': {}, 'loader_quick_work_login': {}}
        expected = {'tasks': {'loading': True}, 'login': {'loading': True}}
        actual = calc_loader_live_data(live_content_d=live_content_d)
        self.assertEqual(expected, actual)

        # loaded
        location_base_content_d = {'tasks.py': {}}
        expected = {'tasks': {'loaded': True}}
        actual = calc_loader_live_data(location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        # do not show hidden swap (edit in progress) files
        location_base_content_d = {'tasks.py': {}, '.tasks2.py.swp': {}}
        expected = {'tasks': {'loaded': True}}
        actual = calc_loader_live_data(location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        location_base_content_d = {'tasks.py': {}, 'settings_lpec5009slicr05.py': {}}
        expected = {'tasks': {'loaded': True}}
        actual = calc_loader_live_data(location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        # loaded with distractions
        location_base_content_d = {'tasks.py': {}, 'test.txt': {}}
        expected = {'tasks': {'loaded': True}}
        actual = calc_loader_live_data(location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        # loading and loaded
        live_content_d = {'loader_quick_work_tasks': {}}
        location_base_content_d = {'tasks.py': {}, 'test.txt': {}}
        expected = {'tasks': {'loading': True, 'loaded': True}}
        actual = calc_loader_live_data(live_content_d=live_content_d, location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        live_content_d = {'loader_quick_work_tasks': {}, 'loader_quick_work_login': {}}
        location_base_content_d = {'tasks.py': {}, 'test.txt': {}}
        expected = {'tasks': {'loading': True, 'loaded': True}, 'login': {'loading': True}}
        actual = calc_loader_live_data(live_content_d=live_content_d, location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)

        # Files on the file system
        live_content_d = {'loader_quick_work_tasks': {}, 'loader_quick_work_login': {}}
        location_base_content_d = {'tasks.py': {}, 'test.txt': {}}
        expected = {'tasks': {'loading': True, 'loaded': True}, 'login': {'loading': True}}
        actual = calc_loader_live_data(live_content_d=live_content_d, location_base_content_d=location_base_content_d)
        self.assertEqual(expected, actual)



    def test_get_zip_version_string_from_filename(self):
        filename = 'cs_sp_pi_slicer-2.1.1.zip'
        expected = '2.1.1'
        actual = get_zip_version_string_from_filename(filename)
        self.assertEqual(expected, actual)

        filename = 'cs_sp_pi_slicer-4.1.1.zip'
        expected = '4.1.1'
        actual = get_zip_version_string_from_filename(filename)
        self.assertEqual(expected, actual)

    def test_parse_psax_all_wsgi_runners(self):
        content = """    772 ?        S      1:00 /usr/libexec/sssd/sssd_nss --uid 0 --gid 0 --logger=files
    773 ?        S      0:06 /usr/libexec/sssd/sssd_pam --uid 0 --gid 0 --logger=files
    774 ?        S      0:03 /usr/libexec/sssd/sssd_pac --uid 0 --gid 0 --logger=files
    893 ?        Ssl    7:05 /opt/dynatrace/oneagent/agent/lib64/oneagentwatchdog -bg -config=/opt/dynatrace/oneagent/agent/conf/watchdog.conf
    906 ?        Sl    81:49 oneagentos -Dcom.compuware.apm.WatchDogPort=51000 -Dcom.compuware.apm.WatchDogTimeout=900 -watchdog.restart_file_location=/va
    922 ?        Sl     6:57 oneagentextensions -Dcom.compuware.apm.WatchDogPort=51003 -Dcom.compuware.apm.WatchDogTimeout=900
    924 ?        Sl     6:42 oneagentplugin -Dcom.compuware.apm.WatchDogPort=51002 -Dcom.compuware.apm.WatchDogTimeout=900
    926 ?        Sl     6:15 oneagentnetwork -Dcom.compuware.apm.WatchDogPort=51001 -Dcom.compuware.apm.WatchDogTimeout=900
    928 ?        Sl    12:34 oneagentloganalytics -Dcom.compuware.apm.WatchDogPort=51004 -Dcom.compuware.apm.WatchDogTimeout=900
    956 ?        Ssl    0:51 /usr/sbin/NetworkManager --no-daemon
   1012 ?        Ss     0:09 python3 /var/www/html/address2location-runner
   1014 ?        Ss     0:09 python3 /var/www/html/algorithms-runner
   1016 ?        Ss     1:55 python3 /var/www/html/certificates-runner
   1020 ?        Ss     0:09 python3 /var/www/html/chart-runner"""
        expected = ['address2location-runner','algorithms-runner','certificates-runner','chart-runner']
        actual = parse_psax_all_wsgi_runners(content)
        self.assertEqual(expected, actual)

        content = """    772 ?        S      1:00 /usr/libexec/sssd/sssd_nss --uid 0 --gid 0 --logger=files
    773 ?        S      0:06 /usr/libexec/sssd/sssd_pam --uid 0 --gid 0 --logger=files
    774 ?        S      0:03 /usr/libexec/sssd/sssd_pac --uid 0 --gid 0 --logger=files
    893 ?        Ssl    7:05 /opt/dynatrace/oneagent/agent/lib64/oneagentwatchdog -bg -config=/opt/dynatrace/oneagent/agent/conf/watchdog.conf
    906 ?        Sl    81:49 oneagentos -Dcom.compuware.apm.WatchDogPort=51000 -Dcom.compuware.apm.WatchDogTimeout=900 -watchdog.restart_file_location=/va
    922 ?        Sl     6:57 oneagentextensions -Dcom.compuware.apm.WatchDogPort=51003 -Dcom.compuware.apm.WatchDogTimeout=900
    924 ?        Sl     6:42 oneagentplugin -Dcom.compuware.apm.WatchDogPort=51002 -Dcom.compuware.apm.WatchDogTimeout=900
    926 ?        Sl     6:15 oneagentnetwork -Dcom.compuware.apm.WatchDogPort=51001 -Dcom.compuware.apm.WatchDogTimeout=900
    928 ?        Sl    12:34 oneagentloganalytics -Dcom.compuware.apm.WatchDogPort=51004 -Dcom.compuware.apm.WatchDogTimeout=900
    956 ?        Ssl    0:51 /usr/sbin/NetworkManager --no-daemon
   1012 ?        Ss     0:09 python3 /var/www/html/address2location-runner
   1014 ?        Ss     0:09 python3 /var/www/html/algorithms-runner
   1016 ?        Ss     1:55 python3 /var/www/html/certificates-runner
   1020 ?        Ss     0:09 python3 /var/www/html/loader-runner"""
        expected = ['address2location-runner','algorithms-runner','certificates-runner']
        actual = parse_psax_all_wsgi_runners(content, do_not_include=['loader-runner'])
        self.assertEqual(expected, actual)

    def test_calculate_wsgi_config_dir_for_os(self):
        os_name = "ubuntu"
        expected = '/etc/apache2/conf-available/'
        actual = calculate_wsgi_config_dir_for_os(os_name)
        self.assertEqual(expected, actual)

        os_name = "rocky"
        expected = '/etc/httpd/conf.d/'
        actual = calculate_wsgi_config_dir_for_os(os_name)
        self.assertEqual(expected, actual)

    def test_extract_valid_possible_server_versions(self):
        folder_contents = {'0.0.0':['loader.py']}
        expected = ['0.0.0']
        actual = extract_valid_possible_server_versions(folder_contents)
        self.assertEqual(expected, actual)

        folder_contents = {'html':['loader.py']}
        expected = []
        actual = extract_valid_possible_server_versions(folder_contents)
        self.assertEqual(expected, actual)

        folder_contents = {'0.0.0':['loader.py'], '1.1.1':[]}
        expected = ['0.0.0']
        actual = extract_valid_possible_server_versions(folder_contents)
        self.assertEqual(expected, actual)

        folder_contents = {'0.0.0':['loader.py'], '1.1.10':['loader.py'], '1.1.1':['loader.py']}
        expected = ['0.0.0', '1.1.1', '1.1.10']
        actual = extract_valid_possible_server_versions(folder_contents)
        self.assertEqual(expected, actual)

    def test_make_sort_from_version(self):
        raw_value = '92.0.4515.98'
        expected = '0000000092.0000000000.0000004515.0000000098'
        actual = make_sort_from_version(raw_value)
        self.assertEqual(expected, actual)

        raw_value = '2.2.2_modified'
        expected = '0000000002.0000000002.0000000002_modified'
        actual = make_sort_from_version(raw_value)
        self.assertEqual(expected, actual)


    def test_extract_current_linked_folder(self):
        # ls -l /var/www
        contents = """total 32
drwxr-xr-x. 20 <USER> <GROUP> 4096 Oct 22 19:32 0.0.0
drwxr-xr-x.  2 <USER> <GROUP> 4096 Sep 24 15:01 2.1.0
drwxr-xr-x.  2 <USER> <GROUP> 4096 Sep 24 14:55 2.1.1
drwxr-xr-x.  2 <USER> <GROUP>    6 Aug  8 16:30 cgi-bin
lrwxrwxrwx.  1 apache apache    5 Oct 11 12:54 html -> 0.0.0
drwxr-xr-x.  2 <USER> <GROUP>    6 Apr  8  2024 htmlfiles
drwxr-xr-x. 20 <USER> <GROUP> 4096 Aug 29 12:22 html_save
drwxr-xr-x. 20 <USER> <GROUP> 4096 Oct 11 12:33 html_test
drwxr-xr-x.  2 <USER> <GROUP>    6 Oct 22 18:44 loader_quick_work
drwxr-xr-x.  3 <USER> <GROUP>   19 May  1 12:28 slicer
drwxr-xr-x.  3 <USER> <GROUP>   17 Sep 23 15:14 var
drwxr-xr-x.  2 <USER> <GROUP>   40 Sep 24 15:01 versions"""
        expected = '0.0.0'
        actual = extract_current_linked_folder(contents)
        self.assertEqual(expected, actual)

        contents = """total 32
drwxr-xr-x. 20 <USER> <GROUP> 4096 Oct 23 15:34 0.0.0
drwxr-xr-x.  3 <USER> <GROUP> 4096 Oct 23 15:38 2.1.0
drwxr-xr-x.  2 <USER> <GROUP> 4096 Sep 24 14:55 2.1.1
drwxr-xr-x.  2 <USER> <GROUP>    6 Aug  8 16:30 cgi-bin
lrwxrwxrwx.  1 root   root     14 Oct 23 15:40 html -> /var/www/0.0.0
drwxr-xr-x.  2 <USER> <GROUP>    6 Apr  8  2024 htmlfiles
drwxr-xr-x. 20 <USER> <GROUP> 4096 Aug 29 12:22 html_save
drwxr-xr-x. 20 <USER> <GROUP> 4096 Oct 11 12:33 html_test
drwxr-xr-x.  2 <USER> <GROUP>    6 Oct 22 18:44 loader_quick_work
drwxr-xr-x.  3 <USER> <GROUP>   19 May  1 12:28 slicer
drwxr-xr-x.  3 <USER> <GROUP>   17 Sep 23 15:14 var
drwxr-xr-x.  2 <USER> <GROUP>   40 Sep 24 15:01 versions"""
        expected = '0.0.0'
        actual = extract_current_linked_folder(contents)
        self.assertEqual(expected, actual)

    def test_extract_server_filenames(self):
        # self archive like cs_sp_pi_slicer_20241023_141834.zip
        zip_list = ['cs_sp_pi_slicer/read_how_to_check_md5.txt', 'cs_sp_pi_slicer/.idea/vcs.xml', 'cs_sp_pi_slicer/__pycache__/slicer_wsgi_datadrop.cpython-311.pyc']
        expected = {'cs_sp_pi_slicer/read_how_to_check_md5.txt':'read_how_to_check_md5.txt'}

        # github zip = cs_sp_pi_slicer-2.1.0.zip
        zip_list = ['cs_sp_pi_slicer-2.1.0/read_how_to_check_md5.txt']
        expected = {'read_how_to_check_md5.txt':'read_how_to_check_md5.txt'}

    def test_make_not_allow_list_from_datastore(self):
        data_store_content = {}
        expected = []
        actual = make_not_allow_list_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

        data_store_content = {'service_loader_template_allow_runner':'no'}
        expected = ['template']
        actual = make_not_allow_list_from_datastore(data_store_content)
        self.assertEqual(expected, actual)

    def test_get_date_formatted(self):
        expected = '20230303081332'
        datetime_now = datetime.datetime.strptime(expected, '%Y%m%d%H%M%S')
        # datetime_now = datetime.datetime.now()
        expected = '20230303_081332_000000'
        actual = datetime_now.strftime('%Y%m%d_%H%M%S_%f')
        self.assertEqual(expected, actual)

    def test_make_sort_from_any_version(self):
        raw_value = '92.0.4515.98'
        expected = '0000000092.0000000000.0000004515.0000000098'
        actual = make_sort_from_any_version(raw_value)
        self.assertEqual(expected, actual)

        raw_value = '20241230_121719'
        expected = '20241230_121719'
        actual = make_sort_from_any_version(raw_value)
        self.assertEqual(expected, actual)

    def test_find_later_version(self):
        version_current = '1.1.0'
        version_list = ['1.1.0']
        expected = ''
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.0'
        version_list = ['1.1.0', '1.1.1']
        expected = '1.1.1'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.0'
        version_list = ['1.1.0', '1.1.1', '1.1.2']
        expected = '1.1.2'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.0'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '20241230_121719']
        expected = '1.1.2'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '20241230_121719'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '20241230_121719']
        expected = ''
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '20241230_121719'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '20241230_121719', '20241230_121720']
        expected = '20241230_121720'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '20241230_121719'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '20241230_121719', '20241230_121719_modified']
        expected = ''
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.2'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '1.1.2_modified', '20241230_121719', '20241230_121719_modified']
        expected = ''
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.1'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '1.1.2_modified', '20241230_121719', '20241230_121719_modified']
        expected = '1.1.2'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        # try with current version being the modified one
        version_current = '1.1.1_modified'
        version_list = ['1.1.0', '1.1.1', '1.1.2', '1.1.2_modified', '20241230_121719', '20241230_121719_modified']
        expected = '1.1.2'
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

        version_current = '1.1.1_modified'
        version_list = ['1.1.0', '1.1.1', '1.1.1_modified', '20241230_121719', '20241230_121719_modified']
        expected = ''
        actual = find_later_version(version_current, version_list)
        self.assertEqual(expected, actual)

    def test_make_value_to_human(self):
        value = 1
        expected = '1'
        actual = make_value_to_human(value)
        self.assertEqual(expected, actual)

        value = 1024
        expected = '1.0K'
        actual = make_value_to_human(value)
        self.assertEqual(expected, actual)

        value = 1024 * 1024
        expected = '1.0M'
        actual = make_value_to_human(value)
        self.assertEqual(expected, actual)

        value = '1K'
        expected = '1K'
        actual = make_value_to_human(value)
        self.assertEqual(expected, actual)

    def test_make_sorted_human(self):
        content = "1K test"
        expected = "1.0K\ttest"
        actual = make_sorted_human(content)
        self.assertEqual(expected, actual)

        content = "1K test\n100 test100"
        expected = "100\ttest100\n1.0K\ttest"
        actual = make_sorted_human(content)
        self.assertEqual(expected, actual)

        content = ""
        expected = ""
        actual = make_sorted_human(content)
        self.assertEqual(expected, actual)

        content = "a"
        expected = "a"
        actual = make_sorted_human(content)
        self.assertEqual(expected, actual)

        content = "1K test\n100 test100"
        threshold = 101
        expected = "1.0K\ttest"
        actual = make_sorted_human(content, threshold)
        self.assertEqual(expected, actual)

        content = "1K test\n100 test100"
        threshold = 100
        expected = "100\ttest100\n1.0K\ttest"
        actual = make_sorted_human(content, threshold)
        self.assertEqual(expected, actual)

        content = "1 test100"
        threshold = 100
        factor = 1024
        expected = "1.0K\ttest100"
        actual = make_sorted_human(content, threshold, factor)
        self.assertEqual(expected, actual)

    def test_make_value(self):
        content = '100'
        expected = 100.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '1K'
        expected = 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '1M'
        expected = 1024.0 * 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '2M'
        expected = 2.0 * 1024.0 * 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '3G'
        expected = 3.0 * 1024.0 * 1024.0 * 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '4T'
        expected = 4.0 * 1024.0 * 1024.0 * 1024.0 * 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = '5P'
        expected = 5.0 * 1024.0 * 1024.0 * 1024.0 * 1024.0 * 1024.0
        actual = make_value(content)
        self.assertEqual(expected, actual)

        content = ''
        expected = -1
        actual = make_value(content)
        self.assertEqual(expected, actual)

    def test_analyze_https_log_rotate(self):
        content = '\n'.join("""
# Note that logs are not compressed unless "compress" is configured,
# which can be done either here or globally in /etc/logrotate.conf.
/var/log/httpd/*log {
    missingok
    notifempty
    sharedscripts
    delaycompress
    postrotate
        /bin/systemctl reload httpd.service > /dev/null 2>/dev/null || true
    endscript
}
""".split('\n')[1:-1]) + '\n'
        expected = '(matches the default)'
        actual = analyze_httpd_log_rotate(content)
        self.assertEqual(expected, actual)

        content = '\n'.join("""
# Note that logs are not compressed unless "compress" is configured,
# which can be done either here or globally in /etc/logrotate.conf.
/var/log/httpd/*log {
    missingok
    notifempty
    sharedscripts
    delaycompress
    postrotate
    endscript
}
""".split('\n')[1:-1]) + '\n'
        expected = """!!! Changed from the default of:
# Note that logs are not compressed unless "compress" is configured,
# which can be done either here or globally in /etc/logrotate.conf.
/var/log/httpd/*log {
    missingok
    notifempty
    sharedscripts
    delaycompress
    postrotate
        /bin/systemctl reload httpd.service > /dev/null 2>/dev/null || true
    endscript
}
"""
        actual = analyze_httpd_log_rotate(content)
#        self.assertEqual(expected, actual)

    def test_locate_first_difference(self):
        content1 = 'test'
        content2 = ''
        expected = 0
        actual = locate_first_difference(content1, content2)
        self.assertEqual(expected, actual)

        content1 = 'test'
        content2 = 't'
        expected = 1
        actual = locate_first_difference(content1, content2)
        self.assertEqual(expected, actual)

        content1 = 'test'
        content2 = 'test5'
        expected = 4
        actual = locate_first_difference(content1, content2)
        self.assertEqual(expected, actual)

        content1 = 'teSt'
        content2 = 'test5'
        expected = 2
        actual = locate_first_difference(content1, content2)
        self.assertEqual(expected, actual)

    def test_strip_comments(self):
        content = "test"
        expected = 'test'
        actual = strip_comments(content)
        self.assertEqual(expected, actual)

        content = "# test"
        expected = ''
        actual = strip_comments(content)
        self.assertEqual(expected, actual)

        content = "# test\ntest2"
        expected = 'test2'
        actual = strip_comments(content)
        self.assertEqual(expected, actual)

        content = "# test\ntest2  # later comment"
        expected = 'test2'
        actual = strip_comments(content)
        self.assertEqual(expected, actual)

        content = "# test\ntest2  # later comment\n      # line3\nline4"
        expected = 'test2\nline4'
        actual = strip_comments(content)
        self.assertEqual(expected, actual)

    def test_make_formatted_text(self):
        content = 'a'
        expected = 'a'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = '<a>'
        expected = '&lt;a&gt;'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = 'a\tb'
        expected = '<table><tr><td>a</td><td>b</td></tr></table>'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

        content = '{"key":"value"}'
        expected = '{<br>&nbsp;&nbsp;&nbsp;&nbsp;"key":"value"<br>}'
        actual = make_formatted_text(content)
        self.assertEqual(expected, actual)

    def test_class_log_monitor(self):
        self.maxDiff = None
        the_class_object = class_log_monitor()

        expected = ''
        actual = the_class_object.report()
        self.assertEqual(expected, actual)

        the_content = ''
        the_class_object.process_log_file_content(the_content)
        expected = ''
        actual = the_class_object.report()
        self.assertEqual(expected, actual)

        the_content = """
"Apr  4 13:06:44 lpec5009slicr04 setroubleshoot[1831769]: SELinux is preventing /usr/sbin/httpd from open access on the file /var/log/slicer/datastore/device_first_date_10000000c2e788cd.#012#012*****  Plugin catchall (100. confidence) suggests   **************************#012#012If you believe that httpd should be allowed open access on the device_first_date_10000000c2e788cd file by default.#012Then you should report this as a bug.#012You can generate a local policy module to allow this access.#012Do#012allow this access for now by executing:#012# ausearch -c 'httpd' --raw | audit2allow -M my-httpd#012# semodule -X 300 -i my-httpd.pp#012"
"""
        the_class_object.process_log_file_content(the_content)
        expected = '1, SELinux is preventing, /usr/sbin/httpd, /var/log/slicer/datastore/, device_first_date_10000000c2e788cd.#012#012*****\n'
        actual = the_class_object.report()
        self.assertEqual(expected, actual)

        the_content = """
Apr  4 09:46:59 lpec5009slicr04 setroubleshoot[1831769]: SELinux is preventing /usr/sbin/httpd from add_name access on the directory /mnt/disks/SSD/var/log/slicer/datadrop/json/id/100000007e3082f5/service_versions.tmp. For complete SELinux messages run: sealert -l 497c0fa2-c44e-48f0-9623-b10f8eb96d19
"""
        the_class_object.process_log_file_content(the_content)
        expected = '1, SELinux is preventing, /usr/sbin/httpd, /mnt/disks/SSD/var/log/slicer/datadrop/json/id/100000007e3082f5/, \n'
        actual = the_class_object.report()
        self.assertEqual(expected, actual)

        the_content = """
"Apr  4 13:06:44 lpec5009slicr04 setroubleshoot[1831769]: SELinux is preventing /usr/sbin/httpd from open access on the file /var/log/slicer/datastore/device_first_date_10000000c2e788cd.#012#012*****  Plugin catchall (100. confidence) suggests   **************************#012#012If you believe that httpd should be allowed open access on the device_first_date_10000000c2e788cd file by default.#012Then you should report this as a bug.#012You can generate a local policy module to allow this access.#012Do#012allow this access for now by executing:#012# ausearch -c 'httpd' --raw | audit2allow -M my-httpd#012# semodule -X 300 -i my-httpd.pp#012"
Apr  4 09:46:59 lpec5009slicr04 setroubleshoot[1831769]: SELinux is preventing /usr/sbin/httpd from add_name access on the directory /mnt/disks/SSD/var/log/slicer/datadrop/json/id/100000007e3082f5/service_versions.tmp. For complete SELinux messages run: sealert -l 497c0fa2-c44e-48f0-9623-b10f8eb96d19
"""
        the_class_object.process_log_file_content(the_content)
        expected = """1, SELinux is preventing, /usr/sbin/httpd, /mnt/disks/SSD/var/log/slicer/datadrop/json/id/100000007e3082f5/, """ + """
1, SELinux is preventing, /usr/sbin/httpd, /var/log/slicer/datastore/, device_first_date_10000000c2e788cd.#012#012*****
"""
        actual = the_class_object.report()
        self.assertEqual(expected, actual)


    def test_update_content_for_site_links(self):
        output_name = ''
        content = ''
        replace_list = []
        expected = ''
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)

        output_name = ''
        content = '<link rel="next" href="getting-started/knowledge/">'
        replace_list = ['getting-started']
        expected = '<link rel="next" href="documentation/site/getting-started/knowledge/">'
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)

        output_name = 'site/releases/2025-03-12_SP.56/index.html'
        content = '<link rel="prev" href="../release-notes/">'
        replace_list = ['getting-started', 'releases']
        expected = '<link rel="prev" href="documentation/site/releases/release-notes/">'
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)

        output_name = 'site/releases/index.html'
        content = '<link rel="stylesheet" href="../assets/stylesheets/main.4af4bdda.min.css">'
        replace_list = ['getting-started', 'releases', 'assets'] # added assets
        expected = '<link rel="stylesheet" href="documentation/site/assets/stylesheets/main.4af4bdda.min.css">'
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)

        output_name = 'site/releases/2025-03-12_SP.56/index.html'
        content = '<link rel="stylesheet" href="../../assets/stylesheets/main.4af4bdda.min.css">'
        replace_list = ['getting-started', 'releases', 'assets'] # added assets
        expected = '<link rel="stylesheet" href="documentation/site/assets/stylesheets/main.4af4bdda.min.css">'
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)

        output_name = 'site/index.html'
        content = '  <img src="images/cardinal-logo-lg.png" alt="Slicer Logo" width="250" height="250" />'
        replace_list = ['images', 'releases', 'assets']
        expected = '  <img src="documentation/site/images/cardinal-logo-lg.png" alt="Slicer Logo" width="250" height="250" />'
        actual = update_content_for_site_links(output_name, content, replace_list)
        self.assertEqual(expected, actual)




#<script src="../../assets/javascripts/bundle.c8b220af.min.js"></script>

















# End of source file
