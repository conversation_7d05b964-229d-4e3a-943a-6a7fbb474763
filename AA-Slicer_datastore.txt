Slicer data store usage map:

Prefix                  Creator         Consumer        Destination
-----------------------------------------------------------------
........................................................................................................
device_profile_(id)     reports         datadrop        used to pull single profile

........................................................................................................
device_service_(id)     reports         datadrop        on RP: pi_runner uses this list, compared to
                                                            its running service versions,
                                                            and pulls any that do not match
                                                            what it has, loads them, and
                                                            starts/restarts that service.

........................................................................................................
device_name_(id)        reports         reports         To show in the device list, as another attribute of the device.
                                        datadrop        (goes out with the profile information?,
                                                            or with the service versions?, ties to (id) like bookmarks)


........................................................................................................
profile_(name)          (manually)      datadrop        on RP: pi_runner saves this information
                                                            locally in /cardinal directory
                                                            and the pi_hmi service picks it up
                                                            from that directory to build the landing
                                                            page, which automatically reloads
                                                            every 30 seconds.




