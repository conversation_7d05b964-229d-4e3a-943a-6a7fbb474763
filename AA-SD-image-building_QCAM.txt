To build a new QCAM image:

https://www.raspberrypi.com/software/operating-systems/

Raspberry Pi OS with desktop
Release date: May 3rd 2023
System: 32-bit
Kernel version: 6.1
Debian version: 11 (bullseye)
Size: 872MB
Show SHA256 file integrity hash:
Release notes

save to /downloads/2023-05-03-raspios-bullseye-armhf.img.xz

copy to /Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/QCAM/

Use balena etcher to burn SD card with this image.

Make sure the pi does not have a network connection, insert sd card, and power on the pi.

Setup comes up, select 'Next'.

Set these:
Country = United States
Language: American English
Timezone: New York
'Next'

//// end here, until more is figured out ////

Username: qcam

OR

Username: cah-pi-su
(enter password twice)
'Next'

Do not connect to WiFI, keep hitting 'Next', until you get 'Restart', and take it.

On boot, click upper left corner raspberry icon, 'Preferences', 'Raspberry Pi Configuration',
    pick the tab 'Interfaces', and turn on 'SSH'

On boot, click upper left corner raspberry icon, 'Preferences', 'Appearance Settings',
    uncheck all; layout = No Image,

On boot, click upper left corner raspberry icon, 'Preferences', 'Main Menu Editor',
    Uncheck all except System Tools

Right click on the terminal shortcut in the top menu bar, and click "remove ...."

# FixMe: Need to disable screen blanking (keep on always)

Float mouse over the networking up/down arrows in the upper right corner to see the ip address

ssh cah-pi-su@(ip)

############################
# configure wifi
############################
sudo vi /etc/wpa_supplicant/wpa_supplicant.conf

add:
network={
	ssid="cah-iot"
	psk="BVIm5bvQ65"
	key_mgmt=WPA-PSK
}


############################
# set up qcam user
############################
sudo adduser qcam
(normal camel case password for worker (Cs))

sudo usermod -a -G video,input,lpadmin qcam

# to see the settings:
ls -l /dev/input/
groups qcam

############################
# Change the autologin user to the qcam
############################
sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf
*** start ***
[Service]
ExecStart=
ExecStart=-/sbin/agetty --autologin qcam --noclear %I \$TERM
*** end ***
sudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>

# Then get the GUI:
sudo vi /etc/lightdm/lightdm.conf
(search for 'autologin-user' by typing '/autologin-user' <enter>)
set to:
autologin-user=qcam

# Test auto login:
sudo reboot

############################
# get updates and required installs
############################

log in as cah-pi-su user

# screen grabs
# sudo apt-get install -y scrot
# 6.1 already has this installed

############################
# Slicer services
############################
sudo mkdir /cardinal

(Manually set up this one, according to the instructions inside it, or push from Slicer)
pi_runner

# Load manually or through Slicer interface to the device (Runner must have been loaded first)
pi_monitor


############################
# Finish up the close out of the image
############################

sudo su

# All of these as a single copy and paste into the terminal:
apt-get clean
apt-get -y autoremove --purge

# All of these as a single copy and paste into the terminal:
rm -rf /Downloads
rm -rf /home/<USER>/*
rm -rf /cardinal/save_values



# All of these as a single copy and paste into the terminal:
systemctl stop pi-hmi.service
systemctl stop pi-runner.service
systemctl stop pi-config.service

find /var/log -type f -regex ".*\.gz$" -delete
find /var/log -type f -regex ".*\.[0-9]$" -delete
rm /var/log/*
echo 'yes' > /cardinal/needsexpand
echo -n "0" > /cardinal/boot_count.txt
echo -n "0" > /cardinal/grab_count.txt
cp /cardinal/browserstart_default /cardinal/browserstart
rm -rf /cardinal/localhtml/*
rm -rf /cardinal/log/*
rm -rf /cardinal/config_*
rm /cardinal/call_home_locations.txt
rm /cardinal/wifi_ssid_psk.txt
rm /cardinal/screen_resolution.txt
rm /cardinal/screen_zoom.txt
rm /cardinal/wifi_config.txt
rm /cardinal/call_home_locations.txt
mkdir /cardinal/localhtml/
echo '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>' > /cardinal/localhtml/index.html
sudo shutdown -h now

====================================================
# to load qcam software, ssh into the device

sudo raspi-config --expand-rootfs
sudo reboot

sudo mkdir /cardinal/thirdparty
cd /cardinal/thirdparty
sudo curl -k --output qcam_20230623_120500.zip https://************/thirdparty?filetodownload=qcam_20230623_120500

sudo unzip qcam_20230623_120500.zip
cd /cardinal/thirdparty/QCAM_Release_2023-06-23
sudo chmod +x ./installer.sh
sudo ./installer.sh

--------------------
Testing Python packages...
cut: requirements.txt: No such file or directory
Testing camera...
Listing all available cameras:
No cameras available!
ERROR: arducam_64mp [9248x6944] camera is not available.
Checking if all cameras are working:
python: can't open file '/cardinal/thirdparty/QCAM_Release_2023-06-22/./test/test_camera_working.py': [Errno 2] No such file or directory
Testing file and folder setup...
Testing Python script installation...
All tests completed.
--------------------


qcamcli --version
systemctl status qcam_ui-runner.service
systemctl status qcam_monitor_weight.service




























# End
