#!/usr/bin/env python3
"""
Development Bridge Module
This single module provides all the necessary imports for WSGI modules to work with dev environment
"""

import sys
import os

# Add dev directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
dev_dir = os.path.join(current_dir, 'dev')
if dev_dir not in sys.path:
    sys.path.insert(0, dev_dir)

# Import and make available all dev modules that WSGI modules need
try:
    # Core modules that WSGI modules import
    from dev import login, datastore, organization, permissions

    # Additional modules that WSGI modules might import
    from dev import address2location, reports

    # Make them available at module level for import
    sys.modules['login'] = login
    sys.modules['datastore'] = datastore
    sys.modules['organization'] = organization
    sys.modules['permissions'] = permissions
    sys.modules['address2location'] = address2location
    sys.modules['reports'] = reports

    print("[DEV_BRIDGE] All dev modules loaded successfully")
    print(f"[DEV_BRIDGE] Available modules: login, datastore, organization, permissions, address2location, reports")

except ImportError as e:
    print(f"[DEV_BRIDGE] Warning: Could not import some dev modules: {e}")

    # Create minimal fallbacks for critical modules
    class MockPermissions:
        @staticmethod
        def log_page_allowed(environ, service, other):
            user = environ.get('REMOTE_USER', 'unknown')
            print(f"[PERMISSIONS] User '{user}' accessed service '{service}' - Status: 200 OK")

        @staticmethod
        def get_module_permissions_for_environ(environ):
            return {
                'to_show_above_login': ['dashboard', 'reports', 'timezones', 'videos'],
                'to_show_for_login': ['login'],
                'to_show_below_login': ['datastore', 'permissions', 'users'],
                'current_user': environ.get('REMOTE_USER', '')
            }



    class MockAddress2Location:
        @staticmethod
        def get_location_from_address(address):
            return {'latitude': 0.0, 'longitude': 0.0, 'address': address, 'status': 'mock'}

    class MockReports:
        @staticmethod
        def get_top_level_issues(siteid=''):
            return {'issues': [], 'count': 0, 'siteid': siteid}

    # Register fallbacks
    sys.modules['permissions'] = MockPermissions()
    sys.modules['address2location'] = MockAddress2Location()
    sys.modules['reports'] = MockReports()

def initialize():
    """Initialize the development bridge"""
    print("[DEV_BRIDGE] Development environment initialized")

def cleanup():
    """Clean up the development bridge"""
    print("[DEV_BRIDGE] Development environment cleaned up")

# Auto-initialize when imported
initialize()
