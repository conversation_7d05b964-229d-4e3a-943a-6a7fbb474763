# A htmlfiles for slicer page services

service = 'htmlfiles'
version = service + '.0.5'

_ = """
This file gets loaded to:
/var/www/html/htmlfiles.py

using:
sudo vi /var/www/html/htmlfiles.py

It also requires:

sudo vi /etc/httpd/conf.d/python-htmlfiles.conf
----- start copy -----
WSGIScriptAlias /htmlfiles /var/www/html/htmlfiles.py
----- end copy -----

sudo chown apache:apache /var/www/html/htmlfiles.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/htmlfiles-runner
sudo chmod +x /var/www/html/htmlfiles-runner

# ===== begin: start file
#!/usr/bin/env python
import htmlfiles
htmlfiles.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/htmlfiles-runner.service
sudo systemctl daemon-reload
sudo systemctl stop htmlfiles-runner.service
sudo systemctl start htmlfiles-runner.service
sudo systemctl enable htmlfiles-runner.service

systemctl status htmlfiles-runner.service

sudo systemctl restart htmlfiles-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep htmlfiles-runner

OR

tail -f /var/log/syslog | fgrep htmlfiles-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/htmlfiles-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!




test on Slicer server with:
cd /var/www/html
sudo python -c "import htmlfiles; print(htmlfiles.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/htmlfiles



"""

import cgi
import copy
import datetime
import traceback
import json
import os
import stat
import sys
import time

import shlex
import subprocess

from tempfile import TemporaryFile

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

base_htmlfiles_path = '/var/www/htmlfiles/'

try:
    import datastore
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import organization
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(htmlfiles status)'

    status = os.system('systemctl is-active --quiet htmlfiles-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# Main is the loop for the "htmlfiles-runner" that the service starts
# ====================================
def main():
    # ====================================

    threshold_time = 90 * 24 * 60 * 60

    pass_count = 0
    while True:
        pass_count += 1

        all_files = os.listdir(base_htmlfiles_path)
        time_of_scan = time.time()
        debug_report = ''
        for file_name in all_files:
            if 'screen_' in file_name:
                file_to_use = base_htmlfiles_path + file_name
                fileStatsObj = os.stat(file_to_use)
                last_modified_time = fileStatsObj[stat.ST_MTIME]
                if time_of_scan - last_modified_time > threshold_time:
                    debug_report += file_name + '\n'
                    try:
                        os.remove(file_to_use)
                    except:
                        pass

        try:
            with open('/dev/shm/htmlfiles_debug.txt', 'w') as f:
                f.write(debug_report)
        except:
            pass

        time.sleep(60)


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}


    if environ['REQUEST_METHOD'] == 'POST':
        body, other = make_body_POST(environ)
        permissions.log_page_allowed(environ, service, other)

    if permissions.permission_prefix_allowed(environ, 'htmlfiles_'):
        try:
            if environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_POST(environ):
    # ====================================
    # https://stackoverflow.com/questions/14355409/getting-the-htmlfiles-file-content-to-wsgi
    # https://stackoverflow.com/questions/14544696/python-simple-wsgi-file-htmlfiles-script-what-is-wrong/14590585

    body = ''

    body += 'method = POST<br>'
    open('/dev/shm/running_exceptions_' + service, 'w').write('htmlfiles POST seen')

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)
    # body += str(field_storage) # FieldStorage(None, None, [FieldStorage('file', 'ChromeZoom.xlsx', 'PK\x03\x...
    # FieldStorage(None, None, [MiniFieldStorage('file_download_allowed', 'testfile1.txt'), MiniFieldStorage('file_download_allowed_allowed', 'Yes')])

    try:
        file_name = field_storage.getvalue('file_download_allowed')
        allow_to_allow = field_storage.getvalue('file_download_allowed_allowed')
        datastore.set_value('download_permission_' + str(file_name), allow_to_allow)
    except:
        open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))
        pass

    try:
        file_name = field_storage.getvalue('file_delete_allowed')
        allow_to_allow = field_storage.getvalue('file_delete_allowed_key')
        if 'delete_it_now' == allow_to_allow:
            try:
                os.remove(base_htmlfiles_path + file_name)
            except:
                pass
            try:
                os.remove(base_htmlfiles_path + file_name + '.md5')
            except:
                pass
    except:
        open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))
        pass

    if len(field_storage.list):
        try:
            for item in field_storage.list:
                if item.filename:
                    body += 'filename, ' + str(item.filename)

                    if True:
                        file_content = item.file.read()
                        body += ', ' + str(len(file_content))
                        output_file = base_htmlfiles_path + item.filename.replace(' ', '_')
                        body += ', ' + str(len(output_file)) + '<br>'
                        if not os.path.exists(os.path.dirname(output_file)):
                            os.makedirs(os.path.dirname(output_file))

                        with open(output_file, 'wb') as f:
                            f.write(file_content)
                    else:
                        file_content = item.file.read().decode('utf-8')
                        body += ', ' + str(len(file_content))
                        output_file = base_htmlfiles_path + item.filename.replace(' ', '_')
                        body += ', ' + str(len(output_file)) + '<br>'
                        if not os.path.exists(os.path.dirname(output_file)):
                            os.makedirs(os.path.dirname(output_file))

                        with open(output_file, 'w') as f:
                            f.write(file_content)

                    open('/dev/shm/running_exceptions_' + service, 'w').write('htmlfiles file: ' + output_file)

                    # build the md5sum
                    command = 'md5sum ' + output_file
                    md5_file = output_file + '.md5'
                    mem_string, fails = do_one_command(command)
                    with open(md5_file, 'w') as f:
                        f.write(mem_string.split()[0])
        except:
            open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))

    else:
        return str(field_storage)

    return make_get_content(environ)


# ====================================
def make_body_GET(environ):
    # ====================================
    return make_get_content(environ)


# ====================================
def get_htmlfiles_file_content(filename=''):
    # ====================================
    return_value = ''

    try:
        return_value = open(base_htmlfiles_path + filename, 'r').read()
    except:
        pass

    return return_value


# ====================================
def get_htmlfiles_files_and_base(filter=''):
    # ====================================
    return_value = []

    if filter:
        found = os.listdir(base_htmlfiles_path)
        for item in found:
            if filter in item:
                if not '.md5' in item:
                    return_value.append(item)
    else:
        return_value = os.listdir(base_htmlfiles_path)

    return sorted(return_value), base_htmlfiles_path


# ====================================
def make_get_content(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}


    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    try:
        body += "<br><br>"
        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += """
<form id="htmlfiles" name="upload" method=post enctype=multipart/form-data>
<input type=file name=file>"""
        body += '</td>'
        body += '<td>'
        body += """
<input type=submit value=Upload>
</form>
"""
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</center>'
        body += '<br><br>'

        body += '<br><br>'

        delete_permission = permissions.permission_allowed(environ, 'htmlfiles_delete')

        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        body += '<tr>'
        body += '<td>'
        body += 'file'
        body += '</td>'
        body += '<td>'
        body += 'size'
        body += '</td>'
        body += '<td>'
        body += 'view'
        body += '</td>'
        body += '<td>'
        body += 'download'
        body += '</td>'
        #        body += '<td>'
        #        body += 'Available for download<br>on the downloads page'
        #        body += '</td>'
        if delete_permission:
            body += '<td>'
            body += 'Delete option<br>(must not be available for download)'
            body += '</td>'
        body += '<td>'
        body += 'md5'
        body += '</td>'
        body += '<td>'
        body += 'Modified'
        body += '</td>'
        body += '<td>'
        body += 'Days Old'
        body += '</td>'

        body += '</tr>'

        url_to_use = make_home_url_from_environ(environ)

        files, base_to_use = get_htmlfiles_files_and_base()
        for file_name in files:
            if not '.md5' in file_name:
                body += '<tr>'
                body += '<td>'
                body += file_name
                body += '</td>'

                body += '<td>'
                body += make_file_size_human_readable(base_to_use + file_name)
                body += '</td>'

                is_allowed = False
                result = datastore.get_value('download_permission_' + file_name)
                if result == 'Yes':
                    is_allowed = True

                if is_allowed:
                    the_string = "Yes"
                    next_Value = "No"
                    color = "(0, 255, 0, 0.3)"
                else:
                    the_string = "No"
                    next_Value = "Yes"
                    color = "(255, 0, 0, 0.3)"

                body += '<td>'
                file_extensions = ['.txt', '.png', '.gif', '.pdf']
                if file_name[-4:] in file_extensions:
                    # the target="_blank" makes the click open a new tab for the result
                    body += '<a href="' + url_to_use + '/download' + '?filetoview=' + file_name + '" target="_blank"> ' + 'view' + '</a>'
                body += '</td>'
                body += '<td>'
                body += '<a href="' + url_to_use + '/download' + '?filetodownload=' + file_name + '"> ' + 'download' + '</a>'
                body += '</td>'

                _ = """
                body += '<td style="background-color:rgba' + color + '">'
                if permissions.permission_allowed(environ, 'htmlfiles_edit'):
                    body += '<form method="post" action="">'
                    body += '<select name="file_download_allowed" id="file_download_allowed" hidden>'
                    body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                    body += '</select>'

                    body += '<select name="file_download_allowed_allowed" id="file_download_allowed_allowed" hidden>'
                    body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                    body += '</select>'
                    body += '<center>'
                    body += '<input type="submit" value="' + the_string + '">'
                    body += '</center>'
                    body += '</form>'
                body += '</td>'
                """

                if delete_permission:
                    body += '<td>'
                    if not is_allowed:
                        next_Value = 'delete_it_now'
                        body += '<form method="post" action="">'
                        body += '<select name="file_delete_allowed" id="file_delete_allowed" hidden>'
                        body += '<option value="' + file_name + '" selected>' + file_name + '</option>'
                        body += '</select>'

                        body += '<select name="file_delete_allowed_key" id="file_delete_allowed_key" hidden>'
                        body += '<option value="' + next_Value + '" selected>' + next_Value + '</option>'
                        body += '</select>'
                        body += '<center>'
                        body += '<input type="submit" value="' + next_Value + '">'
                        body += '</center>'
                        body += '</form>'
                    body += '</td>'

                body += '<td>'
                md5_file = base_to_use + file_name + '.md5'
                md5_value = ''
                if os.path.isfile(md5_file):
                    with open(md5_file, 'r') as f:
                        md5_value = f.read()
                body += md5_value
                body += '</td>'

                body += '<td>'
                days_old = -1
                try:
                    file_to_use = base_to_use + file_name
                    fileStatsObj = os.stat(file_to_use)
                    last_modified_time = fileStatsObj[stat.ST_MTIME]
                    days_old = int((time.time() - last_modified_time) / 60.0 / 60.0 / 24.0)
                    body += datetime.datetime.fromtimestamp(last_modified_time).strftime('%Y.%m.%d')

                except:
                    body += file_to_use

                body += '</td>'

                body += '<td>'
                body += str(days_old)
                body += '</td>'

                body += '</tr>'

        body += '</table>'
        body += '</center>'

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_file_size_human_readable(file_name):
    # ====================================
    fileSizeStr = "???"
    if os.path.isfile(file_name):
        valid = True
        size = os.path.getsize(file_name)
        if size > 0:
            fileSizeStr = str(int(size / 1.0)) + " B"
        if size > 1024:
            fileSizeStr = str(int(size / 1024)) + " KB"
        if size > 1024 ** 2:
            fileSizeStr = str(int(size / (1024 ** 2))) + " MB"
        if size > 1024 ** 3:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " GB"
        if size > 1024 ** 4:
            fileSizeStr = str("{0:.1f}".format(int(10.0 * size / (1024 ** 3)) / 10.0)) + " TB"

    return fileSizeStr


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n<body>\n'

    try:
        body, other = make_body(environ)
        html += body
    except Exception as e:
        html += 'exception: ' + str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import unittest


class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)
