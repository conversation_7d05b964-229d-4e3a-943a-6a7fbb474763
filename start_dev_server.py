#!/usr/bin/env python3
"""
Simple script to start the Slicer local development server
"""

import os
import sys

def main():
    print("=" * 60)
    print("Slicer Local Development Server")
    print("=" * 60)
    print()

    # Check if we're in the right directory
    if not os.path.exists('local_dev_server.py'):
        print("Error: local_dev_server.py not found!")
        print("Please run this script from the cs_sp_pi_slicer directory.")
        sys.exit(1)

    # Import and run the server
    try:
        import local_dev_server
        local_dev_server.main()
    except ImportError as e:
        print(f"Error importing local_dev_server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
