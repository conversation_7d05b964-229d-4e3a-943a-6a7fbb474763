# Keep most recent runs at the top

# Ignore_for_mass_testing

===========================================
manual_tests_completed = 2025.07.16

6) ok
14) There are two new builds available, try the most recent (Card in Jira: CSSPM-9449)

Nothing else changed

===========================================
manual_tests_completed = 2025.06.25

6) ok
14) There are two new builds available, try the most recent (Card in Jira: CSSPM-9449)

Nothing else changed

===========================================
manual_tests_completed = 2025.06.04

6) ok
14) There are two new builds available, try the most recent (Card in Jira)

Nothing else changed

===========================================
manual_tests_completed = 2025.05.15

6) ok

Nothing else changed

===========================================
manual_tests_completed = 2025.04.24

build: 2.2.12_modified

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 187 days remaining
8) count of non-conforming = 117 (oops, need to work on this)
9) ok
10) ok
11) (Local RCS can now make the IOT add request)
12) ok
13) cah-david-ferguson logged in, reminded Jack
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) (skipped)



===========================================
manual_tests_completed = 2025.04.03

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 208 days remaining
8) count of non-conforming = 115
9) ok
10) ok
11) (Local RCS can now make the IOT add request)
12) ok
13) cah-david-ferguson logged in, reminded Jack
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) (skipped)


===========================================
manual_tests_completed = 2025.03.13

Slicer 2.04 -> build: 2.2.6_modified

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 229 days remaining
8) count of non-conforming = 115
9) ok
10) ok
11) (Local RCS can now make the IOT add request)
12) ok
13) cah-david-ferguson logged in, reminded Jack
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) (skipped)

===========================================
manual_tests_completed = 2025.02.21

Slicer 2.04 -> build: 2.2.5_modified

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 249 days remaining
8) count of non-conforming = 115
9) ok
10) ok
11) (Local RCS can now make the IOT add request)
12) ok
13) cah-david-ferguson logged in
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) (skipped)

===========================================
manual_tests_completed = 2025.01.31

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 270 days remaining
8) count of non-conforming = 111
9) ok
10) ok
11) (Local RCS can now make the IOT add request)
12) ok
13) cah-david-ferguson logged in
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) (skipped)

===========================================
manual_tests_completed = 2025.01.10

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) 291 days remaining
8) count of non-conforming = 111
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) ok

===========================================
manual_tests_completed = 2024.12.23

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 309 days remaining
8) count of non-conforming = 111
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) ok

===========================================
manual_tests_completed = 2024.12.02

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 8 days remaining (in red) (We have a new cert, ready to install on the 4th)
8) count of non-conforming = 106
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) 2024-11-19 release already tried, and failed to make a useable image (X/(other) change needed)
15) ok
16) ok

===========================================
manual_tests_completed = 2024.11.07

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 33 days remaining (in red)
8) count of non-conforming = 109
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) New release 2024-10-22, Jira card made to check it out
15) ok
16) ok

===========================================
manual_tests_completed = 2024.10.23

server: slicer04

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 48 days remaining (in red)
8) count of non-conforming = 109
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in, and reminded Jack to do same
14) Nothing new since the 2024-07-04 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.10.02

server: slicer04

1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 69 days remaining (in red)
8) count of non-conforming = 109
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in, and reminded Jack to do same
14) Nothing new since the 2024-07-04 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.09.18

server: slicer04
1) Initially failed. Made code change to reports to make it pass.
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) warning 83 days remaining (in red)
8) count of non-conforming = 109
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) Nothing new since the 2024-07-04 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.08.29

server: slicer04
1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) ok 103 days remaining
8) count of non-conforming = 109, but there is a branch active for refactoring some files
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in
14) Nothing new since the 2024-07-04 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.08.09

server: slicer04
1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) ok 123 days remaining
8) count of non-conforming = 108, but there is a branch active for refactoring some files
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) cah-david-ferguson logged in, and reminded Jack to do same
14) Nothing new since the 2024-07-04 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.06.25

server: slicer04
1) ok
2) ignore for now
3) ok
4) ok
5) ok
6) ok
7) ok
8) count of non-conforming = 106
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok
13) dwf logged in.
14) Nothing new since the 2024-03-15 release
15) ok
16) ok

===========================================
manual_tests_completed = 2024.06.03

Did the datastore snapshot (the most important recurring item)

===========================================
manual_tests_completed = 2024.05.15

Did the datastore snapshot (the most important recurring item)

===========================================
manual_tests_completed = 2024.05.01

Did the datastore snapshot (the most important recurring item)

===========================================
manual_tests_completed = 2024.04.17

Did the datastore snapshot (the most important recurring item)

===========================================
manual_tests_completed = 2024.04.02

Did the datastore snapshot (the most important recurring item)

===========================================
manual_tests_completed = 2024.03.11

server: slicer04
1) ok
2) First cleanup, see what comes next round.
3) ok
4) ok
5) ok
6) ok
7) ok
8) count of non-conforming = 102 (start from here for slicer04 server starting effort)
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2024.02.26
# skipping this cycle, to get server cross over completed

===========================================
manual_tests_completed = 2024.02.12
# skipping this cycle, to get server cross over completed

===========================================
manual_tests_completed = 2024.01.26

Dave Ferguson was out the last two weeks, and made no changes, so do no tests.

===========================================
manual_tests_completed = 2024.01.12

server: slicer01
1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 96
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2023.12.28

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 97
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2023.12.14

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 99
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2023.11.27

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 102
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2023.11.07

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 104
9) ok
10) ok
11) Wait for users to ask, as a test right now, to see if they need them on IOT.
12) ok

===========================================
manual_tests_completed = 2023.10.19

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 109
9) ok
10) ok
11) A few, wait until next cycle.
12) ok

===========================================
manual_tests_completed = 2023.10.05

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 112
9) ok
10) ok
11) A few, with some already requested, so wait until next cycle, when that request is complete.
12) ok

===========================================
manual_tests_completed = 2023.09.11

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
8) count of non-conforming = 114
9) ok
10) ok
11) Two found..., one is QCAM test, and the other was seen at TX015,
    on next cycle, after change freeze, add the TX015 one (100000001cf0cd2d)

===========================================
manual_tests_completed = 2023.08.28

1) (not run on slicer01)
2) (not run on slicer01 anymore)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert
    Could not use Mac to log into account used for cert.
8) count of non-conforming = 116
9) dashboard last day is ********. This is current
10) ok
11) Two found..., one is QCAM test, and the other was seen at TX015, but is offline now

===========================================
manual_tests_completed = 2023.08.11

1) (not run on slicer01)
2) (not reacted to yet)
3) ok
4) ok
5) ok
6) ok
7)  Feb 6, 2024  expiration of corp WiFi cert
    Jan 31, 2024 expiration of slicer.cert (172 days remaining)
    Could not use Mac to log into account used for cert.
8) count of non-conforming = 118
9) dashboard last day is ********. This is current

===========================================
manual_tests_completed = 2023.07.28

1) (not run on slicer01)
2) ok
3) ok
4) ok
5) ok
6) ok

===========================================
manual_tests_completed = 2023.07.14

1) (not run on slicer01)
2) ??? lots of little stuff... need to look into it
3) ok
4) ok
5) ok
6) ok

===========================================
manual_tests_completed = 2023.06.30

1) (not run on slicer01)
2) ??? lots of little stuff... need to look into it
3)
4) ok

===========================================
manual_tests_completed = 2023.06.14

1) (not run on slicer01)
2) Some, but were from development changes, that threw some errors
3) Still broken, working on the fix currently

===========================================
manual_tests_completed = 2023.05.31

1) (not run)
2) Just certificate service prints, which I removed, updated, and cleared the logs

===========================================
manual_tests_completed = 2023.05.30

1) (not run)
2) Lots of errors... make a card to work through them
    offline_apache_error_logs_active.txt


===========================================
manual_tests_completed = 2023.05.16

1) ok
2) ok


===========================================

