_ = """

cd /Users/<USER>/Documents/Cordis

python3 /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/offline_split_datastore.py


"""

ids = (open('ids.txt', 'r').read()).split()
# print ('ids', ids)


datastore = (open('datastore.txt', 'r').read()).split('\n')
# print ('datastore', datastore)

output = []
for item in datastore:
    for id in ids:
        if id:
            if id in item:
                if not 'device_service_' in item:
                    output.append(item)

output_content = ''
profiles = {}
for item in output:
    if 'profile' in item:
        profile_name = item.split('\t')[1]
        profiles[profile_name] = 1
    output_content += item + '\n'

print('profiles', profiles)

for key in profiles.keys():
    for item in datastore:
        if item.split('\t')[0] == key:
            output_content += item + '\n'

open('cordis_datastore.txt', 'w').write(output_content)

print('output', output)

print('ids', len(ids))
print('datastore', len(datastore))
print('output', len(output))

# Ignore_for_mass_testing

# End of file
