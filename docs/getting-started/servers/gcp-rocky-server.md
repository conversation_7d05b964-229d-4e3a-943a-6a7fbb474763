# GCP Rocky Server Setup & Configuration

## AD Groups

### To be able to create the slicer project in GCP:
`A-APM0028269-Slicer-admins`

### To be able to operate the slicer server(s) in GCP:
`a-cloud-slicer-pr-editor`
`a-cloud-slicer-pr-owner`

### To gain access to the reports from BigPanda/Dynatrace:
`A-OktaSSO-Dynatrace-Users`

---

## SNOW Request to Create a New Server

**02 → 2023.01.05 07:55:01**
`REQ3264966`

**03 → 2023.02.10**
`REQ3310940`

`https://cardinal.service-now.com/gith/?id=sc_cat_item&sys_id=67bcbe291b450150ca2ba645624bcb62`

- **Type:** Create VM
- **( !!!! go set the AD Groups first, even though it is lower on the page, it has to be set first.)**
- **Image:** `cah-rocky-8`
- **GCP Project:** `mac-mgmt-pr-cah`
- **Environment:** Production
- **Internet facing:** No
- **Instance name:** `slicr03`
- **(builds instance name of lpec5009slicr03)**
- **Zone:** `us-central1-a`
- **Service account:** `pi-mgmt-pr-slicer-main`
- **Network tags:** `int-webserver`
- **AD Groups:** `A-MacSysAdmins` **(Must add this before selecting cah-rocky-8)**

**Machine Family:** GENERAL-PURPOSE
**Series:** N1
**Machine Type:** `n1-standard-4`
**Boot Disk Type:** `pd-standard`
**Boot Disk Size:** `30`
**Default Disk Type:** `pd-ssd`
**Name:** `slicer03-ssd`
**Size:** `60`
**Additional:** No
**APM ID:** `APM0022695` (not changeable)
*(this is JamfPro APM, Slicer is APM0028269, Raspberry Pi is APM0027587, Jamf Cloud is 28731)*
**Funded:** No
**Submit**

---

## ServiceNow Requests for Additional Servers

**04 → 2024.01.08**
**GCP Project Request:**
`https://cardinal.service-now.com/now/nav/ui/classic/params/target/com.glideapp.servicecatalog_cat_item_view.do%3Fv%3D1%26sysparm_id%3Df432c90edb7b9300be676165ca9619dd`

- **Looking to do:** Create New
- **Is this an EDDIE, ...:** No
- **Project Name:** slicer
- **Environment:** Prod
- **Project ID:** `slicer-pr-cah` *(auto populate from choices)*
- **Primary Application:** Slicer (`APM0028269`)
- **Contact Manager:** Aaron Perkins *(auto populated from application)*
- **Cost Center:** `**********` *(auto populated from application)*
- **Click 'Add to Cart' → 'Submit Items'**

`REQ3708042`

After creation, go to the GCP page:
`https://login.cardinalhealth.net/`

Search and open **"slicer-pr"**. If you see an error:
You need additional access to the project: slicer-pr
To request access, contact your project administrator and provide them a copy of the following information:
- **Missing permissions:**
  - `compute.instances.list`
- **Troubleshooting URL:**
  `console.cloud.google.com/iam-admin/troubleshooter;permissions=compute.instances.list;principal=<EMAIL>;resources=%2F%2Fcloudresourcemanager.googleapis.com%2Fprojects%2Fslicer-pr-cah/result`

To request access:
`https://cardinal.service-now.com/gith?sys_id=6e98df6a4763715073d32a24836d43e2&view=sp&id=ticket_request&table=sc_req_item`

Post a question: **"How do we resolve this?"**

---

## Granting Access to AD Groups

Go to **"Get IT Help"**:
`https://cardinal.service-now.com/gith?id=sc_home`

Click **"Access Request for Active Directory Groups"**

- **Type of Request:** Grant User Access
- **Account Type:** Normal
- **Domain:** `CardinalHealth.net`
- **Select the Group:** `a-cloud-slicer-pr-owner`
- **Click "Add to Cart" → Checkout → Submit**

`REQ3712842` (Created 2024.01.11)

---

## VM Creation & Setup

Check the wiki for instructions:

- **(old)** `https://wiki.cardinalhealth.net/GCP_Create_VM_Instance`
- **(current)** `https://wiki.cardinalhealth.net/GCP_VM_Instance_Requests`

If prompted to log in, follow:
`https://cardinal.service-now.com/gith?id=sc_cat_item&table=sc_cat_item&sys_id=5e0450f71be93550473c36ef034bcbec`

---

## 🔍 Troubleshooting SSH Access Issues

If **Remote into it via SSH** fails, toggle the `oslogin` metadata:
gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=TRUE
gcloud compute instances add-metadata lpec5009slicr04 –zone us-central1-a –metadata enable-oslogin=FALSE

---

## Mounting Persistent Disk (SSD)
lsblk
sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb
sudo mkdir -p /mnt/disks/SSD
sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD
sudo chmod a+w /mnt/disks/SSD

Make mount persistent:
sudo cp /etc/fstab /etc/fstab.backup
sudo blkid /dev/sdb

---

## Installing Apache & Configuring TLS 1.3
sudo dnf update -y
sudo dnf install -y mod_ssl httpd

Edit **`/etc/httpd/conf.d/ssl.conf`** and add:
SSLProtocol +TLSv1.3

Restart Apache:
sudo systemctl restart httpd

---

## Apache Tuning (Avoid Scoreboard Full Issue)

Modify **`/etc/httpd/conf.modules.d/10-mpm-event.conf`**:

```
<IfModule mpm_event_module>
    StartServers             3
    MinSpareThreads          75
    MaxSpareThreads          250
    ThreadLimit              64
    ThreadsPerChild          25
    MaxRequestWorkers        400
    MaxConnectionsPerChild   0
</IfModule>
```

Restart Apache:
sudo apachectl configtest
sudo systemctl restart httpd

---

## Additional Steps

### VM Creation for slicr04

- **Type:** Create VM
- **Image:** `cah-rocky-9`
- **GCP Project:** `slicer-pr-cah`
- **Environment:** Production
- **Internet facing:** No
- **Instance name:** `slicr04`
- **Zone:** `us-central1-a`
- **Service account:** `slicer-pr-def`
- **Network tags:** `int-webserver`
- **AD Groups:** `A-APM0028269-Slicer-admins` **(Must add this before selecting cah-rocky-9)**

**Machine Family:** GENERAL-PURPOSE
**Series:** N1
**Machine Type:** `n1-standard-4`
**Boot Disk Type:** `pd-standard`
**Boot Disk Size:** `30`
**Default Disk Size:** `200`
**Default Disk Name:** `slicer04-ssd`
**Additional:** No
**APM ID:** `APM0028269` (not changeable)
**Funded:** No
**Submit Now**

**2024.01.10**
`REQ3711830`
`RITM5739116` (Since there was not an owner at the time of the request, this request was cancelled. Make a new one)

**2024.01.16**
`REQ3718032`
`RITM5747141`

---

### VM Creation for slicr05

- **Type:** Create VM
- **Image:** `cah-rocky-9`
- **GCP Project:** `slicer-pr-cah`
- **Environment:** Production
- **Internet facing:** No
- **Instance name:** `slicr05`
- **Zone:** `us-central1-a`
- **Service account:** `slicer-pr-def`
- **Network tags:** `int-webserver`
- **AD Groups:** `A-APM0028269-Slicer-admins` **(Must add this before selecting cah-rocky-9)**

**Machine Family:** GENERAL-PURPOSE
**Series:** N1
**Machine Type:** `n1-standard-4`
**Boot Disk Type:** `pd-standard`
**Boot Disk Size:** `30`
**Additional:** 1 additional
**Type:** `pd-ssd`
**Default Disk Size:** `200`
**Default Disk Name:** `slicer05-ssd`
**APM ID:** `APM0028269` (not changeable)
**Funded:** No
**Submit Now**

**2024.03.19**
`REQ3799382`
`RITM5856652`

---

### Setting Root Password to Never Expire

Check if the root account is set to expire, and if so, set it to not expire:

```sh
sudo chage -l root
sudo chage -l david.ferguson

sudo chage -M -1 root
sudo chage -M -1 david.ferguson
```

---

### Additional Configuration for Apache

```sh
sudo setfacl -m "u:apache:rwx" "/usr/local/lib/python3.6/site-packages"
sudo chmod 755 /usr/local/lib/python3.6/site-packages -R
sudo restorecon -R /usr/local/lib/python3.6/site-packages/
sudo setsebool -P httpd_tmp_exec on
sudo chmod o+rx /usr/local/lib/python3.6/site-packages -R
sudo setsebool -P httpd_read_user_content 1
```

---

### Installing htop on Rocky Linux

```sh
sudo dnf upgrade --refresh
sudo dnf config-manager --set-enabled crb
sudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
sudo dnf install htop -y
```

---

### DNS Configuration

**2023.01.25**

- **What:** Add
- **Type:** A Record
- **ASAP:** Yes
- **Host:** slicer2
- **Domain:** cardinalhealth.net
- **FQDN:** slicer2.cardinalhealth.net
- **IP:** ************
- **Internal**

Submit:
`REQ3288460`

**2023.01.25**

- **Modify**
- **A Record**
- **ASAP:** Yes
- **Host:** slicer2
- **Domain:** cardinalhealth.net
- **FQDN:** slicer2.cardinalhealth.net
- **OLD:** ************
- **NEW:** ***********
- **Internal**

---

### Troubleshooting Apache Lockup

**2024.04.07**

Midnight Saturday to Sunday rollover lockup of apache:

```sh
sudo apachectl configtest
systemctl restart httpd
```

Modify **`/etc/httpd/conf.modules.d/10-mpm-event.conf`**:

```sh
<IfModule mpm_event_module>
    StartServers             3
    MinSpareThreads          75
    MaxSpareThreads          250
    ThreadLimit              64
    ThreadsPerChild          25
    MaxRequestWorkers        400
    MaxConnectionsPerChild   0
</IfModule>
```

---

### Additional Steps for Content Loading

- Browse to the IP address of the server.
- Enable Trust by clicking on the "Not Trusted" when the index (home) page first loads.
- Log in as a user that has the admin privilege by being a member of "A-APM0028269-Slicer-admins".
- Click into 'users' page, and give yourself 'loader create', 'dataport_create' permissions.
- Go back home, see the dataport link, follow it, and then choose the saved datastore_snapshot.txt file.
- Go back home, see all permissions restored, and shows all allowed modules.
- Go to 'upload', and upload the file 'read_release_notes_slicer_pi.txt'.
- Pull down htmlfiles content, and upload to the new server.
- Pull down the multimedia content for all, and load to new server.
- Codeupload all the pi content.
- Download all download content from old server, then "upload" to new.

---

### Manual Fixes After First Loader Install Attempt

```sh
sudo setfacl -m "u:apache:rwx" "/usr/local/lib/python3.6/site-packages"
sudo chmod 755 /usr/local/lib/python3.6/site-packages -R
sudo restorecon -R /usr/local/lib/python3.6/site-packages/
sudo setsebool -P httpd_tmp_exec on
sudo chmod o+rx /usr/local/lib/python3.6/site-packages -R
sudo setsebool -P httpd_read_user_content 1
```

---

### Dev Setup for Rocky Linux

**2022.12.20**

- Download Rocky Linux 8.7, x86, minimal from `https://rockylinux.org/download/`.
- Set up VirtualBox with the following settings:
  - Name: Rocky8
  - Image: Rocky 8 minimal
  - Skip unattended: checked
  - Base memory: 8000 MB
  - Processors: 4
  - Create: 20 GB
  - Network: Bridged Adapter (for CAH remote, must be wired to USB ethernet adapter, to Meraki, and select that for the bridge)
- Start the VM and follow the installation prompts.
- Set root password and create a user account.
- Configure network and host settings.
- Begin installation and reboot system after completion.
- Take a snapshot of the VM.

---

### Additional Configuration for DNS

**2023.01.25**

- **What:** Add
- **Type:** A Record
- **ASAP:** Yes
- **Host:** slicer2
- **Domain:** cardinalhealth.net
- **FQDN:** slicer2.cardinalhealth.net
- **IP:** ************
- **Internal**

Submit:
`REQ3288460`

**2023.01.25**

- **Modify**
- **A Record**
- **ASAP:** Yes
- **Host:** slicer2
- **Domain:** cardinalhealth.net
- **FQDN:** slicer2.cardinalhealth.net
- **OLD:** ************
- **NEW:** ***********
- **Internal**

---

### Installing htop on Rocky Linux

```sh
sudo dnf upgrade --refresh
sudo dnf config-manager --set-enabled crb
sudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm https://dl.fedoraproject.org/pub/epel/epel-next-release-latest-9.noarch.rpm
sudo dnf install htop -y
```

---

### Troubleshooting Apache Lockup

**2024.04.07**

Midnight Saturday to Sunday rollover lockup of apache:

```sh
sudo apachectl configtest
systemctl restart httpd
```

Modify **`/etc/httpd/conf.modules.d/10-mpm-event.conf`**:

```sh
<IfModule mpm_event_module>
    StartServers             3
    MinSpareThreads          75
    MaxSpareThreads          250
    ThreadLimit              64
    ThreadsPerChild          25
    MaxRequestWorkers        400
    MaxConnectionsPerChild   0
</IfModule>
```

---
**End of document.**
