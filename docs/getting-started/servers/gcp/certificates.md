# Certificates

## **Self-Signed TLS Certificate for Initial Configuration**

[Linode Guide: Create a Self-Signed TLS Certificate](https://www.linode.com/docs/guides/create-a-self-signed-tls-certificate/)

[Unix SE: Trust Self-Signed Certificates in cURL](https://unix.stackexchange.com/questions/451207/how-to-trust-self-signed-certificate-in-curl-command-line)

```sh title="Generate Self-Signed Certificate"
sudo su
cd ~
mkdir /root/certs && cd /root/certs
openssl req -new -newkey rsa:4096 -x509 -sha256 -days 365 -nodes -out slicer.crt -keyout slicer.key -addext "basicConstraints=critical,CA:TRUE,pathlen:1"
```

Use the following answers for the certificate prompt:

```
Country Name (2 letter code) [US]: US
State or Province Name (full name) [Ohio]: Ohio
Locality Name (eg, city) [Dublin]: Dublin
Organization Name (eg, company) [CardinalHealth]: CardinalHealth
Organizational Unit Name (eg, section) [ClientEngineering]: ClientEngineering
Common Name (e.g. server FQDN or YOUR name) [slicer.cardinalhealth.net]: slicer.cardinalhealth.net
Email Address [<EMAIL>]: <EMAIL>
```

```sh title="Install Additional Certificate Tools"
sudo yum install gnutls-utils
```

```sh title="Verify Certificate"
certtool -i < slicer.crt
```

```sh title="Generate Additional Certificates"
certtool -p --outfile localhost.key
certtool -s --load-privkey localhost.key --outfile localhost.crt
```

Use the same certificate details as before.

```sh title="Move Certificates to Secure Location"
cp localhost.crt /etc/pki/tls/private/slicer.cert
cp localhost.key /etc/pki/tls/private/slicer.key
```

```sh title="Exit Root User"
exit
```

---

## **Certificate**

[ServiceNow Cert Request](https://cardinal.service-now.com/gith?id=sc_cat_item&amp;sys_id=8f306fc72bf235000a05533219da153c)

| **Property**        | **Details** |
|---------------------|------------|
| **Project**        | Standard |
| **Name**           | <https://slicer.cardinalhealth.net> |
| **Server Type**    | Unix |
| **Hosting Server** | GCP `lpec5009slicr01` in project `mac-mgmt-pr-cah` |
| **IP**             | `***********` |
| **Audience**       | Internal |
| **Environment**    | Production |
| **Application**    | Slicer |
| **Owner**         | Russell Lobuzzetta |
| **Group Mailbox**  | GMB-EIT-RaspberryPi |
| **Port**           | 443 |

### **When Cert is Issued:**

[SSL Certificate Installation Guide](https://www.ssls.com/knowledgebase/how-to-install-an-ssl-certificate-on-apache/)

Cert arrived as a compressed file **"slicercardinalhealthnet.7z"**, with a password sent in a separate email.

To extract on **Mac**:

- Install **"The Unarchiver"** or **"Extractor - Unarchive Files"** from the App Store.
  `(one of them worked, I forget which; once I got a clean file and password)`

!!! info "To prepare the Cert on **Mac**"
    In terminal on your Mac, go to where the pfx file is unzipped:
    ```sh
    cd /Users/<USER>/Documents/slicerCert
    xxd slicercardinalhealthnet.pfx
    ```
    Copy all text output, paste into a text document, and clean up the start and end to remove prompt lines.

### **On Slicer Server:**

```sh
sudo su
mkdir /etc/pki/tls/private/20210404
cd /etc/pki/tls/private/20210404
vi slicercardinalhealthnet.txt
```

```sh title="Insert the cleaned-up content"
cat slicercardinalhealthnet.txt | xxd -r >slicercardinalhealthnet.pfx
```

### **Convert PFX to Cert & Key Files:**

[Apache SSL Setup](https://stackoverflow.com/questions/8774574/how-can-i-convert-a-pfx-certificate-file-for-use-with-apache-on-a-linux-server)
[Converting PFX Certificate with Apache SSL](https://www.jasonheckman.com/technology/converting-a-pfx-certificate-bag-to-use-with-apache-ssl/)

!!! info ""
    ```sh
    openssl pkcs12 -info -in slicercardinalhealthnet.pfx
    ```
    ```sh
    openssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert
    ```
    Use the password that was supplied in the email for this cert

    ```sh
    openssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes -out slicer.key
    ```
    Use the password that was supplied in the email for this cert

    ```sh
    openssl rsa -in slicer.key -outform PEM -out slicer_pem.key
    ```

    ```sh
    openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem
    ```
    Use the password that was supplied in the email for this cert

    ```sh
    openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -nodes -out domain-ca.crt
    ```
    Use the password that was supplied in the email for this cert

### **Move Certificates to Secure Location:**

```sh
cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt
cp slicer.cert /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
cp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem

chmod 600 /etc/pki/tls/private/slicer.cert
chmod 600 /etc/pki/tls/private/slicer.key
chmod 600 /etc/pki/tls/private/domain-cabundle.pem
```

### **Verify Certificate Installation:**

```sh
openssl verify /etc/pki/tls/private/slicer.cert
```

!!! info "Expected Output"
    ```
    /etc/pki/tls/private/slicer.cert: C = US, ST = Ohio, L = Dublin, O = Cardinal Health, OU = EIT, CN = slicer.cardinalhealth.net
    error 20 at 0 depth lookup:unable to get local issuer certificate
    ```

### **Restart Apache:**

```sh
service httpd restart
exit
```

### **Check Certificate Validity:**

```sh
openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2>/dev/null | openssl x509 -noout -dates
```

!!! info "Expected Output"
    ```
    notBefore=Apr  5 15:37:06 2021 GMT
    notAfter=Apr  5 15:37:06 2022 GMT
    ```

### **Validate Certificate in Browser (Chrome, Edge):**

- Open **[https://slicer.cardinalhealth.net](https://slicer.cardinalhealth.net)** and check for security warnings.

### **Test from a Raspberry Pi:**

```sh
curl https://slicer.cardinalhealth.net
```

!!! danger "If it fails:"
    ```
    curl: (60) SSL certificate problem: unable to get local issuer certificate
    ```
    More details here: [https://curl.haxx.se/docs/sslcerts.html](https://curl.haxx.se/docs/sslcerts.html)
    ```
    curl failed to verify the legitimacy of the server and therefore could not
    establish a secure connection to it. To learn more about this situation and
    how to fix it, please visit the web page mentioned above.
    ```

!!! note "Try:"
    ```sh
    openssl s_client -connect slicer.cardinalhealth.net:443
    ```

???+ info
    Need to add root trust cert for Cardinal?
    [Pi Trusted Certificate Setup](https://raspberrypi.stackexchange.com/questions/76419/entrusted-certificates-installation)

    How to install certificates for command line?
    [Ask Ubuntu - How to Install Certificates](https://askubuntu.com/questions/645818/how-to-install-certificates-for-command-line)

On Pi, to add Trusted Cert put the Cert in `/usr/local/share/ca-certificates/`

```sh
sudo cp domain-ca.crt /usr/local/share/ca-certificates/
```

```sh
sudo rm /etc/ssl/certs/ca-certificates.crt
sudo apt-get update && sudo apt-get install -y --reinstall ca-certificates
# sudo dpkg-reconfigure ca-certificates
```

```sh title="Re-run the update command"
sudo update-ca-certificates
```

---
