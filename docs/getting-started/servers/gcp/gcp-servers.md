# Servers

## List of Servers

| Server   | IP Address     |
|----------|----------------|
| slicer01 | `***********`  |
| slicer03 | `************` |
| slicer04 | `***********`  |

!!! note
    > If the server moves again, then the firewall will need to be updated for the ??? project
    > (Qcam was going to be a dependency but is not now).

---

## Notes

This was the original instance.
As of **2022.10.14**, refer to [GCP Rocky Server](../gcp-rocky-server.md).

???+ info "slicer01"

    **📅 Date:** 2024.01.08

    ```sh
    hostnamectl
    ```

    | **Property**           | **Value**                                      |
    |------------------------|----------------------------------------------|
    | **Static Hostname**    | `lpec5009slicr01`                            |
    | **Icon Name**         | `computer-vm`                                |
    | **Chassis**           | `vm`                                          |
    | **Machine ID**        | `012a787168254cbcaa5f13dde54611bc`           |
    | **Boot ID**           | `6b72c8126ef94b4bbfb2d86e0de5c4a0`           |
    | **Virtualization**    | `kvm`                                        |
    | **Operating System**  | `CentOS Linux 7 (Core)`                      |
    | **CPE OS Name**       | `cpe:/o:centos:centos:7`                     |
    | **Kernel**            | `Linux 3.10.0-1160.102.1.el7.x86_64`         |
    | **Architecture**      | `x86-64`                                     |

---

## Old Setup: Creating the Slicer Server Instance on GCP

### **Starting Point**

Refer to: [GCP Create VM Instance](https://wiki.cardinalhealth.net/GCP_Create_VM_Instance).

### **Install Google Cloud SDK on Your PC**

On your PC (tested on MAC), install the GCloud SDK, which includes the gcloud command line utility:

Download and Install:
[Google Cloud SDK Installation Guide](https://cloud.google.com/sdk/docs/install)

---

## **Authenticate with GCP**

```sh
gcloud auth login
```

!!! info "Get someone to add you to the required permissions group to access: `(Russ L. set this up for Dave F.)`"
    ```
    mac-mgmt-pr
    mac-mgmt-np
    ```

    That is:
        ```
        a-cloud-mac-mgmt-np-editor
        a-cloud-mac-mgmt-np-owner
        a-cloud-mac-mgmt-pr-editor
        a-cloud-mac-mgmt-pr-owner
        ```

---

## **Projects Based on Given Permissions**

```sh
gcloud components update
gcloud init
gcloud auth login
gcloud auth list
gcloud <NAME_EMAIL>
gcloud auth login
```

```sh
gcloud config set project mac-mgmt-np-cah
gcloud projects list
```

???+ info "Expected Output"
    ```
    cah-host-nonprod  cah-host-nonprod  ************
    cah-host-prod     cah-host-prod     ************
    cims-np-cah       cims-np           ************
    mac-mgmt-np-cah   mac-mgmt-np       ************
    mac-mgmt-pr-cah   mac-mgmt-pr       92183180931
    management-cah    management        372449746971
    slicer-pr-cah     slicer-pr         361361986442
    ```

---
