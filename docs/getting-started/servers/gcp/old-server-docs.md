# Old Development Server Notes

## New Development VM

In a terminal window on the PC (only do this to create a new instance):

[Google Cloud Deployment Manager Quickstart](https://cloud.google.com/deployment-manager/docs/quickstart)

### **Step 1: Set the Correct Project**
```sh
gcloud config set project mac-mgmt-np-cah
```

### **Step 2: Name Your Instance**

Refer to the [GCP Naming Standards](https://wiki.cardinalhealth.net/GCP_Naming_Standards/Hostnames).

Example:
```sh
ldec5009slicr01
```

### **Step 3: Manage Service Accounts**

Service accounts define how VMs obtain permissions.

[Google Cloud IAM Service Accounts](https://cloud.google.com/iam/docs/creating-managing-service-accounts)

```sh
gcloud iam service-accounts list
```

```sh
gcloud iam service-accounts create pi-mgmt-np-slicer-master \
    --description="pi-mgmt-np-slicer-master" \
    --display-name="pi-mgmt-np-slicer-master"
```

!!! info "Generated Service Account:"
    ```sh
    <EMAIL>
    ```

### **Step 4: Create with YAML**

```sh
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create ldec5009slicr01 --config ldec5009slicr01.yaml --project mac-mgmt-np-cah
```

### **Step 5: Access (maybe needed)**

You may need to be added to the `"A-MacSysAdmins"` group.
(**Russ L.** owns it, **Matt W.** asked for it for **David F.**).

### **Step 6: Fix SSH Access Issues (if fails)**

If the "Remote into it via SSH" in below here fails, then toggle the **OS Login** on then off, like:

```sh
gcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=TRUE
gcloud compute instances add-metadata ldec5009slicr01 --metadata enable-oslogin=FALSE
```

### **Step 7: Remote into the Instance via SSH**

```sh
gcloud compute ssh david.ferguson@ldec5009slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip
```

---

## Install Apache

[DigitalOcean Guide: How to Install Apache on CentOS 7](https://www.digitalocean.com/community/tutorials/how-to-install-the-apache-web-server-on-centos-7)

```sh title="Update and Install Apache"
sudo yum update httpd
sudo yum install httpd
```

```sh title="Configure Firewall for Apache"
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

```sh title="Start and Verify Apache"
sudo systemctl start httpd
sudo systemctl status httpd
```

```sh title="Check Default Configuration"
cat /etc/httpd/conf.d/welcome.conf
```

```sh title="Set Up a Test Page"
sudo su
echo "empty" > /var/www/html/index.html
exit
```

---

## SSL Certificate Setup

### Certificate Generation

References

- [DigitalOcean: Create SSL Certificate on Apache for CentOS 7](https://www.digitalocean.com/community/tutorials/how-to-create-an-ssl-certificate-on-apache-for-centos-7)
- [The SSL Store: Apache OpenSSL Installation](https://www.thesslstore.com/knowledgebase/ssl-install/apache-openssl-installation/)
- [The SSL Store: SSL Certificate Generation](https://www.thesslstore.com/knowledgebase/ssl-generate/)
- [GetPageSpeed: SSL Directory Setup](https://www.getpagespeed.com/server-setup/ssl-directory)

### Generate SSL Certificate Signing Request (CSR)

```sh
openssl req -new -newkey rsa:2048 -nodes -keyout server.key -out server.csr
```

Provide the following answers when prompted:

```
Country Name (2 letter code) [US]: US
State or Province Name (full name) [Ohio]: Ohio
Locality Name (eg, city) [Dublin]: Dublin
Organization Name (eg, company) [CardinalHealth]: CardinalHealth
Organizational Unit Name (eg, section) [ClientEngineering]: ClientEngineering
Common Name (e.g. server FQDN or YOUR name) [dslicer.cardinalhealth.net]: dslicer.cardinalhealth.net
Email Address [<EMAIL>]: <EMAIL>
Challenge Password: thisisslicer
Optional Company Name (Leave blank): (empty)
```

```sh title="View the generated CSR"
cat server.csr
```

### Send CSR to AD Team (not completed)

[Submit Request in ServiceNow](https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=8f306fc72bf235000a05533219da153c)

---

## SSL Certificate Installation

### Move and Secure the Certificate

```sh
sudo cp server.key /etc/pki/tls/private/dslicer.cardinalhealth.net
sudo chown root:root /etc/pki/tls/private/dslicer.cardinalhealth.net
sudo chmod 0600 /etc/pki/tls/private/dslicer.cardinalhealth.net
```

```sh title="Verify Apache Configuration"
cat /etc/httpd/conf/httpd.conf
```

---

## Internal DNS Configuration

Verify Internal DNS Resolution

```sh
ping ldec5009slicr01.c.mac-mgmt-np-cah.internal
```

!!! note
    This may not work out of the box.

Check Current GCP IP Address

```sh
nslookup ************
```

*Current address in GCP: `************`*

!!! info "Request DNS Entry Addition"
    [Request DNS Entry in ServiceNow](https://cardinal.service-now.com/gith?id=sc_cat_item&sys_id=9e9f167cdbf0e344be676165ca961963)

---

## Install Python3 and Required Libraries

```sh
sudo yum update -y
sudo yum install -y python3
python3 --version
```

---

## Flask Setup for Running Python

References

- [Build a Simple Python REST API with Apache2, Gunicorn, and Flask](https://medium.com/@thishantha17/build-a-simple-python-rest-api-with-apache2-gunicorn-and-flask-on-ubuntu-18-04-c9d47639139b)
- [Flask Deployment with Apache on CentOS](https://dev.to/sm0ke/flask-deploy-with-apache-on-centos-minimal-setup-2kb7)
- [Flask Deployment using mod_wsgi](https://flask.palletsprojects.com/en/1.1.x/deploying/mod_wsgi/)
- [Flask with uWSGI on CentOS](https://mitchjacksontech.github.io/How-To-Flask-Python-Centos7-Apache-uWSGI/)
- [Setting Up a Python Virtual Environment on CentOS](https://www.liquidweb.com/kb/how-to-setup-a-python-virtual-environment-on-centos/)

### Install Required Packages

```sh
sudo pip3 install --upgrade pip
sudo pip3 install virtualenv flask
```

---

## Setting Up a Flask Application

### Step 1: Create Project Directory

```sh
cd /var/www
sudo mkdir hitme
```

### Step 2: Create the Main Application File

```sh
sudo vi /var/www/hitme/run.py
```

Paste the following code:

```python
import os
from app import app

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
```

### Step 3: Create the Flask Application Directory

```sh
sudo mkdir /var/www/hitme/app
```

### Step 4: Create the Flask App Initialization File

Edit `__init__.py`:

```sh
sudo vi /var/www/hitme/app/__init__.py
```

```python title="Paste the following code:"
from flask import Flask
app = Flask(__name__)

@app.route("/")
def hello():
    return "Hello world!"
```

### Step 5: Set Up a Virtual Environment

```sh
cd /var/www
sudo python3 -m venv hitme
```

### Step 6: Activate the Virtual Environment and Install Flask

```sh
cd /var/www/hitme
source bin/activate
pip install flask
```

### Step 7: Run the Flask Application

```sh
export FLASK_APP=run.py
flask run
```

## Additional Setup

```sh
sudo virtualenv --python=python3 hitme # the venv is created inside the app folder
sudo cd /var/www/hitme
source bin/activate
pip install --upgrade pip
pip freeze
pip install -U pip requests
pip freeze
deactivate
```

```sh title="Configure WSGI"
sudo vi /var/www/hitme/wsgi.py
```

```python title="Paste the following code:"
#!/usr/bin/env python
import sys
import site

site.addsitedir('/var/www/hitme/lib/python3.6/site-packages')

sys.path.insert(0, '/var/www/hitme')

from app import app as application
```

```sh title="Configure Apache for Flask"
sudo vi /etc/httpd/conf.d/flask-hitme.conf
```

```apache title="Paste the following:"
<VirtualHost _default_:443>
     WSGIDaemonProcess hitme user=apache group=apache threads=2
     WSGIScriptAlias /hitme /var/www/hitme/wsgi.py
     <Directory /var/www/hitme>
         Require all granted
     </Directory>
SSLEngine on
SSLCertificateFile "/etc/pki/tls/private/slicer.cert"
SSLCertificateKeyFile "/etc/pki/tls/private/slicer.key"
SSLCACertificateFile  "/etc/pki/tls/private/domain-cabundle.pem"
</VirtualHost>
```

```sh title="Restart Apache"
sudo systemctl restart httpd
```

---

## Extra Dev Server with 5008

```sh title="Create with YAML"
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create ldec5008slicr01 --config ldec5008slicr01.yaml --project mac-mgmt-np-cah
```

May need to be added to `A-MacSysAdmins` as a member `(Russ L. owns it, Matt W. asked for it for David F.)`

!!! info "If the remote in below here fails, then toggle the oslogin on then off, like:"
    ```sh
    gcloud config set project mac-mgmt-np-cah
    gcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=TRUE
    gcloud compute instances add-metadata ldec5008slicr01 --metadata enable-oslogin=FALSE
    ```

```sh title="Remote Access via SSH"
gcloud compute ssh david.ferguson@ldec5008slicr01 --zone us-central1-c --project mac-mgmt-np-cah --internal-ip
```

```sh title="Check Known Hosts:"
/root/.ssh/known_hosts
```

```sh title="User Login Attempt"
cp /var/log/slicer/login/throw/david.ferguson /var/log/slicer/login/keep/david.ferguson
sudo chown apache:apache /var/log/slicer/login/keep/david.ferguson
```

---

## Tornado (Server-side)

References

- [Tornado Setup Guide](https://itekblog.com/centos-tornado-installation/)
- [Tornado WSGI](https://www.tornadoweb.org/en/stable/wsgi.html)
- [Tornado Best Web Framework](http://blog.zaremba.ch/2013/01/25/tornado___the_best_web_framework.html)
- [Tornado Web](https://github.com/tornadoweb/tornado/blob/master/tornado/ioloop.py#L52)
- [Python Tornado Guide](https://www.velotio.com/engineering-blog/python-tornado-guide)
- [Flask vs Tornado](https://codeahoy.com/compare/flask-vs-tornado)

!!! info "This file gets loaded to:"
    `/var/www/html/tornado_server.py`
    ```sh title="Using"
    sudo vi /var/www/html/tornado_server.py
    ```
    ```sh title="Run with:"
    sudo python /var/www/html/tornado_server.py
    ```

```python title="Add the following:"
import tornado.ioloop
import tornado.web

class MainHandler(tornado.web.RequestHandler):
   def get(self):
      self.write("Hello, world")

application = tornado.web.Application([
   (r"/", MainHandler),
])

if __name__ == "__main__":
   application.listen(8888)
   tornado.ioloop.IOLoop.instance().start()
```

!!! info "2023.01.25: After updating to Mac OS Ventura 13.2, ssh breaks."
    [SSH Issue After Updating MacOS Ventura](https://apple.stackexchange.com/questions/450392/ssh-issue-after-updating-mac-os-13)
    ```sh
    ~/.ssh/config
    ```
    ```
    Host *
    PubkeyAcceptedKeyTypes=+ssh-rsa
    ```

```sh title="Run Tornado:"
sudo python /var/www/html/tornado_server.py
```

---

## Certificate Renewal

Reference Ticket

- CHG0321708

Cert arrived as a compressed file "slicercardinalhealthnet.7z", with a password sent in a separate email.

!!! info ""
    Install on Mac, the app "Extractor - Unarchive Files" from the app store (free)

    In terminal on mac, go to where the pfx file is unzipped:

```sh
cd /Users/<USER>/Downloads/20240112
xxd slicercardinalhealthnet.pfx
```

Prints text. Copy all in terminal, paste into a text document, clean up the start and end to remove prompt lines

### On Slicer Server

```sh title="Create certificate directory:"
sudo su
mkdir /etc/pki/tls/private/20240112
cd /etc/pki/tls/private/20240112
vi slicercardinalhealthnet.txt
```

```sh title="Insert cleaned certificate content, then convert PFX:"
cat slicercardinalhealthnet.txt | xxd -r >slicercardinalhealthnet.pfx
```

!!! info "Make Cert and Key files from PFX"
    - [Convert a PFX Certficate file on Linux Server](https://stackoverflow.com/questions/8774574/how-can-i-convert-a-pfx-certificate-file-for-use-with-apache-on-a-linux-server)
    - [Converting PFX Certficate Bag to Apache SSL](https://www.jasonheckman.com/technology/converting-a-pfx-certificate-bag-to-use-with-apache-ssl/)

```sh title="Convert PFX"
openssl pkcs12 -info -in slicercardinalhealthnet.pfx
openssl pkcs12 -in slicercardinalhealthnet.pfx -clcerts -nokeys -out slicer.cert
openssl pkcs12 -in slicercardinalhealthnet.pfx -nocerts -nodes -out slicer.key
openssl rsa -in slicer.key -outform PEM -out slicer_pem.key
openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -out slicer_cabundle.pem
openssl pkcs12 -in slicercardinalhealthnet.pfx -cacerts -nokeys -nodes -out domain-ca.crt
```

```sh title="Move certificates to the correct location:"
cp domain-ca.crt /etc/pki/tls/private/domain-ca.crt
cp slicer.cert /etc/pki/tls/private/slicer.cert
cp slicer.key /etc/pki/tls/private/slicer.key
cp slicer_cabundle.pem /etc/pki/tls/private/slicer_cabundle.pem
```

```sh title="Set permissions:"
chmod 600 /etc/pki/tls/private/slicer.cert
chmod 600 /etc/pki/tls/private/slicer.key
chmod 600 /etc/pki/tls/private/domain-cabundle.pem
```

```sh title="Verify and restart Apache:"
openssl verify /etc/pki/tls/private/slicer.cert
service httpd restart
```

```sh title="Check certificate validity:"
openssl s_client -servername slicer.cardinalhealth.net -connect slicer.cardinalhealth.net:443 2>/dev/null | openssl x509 -noout -dates
```
