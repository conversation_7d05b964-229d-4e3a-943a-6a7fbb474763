# Install Packages

## **Installing Required Packages**

### **LDAP Login Support**

```sh
sudo yum install -y python-ldap
```

### **Network Scanner**

```sh
sudo yum install -y nmap
```

### **Timezone Library**

```sh
sudo yum install -y pytz
```

### **Scientific Computing Libraries**

```sh
sudo yum install -y numpy scipy
```

### **Tornado Web Server**

```sh
sudo yum install -y python-tornado
```

### **Requests Library**

```sh
sudo yum install -y python-requests
```

### **New LDAP Method (2023.09.05)**

```sh
sudo yum install openldap-clients
```

### **Set Proper File Ownership**

```sh
sudo chown -R apache:apache /var/www/slicer
sudo chown -R apache:apache /var/www/slicer/login
```

---

## **Security Issue: PolicyKit, Polkit Vulnerability (PwnKit) (CVE-2021-4034)**

!!! warning "2022.01.16 - Security Fix"
    PolicyKit (Polkit) vulnerability, also known as **PwnKit**, needs to be mitigated by disabling privilege escalation.

```sh title="Check current permissions"
ls -l /usr/bin/pkexec
```

???+ info "Expected Output"
    ```
    -rwsr-xr-x. 1 root root 23576 Apr  1  2020 /usr/bin/pkexec
    ```

```sh title="Fix: Disable Privilege Escalation"
sudo chmod 0755 /usr/bin/pkexec
```

```sh title="Verify Changes"
ls -l /usr/bin/pkexec
```

???+ info "Expected Output After Fix"
    ```
    -rwxr-xr-x. 1 <USER> <GROUP> 23576 Apr  1  2020 /usr/bin/pkexec
    ```

---
