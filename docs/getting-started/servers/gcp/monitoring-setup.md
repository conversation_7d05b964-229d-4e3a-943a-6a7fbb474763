# Monitoring Setup

## **NewRelic**

📅 **Date:** 2022.02.08

!!! info ""
    > Hello Project Owners,
    >
    > Attached is a list of production cloud instances which are not currently being monitored by New Relic. Cardinal standard is that all production instances be monitored by New Relic. To bring these instances into compliance we are submitting a change control to push the New Relic client to these instances on 2/19. This should be transparent to the instance and does not require a reboot unless the instance is running 32-bit windows OS in which case the instance will be rebooted.
    >
    > If the timing of this activity is in conflict with other activities planned for the instance you may use the self installation tools located here [https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts](https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts). If there are any issues with the installation please submit a request to EITSS-MONITORING.
    >
    > If there is a technical reason where New Relic cannot be installed on this instance please reach out to the Infrastructure Security and Governance team to document this issue in as an Archer item. Any other questions can be sent directly to me.
    >
    > Thank you for your compliance.
    >
    > -<PERSON>
    >
    > Sr. Engineer, Infrastructure Security and Governance

For **self-installation** tools, refer to:
🔗 [New Relic Installation Scripts](https://github.com/CardinalHealth/esm_self_service/tree/master/new%20relic/installation%20scripts)

For **issues with installation**, submit a request to **EITSS-MONITORING**. If New Relic cannot be installed for technical reasons, document the issue as an **Archer item**.

???+ warning "NewRelic License Key"
    ```
    mac-mgmt-pr-cah
    8919c2c426b3b41c45e5bb2f0b429d8bb4beNRAL
    ```

### **Installation Steps**

```sh
# Clone the NewRelic installation repository
git clone https://github.com/CardinalHealth/esm_self_service.git
```

### **Verify NewRelic Monitoring**

View instance status at:
🔗 [NewRelic One Dashboard](https://one.newrelic.com/nr1-core?account=3414131&state=86ac0ddb-8f36-c143-731d-abe2c824da73)

???+ warning "NewRelic User Access"

    ```
    david.ferguson
    Nrbud+1!
    ```

---
