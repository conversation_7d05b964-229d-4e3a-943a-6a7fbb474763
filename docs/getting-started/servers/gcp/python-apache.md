# Python Apache

### **Python on Apache**

- [UnixMen: Set up Python scripting for Apache](https://www.unixmen.com/how-to-set-up-python-scripting-for-apache/)
- [Stack Overflow: Running Python script from Apache](https://stackoverflow.com/questions/26829387/easiest-way-to-run-python-script-from-apache)

### **WebSockets in JavaScript**

- [MDN: Writing WebSocket Client Applications](https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_client_applications)

---

## **Firewall and IP tables notes (not used)**

#### **List open ports**

```sh
sudo firewall-cmd --list-ports
```

#### **Enable HTTPS and disable HTTP**

```sh
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --remove-service=http
sudo firewall-cmd --permanent --remove-port=80/tcp
sudo firewall-cmd --reload
```

#### **Check and modify iptables rules**

```sh
sudo iptables -L
sudo iptables -A INPUT -p tcp --destination-port 80 -j DROP
sudo firewall-cmd --reload
```

___

## **Apache**

[Apache Redirect to HTTPS](https://www.namecheap.com/support/knowledgebase/article.aspx/9821/38/apache-redirect-to-https/https://www.guru99.com/apache.html)

:x: Not Used:

```sh
vi /etc/httpd/conf/httpd.conf
```

!!! info "Add to end"
    ```
    ServerSignature Off
    ServerTokens Prod
    ```

!!! note "Install mod_wsgi"
    ```sh
    sudo yum install mod_wsgi
    sudo systemctl restart httpd
    sudo httpd -M | grep wsgi
    ```
