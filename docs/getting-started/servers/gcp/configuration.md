# Configuration

## **Production Configuration**

```sh
gcloud config set project mac-mgmt-pr-cah
gcloud deployment-manager deployments list
```

???+ info "Example Output"
    ```
    NAME            LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS
    jamf-pro-prod   update               DONE                 manifest-1584487098797  []
    tech-help-prod  insert               DONE                 manifest-1578490211746  []
    ```

```sh title="To SSH into the server:"
gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip
```

```sh title="When that log in eventually does not work, due to an expiration, then use a new name, like:"
gcloud compute ssh david.ferguson2@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip
```

---

## **Non-Production Configuration**

```sh
gcloud config set project mac-mgmt-np-cah
gcloud deployment-manager deployments list
```

???+ info "Example Output"
    ```
    NAME               LAST_OPERATION_TYPE  STATUS  DESCRIPTION  MANIFEST                ERRORS
    graylog-dev        update               DONE                 manifest-1551531702436  []
    jamf-pro-dev       insert               DONE                 manifest-1552401100556  []
    jamf-pro-frontend  insert               DONE                 manifest-1551992656221  []
    kolide-fleet-dev   insert               DONE                 manifest-1550851334781  []
    ldec5009slicr01    insert               DONE                                         [MANIFEST_EXPANSION_USER_ERROR]
    puppet-master-dev  insert               DONE                 manifest-1571256331515  []
    resillio-dev       insert               DONE                 manifest-1556562646161  []
    ```

```sh title="To describe a specific deployment:"
gcloud deployment-manager deployments describe my-deployment
```

---

## **Web View of Deployments**

[Google Cloud Deployment Manager Quickstart](https://cloud.google.com/deployment-manager/docs/quickstart)

Click the link that says **"Go to Deployment Manager"**:
[Google Cloud Deployment Manager Console](https://console.cloud.google.com/dm/deployments?project=_&_ga=2.98910556.316027019.**********-303727487.**********)

---

## **New Production VM Setup**

To create a new instance, execute the following steps in a terminal window on your PC:

[Google Cloud Deployment Manager Quickstart](https://cloud.google.com/deployment-manager/docs/quickstart)

### **Step 1: Set the Correct Project**

```sh
gcloud config set project mac-mgmt-pr-cah
```

### **Step 2: Name Your Instance**

Refer to the [GCP Naming Standards](https://wiki.cardinalhealth.net/GCP_Naming_Standards/Hostnames).

Example:

```sh
lpec5009slicr01
```

### **Step 3: Manage Service Accounts**

Service accounts define how VMs obtain permissions.

[Google Cloud IAM Service Accounts](https://cloud.google.com/iam/docs/creating-managing-service-accounts)

```sh
gcloud iam service-accounts list
```

```sh
gcloud iam service-accounts create pi-mgmt-pr-slicer-main \ --description="pi-mgmt-pr-slicer-main" \ --display-name="pi-mgmt-pr-slicer-main"
```

!!! info "Generated Service Account"
    ```sh
    <EMAIL>
    ```

### **Step 4: Create with YAML**

```sh
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer
gcloud deployment-manager deployments create lpec5009slicr01 --config config_lpec5009slicr01.yaml --project mac-mgmt-pr-cah
```

!!! info "After deployment, update the **boot disk** in the GCP console"
    Add the tag `"snapshots"` with a value of `"h0_d10-2"` to enable hourly snapshots of trhe disk.

### **Step 5: Access (maybe needed)**

!!! note
    You may need to be added to the `"A-MacSysAdmins"` group.
    (**Russ L.** owns it, **Matt W.** asked for it for **David F.**).

### **Step 6: Fix SSH Access Issues (if fails)**

If the "Remote into it via SSH" in below here fails, then toggle the **OS Login** on then off, like:

```sh title="Turn ON"
gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=TRUE
```

```sh title="Turn OFF"
gcloud compute instances add-metadata lpec5009slicr01 --metadata enable-oslogin=FALSE
```

### **Step 7: Remote into the Instance via SSH**

```sh
gcloud compute ssh david.ferguson@lpec5009slicr01 --zone us-central1-c --project mac-mgmt-pr-cah --internal-ip
```

### **Step 8: Retrieve Instance IP Address**

```sh
ip addr | fgrep inet
```

!!! info "Example Output"
    ```ip
    ***********
    ```

---
