# Bucket Storage

## **Connection to Bucket Storage in GCP**

[Google Cloud Storage Guide](https://cloud.google.com/dataprep/docs/concepts/gcs-buckets?hl=en_US&_ga=2.*********.-**********.**********)

**Step 1: Initialize GCloud on Slicer Server**

```sh
gcloud init
```

**Step 2: Select Service Account**

```sh
# Choose the correct service account for bucket access
<EMAIL>
```

**Step 3: Verify Connection**

```sh
gcloud compute instances list
gcloud auth list
gcloud storage buckets list
```

This ensures **secure storage access** for **Slicer** in **Google Cloud Platform**.

[GCS Buckets](https://cloud.google.com/dataprep/docs/concepts/gcs-buckets?hl=en_US&_ga=2.*********.-**********.**********)

---

## **Firestore Setup**

You must have these permissions in GCP to see the interface in GCP:

- `appengine.applications.get`
- `datastore.entities.get`
- `datastore.entities.list`

## **Things that do not work yet**

One-liner create fails, but provides useful information
As of **2021.03.26**, this gets us a small CentOS7 server in **np (non-production)**:

#### **Point to the correct project**

```sh
gcloud config set project mac-mgmt-np-cah
```

#### **Create the VM with command line (no yaml)**

```sh
gcloud deployment-manager deployments create ldec5009slicr01 --composite-type management-cah/composite:vm --properties name:ldec5009slicr01,region:'us-central1' --project mac-mgmt-np-cah
```

- This creates the instance, but it **cannot be accessed via SSH**.
- However, it does show the **service account** in the GUI:

  ```
  <EMAIL>
  ```

#### **Check VM details**

```sh
gcloud deployment-manager deployments describe ldec5009slicr01
```

### **To remove the instance**

!!! warning "Use caution before deleting deployments"
    The following command is commented out to prevent accidental deletion.
    Uncomment and execute if removal is necessary.

    ```sh
    # gcloud deployment-manager deployments delete ldec5009slicr01
    # gcloud deployment-manager deployments delete lpec5009slicr01
    ```

---
