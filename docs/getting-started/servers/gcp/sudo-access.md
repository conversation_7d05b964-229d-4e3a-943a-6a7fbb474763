# Sudo Access

**Request permission** via **ServiceNow**.

**Service account** on the **Chef production server** (where <PERSON> has sudo):

  ```sh
  <EMAIL>
  ```

**Has SSH key added in GCP GUI panel**.
**<PERSON><PERSON> works freely**.

---

## **Troubleshooting OS Login Issues**

```sh
"Using OS Login user [david_ferguson_cardinalhealth_co] instead of requested user [david.ferguson]"
```

---

## **Manually add SSH key to VM**

Edit the VM and enter the SSH key from **prod**:

``` sh
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDVkewljS/EuebHOO7fTjX3TSsdjYPqxBnEhzjXxDiNw3PNXRNnaSc8h4KcdUR+QUNjlVhSHbOmJzMkFPjSy1XOADi9ax+DsG64NlKcTOvS7NeNFBw1f3UqV58lQTH7xQhL7TcGhJUhopCRt/O54GfUD4qiku8Be9D+XluyJsMBNaTJX2dmnseSp+8GKq2JUQn/jBv13Of43lyUpDTxmzKq4yyOANwKyWDcwoQ3zu3f57qkTij5tTQm9OCSq+oBXYC4iMnWMwtnltY2mhZda0BTGu/8wmf+TRDQAr6F9Env7HPzhRow6inU6qUoEch/orIOyvITFv0PA5C2TWEjG7+cyPPpilVrJ67+kDx7bEu2/LrEhlUH6myj58K4Jg7yCFR7lzBe/C9E0OqinLoeqfHowBDvImAtDx3QXwVBorMPe0PaKuwAVuOY8aXmxUh+S9cSbkIPOxVCraCyjmIyIhAdkkrPbmMn6/ewmHM4xJzoCr2YctXj5mgISRkMEbSc+l8= <EMAIL>

```

For reference:
[Google Cloud: Managing Instance Access](https://cloud.google.com/compute/docs/instances/managing-instance-access)

---
