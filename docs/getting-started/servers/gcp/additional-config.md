# Additional Config

### **Additional Configurations**

```sh
cat /etc/httpd/conf/httpd.conf
cp
```

### **Install Python3 and Required Libraries:**

```sh
sudo yum update -y
sudo yum install -y python3
python3 --version
sudo yum install traceroute
sudo yum install sshpass
```

---

!!! info "Can not see it with the internal address (from any place other than the server itself)"
    ```sh
    ping lpec5009slicr01.c.mac-mgmt-pr-cah.internal
    ```

[Firewall Rules for GCP Instances](https://wiki.cardinalhealth.net/GCP_Naming_Standards/FirewallRules)

```sh
gcloud config set project mac-mgmt-pr-cah
gcloud deployment-manager deployments list
gcloud deployment-manager deployments describe lpec5009slicr01
gcloud compute firewall-rules list --project mac-mgmt-pr-cah --format="table(selfLink)"
(empty)
```

???+ info "Network tags that provide rules:"
    [GCP Create VM Instance](https://wiki.cardinalhealth.net/GCP_Create_VM_Instance)

    Remove the rule called `edge` also `glb` also `internal-smb`, save and reboot
    (updated the yaml, to be clean for these tags)
---

### **Python on Apache (mod_wsgi)**

[HowToForge Guide](https://www.howtoforge.com/tutorial/python-apache-mod_wsgi_ubuntu/)

```sh
sudo yum install -y apache2-utils ssl-cert libapache2-mod-wsgi-py3
```

```sh
sudo vi /var/www/html/wsgy.py
```

```python title="Paste the following:"
import json

def application(environ, start_response):
    status = '200 OK'
    html = '''
    <html>
    <body>
    <div style="width: 100%; font-size: 40px; font-weight: bold; text-align: center;">
    Welcome to mod_wsgi Test Page
    </div>
    </body>
    </html>
    '''
    response_header = [('Content-type','text/html')]
    start_response(status, response_header)
    return str(environ['QUERY_STRING'])
```

```sh
sudo chown apache:apache /var/www/html/wsgy.py
```

```sh
vi /etc/apache2/conf-available/wsgi.conf
sudo vi /etc/httpd/conf.d/python-wsgi.conf
```

```apache title="Paste:"
WSGIScriptAlias /wsgi /var/www/html/wsgy.py
```

```sh
sudo systemctl restart httpd
```

### **Test WSGI**

- Open **[https://slicer.cardinalhealth.net/wsgi](https://slicer.cardinalhealth.net/wsgi)**
- Test with parameters: **[https://slicer.cardinalhealth.net/wsgi?parametersGoHere](https://slicer.cardinalhealth.net/wsgi?parametersGoHere)**

---

### **Monitor Check-in Receiver**

Refer to **`slicer_wsgi_checkin.py`** for details.

---

### Connection to persistent disk in GCP VM

[GCP VM Console](https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false)

```sh title="Disc created in Cloud Console"
[david.ferguson2@lpec5009slicr01 html]$ lsblk
NAME   MAJ:MIN RM  SIZE RO TYPE MOUNTPOINT
sda      8:0    0   30G  0 disk
├─sda1   8:1    0  200M  0 part /boot/efi
└─sda2   8:2    0 29.8G  0 part /
sdb      8:16   0   60G  0 disk
```

```sh
sudo mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/sdb
[y/N]: y

sudo mkdir -p /mnt/disks/SSD
sudo mount -o discard,defaults /dev/sdb /mnt/disks/SSD
sudo chmod a+w /mnt/disks/SSD

sudo cp /etc/fstab /etc/fstab.backup
sudo blkid /dev/sdb
```

```sh
/dev/sdb: UUID="736ba877-71b1-43c5-93e3-e7676892e65b" TYPE="ext4"
```

```sh
sudo vi /etc/fstab
UUID=736ba877-71b1-43c5-93e3-e7676892e65b /mnt/disks/SSD ext4 discard,defaults,nofail 0 2

sudo chmod 755 /mnt
sudo chmod 755 /mnt/disks

sudo chown -R apache:apache /mnt/disks/SSD
sudo chmod 777 /mnt/disks/SSD

sudo mkdir /mnt/disks/SSD/var
sudo chown -R apache:apache /mnt/disks/SSD/var
```

## Set root password to never expire

```sh
sudo chage -M -1 root
sudo chage -M -1 david.ferguson
```

## Data view and delete NCDU

[Install NCDU on Red Hat](https://unix.stackexchange.com/questions/3979/how-can-i-install-ncdu-on-red-hat)

=== "On Mac:"
    [NCDU](https://dev.yorhel.nl/ncdu)

    Download the `gz`

    ```sh
    xxd ~/Downloads/ncdu-1.16.tar.gz > ncdu-1.16.tar.gz.xxd
    cat ncdu-1.16.tar.gz.xxd
    ```
    (Copy from terminal, paste into text editor, trim off extra lines)

=== "On Slicer Server:"

    ```sh
    mkdir /home/<USER>/install_ncdu
    cd /home/<USER>/install_ncdu
    vi ncdu-1.16.tar.gz.xxd
    ```
    (Paste contents of the ncdu-1.16.tar.gz.xxd)

    ```sh
    cat ncdu-1.16.tar.gz.xxd | xxd -r >ncdu-1.16.tar.gz
    tar -xzvf ncdu-1.16.tar.gz
    cd ncdu-1.16
    sudo yum install -y gcc
    ./configure --prefix=/usr
    make
    sudo make install
    ```

**Usage:**

```sh
sudo ncdu /
sudo ncdu /var/log/slicer
```

```sh
sudo yum clean all
sudo rm -rf /var/cache/yum
```

**Log Cleanup (Log is 14.4 GB on 2021.09.27):**

```sh
sudo rm /var/log/httpd/access_log
```

???+ warning "Rotate Logs (Rotation was not working since June 2021)"
    [Changing Apache Log Rotation Behaviour](https://electrictoolbox.com/changing-apache-log-rotation-behaviour/)

```sh
sudo vi /etc/logrotate.conf
```

(Change to only 7 days of logs)

=== "New"

    Rotate log files daily
    ```
    daily
    ```

    Keep 7 days worth of backlogs
    ```
    rotate 7
    ```

=== "Old"

    Rotate log files weekly
    ```
    weekly
    ```

    Keep 4 weeks worth of backlogs
    ```
    rotate 4
    ```

```sh title="Run Rotation"
sudo logrotate --force /etc/logrotate.conf
```

```sh
sudo cat /var/log/cron
```

## **User Access and Password Expiry Check**

Shows that **June 6** was the last clean run, which coincides with login failures and the need to create a new persona to log into **gcloud** for Slicer.

```sh
sudo chage -l root
sudo passwd root
```

???+ info "Root Password Expiry Details"
    ```
    [david.ferguson2@lpec5009slicr01 ~]$ sudo chage -l root
    Last password change                : Mar 11, 2021
    Password expires                     : Jun 09, 2021
    Password inactive                     : never
    Account expires                       : never
    Minimum number of days between password change  : 0
    Maximum number of days between password change  : 90
    Number of days of warning before password expires   : 7
    ```

**Jamf Password Expiry Details**

```sh
[david.ferguson2@lpec5008jamfa01 ~]$ sudo chage -l root
```

???+ info "Jamf Password Expiry Details"
    ```
    Last password change                : Sep 20, 2021
    Password expires                     : never
    Password inactive                     : never
    Account expires                       : never
    Minimum number of days between password change  : 0
    Maximum number of days between password change  : -1
    Number of days of warning before password expires   : 7
    ```

---
