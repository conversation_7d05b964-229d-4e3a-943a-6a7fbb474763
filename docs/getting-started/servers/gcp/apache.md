# Apache

## **Install Apache**

[DigitalOcean Guide: How to Install Apache on CentOS 7](https://www.digitalocean.com/community/tutorials/how-to-install-the-apache-web-server-on-centos-7)

```sh title="Update and Install Apache"
sudo yum update httpd
sudo yum install httpd
```

```sh title="Enable and Start Apache"
sudo systemctl enable httpd
sudo systemctl start httpd
sudo systemctl status httpd
```

```sh title="Check Default Configuration"
cat /etc/httpd/conf.d/welcome.conf
```

```sh title="Set Up a Test Page"
sudo su
echo "Slicer Server" > /var/www/html/index.html
chmod 644 /var/www/html/index.html
ls -l /var/www/html/index.html
exit
```

Access the server:

- `http://10.50.40.47`
- `https://10.50.40.47`

---

## **Configure WSGI Modules**

Load up the `slicer_wsgi_*` modules.

To prioritize the **index wsgi page**, update Apache's configuration:

```sh title="Edit Apache Configuration"
sudo vi /etc/httpd/conf/httpd.conf
```

Modify the **DirectoryIndex** section to look like this:

```
#
# DirectoryIndex: sets the file that Apache will serve if a directory
# is requested.
#
<IfModule dir_module>
    DirectoryIndex index index.html
</IfModule>
```

```sh title="Restart Apache"
sudo systemctl restart httpd
```

---
