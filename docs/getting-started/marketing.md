# Marketing Overview

## What is it?

- **General Purpose Computer**
  _(Can easily run any web page)_

- **Small**
  _(The size of a credit card and only one inch thick)_

- **Low Cost**
  _($100 all in—just add a monitor, keyboard, and mouse)_

- **Low Power**
  _(Consumes only 5 Watts—about the same as a night light; can run a full shift on a 60 WHr battery)_

- **Low Maintenance**
  _(No fans, no spinning disks)_

---

## What Can it Do?

- **Run any website**
  _(Ideal for kiosks, display boards, metrics boards, ERP, VOE)_

- **Replace a Desktop**
- **Replace a Laptop**
- **Replace a Thin Client**
- **Interface to sensors and provide central visibility**

---

## What Have We Done?

### **Cardinal PI OS**

- Secure private web browsing as the only function
- Allow list enforced _(Work sites only)_
- Option to auto-launch a site _(Display boards)_
- Option to auto-logout _(VOE and ERP systems)_

### **Slicer**

- Central Configuration and Management Portal

---

## Small Deployment

- Have many of them ready, standing by, for **VOE** uses.

---

## Where Can it Be?

- Anywhere on the **CAH wired network**
- Where available, on the **IoT WiFi network**

---

## Why the Pi?

- **Lowest cost tech refresh available**
- **Regional RCS configurable**

---

## Where to Buy Pi?

[Now In Stock](https://www.nowinstock.net/computers/developmentboards/raspberrypi/)

---

**End of document.**
