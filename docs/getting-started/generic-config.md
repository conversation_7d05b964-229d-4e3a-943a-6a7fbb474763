---
title: Generic Configuration
authors:
    - <PERSON>
    - <PERSON>
version: 1.0.0
---

# Generic Configuration Guide

This document outlines the steps for configuring and resetting a system to its default state.

## System Cleanup Commands

```sh
sudo su

# Remove unnecessary files and stop services
rm -rf /Downloads
rm -rf /home/<USER>/*
rm -rf /cardinal/save_values

systemctl stop pi-hmi.service
systemctl stop pi-runner.service
systemctl stop pi-config.service

# Reset organization logo
rm /cardinal/organization_logo.gif
echo 47494638376101000100910000000000ffffff00000000000021f90409000002002c00000000010001000002024c01003b >/cardinal/organization_logo.xxd
cat /cardinal/organization_logo.xxd | xxd -p -r >/cardinal/organization_logo.gif
rm /cardinal/organization_logo.xxd

# Clean up logs
find /var/log -type f -regex ".*\.gz$" -delete
find /var/log -type f -regex ".*\.[0-9]$" -delete
rm /var/log/*
echo 'yes' > /cardinal/needsexpand
echo -n "0" > /cardinal/boot_count.txt
echo -n "0" > /cardinal/grab_count.txt
cp /cardinal/browserstart_default /cardinal/browserstart
rm -rf /cardinal/localhtml/*
rm -rf /cardinal/log/*

# Remove configuration files
rm -rf /cardinal/config_*
rm /cardinal/call_home_locations.txt
rm /cardinal/wifi_ssid_psk.txt
rm /cardinal/screen_resolution.txt
rm /cardinal/screen_zoom.txt
rm /cardinal/wifi_config.txt
rm /cardinal/TempCert.p12
rm /cardinal/wifi_settings.txt
rm -rf /cardinal/corp_certs

# Reset call home locations
echo "['']" >/cardinal/call_home_locations.txt
```

## Creating Startup Page

```sh
mkdir /cardinal/localhtml/
echo '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>' > /cardinal/localhtml/index.html
```

## System Shutdown

```sh
sudo shutdown -h now
```

## Creating an Image

```sh
Downloads/20221102/slicer_pi_image_2.4.1.SP.36_generic.zip
```

## Configuring for CAH

- Set the call home location:

```sh
config: set the call home to ['https://slicer.cardinalhealth.net']
```

- Load `pi_organization` and start it (Slicer pull).
- Remove the WiFi config, so that it can use old style Corp and IoT:

```sh
rm /cardinal/wifi_config.txt
```

## Testing Updates

- Load an old image, then pull `pi_organization` and verify functionality.
- Pull the latest networking updates and ensure everything is functioning as expected.

---
**End of document.**
