# Knowledge

A place to hold the key pieces of information about the project.

## Repository

!!! note
    The `README.md` file is meant to be viewed on GitHub (.md is Markdown formatting). You can access the repository here: [GitHub Repository](https://github.com/CardinalHealth/cs_sp_pi_slicer)

**Login with the CAH GitHub-specific username**, such as:

```sh
cah-david-ferguson
```

## Google Cloud Commands

To modify metadata for the instance `lpec5009slicr04` in `us-central1-a`:

```sh title="Disable OS Login"
gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=FALSE
```

```sh title="Enable OS Login"
gcloud compute instances add-metadata lpec5009slicr04 --zone us-central1-a --metadata enable-oslogin=TRUE
```

## APMs

  ``` title="Raspberry Pi"
  APM0027587
  ```

  ``` title="Slicer"
  APM0028269
  ```

## Filename Prefixes

The project follows a structured naming convention for files:

### `AA-*`

- Documentation for developers.
- Named `AA` so they appear at the top of the file list.

### `config_*`

- Files needed to configure the project.

### `datastore_*`

- Captures of the Slicer datastore content.

### `offline_*`

- Tools used outside of Pi or Slicer (meant for developer use).

### `pi_*`

- Source code files loaded onto the Raspberry Pi.
- See `AA-pi-applications.md` for details.

### `read_*`

- Text files meant for users to read.

#### `how_to_*`

  - Instructional documents.
  - Uploaded to Slicer for user downloads.

#### `release_notes_*`

  - Explains changes in new releases.

Example:

```sh
read_release_notes_slicer_pi.md
```

- Contains release notes and definitions of service packs (SP).

### `slicer_wsgi_*`

- Source code files loaded onto the main Slicer server.

---
**End of document.**
