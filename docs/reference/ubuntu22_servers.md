---
title: Ubuntu 22 Server Setup
description: Instructions for setting up Ubuntu 22 servers with required configurations
---

# Ubuntu 22 Server Setup

## System Requirements

- Ubuntu 22
- 2 cores
- 4GB RAM
- 20GB disk
- Networking: bridged

---

## Initial Setup

When installing Ubuntu 22, take the default options but ensure SSH is installed.

---

## System Configuration

### Python Setup

```bash
sudo su
apt install -y python3-pip
exit
```

### Required Python Packages

```bash
pip install pexpect
```

### Firewall Configuration

1. List available firewall applications:
   ```bash
   sudo ufw app list
   ```

2. Allow SSH connections:
   ```bash
   sudo ufw allow OpenSSH
   ```

3. Enable the firewall:
   ```bash
   sudo ufw enable
   ```
   > **Note**: This command may disrupt existing SSH connections. You will be prompted to proceed.

4. Verify firewall status:
   ```bash
   sudo ufw status
   ```

## Additional Configuration

For additional configuration details, refer to the `slicer_wsgi_loader.py` file.
