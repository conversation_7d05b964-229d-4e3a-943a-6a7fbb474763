# Data Migration Guide

---

## Migrating Run Data to a New Server

Follow these steps to migrate data from an old server to a new one:

### **Tasks That Can Be Done Anytime**

1. **Snapshot the datastore on the old server**
   - Check the snapshot into **GIT**.
   - Load the snapshot onto the **new server** to populate all profiles.
   - This ensures that **multimedia loads can happen** properly.

2. **Migrate Device Media Content**
   - Retrieve media content from the **multimedia page**.
   - Download the content to a temporary location *(e.g., OneDrive?)*.
   - Upload the content to the **new server**.

---

### **Final Step**

- **Take another datastore snapshot on the old server**.
- **Check it into GIT** and **load it onto the new server**.

This ensures all the latest data is transferred successfully.

---
**End of document.**
