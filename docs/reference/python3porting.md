---
title: Python 3 Porting Guide
description: Common issues and solutions when porting Python 2 code to Python 3\
---

# Python 3 Porting Guide

!!! info "About this guide"
    This document highlights common issues encountered when porting Python 2 code to Python 3, with practical solutions for each problem.

## String Handling Issues

### Explicit Encoding with `.encode()`

!!! warning "Type Mismatch"
    Certain operations require explicitly encoding strings to avoid type mismatches in Python 3.

```python
# Python 2
my_string = "hello"
some_function(my_string)  # Worked fine

# Python 3
my_string = "hello"
some_function(my_string.encode('utf-8'))  # Explicit encoding needed
```

### Byte String Operations with `split()`

When working with byte strings, methods like `split()` must be used with byte-like objects:

```python
# This won't work in Python 3
# byte_string.split('\n')  # TypeError: a bytes-like object is required

# Correct approach
byte_string = b"line1\nline2\nline3"
split_lines = byte_string.split(b'\n')  # Returns a list of byte strings
```

## Module Import Changes

### `urllib` Restructuring

!!! note "Module reorganization"
    Many modules were reorganized in Python 3, with `urllib` being significantly restructured.

=== "Python 3 :material-checkbox-marked-circle:"
    ```python
    from urllib.parse import parse_qs
    ```

=== "Python 2 :material-clock-outline:"
    ```python
    from cgi import parse_qs
    ```

!!! danger "JSON Serialization Issue"
    Using `parse_qs` in Python 3 results in a dictionary where all strings are **bytes**, which `json.dumps()` does not handle well.

**Solution:** Decode the request body before passing it to `parse_qs`:

```python
# Fix JSON serialization issues
d = parse_qs(request_body.decode("utf-8"))
```

## Additional Resources

- [Python 3 Porting Guide (External)](http://python3porting.com/problems.html)
- [Official 2to3 Documentation](https://docs.python.org/3/library/2to3.html)
- [Six - Python 2 and 3 Compatibility Library](https://pypi.org/project/six/)

*[JSON]: JavaScript Object Notation
