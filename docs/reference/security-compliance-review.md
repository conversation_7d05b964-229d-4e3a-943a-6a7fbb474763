# Security Compliance Review

As of **2021.08.26**, this is a review of the current state of the recommended security notes.

The input to this review comes from **<PERSON>**, in an email on **August 12, 2021**, and is attached to **Jira card EUDE-773**. The goal of this section is to list out each requirement and provide a place for response/evidence that the requirement is either currently met or is being worked on.

---

## Security Controls / Requirements

### 1. Device Theft Prevention

> "We must have controls in place that ensure the device itself is not stolen, see Global Security for physical security controls."

**Response:** This is an open item and is anticipated to be addressed by providing a complete 'kit' as an orderable item.

---

### 2. Centralized Management Tool

> "We must use a centralized management tool for device governance including, but not limited to: OS Management, Device Enrollment, Patching, Updates, Enforce Encryption, Inventory."

**Response:** The site **[Slicer](https://slicer.cardinalhealth.net)** provides the central communication and control point for the configuration and update of Raspberry Pis. Currently, device enrollment is fully automatic. Encryption is enforced through **HTTPS** as the sole communication path. Patching and updates are an active backlog item (**EUDE-1328**).

---

### 3. Device Inventory

> "We must maintain an inventory of these devices."

**Response:** ??? This is outside the scope of software.

---

### 4. Network Monitoring

> "We must be able to monitor these devices and their behavior on the network."

**Response:** Devices check in to the **Slicer central site** every minute. Status is available there. **EUDE-1329** will add monitoring of network behavior.

---

### 5. Data Security

> "No Cardinal Health information is stored on the device in any way."

**Response:** The Raspberry Pi operates using the **Chromium browser in incognito mode**, ensuring that when the browser is closed/restarted, no information is saved. There is no capability for downloading, storing, or saving Cardinal Health information.

---

### 6. OS Hardening & Media Theft Prevention

> "We need to ensure that the OS cannot be compromised. Theft prevention of any and all media that is installed on the device, i.e. SD card, etc. Prevent unauthorized OS modification."

**Response:** ??? This is outside the scope of software.

---

### 7. Machine-Level Certificates

> "Machine-level certificates must be leveraged in order to identify the device."

**Response:** **EUDE-1331** confirms this requirement is outdated per email from **Andrew Beardsley**.

---

### 8. Restricted Device Communication

> "Must ensure that controls are in place to ensure that devices cannot communicate with each other directly."

**Response:** Discussions with **Jeff Paugh** indicate that **IOT network-level controls** will fulfill this requirement.

---

### 9. Least Privilege Access

> "Least Privilege access must be followed. The Pi can access only what is needed and nothing else."

**Response:** The **Chromium browser is in incognito mode**, meaning no history is saved. **Privoxy proxy filtering** enforces a strict allowlist of sites tailored to the specific use case.

---

### 10. No Local Password Authentication

> "No password local accounts that can be used to authenticate to the device."

**Response:** Addressed in **EUDE-1333**.

---

### 11. OS Baselines & Workstation Hardening

> "Leverage CIS OS Baselines / Workstation Hardening."

**Response:** The current build includes a **pi_security service**, which uses **lynis** to report system hardening status. **EUDE-1350** will further improve the hardening score.

---
**End of document.**
