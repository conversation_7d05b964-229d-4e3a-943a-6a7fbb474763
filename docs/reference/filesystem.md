# Slicer Filesystem Usage & Layout

---

## **Main Directories**

### **1. WSGI Applications**

```sh
/var/www/html
```

This is the location of all **WSGI apps** running on the server.

### **2. Versioned Service Files**

```sh
/var/www/html/pi_services/(app)/(version)/(app).py
```

- This is where **versioned files** are stored.
- `pi_runner` pulls files from here when needed.
- Files are **initially placed manually**.
- Pis retrieve them via API calls to the **Slicer app** (`download`).

### **3. Static Content**

```sh
/var/www/htmlfiles
```

- Intended for **statically served content** (none as of `2021.05.21`).
- Created so that Apache **serves files away from** `/var/www/html`.

---

## **Log Files & Data Storage**

### **4. Log Directory for Slicer Apps**

```sh
/var/log/slicer/(app)
```

Each **Slicer app** (WSGI-based Python module) has its own log directory.

??? info "Logging Structure"
    - **checkin**:
      ```sh
      /var/log/slicer/checkin/json
      /var/log/slicer/checkin/raw
      ```
      Logs device check-ins and updates.
    - **datadrop**:
      ```sh
      /var/log/slicer/datadrop/json/id/(id)/(service)
      ```
      Stores the most **recently received JSON** data from a device.
      **Example:**
      ```sh
      sudo cat /var/log/slicer/datadrop/json/id/10000000bd6f7a19/runner
      ```
      Returns:
      ```json
      {"service:pi_monitor": "M.2.2", "temperature": "61.3", "Memory:MemFree": "2920873984"}
      ```
    - **Raw Data Logs**:
      ```sh
      /var/log/slicer/datadrop/raw/(YYYYMMDD)/(HH)/(YYYYMMDDHHMMSSmmmuuu)_(id).txt
      ```
      **Example:**
      ```sh
      /var/log/slicer/datadrop/raw/20210526/08/20210526085941378807_100000004e61fc41.txt
      ```
      - **Rollups:**
        ```sh
        /mnt/disks/SSD/var/log/slicer/datadrop/raw/20220728/00/rollup_20000000cf60766f
        ```
      - **Statistical Reports:**
        ```sh
        /mnt/disks/SSD/var/log/slicer/datadrop/stats/id/10000000fc5317fa
        ```

### **5. Key-Value Data Storage**

```sh
/var/log/slicer/datastore
```

- Stores **key-value pairs**.
- Each file represents a **'key'**, and its content is a **JSON-dumped 'value'**.

### **6. Login Logs**

```sh
/var/log/slicer/login
```

??? info "Login Structure"
    - **Persistent Logs** (`keep`):
      ```sh
      /var/log/slicer/login/keep
      ```
      Stores authenticated user sessions.
    - **Temporary Logs** (`throw`):
      ```sh
      /var/log/slicer/login/throw
      ```
      Stores temporary login attempts.
    - **Login Activity Tracking** (`touch`):
      ```sh
      /var/log/slicer/login/touch
      ```
      Logs login touchpoints.
    - **User-Specific Data** (`user`):
      ```sh
      /var/log/slicer/login/user
      ```
      Tracks per-user authentication data.

### **7. Task Management Logs**

```sh
/var/log/slicer/tasks
```

??? info "Task Processing Logs"
    - **Task Status Summary**:
      ```sh
      /var/log/slicer/tasks/status_summary
      ```
    - **Task Request Logs**:
      ```sh
      /var/log/slicer/tasks/(datetime_stamp)/request
      ```
        - Stores **JSON-dumped task requests**.
        - Created by the reports module, dropping the request here, to kick off the process
    - **Task Processing Logs**:
      ```sh
      /var/log/slicer/tasks/(datetime_stamp)/status
      ```
      Text file with the JSON-dumped of the tasks runner work content
    - **Task Completion Logs**:
      ```sh
      /var/log/slicer/tasks/(datetime_stamp)/complete
      ```
      The existence of this file **indicates task completion**.

### **8. File Uploads**

```sh
/var/log/slicer/uploads
```

- **(Currently unused)**
- Intended for future **file upload functionality**.

---

## **Summary Table**

| **Directory** | **Purpose** |
|--------------|------------|
| `/var/www/html` | Hosts WSGI applications |
| `/var/www/html/pi_services` | Stores versioned files for Pis |
| `/var/www/htmlfiles` | Static content serving location |
| `/var/log/slicer` | Central logging directory for Slicer apps |
| `/var/log/slicer/datadrop` | Stores incoming data from Pis |
| `/var/log/slicer/login` | Logs authentication attempts |
| `/var/log/slicer/tasks` | Logs task requests and processing statuses |
| `/var/log/slicer/uploads` | (Future use) File uploads |

---
**End of document.**
