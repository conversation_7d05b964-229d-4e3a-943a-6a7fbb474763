# Slicer Datastore Usage Map

The following table provides a mapping of data usage within the **Slicer** datastore, including data creators, consumers, and destinations.

---

## Data Mapping

| **Prefix**              | **Creator**   | **Consumer**    | **Destination** |
|-------------------------|--------------|----------------|----------------|
| `device_profile_(id)`   | reports      | datadrop       | Used to pull single profile |
| `device_service_(id)`   | reports      | datadrop       | On **Raspberry Pi**: `pi_runner` uses this list, compares it to its running service versions, and pulls any that do not match. The services are then loaded and started/restarted. |
| `device_name_(id)`      | reports      | reports, datadrop | **Reports**: Displays in device list as an additional attribute. <br> **Datadrop**: Goes out with profile information or service versions, tied to `(id)`, like bookmarks. |
| `profile_(name)`        | (manually)   | datadrop       | On **Raspberry Pi**: `pi_runner` saves this information locally in the `/cardinal` directory. The `pi_hmi` service picks it up from that directory to build the landing page, which reloads every **30 seconds**. |

??? info "Datastore Overview"
    - **Reports**: Generates data to track device attributes and configurations.
    - **Datadrop**: Provides a way for Raspberry Pi services to fetch required configurations and updates.
    - **pi_runner**: A key service on Raspberry Pi that ensures services are up to date.
    - **pi_hmi**: Handles the user interface and profile-based landing pages.

---
**End of document.**
