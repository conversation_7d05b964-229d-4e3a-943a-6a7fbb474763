---
title: Coding Bible
description: Best practices and coding standards for our development team
icon: material/book-open-variant
---

# 📖 Coding Bible

!!! quote "Our Philosophy"
    Code is read much more often than it is written. Prioritize clarity and maintainability over cleverness.

## ✨ Code Commandments

Be mindful of your fellow developers and make your code **READABLE**.
Writing clever code hurts the productivity of your team. **Don't be that person.**

- ✅ **If you don't know the answer, ask.**
- ✅ **Before asking a question, do your research.**
- ✅ **Always write tests** – they have saved millions of lives already.

### 🧩 Code Organization

- ✅ **Follow the Single Responsibility Principle** - Functions and classes should do one thing only
- ✅ **Keep functions small and focused** - Ideally under 30 lines
- ✅ **Use descriptive variable and function names** - `calculate_total_price()` instead of `calc()`
- ✅ **Comment on "why", not "what"** - Code shows what, comments explain why

### 🧹 Code Cleanliness

- ✅ **Remove dead/commented-out code** - We have version control for a reason
- ✅ **Avoid nested conditionals** - Extract to helper functions when nesting gets deep
- ✅ **Use consistent formatting** - Follow the team's style guide or use autoformatters
- ✅ **Handle errors gracefully** - Never silently swallow exceptions

!!! tip "Code Review Tip"
    Review your own code first. If you can't explain a part clearly, refactor it.

## 🔄 Version Control Commandments

💡 **Are you making awesome changes to the repository?**
👉 **Branch out!**

💾 **Always commit your changes to GitHub.**

🚀 **Ready to ship? Create a Pull Request and follow the message format.**

🗑️ **Clean up stale branches – just like spoiled food in a fridge.**

### 🌿 Branching Strategy

- ✅ **Branch per feature/bug fix** - Never commit directly to main/master
- ✅ **Keep branches short-lived** - Merge frequently to avoid drift
- ✅ **Use descriptive branch names** - Format: `type/description` (e.g., `feat/user-authentication`)
- ✅ **Rebase before merging** - Keep history clean and linear when possible

### 🔍 Code Review Best Practices

- ✅ **Review changes in small batches** - Aim for PRs under 400 lines
- ✅ **Be kind and constructive** - Focus on the code, not the coder
- ✅ **Respond to reviews promptly** - Don't let PRs languish
- ✅ **Use automated checks** - Let CI/CD catch formatting and basic issues

### ✅ **Pull Request Message Format**

Use the format: `<type>(<optional scope>): subject` for all pull requests.

**Example:**

- **feat**: A new feature
- **chore(deps)**: update packages

## 🏗️ **Commit Message Types**

| **Type**      | **Description** |
|--------------|----------------|
| `build`      | Build-related changes (e.g., npm updates, adding dependencies). |
| `chore`      | Internal code changes (e.g., updating `.gitignore`, `.prettierrc`). |
| `feat`       | A new feature implementation. |
| `fix`        | A bug fix. |
| `docs`       | Documentation updates or improvements. |
| `refactor`   | Code restructuring without fixing bugs or adding features (eg: You can use this when there have semantic changes like renaming a variable/ function name). |
| `perf`       | Performance improvements. |
| `style`      | Styling updates (e.g., formatting, spacing, missing semicolons). |
| `test`       | Adding new test or making changes to existing test. |

## 🛠️ Development Best Practices

### 📊 Testing Guidelines

- ✅ **Write tests first (TDD)** - Understand requirements before coding
- ✅ **Test edge cases** - Empty inputs, large data, invalid formats
- ✅ **Keep tests independent** - No dependencies between test cases
- ✅ **Use descriptive test names** - `test_user_cannot_access_admin_area()`

### 🔒 Security Practices

- ✅ **Never commit credentials** - Use environment variables
- ✅ **Validate all inputs** - Especially user-provided data
- ✅ **Use prepared statements** - Prevent SQL injection
- ✅ **Keep dependencies updated** - Regular security audits

### 🚀 Performance Considerations

- ✅ **Profile before optimizing** - Identify actual bottlenecks
- ✅ **Optimize database queries** - Use indexes and minimize N+1 problems
- ✅ **Consider caching** - For expensive or repetitive operations
- ✅ **Lazy load resources** - Only load what you need, when you need it

!!! warning "Premature Optimization"
    "Premature optimization is the root of all evil" - Donald Knuth.
    Focus on clean, working code first. Optimize only where necessary.

---

🚀 **Follow these guidelines to keep the codebase clean and maintainable!**
