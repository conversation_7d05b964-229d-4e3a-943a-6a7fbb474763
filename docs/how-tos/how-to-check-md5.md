# How to Check an MD5 Hash on a Downloaded File

To verify the integrity of a downloaded file, you can check its **MD5 checksum** against the original value provided by the developer or the download page. Follow the instructions for your operating system below.

=== "Windows"
    1. **Download** the latest version of [WinMD5Free](http://www.winmd5.com/).
    2. **Extract** the downloaded zip file and launch `WinMD5.exe`.
    3. Click **Browse**, navigate to the file you want to check, and select it.
    4. The tool will **automatically display** the file’s MD5 checksum.
    5. Copy and paste the **original MD5 value** provided by the developer or download page.
    6. Click **Verify** to compare the values.

=== "macOS"
    1. **Download** the file you want to check and open the **Downloads** folder in Finder.
    2. Open **Terminal** from **Applications > Utilities**.
    3. Type the following command **without pressing Enter**:
       ```sh
       md5
       ```
    4. **Drag and drop** the downloaded file from Finder into the Terminal window.
    5. Press **Enter**, and wait for the MD5 hash to be displayed.
    6. Open the checksum file provided on the download page (**usually a `.cksum` file**).
    7. Compare the **MD5 hash** in the checksum file with the one displayed in Terminal.
    8. If they match, the file was downloaded successfully. Otherwise, re-download the file.

=== "Linux"
    1. Open a **terminal window**.
    2. Type the following command:
       ```sh
       md5sum [file_name]
       ```
       _Alternatively, you can drag the file into the terminal to insert its path automatically._
    3. Press **Enter**.
    4. The MD5 hash of the file will be displayed.
    5. Compare it with the original MD5 checksum.
    6. If the values match, the file is valid; otherwise, re-download the file.

---
???+ tip "**Pro Tip**: "
    Always verify the checksum of files downloaded from the internet, especially for software, to prevent corruption or tampering!

---
**End of document.**
