# How to Burn Image

## Flashing the SD Card

!!! warning "USB Write Restrictions & Admin Privileges"
    **Cardinal Health Computer Use Policies** may restrict your ability to write to external media. If your system is affected, follow the steps below to gain the necessary permissions.

    ### **Enabling USB Write Access (Mac)**
    - On **Mac**, this restriction is controlled via **DLP-15.0**.
    - To remove it, use the **Self-Service** program:
      **`Remove DLP-15.0`** (This option is not visible by default and must be enabled in JAMF.)

    - To be added to the **USB Exception Group**, send an email to:
      ```
      <EMAIL>
      ```

    - If a **ServiceNow request** is required, submit a **Device Exception Request** here:
      [🔗 Device Exception Request](https://cardinal.service-now.com/gith?id=sc_cat_item&table=sc_cat_item&sys_id=9f75bfd9dbc29450cd6d9a82ca961954&searchTerm=USB%20exception)

    ### **Gaining <PERSON>min Privileges for <PERSON><PERSON>er**
    - **Mac Admin Access** is needed to install and run <PERSON><PERSON>tcher.
    - Contact **Client Engineering (<PERSON>)** to have the **"Privileges"** app installed.
    - Open the **Privileges** app → **Request Install Permission**.
    - **Quickly** open Balena Etcher and start the image writing process (**permissions last only 5 minutes**).

## Using Balena Etcher

Follow these steps to correctly write the Raspberry Pi OS image to an SD card using **Balena Etcher**:

1. **Download & Install** [Balena Etcher](https://www.balena.io/etcher).
2. **Launch Balena Etcher** and select:
      - **"Flash from file"** → Choose the **zip file** (Balena Etcher will extract it automatically).
      - **"Select target"** → Choose the **16GB SD card**.
      - **"Flash!"** → Start the process. _(Enter your Mac password if prompted.)_
3. **Wait for the flashing process to complete.** _(This may take a few minutes.)_
4. **Safely eject the SD card** and insert it into the **Raspberry Pi**.
5. **Prepare for First Boot**:
      - **Connect an HDMI monitor** to the **HDMI0** port _(closest to the power connector)_.
      - **Attach a keyboard** for setup.

    ???+ warning "🚨 Important: Disconnect Pi from Network Before Booting"
        - Before booting the Raspberry Pi:
            1. Ensure it is **not connected** to any **wired network**.

6. **Power on the Raspberry Pi** by plugging in the power adapter.

!!! info "Device Registration Process"
    - The **Pi will reboot once** as part of the process and will then land on a **home page**
      displaying the **Device ID** and other important details.
    - Now you can connect to the **wired network** and proceed with the **device registration process**.
    - After **one minute**, the device will **register with Slicer** and appear in the list at:
      👉 [Slicer Reports](https://slicer.cardinalhealth.net/reports)
    - Use the **Find** feature in your browser to search by **ID** or **IDr**, then click on the
      **ID** to view the device's details.
    - If you are **logged into Slicer** and have the necessary permissions, you will be able to
      **configure the device** directly from this page.

---
**End of document.**
