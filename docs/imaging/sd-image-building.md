# SD Image Building Guide

## Overview

This guide provides step-by-step instructions on how to build and configure a new Raspberry Pi OS image.

---

## To Build a New Image

### 📅 2023.10.30 - Pi5 Image

[Download Raspberry Pi OS Lite (2023-10-10)](https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-10-10/2023-10-10-raspios-bookworm-armhf-lite.img.xz)

📌 **Location:**

```sh
/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/Starting_image/2023-10-10-raspios-bookworm-armhf-lite.img.xz
```

### 📅 2024.03.12 - Updated Image

[Download Raspberry Pi OS Lite (2023-12-11)](https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2023-12-11/2023-12-11-raspios-bookworm-armhf-lite.img.xz)

### 📅 2024.04.17 - Latest Image

[Download Raspberry Pi OS Lite (2024-03-15)](https://downloads.raspberrypi.com/raspios_lite_armhf/images/raspios_lite_armhf-2024-03-15/2024-03-15-raspios-bookworm-armhf-lite.img.xz)

!!! info "For official Raspberry Pi OS releases, visit:"
    [Raspberry Pi OS](https://www.raspberrypi.com/software/operating-systems/)

---

## Testing on Raspberry Pi at Dave's House

**Test Device IDs:**

```sh
(ID=10000000e3669edf)
(IDr=1530-0022-4805-1292-511)
```

**SSH into the Pi:**

```sh
ssh pi@10.226.171.139
```

---

## Original Raspberry Pi OS Lite Download

📌 **[Official Download Page](https://www.raspberrypi.org/software/operating-systems/#raspberry-pi-os-32-bit)**

📌 **GCP Bucket Location:**
[View in Google Cloud Console](https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/raw_image_starts?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false)

📌 **Alternative Download from GCP Storage:**

```sh
gs://pi-mgmt-pr-cah-distribution/raw_image_starts/2021-03-04-raspios-buster-armhf-lite.zip
```

### Release Information

```sh
Release date: March 4th 2021
Kernel version: 5.10
Size: 442MB
SHA256: ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39
```

???+ abstract "Most recent release note:"
    **2021-03-04:**
        ```
        * Thonny upgraded to version ********
        * SD Card Copier made compatible with NVMe devices; now built against GTK+3 toolkit
        * Composite video options removed from Raspberry Pi 4 in Raspberry Pi Configuration
        * Boot order options in raspi-config adjusted for more flexibility
        * Recommended Software now built against GTK+3 toolkit
        * Fix for crash in volume plugin when using keyboard could push value out of range
        * Fix for focus changing between windows in file manager when using keyboard to navigate directory view
        * Fix for Raspberry Pi 400 keyboard country not being read correctly in startup wizard
        * Armenian and Japanese translations added to several packages
        * Automatically load aes-neon-bs on ARM64 to speed up OpenSSL
        * Raspberry Pi firmware fcf8d2f7639ad8d0330db9c8db9b71bd33eaaa28
        * Linux kernel 5.10.17
        ```

### Verify SHA256 Checksum on Mac

```sh
shasum -a 256 ~/Downloads/2021-03-04-raspios-buster-armhf-lite.zip
# or
shasum -a 256 ~/Downloads/raw_image_starts-2021-03-04-raspios-buster-armhf-lite.zip
```

???+ "Expected SHA256"
    ```sh
    ea92412af99ec145438ddec3c955aa65e72ef88d84f3307cea474da005669d39
    ```

**Leave the file zipped** - Balena Etcher can flash directly from the zip file.

!!! danger "**Mac Users:**"
    If Mac has been forced to not allow USB memory, go to Self Service find the **Remove DLP-15.0**, and run the remove.
    (This is a not-normal remove, and must be made visible by using Jamf tools to show it)

---

## Flashing the SD Card

This section has been moved to a dedicated guide.
For step-by-step instructions on burning an image, visit:

📌 **[How to Burn an Image](../how-tos/how-to-burn.md)**

---

### Initial Setup

Added 2024.03.12

- **Keyboard Layout:** English (US)

=== "New"
    !!! info "**Login Credentials:**"
        - Set a new user as `pi`
        - Use the cah pi default password, that is not `raspberry`
        - Login as the `pi user`

=== "Old"
    !!! info "**Login Credentials:**"
        - **Username:** `pi`
        - **Password:** `raspberry` (Change immediately!)
        ```sh title="Change Password To:"
        passwd
        ```

### Start SSH

```sh
sudo systemctl start ssh
```

!!! info ""
    **Now you can connect the Pi to a wired network.**

**Get the IP Address:**

```sh
ip addr
```

**SSH into the Pi:**
!!! info
    ---- At this point, you no longer need the keyboard, and can do the rest from another station
    ```sh title="Connect from another computer/station:"
    ssh pi@<ip-address>
    ```

### Enable SSH

```sh
sudo systemctl enable ssh
```

---

## Setting Up the Hardware

~~Change Keyboard Layout (Old):~~

```sh
sudo vi /etc/default/keyboard
# Change "gb" to "us"
```

---

## Creating Admin & Worker Users

!!! info "Started 2021.07.23 with Image version 2.2.0"

### **Create Admin User (`cah-pi-su`)**

```sh
sudo adduser cah-pi-su
```

> (vault lookup?)
>
> (leave all entries blank, and keep hitting enter until it saves)

```sh title="ADM group gets us the journalctl log prints for all"
sudo usermod -a -G video,adm cah-pi-su
sudo adduser cah-pi-su sudo
```

**Grant Passwordless Sudo Access:**
To avoid typing password at each restart of sudo permission window

```sh
sudo vi /etc/sudoers.d/010_cah-pi-su
```

```sh title="Add the following line:"
cah-pi-su ALL=(ALL) NOPASSWD: ALL
```

```sh title="Log out of pi user"
exit
```

**Disable Default `pi` User:**

```sh
sudo vi /etc/shadow
```

!!! info
    ```sh title="Find"
    "pi:$6$rseVWwvM7AfG7bKY$TV6WN558gwmBs.idUmnkzO9PlOgYJtenJM5oBJeDO4FcGAo.qEWdkHAw4CBDmYR.q3HZxRkO8fVPEa69t2cGo1:18690:0:99999:7:::"
    # And remove the entire line
    ```

### **Create Worker User (`worker`)**

```sh
sudo adduser worker
```

Use a `CamelCase` password.

!!! info "Printing (long install) (Must do it now, to set permissions just once, which include `lpadmin`)"

> Added 2024.03.12

```sh
sudo apt-get update
```

```sh title="This is the long time to install one"
sudo apt-get install -y cups
```

```sh title="Assign the necessary groups"
sudo usermod -a -G video,input,lpadmin worker
```

### **Check the settings**

```sh
ls -l /dev/input/
groups worker
```

---

## Setting Up Printer User

```sh
sudo adduser printer
# Password: printer

sudo usermod -a -G lpadmin printer
```

---

## WLAN Pi5 Settings

In `sudo raspi-config`, under **Localization Options**, there is a choice for WLAN legal settings:

```sh
Localization... > WLAN Country... > US
```

> **FixMe:** Figure out what we need to set for WLAN legal.
>
> **FixMe:** Does this also impact Bluetooth?

---

## Get Updates and Required Installs

> **Reference:** [Minimal RPi Kiosk Setup](https://blog.r0b.io/post/minimal-rpi-kiosk/)

```sh
# sudo apt-get update # now included in pi_hmi

# Install browser and X environment
# sudo apt-get install -y --no-install-recommends xserver-xorg-video-all \
#   xserver-xorg-input-all xserver-xorg-core xinit x11-xserver-utils \
#   chromium-browser unclutter # now in pi_hmi

# Install Chromium trust store manager
# sudo apt-get install -y libnss3-tools # now in pi_hmi

# Install MyQR for QR code generation
# sudo apt-get install -y python3-pip
# sudo pip3 install MyQR
# sudo apt-get install libopenjp2-7 # now in pi_hmi

# Install Privoxy for whitelisting
# sudo apt-get install -y privoxy # now in pi_hmi
```

> **Note:** The above packages are now included in `pi_hmi`. Continue installing manually until `hmi` can be debugged.

Log in as **cah-pi-su** user before proceeding.

---

## Install Necessary Packages

### **Step 1: Update and Upgrade**

```sh
sudo apt -y update

# This next one takes a while
sudo apt -y upgrade
```

### **Step 2: Fix Unfinished Package Configurations**

```sh
sudo dpkg --configure -a
```

### **Step 3: Install Core Dependencies**

```sh
sudo apt-get install -y privoxy
sudo apt-get install -y python3-pip
```

### **Step 4: Fix Python Package Manager Issues (If Needed)**

> Added 2024.03.12
>
> **Reference:** [Fixing Pip Error in Linux](https://www.makeuseof.com/fix-pip-error-externally-managed-environment-linux/)

```sh
ls -l /usr/lib/ | fgrep python3
# Modify the following line based on the Python version found above
sudo rm /usr/lib/python3.11/EXTERNALLY-MANAGED
```

### **Step 5: Install MyQR for QR Code Generation**

```sh
sudo pip3 install MyQR
```

**If the above fails, try:**

```sh
sudo apt-get install -y cmake
sudo apt-get install -y libblas-dev
sudo apt install -y libblas3 liblapack3 liblapack-dev libblas-dev

# Fix dependencies for MyQR
sudo apt install -y libjpeg-dev zlib1g-dev
sudo pip3 install MyQR --break-system-packages
```
> Reference: [Pillow Installation](https://pillow.readthedocs.io/en/latest/installation.html)

**Test the installation:**

```sh
sudo python3 -c "from MyQR import myqr as mq"
# Should return no errors
```

### **Step 6: Install Additional Packages**

```sh
sudo apt-get install libopenjp2-7

sudo apt-get install -y --no-install-recommends \
    xserver-xorg-video-all xserver-xorg-input-all \
    xserver-xorg-core xinit x11-xserver-utils \
    chromium-browser unclutter

sudo apt-get install -y libnss3-tools
```

### **Step 7: Install Screen Capture Tool**

```sh
sudo apt-get install -y scrot
```

### **Step 8: Install Required Python Modules and Utilities**

```sh
# Required for pi_bluetooth and pi_runner
sudo pip3 install apscheduler --break-system-packages

# Install system monitoring tools
sudo apt-get install -y tmux
sudo apt-get install -y nmon
```

**To use nmon:**

```sh
nmon
```

---

## Edits to Configure

### Privoxy Configuration

```sh
sudo vi /etc/privoxy/config
```

- Find the line that starts with `#debug 1024` and **uncomment** it.
- This enables logging to `/var/log/privoxy/logfile`, which starts automatically based on the config change.
- Uncommenting this line allows us to collect details about what is blocked.

### CUPS Configuration

```sh
sudo vi /etc/cups/cupsd.conf
```

  ```sh title="Change the line:"
  DefaultAuthType Basic
  ```

  ```sh title="To:"
  DefaultAuthType None
  ```

- Restart CUPS:

  ```sh
  sudo systemctl restart cups
  ```

---

## Security Audit

### Install Lynis Security Audit Tool

> [Lynis Documentation](https://cisofy.com/lynis/)

```sh
sudo apt-get install -y lynis
```

### Initial Test (Optional)

```sh
sudo lynis audit system -Q
```

#### Initial result

> As of 2021-07-24

```sh
Hardening index : 55 [###########         ]
Tests performed : 213
Plugins enabled : 1
```

## System Updates

```sh
sudo apt update
sudo apt upgrade -y
```

---

## System Hardening

### SSH Configuration (Now done in `pi_security`)

```sh
sudo vi /etc/ssh/sshd_config
```

### Update System Message

```sh
sudo vi /etc/init.net
```

```sh title="Expected Output"
********************************************************************************
This computer system and associated networks are the property of and for the
sole business use of Cardinal Health, Inc. authorized users. Cardinal Health
reserves the right to monitor computer and network usage, and your use of
Cardinal systems constitutes consent to such monitoring. The company's
computers and the proprietary data and information stored on them remain at all
times the property of Cardinal Health, Inc. Unauthorized access to this system
is strictly forbidden. This server and your actions after login are monitored.
********************************************************************************
```

### Restrict Access to Compilers

```sh
sudo chmod o-rx /usr/bin/gcc
sudo chmod o-rx /usr/bin/g++
```

### Disable USB Storage

[Reference](https://www.cyberciti.biz/faq/linux-disable-modprobe-loading-of-usb-storage-driver/)

```sh
sudo vi /etc/modprobe.d/blacklist.conf
```

```sh
blacklist usb-storage
```

???+ info "Skip doing this test, unless you want to know the numbers now"
    ```sh
    sudo lynis audit system -Q
    ```
    ```sh
    Hardening index : 55 [###########         ]
    Tests performed : 213
    Plugins enabled : 1
    ```

---

## Network Configuration

### Network Manager Installation

```sh
sudo apt-get update
sudo apt -y --fix-broken install
sudo apt-get install -y network-manager
```

### Set Network Manager as Primary

```sh
sudo vi /etc/dhcpcd.conf
```

```bash title="Add the following lines at the end:"
denyinterfaces wlan0
denyinterfaces wlan1
```

### Disable WiFi MAC Address Randomization
[Reference](https://www.raspberrypi.org/forums/viewtopic.php?t=237623)

Configure Network Manager to not randomize the WiFi Mac Address

```sh
sudo vi /etc/NetworkManager/conf.d/100-disable-wifi-mac-randomization.conf
```

```sh
[connection]
wifi.mac-address-randomization=1

[device]
wifi.scan-rand-mac-address=no
```

### Reboot to Apply Changes

To fix the randomized `wlan0` mac address

```sh
sudo reboot
```

___

## Config Browser

[Source: Raspberry Pi Kiosk Setup](https://pimylifeup.com/raspberry-pi-kiosk/)

### Configure `.xinitrc`

```sh
sudo vi /home/<USER>/.xinitrc
```

```sh title="Contents"
#!/usr/bin/env sh
xset -dpms
xset s off
xset s noblank

screen_width="$(fbset -s | awk '$1 == "geometry" {print $2}')"
screen_height="$(fbset -s | awk '$1 == "geometry" {print $3}')"

# Disable keyboard shortcuts
unclutter -idle 2 &
xmodmap -e "keycode 37="
xmodmap -e "keycode 67="
xmodmap -e "keycode 105="
xmodmap -e "keycode 133="
xmodmap -e "keycode 134="

rm -rf /home/<USER>/.config/chromium/Singleton*

while true; do
    /cardinal/browserstart
done;
```

```sh
sudo chown -R worker:worker /home/<USER>/.xinitrc
```

> ```# https://edhr-na-jz.cardinalhealth.net/camstarportal/default.htm#/login```

### Set up local HTML directory

```sh
sudo mkdir /cardinal
sudo mkdir /cardinal/localhtml
sudo su

echo "blank page" > /cardinal/localhtml/index.html
exit
```

### Configure browser startup script

```sh
sudo vi /cardinal/browserstart_default
```

The following file is also maintained by pi_runner, so make matching changes there.

```sh title="Contents"
cp /cardinal/localhtml/chromium_Default_Preferences /home/<USER>/.config/chromium/Default/Preferences

chromium-browser \
  --proxy-server="https=127.0.0.1:8118;http=127.0.01:8118" \
  --window-size=$screen_width,$screen_height \
  --window-position=0,0 \
  --start-fullscreen \
  --incognito \
  --noerrdialogs \
  --disable-translate \
  --no-first-run \
  --fast \
  --fast-start \
  --disable-infobars \
  --disable-features=TranslateUI \
  --disable-features=Translate \
  --disk-cache-dir=/dev/null \
  --overscroll-history-navigation=0 \
  --disable-pinch \
  --kiosk \
  file:///cardinal/localhtml/index.html
```

```sh
sudo chown -R worker:worker /cardinal/browserstart_default
sudo chmod +x /cardinal/browserstart_default
sudo cp /cardinal/browserstart_default /cardinal/browserstart
```

---

## Startx Issues on Raspberry Pi 5

> Added 2024-03-12

```bash
bash: startx: command not found
```

[Reference](https://forums.raspberrypi.com/viewtopic.php?t=361722)

```sh
sudo apt install gldriver-test
```

[Reference](https://iiab.me/kiwix/raspberrypi.stackexchange.com_en_all_2022-11/questions/84804/how-to-start-gui-with-startx-command-not-found)

```sh
sudo apt-get install xinit -y
```

---

## Runner Fails (2024-03-12)

```bash
Mar 13 07:57:07 raspberrypi pi-runner[945]:
zoneinfo._common.ZoneInfoNotFoundError: 'Multiple conflicting time zone configurations found:\n/etc/timezone:
Europe/London\n/etc/localtime is a symlink to: America/New_York\nFix the configuration, or set the time zone in a TZ environment variable.\n'
```

### Check Timezone

!!! info "Check Current Timezone Configuration"
    ```sh
    cat /etc/timezone
    Europe/London
    ls -l /etc/localtime
    lrwxrwxrwx 1 root root 38 Mar 12 15:12 /etc/localtime -> ../usr/share/zoneinfo/America/New_York
    ```

!!! warning "Fix Timezone Issue"
    ```sh
    sudo rm /etc/localtime
    ```

!!! tip "Note"
    After removing the conflicting timezone link, the system will use the timezone specified in `/etc/timezone`

---

## Disable Pi5 Power Button

> Added 2024.03.12
>
> [Reference](https://forums.raspberrypi.com/viewtopic.php?p=2188834&hilit=power+button+disable#p2188834)

```sh
sudo vi /etc/systemd/logind.conf
```

Add the following configuration:

```sh
[Login]
HandlePowerKey=ignore
HandlePowerKeyLongPress=ignore
```

!!! warning "FixMe: Update/Upgrade Considerations"
    Does this setting get modified when we do update/upgrade activity?
    If so, then do it at the end, or better yet, auto fix it if anyone changes it.

---

## Create System Snapshot

Shut down the system to create a backup image:

```sh
sudo shutdown -h now
```

### Make Image of the 16GB Card

Use ApplePi Baker with shrink turned off:

- 2024.03.28 using Pi OS 2023-12-11 → (`downloads/20240328/backup3`)
- 2024.04.17 using Pi OS 2024-03-15 → (`downloads/20240417/backup1`)
- 2024.04.23 using Pi OS 2024-03-15 → (`downloads/20240417/backup1a`)
- 2024.11.25 → `Downloads/pi_image_2024.11.25.zip`

!!! info "Testing"
    Feel free to stop here, reboot, manually log in as worker, then manually run `startx`

### Test Browser Process

```sh
# On the device:
startx

# On a remote SSH terminal
ps ax | fgrep chromium
```

---

## Bluetooth Troubleshooting for Pi4/5

!!! warning "Development Test - Not for Production"
    This section contains experimental configurations for addressing Bluetooth issues on Pi4 and Pi5 devices.

### Initial Approach

[Reference: Debian Forum](https://forums.debian.net/viewtopic.php?t=151557) (Near the bottom of the post)

```bash
sudo apt-get install pulseaudio-module-bluetooth -y
pulseaudio --kill
pulseaudio --start

sudo journalctl --vacuum-time=1m
journalctl -u pi-bluetooth.service --no-pager
```

### Error Analysis

After attempting the above solution, the following errors were observed:

```
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/vcp.c:vcp_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init vcp plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/mcp.c:mcp_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init mcp plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/audio/bap.c:bap_init() D-Bus experimental not enabled
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: src/plugin.c:plugin_init() Failed to init bap plugin
Apr 05 19:26:41 cah-rp-10000000e3669edf bluetoothd[17537]: Bluetooth management interface 1.22 initialized
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: profiles/sap/server.c:sap_server_register() Sap driver initialization failed.
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: sap-server: Operation not permitted (1)
Apr 05 19:26:42 cah-rp-10000000e3669edf bluetoothd[17537]: Failed to set privacy: Rejected (0x0b)
```

### Alternative Solution 1

[Reference: Debian Forum](https://forums.debian.net/viewtopic.php?t=154544)

```bash
sudo apt-get install libspa-0.2-bluetooth -y
sudo apt-get remove pulseaudio-module-bluetooth -y
systemctl --user start pulseaudio.socket
```

### Alternative Solution 2

[Reference: Reddit](https://www.reddit.com/r/archlinux/comments/yu9az9/bluetooth_errors_since_2_days_ago/)

A user reported fixing similar issues with the following commands:

```bash
sudo rmmod btusb
sudo rmmod btintel
sudo modprobe btintel
sudo modprobe btusb
```

### Additional Packages (2024.04.17)

[Reference: Pi My Life Up](https://pimylifeup.com/raspberry-pi-bluetooth/)

```bash
sudo apt install -y bluetooth pi-bluetooth bluez blueman
```

!!! note ""
    Result: No improvement observed

### System Diagnostics

Useful commands to check Bluetooth version information:

```bash
bluetoothctl --version
bluetoothd --version
```

Also check `hciuart` status if needed.

### Version Comparisons and Behaviors

#### SP.40 Image (Old)

```bash
bluetoothctl --version
bluetoothd --version
```
Output:
```
bluetoothctl: 5.50
5.50
```

```bash
dmesg | tee | fgrep Bluetooth
```
Output:
```
(empty) # But maybe only empty of bluetooth, because it was pushed out the beginning of the log...
```

```bash
uname -a
```
Output:
```
Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux
```

```bash
dpkg --status bluez | fgrep Version
```
Output:
```
Version: 5.50-1.2~deb10u3+rpt1
```

Result: RS6000 worked as expected

#### After Update

```bash
sudo apt-get update
reboot
```

Version check after update:
```
bluetoothctl: 5.50
5.50
```

Result: RS6000 worked as expected

#### After Upgrade

```bash
sudo apt-get upgrade
reboot
```

Version check after upgrade:
```
bluetoothctl: 5.50
5.50
```

System information:
```
Linux cah-rp-10000000e3669edf 5.10.103-v7l+ #1529 SMP Tue Mar 8 12:24:00 GMT 2022 armv7l GNU/Linux
```

Bluetooth package version:
```
Version: 5.50-1.2~deb10u4
```

Result: RS6000 stopped working

Update to latest pi-bluetooth to handle tmux changes: Still failed.

#### Kernel Messages After Upgrade

```bash
dmesg | tee | fgrep Bluetooth
```

```
[    8.455095] Bluetooth: Core ver 2.22
[    8.455217] Bluetooth: HCI device and connection manager initialized
[    8.455342] Bluetooth: HCI socket layer initialized
[    8.455403] Bluetooth: L2CAP socket layer initialized
[    8.455454] Bluetooth: SCO socket layer initialized
[    8.491685] Bluetooth: HCI UART driver ver 2.3
[    8.491710] Bluetooth: HCI UART protocol H4 registered
[    8.491849] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    8.506279] Bluetooth: HCI UART protocol Broadcom registered
[    9.130041] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    9.130061] Bluetooth: BNEP filters: protocol multicast
[    9.130106] Bluetooth: BNEP socket layer initialized
[ 1385.452174] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[ 1385.452213] Bluetooth: HIDP socket layer initialized
```

#### SP.47 Image (Middle)

Version information:
```
bluetoothctl: 5.50
5.50
```

Kernel messages:
```
[    7.884677] Bluetooth: Core ver 2.22
[    7.884794] Bluetooth: HCI device and connection manager initialized
[    7.884821] Bluetooth: HCI socket layer initialized
[    7.884840] Bluetooth: L2CAP socket layer initialized
[    7.884874] Bluetooth: SCO socket layer initialized
[    7.934892] Bluetooth: HCI UART driver ver 2.3
[    7.934918] Bluetooth: HCI UART protocol H4 registered
[    7.935024] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    7.935448] Bluetooth: HCI UART protocol Broadcom registered
[    8.494406] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    8.494432] Bluetooth: BNEP filters: protocol multicast
[    8.494466] Bluetooth: BNEP socket layer initialized
```

#### Pi5 Image (Current)

Version information:
```
bluetoothctl: 5.66
bluetoothd: 5.66
```

System information:
```
Linux cah-rp-ec885c8aa5f46f0d 6.6.20+rpt-rpi-v8 #1 SMP PREEMPT Debian 1:6.6.20-1+rpt1 (2024-03-07) aarch64 GNU/Linux
```

Bluetooth package version:
```
Version: 5.66-1+rpt1+deb12u1
```

Kernel messages:
```
[    5.664370] Bluetooth: Core ver 2.22
[    5.664402] NET: Registered PF_BLUETOOTH protocol family
[    5.664404] Bluetooth: HCI device and connection manager initialized
[    5.664411] Bluetooth: HCI socket layer initialized
[    5.664415] Bluetooth: L2CAP socket layer initialized
[    5.664420] Bluetooth: SCO socket layer initialized
[    5.674567] Bluetooth: HCI UART driver ver 2.3
[    5.674575] Bluetooth: HCI UART protocol H4 registered
[    5.674609] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    5.674840] Bluetooth: HCI UART protocol Broadcom registered
[    6.042366] Bluetooth: hci0: BCM: chip id 107
[    6.043130] Bluetooth: hci0: BCM: features 0x2f
[    6.044254] Bluetooth: hci0: BCM4345C0
[    6.044261] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0000
[    6.047494] Bluetooth: hci0: BCM4345C0 'brcm/BCM4345C0.raspberrypi,5-model-b.hcd' Patch
[    6.759113] Bluetooth: hci0: BCM: features 0x2f
[    6.760571] Bluetooth: hci0: BCM43455 37.4MHz Raspberry Pi 3+-0190
[    6.760583] Bluetooth: hci0: BCM4345C0 (003.001.025) build 0382
[    6.761807] Bluetooth: hci0: BCM: Using default device address (43:45:c0:00:1f:ac)
[   13.840787] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[   13.840794] Bluetooth: BNEP filters: protocol multicast
[   13.840800] Bluetooth: BNEP socket layer initialized
[   13.842897] Bluetooth: MGMT ver 1.22
```

---

## Set Image Version

Must be ready before runner install

```sh
sudo su

# This sets the base image value that gets reported
echo "Image version: 2.4.1" > /cardinal/image_ver.txt
# 2024.03.13 Bookworm Build for Pi5
echo "Image version: 2.4.2" > /cardinal/image_ver.txt

exit
```

---

## Fix NumPy Issues

```sh
sudo pip3 uninstall numpy -y
sudo apt install python3-numpy
```

---

## Load Pi Runner

### Get Raspberry Pi Serial Number

```sh
# To collect serial number like: 10000000e3669edf
cat /proc/cpuinfo | fgrep Serial
```

### Manual Installation

```sh title="pi_runner"
sudo vi /cardinal/pi_runner.py
# Copy and paste the content of the source file

cd /cardinal
sudo python3 pi_runner.py install
```

!!! warning "Important Note"
    Once running and checking into Slicer, if there is an update to settings ready for it in Slicer, then a reboot may occur automatically. This is normal behavior.

### Verify Registration

Check that the device appears in Slicer:
```
https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf
```

---

## Install Components via Slicer

The following components should be loaded through the Slicer interface (Runner must be loaded and running first):

- pi_bluetooth
- pi_config
- pi_hmi
- pi_logging
- pi_monitor
- pi_network
- pi_organization
- pi_security
- pi_settings

```sh
sudo reboot
```

!!! danger "Testing Required"
    **Stop here** and manually test by logging in as worker, then running `startx`

---

## Configure Autologin for Worker

!!! note "Command Line Only Login"
    This configuration gets to the command line prompt only. Do not expect auto startx or anything further.

### For Pi5 and Latest OS (After 2023.10.30)

[Reference](https://www.nixcraft.com/t/how-to-configure-autologin-on-the-raspbian-buster-console/3922/2)

```sh
sudo vi /etc/systemd/logind.conf
# Replace "#NAutoVTs=6" with "NAutoVTs=1"
```

### Edit Getty Service

```sh
sudo <NAME_EMAIL>

# sudo vi /etc/systemd/system/<EMAIL>.d
```

Add the following above "### Lines below this comment will be discarded":

```sh
[Service]
ExecStart=
ExecStart=-/usr/sbin/agetty --autologin worker --noclear %I $TERM
```

Save with `Ctrl+X`, `Y`, `Enter`

???+ info "Old Method (For Reference)"
    ```sh
    sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf
    # Add:
    [Service]
    ExecStart=
    ExecStart=-/sbin/agetty --autologin worker --noclear %I \$TERM

    sudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>
    ```

### Test Autologin

```sh
sudo reboot
```

---

## Configure Automatic Startx on Login

### Add to Worker's Bash Profile

```sh
sudo vi /home/<USER>/.bash_profile
```

Add:

```sh
if [ -z $DISPLAY ] && [ $(tty) = /dev/tty1 ]
then
  startx
fi
```

Set ownership:

```sh
sudo chown -R worker:worker /home/<USER>/.bash_profile
```

---

## Verify HMI Installation

The HMI install script should no longer be present when installation is complete:

```sh
# Check that tempscript is gone (installation may take up to 10 minutes)
ls -l /cardinal/pi_hmi_tempscript
```

---

## Create Test Image Backup

Make a backup at this stage:

```sh
# Recommended backup naming conventions:
# 2024.04.17 -> backup2
# 2024.04.23 -> starting from backup1b, worked to here, then save as -> 20240417/backup2b
```

---

## Test GUI Performance

Reboot and test the GUI:

```sh
sudo reboot
```

---

## Browser Security Testing

[Reference: Function Keys in Google Chrome](https://www.howtogeek.com/408289/what-your-function-keys-do-in-google-chrome/)

### Blocked Shortcuts

The following keyboard combinations should be blocked:

- `Ctrl+O` (open file)
- `Ctrl+Alt+F2` (opens terminal)
- `Ctrl+W` (close window)
- `Alt+Esc` (incognito)

Dont want to allow F1, but cannot block F1

- `F1` (opens help, should show a fail page due to Privoxy blacklist)

### Allowed Shortcuts

Should be covered by do while loop:

- `Alt+F4` (exit) - Use this to escape from F1 result screens

---

## Test Bookmark Integration

Load bookmarks via the Slicer interface and test all links on the Pi.

---

## Multi-Device Testing

### Chromium Profile Test

Load the card into a second pi, and see that there is not a warning about a duplicate chromium profile

Test that the image works correctly across multiple devices:

1. Shut down the current Pi:
   ```sh
   sudo shutdown -h now
   ```

2. Move the SD card to a second Raspberry Pi

3. Verify it boots without warnings about duplicate Chromium profiles

---

## Bluetooth Scanner Testing

1. Configure two devices with Bluetooth enabled via the Slicer interface
2. Start with all devices powered off, then power on the Raspberry Pi devices first
3. Verify devices report "(no devices seen)"
4. Cold boot the scanner:
      - Press the side button while inserting the battery
      - Hold until it beeps
      - Scan the one-time configuration barcode
      - Confirm both devices show a pairing barcode after 15-30 seconds

5. Pairing Tests:
      - Pair to device 1 (may require two scans over 30-40 seconds)
      - Pair to device 2 (should unpair from device 1 after 30-60 seconds)
      - Remove scanner battery for at least 10 minutes
      - Verify both devices revert to "(searching..)"
      - Power up scanner (should not auto-reconnect)
      - Manually pair to device 1, then to device 2
      - With scanner powered, reboot devices and verify no automatic pairing occurs within 10 minutes

---

## Finalize Image for Distribution

### System Cleanup

Log in as root:

```sh
sudo su
```

Clean packages:

All of these as a single copy and paste into the terminal:

```sh
apt-get clean
apt-get -y autoremove --purge
```

### Remove Temporary Files

```sh
rm -rf /Downloads
rm -rf /home/<USER>/*
rm -rf /cardinal/save_values
```

### Stop Services

```sh
systemctl stop pi-bluetooth.service
systemctl stop pi-config.service
systemctl stop pi-hmi.service
systemctl stop pi-logging.service
systemctl stop pi-monitor.service
systemctl stop pi-network.service
systemctl stop pi-organization.service
systemctl stop pi-runner.service
systemctl stop pi-security.service
systemctl stop pi-settings.service
```

### Clean Log Files

```sh
find /var/log -type f -regex ".*\.gz$" -delete
find /var/log -type f -regex ".*\.[0-9]$" -delete
rm /var/log/*
```

### Reset Configuration

```sh
echo 'yes' > /cardinal/needsexpand
echo -n "0" > /cardinal/boot_count.txt
echo -n "0" > /cardinal/grab_count.txt
cp /cardinal/browserstart_default /cardinal/browserstart
rm -rf /cardinal/localhtml/*
rm -rf /cardinal/log/*
rm -rf /cardinal/config_*
rm /cardinal/call_home_locations.txt
rm /cardinal/wifi_ssid_psk.txt
rm /cardinal/screen_resolution.txt
rm /cardinal/screen_zoom.txt
rm /cardinal/wifi_config.txt
rm /cardinal/call_home_locations.txt
```

### Create Default Startup Page

```sh
mkdir /cardinal/localhtml/
echo '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>' > /cardinal/localhtml/index.html
```

### Final Shutdown

```sh
sudo shutdown -h now
```

---

## Create Distribution Image

### Using ApplePi Baker

1. Install ApplePi Baker from [Tweaking4All](https://www.tweaking4all.com/software/macosx-software/applepi-baker-v2/#DownloadApplePiBaker)
   ```
   https://www.tweaking4all.com/downloads/ApplePi-Baker-v2.dmg
   ```

2. Run ApplePi-Baker from the Applications folder
   - Select the external disk to get the permissions popup
   - Follow prompts to allow full disk access
   - Reboot Mac to apply changes correctly

!!! danger "MacOS USB Access"
    On a Mac, JAMF settings may block USB memory card access. If you don't see the SD card reader, contact the Mac admin to enable the Self Service item 'Remove DLP-15.0', then run that program.

### Creating the Image

Each Time:

1. Run ApplePi Baker from Applications
2. Select the disk
3. In the lower right corner, enable the shrink option (2nd from left)
4. Click "Backup" and name it (e.g., "cah_pi_raw_image_2.4.2.SP.XX")
      - For a 64GB card, this takes 11-12 minutes
      - The ZIP should be under 2.5GB; if larger, you missed the shrink setting

!!! info "Future:"
    [Resizing SD Images](http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php)

### Test the Image

1. Use Balena Etcher to flash a test card from the newly created ZIP file
      - If CrowdStrike blocks this, email <EMAIL> for an exception
2. Insert the card in a Raspberry Pi and verify it boots correctly

---

## Slicer Upload Folder

### Upload to Slicer

1. Upload the image to Slicer
2. Mark it as "available for download"

### Notify Users

Inform the "LATAM Raspberry Pi" group that the new version is available:

```
Pi image version 2.Y.Z is available.

https://slicer.cardinalhealth.net
Log in
Go to the "download" page to view version text file, and to download images.
```

---

## Development Notes

!!! warning "Development Status"
    The following notes contain configurations that have been tested but are not yet integrated into the standard build process.

### GCP Image Upload

#### Storage Location

[View in Google Cloud Console](https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false)

#### Datastore Management

!!! warning "Important"
    Revert any old "roll forward" "device_service_" entries in the datastore to prevent accidental version rollbacks.

#### File Sharing

1. **Generate Download Link**
   ```
   https://storage.cloud.google.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.2.zip
   ```

2. **Set Permissions**
      - Add users at file level (e.g., <EMAIL>) - (Did not work)
      - Add users at bucket level for `pi-mgmt-pr-cah-distribution`

---

## USB Control

#### Power Control Installation

For power control of USB in the `pi_hmi` service:

[HUB CTRL: Raspbian](https://snapcraft.io/install/hub-ctrl/raspbian)

1. **Using hub-ctrl**
   ```bash
   # Install snapd
   sudo apt install -y snapd

   # Install hub-ctrl
   sudo snap install hub-ctrl
   ```

2. **Using uhubctl**

    [Github: UHUBCTL](https://github.com/mvp/uhubctl)

    ```bash
    # Install dependencies
    sudo apt-get install -y libusb-1.0-0-dev
    sudo apt-get install -y git

    # Clone and build
    git clone https://github.com/mvp/uhubctl
    cd uhubctl
    make
    sudo make install
    ```

---

## GCP Bucket Upload Process

1. **Access Storage Console**

      - [View in Google Cloud Console](https://console.cloud.google.com/storage/browser/pi-mgmt-pr-cah-distribution/production_image_releases?project=mac-mgmt-pr-cah&pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false)

      - [Wiki: GCP Cloud Storage Guide](https://wiki.cardinalhealth.net/GCP_Cloud_Storage/PublicBuckets#Requesting_an_Exception())

2. **Upload Steps**
      - Upload the zip file
      - Click into the file
      - Edit Permissions
      - Set Entity=Public, Name=allUsers, Access=Reader
      - Save permissions

3. **Generate Public URL**

    ```sh title="Example format:"
    https://storage.googleapis.com/pi-mgmt-pr-cah-distribution/production_image_releases/cah_pi_raw_image_2.0.0.zip
    ```

---

## Generic Lite Image

### Initial Image Setup

1. **Base Image Preparation**
      - Set up a generic new Lite image (not NOOBS)
      - Expand to only 1 GB (consider doing in VM)
      - Connect Pi to network

2. **Initial Configuration**
   ```bash
   curl -k slicer.cardinalhealth.net/build
   ```
    This initiates an SSH connection to:
    - Install initial monitor
    - Allow full compliance push

3. **File System Management**
      - Shrink the file system
      - Re-expand on next boot

    *Reference Links:*

    - [Shrink Tool](https://github.com/qrti/shrink)
    - [Shrinking Guide](https://blog.febo.com/?p=283)
    - [Reverse Root FS Expansion](https://raspberrypi.stackexchange.com/questions/29947/reverse-the-expand-root-fs)

4. **Image Creation Process**
      - Perform clean shutdown
      - Create ISO/equivalent of card
      - Burn to new card
      - Allow startup process to expand card to full size

    *Reference:* [SD Image Resizing Guide](http://www.aoakley.com/articles/2015-10-09-resizing-sd-images.php)

5. **Expansion Commands**
   ```bash
   sudo raspi-config --expand-rootfs
   sudo reboot

   raspi-config --expand-rootfs
   ```

#### Password Change
```bash
passwd
```

#### Create Admin User
```bash
# Create new admin user
sudo adduser pisuperuser

# Add to necessary groups
sudo usermod -a -G video pisuperuser
sudo adduser pisuperuser sudo
```

#### Create Worker User
```bash
# Create worker user
sudo adduser worker

# Add to necessary groups
sudo usermod -a -G video worker
sudo usermod -a -G audio worker  # Added 2024.05.09
```

#### Configure Autologin

Change the autologin user to the worker

```bash
sudo vi /etc/lightdm/lightdm.conf
```

```sh title="Replace:"
autologin-user=pi
```

```sh title="With:"
autologin-user=worker
```

## Chromium CAH Root Certificate

```bash
# Navigate to certificates directory
cd /usr/local/share/ca-certificates

# Convert and install certificate
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt
sudo update-ca-certificates

# Update system packages
sudo apt-get update
sudo apt -y --fix-broken install
sudo apt-get install -y libnss3-tools

# Add certificate to NSS database
certutil -d sql:$HOME/.pki/nssdb -L
certutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i CAH-Root-CA-PR1.crt

# Restart Browser

# Set permissions for worker user
sudo chmod 644 /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt

# sudo mkdir -p /home/<USER>/.pki/nssdb
# sudo certutil -d /home/<USER>/.pki/nssdb -N --empty-password

# Add certificate for worker user
sudo su worker
certutil -d sql:$HOME/.pki/nssdb -L
certutil -d sql:$HOME/.pki/nssdb -A -t C -n CAH-Root-CA-PR1 -i /usr/local/share/ca-certificates/CAH-Root-CA-PR1.crt
exit
```

#### Prevent Screen Blanking

[Raspberry Pi: Configuration](https://www.raspberrypi.org/documentation/configuration/)
[Raspberry Pi: Screensaver](https://www.raspberrypi.org/documentation/configuration/screensaver.md)

1. **Edit Boot Configuration**
    ```bash
    sudo vi /boot/cmdline.txt
    ```

    ```sh title="Add to end of line:"
    consoleblank=0
    ```

2. **Configure LightDM**

    [How to Prevent Screen going Blank](https://raspberrypi.stackexchange.com/questions/752/how-do-i-prevent-the-screen-from-going-blank)

    ```bash
    sudo vi /etc/lightdm/lightdm.conf
    ```

    ```sh title="Change:"
    #xserver-command=X
    ```

    ```sh title="To:"
    xserver-command=X -s 0 dpms
    ```

## Planning and Untested Features

!!! warning "Development Status"
    The following features are planned but have not yet been tested or integrated into the standard build process.

### Step 1 List Completion
- Complete remaining items from the "step1" list

### Browser Configuration
1. **Auto-start Browser**
   - Implement automatic browser startup on system boot

2. **Disable Update Warnings**
   - Configure browser to suppress update notification popups

### Remote Access

#### VNC Setup

[Reference: Remote Desktop VNC Guide](https://mike632t.wordpress.com/2013/04/18/remote-desktop-vnc/)

- [VNC Viewer Download](https://www.realvnc.com/en/connect/download/viewer/)

### Security Enhancements

#### Remove Default Pi User

[Reference: Security Documentation](https://www.raspberrypi.org/documentation/configuration/security.md)

### Certificate Management

#### RC.Local Configuration

Add to `/etc/rc.local`:
```bash
update-ca-certificates
```

[Reference: update-ca-certificates Manual](http://manpages.ubuntu.com/manpages/xenial/man8/update-ca-certificates.8.html)

#### Certificate Installation
All `*.crt` files in `/usr/local/share/ca-certificates` are trusted.

```bash
cd /usr/local/share/ca-certificates

# Convert and install certificates
openssl x509 -inform PEM -in entrust_l1k.cer -outform PEM -out entrust_l1k.crt

# Install CAH certificates
sudo openssl x509 -inform DER -in CAH-Dev-Root01.cer -out CAH-Dev-Root01.crt
sudo openssl x509 -inform DER -in CAH-DMZ-Root01.cer -out CAH-DMZ-Root01.crt
sudo openssl x509 -inform DER -in CAH-Issuing01.cer -out CAH-Issuing01.crt
sudo openssl x509 -inform DER -in CAH-Issuing02.cer -out CAH-Issuing02.crt
sudo openssl x509 -inform DER -in CAH-Issuing03.cer -out CAH-Issuing03.crt
sudo openssl x509 -inform DER -in CAH-Issuing-CA-PR1.cer -out CAH-Issuing-CA-PR1.crt
sudo openssl x509 -inform DER -in CAH-Issuing-CA-PR2.cer -out CAH-Issuing-CA-PR2.crt
sudo openssl x509 -inform DER -in CAH-Root01.cer -out CAH-Root01.crt
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt
sudo openssl x509 -inform DER -in CAIssuing3.cer -out CAIssuing3.crt
sudo openssl x509 -inform DER -in CAIssuing4.cer -out CAIssuing4.crt
sudo openssl x509 -inform DER -in 'Cardinal Health JSS Built-in Certificate Authority.cer' -out 'Cardinal Health JSS Built-in Certificate Authority.crt'
sudo openssl x509 -inform DER -in 'CAROOT 1.cer' -out 'CAROOT 1.crt'
sudo openssl x509 -inform DER -in CARoot.cer -out CARoot.crt
sudo openssl x509 -inform DER -in 'COMODO ECC Certification Authority.cer' -out 'COMODO ECC Certification Authority.crt'
sudo openssl x509 -inform DER -in 'COMODO RSA Certification Authority.cer' -out 'COMODO RSA Certification Authority.crt'
sudo openssl x509 -inform DER -in 'COMODO RSA Organization Validation Secure Server CA.cer' -out 'COMODO RSA Organization Validation Secure Server CA.crt'
```

```sh
# Did not help
sudo update-ca-certificates
# or just the one
sudo openssl x509 -inform DER -in CAH-Root-CA-PR1.cer -out CAH-Root-CA-PR1.crt
```

#### Chromium Certificate Configuration
1. **In Chromium**
      - Settings → Advanced → Privacy and security → Manage certificates → Authorities
      - Import → Other Locations → Computer → `/usr/local/share/ca-certificates`
      - Select `CAH-Root-CA-PR1.crt` → Open
      - Refresh browser page (with the url that had the issue)

2. **Alternative Methods**
     - [Chromium Certificate Management](https://chromium.googlesource.com/chromium/src/+/master/docs/linux/cert_management.md)
     - [Arch Linux Chromium Guide](https://wiki.archlinux.org/index.php/Chromium)

    === "**Configuration Files**"
        ```bash
        ~/.pki/nssdb
        /home/<USER>/.config/chromium/Default/Preferences
        '/home/<USER>/.config/chromium/Default/Current Session'
        ```

    === "**Command Line Options**"
        - [Chromium Command Line Switches](https://peter.sh/experiments/chromium-command-line-switches/)
        - [Running Chromium with Flags](https://www.chromium.org/developers/how-tos/run-chromium-with-flags)
        - [Raspberry Pi Touchscreen Kiosk Setup](https://desertbot.io/blog/raspberry-pi-touchscreen-kiosk-setup)

    #### Testing
    ```bash
    # Verify connectivity
    curl https://slicer.cardinalhealth.net

    # Install and test with Lynx
    sudo apt-get install -y lynx
    lynx https://slicer.cardinalhealth.net
    lynx https://slicer.cardinalhealth.net/reports

    # openssl x509 -in /etc/ssl/certs/ca-certificates.crt -text -noout
    # restart:
    # sudo rm /etc/ssl/certs/ca-certificates.crt
    ```

---

## System Updates

### CVE and Kernel Updates

[Reference: Updating Documentation](https://www.raspberrypi.org/documentation/raspbian/updating.md)

### Keyboard Configuration

#### Disable Specific Keys

[Reference: Making xmodmap Changes Permanent](https://stackoverflow.com/questions/50646587/making-xmodmap-changes-permanent-on-raspberry-pi)

```bash
sudo vi /usr/share/X11/xkb/keycodes/evdev
```

Add `//` to front of specified lines:

```bash
*** start ***
//      <LALT> = 64;
//      <LCTL> = 37;
        <SPCE> = 65;
//      <RCTL> = 105;
//      <RALT> = 108;
        // Microsoft keyboard extra keys
//      <LWIN> = 133;
//      <RWIN> = 134;

        <ESC> = 9;
//      <FK01> = 67;
//      <FK02> = 68;
//      <FK03> = 69;
//      <FK04> = 70;
//      <FK05> = 71;
//      <FK06> = 72;
//      <FK07> = 73;
//      <FK08> = 74;
//      <FK09> = 75;
//      <FK10> = 76;
//      <FK11> = 95;
//      <FK12> = 96;

//      <VOL-> = 122;
*** end ***
```

### Boot Configuration

#### Startup Screen Management

```bash
sudo vi /home/<USER>/.xinitrc
```

Add tvservice:

We could do this inside of /cardinal/browserstart, which is managed in pi_runner

```bash
while true; do
    tvservice -p
    /cardinal/browserstart
done;
```

#### Boot Sequence Customization

[Reference: Customizing Boot Screen](https://scribles.net/customizing-boot-up-screen-on-raspberry-pi/)

1. **Configure Console**

   ```bash
   sudo vi /boot/cmdline.txt
   ```

   Set to `console=tty3`

2. **Add Boot Parameters**

   ```bash
   splash quiet logo.nologo vt.global_cursor_default=0
   ```

3. **Custom Boot Logo**

   [Reference: Custom Boot Screen Guide](https://raspberry-projects.com/pi/pi-operating-systems/raspbian/custom-boot-up-screen)

   ```bash
   sudo apt-get update
   sudo apt-get install -y fbi

   sudo vi /etc/systemd/system/splashscreen.service
   ```

   ```ini title="Add"
   [Unit]
   Description=Splash screen
   DefaultDependencies=no
   After=local-fs.target

   [Service]
   ExecStart=/usr/bin/fbi -d /dev/fb0 --noverbose -a /opt/splash.png
   StandardInput=tty
   StandardOutput=tty

   [Install]
   WantedBy=sysinit.target
   ```

   ```bash
   sudo systemctl enable splashscreen
   ```

### Testing Checklist

#### Power Button

```
PASS    - Press power button, verify no shutdown
PASS    - Long press power button, verify no shutdown/reboot
```

#### Profiles/Settings (Pass/Fail on Pi4/Pi5)

```
F4P5    - Set to MP4 profile, verify video plays with sound
         - Sound issues on Pi4: [Reference](https://www.omglinux.com/raspberry-pi-os-bookworm/)
PASS    - Set to GIF profile, verify functionality
PASS    - Set bluetooth enabled, connect scanner, verify operation
F4P5    - Autodetect 720p screen (1360x768 on test device)
FAIL    - Set to 720p resolution
```

#### Connectivity

```
PASS    - Connect to non-NAC ethernet, verify check-in
P4?5    - Connect to IoT WiFi, verify check-in
?4P5    - SSH from laptop to Pi (expect failure)
?4P5    - SSH from slicer to Pi (expect success)
?4F5    - WiFi reporting from scan data shows correct SSID
```

#### Bluetooth

```
FAIL    - Connect scanner and scan test barcode
```

### Bookworm-Specific Updates

#### HDMI Configuration
(2024.04.22)

[Reference: HDMI Output Configuration](https://raspberrypi.stackexchange.com/questions/144876/forcing-hdmi-output-on-bookworm)

[Reference: HDMI Mode Documentation](https://www.raspberrypi.com/documentation/computers/config_txt.html#hdmi-mode)

!!! info "Pi5 Hardware Decoding"
    The Raspberry Pi 5 includes H.265 (HEVC) hardware decoding enabled by default. A hardware codec licence key is not needed.

#### Audio Configuration

(2024.05.09)

#### Pi4 HDMI Audio Issues

[Reference: Bookworm Feedback](https://github.com/raspberrypi/bookworm-feedback/issues/233)

[Reference: DietPi Forum](https://dietpi.com/forum/t/raspberry-pi-5-bookworm-aplay-l-no-soundcards-found/19760)

```
cat /proc/asound/cards
```

#### Audio Backend Options
[Reference: Audio Configuration](https://www.raspberrypi.com/documentation/computers/configuration.html)

!!! info "Audio Backend"
    Use this option to switch between PulseAudio and PipeWire backends. PipeWire was introduced in Bookworm.

!!! note "Not yet answered"
    [Raspberry Pi: Forum Topic](https://forums.raspberrypi.com/viewtopic.php?t=365650)

#### Troubleshooting Steps

1. **Check Audio Devices**
   ```bash
   cat /proc/asound/cards
   ```

2. **System Information**
   ```bash
   sudo apt-get install inxi -y
   inxi -ACSxxz
   ```

3. **Config.txt Settings**
   ```bash
   # Required settings
   dtparam=audio=on
   disable_overscan=1
   hdmi_force_hotplug=1
   hdmi_drive=2
   ```

4. **Pi4 Specific Settings**
   ```bash
   dtoverlay=vc4-fkms-v3d
   max_framebuffers=2
   ```

5. **Audio Control**

    [Reference: Speaker Troubleshooting](https://askubuntu.com/questions/1129013/speakers-not-working-sunrise-point-lp-hd-audio)

    ```bash
    sudo apt-get install -y pavucontrol
    pavucontrol
    ```

    Ensure auto-mute is disabled.
