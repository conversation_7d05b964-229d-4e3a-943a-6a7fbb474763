---
title: SD Image Building Guide QCAM
status: deprecated
---

# SD Image Building Guide QCAM

!!! warning "Deprecated"
    This document is no longer being used and is considered **deprecated**. For the latest information on building an image, please refer to the [SD Image Building](sd-image-building.md) guide.

---

## Overview

To build a new QCAM image, follow these steps.

---

### Download Raspberry Pi OS

- Visit: [Raspberry Pi OS](https://www.raspberrypi.com/software/operating-systems/)
- Select: **Raspberry Pi OS with Desktop**
- **Release Date**: May 3rd, 2023
- **System**: 32-bit
- **Kernel Version**: 6.1
- **Debian Version**: 11 (Bullseye)
- **Size**: 872MB
- Show SHA256 file integrity hash:
- Release Notes

=== "Save To:"
     `/downloads/2023-05-03-raspios-bullseye-armhf.img.xz`

=== "Copy To:"
     `/Users/<USER>/Library/CloudStorage/OneDrive-CardinalHealth/Pi_images/QCAM/`

---

### Flash the SD Card

Use [<PERSON><PERSON>](https://etcher.balena.io/) to burn the SD card with the downloaded image.

### Initial Setup (Without Network Connection)

1. Insert the SD card into the Raspberry Pi and power it on.
2. Follow the setup wizard:
   - **Country**: United States
   - **Language**: American English
   - **Timezone**: New York
   - Click **Next**.

### Create a User Account

- **Username**: `qcam`
- **Alternate Username**: `cah-pi-su`
- Enter and confirm password.
- Click **Next**.

!!! note
    Do not connect to WiFI, keep hitting 'Next', until you get 'Restart', and take it.

### Configure System Preferences

1. **Enable SSH (On boot)**:
      - Go to **Preferences > Raspberry Pi Configuration > Interfaces**
      - Enable **SSH**
2. **Modify UI Settings (On boot)**:
      - Go to **Preferences > Appearance Settings**
      - Uncheck all options; set layout to **No Image**
3. **Restrict Menu Access (On boot)**:
      - Go to **Preferences > Main Menu Editor**
      - Uncheck all except **System Tools**
4. **Remove Terminal Shortcut**:
      - Right-click the **Terminal** shortcut and remove it.

!!! bug "FixMe"
    Need to disable screen blanking (keep on always)

### Find Device IP Address

- Hover over the network icon in the top-right corner.
- SSH into the device:

  ```sh
  ssh cah-pi-su@<device-ip>
  ```

---

## Configure Wi-Fi

Edit the Wi-Fi config file:

```sh
sudo vi /etc/wpa_supplicant/wpa_supplicant.conf
```

Add the following:

```sh
network={
    ssid="cah-iot"
    psk="BVIm5bvQ65"
    key_mgmt=WPA-PSK
}
```

!!! info "Set Up `qcam` User"
    ```sh
    sudo adduser qcam
    ```

- Use standard **Camel Case** password.
- Assign necessary groups:

  ```sh
  sudo usermod -a -G video,input,lpadmin qcam
  ```

- Verify settings:

  ```sh
  ls -l /dev/input/
  groups qcam
  ```

---

## Enable Auto-login for `qcam`

Edit the **autologin config**:

```sh
sudo vi /etc/systemd/system/<EMAIL>.d/autologin.conf
```

Add the following:

```sh
[Service]
ExecStart=
ExecStart=-/sbin/agetty --autologin qcam --noclear %I \$TERM
```

```sh title="Run Command"
sudo ln -fs /lib/systemd/system/getty@.service /etc/systemd/system/getty.target.wants/<EMAIL>
```

Enable GUI auto-login:

```sh
sudo vi /etc/lightdm/lightdm.conf
```

Find and set (Search for `autologin-user` by typing `/autologin-user`):

```sh title="Set To:"
autologin-user=qcam
```

Reboot to test auto-login:

```sh
sudo reboot
```

---

## Install Updates and Required Packages

!!! info ""
    Log in as `cah-pi-su` user

```sh title="Screen grabs"
sudo apt-get install -y scrot
```

6.1 already has this installed

---

## Install Slicer Services

```sh
sudo mkdir /cardinal
```

`pi_runner`

- Manually set up this one, according to the instructions inside it, or push from Slicer

`pi_monitor`

- Load manually or through Slicer interface to the device (Runner must have been loaded first)

---

## Clean Up and Finalize Image

```sh
sudo su
```

```sh title="All of these as a single copy and paste into the terminal:"
apt-get clean
apt-get -y autoremove --purge
```

```sh title="All of these as a single copy and paste into the terminal:"
rm -rf /Downloads
rm -rf /home/<USER>/*
rm -rf /cardinal/save_values
```

```sh title="All of these as a single copy and paste into the terminal:"
systemctl stop pi-hmi.service
systemctl stop pi-runner.service
systemctl stop pi-config.service
find /var/log -type f -regex ".*\.gz$" -delete
find /var/log -type f -regex ".*\.[0-9]$" -delete
rm /var/log/*
echo 'yes' > /cardinal/needsexpand
echo -n "0" > /cardinal/boot_count.txt
echo -n "0" > /cardinal/grab_count.txt
cp /cardinal/browserstart_default /cardinal/browserstart
rm -rf /cardinal/localhtml/*
rm -rf /cardinal/log/*
rm -rf /cardinal/config_*
mkdir /cardinal/localhtml/
```

Create startup page:

```sh
echo '<head><meta http-equiv="refresh" content="5" ></head><body><center><br><br><br><br><br><br><br><table border="1" cellpadding="10"><tr><td style="font-size:30px"><center>Starting up...</center></td></tr><tr><td style="font-size:30px"><center>3 boot ups is the normal sequence for a new image.</center></td></tr><tr><td style="font-size:30px"><center>Screen may not fill to edges until all boots complete.</center></td></tr><tr><td style="font-size:30px"><center>If this screen does not disappear after 10 seconds,<br>then press Alt F4 to reset the screen.</center></td></tr></table></center></body>' > /cardinal/localhtml/index.html
```

Shutdown:

```sh
sudo shutdown -h now
```

---

## Load QCAM Software

```sh
sudo raspi-config --expand-rootfs
sudo reboot
```

```sh
sudo mkdir /cardinal/thirdparty
cd /cardinal/thirdparty
sudo curl -k --output qcam_20230623_120500.zip https://************/thirdparty?filetodownload=qcam_20230623_120500
```

```sh
sudo unzip qcam_20230623_120500.zip
cd /cardinal/thirdparty/QCAM_Release_2023-06-23
sudo chmod +x ./installer.sh
sudo ./installer.sh
```

!!! info "Output"
    ```
    --------------------
    Testing Python packages...
    cut: requirements.txt: No such file or directory
    Testing camera...
    Listing all available cameras:
    No cameras available!
    ERROR: arducam_64mp [9248x6944] camera is not available.
    Checking if all cameras are working:
    python: can't open file '/cardinal/thirdparty/QCAM_Release_2023-06-22/./test/test_camera_working.py': [Errno 2] No such file or directory
    Testing file and folder setup...
    Testing Python script installation...
    All tests completed.
    --------------------
    ```

## Verify Installation

```sh
qcamcli --version
systemctl status qcam_ui-runner.service
systemctl status qcam_monitor_weight.service
```

---
**End of document.**
