# Raspberry Pi Applications

There are many "applications" that we run on the Raspberry Pi:

---

## Monitor

This is the simplest application that should change the least. Its purpose is to call home to the server every 30 minutes and report the IP address of the device so that we can remote back into it using SSH.

---

## Runner

This application also checks into the server once a minute to check for new content, including profiles, application updates, and settings.

---

## Localhost Web Servers

(Search for `class MyServer`)

| Port | Application |
|------|------------|
| 6999 | template |
| 7000 | config |
| 7010 | hmi H.5.1 (builds profile-specific dynamic pages to be shown locally) |
| 7020 | logging |
| 7040 | network_* |
| 7080 | bluetooth |

---

## File Access

- `file:///cardinal/localhtml/index.html` → hmi

???+ info
    File interactions were originally used because they were simple to implement. However, as more features were needed (such as POST of data or dynamic content), file access became cumbersome. To address this, `from http.server import` HTTPServer was introduced as a lightweight solution, which is only accessed on the device itself.

---
**End of document.**
