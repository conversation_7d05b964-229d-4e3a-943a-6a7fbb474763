# Raspberry Pi Run Book

## Introduction

The **Raspberry Pi** project aims to provide clients with a **low-cost** computer that enables **web-based access** to designated applications.

**Key Features:**

- **Automatic startup** into a full-screen web browser
- **Limited user interaction** (bookmarks only, no OS access)
- **Optional auto-click bookmarks** (for sign boards, delay of 25s)

## Slicer Overview

Slicer is a **web application** for managing Raspberry Pi devices.

???+ note "Key Functions of Slicer"

    - **Device Visibility:** Tracks all registered devices.
    - **Usage Profiles:** Manages settings like **time zones** and **bookmarks**.
    - **Version Management:** Allows **upgrades/rollbacks** for device services.
    - **Access Control:** Restricts features based on user permissions.
    - **Security Compliance:** Runs **HTTPS only** (port **443**), hosted on **GCP (mac-mgmt-pr-cah)**.

**Configure a Pi using Slicer:**
▶️ [Watch Configuration Guide](https://slicer.cardinalhealth.net/download?filetodownload=SlicerConfigurationOfPi_720p.mov)

## **GitHub Repository**

All **source code** and **project notes** are stored in a **private GitHub repository**:

[GitHub Repository](https://github.com/CardinalHealth/cs_sp_pi_slicer) _(Restricted Access)_

**Admin Access:** David Ferguson & Jack Vincent Balcita

---

## **Security Compliance Review**

**Review Date:** _2021.08.26_
This review aligns with security recommendations from **Andrew Beardsley** _(Email: Aug 12, 2021, Jira: EUDE-773)_

???+ info "Security Controls & Responses"

    1️⃣ **Device Theft Prevention**
    🔹 Pending - Expected resolution via an orderable **'kit'**.

    2️⃣ **Centralized Management Tool**
    ✅ **Slicer** serves as the central tool for **device configuration and updates**.

    3️⃣ **Inventory Tracking**
    ❌ Out of scope for **software**; requires separate inventory management.

    4️⃣ **Network Monitoring**
    ✅ Devices **check in every minute** for monitoring _(EUDE-1329)_.

    5️⃣ **No Data Storage on Device**
    ✅ Uses **Chromium Incognito Mode** (no downloads or history saved).

    6️⃣ **OS Security & Hardening**
    ❌ Pending - Requires further policy implementation.

    7️⃣ **Device Authentication via Certificates**
    ✅ No longer required _(EUDE-1331)_.

    8️⃣ **Restrict Device Communication**
    ✅ Enforced via **IOT network controls**.

    9️⃣ **Least Privilege Access**
    ✅ Enforced through **whitelisted access only**.

    🔟 **No Password-Based Local Accounts**
    ❌ Pending _(EUDE-1333)_

    🔢 **CIS OS Baseline Compliance**
    ✅ Uses **Lynis Security Audits** _(EUDE-1350)_.

---

## **Hardware Setup**

???+ info "**Important:**"
    On **Raspberry Pi 4**, when using only one monitor, plug it into **HDMI0** (closest to power connection).

---
**End of document.**
