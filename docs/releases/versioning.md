# Versioning Guide

---

## Overview

Semantic Versioning (SemVer) is a versioning scheme that aims to convey meaning about the underlying changes in a release through the version number itself. It provides a consistent way to communicate when backwards-incompatible changes, new features, or bug fixes are introduced.

---

## Basic Format

A semantic version number takes the form of `X.Y.Z` where:

- **X** is the **major** version
- **Y** is the **minor** version
- **Z** is the **patch** version

Additional labels for pre-release or build metadata can be appended as extensions to this format.

---

## Version Increments

### Major Version (X)

Increment when making incompatible API changes.

- Changes that break backward compatibility
- Removing deprecated features
- Changing the expected behavior of existing functionality

**Example**: `1.9.0` → `2.0.0`

### Minor Version (Y)

Increment when adding functionality in a backward compatible manner.

- New features that don't break existing functionality
- Deprecating existing functionality (but not removing it)
- Large internal refactors that don't change the public API

**Example**: `1.8.3` → `1.9.0`

### Patch Version (Z)

Increment when making backward compatible bug fixes.

- Bug fixes and patches that don't change the API
- Performance improvements
- Small internal changes

**Example**: `1.8.2` → `1.8.3`

---

## Pre-release Versions

Pre-release versions can be denoted by appending a hyphen and a series of dot-separated identifiers after the patch version:

1.0.0-alpha 1.0.0-alpha.1 1.0.0-beta 1.0.0-beta.2 1.0.0-rc.1

---

## Build Metadata

Build metadata can be denoted by appending a plus sign and a series of dot-separated identifiers after the patch or pre-release version:

1.0.0+build.1 1.0.0-alpha+build.5

---

## Version Precedence

Precedence refers to how versions are compared to each other when ordered.

1. Major, minor, and patch versions are always compared numerically.
   - Example: `1.0.0` < `2.0.0` < `2.1.0` < `2.1.1`

2. When major, minor, and patch are equal, a pre-release version has lower precedence than a normal version.
   - Example: `1.0.0-alpha` < `1.0.0`

3. Precedence for pre-release versions is determined by comparing each dot-separated identifier numerically if they are numbers, and lexically if they are letters or hyphens.
   - Example: `1.0.0-alpha` < `1.0.0-alpha.1` < `1.0.0-beta` < `1.0.0-beta.2` < `1.0.0-rc.1` < `1.0.0`

---

## Adoption in Slicer Project

### Current Implementation

In the Slicer project, we follow Semantic Versioning with the SP.XX format representing our releases, where SP stands for "Service Pack" and XX is an incremental number.

Each SP release corresponds to a specific semantic version and may contain:

- Bug fixes (patch)
- New features (minor)
- Breaking changes (major)

### SP to SemVer Mapping

| SP Release | Semantic Version | Release Date | Type of Changes |
|------------|------------------|--------------|-----------------|
| SP.56      | 3.2.0            | 2025-03-12   | Feature Release |
| SP.55      | 3.1.1            | 2025-01-24   | Bug Fix Release |
| SP.54      | 3.1.0            | 2025-01-24   | Feature Release |
| SP.53      | 3.0.1            | 2024-12-16   | Bug Fix Release |

### Version Notes

- Major version changes (X) typically occur with significant infrastructure changes or when breaking compatibility with older clients.
- Minor version changes (Y) occur with new feature additions that maintain backwards compatibility.
- Patch version changes (Z) occur with bug fixes and minor improvements.

## Best Practices

1. **Document All Changes**: Maintain detailed release notes for each version.
2. **Version Control Tags**: Tag each release in your version control system.
3. **Communicate Changes**: Clearly communicate when a new version introduces breaking changes.
4. **Deprecation Notices**: Use deprecation notices in minor releases before removing functionality in a major release.
5. **Testing**: Ensure comprehensive testing for all versioned releases.

## References

- [Official Semantic Versioning Specification](https://semver.org/)
- [NPM's Semantic Versioning Documentation](https://docs.npmjs.com/about-semantic-versioning)
- [GitHub's Semantic Versioning Guide](https://github.com/semver/semver/blob/master/semver.md)
