# SP.11 (2021-07-20)

!!! info "Release Information"
    - **Version**: SP.11
    - **Release Date**: July 20, 2021
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Remote Pi reboot from Slicer configuration page
- Allow full keyboard access, dependent on profile setting
- Build index page in a way that should always be able to load, to address the issue where it shows as missing sometimes

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.1.5 | Add note to top of selection list, that only a single click is required |
| pi_monitor | M.2.2 | Same as before |
| pi_runner | R.2.1 | Send more in checkin: uptime, boot_count, React to reboot request, React to setting of all keyboard access |
| pi_hmi | H.1.8 | If all keyboard access is allowed, show ctrl key hints in the tabbed homepage view |
| pi_network | N.1.2 | Same as before |

### Image Changes

!!! info "Image Updates"
    - No Changes

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
