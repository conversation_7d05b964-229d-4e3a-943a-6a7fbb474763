# SP.13 (2021-08-14)

!!! info "Release Information"
    - **Version**: SP.13
    - **Release Date**: August 14, 2021
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Build second page with device info, by clicking on bolded ID title field
- Add first round of security audit scanning
- Wait for screen resolution to be set before making screen settings on boot
- Allow support for disabling kiosk mode on the browser on the pi
  - This allows F11 to be used to switch back and forth to Full Screen mode
  - This allows the open keys to be used for 'ctrl tab' to move between tabs, even for the ERP key grabbed page at PR005, when exited from full screen mode

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.1.6 | Extend the polling time, to be better where there are lots of devices |
| pi_monitor | M.2.2 | Same as before |
| pi_runner | R.2.2 | Allow support for disabling kiosk mode, Add startup delay to let HMI settings settle |
| pi_hmi | H.2.0 | Show ID screen with MAC address, and other items, If not in kiosk mode, show prompt for F11 to exit full screen mode |
| pi_network | N.2.0 | Add support to show cah-iot ssid status on the ID screen, Not ready to connect to it yet, just supporting development |
| pi_security | S.1.0 | New service, to report on security issues |

### Image Changes

!!! info "Image Updates"
    - Install lynis security audit tool
    - Lock down the default account

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
