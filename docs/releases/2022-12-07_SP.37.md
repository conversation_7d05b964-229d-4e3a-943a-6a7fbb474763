# SP.37 (2022-12-07)

!!! info "Release Information"
    - **Version**: SP.37
    - **Release Date**: December 7, 2022
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Better SSH rules definition for the case of multiple address configuration

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.2.5 | - |
| pi_config | C.1.2 | Support manual configuration of a generic image |
| pi_hmi | H.4.6 | If manual settings are present, then use them, otherwise default to original |
| pi_logging | L.2.9 | Make a privoxy log by day, that is viewable on the pi |
| pi_monitor | M.2.4 | - |
| pi_network | N.6.8 | If manual network settings are found, then use those |
| pi_organization | O.1.0 | Set the company specifics from settings |
| pi_runner | R.7.7 | Handle manual settings, handle multiple call_homes cleanly, add timeout on get_cpu_temperature |
| pi_security | S.2.0 | Pull from settings, to add sshd target(s) and allowed user(s) |
| pi_settings | s.0.5 | The one source of configuration settings |

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
