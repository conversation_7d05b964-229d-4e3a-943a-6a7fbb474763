# Release Notes

## 1. Known Issues

### **2023.11.01**

**Screen resolution not correctly automatically detecting the screen attached:**

- Sometimes the default "automatically detect" setting results in the screen being oversized, undersized, or generally incorrect.
- This can happen when unplugging the HDMI cable from the Pi, powering off, then back on, and after a minute, plugging the HDMI cable back in.
- **Fix:** Reboot the device twice. This can be accomplished remotely via the Slicer interface:
- Browse to the device configuration page:
    [Slicer Interface](https://slicer.cardinalhealth.net/reports?serial=10000000e3669edf)
- Find the **"Request Reboot"** line, select the reboot marker value from the dropdown, then click **"Submit"**.
- Wait a few minutes, refresh the Slicer page to get a new reboot marker number, then request another reboot.
- Wait a few minutes and check that the device displays correctly (filled to edges, not overfilled).

---

## 2. Configuration Notes

### To configure printing on the Pi (Base Image 2.3.5 and later):

- On the main page, click the bold **"ID"** in the upper-left corner.
- On the line with **"Printer"**, click the bold **"Configure"**.
- Navigate to: **Administration -> Add Printer**
  - Username: **printer**
  - Password: **printer**
- When done, press **Alt + F4** to return.
- Click the bold **"ID"** to return to the main page.

---

## 3. Target Service Pack

### **2024.02.17**

Bring all up to this level.

- **Target SP:** `SP.47`
- **Optional Service:** `pi_thirdparty, P.0.0 to P.9.9`

---

## 4. Rollback Service Pack

- **Rollback SP:** `SP.47`
- The rollback service pack is the lowest version allowed for rollback, both for the collection and individual services.
