# SP.4 (2021-06-21)

!!! info "Release Information"
    - **Version**: SP.4
    - **Release Date**: June 21, 2021
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Adds support for the pairing of Zebra RS6000 bluetooth connected scanner
- This feature is hidden by default. Slicer must be set to enable Bluetooth on each device

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.1.0 | Support interfacing to Zebra RS6000 scanner |
| pi_monitor | M.2.2 | Same as before |
| pi_runner | R.1.6 | Screen Height and Width sent to <PERSON>lice<PERSON>, Bluetooth Enable picked up from <PERSON>licer is saved locally |
| pi_hmi | H.1.3 | Add support for bluetooth pairing visual interface |
| pi_network | N.1.2 | Same as before |

### Image Changes

!!! info "Image Changes"
    - Adds the background scheduler required by the pi_bluetooth service
    - Drive HDMI output always (Force hot-plug)
    - Adds the screen capture driver scrot

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
