# SP.21 (2021-10-14)

!!! info "Release Information"
    - **Version**: SP.21
    - **Release Date**: October 14, 2021
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Allow Pi OS updates, on request in Slicer
- Collect network usage data
- Do not let USB devices sleep
- Support browser restart on inactivity
- OS update support
- Limit inbound to Slicer only
- System logging service active
- Bluetooth change to not show RSSI column, because we no longer collect that data

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.2.2 | Do not show RSSI column, because we no longer collect that data |
| pi_monitor | M.2.2 | Same as before |
| pi_runner | R.3.8 | Be able to pull service updates from Slicer, Do not let USB devices sleep, Support browser inactivity restart, Network monitoring |
| pi_hmi | H.2.0 | Same as before |
| pi_network | N.2.0 | Same as before |
| pi_security | S.1.5 | Scan for updates, Allow Slicer to have us take updates, Limit inbound to <PERSON>licer only |
| pi_logging | L.1.4 | System activity logging |

### Image Changes

!!! info "Image Updates"
    - No Changes

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
