# SP.10 (2021-07-12)

!!! info "Release Information"
    - **Version**: SP.10
    - **Release Date**: July 12, 2021
    - **Type**: Service Pack

## Known Issues

!!! info "Current Status"
    - None reported

## Changes

### System Maintenance

- Make a tab mode for the bookmarks and browser; go between using Alt 1, Alt 2, etc
- Allow Slicer to enable showing a Menu to show shutdown and reboot on the pi
- Screen resolution set by Slicer configuration

### Component Updates

| Component | Version | Changes |
|-----------|---------|---------|
| pi_bluetooth | B.1.4 | Once paired, turn off scan, to allow for fast data transfers |
| pi_monitor | M.2.2 | Same as before |
| pi_runner | R.2.0 | Pulling out screen resolution setting, and show menu option |
| pi_hmi | H.1.7 | Tabs mode for browser and Menu for shutdown/reboot |
| pi_network | N.1.2 | Same as before |

### Image Changes

!!! info "Image Updates"
    - Browser start pulled out of init, to allow for it to be configured for tabs by pi_runner

## Additional Information

!!! tip "Upgrade Notes"
    No special upgrade procedures are required for this release.

## Support

For any issues or questions regarding this release, please contact the support team.
