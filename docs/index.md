---
title: Slicer Documentation
description: Central knowledge base for the Slicer project, containing system configurations, usage instructions, and development processes
icon: material/book-open-page-variant
---

# Slicer Documentation

<p align="center">
  <img src="images/cardinal-logo-lg.png" alt="Slicer Logo" width="250" height="250" />
</p>

Welcome to the **Slicer Documentation**! This guide serves as a central knowledge base for developers and users working on the Slicer project. Here, you'll find detailed information about system configurations, usage instructions, and development processes.

## Quick Links

=== "Documentation"
    - [System Overview](#system-overview)
    - [Architecture](#architecture)
    - [Disaster Recovery Plan](#disaster-recovery-plan)
    - [Standard Operating Procedures](#sop)
    - [Backup and Recovery](#backup-and-recovery)

=== "Technical"
    - [Communication from the Pi](#communication-from-the-pi)
    - [Runtime Layout](#runtime-layout)
    - [Pi Services](#pi-services)
    - [GCP Services](#gcp-services)
    - [Pi Services Versioning](#pi-services-versioning-on-slicer)

=== "Project Info"
    - [Authors](#authors)
    - [Acknowledgments](#acknowledgments)

## System Overview { data-toc-label="System Overview" }

!!! info "Project Overview"
    The **Slicer Project** is designed to run on **Raspberry Pi**, supporting automated processing and data management functionalities. This documentation consolidates all relevant project details, making it easier for developers and maintainers to navigate.

### Key Features

- [x] Fully documented setup and configuration
- [x] Branch and version tracking
- [x] Guidelines for deployment and maintenance
- [x] Raspberry Pi compatibility
- [x] User-friendly interface for seamless interaction
- [x] Robust API for integration with other services

### Built With

![Raspberry Pi](https://img.shields.io/badge/Raspberry_Pi-4B-red){ .off-glb }
![Python](https://img.shields.io/badge/Python-3.8-blue){ .off-glb }

---

## Architecture

!!! abstract "System Architecture"

    |Item| ||
    |:---:|:---:|:---:|
    |Application Name|Slicer|
    |Application Status|Standard|
    |Criticality[^A1]|Operational|
    |RTO[^A2]|Tier 4 (120+ Hours)|
    |RPO[^A3]|Data Recovery Not Required|
    |Landscape[^A4]|GCP|
    |Primary[^A5]|us-central1-c|
    |Alternate[^A6]|(none yet)|
    |Project|`mac-mgmt-pr`|
    |Primary Server|`lpec5009slicr01`|
    |Boot disk|31 GB, standard|lpec5009slicr01-boot|
    |Data Disk|60 GB, SSD|lpec5009slicr01-disk-lpec5009slicr01-d|
    |DNS|`slicer.cardinalhealth.net`|
    |IP address|`***********`|
    |Certificate|???|

---

## Disaster Recovery Plan

!!! danger "Recovery Procedures"
    In the event of GCP disk loss, follow these procedures to ensure data integrity and system restoration:

    1. Use the latest one-hour disk snapshot to set up a new instance
    2. Verify system integrity and ensure all necessary services are running
    3. Restore any additional configurations and data from backups if required
    4. Conduct a system test to confirm full functionality

---

## SOP

!!! note "Standard Operating Procedures"
    There is no particular maintenance action(s) required, in order to provide day-to-day operation.

---

## Backup and Recovery

### Backup Strategy

!!! tip "Backup Methods"
    - **Automated Hourly Snapshots**: Backups are set up in GCP to create system snapshots every hour
    - **Daily Database Backups**: Critical databases are backed up daily and stored securely
    - **Weekly Full System Snapshots**: A complete system backup is taken weekly to ensure redundancy
    - **Offsite Backup Storage**: Backups are securely stored in a separate GCP region for disaster recovery
    - **Incremental Backups**: Only changed data is backed up to optimize storage and speed

### Recovery Procedure

!!! example "Recovery Steps"
    1. **Identify the Latest Snapshot** – Determine the most recent viable backup
    2. **Rebuild from Snapshot** – Use the latest one-hour snapshot to set up a new instance
    3. **Verify System Integrity** – Ensure all necessary services are running properly
    4. **Restore Additional Configurations** – Apply any additional configuration or database backups if required
    5. **Perform System Testing** – Conduct a full system test to confirm functionality and data consistency
    6. **Deploy Fixes if Necessary** – Address any detected issues before bringing the system live

    Regular recovery drills are conducted to ensure a smooth and reliable restoration process.

---

## Communication from the Pi

!!! info "Communication Methods"

    | **Interface**      | **User**        | **pi_runner**               |
    |------------------|----------------------------------|-------------------------------------------|
    | **Method**       | Chromium Browser                | requests library                         |
    | **Options**      | Bookmark List                   | N/A                                       |
    | **Restriction**  | Whitelist Proxy                 | N/A                                       |
    | **Corp Network** | N/A                              | N/A                                       |
    | **Destination**  | Any whitelisted URL             | [slicer.cardinalhealth.net](https://slicer.cardinalhealth.net) |

## Runtime Layout

!!! abstract "System Components"

    ### Deployment Overview
    | **Category**        | **Server (Slicer)**                        | **Client**             |
    |--------------------|-------------------------------------------|------------------------|
    | **Data Location**  | `/mnt/disks/SSD/var/log/slicer`<br>`/var/log/slicer` | `/cardinal/`           |
    | **Code Location**  | `/var/www/html/`                          | `/cardinal/`           |
    | **Deployed Files** | `*.py`                                   | `pi_*.py`              |
    | **Repository Files** | `slicer_wsgi_*.py`                     | `pi_*.py`              |
    | **Programming Language** | `Python`                           | `Python 3`             |
    | **Runtime**        | `WSGI`                                   | `(Custom Wrapper)`     |
    | **Static Files**   | `/var/www/htmlfiles/`                    | `N/A`                  |
    | **Application**    | `Apache`                                 | `Shell`                |
    | **Startup Method** | `SystemD`                                | `SystemD`              |
    | **Operating System** | `CentOS`                              | `Raspbian`             |
    | **Location**       | `GCP`                                    | `Raspberry Pi`         |

---

## Pi Services

!!! tip "Image Building"
    The information needed to build a new full image is available in the [Image Building Documentation](imaging/sd-image-building.md).

Files named as `pi_*.py` fully describe a service to be run on the remote Pi device.

---

## GCP Services

!!! info "Server Information"
    This is the code that runs on a central server, managing remote devices. It is set up on a **Production GCP server**.

    Refer to the [GCP Servers Documentation](getting-started/servers/gcp/gcp-servers.md) file for further details.

### Key Functionalities

1. **Remote Data Collection** – Gathers current IP addresses from connected devices
2. **Device Status Reporting** – Displays real-time device status in a user-friendly table
3. **Configuration Management** – Provides bookmarks and whitelist configurations to devices
4. **Pi Service Updates** – Manages and distributes updates for Pi services

### Deployed Files

Files beginning with `slicer_wsgi_*.py` are deployed to the server at `/var/www/html/*.py`. Additionally, a corresponding Apache configuration file is created at `/etc/httpd/conf.d/python-*.conf`.

Example deployed files:

- `slicer_wsgi_checkin.py` – Handles check-ins from field devices
- `slicer_wsgi_reports.py` – Generates reports based on check-in data

!!! success "Access Point"
    Access the GCP-hosted Slicer services at:
    [https://slicer.cardinalhealth.net](https://slicer.cardinalhealth.net)

---

## Pi Services Versioning on Slicer

!!! example "Version Structure"
    The `pi_(service)` files are loaded onto the Slicer server to be served out to the Raspberry Pi devices.

### Storage Structure

```
/var/www/html/pi_services/pi_runner/R.1.0/pi_runner.py
/var/www/html/pi_services/pi_runner/R.1.1/pi_runner.py
```

Each version (`R.x.x`) corresponds to a different release of the service, ensuring that multiple versions can be maintained and deployed as needed.

---

## Authors

!!! info "Contributors"
    - **David Ferguson** – Lead Developer
    - **Jack Vincent Balcita** – Developer

---

## Acknowledgments

### Markdown Resources

- [Markdown Guide](https://www.markdownguide.org)
- [Basic Writing and Formatting Syntax](https://docs.github.com/en/github/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax)
- [Mastering Markdown](https://guides.github.com/features/mastering-markdown/)

### Live Edit of Markdown Content

Edit Markdown content live using:
- [StackEdit](https://stackedit.io/app#)

[^A1]: What is the criticality of this business process, application, or infrastructure component?
    **Essential**: Business has defined these processes, systems, and data as crucial for operating the business. Cardinal Health will likely cease to exist or experience major operational impacts if these first-tier processes, systems, and data are unavailable or compromised. These systems must be recovered within the first 24 hours of a disaster, cyber event, or outage.
    **Mission**: Business has defined these processes, systems, or data as mission-critical to support the business. There will be significant, but not existential harm to employee productivity, Cardinal Health reputation, revenue, and customer impact if these second-tier systems are unavailable or compromised. These processes, systems, and data must be recovered within the first 24 - 48 hours of a disaster or outage.
    **Business**: Business has defined these processes, systems, or data as critical to support the business. These systems are critical to day-to-day operations but will not cause existential harm to Cardinal Health, patients, customers, or employees if they are unavailable for 48 hours. These systems do need to be recovered within 48 – 120 hours following a disaster.
    **Operational**: Processes, systems, or data defined as required for day-to-day operations of the business but not critical in the event of a disaster or other major system outage/event. There will be a reduction in organizational efficiency but otherwise limited impact on Cardinal Health if these fourth-tier processes, systems, or data are unavailable or compromised.

[^A2]: What is the Recovery Time Objective (RTO) for this application/data/process?
    This is the timeframe in which the business expects this application/data/process to be recovered following a disaster (this is the timeframe before a financial, operational, and/or legal impact is expected).
    **Tier 1** (0 - 24 Hours)
    **Tier 2** (24 - 48 Hours)
    **Tier 3** (48 - 120 Hours)
    **Tier 4** (120+ Hours)
    **DR Not Required**
    **Needs Assessed**
    **Incubate Status**

[^A3]: What is the maximum timeframe amount of data that can be lost due to an unplanned solution outage?
    **The Recovery Point Objective (RPO)** is the maximum allowable amount of data loss the business can tolerate following an outage, business interruption, or disaster. (RPO) describes a point in time to which data must be restored in order to be acceptable to the owner(s) of the processes supported by that data. This is often thought of as the time between the last available backup and the time a disruption could potentially occur. The RPO is established based on tolerance for loss of data or re-entering of data.

[^A4]: Where is your application located?
    Examples: **GCP**, **AWS**, **Dublin**, **Colo**, **SaaS**, **Azure**, **Cardinal Site** (Manufacturing/Distribution), **Other**.

[^A5]: What is your Primary Location? (GCP Zone)

[^A6]: What is your Alternate Location? (GCP Zone)
