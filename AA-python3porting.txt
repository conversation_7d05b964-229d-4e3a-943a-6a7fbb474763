AA-python3porting.txt


String issues requiring *.encode()

String issues for split, needing byte like (b'\n')

urllib import (http://python3porting.com/problems.html)

instead of "from cgi import parse_qs"
use "from urllib.parse import parse_qs"
    Which leaves the dictionary result of parse_qs making all strings as 'bytes',
    which json.dumps hates.
    so must use "d = parse_qs(request_body.decode('utf-8')
