# A certificates for slicer page services

service = "certificates"
version = service + '.0.4.c'

release_notes = """
0.4
2023.02.02
Write a report file when processing an uploaded certificate

0.3
2023.02.01
If the active cert was warnings of days less than threshold, then show them in red.

0.2
2022.11.30
Handling new apache 2 setups

Able to take a GoDaddy private key and certificate, and make a working TLS trust.

Save the private key file as "slicer.key"
Take the crt file from GoDaddy, rename to "slicer.crt"

zip those two files into a single zip file (no folder structure).
Upload that zip to certificates interface page.Refresh page to see content.
Click the "Copy these certificates to (active)".
Wait for the process to happen, and self restart of apache.
Reload page, and see that the url trust indicator is now happy.

"""

_ = """
This file gets loaded to:
/var/www/html/certificates.py

using:
sudo vi /var/www/html/certificates.py

It also requires:

sudo vi /etc/httpd/conf.d/python-certificates.conf
----- start copy -----
WSGIScriptAlias /certificates /var/www/html/certificates.py
----- end copy -----

sudo chown apache:apache /var/www/html/certificates.py

sudo systemctl restart httpd


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Extra runner thread is created here:

sudo vi /var/www/html/certificates-runner
sudo chmod +x /var/www/html/certificates-runner

# ===== begin: start file
#!/usr/bin/env python
import certificates
certificates.main()
# ===== end: start file


# https://net2.com/how-to-use-systemd-to-troubleshoot-linux-problems/

sudo vi /lib/systemd/system/certificates-runner.service
sudo systemctl daemon-reload
sudo systemctl stop certificates-runner.service
sudo systemctl start certificates-runner.service
sudo systemctl enable certificates-runner.service

systemctl status certificates-runner.service

sudo systemctl restart certificates-runner.service

sudo systemctl status certificates-runner.service

systemctl --failed


# Logging of std out
cat /var/log/syslog | fgrep certificates-runner

OR

tail -f /var/log/syslog | fgrep certificates-runner


# ===== begin: service file
[Unit]
Description=Slicer maintenance daemon
After=network.target
StartLimitIntervalSec=0
StartLimitBurst=5
StartLimitIntervalSec=10

[Service]
ExecStart=/var/www/html/certificates-runner
WorkingDirectory=/var/www/html/
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=1
User=root

[Install]
WantedBy=multi-user.target
# ===== end: service file


!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!



test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import certificates; print(certificates.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/certificates

https://slicer.cardinalhealth.net/certificates?siteid=PR005

https://slicer.cardinalhealth.net/certificates?serial=100000002a5da842

https://slicer.cardinalhealth.net/certificates?monitorNot=M.1.2

"""

_Testing = """

on DWF laptop:
cd /Users/<USER>/Documents/GIT/cs_sp_pi_slicer/

python3 -m unittest slicer_wsgi_certificates


"""

import cgi
import copy
import datetime
import hashlib
import json
import os
import pdb
import shlex
import subprocess
import sys
import time
import traceback
import unittest

from tempfile import TemporaryFile

try:
    open('/dev/shm/running_exceptions_' + service, 'w').write('')
except:
    pass

startup_exceptions = ''

service_config = {}
path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    apache_user_name = service_config['apache_user_name']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import login
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:  # for unittest to work
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

# where the certs live, that are the currently active certs:
s_days_remaining_threshold_for_flag = 90
s_certificates_dir = '/etc/pki/tls/private/'
s_certificate_locations = {
    'ca-bundle': '/etc/pki/tls/private/domain-cabundle.pem',
    'slicer-key': '/etc/pki/tls/private/slicer.key',
    'domain-ca': '/etc/pki/tls/private/domain-ca.crt',
    'slicer-cert': '/etc/pki/tls/private/slicer.cert',
}

s_upload_spot_for_new_certs = '/dev/shm/certificates/'

s_request_file_location = "/dev/shm/certificates_request.txt"
s_results_file_location = "/dev/shm/certificates_result.txt"

# ignore_line_count_down_for_syntax_checking=26
s_localhost_private_key = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

_ = """
2024.02.08

Message starts "Could not read certificate from", and ends with "Unable to load certificate"



"""


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def any_flags():
    # ====================================
    return_value = False

    try:
        any_concerns = False
        if os.path.isfile(s_results_file_location):
            #            print ('file found')
            try:
                content_of_response = str(open(s_results_file_location, 'r').read())
                result = json.loads(content_of_response)

                #                print (str(result))

                for result_d in result:
                    if result_d['directory'] == '(active)':
                        for file_record_d in result_d['files']:
                            try:
                                if float(file_record_d['warning'].split()[0]) < s_days_remaining_threshold_for_flag:
                                    any_concerns = True
                            except:
                                #                                print (str(traceback.format_exc().replace("\n","<br>").replace("\"","'")))
                                pass

            except:
                print(str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'")))
        else:
            print('file not found')

        if any_concerns:  # add a test here, that should show that we need some attention
            _ = """
                Need to have the runner do a once every 30 minute self run (just do the status call, but on a timer)
                Write out a summary of expirations, and specifically, if the (active) has any expiration
                less than 90 days out, then set this flag.

                In the main view (active), if less than 90 days, show yellow, less than 60, show red.


            """

            return_value = True
    except:
        pass

    return return_value


# ====================================
def status_report():
    # ====================================
    # show what our runner is doing
    return_value = '(certificates status)'

    status = os.system('systemctl is-active --quiet certificates-runner.service')

    if str(status) == '0':
        return_value = 'runner: ok'
    else:
        return_value = 'runner: FAILED!!!'

    return return_value


# ====================================
def certificate_report(files_found):
    # ====================================

    # get data into our format
    parsed_items = {}
    for directory in files_found:
        if not directory in parsed_items:
            parsed_items[directory] = {}

        for file_name in files_found[directory]:
            parsed_items[directory][file_name] = files_found[directory][file_name]
            parsed_items[directory][file_name]['matches'] = ''

    # do the matching work
    for directory in parsed_items:
        for file in parsed_items[directory]:
            for directory_sub in sorted(parsed_items):
                # for directory_sub in parsed_items:
                if directory_sub != directory:
                    if file in parsed_items[directory_sub]:
                        if parsed_items[directory][file]['md5'] == parsed_items[directory_sub][file]['md5']:
                            if parsed_items[directory][file]['matches']:
                                parsed_items[directory][file]['matches'] += ', '
                            parsed_items[directory][file]['matches'] += directory_sub

    # build it for reporting
    sorted_list = []
    for directory in sorted(parsed_items):
        files_list = []
        for file in sorted(parsed_items[directory]):
            files_list.append({'name': file, 'status': parsed_items[directory][file]['status'],
                               'md5': parsed_items[directory][file]['md5'],
                               'matches': parsed_items[directory][file]['matches'],
                               'warning': parsed_items[directory][file]['warning']})

        sorted_list.append({'directory': directory, 'files': files_list})
    return sorted_list


# Main is the loop for the "certificates-runner" that the service starts
# ====================================
def main():
    # ====================================
    pass_count = 0

    open('/dev/shm/running_exceptions_' + service, 'w').write('start')

    time_for_self_refresh = 0  # Set this zero, so that it fires quickly after boot/restart

    while True:
        pass_count += 1

        request = ''

        if time.time() > time_for_self_refresh:
            request = 'status'

        if os.path.isfile(s_request_file_location):
            request = open(s_request_file_location, 'r').read()
            os.remove(s_request_file_location)

        if request:
            time_for_self_refresh = time.time() + 30 * 60  # top off, from this point forward
            if request == 'uploaded':
                # there is a zip file in the s_upload_spot_for_new_certs location
                # Unzip it, into the certificate holding location
                try:
                    list_of_files = os.listdir(s_upload_spot_for_new_certs)
                    for file_name in list_of_files:
                        if ".zip" in file_name and 'ssl_' in file_name:
                            report = ''

                            command = 'sudo apt install unzip -y'
                            the_pass, the_fail = do_one_command(command)
                            report += '\n'
                            report += 'command = ' + command + '\n'
                            report += 'the_pass = ' + the_pass + '\n'
                            report += 'the_fail = ' + the_fail + '\n'

                            directory_to_make = s_certificates_dir + file_name.replace('ssl_', '').replace('.zip',
                                                                                                           '') + '/'
                            command = 'unzip ' + s_upload_spot_for_new_certs + file_name + ' -d ' + directory_to_make
                            the_pass, the_fail = do_one_command(command)
                            report += '\n'
                            report += 'command = ' + command + '\n'
                            report += 'the_pass = ' + the_pass + '\n'
                            report += 'the_fail = ' + the_fail + '\n'

                            # clean up the original zip
                            os.remove(s_upload_spot_for_new_certs + file_name)

                            # try to extract the contents of the password protected zip file inside
                            list_of_files_inside = os.listdir(directory_to_make)
                            the_zip = ''
                            the_pfx = ''
                            the_txt = ''
                            the_cert = ''
                            the_key = ''
                            for file_inside in list_of_files_inside:
                                if '.pfx' in file_inside:
                                    the_pfx = file_inside
                                if '.7z' in file_inside:
                                    the_zip = file_inside
                                if '.txt' in file_inside:
                                    the_txt = file_inside
                                if '.cert' in file_inside:
                                    the_cert = file_inside
                                if '.key' in file_inside:
                                    the_key = file_inside

                            report += '\n'
                            report += 'the_txt = ' + the_txt + '\n'
                            report += 'the_zip = ' + the_zip + '\n'
                            report += 'the_pfx = ' + the_pfx + '\n'

                            if the_txt and (the_zip or the_pfx):
                                # got them both, now use them
                                the_password_to_use = open(directory_to_make + the_txt, 'r').read().split('\n')[
                                    0]  # be sure to just get the contents of the first line
                                report += 'the_password_to_use = ' + the_password_to_use + '\n'

                                if the_zip:
                                    # Strange, but true, there needs to be no space after the -p, before the password content, and also none after the -0
                                    command = 'sudo 7za e -p' + the_password_to_use + ' -o' + directory_to_make + ' ' + directory_to_make + the_zip
                                    the_pass, the_fail = do_one_command(command)
                                    report += '\n'
                                    report += 'command = ' + command + '\n'
                                    report += 'the_pass = ' + the_pass + '\n'
                                    report += 'the_fail = ' + the_fail + '\n'

                                # ok, I should now have the pfx file. Now do the extractions.
                                command = 'sudo openssl pkcs12 -in ' + directory_to_make + 'slicercardinalhealthnet.pfx -clcerts -nokeys -out ' + directory_to_make + 'slicer.cert -passin pass:' + the_password_to_use + ' -legacy'
                                the_pass, the_fail = do_one_command(command)
                                report += '\n'
                                report += 'command = ' + command + '\n'
                                report += 'the_pass = ' + the_pass + '\n'
                                report += 'the_fail = ' + the_fail + '\n'

                                command = 'sudo openssl pkcs12 -in ' + directory_to_make + 'slicercardinalhealthnet.pfx -nocerts -nodes -out ' + directory_to_make + 'slicer.key -passin pass:' + the_password_to_use + ' -legacy'
                                the_pass, the_fail = do_one_command(command)
                                report += '\n'
                                report += 'command = ' + command + '\n'
                                report += 'the_pass = ' + the_pass + '\n'
                                report += 'the_fail = ' + the_fail + '\n'

                                command = 'sudo openssl pkcs12 -in ' + directory_to_make + 'slicercardinalhealthnet.pfx -cacerts -nokeys -out ' + directory_to_make + 'slicer_cabundle.pem -passin pass:' + the_password_to_use + ' -legacy'
                                the_pass, the_fail = do_one_command(command)
                                report += '\n'
                                report += 'command = ' + command + '\n'
                                report += 'the_pass = ' + the_pass + '\n'
                                report += 'the_fail = ' + the_fail + '\n'

                                command = 'sudo openssl pkcs12 -in ' + directory_to_make + 'slicercardinalhealthnet.pfx -cacerts -nokeys  -nodes -out ' + directory_to_make + 'domain-ca.crt -passin pass:' + the_password_to_use + ' -legacy'
                                the_pass, the_fail = do_one_command(command)
                                report += '\n'
                                report += 'command = ' + command + '\n'
                                report += 'the_pass = ' + the_pass + '\n'
                                report += 'the_fail = ' + the_fail + '\n'

                            if the_cert:
                                the_content_to_use = open(directory_to_make + the_cert, 'r').read()
                                open(directory_to_make + the_cert, 'w').write(the_content_to_use)

                            if the_key:
                                the_content_to_use = open(directory_to_make + the_key, 'r').read()
                                open(directory_to_make + the_key, 'w').write(the_content_to_use)

                        open(directory_to_make + 'report', 'w').write(report)

                except:
                    open('/dev/shm/running_exceptions_' + service, 'w').write(traceback.format_exc().replace("\"", "'"))
                    pass

            if 'delete:' in request:
                try:
                    cert_folder = request.split(':')[1] + '/'
                    if cert_folder:
                        directory_to_delete = s_certificates_dir + cert_folder
                        command = 'sudo rm -rf ' + directory_to_delete
                        do_one_command(command)
                except:
                    pass

            if 'copy:' in request:
                directory_to_copy_from = s_certificates_dir + request.split(':')[1] + '/'

                found_files = os.listdir(directory_to_copy_from)

                extensions = ['.crt', '.cert', '.key', '.pem']
                #                list_to_copy = ['domain-ca.crt', 'slicer.cert', 'slicer.key', 'slicer_cabundle.pem']

                for found_file in found_files:
                    item_to_copy = ''
                    for extension in extensions:
                        if extension in found_file:
                            item_to_copy = found_file

                    if item_to_copy:
                        source_file = directory_to_copy_from + item_to_copy
                        if os.path.isfile(source_file):
                            command = 'sudo cp ' + source_file + ' ' + s_certificates_dir + item_to_copy
                            do_one_command(command)

                # Then restart the Apache service, to make it use the new certificates
                command = 'sudo systemctl restart apache2'
                do_one_command(command)

            # for now, always refresh the report
            if True:  # if request == 'status'
                files_found = {}

                for top_level in os.listdir(s_certificates_dir):
                    if os.path.isdir(s_certificates_dir + top_level):
                        directory = top_level
                        if not directory in files_found:
                            files_found[directory] = {}
                        for second_level in os.listdir(s_certificates_dir + top_level):
                            if not os.path.isdir(s_certificates_dir + top_level + '/' + second_level):
                                files_found[directory][second_level] = {
                                    'full_path': s_certificates_dir + top_level + '/' + second_level, 'md5': '',
                                    'status': '', 'warning': ''}
                    else:
                        directory = '(active)'
                        if not directory in files_found:
                            files_found[directory] = {}
                        files_found[directory][top_level] = {'full_path': s_certificates_dir + top_level, 'md5': '',
                                                             'status': '', 'warning': ''}

                # fill in details
                now = datetime.datetime.now()
                for directory in files_found:
                    for name in files_found[directory]:
                        location = files_found[directory][name]['full_path']
                        if location[-4:] == '.pem' or location[-4:] == '.crt' or location[-5:] == '.cert':
                            command = 'sudo openssl x509 -enddate -noout -in ' + location
                            mem_string, fails = do_one_command(command)
                            files_found[directory][name]['status'] = mem_string + '\n' + fails

                            try:
                                if 'notAfter=' in files_found[directory][name]['status']:
                                    # s='notAfter=Jul 12 19:30:54 2044 GMT'
                                    # splits = s.split('=')[1].split()
                                    # ['Jul', '12', '19:30:54', '2044', 'GMT']
                                    # datetime.datetime.strptime('Jul 12 19:30:54 2044 GMT','%b %d %I:%M%p %Y')
                                    # d = datetime.datetime.strptime('Jul 12 2044','%b %d %Y')
                                    splits = files_found[directory][name]['status'].split('=')[1].split()
                                    expiration = datetime.datetime.strptime(
                                        splits[0] + ' ' + splits[1] + ' ' + splits[3], '%b %d %Y')
                                    days_remaining = (expiration - now).days
                                    files_found[directory][name]['warning'] = str(days_remaining) + ' days remaining'
                            except:
                                files_found[directory][name]['warning'] = str(
                                    traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
                                open('/dev/shm/running_exceptions_' + service, 'w').write(
                                    traceback.format_exc().replace("\"", "'"))

                        md5_result = ''
                        try:
                            # md5_result = hashlib.md5(open(location, 'r').read().encode('utf-8')).hexdigest()
                            md5_result = hash_file(location)
                        except:
                            md5_result = '(error)'
                            open('/dev/shm/running_exceptions_' + service, 'w').write(
                                traceback.format_exc().replace("\"", "'"))

                        files_found[directory][name]['md5'] = md5_result

                files_found['(defaults)'] = {
                    'localhost.key': {'md5': hashlib.md5(s_localhost_private_key.encode('utf-8')).hexdigest(),
                                      'status': 'default', 'warning': ''}}

                result = certificate_report(files_found)

                atomic_file_write(s_results_file_location, json.dumps(result), owner=apache_user_name)

        time.sleep(0.5)


# ====================================
def hash_file(filename: str, blocksize: int = 4096) -> str:
    # ====================================
    # https://stackoverflow.com/questions/55614741/python-3-7-hashing-a-binary-file
    hsh = hashlib.md5()
    with open(filename, "rb") as f:
        while True:
            buf = f.read(blocksize)
            if not buf:
                break
            hsh.update(buf)
    return hsh.hexdigest()


# ====================================
def make_body_POST(environ):
    # ====================================
    just_did_upload = False
    copy_to_do = ''
    delete_to_do = ''

    body = ''

    # use cgi module to read data
    body_of_form = read(environ)
    field_storage = cgi.FieldStorage(fp=body_of_form, environ=environ, keep_blank_values=True)

    TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    if len(field_storage.list):
        # on file upload
        # [FieldStorage('file', 'Archive.zip', 'PK...

        for item in field_storage.list:
            if item.filename:
                body += 'filename, ' + str(item.filename)
                file_content = item.file.read()  # .decode('utf-8')

                # Need this to write to /dev/shm, and then tell runner service to process it
                output_file = s_upload_spot_for_new_certs + 'ssl_' + TS + '.zip'
                if not os.path.exists(os.path.dirname(output_file)):
                    os.makedirs(os.path.dirname(output_file))
                with open(output_file, 'wb') as f:
                    f.write(file_content)

                # tell runner to process it
                just_did_upload = True

            # return (str(dir(item)))
            # ['__doc__', '__init__', '__module__', '__repr__', 'disposition', 'disposition_options', 'file', 'filename', 'headers', 'list', 'name', 'type', 'type_options', 'value']

            # return (str(item.name) + '|' + str(item.value))
            # service_certificates_set_selection|20210404
            if item.name == 'service_certificates_set_selection':
                copy_to_do = str(item.value)
                # return ("ready to copy: " + copy_to_do)

            if item.name == 'service_certificates_delete_selection':
                delete_to_do = str(item.value)

    # then return what GET would have done
    return make_body_GET(environ, just_did_upload=just_did_upload, copy_to_do=copy_to_do, delete_to_do=delete_to_do)


# ====================================
def read(environ):
    # ====================================
    length = int(environ.get('CONTENT_LENGTH', 0))
    stream = environ['wsgi.input']
    body = TemporaryFile(mode='w+b')
    while length > 0:
        part = stream.read(min(length, 1024 * 200))  # 200KB buffer size
        if not part: break
        body.write(part)
        length -= len(part)
    body.seek(0)
    environ['wsgi.input'] = body
    return body


# ====================================
def make_body_GET_tables(result, show_modify=False, show_delete=False):
    # ====================================
    # return json.dumps(result)

    body = ''

    body += '<center>'
    if not result:
        body += 'Request timed out'
    else:
        for directory_d in result:
            name_of_certs_folder = str(directory_d['directory'])

            if '(' in name_of_certs_folder:
                is_the_active_certs = True
            else:
                is_the_active_certs = False

            if show_modify and ('(' not in name_of_certs_folder):
                body += '<br>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<form method="post" action="">'
                body += '<select name="service_certificates_set_selection" id="service_certificates_set_selection" hidden>'
                body += '<option value="' + name_of_certs_folder + '" selected>' + '' + '</option>'
                body += '</select>'
                body += '<td>'
                body += '<B>' + name_of_certs_folder + '</B>'
                body += '</td>'

                body += '<td>'
                body += '<input type="submit" value="Copy these certificates to (active)">'
                body += '</form>'
                body += '</td>'
                body += '<td>'
                body += 'One click is all you get, and the process of copy will happen. After you do a loader reboot (instructions above),<br> then you will need to reload the page to see the results.'
                body += '</td>'
                body += '</tr>'
                body += '</table>'

            else:
                body += '<br><B>' + name_of_certs_folder + '</B>'

            if show_delete and ('(' not in name_of_certs_folder):
                body += '<br>'
                body += '<table border="1" cellpadding="5">'
                body += '<tr>'
                body += '<form method="post" action="">'
                body += '<select name="service_certificates_delete_selection" id="service_certificates_delete_selection" hidden>'
                body += '<option value="' + name_of_certs_folder + '" selected>' + '' + '</option>'
                body += '</select>'
                body += '<td>'
                body += '<B>' + name_of_certs_folder + '</B>'
                body += '</td>'

                body += '<td>'
                body += '<input type="submit" value="Delete these certificates">'
                body += '</form>'
                body += '</td>'
                body += '<td>'
                body += 'One click is all you get, and the process of delete will happen.<br>Then you will need to reload the page to see the results.'
                body += '</td>'
                body += '</tr>'
                body += '</table>'

            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            body += 'File'
            body += '</td>'
            body += '<td>'
            body += 'Status'
            body += '</td>'
            body += '<td>'
            body += 'Warning'
            body += '</td>'
            body += '<td>'
            body += 'md5'
            body += '</td>'
            body += '<td>'
            body += 'matches'
            body += '</td>'
            body += '</tr>'

            for file_d in directory_d['files']:
                body += '<tr>'
                body += '<td>'
                body += str(file_d['name'])
                body += '</td>'
                body += '<td>'
                body += str(file_d['status'])
                body += '</td>'

                color_to_use = ''
                value_to_use = ''
                try:
                    value_to_use = str(file_d['warning'])
                except:
                    pass

                color_to_use = ''
                if is_the_active_certs:
                    try:
                        days_remaining = int(str(file_d['warning']).split()[0])
                        if days_remaining < s_days_remaining_threshold_for_flag:
                            color_to_use = '(255, 100, 100, 0.3)'
                    except:
                        pass
                if color_to_use:
                    body += '<td style="background-color:rgba' + color_to_use + '">'
                else:
                    body += '<td>'

                body += value_to_use
                body += '</td>'

                body += '<td>'
                body += str(file_d['md5'])
                body += '</td>'
                body += '<td>'
                body += str(file_d['matches'])
                body += '</td>'
                body += '</tr>'
            body += '</table>'
    body += '</center>'

    return body


# ====================================
def make_body_GET(environ, just_did_upload=False, copy_to_do='', delete_to_do=''):
    # ====================================
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    show_modify = False
    if permissions.permission_prefix_allowed(environ, 'certificates_create'):
        show_modify = True

    show_delete = False
    if permissions.permission_prefix_allowed(environ, 'certificates_delete'):
        show_delete = True

    body = ''

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    # --------------------------------
    # show upload options
    # --------------------------------
    if show_modify:
        try:
            body += "<br><br>"
            body += '<center>'
            body += '<table border="1" cellpadding="5">'
            body += '<tr>'
            body += '<td>'
            body += 'Option 1) A *.zip file with new certificate password in a *.txt file, '
            body += 'and Slicer SSL certificate in a *.7z file (a direct zip of those two files, '
            body += 'not a zip of a folder containing those two files)<br><br>'
            body += 'Option 2) A *.zip with .cert and/or .key file(s))<br><br>'
            body += 'Option 3) A *.zip file with new certificate password in a *.txt file, '
            body += 'and Slicer SSL certificate in a slicercardinalhealthnet.pfx file (a direct zip of those two files, '
            body += 'not a zip of a folder containing those two files)<br><br>'
            body += 'After upload, wait a minute, refresh this page, to see the new content.<br><br>'
            body += 'Then, find the new item below here, and click on the "Copy these certificates to (active)" button.<br><br>'
            body += 'To make the new certificate be utilized, go to ' + url_to_use + '/loader and (with the correct permissions) then enter the command of "reboot", and click the cmd line "Submit" button'
            body += '</td>'

            body += '<td>'
            body += """
<form id="slicercertificateupload" name="slicercertificateupload" method=post enctype=multipart/form-data>
<input type=file name=file>"""
            body += '</td>'
            body += '<td>'
            body += """
<input type=submit value=upload>
</form>
"""
            body += '</td>'
            body += '</tr>'
            body += '</table>'
            body += '</center>'

            body_CHG = """
<B>Certificate update Change Control notes:</B>
Create a change without a template...
Assignment group: EITSS-Client Engineering
Assigned to: (yourself)
Category: Software/Application
Subcategory: Non-emergency Patch/Upgrade
Short description: Slicer.cardinahealth.net certificate update (month date, year)
Description: (same as short description)
Configuration item: Slicer
Environment Type: Prod
Change is for deployments of...: No
Next Stage: (click)
* Schedule (tab): (click)
Planned start date: (enter date and time)
Planned end date: (enter date and time)
Next Stage: (click)
* Planning (tab): (click)
Implementation plan: Load new cert
Rollback plan: Load old cert
Post - Implementation: check cert status
Pre-implementation testing: Yes (load to a develpment server, and check that it unzips and loads)
Peer Reviewer Name: (go over it with someone)
Is rollback possible: Yes
* Risk/Impact (tab): (click)
Risk: Low
Impact: 3-Low
The Impacted and Affected...: Yes



"""

            body += '<br>' + body_CHG.replace('\n', '<br>')

        except:
            pass

    # --------------------------------
    # Do the file system reporting
    # --------------------------------
    try:
        # create the file system request flag file
        try:
            os.remove(s_results_file_location)
        except:
            # body = str(traceback.format_exc().replace("\n","<br>").replace("\"","'"))
            # return body
            # ok to not already exist...
            pass

        if just_did_upload:
            atomic_file_write(s_request_file_location, 'uploaded')
        elif copy_to_do:
            atomic_file_write(s_request_file_location, 'copy:' + copy_to_do)
        elif delete_to_do:
            atomic_file_write(s_request_file_location, 'delete:' + delete_to_do)
        else:
            atomic_file_write(s_request_file_location, 'status')

        result = {}
        done = False
        start_time = time.time()
        while not done:
            if os.path.isfile(s_results_file_location):
                try:
                    content_of_response = str(open(s_results_file_location, 'r').read())
                    result = json.loads(content_of_response)
                    done = True
                except:
                    pass

            time.sleep(0.1)
            if abs(time.time() - start_time) > 10:
                done = True

        # do the reporting on what is in the file system
        body += make_body_GET_tables(result, show_modify, show_delete=show_delete)

    except Exception as e:
        body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    return body, other


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK',
             'response_header': [('Content-type', 'text/html')],
             'add_wrapper': True}

    if permissions.permission_prefix_allowed(environ, service + '_'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body, other = make_body_POST(environ)
            elif environ['REQUEST_METHOD'] == 'GET':
                body, other = make_body_GET(environ)
            permissions.log_page_allowed(environ, service, other)
        except:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))
    else:
        body = ""
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"
    return body, other


# ====================================
def application(environ, start_response):
    # ====================================
    status = '200 OK'
    html = '<html>\n' \
           '<body>\n'
    try:
        body, other = make_body(environ)
        html += body
    except:
        html += str(sys.version_info)

    html += '</body>\n' \
            '</html>\n'
    response_header = [('Content-type', 'text/html')]

    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)
    return [html.encode()]


# ----------------------------
def atomic_file_write(the_file_name, the_string_content, owner=''):
    # ----------------------------
    try:
        time_stamp = str(time.time())
        open(the_file_name + time_stamp, 'w').write(the_string_content)

        if owner:
            cmd = 'sudo chown ' + owner + ':' + owner + ' ' + the_file_name + time_stamp
            do_one_command(cmd)
            cmd = 'sudo mv -f ' + the_file_name + time_stamp + ' ' + the_file_name
            do_one_command(cmd)
        else:
            cmd = 'mv -f ' + the_file_name + time_stamp + ' ' + the_file_name
            do_one_command(cmd)

    except:
        pass


# ----------------------------
def do_one_command(command):
    # ----------------------------
    command_splits = shlex.split(command)

    doit = subprocess.Popen(command_splits, universal_newlines=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    (mem_string, fails) = doit.communicate()

    return (mem_string, fails)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_certificate_reporting_start(self):
        """
        (fill in here)
        """

        files_found = {
            '(active)':
                {
                    'test1.cer': {'full_path': '/tls/test1.cer', 'md5': 'fake_md5_1', 'status': 'expired long ago',
                                  'warning': ''}
                }
        }

        expected = [
            {
                'directory': '(active)',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1', 'matches': '',
                     'warning': ''}
                ]
            }
        ]

        actual = certificate_report(files_found)
        self.assertEqual(actual, expected)

    def test_certificate_reporting_subdir(self):
        """
        (fill in here)
        """
        files_found = {
            '(active)':
                {
                    'test1.cer': {'full_path': '/tls/test1.cer', 'md5': 'fake_md5_1', 'status': 'expired long ago',
                                  'warning': ''}
                },
            '20210401':
                {
                    'test1.cer': {'full_path': '/tls/20210401/test1.cer', 'md5': 'fake_md5_1',
                                  'status': 'expired long ago', 'warning': ''}
                }
        }

        expected = [
            {
                'directory': '(active)',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1', 'matches': '20210401',
                     'warning': ''}
                ]
            },
            {
                'directory': '20210401',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1', 'matches': '(active)',
                     'warning': ''}
                ]
            }
        ]

        actual = certificate_report(files_found)

        self.assertEqual(actual, expected)

    def test_certificate_reporting_subdir2(self):
        """
        (fill in here)
        """
        files_found = {
            '(active)':
                {
                    'test1.cer': {'full_path': '/tls/test1.cer', 'md5': 'fake_md5_1', 'status': 'expired long ago',
                                  'warning': 'test123'}
                },
            '20210401':
                {
                    'test1.cer': {'full_path': '/tls/20210401/test1.cer', 'md5': 'fake_md5_1',
                                  'status': 'expired long ago', 'warning': ''}
                },
            '20210301':
                {
                    'test1.cer': {'full_path': '/tls/20210401/test1.cer', 'md5': 'fake_md5_1',
                                  'status': 'expired long ago', 'warning': ''}
                },
        }

        expected = [
            {
                'directory': '(active)',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '20210301, 20210401', 'warning': 'test123'}
                ]
            },
            {
                'directory': '20210301',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '(active), 20210401', 'warning': ''}
                ]
            },
            {
                'directory': '20210401',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '(active), 20210301', 'warning': ''}
                ]
            },
        ]

        actual = certificate_report(files_found)

        self.assertEqual(actual, expected)

    def test_certificate_reporting_make_tables(self):
        """
        (fill in here)
        """

        result = [
            {
                'directory': '(active)',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '20210301, 20210401', 'warning': ''}
                ]
            },
            {
                'directory': '20210301',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '(active), 20210401', 'warning': ''}
                ]
            },
            {
                'directory': '20210401',
                'files': [
                    {'name': 'test1.cer', 'status': 'expired long ago', 'md5': 'fake_md5_1',
                     'matches': '(active), 20210301', 'warning': ''}
                ]
            },
        ]

        tables = make_body_GET_tables(result)
        tables = make_body_GET_tables(result, True)

# (end of file)
