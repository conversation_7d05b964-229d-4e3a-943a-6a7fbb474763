# A datastore for slicer page services

service = "datastore"
version = service + '.0.2'

_ = """
This file gets loaded to:
/var/www/html/datastore.py

using:
sudo vi /var/www/html/datastore.py

It also requires:

sudo vi /etc/httpd/conf.d/python-datastore.conf
----- start copy -----
WSGIScriptAlias /datastore /var/www/html/datastore.py
----- end copy -----

sudo chown apache:apache /var/www/html/datastore.py

sudo systemctl restart httpd

test on Slicer server with:
sudo su
cd /var/www/html
sudo python -c "import datastore; print(datastore.make_body())"


##############
# notes
##############

The url to hit it is:
https://slicer.cardinalhealth.net/datastore



"""

startup_exceptions = ''

import copy
import datetime
import traceback
import json
import os
import shutil
import sys
import time
import unittest

path_to_add = '/var/www/html/'
if not path_to_add in str(sys.path):
    sys.path.append(path_to_add)

try:
    import permissions
except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

"""
define key value storage
    have a get and a set
        These expect strings.
        If you need to store a number, then you have to convert it when you get it back
        If you have troubles, then only send strings in the first place (jsondumps before calling with a dictionary)
"""

service_config = {}
try:
    import organization

    service_config = organization.get_config(service)
    organization.make_all_dirs(service_config)

    datadrop_save_path = service_config['datadrop_save_path']
    base_datastore_path = service_config['base_datastore_path']
    base_datastorelog_path = service_config['base_datastorelog_path']
    base_datastorelog_cache_file = service_config['base_datastorelog_cache_file']
    do_trust_path = service_config['do_trust_path']
    time_of_last_tasks_run_trust_path = service_config['time_of_last_tasks_run_trust_path']
    datastore_first_trust_diff_path = service_config['datastore_first_trust_diff_path']

except:
    startup_exceptions += traceback.format_exc().replace("\"", "'")

try:
    open('/dev/shm/startup_exceptions_' + service, 'w').write(startup_exceptions)
except:
    pass

first_trust_test = True


# ----------------------------
def make_home_url_from_environ(environ):
    # ----------------------------
    home_url = environ['REQUEST_SCHEME'] + '://' + environ['HTTP_HOST']
    return home_url


# ====================================
def all_datastore(sub_string='', data_store_content=None):
    # ====================================
    result = {}

    try:
        if data_store_content:
            key_names = data_store_content.keys()
            for key_name in key_names:
                if sub_string:
                    if sub_string in key_name:
                        result[key_name] = data_store_content(key_name)
                else:
                    result[key_name] = data_store_content(key_name)
        else:
            key_names = os.listdir(base_datastore_path)

            for key_name in key_names:
                if sub_string:
                    if sub_string in key_name:
                        result[key_name] = get_value(key_name).replace('\n', '')
                else:
                    result[key_name] = get_value(key_name).replace('\n', '')
    except:
        pass

    return result


# ====================================
def get_all_devices():
    # ====================================
    ids = {}
    base_path = '/var/log/slicer/checkin/json/id/'
    id_files = os.listdir(base_path)
    return id_files


# ====================================
def format_macs():
    # ====================================
    body = ''

    try:
        # get all the mac addresses that we know of
        body += '<center>'
        body += '<table border="1" cellpadding="5">'
        for id in get_all_devices():
            datadrop_runner_file = datadrop_save_path + id + '/runner'
            runner_content = {}
            try:
                with open(datadrop_runner_file, 'r') as f:
                    runner_content = json.loads(f.read())
            except:
                pass

            to_show = []
            if 'wlan0mac' in runner_content:
                to_show.append(runner_content['wlan0mac'])
            if 'wlan1mac' in runner_content:
                to_show.append(runner_content['wlan1mac'])

            for item_to_show in to_show:
                body += '<tr>'
                body += '<td>'
                body += str(item_to_show)
                body += '</td>'
                body += '</tr>'
        body += '</table>'
        body += '</center>'

        pass
    except:
        pass

    return body


# ----------------------------
def do_atomic_write_if_different(output_file, content):
    # ----------------------------
    if not os.path.exists(os.path.dirname(output_file)):
        os.makedirs(os.path.dirname(output_file))

    try:
        with open(output_file, 'r') as f:
            existing_content = f.read()
    except:
        existing_content = ''

    temp_name = output_file + '.tmp'
    if existing_content != content:
        with open(temp_name, 'w') as f:
            f.write(content)

        shutil.move(temp_name, output_file)


# ====================================
def get_logs_by_key(partial_key):
    # ====================================
    logs = {}

    try:
        cache = json.loads(open(base_datastorelog_cache_file, 'r').read())
    except:
        cache = {}

    try:
        log_names = os.listdir(base_datastorelog_path)
        for log_name in log_names:
            try:
                if log_name in cache:
                    d = cache[log_name]
                else:
                    d = json.loads(open(base_datastorelog_path + log_name, 'r').read())
                    cache[log_name] = d

                if partial_key in d['key']:
                    logs[log_name] = d
            except:
                pass
    except:
        pass

    try:
        do_atomic_write_if_different(base_datastorelog_cache_file, json.dumps(cache))
    except:
        pass

    return logs


# ====================================
def begin_trust():
    # ====================================
    try:
        if not os.path.exists(os.path.dirname(do_trust_path)):
            os.makedirs(os.path.dirname(do_trust_path))
        with open(do_trust_path, 'w') as f:
            f.write('True')
    except:
        pass


# ====================================
def is_auto_trust_able(debugme=False):
    # ====================================
    global first_trust_test
    return_value = False

    # look to see if we can auto-start the trust
    try:
        with open(time_of_last_tasks_run_trust_path, 'r') as f:
            last_trust_time = float(f.read())
        time_diff = abs(time.time() - last_trust_time)
        if debugme:
            print(time_diff, time.time(), last_trust_time)

        if first_trust_test:
            first_trust_test = False
            try:
                with open(datastore_first_trust_diff_path, 'w') as f:
                    f.write(str(time_diff))
            except:
                if debugme:
                    print(traceback.format_exc())

        if time_diff < 60 * 60:
            return_value = True
    except:
        if debugme:
            print(traceback.format_exc())

    return return_value


# ====================================
def trust():
    # ====================================
    return_value = False

    try:
        if os.path.isfile(do_trust_path):
            return_value = True
    except:
        pass

    if not return_value:
        if is_auto_trust_able():
            begin_trust()
            return_value = True

    return return_value


# ====================================
def set_value(the_key, the_value, who='(who.missing)'):
    # ====================================
    if trust():
        output_file = base_datastore_path + the_key

        if not os.path.exists(os.path.dirname(output_file)):
            os.makedirs(os.path.dirname(output_file))

        with open(output_file, 'w') as f:
            f.write(str(the_value))

        # write a log record of what was set, and who set it
        TS = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
        # 20210406201829131704
        output_file = base_datastorelog_path + TS

        try:
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            with open(output_file, 'w') as f:
                f.write(json.dumps({'key': the_key, 'value': the_value, 'who': who}))
        except:
            pass


# ====================================
def get_keys_starting_with(the_key_prefix):
    # ====================================
    return_value = []

    if trust():
        try:
            list_of_keys = os.listdir(base_datastore_path)
            for item in list_of_keys:
                if the_key_prefix == item[:len(the_key_prefix)]:
                    return_value.append(item)
        except:
            pass

    return return_value


# ====================================
def get_value_stored(data_store_content, the_key, default_if_not_exist=''):
    # ====================================
    return_value = default_if_not_exist
    if the_key in data_store_content:
        return_value = data_store_content[the_key]

    return return_value


# ====================================
def get_value(the_key, default_if_not_exist=''):
    # ====================================
    if trust():
        output_file = base_datastore_path + the_key

        return_value = ''
        try:
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            if os.path.isfile(output_file):
                with open(output_file, 'r') as f:
                    return_value = f.read()
            else:
                return_value = default_if_not_exist
        except:
            return_value = '(exception on read)'

        return return_value.replace("<br>", "|")
    else:
        return ''


# ====================================
def get_value_with_trust(the_key, default_if_not_exist=''):
    # ====================================
    trust_value = trust()
    if trust_value:
        output_file = base_datastore_path + the_key

        return_value = ''
        try:
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))

            if os.path.isfile(output_file):
                with open(output_file, 'r') as f:
                    return_value = f.read()
            else:
                return_value = default_if_not_exist
        except:
            return_value = '(exception on read)'

        return return_value.replace("<br>", "|"), trust_value
    else:
        return '', trust_value


# ====================================
def make_body(environ):
    # ====================================
    body = ''
    other = {'status': '200 OK', 'response_header': [('Content-type', 'text/html')], 'add_wrapper': True}

    body += """
<script>

function URLjump(jumpLocation) {
    location.href = jumpLocation;
}

</script>
    """

    name_to_show = "Home"
    url_to_use = make_home_url_from_environ(environ)
    onclick = """\"""" + 'URLjump' + """('""" + url_to_use + """');return false\""""
    body += """<a href="" onclick=""" + onclick + """ style="text-decoration:none;color:inherit">""" + name_to_show + """</a>"""

    query_items = {}
    for item in environ['QUERY_STRING'].split(','):
        parms = item.split('=')
        if len(parms) > 1:
            query_items[parms[0]] = parms[1]

    if 'action' in query_items:
        if query_items['action'] == 'begintrust':
            begin_trust()

    if permissions.permission_allowed(environ, 'datastore_edit'):
        try:
            if environ['REQUEST_METHOD'] == 'POST':
                body += 'method = POST'
            elif environ['REQUEST_METHOD'] == 'GET':
                action_to_do = 'dump'
                if 'action' in query_items:
                    action_to_do = query_items['action']

                if action_to_do == 'begintrust':
                    begin_trust()
                    body += '<center>'
                    body += 'Done. Click Home to return to main.'
                    body += '</center>'

                elif action_to_do == 'macs':
                    body += format_macs()

                elif action_to_do == 'dump':
                    try:
                        all_items = all_datastore()

                        body += '<center>'
                        body += '<table border="1" cellpadding="5">'
                        for key_name in sorted(all_items):
                            body += '<tr>'
                            body += '<td>'
                            body += key_name
                            body += '</td>'
                            body += '<td>'
                            body += all_items[key_name]
                            body += '</td>'
                            body += '</tr>'
                        body += '</table>'
                        body += '</center>'
                    except:
                        pass
                else:
                    body += '<center>'
                    body += 'Action not found => ' + action_to_do
                    body += '</center>'

            else:
                body += 'method = other'
                body += str(environ)

        except Exception as e:
            body = str(traceback.format_exc().replace("\n", "<br>").replace("\"", "'"))

    else:
        body += "<br><br><br><br><br>"
        body += "<center><B>you do not have permissions for this page (are you logged in?)</B></center>"

    return body, other


# ====================================
def create_html_body(environ):
    try:
        body, other = make_body(environ)
        body_content = body
        permissions.log_page_allowed(environ, service, other)
    except:
        body_content = str(sys.version_info)
    return f"<html>\n<body>\n{body_content}\n</body>\n</html>\n"


def application(environ, start_response):
    status = '200 OK'
    html = create_html_body(environ)
    response_header = [('Content-type', 'text/html')]
    html = organization.wrap_page_with_session(environ, html)
    start_response(status, response_header)

    return [html.encode()]


# ====================================
def main():
    # ====================================
    pass_count = 0
    while True:
        pass_count += 1

        time.sleep(2)


# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Test Code
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
class TestAllMethods(unittest.TestCase):
    """
Test all
    """

    def setUp(self):
        """ doc String """
        self.start_time = time.time()

    def tearDown(self):
        """ doc String """
        time_spent = time.time() - self.start_time
        print('%s: %.3f' % (self.id(), time_spent))

    def test_template(self):
        """
        (fill in here)
        """
        self.assertEqual(True, True)

# End of source file
